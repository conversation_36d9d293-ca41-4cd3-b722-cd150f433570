# Configuration Guide

This document provides detailed configuration options for the OpenAI Plus Redeem Plugin.

## Configuration File Location

Plugin configuration is stored in:
- **Main config**: `configs/core/plugin_config.json`
- **Plugin-specific**: `configs/plugins/openai_plus_redeem/config.json` (optional)

## Configuration Schema

### Core Settings

```json
{
  "openai_plus_redeem": {
    "enabled": true,
    "debug": false,
    "email_config": {
      "imap_server": "imap.gmail.com",
      "imap_port": 993,
      "use_ssl": true,
      "require_verification": true,
      "global_credentials": {
        "email": "<EMAIL>",
        "password": "your-app-password"
      },
      "search_config": {
        "max_search_results": 10,
        "search_timeout_seconds": 30,
        "verification_keywords": ["openai", "verification", "code"]
      }
    },
    "cooldown_config": {
      "default_cooldown_hours": 24,
      "max_reset_attempts": 3,
      "enable_cooldown_management": true,
      "admin_can_override": true
    },
    "security_config": {
      "max_redemptions_per_user": 5,
      "enable_abuse_prevention": true,
      "rate_limit_requests_per_minute": 10,
      "admin_rate_limit_requests_per_minute": 100,
      "enable_ip_whitelist": false,
      "whitelisted_ips": []
    },
    "shopee_integration": {
      "enabled": true,
      "webhook_secret": "your-webhook-secret",
      "auto_process_orders": true,
      "supported_skus": ["chatgpt_plus", "chatgpt_premium"],
      "order_validation": {
        "require_buyer_username": true,
        "validate_order_status": true,
        "allowed_order_statuses": ["COMPLETED", "SHIPPED"]
      }
    },
    "account_management": {
      "auto_cleanup_expired": true,
      "cleanup_interval_hours": 24,
      "max_concurrent_users_default": 5,
      "account_expiry_warning_days": 7
    },
    "data_persistence": {
      "backup_enabled": true,
      "backup_interval_hours": 6,
      "max_backup_files": 10,
      "data_encryption": false
    }
  }
}
```

## Detailed Configuration Options

### 1. Core Plugin Settings

#### enabled
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable or disable the entire plugin
- **Example**: `"enabled": true`

#### debug
- **Type**: Boolean
- **Default**: `false`
- **Description**: Enable debug logging for troubleshooting
- **Example**: `"debug": true`

### 2. Email Configuration

The email configuration is crucial for verification code retrieval from Gmail.

#### imap_server
- **Type**: String
- **Default**: `"imap.gmail.com"`
- **Description**: IMAP server hostname
- **Example**: `"imap_server": "imap.gmail.com"`

#### imap_port
- **Type**: Integer
- **Default**: `993`
- **Description**: IMAP server port (993 for SSL, 143 for non-SSL)
- **Example**: `"imap_port": 993`

#### use_ssl
- **Type**: Boolean
- **Default**: `true`
- **Description**: Use SSL/TLS for IMAP connection
- **Example**: `"use_ssl": true`

#### require_verification
- **Type**: Boolean
- **Default**: `true`
- **Description**: Require email verification for redemptions
- **Example**: `"require_verification": true`

#### global_credentials
Configuration for the Gmail account used for verification:

```json
{
  "email": "<EMAIL>",
  "password": "your-app-password"
}
```

**Important**: Use Gmail App Passwords, not your regular Gmail password.

#### search_config
Email search configuration:

```json
{
  "max_search_results": 10,
  "search_timeout_seconds": 30,
  "verification_keywords": ["openai", "verification", "code"]
}
```

### 3. Cooldown Configuration

Controls user access restrictions to prevent abuse.

#### default_cooldown_hours
- **Type**: Integer
- **Default**: `24`
- **Description**: Default cooldown period in hours after redemption
- **Example**: `"default_cooldown_hours": 24`

#### max_reset_attempts
- **Type**: Integer
- **Default**: `3`
- **Description**: Maximum cooldown reset attempts per user
- **Example**: `"max_reset_attempts": 3`

#### enable_cooldown_management
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable cooldown system
- **Example**: `"enable_cooldown_management": true`

#### admin_can_override
- **Type**: Boolean
- **Default**: `true`
- **Description**: Allow admins to override cooldowns
- **Example**: `"admin_can_override": true`

### 4. Security Configuration

#### max_redemptions_per_user
- **Type**: Integer
- **Default**: `5`
- **Description**: Maximum redemptions allowed per user
- **Example**: `"max_redemptions_per_user": 5`

#### enable_abuse_prevention
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable abuse prevention mechanisms
- **Example**: `"enable_abuse_prevention": true`

#### rate_limit_requests_per_minute
- **Type**: Integer
- **Default**: `10`
- **Description**: Rate limit for customer endpoints
- **Example**: `"rate_limit_requests_per_minute": 10`

#### admin_rate_limit_requests_per_minute
- **Type**: Integer
- **Default**: `100`
- **Description**: Rate limit for admin endpoints
- **Example**: `"admin_rate_limit_requests_per_minute": 100`

#### enable_ip_whitelist
- **Type**: Boolean
- **Default**: `false`
- **Description**: Enable IP whitelisting for admin endpoints
- **Example**: `"enable_ip_whitelist": true`

#### whitelisted_ips
- **Type**: Array of Strings
- **Default**: `[]`
- **Description**: List of whitelisted IP addresses
- **Example**: `"whitelisted_ips": ["*************", "*********"]`

### 5. Shopee Integration

#### enabled
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable Shopee integration
- **Example**: `"enabled": true`

#### webhook_secret
- **Type**: String
- **Default**: `null`
- **Description**: Secret key for webhook validation
- **Example**: `"webhook_secret": "your-secret-key"`

#### auto_process_orders
- **Type**: Boolean
- **Default**: `true`
- **Description**: Automatically process incoming Shopee orders
- **Example**: `"auto_process_orders": true`

#### supported_skus
- **Type**: Array of Strings
- **Default**: `["chatgpt_plus", "chatgpt_premium"]`
- **Description**: List of supported product SKUs
- **Example**: `"supported_skus": ["chatgpt_plus", "chatgpt_premium"]`

#### order_validation
Order validation settings:

```json
{
  "require_buyer_username": true,
  "validate_order_status": true,
  "allowed_order_statuses": ["COMPLETED", "SHIPPED"]
}
```

### 6. Account Management

#### auto_cleanup_expired
- **Type**: Boolean
- **Default**: `true`
- **Description**: Automatically cleanup expired accounts
- **Example**: `"auto_cleanup_expired": true`

#### cleanup_interval_hours
- **Type**: Integer
- **Default**: `24`
- **Description**: Interval between cleanup operations
- **Example**: `"cleanup_interval_hours": 24`

#### max_concurrent_users_default
- **Type**: Integer
- **Default**: `5`
- **Description**: Default maximum concurrent users per account
- **Example**: `"max_concurrent_users_default": 5`

#### account_expiry_warning_days
- **Type**: Integer
- **Default**: `7`
- **Description**: Days before expiry to show warnings
- **Example**: `"account_expiry_warning_days": 7`

### 7. Data Persistence

#### backup_enabled
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable automatic data backups
- **Example**: `"backup_enabled": true`

#### backup_interval_hours
- **Type**: Integer
- **Default**: `6`
- **Description**: Interval between backups in hours
- **Example**: `"backup_interval_hours": 6`

#### max_backup_files
- **Type**: Integer
- **Default**: `10`
- **Description**: Maximum number of backup files to keep
- **Example**: `"max_backup_files": 10`

#### data_encryption
- **Type**: Boolean
- **Default**: `false`
- **Description**: Enable data encryption for sensitive information
- **Example**: `"data_encryption": true`

## Setup Instructions

### 1. Gmail Configuration

To enable email verification, you need to set up Gmail App Passwords:

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Navigate to Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
   - Copy the generated password

3. **Configure Email Settings**:
   ```json
   {
     "email_config": {
       "imap_server": "imap.gmail.com",
       "imap_port": 993,
       "use_ssl": true,
       "global_credentials": {
         "email": "<EMAIL>",
         "password": "your-16-character-app-password"
       }
     }
   }
   ```

### 2. Shopee Integration Setup

1. **Configure Webhook Endpoint**:
   - Set up webhook URL: `https://your-domain.com/openai-plus-redeem/api/shopee/redeem`
   - Configure webhook secret in Shopee dashboard
   - Update plugin configuration with the secret

2. **Configure Supported Products**:
   ```json
   {
     "shopee_integration": {
       "enabled": true,
       "webhook_secret": "your-webhook-secret-from-shopee",
       "supported_skus": ["chatgpt_plus", "chatgpt_premium"],
       "auto_process_orders": true
     }
   }
   ```

### 3. Security Configuration

1. **Rate Limiting**:
   ```json
   {
     "security_config": {
       "rate_limit_requests_per_minute": 10,
       "admin_rate_limit_requests_per_minute": 100
     }
   }
   ```

2. **IP Whitelisting** (Optional):
   ```json
   {
     "security_config": {
       "enable_ip_whitelist": true,
       "whitelisted_ips": [
         "*************",
         "*********",
         "***********/24"
       ]
     }
   }
   ```

### 4. Data Directory Setup

Ensure proper permissions for data directories:

```bash
# Create data directories
mkdir -p configs/data/openai_plus_redeem
mkdir -p configs/data/openai_plus_redeem/backups

# Set permissions (Linux/Mac)
chmod 755 configs/data/openai_plus_redeem
chmod 755 configs/data/openai_plus_redeem/backups

# For Docker environments
chown -R app:app configs/data/openai_plus_redeem
```

## Configuration Examples

### 1. Minimal Configuration

Basic setup with essential features:

```json
{
  "openai_plus_redeem": {
    "enabled": true,
    "email_config": {
      "global_credentials": {
        "email": "<EMAIL>",
        "password": "your-app-password"
      }
    }
  }
}
```

### 2. Production Configuration

Full production setup with all features:

```json
{
  "openai_plus_redeem": {
    "enabled": true,
    "debug": false,
    "email_config": {
      "imap_server": "imap.gmail.com",
      "imap_port": 993,
      "use_ssl": true,
      "require_verification": true,
      "global_credentials": {
        "email": "<EMAIL>",
        "password": "your-app-password"
      },
      "search_config": {
        "max_search_results": 20,
        "search_timeout_seconds": 45,
        "verification_keywords": ["openai", "chatgpt", "verification", "code"]
      }
    },
    "cooldown_config": {
      "default_cooldown_hours": 24,
      "max_reset_attempts": 3,
      "enable_cooldown_management": true,
      "admin_can_override": true
    },
    "security_config": {
      "max_redemptions_per_user": 10,
      "enable_abuse_prevention": true,
      "rate_limit_requests_per_minute": 15,
      "admin_rate_limit_requests_per_minute": 200,
      "enable_ip_whitelist": true,
      "whitelisted_ips": ["***********/24", "10.0.0.0/8"]
    },
    "shopee_integration": {
      "enabled": true,
      "webhook_secret": "your-production-webhook-secret",
      "auto_process_orders": true,
      "supported_skus": ["chatgpt_plus", "chatgpt_premium", "chatgpt_team"],
      "order_validation": {
        "require_buyer_username": true,
        "validate_order_status": true,
        "allowed_order_statuses": ["COMPLETED", "SHIPPED", "DELIVERED"]
      }
    },
    "account_management": {
      "auto_cleanup_expired": true,
      "cleanup_interval_hours": 12,
      "max_concurrent_users_default": 5,
      "account_expiry_warning_days": 14
    },
    "data_persistence": {
      "backup_enabled": true,
      "backup_interval_hours": 4,
      "max_backup_files": 20,
      "data_encryption": true
    }
  }
}
```

### 3. Development Configuration

Development setup with debugging enabled:

```json
{
  "openai_plus_redeem": {
    "enabled": true,
    "debug": true,
    "email_config": {
      "global_credentials": {
        "email": "<EMAIL>",
        "password": "test-app-password"
      }
    },
    "cooldown_config": {
      "default_cooldown_hours": 1,
      "enable_cooldown_management": false
    },
    "security_config": {
      "max_redemptions_per_user": 100,
      "enable_abuse_prevention": false,
      "rate_limit_requests_per_minute": 1000
    },
    "shopee_integration": {
      "enabled": false
    }
  }
}
```

### 4. High-Security Configuration

Maximum security setup:

```json
{
  "openai_plus_redeem": {
    "enabled": true,
    "debug": false,
    "email_config": {
      "require_verification": true,
      "global_credentials": {
        "email": "<EMAIL>",
        "password": "your-secure-app-password"
      }
    },
    "cooldown_config": {
      "default_cooldown_hours": 48,
      "max_reset_attempts": 1,
      "enable_cooldown_management": true,
      "admin_can_override": false
    },
    "security_config": {
      "max_redemptions_per_user": 3,
      "enable_abuse_prevention": true,
      "rate_limit_requests_per_minute": 5,
      "admin_rate_limit_requests_per_minute": 50,
      "enable_ip_whitelist": true,
      "whitelisted_ips": ["*************"]
    },
    "data_persistence": {
      "backup_enabled": true,
      "backup_interval_hours": 2,
      "max_backup_files": 50,
      "data_encryption": true
    }
  }
}
```

## Configuration Validation

The plugin validates configuration on startup. Common validation errors:

### 1. Email Configuration Errors

**Error**: `Invalid email credentials`
- **Cause**: Incorrect Gmail credentials or app password
- **Solution**: Verify email and regenerate app password

**Error**: `IMAP connection failed`
- **Cause**: Network issues or incorrect IMAP settings
- **Solution**: Check network connectivity and IMAP server settings

### 2. Security Configuration Errors

**Error**: `Invalid rate limit configuration`
- **Cause**: Rate limit values are not positive integers
- **Solution**: Ensure rate limits are positive numbers

**Error**: `Invalid IP address in whitelist`
- **Cause**: Malformed IP address or CIDR notation
- **Solution**: Use valid IP addresses (e.g., "***********" or "***********/24")

### 3. Shopee Integration Errors

**Error**: `Invalid webhook secret`
- **Cause**: Missing or empty webhook secret
- **Solution**: Configure webhook secret from Shopee dashboard

**Error**: `Unsupported SKU format`
- **Cause**: Invalid SKU format in supported_skus
- **Solution**: Use valid SKU formats (e.g., "chatgpt_plus")

## Environment Variables

You can override configuration using environment variables:

```bash
# Email configuration
export OPR_EMAIL_ADDRESS="<EMAIL>"
export OPR_EMAIL_PASSWORD="your-app-password"

# Security settings
export OPR_RATE_LIMIT="10"
export OPR_MAX_REDEMPTIONS="5"

# Shopee integration
export OPR_SHOPEE_WEBHOOK_SECRET="your-webhook-secret"

# Debug mode
export OPR_DEBUG="true"
```

Environment variables take precedence over configuration file settings.

## Dynamic Configuration Updates

The plugin supports runtime configuration updates through the admin interface or API.

### 1. Admin Interface

1. Navigate to `/admin/openai-plus-redeem/`
2. Click "Configuration" tab
3. Update settings and click "Save"
4. Changes take effect immediately

### 2. API Updates

```bash
# Update email configuration
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/config/email \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "imap_server": "imap.gmail.com",
    "global_credentials": {
      "email": "<EMAIL>",
      "password": "new-app-password"
    }
  }'

# Update security settings
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/config/security \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "rate_limit_requests_per_minute": 20,
    "max_redemptions_per_user": 10
  }'
```

### 3. Configuration Reload

To reload configuration without restarting:

```bash
# Reload plugin configuration
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/reload \
  -H "Authorization: Bearer admin_token"
```

## Configuration Best Practices

### 1. Security Best Practices

- **Use strong app passwords** for Gmail integration
- **Enable IP whitelisting** for admin endpoints in production
- **Set appropriate rate limits** based on expected usage
- **Enable data encryption** for sensitive information
- **Regularly rotate webhook secrets**

### 2. Performance Best Practices

- **Adjust cleanup intervals** based on data volume
- **Configure appropriate backup intervals** (not too frequent)
- **Set reasonable timeout values** for email searches
- **Monitor rate limits** and adjust based on usage patterns

### 3. Monitoring Best Practices

- **Enable debug logging** during initial setup
- **Monitor backup creation** and storage usage
- **Set up alerts** for configuration validation errors
- **Regularly review** security logs and access patterns

### 4. Backup and Recovery

- **Enable automatic backups** in production
- **Store backups** in secure, separate location
- **Test backup restoration** procedures regularly
- **Document recovery procedures** for your team

## Troubleshooting Configuration Issues

### 1. Plugin Won't Start

Check the main application logs for configuration validation errors:

```bash
tail -f logs/application.log | grep "openai_plus_redeem"
```

### 2. Email Verification Fails

1. Verify Gmail app password is correct
2. Check IMAP server settings
3. Test email connectivity manually
4. Review email search configuration

### 3. Shopee Integration Issues

1. Verify webhook URL is accessible
2. Check webhook secret configuration
3. Review supported SKUs list
4. Test webhook endpoint manually

### 4. Performance Issues

1. Check rate limit settings
2. Review cleanup intervals
3. Monitor backup frequency
4. Analyze data volume and growth

For additional troubleshooting, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).
