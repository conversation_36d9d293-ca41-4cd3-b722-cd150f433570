#!/usr/bin/env python3
"""
Email Service Test Script

This script provides manual testing capabilities for the email service
with real Gmail accounts. Use this for testing actual IMAP connections
and verification code extraction.

IMPORTANT: This script requires real Gmail credentials and should only
be used in development/testing environments.
"""

import sys
import os
import json
import getpass
from pathlib import Path
from datetime import datetime

# Add plugin directory to Python path
plugin_dir = Path(__file__).parent
sys.path.insert(0, str(plugin_dir.parent.parent))

from plugins.openai_plus_redeem.services.email_service import EmailService


class EmailServiceTester:
    """Manual email service tester"""
    
    def __init__(self):
        self.email_service = None
        self.test_config = None
    
    def setup_test_config(self, email=None, password=None):
        """Setup test configuration"""
        if not email:
            email = input("Enter Gmail address: ")
        
        if not password:
            password = getpass.getpass("Enter Gmail app password: ")
        
        self.test_config = {
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'use_ssl': True,
                'require_verification': True,
                'global_credentials': {
                    'email': email,
                    'password': password
                }
            }
        }
        
        print(f"✅ Configuration set for: {email}")
    
    def initialize_service(self):
        """Initialize email service"""
        if not self.test_config:
            print("❌ No configuration set. Run setup_test_config() first.")
            return False
        
        print("🔄 Initializing email service...")
        
        # Create mock logger
        class MockLogger:
            def info(self, msg): print(f"INFO: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
            def debug(self, msg): print(f"DEBUG: {msg}")
        
        self.email_service = EmailService(self.test_config, MockLogger())
        
        result = self.email_service.initialize()
        if result:
            print("✅ Email service initialized successfully")
        else:
            print("❌ Failed to initialize email service")
        
        return result
    
    def test_connection(self):
        """Test IMAP connection"""
        if not self.email_service:
            print("❌ Email service not initialized")
            return False
        
        print("🔄 Testing IMAP connection...")
        
        credentials = self.test_config['email_config']['global_credentials']
        result = self.email_service._test_email_connection(
            credentials['email'],
            credentials['password']
        )
        
        if result['success']:
            print("✅ IMAP connection successful")
        else:
            print(f"❌ IMAP connection failed: {result['error']}")
        
        return result['success']
    
    def test_verification_code_extraction(self, target_email=None):
        """Test verification code extraction"""
        if not self.email_service:
            print("❌ Email service not initialized")
            return False
        
        if not target_email:
            target_email = input("Enter email to check for verification codes: ")
        
        print(f"🔄 Searching for verification codes for: {target_email}")
        print("📧 Looking for recent emails from OpenAI/ChatGPT...")
        
        result = self.email_service.get_verification_code(target_email)
        
        if result['success']:
            print(f"✅ Verification code found: {result['code']}")
            print(f"📧 Source: {result['source']}")
            if 'timestamp' in result:
                print(f"🕒 Timestamp: {result['timestamp']}")
        else:
            print(f"❌ No verification code found: {result['error']}")
        
        return result
    
    def list_recent_emails(self, target_email=None, limit=10):
        """List recent emails for debugging"""
        if not self.email_service:
            print("❌ Email service not initialized")
            return False
        
        if not target_email:
            target_email = input("Enter email to check: ")
        
        print(f"🔄 Listing recent emails for: {target_email}")
        
        try:
            # Get recent emails for debugging
            credentials = self.test_config['email_config']['global_credentials']
            emails = self.email_service._get_recent_emails(
                credentials['email'],
                credentials['password'],
                limit=limit
            )
            
            if emails:
                print(f"📧 Found {len(emails)} recent emails:")
                for i, email_data in enumerate(emails, 1):
                    print(f"  {i}. From: {email_data.get('from', 'Unknown')}")
                    print(f"     Subject: {email_data.get('subject', 'No subject')}")
                    print(f"     Date: {email_data.get('date', 'Unknown')}")
                    print()
            else:
                print("📧 No recent emails found")
            
            return emails
            
        except Exception as e:
            print(f"❌ Error listing emails: {e}")
            return False
    
    def test_verification_logging(self):
        """Test verification logging functionality"""
        if not self.email_service:
            print("❌ Email service not initialized")
            return False
        
        print("🔄 Testing verification logging...")
        
        # Get current logs
        logs = self.email_service.get_verification_logs()
        initial_count = len(logs)
        
        print(f"📊 Current log entries: {initial_count}")
        
        if logs:
            print("📋 Recent log entries:")
            for log in logs[-5:]:  # Show last 5 entries
                print(f"  - {log.email} | {log.status.value} | {log.timestamp}")
        
        return True
    
    def run_comprehensive_test(self):
        """Run comprehensive email service test"""
        print("🚀 Starting comprehensive email service test")
        print("=" * 50)
        
        # Step 1: Setup configuration
        print("\n1️⃣ Setting up configuration...")
        self.setup_test_config()
        
        # Step 2: Initialize service
        print("\n2️⃣ Initializing service...")
        if not self.initialize_service():
            print("❌ Test failed at initialization")
            return False
        
        # Step 3: Test connection
        print("\n3️⃣ Testing IMAP connection...")
        if not self.test_connection():
            print("❌ Test failed at connection")
            return False
        
        # Step 4: Test verification code extraction
        print("\n4️⃣ Testing verification code extraction...")
        target_email = input("Enter email to test verification code extraction (or press Enter to skip): ")
        if target_email.strip():
            self.test_verification_code_extraction(target_email)
        else:
            print("⏭️ Skipping verification code extraction test")
        
        # Step 5: Test logging
        print("\n5️⃣ Testing verification logging...")
        self.test_verification_logging()
        
        print("\n✅ Comprehensive test completed!")
        print("=" * 50)
        
        return True


def main():
    """Main entry point"""
    print("📧 OpenAI Plus Redeem Plugin - Email Service Tester")
    print("=" * 60)
    print("⚠️  WARNING: This script requires real Gmail credentials!")
    print("⚠️  Only use in development/testing environments!")
    print("=" * 60)
    
    tester = EmailServiceTester()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'test':
            # Run comprehensive test
            tester.run_comprehensive_test()
        elif command == 'connection':
            # Test connection only
            tester.setup_test_config()
            tester.initialize_service()
            tester.test_connection()
        elif command == 'verify':
            # Test verification code extraction
            tester.setup_test_config()
            tester.initialize_service()
            if len(sys.argv) > 2:
                tester.test_verification_code_extraction(sys.argv[2])
            else:
                tester.test_verification_code_extraction()
        elif command == 'logs':
            # Test logging
            tester.setup_test_config()
            tester.initialize_service()
            tester.test_verification_logging()
        else:
            print(f"❌ Unknown command: {command}")
            print_usage()
    else:
        # Interactive mode
        print("\n🔧 Interactive mode - choose an option:")
        print("1. Run comprehensive test")
        print("2. Test connection only")
        print("3. Test verification code extraction")
        print("4. View verification logs")
        print("5. List recent emails")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == '1':
            tester.run_comprehensive_test()
        elif choice == '2':
            tester.setup_test_config()
            tester.initialize_service()
            tester.test_connection()
        elif choice == '3':
            tester.setup_test_config()
            tester.initialize_service()
            tester.test_verification_code_extraction()
        elif choice == '4':
            tester.setup_test_config()
            tester.initialize_service()
            tester.test_verification_logging()
        elif choice == '5':
            tester.setup_test_config()
            tester.initialize_service()
            tester.list_recent_emails()
        else:
            print("❌ Invalid choice")

def print_usage():
    """Print usage information"""
    print("\nUsage:")
    print("  python test_email_service.py                    # Interactive mode")
    print("  python test_email_service.py test               # Run comprehensive test")
    print("  python test_email_service.py connection         # Test connection only")
    print("  python test_email_service.py verify [email]     # Test verification extraction")
    print("  python test_email_service.py logs               # View verification logs")

if __name__ == '__main__':
    main()
