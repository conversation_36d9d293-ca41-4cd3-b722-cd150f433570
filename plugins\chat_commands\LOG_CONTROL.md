# Chat Commands Plugin - Log Control

This document explains how to control logging output from the Chat Commands plugin to reduce console spam.

## Overview

The Chat Commands plugin now includes advanced log filtering capabilities that allow you to control which types of logs are displayed in the console. This is particularly useful when the plugin is receiving many webhook messages, which can generate excessive log output.

## Accessing Log Controls

1. Navigate to the Chat Commands plugin interface: `http://localhost:5000/chat-commands/`
2. Scroll down to the **Debug Config** section
3. Configure the logging options according to your needs

## Log Control Options

### Basic Debug Control
- **Enable Debug Mode**: Master switch for all debug logging. When disabled, most noisy logs are automatically filtered out.

### Webhook & Message Processing
- **Log Webhook Data**: Shows incoming webhook data from ShopeeAPI
- **Log Message Parsing**: Shows how messages are parsed and interpreted
- **Log Command Processing**: Shows command matching and processing logic
- **Log Response Generation**: Shows response creation and sending details

### System Logs Control
- **Log Info Messages**: Controls general INFO level messages from the plugin
- **Log Access Audit**: Controls HTTP request audit logs (these can be very noisy)
- **Log Werkzeug**: Controls Flask development server logs
- **Log Service Operations**: Controls service startup, configuration loading, etc.

## Recommended Settings

### For Normal Operation (Minimal Logs)
```
Debug Mode: ❌ Disabled
All other options: ❌ Disabled
```
This will filter out most noisy logs while keeping errors and warnings visible.

### For Troubleshooting Webhook Issues
```
Debug Mode: ✅ Enabled
Log Webhook Data: ✅ Enabled
Log Message Parsing: ✅ Enabled
Log Command Processing: ✅ Enabled
Log Response Generation: ✅ Enabled
System logs: ❌ Disabled (unless needed)
```

### For Development/Debugging
```
Debug Mode: ✅ Enabled
All options: ✅ Enabled (as needed)
```

## What Gets Filtered

When debug mode is **disabled**, the following logs are automatically filtered out:
- `access_audit` logs (HTTP request auditing)
- `werkzeug` logs (Flask development server)
- `chat_commands` INFO level logs
- Service operation logs (loading, saving, etc.)

When debug mode is **enabled**, you can fine-tune which specific log types to show.

## Technical Details

The log filtering system works by:
1. Installing custom log filters when the plugin starts
2. Reading the debug configuration from `config.json`
3. Filtering log records based on logger name and message content
4. Automatically reloading configuration changes every 5 seconds

## Troubleshooting

If logs are not being filtered as expected:
1. Check that the plugin has been restarted after configuration changes
2. Verify the `config.json` file has the correct debug settings
3. Wait up to 5 seconds for configuration changes to take effect
4. Check for any errors in the console related to log filtering

## Configuration File

The log control settings are stored in `plugins/chat_commands/config.json` under the `debug_config` section:

```json
{
  "debug_config": {
    "enabled": false,
    "log_webhook_data": false,
    "log_message_parsing": false,
    "log_command_processing": false,
    "log_response_generation": false,
    "log_info_messages": false,
    "log_access_audit": false,
    "log_werkzeug": false,
    "log_service_operations": false
  }
}
```

## Impact on Performance

Log filtering has minimal performance impact and actually improves performance by reducing I/O operations when many logs would otherwise be written to the console.
