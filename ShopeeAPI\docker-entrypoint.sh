#!/bin/bash
# Docker entrypoint script for ShopeeAPI

# Function to fix permissions for config files
fix_permissions() {
    echo "Fixing permissions for config files..."
    sudo chown shopeeapi:shopeeapi /app/config.json 2>/dev/null || true
    sudo chmod 664 /app/config.json 2>/dev/null || true
    sudo chown -R shopeeapi:shopeeapi /app/cache 2>/dev/null || true
    sudo chmod -R 775 /app/cache 2>/dev/null || true
    sudo chown -R shopeeapi:shopeeapi /app/logs 2>/dev/null || true
    sudo chmod -R 775 /app/logs 2>/dev/null || true
    echo "Permissions fixed."
}

# Create config.json if it doesn't exist
CONFIG_PATH="/app/config.json"

if [ ! -f "$CONFIG_PATH" ]; then
    echo "No config.json found, creating a default one"
    echo '{
  "AUTHORIZATION_CODE": "",
  "COOKIE": "",
  "SHOP_ID": 0,
  "REGION_ID": "MY",
  "PAGE_SIZE": 40,
  "REQUEST_TIMEOUT": 60,
  "WEBSOCKET": {
    "ENABLED": true,
    "RECONNECT_INTERVAL": 30,
    "MAX_RECONNECT_ATTEMPTS": 10,
    "PING_INTERVAL": 25,
    "PING_TIMEOUT": 5,
    "CLIENT_MAX_SIZE": 100
  },
  "WEBHOOK": {
    "ENABLED": false,
    "MESSAGE_RECEIVED": {
      "ENABLED": false,
      "URL": "http://your-webhook-url/message-received",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    },
    "MESSAGE_SENT": {
      "ENABLED": false,
      "URL": "http://your-webhook-url/message-sent",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    }
  },
  "CACHE": {
    "ENABLED": true,
    "USERNAME_TO_CONVERSATION_ID": {
      "ENABLED": true,
      "EXPIRY_SECONDS": 86400,
      "MAX_SIZE": 1000
    },
    "CONVERSATION_MESSAGES": {
      "ENABLED": true,
      "EXPIRY_SECONDS": 3600,
      "MAX_SIZE": 50,
      "WEBSOCKET_ONLY": true
    }
  }
}' > "$CONFIG_PATH"
fi

# Fix permissions for all necessary files
fix_permissions

# Create cache and logs directories if they don't exist
mkdir -p /app/cache /app/logs

# Fix permissions again after creating directories
fix_permissions

# Set default log levels if not provided
export LOG_LEVEL=${LOG_LEVEL:-WARNING}
export WEBSOCKET_LOG_LEVEL=${WEBSOCKET_LOG_LEVEL:-ERROR}
export WEBHOOK_LOG_LEVEL=${WEBHOOK_LOG_LEVEL:-ERROR}

# If DEBUG_MODE is enabled, set all log levels to DEBUG
if [ "${DEBUG_MODE:-false}" = "true" ]; then
    export LOG_LEVEL=DEBUG
    export WEBSOCKET_LOG_LEVEL=DEBUG
    export WEBHOOK_LOG_LEVEL=DEBUG
    echo "DEBUG_MODE enabled - all log levels set to DEBUG"
fi

# Start the application
export PYTHONPATH=/app
cd /app
echo "Starting ShopeeAPI on port: ${PORT:-8000}"
echo "Log levels - General: $LOG_LEVEL, WebSocket: $WEBSOCKET_LOG_LEVEL, Webhook: $WEBHOOK_LOG_LEVEL"

# Environment check
echo "Environment check:"
if [ -f /app/check_env.py ]; then
    python /app/check_env.py
fi

# Make run_docker.py executable
chmod +x /app/run_docker.py

# Start the application using our custom script
exec python /app/run_docker.py
