# VPN Config Generator Commands

This document explains the available commands for the VPN Config Generator plugin.

## Available Commands

### 1. `#v` - Generate VPN Configuration

**Syntax:** `#v <server> <days> <telco> <plan>`

**Parameters:**
- `<server>` - Server ID (e.g., `11`, `5`) or server name (e.g., `server11`)
- `<days>` - Validity period in days (e.g., `30`, `7`)
- `<telco>` - Telco name (e.g., `digi`, `maxis`)
- `<plan>` - Plan name (e.g., `unlimited`, `basic`)

**Examples:**
```
#v 11 30 digi unlimited
#v 5 7 maxis basic
#v server12 14 celcom premium
```

**New Feature:** You can now use direct server IDs instead of the `server11` format:
- ✅ `#v 11 30 digi unlimited` (New - Direct ID)
- ✅ `#v server11 30 digi unlimited` (Old - Still works)

### 2. `#vlist` - List Available Servers and Configurations

**Syntax:** `#vlist`

**Description:** Shows all available servers, telcos, and plans that can be used with the `#v` command.

**Example:**
```
#vlist
```

**Output includes:**
- Available servers with their IDs and status
- Available telcos and their plans
- Usage examples
- Total number of configurations available

### 3. `#vservers` - List All VPN Servers with IDs

**Syntax:** `#vservers`

**Description:** Shows a detailed list of all VPN servers with their IDs, names, status, and connection information. This is useful when you need to remember server IDs for the `#v` command.

**Example:**
```
#vservers
```

**Output includes:**
- Server ID and name
- Server status (Online/Offline)
- Server location/description
- Host address
- Health status
- Summary of total servers and their status

**Use Case:** When you want to generate a VPN config but can't remember the server IDs, use this command to see all available servers with their IDs.

## Server Parameter Format

The `<server>` parameter now supports two formats:

### Direct Server ID (Recommended)
- Format: Just the number (e.g., `11`, `5`, `20`)
- Examples: `#v 11 30 digi unlimited`

### Legacy Server Name Format
- Format: `server` + number (e.g., `server11`, `server5`)
- Examples: `#v server11 30 digi unlimited`

Both formats work identically - the system extracts the numeric ID from either format.

## Getting Help

1. **List server IDs:** Use `#vservers` to see all servers with their IDs and status
2. **List available options:** Use `#vlist` to see all available servers, telcos, and plans
3. **Check syntax:** If you use incorrect parameters, the system will show the correct usage format
4. **Server status:** Both `#vservers` and `#vlist` commands show which servers are online/offline

## Configuration Management

Administrators can manage telcos and plans through the web interface:
- **Telco Management:** `/vpn-config-generator/telco-management`
- **API Configuration:** `/vpn-config-generator/`

## Troubleshooting

**Common Issues:**

1. **"No configuration template found"**
   - Use `#vlist` to check available telcos and plans
   - Ensure the telco and plan names match exactly

2. **"Invalid server format"**
   - Use either direct ID (e.g., `11`) or server name (e.g., `server11`)
   - Check available servers with `#vservers` or `#vlist`

3. **"Failed to generate VPN config"**
   - Check if the VPN API is configured properly
   - Contact administrator if the issue persists

## Examples by Use Case

**Quick 7-day config:**
```
#vservers                 # Check server IDs
#vlist                    # Check available options
#v 11 7 digi basic       # Generate 7-day config
```

**Long-term 30-day config:**
```
#v 5 30 maxis unlimited  # Generate 30-day unlimited plan
```

**Testing different servers:**
```
#vlist                   # See server status
#v 12 1 digi test       # 1-day test config on server 12
```

## User Management Commands

### 3. `#vuser` - View User VPN Configurations

**Syntax:** `#vuser [username]`

**Description:** Shows all VPN configurations for a specific user or the current user.

**Parameters:**
- `[username]` - Optional. Username to search for. If not provided, shows configurations for the current user (message sender)

**Examples:**
```
#vuser                    # Show your own configurations
#vuser john_doe          # Show configurations for user 'john_doe'
```

**Output includes:**
- List of all VPN configurations for the user
- Configuration status (active/expired)
- Server information
- Creation and expiry dates
- Client IDs for management

### 4. `#vdel` - Delete VPN Configuration

**Syntax:** `#vdel <client_id>`

**Description:** Deletes a specific VPN configuration by client ID.

**Parameters:**
- `<client_id>` - Required. The numeric client ID to delete (get from `#vuser` command)

**Examples:**
```
#vdel 123                # Delete client ID 123
```

**Note:** This action is permanent and cannot be undone.

### 5. `#vrenew` - Renew/Extend VPN Configuration

**Syntax:** `#vrenew <client_id> <days>`

**Description:** Extends the expiry date of a VPN configuration by the specified number of days.

**Parameters:**
- `<client_id>` - Required. The numeric client ID to renew (get from `#vuser` command)
- `<days>` - Required. Number of days to extend the configuration

**Examples:**
```
#vrenew 123 30          # Extend client ID 123 by 30 days
#vrenew 456 7           # Extend client ID 456 by 7 days
```

### 6. `#vtest` - Test VPN API Connectivity

**Syntax:** `#vtest`

**Description:** Tests VPN API connectivity and configuration. Useful for troubleshooting.

**Parameters:** None

**Examples:**
```
#vtest                  # Run VPN API connectivity test
```

**Output includes:**
- Plugin availability status
- API service configuration
- Authentication status
- Connectivity test results
- Troubleshooting information

### 7. `#vhelp` - Show VPN Commands Help

**Syntax:** `#vhelp`

**Description:** Displays comprehensive help information for all VPN commands. This command dynamically adapts to the configured command prefix.

**Parameters:** None

**Examples:**
```
#vhelp                  # Show help for all VPN commands
```

**Output includes:**
- Complete list of all available VPN commands
- Usage syntax for each command
- Parameter descriptions and examples
- Tips for effective usage
- Command prefix adaptation (e.g., if prefix is changed to `!`, shows `!vhelp`)

**Features:**
- **Dynamic prefix support:** Automatically uses the configured command prefix
- **Comprehensive coverage:** Shows all available commands in one place
- **User-friendly format:** Clear descriptions and examples for each command
- **Context-aware:** Adapts to current plugin configuration

## User Management Workflow

**Typical workflow for managing user VPN configurations:**

1. **Check user configurations:**
   ```
   #vuser john_doe       # View all configs for john_doe
   ```

2. **Delete expired or unwanted configurations:**
   ```
   #vdel 123            # Delete client ID 123
   ```

3. **Extend active configurations:**
   ```
   #vrenew 456 30       # Extend client ID 456 by 30 days
   ```

4. **Create new configuration if needed:**
   ```
   #v 11 30 digi unlimited  # Generate new config
   ```
