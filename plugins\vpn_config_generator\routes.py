"""
Routes for VPN Config Generator Plugin
"""

import logging
import os
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, render_template, flash, redirect, url_for, current_app
from typing import Dict, Any

from .models import VPNConfigRequest, VPNAPIConfig, ConfigGeneratorSettings, ConfigTemplate, WebhookMessage, VPNTelco, VPNPlan, SKURestriction
from .services import VPNConfigGeneratorService, VPNChatCommandService, VPNOrderService, SKURestrictionService, VPNSKUTagsService

logger = logging.getLogger(__name__)

def resolve_client_id(client_id_input, user):
    """
    Resolve client_id to numeric format.
    Handles both numeric IDs and UUIDs by looking up the corresponding numeric_id.
    """
    logger.debug(f"Resolving client_id: {client_id_input} (type: {type(client_id_input)})")

    try:
        # Try direct conversion first
        numeric_id = int(client_id_input)
        logger.debug(f"Successfully converted client_id to numeric: {numeric_id}")
        return numeric_id
    except (ValueError, TypeError):
        logger.debug(f"client_id '{client_id_input}' is not numeric, searching in user configs")

        # If it's not a number, try to find numeric_id from user's configs
        if user and hasattr(user, 'generated_configs') and user.generated_configs:
            logger.debug(f"User has {len(user.generated_configs)} generated configs")
            for i, config in enumerate(user.generated_configs):
                logger.debug(f"Config {i}: client_id={config.get('client_id')}, numeric_id={config.get('numeric_id')}")
                if config.get('client_id') == client_id_input:
                    numeric_id = config.get('numeric_id')
                    if numeric_id:
                        try:
                            resolved_id = int(numeric_id)
                            logger.debug(f"Found matching config with numeric_id: {resolved_id}")
                            return resolved_id
                        except (ValueError, TypeError):
                            logger.warning(f"Found matching config but numeric_id '{numeric_id}' is not valid")
                            pass
        else:
            logger.debug("User has no generated_configs or user is None")

        # If all else fails, raise an error
        error_msg = f"Could not resolve client_id '{client_id_input}' to a numeric ID"
        logger.error(error_msg)
        raise ValueError(error_msg)


def create_routes(plugin_instance) -> Blueprint:
    """Create and configure routes for the VPN Config Generator plugin"""

    # Get the plugin directory for template folder
    plugin_dir = plugin_instance.plugin_dir
    template_folder = os.path.join(plugin_dir, 'templates')

    bp = Blueprint('vpn_config_generator', __name__,
                   url_prefix='/vpn-config-generator',
                   template_folder=template_folder)
    config_service = plugin_instance.config_service

    def get_redemption_service():
        """Helper function to get or initialize redemption service"""
        if not hasattr(plugin_instance, 'redemption_service'):
            from .services import RedemptionLinkService
            plugin_instance.redemption_service = RedemptionLinkService(plugin_instance.plugin_dir, plugin_instance.plugin_manager)
        return plugin_instance.redemption_service
    
    @bp.route('/')
    def index():
        """VPN Config Generator dashboard"""
        try:
            api_config = config_service.get_api_config()
            generator_settings = config_service.get_generator_settings()
            telcos = config_service.get_all_telcos()

            return render_template('vpn_config_generator/dashboard.html',
                                 api_config=api_config.to_dict(),
                                 generator_settings=generator_settings.to_dict(),
                                 telcos={telco_id: telco.to_dict() for telco_id, telco in telcos.items()})
        except Exception as e:
            logger.error(f"Error loading dashboard: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/telco-management')
    def telco_management():
        """Telco and Plan management interface"""
        try:
            return render_template('vpn_config_generator/telco_management.html')
        except Exception as e:
            logger.error(f"Error loading telco management: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/command-management')
    def command_management():
        """Command management interface"""
        try:
            return render_template('vpn_config_generator/command_management.html')
        except Exception as e:
            logger.error(f"Error loading command management: {e}")
            return jsonify({'error': str(e)}), 500
    
    @bp.route('/api/generate', methods=['POST'])
    def generate_config():
        """Generate VPN configuration via API"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['server', 'days', 'telco', 'plan', 'username']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            # Optional SKU validation for enhanced security
            sku = data.get('sku') or data.get('var_sku')
            if sku:
                try:
                    from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
                    if not VPNStrategyFactory.is_vpn_product(sku):
                        return jsonify({
                            'success': False,
                            'error': f'Invalid product SKU: {sku}. Only VPN products are supported.'
                        }), 400
                    logger.info(f"SKU validation passed for direct API call: {sku}")
                except ImportError:
                    logger.warning("VPN strategy factory not available for SKU validation in direct API")

            # Create request object
            config_request = VPNConfigRequest.from_dict(data)

            # Generate configuration
            result = config_service.generate_config(config_request)
            
            if result.success:
                return jsonify({
                    'success': True,
                    'config': result.config,
                    'created_date': result.created_date,
                    'expired_date': result.expired_date,
                    'message': result.message
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result.error
                }), 400
                
        except Exception as e:
            logger.error(f"Error generating config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/config', methods=['GET'])
    def get_config():
        """Get current configuration"""
        try:
            api_config = config_service.get_api_config()
            generator_settings = config_service.get_generator_settings()
            
            return jsonify({
                'success': True,
                'api_config': api_config.to_dict(),
                'generator_settings': generator_settings.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/config/api', methods=['PUT'])
    def update_api_config():
        """Update API configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400
            
            # Create config object
            config = VPNAPIConfig.from_dict(data)
            
            # Update config
            if config_service.update_api_config(config):
                return jsonify({
                    'success': True,
                    'message': 'API configuration updated successfully',
                    'config': config.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update API configuration'}), 500
                
        except Exception as e:
            logger.error(f"Error updating API config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/config/generator', methods=['PUT'])
    def update_generator_settings():
        """Update generator settings"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400
            
            # Create settings object
            settings = ConfigGeneratorSettings.from_dict(data)
            
            # Update settings
            if config_service.update_generator_settings(settings):
                return jsonify({
                    'success': True,
                    'message': 'Generator settings updated successfully',
                    'settings': settings.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update generator settings'}), 500
                
        except Exception as e:
            logger.error(f"Error updating generator settings: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/config/base-url', methods=['GET'])
    def get_base_url():
        """Get base URL configuration for redemption links"""
        try:
            # Read config directly from file
            config_path = config_service.config_file
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
            
            base_url = config.get('redemption_links', {}).get('base_url', 'http://localhost:5000')
            
            return jsonify({
                'success': True,
                'base_url': base_url
            })
                
        except Exception as e:
            logger.error(f"Error getting base URL: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/config/base-url', methods=['POST'])
    def save_base_url():
        """Save base URL configuration for redemption links"""
        try:
            data = request.get_json()
            if not data or 'base_url' not in data:
                return jsonify({'success': False, 'error': 'Base URL is required'}), 400
            
            base_url = data['base_url'].rstrip('/')  # Remove trailing slash
            
            # Validate URL format
            from urllib.parse import urlparse
            parsed = urlparse(base_url)
            if not parsed.scheme or not parsed.netloc:
                return jsonify({'success': False, 'error': 'Invalid URL format'}), 400
            
            # Load current config
            config_path = config_service.config_file
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # Update base URL in redemption_links section
            if 'redemption_links' not in config:
                config['redemption_links'] = {}
            config['redemption_links']['base_url'] = base_url
            
            # Save config
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            return jsonify({
                'success': True,
                'message': 'Base URL saved successfully',
                'base_url': base_url
            })
                
        except Exception as e:
            logger.error(f"Error saving base URL: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/test-connection', methods=['POST'])
    def test_connection():
        """Test VPN API connection"""
        try:
            result = config_service.test_connection()
            return jsonify(result)
        except Exception as e:
            logger.error(f"Error testing connection: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/generate-username', methods=['POST'])
    def generate_username():
        """Generate username with optional random suffix"""
        try:
            data = request.get_json()
            base_name = data.get('base_name', 'user') if data else 'user'

            username = config_service.generate_username(base_name)

            return jsonify({
                'success': True,
                'username': username
            })
        except Exception as e:
            logger.error(f"Error generating username: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # Template Management Routes

    @bp.route('/api/templates', methods=['GET'])
    def get_templates():
        """Get all configuration templates"""
        try:
            templates = config_service.get_all_templates()
            return jsonify({
                'success': True,
                'templates': [template.to_dict() for template in templates]
            })
        except Exception as e:
            logger.error(f"Error getting templates: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/templates/<template_id>', methods=['GET'])
    def get_template(template_id: str):
        """Get a specific template"""
        try:
            template = config_service.get_template_by_id(template_id)
            if template:
                return jsonify({
                    'success': True,
                    'template': template.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Template not found'}), 404
        except Exception as e:
            logger.error(f"Error getting template: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/templates', methods=['POST'])
    def create_template():
        """Create a new configuration template"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['name', 'template']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            if config_service.create_template(data):
                return jsonify({
                    'success': True,
                    'message': 'Template created successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to create template'}), 500

        except Exception as e:
            logger.error(f"Error creating template: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/templates/<template_id>', methods=['PUT'])
    def update_template(template_id: str):
        """Update an existing configuration template"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            if config_service.update_template(template_id, data):
                return jsonify({
                    'success': True,
                    'message': 'Template updated successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update template'}), 500

        except Exception as e:
            logger.error(f"Error updating template: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/templates/<template_id>', methods=['DELETE'])
    def delete_template(template_id: str):
        """Delete a configuration template"""
        try:
            if config_service.delete_template(template_id):
                return jsonify({
                    'success': True,
                    'message': 'Template deleted successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to delete template'}), 500

        except Exception as e:
            logger.error(f"Error deleting template: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/templates/<template_id>/generate', methods=['POST'])
    def generate_from_template(template_id: str):
        """Generate configuration from template"""
        try:
            data = request.get_json()
            variables = data.get('variables', {}) if data else {}

            config = config_service.generate_config_from_template(template_id, variables)
            if config:
                return jsonify({
                    'success': True,
                    'config': config
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to generate config from template'}), 500

        except Exception as e:
            logger.error(f"Error generating from template: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # Webhook Routes for Chat Commands

    @bp.route('/api/webhook', methods=['POST'])
    def webhook():
        """Handle incoming webhook messages for VPN commands"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Check if chat command service is available
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            # Parse webhook message
            message = WebhookMessage.from_dict(data)

            # Process message and get responses
            responses = plugin_instance.chat_command_service.process_webhook_message(message)

            if responses:
                # Send responses back to ShopeeAPI
                webhook_config = plugin_instance.chat_command_service.get_webhook_config()
                success = send_responses_to_shopee(responses, message, webhook_config)

                return jsonify({
                    'success': True,
                    'responses_sent': len(responses),
                    'shopee_delivery': success
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'No VPN commands found in message'
                })

        except Exception as e:
            logger.error(f"Error processing VPN webhook: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/webhook/config', methods=['GET'])
    def get_webhook_config():
        """Get webhook configuration"""
        try:
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            config = plugin_instance.chat_command_service.get_webhook_config()
            return jsonify({
                'success': True,
                'config': config.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting VPN webhook config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/webhook/config', methods=['PUT'])
    def update_webhook_config():
        """Update webhook configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            from .models import WebhookConfig
            config = WebhookConfig.from_dict(data)

            if plugin_instance.chat_command_service.update_webhook_config(config):
                return jsonify({
                    'success': True,
                    'message': 'Webhook configuration updated successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update webhook configuration'}), 500

        except Exception as e:
            logger.error(f"Error updating VPN webhook config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/commands', methods=['GET'])
    def get_commands():
        """Get all VPN chat commands"""
        try:
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            commands = plugin_instance.chat_command_service.get_all_commands()
            return jsonify({
                'success': True,
                'commands': {name: cmd.to_dict() for name, cmd in commands.items()}
            })
        except Exception as e:
            logger.error(f"Error getting VPN commands: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/commands', methods=['POST'])
    def create_command():
        """Create a new VPN chat command"""
        try:
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            command_key = data.get('command_key')
            if not command_key:
                return jsonify({'success': False, 'error': 'Command key is required'}), 400

            if plugin_instance.chat_command_service.create_command(command_key, data):
                # Re-register commands with chat commands plugin
                plugin_instance._register_chat_commands()

                return jsonify({
                    'success': True,
                    'message': 'Command created successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to create command'}), 500

        except Exception as e:
            logger.error(f"Error creating VPN command: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/commands/<command_key>', methods=['PUT'])
    def update_command(command_key):
        """Update a VPN chat command"""
        try:
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            if plugin_instance.chat_command_service.update_command(command_key, data):
                # Re-register commands with chat commands plugin
                plugin_instance._register_chat_commands()

                return jsonify({
                    'success': True,
                    'message': 'Command updated successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Command not found or failed to update'}), 404

        except Exception as e:
            logger.error(f"Error updating VPN command: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/commands/<command_key>', methods=['DELETE'])
    def delete_command(command_key):
        """Delete a VPN chat command"""
        try:
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            if plugin_instance.chat_command_service.delete_command(command_key):
                # Re-register commands with chat commands plugin
                plugin_instance._register_chat_commands()

                return jsonify({
                    'success': True,
                    'message': 'Command deleted successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Command not found'}), 404

        except Exception as e:
            logger.error(f"Error deleting VPN command: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/chat-commands/register', methods=['POST'])
    def register_chat_commands():
        """Register VPN commands with chat commands plugin"""
        try:
            plugin_instance._register_chat_commands()
            return jsonify({
                'success': True,
                'message': 'VPN commands registered with chat commands plugin'
            })
        except Exception as e:
            logger.error(f"Error registering chat commands: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/chat-commands/unregister', methods=['POST'])
    def unregister_chat_commands():
        """Unregister VPN commands from chat commands plugin"""
        try:
            plugin_instance._unregister_chat_commands()
            return jsonify({
                'success': True,
                'message': 'VPN commands unregistered from chat commands plugin'
            })
        except Exception as e:
            logger.error(f"Error unregistering chat commands: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/chat-commands/status', methods=['GET'])
    def get_chat_commands_status():
        """Get status of VPN command registration with chat commands plugin"""
        try:
            # Check if chat commands plugin is available
            chat_commands_plugin = plugin_instance.plugin_manager.get_plugin('chat_commands')
            if not chat_commands_plugin:
                return jsonify({
                    'success': True,
                    'registered': False,
                    'message': 'Chat commands plugin not available'
                })

            # Get current command configuration
            command_config = plugin_instance.chat_command_service.get_command_config()

            # Check if our command is registered
            if hasattr(chat_commands_plugin, 'command_service') and chat_commands_plugin.command_service:
                command = chat_commands_plugin.command_service.get_command(command_config.command_name)
                
                # Enhanced debugging information
                debug_info = {
                    'command_found': command is not None,
                    'has_plugin_source': hasattr(command, 'plugin_source') if command else False,
                    'plugin_source_value': getattr(command, 'plugin_source', None) if command else None,
                    'expected_plugin_name': plugin_instance.name,
                    'command_name_searched': command_config.command_name
                }
                
                is_registered = (command is not None and
                               hasattr(command, 'plugin_source') and
                               command.plugin_source == plugin_instance.name)

                return jsonify({
                    'success': True,
                    'registered': is_registered,
                    'command_name': command_config.command_name,
                    'message': f'VPN {command_config.command_name} command is registered' if is_registered else f'VPN {command_config.command_name} command is not registered',
                    'debug': debug_info
                })
            else:
                return jsonify({
                    'success': True,
                    'registered': False,
                    'message': 'Chat commands service not available'
                })

        except Exception as e:
            logger.error(f"Error getting chat commands status: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/command-config', methods=['GET'])
    def get_command_config():
        """Get VPN command configuration"""
        try:
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            config = plugin_instance.chat_command_service.get_command_config()
            return jsonify({
                'success': True,
                'config': config.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting command config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/command-config', methods=['PUT'])
    def update_command_config():
        """Update VPN command configuration"""
        try:
            if not hasattr(plugin_instance, 'chat_command_service'):
                return jsonify({'success': False, 'error': 'Chat command service not available'}), 500

            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Create config object
            from .models import VPNCommandConfig
            config = VPNCommandConfig.from_dict(data)

            # Retrieve Old Command Name
            old_command_config = plugin_instance.chat_command_service.get_command_config()
            old_command_name = old_command_config.command_name if old_command_config else None

            # Update config
            if plugin_instance.chat_command_service.update_command_config(config):
                new_command_name = config.command_name
                chat_commands_plugin = plugin_instance.plugin_manager.get_plugin('chat_commands')

                if chat_commands_plugin:
                    if old_command_name and old_command_name != new_command_name:
                        # Command name has actually changed
                        logger.info(f"Command name changed from '{old_command_name}' to '{new_command_name}'.")

                        # 1. Unregister Old Main Command
                        logger.info(f"Attempting to unregister old VPN command '{old_command_name}'.")
                        try:
                            chat_commands_plugin.unregister_external_command(old_command_name, plugin_instance.name)
                            logger.info(f"Successfully unregistered old VPN command '{old_command_name}'.")
                        except Exception as e:
                            logger.warning(f"Warning unregistering old command '{old_command_name}': {e}")

                        # 2. Unregister All Old Sub-Commands
                        old_sub_commands = [
                            f"{old_command_name}list",
                            f"{old_command_name}users", 
                            f"{old_command_name}del",
                            f"{old_command_name}renew",
                            f"{old_command_name}test",
                            f"{old_command_name}servers",
                            f"{old_command_name}help"
                        ]
                        
                        for old_sub_cmd in old_sub_commands:
                            try:
                                chat_commands_plugin.unregister_external_command(old_sub_cmd, plugin_instance.name)
                                logger.info(f"Successfully unregistered old sub-command '{old_sub_cmd}'.")
                            except Exception as e:
                                logger.warning(f"Warning unregistering old sub-command '{old_sub_cmd}': {e}")

                        # 3. Defensively Unregister New Command Names (to clear any stale state)
                        new_commands_to_unregister = [
                            new_command_name,
                            f"{new_command_name}list",
                            f"{new_command_name}users", 
                            f"{new_command_name}del",
                            f"{new_command_name}renew",
                            f"{new_command_name}test",
                            f"{new_command_name}servers",
                            f"{new_command_name}help"
                        ]
                        
                        for new_cmd in new_commands_to_unregister:
                            try:
                                chat_commands_plugin.unregister_external_command(new_cmd, plugin_instance.name)
                                logger.info(f"Defensive unregistration of '{new_cmd}' completed.")
                            except Exception as e:
                                logger.warning(f"Warning during defensive unregistration of '{new_cmd}': {e}")

                    # 4. Always (Re-)Register All Commands
                    # This uses the latest command_config (including new_command_name if it changed)
                    # from self.chat_command_service.get_command_config() inside _register_chat_commands
                    logger.info(f"Attempting to register/re-register VPN commands with new prefix '{new_command_name}'.")
                    plugin_instance._register_chat_commands()
                else:
                    logger.warning("Chat commands plugin not found. Cannot unregister or (re-)register VPN command with it.")

                return jsonify({
                    'success': True,
                    'message': 'Command configuration updated. Re-registration with chat_commands plugin attempted.',
                    'config': config.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update command configuration'}), 500

        except Exception as e:
            logger.error(f"Error updating command config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # ========== Telco Management API Routes ==========

    @bp.route('/api/telcos', methods=['GET'])
    def get_telcos():
        """Get all telcos and their plans"""
        try:
            telcos = config_service.get_all_telcos()
            return jsonify({
                'success': True,
                'telcos': {telco_id: telco.to_dict() for telco_id, telco in telcos.items()}
            })
        except Exception as e:
            logger.error(f"Error getting telcos: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos', methods=['POST'])
    def create_telco():
        """Create a new telco"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['name']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            if config_service.create_telco(data):
                return jsonify({
                    'success': True,
                    'message': 'Telco created successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to create telco'}), 500

        except Exception as e:
            logger.error(f"Error creating telco: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos/<telco_id>', methods=['GET'])
    def get_telco(telco_id: str):
        """Get a specific telco"""
        try:
            telco = config_service.get_telco_by_id(telco_id)
            if telco:
                return jsonify({
                    'success': True,
                    'telco': telco.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Telco not found'}), 404
        except Exception as e:
            logger.error(f"Error getting telco: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos/<telco_id>', methods=['PUT'])
    def update_telco(telco_id: str):
        """Update an existing telco"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            if config_service.update_telco(telco_id, data):
                return jsonify({
                    'success': True,
                    'message': 'Telco updated successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update telco'}), 500

        except Exception as e:
            logger.error(f"Error updating telco: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos/<telco_id>', methods=['DELETE'])
    def delete_telco(telco_id: str):
        """Delete a telco"""
        try:
            if config_service.delete_telco(telco_id):
                return jsonify({
                    'success': True,
                    'message': 'Telco deleted successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to delete telco'}), 500

        except Exception as e:
            logger.error(f"Error deleting telco: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # ========== Plan Management API Routes ==========

    @bp.route('/api/telcos/<telco_id>/plans', methods=['GET'])
    def get_telco_plans(telco_id: str):
        """Get all plans for a specific telco"""
        try:
            plans = config_service.get_telco_plans(telco_id)
            return jsonify({
                'success': True,
                'plans': {plan_id: plan.to_dict() for plan_id, plan in plans.items()}
            })
        except Exception as e:
            logger.error(f"Error getting telco plans: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos/<telco_id>/plans', methods=['POST'])
    def create_plan(telco_id: str):
        """Create a new plan for a telco"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['name', 'template']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            if config_service.create_plan(telco_id, data):
                return jsonify({
                    'success': True,
                    'message': 'Plan created successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to create plan'}), 500

        except Exception as e:
            logger.error(f"Error creating plan: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos/<telco_id>/plans/<plan_id>', methods=['GET'])
    def get_plan(telco_id: str, plan_id: str):
        """Get a specific plan"""
        try:
            plan = config_service.get_plan_by_id(telco_id, plan_id)
            if plan:
                return jsonify({
                    'success': True,
                    'plan': plan.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Plan not found'}), 404
        except Exception as e:
            logger.error(f"Error getting plan: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos/<telco_id>/plans/<plan_id>', methods=['PUT'])
    def update_plan(telco_id: str, plan_id: str):
        """Update an existing plan"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            if config_service.update_plan(telco_id, plan_id, data):
                return jsonify({
                    'success': True,
                    'message': 'Plan updated successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update plan'}), 500

        except Exception as e:
            logger.error(f"Error updating plan: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/telcos/<telco_id>/plans/<plan_id>', methods=['DELETE'])
    def delete_plan(telco_id: str, plan_id: str):
        """Delete a plan"""
        try:
            if config_service.delete_plan(telco_id, plan_id):
                return jsonify({
                    'success': True,
                    'message': 'Plan deleted successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to delete plan'}), 500

        except Exception as e:
            logger.error(f"Error deleting plan: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/command-prefix', methods=['GET'])
    def get_command_prefix():
        """Get the current command prefix from chat commands plugin"""
        try:
            chat_commands_plugin = plugin_instance.plugin_manager.get_plugin('chat_commands')
            if not chat_commands_plugin or not hasattr(chat_commands_plugin, 'command_service'):
                return jsonify({
                    'success': False,
                    'error': 'Chat commands plugin not available'
                }), 500

            prefix = chat_commands_plugin.command_service.get_command_prefix()
            return jsonify({
                'success': True,
                'prefix': prefix
            })
        except Exception as e:
            logger.error(f"Error getting command prefix: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # VPN Configuration Management Routes

    @bp.route('/debug')
    def vpn_debug_interface():
        """VPN Debug Interface for testing with fake orders"""
        try:
            return render_template('vpn_config_generator/debug_interface.html')
        except Exception as e:
            logger.error(f"Error loading VPN debug interface: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/order/verify', methods=['POST'])
    def verify_order():
        """Verify order status and create/retrieve user"""
        try:
            data = request.get_json()
            if not data or 'order_sn' not in data:
                return jsonify({'success': False, 'error': 'Order number is required'}), 400

            from .models import VPNOrderRequest
            order_request = VPNOrderRequest(
                order_sn=data['order_sn'],
                buyer_username=data.get('buyer_username')
            )

            # Process order using the order service
            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            result = plugin_instance.order_service.process_order(order_request)

            if result.success:
                # Log the order status for debugging
                logger.info(f"Order {result.order_sn} verification successful. Status: {result.status}")

                # Only ship orders that are in "To Ship" status
                # "Order Received" orders for VPN products don't need shipping since they're digital goods
                if result.status == 'To Ship':
                    try:
                        logger.info(f"Attempting to ship order {result.order_sn} with status 'To Ship'")
                        from services.order_service import ship_order
                        ship_result, ship_status = ship_order(result.order_sn)
                        if ship_status == 200:
                            logger.info(f"Order {result.order_sn} shipped successfully: {ship_result}")
                        else:
                            logger.warning(f"Failed to ship order {result.order_sn}: {ship_result}")
                    except Exception as ship_error:
                        logger.error(f"Error shipping order {result.order_sn}: {ship_error}")
                        # Don't fail the whole request if shipping fails
                elif result.status == 'Shipped':
                    logger.info(f"Order {result.order_sn} is already shipped - no action needed")
                elif result.status == 'Completed':
                    logger.info(f"Order {result.order_sn} is already completed - no action needed")
                elif result.status == 'Order Received':
                    logger.info(f"Order {result.order_sn} is in 'Order Received' status - no shipping required for VPN product")
                else:
                    logger.info(f"Order {result.order_sn} has status '{result.status}' - no shipping action taken")

                # Get user data to include configurations_generated count
                user = plugin_instance.order_service._users.get(result.user_uuid)
                configurations_generated = user.configurations_generated if user else 0

                return jsonify({
                    'success': True,
                    'user_uuid': result.user_uuid,
                    'order_sn': result.order_sn,
                    'buyer_username': result.buyer_username,
                    'sku': result.sku,
                    'var_sku': result.var_sku,
                    'is_repeat_customer': result.is_repeat_customer,
                    'assigned_telco': result.assigned_telco,
                    'allowed_telcos': result.allowed_telcos,
                    'is_restricted': result.is_restricted,
                    'message': result.message,
                    'order_status': result.status,  # Include order status in response
                    'configurations_generated': configurations_generated,  # Include config count
                    'order_claimed': user.order_claimed if user else False  # Include claimed status
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result.error,
                    'status': result.status
                }), 400

        except Exception as e:
            logger.error(f"Error verifying order: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/order/claim-details', methods=['POST'])
    def get_claim_details():
        """Get details for a claimed order, including existing configs and available plans."""
        try:
            data = request.get_json()
            if not data or 'user_uuid' not in data:
                return jsonify({'success': False, 'error': 'User UUID is required'}), 400

            user_uuid = data['user_uuid']

            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            # Get user and their existing configurations
            user = plugin_instance.order_service.get_user(user_uuid)
            if not user:
                return jsonify({'success': False, 'error': 'User not found'}), 404

            existing_configs = plugin_instance.order_service.get_user_configs_by_buyer_username(user.buyer_username)

            # Get the user's assigned telco (for claimed orders, they should be locked to one telco)
            current_telco_id = user.assigned_telco
            
            # If no assigned telco but has configs, get from first config
            if not current_telco_id and existing_configs:
                current_telco_id = existing_configs[0].get('telco')
                # Update user's assigned telco for consistency
                if current_telco_id:
                    user.assigned_telco = current_telco_id
                    plugin_instance.order_service.save_users()

            # Get available plans for the current telco
            available_plans = []
            if current_telco_id:
                telco = config_service.get_telco_by_id(current_telco_id)
                if telco:
                    available_plans = [plan.to_dict() for plan in telco.plans.values() if plan.enabled]

            return jsonify({
                'success': True,
                'existing_configs': existing_configs,
                'available_plans': available_plans,
                'current_telco_id': current_telco_id,
                'user_assigned_telco': user.assigned_telco
            })

        except Exception as e:
            logger.error(f"Error getting claim details: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/order/get-suggestions', methods=['POST'])
    def get_order_suggestions():
        """Get auto-suggested server and validity values based on SKU"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            sku = data.get('sku') or data.get('var_sku')
            if not sku:
                return jsonify({'success': False, 'error': 'SKU is required'}), 400

            # Import VPN strategy factory to get suggestions
            try:
                from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
                vpn_available = True
            except ImportError:
                vpn_available = False

            suggestions = {
                'sku': sku,
                'auto_server': None,
                'auto_validity_days': 30,
                'server_tags': [],
                'suggested_server_name': None,
                'vpn_available': vpn_available
            }

            if vpn_available:
                # Get server tags for this SKU
                server_tags = VPNStrategyFactory.get_server_tags_for_sku(sku)
                suggestions['server_tags'] = server_tags
                logger.info(f"Got server tags for SKU {sku}: {server_tags}")

                # Get validity days for this SKU
                validity_days = VPNStrategyFactory.get_validity_days_for_sku(sku)
                suggestions['auto_validity_days'] = validity_days
                logger.info(f"Got validity days for SKU {sku}: {validity_days}")

                # Get suggested server based on SKU
                config_service = plugin_instance.config_service
                matching_servers = config_service._get_servers_by_tags(server_tags)
                
                if matching_servers:
                    # Select the first available server
                    selected_server = matching_servers[0]
                    suggestions['auto_server'] = str(selected_server.get('id'))
                    suggestions['suggested_server_name'] = selected_server.get('name', 'Unknown')
                    
                    logger.info(f"Auto-selected server {suggestions['auto_server']} ({suggestions['suggested_server_name']}) for SKU {sku}")
                else:
                    logger.warning(f"No servers found matching tags {server_tags} for SKU {sku}")
                    # Fallback to auto selection
                    suggestions['auto_server'] = 'auto'
                    suggestions['suggested_server_name'] = 'Auto-selected by system'
            else:
                # Fallback when VPN plugin not available
                suggestions['auto_server'] = 'auto'
                suggestions['suggested_server_name'] = 'Auto-selected by system'

            return jsonify({
                'success': True,
                'suggestions': suggestions
            })

        except Exception as e:
            logger.error(f"Error getting order suggestions: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/sku/server-tags', methods=['POST'])
    def get_server_tags_by_sku():
        """Get server tags for a specific SKU"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            sku = data.get('sku')
            if not sku:
                return jsonify({'success': False, 'error': 'SKU is required'}), 400

            # Check if VPN strategy factory is available
            try:
                from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
                server_tags = VPNStrategyFactory.get_server_tags_for_sku(sku)

                return jsonify({
                    'success': True,
                    'sku': sku,
                    'tags': server_tags
                })

            except ImportError:
                logger.warning("VPN strategy factory not available")
                return jsonify({
                    'success': False,
                    'error': 'VPN strategy factory not available'
                }), 503

        except Exception as e:
            logger.error(f"Error getting server tags for SKU {sku}: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/servers/by-tags', methods=['POST'])
    def get_servers_by_tags():
        """Get servers that match specific tags"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            tags = data.get('tags', [])
            if not tags:
                return jsonify({'success': False, 'error': 'Tags are required'}), 400

            # Get servers matching the provided tags
            config_service = plugin_instance.config_service
            matching_servers = config_service._get_servers_by_tags(tags)
            
            servers = []
            for server in matching_servers:
                servers.append({
                    'id': server.get('id'),
                    'name': server.get('name', 'Unknown'),
                    'location': server.get('description', 'Unknown'),  # Use description as location
                    'tags': server.get('tags', [])
                })

            return jsonify({
                'success': True,
                'servers': servers,
                'total': len(servers)
            })

        except Exception as e:
            logger.error(f"Error getting servers by tags: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/order/generate-config', methods=['POST'])
    def generate_order_config():
        """Generate or renew VPN configuration for a verified order."""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            action = data.get('action', 'create')  # Default to 'create'

            if action == 'renew':
                # Handle renewal logic inline
                required_fields = ['client_id', 'user_uuid', 'order_sn']
                for field in required_fields:
                    if field not in data or data[field] is None:
                        return jsonify({'success': False, 'error': f'Missing or null field: {field}'}), 400

                # Get user first to resolve client_id
                user = plugin_instance.order_service.get_user(data['user_uuid'])
                if not user:
                    return jsonify({'success': False, 'error': 'User not found'}), 404

                # Re-validate order status before renewal to prevent refunded orders
                try:
                    from services.order_service import get_order_details
                    order_details, status_code = get_order_details(user.order_sn)
                    
                    if status_code == 200 and order_details:
                        extracted_data = plugin_instance.order_service._extract_order_data(order_details)
                        current_status = extracted_data.get('status', '').lower()
                        
                        if current_status in ['refunded', 'refund', 'cancelled']:
                            logger.warning(f"Blocking config renewal for refunded/cancelled order {user.order_sn}")
                            return jsonify({
                                'success': False, 
                                'error': f'Cannot renew VPN configuration. Order {user.order_sn} status is: {current_status}. Please contact support if you believe this is an error.'
                            }), 403
                        
                        logger.info(f"Order {user.order_sn} status validation passed for renewal: {current_status}")
                    else:
                        logger.warning(f"Could not verify current status for order {user.order_sn}, allowing renewal")
                        
                except ImportError:
                    logger.warning("ShopeeAPI not available for renewal status validation, allowing renewal")
                except Exception as e:
                    logger.error(f"Error validating order status for renewal {user.order_sn}: {e}, allowing renewal")

                # Resolve client_id to numeric format
                try:
                    client_id = resolve_client_id(data['client_id'], user)
                except ValueError as e:
                    return jsonify({'success': False, 'error': str(e)}), 400

                # Security Check: Verify SKU allows renewing to this server's tag
                can_renew, error_message = plugin_instance.order_service.verify_renewal_permission(user, client_id)
                if not can_renew:
                    return jsonify({'success': False, 'error': error_message}), 403

                renewal_days = plugin_instance.order_service.get_renewal_days_from_sku(user.var_sku)

                # Create renewal request
                from .models import VPNRenewalRequest
                renewal_request = VPNRenewalRequest(
                    client_id=client_id,
                    user_uuid=data['user_uuid'],
                    order_sn=data['order_sn'],
                    days=renewal_days
                )

                # Process renewal
                result = plugin_instance.order_service.renew_vpn_config(renewal_request)

                if result and result.get('success'):
                    # Update user's record
                    if not user.order_claimed:
                        user.order_claimed = True
                        logger.info(f"Marked order as claimed for user {user.uuid} after renewing configuration")

                    # Add renewal record
                    if not hasattr(user, 'renewals'):
                        user.renewals = []
                    user.renewals.append({
                        'order_sn': data['order_sn'],
                        'client_id': client_id,
                        'days_extended': renewal_days,
                        'new_expiry_date': result.get('new_expiry_date'),
                        'renewed_at': datetime.now().isoformat()
                    })
                    plugin_instance.order_service.save_users()

                    return jsonify({
                        'success': True,
                        'message': f'Configuration renewed successfully for {renewal_days} days',
                        'new_expiry_date': result.get('new_expiry_date'),
                        'client_id': str(client_id),
                        'days_extended': renewal_days
                    })
                else:
                    error_msg = result.get('error', 'Unknown error') if result else 'Renewal service unavailable'
                    return jsonify({'success': False, 'error': f'Failed to renew configuration: {error_msg}'}), 500

            # --- Existing logic for creating a new config ---
            required_fields = ['user_uuid', 'telco', 'plan']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            user_uuid = data['user_uuid']
            telco = data['telco']

            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            user = plugin_instance.order_service.get_user(user_uuid)
            if not user:
                return jsonify({'success': False, 'error': 'User not found'}), 404

            # Re-validate order status before generating config to prevent refunded orders
            try:
                from services.order_service import get_order_details
                order_details, status_code = get_order_details(user.order_sn)
                
                if status_code == 200 and order_details:
                    extracted_data = plugin_instance.order_service._extract_order_data(order_details)
                    current_status = extracted_data.get('status', '').lower()
                    
                    if current_status in ['refunded', 'refund', 'cancelled']:
                        logger.warning(f"Blocking config generation for refunded/cancelled order {user.order_sn}")
                        return jsonify({
                            'success': False, 
                            'error': f'Cannot generate VPN configuration. Order {user.order_sn} status is: {current_status}. Please contact support if you believe this is an error.'
                        }), 403
                    
                    logger.info(f"Order {user.order_sn} status validation passed: {current_status}")
                else:
                    logger.warning(f"Could not verify current status for order {user.order_sn}, allowing config generation")
                    
            except ImportError:
                logger.warning("ShopeeAPI not available for order status validation, allowing config generation")
            except Exception as e:
                logger.error(f"Error validating order status for {user.order_sn}: {e}, allowing config generation")

            # For claimed orders, enforce telco restrictions
            if user.order_claimed:
                if user.assigned_telco and user.assigned_telco != telco:
                    return jsonify({
                        'success': False, 
                        'error': f'This order is locked to {user.assigned_telco} telco. You can only change plans within this telco.'
                    }), 403
                
                # If no assigned telco but order is claimed, this shouldn't happen but handle gracefully
                if not user.assigned_telco:
                    logger.warning(f"Claimed order {user.order_sn} has no assigned telco, allowing telco selection")
            else:
                # For new orders, check normal access permissions
                can_access, access_message = plugin_instance.order_service.can_user_access_telco(user_uuid, telco)
                if not can_access:
                    return jsonify({'success': False, 'error': access_message}), 403

            # Get server and validity from request or SKU
            selected_server = data.get('server', 'auto')  # This is for template generation only
            selected_days = data.get('days')

            # Get SKU from request data or user object (prioritize request data)
            sku_to_use = data.get('sku') or data.get('var_sku') or user.var_sku

            # Get auto-suggestions from SKU
            try:
                from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
                server_tags = VPNStrategyFactory.get_server_tags_for_sku(sku_to_use)
                auto_validity = VPNStrategyFactory.get_validity_days_for_sku(sku_to_use)
                matching_servers = config_service._get_servers_by_tags(server_tags)

                # Update user's var_sku if it was provided in request and different from stored value
                if sku_to_use and sku_to_use != user.var_sku:
                    logger.info(f"Updating user {user.buyer_username} var_sku from '{user.var_sku}' to '{sku_to_use}'")
                    user.var_sku = sku_to_use
                    plugin_instance.order_service.save_users()

            except ImportError:
                auto_validity = 30
                matching_servers = []

            # Use provided values or fallback to auto-suggestions
            final_days = selected_days if selected_days else str(auto_validity)

            logger.info(f"Config generation - User: {user.buyer_username}, Selected Server (for template): {selected_server}, Days: {final_days}, Telco: {telco}, Plan: {data['plan']}")
            logger.info(f"Available servers for SKU {sku_to_use}: {[s.get('name', s.get('id')) for s in matching_servers]}")

            # Check for force_multi_server flag
            force_multi_server = data.get('force_multi_server', False)
            if force_multi_server:
                logger.info(f"🚀 Force multi-server creation requested for user {user.buyer_username}")

            # UUID Creation Logic: Create UUIDs on ALL servers mapped to the SKU
            # But use selected server for template generation
            all_configs = []
            template_config = None
            
            if matching_servers:
                # Use parallel processing for multiple servers
                import concurrent.futures
                import threading
                from .models import VPNConfigRequest

                logger.info(f"🚀 Starting parallel UUID creation on {len(matching_servers)} servers")

                def create_config_for_server(server):
                    """Create config for a single server"""
                    server_id = str(server.get('id'))
                    server_name = server.get('name', f'Server {server_id}')

                    try:
                        config_request = VPNConfigRequest(
                            server=server_id,
                            days=final_days,
                            telco=telco,
                            plan=data['plan'],
                            username=user.buyer_username,
                            sku=user.sku,
                            var_sku=sku_to_use
                        )

                        logger.info(f"⚡ Creating UUID on server {server_name} (ID: {server_id})")
                        result = config_service.generate_config(config_request)

                        if result.success:
                            config_info = {
                                'client_id': result.client_id,
                                'numeric_id': result.numeric_id,
                                'telco': telco,
                                'plan': data['plan'],
                                'server_id': server_id,
                                'server_name': server_name,
                                'created_date': result.created_date,
                                'expired_date': result.expired_date,
                                'active': True,
                                'config_text': str(result.config),
                                'order_sn': data.get('order_sn', user.order_sn),
                            }
                            logger.info(f"✅ Successfully created UUID {result.client_id} on server {server_name}")
                            return config_info
                        else:
                            logger.error(f"❌ Failed to create UUID on server {server_name}: {result.error}")
                            return None

                    except Exception as e:
                        logger.error(f"❌ Exception creating UUID on server {server_name}: {e}")
                        return None

                # Execute parallel creation with a reasonable thread pool size
                max_workers = min(len(matching_servers), 5)  # Limit to 5 concurrent requests
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # Submit all server creation tasks
                    future_to_server = {executor.submit(create_config_for_server, server): server for server in matching_servers}

                    # Collect results as they complete
                    for future in concurrent.futures.as_completed(future_to_server):
                        server = future_to_server[future]
                        try:
                            config_info = future.result()
                            if config_info:
                                all_configs.append(config_info)
                                plugin_instance.order_service.add_generated_config_to_user(user_uuid, config_info)

                                # If this is the selected server (for template), save it as primary
                                if selected_server != 'auto' and config_info['server_id'] == selected_server:
                                    template_config = config_info
                                elif selected_server == 'auto' and template_config is None:
                                    # Use first successful config as template if auto-selected
                                    template_config = config_info

                        except Exception as e:
                            server_name = server.get('name', f'Server {server.get("id")}')
                            logger.error(f"❌ Exception processing result for server {server_name}: {e}")

                logger.info(f"🏁 Parallel UUID creation completed: {len(all_configs)}/{len(matching_servers)} servers successful")
                
                if all_configs:
                    # Use template config or first config for response
                    primary_config = template_config if template_config else all_configs[0]
                    
                    # Update user records
                    plugin_instance.order_service.update_user_telco_selection(user_uuid, telco)
                    user.configurations_generated += len(all_configs)
                    user.last_access = datetime.now().isoformat()
                    if not user.order_claimed:
                        user.order_claimed = True
                    plugin_instance.order_service.save_users()

                    logger.info(f"✅ Created {len(all_configs)} configurations across servers: {[c['server_name'] for c in all_configs]}")
                    logger.info(f"📋 Template config from server: {primary_config['server_name']}")

                    if force_multi_server:
                        logger.info(f"🚀 Force multi-server creation completed successfully - {len(all_configs)} servers configured")

                    return jsonify({
                        'success': True,
                        'config': primary_config['config_text'],
                        'created_date': primary_config['created_date'],
                        'expired_date': primary_config['expired_date'],
                        'server_name': primary_config['server_name'],
                        'client_id': primary_config['client_id'],
                        'numeric_id': primary_config['numeric_id'],
                        'uuid': primary_config['client_id'],  # Add UUID field for consistency
                        'message': f'UUID created on {len(all_configs)} servers. Template from {primary_config["server_name"]}.',
                        'total_configs_created': len(all_configs),
                        'servers_used': [c['server_name'] for c in all_configs],
                        'servers_configured': len(all_configs)  # Add for backward compatibility
                    })
                else:
                    return jsonify({'success': False, 'error': 'Failed to create configurations on any server'}), 500
            else:
                # Fallback: No matching servers found, use single server approach
                logger.warning(f"❌ No matching servers found for SKU {sku_to_use}, using fallback single server approach")
                logger.warning(f"Server tags attempted: {server_tags if 'server_tags' in locals() else 'None'}")
                if force_multi_server:
                    logger.error(f"🚨 Force multi-server creation requested but no matching servers found for SKU {sku_to_use}")

                final_server = selected_server if selected_server != 'auto' else 'auto'

                from .models import VPNConfigRequest
                config_request = VPNConfigRequest(
                    server=final_server,
                    days=final_days,
                    telco=telco,
                    plan=data['plan'],
                    username=user.buyer_username,
                    sku=user.sku,
                    var_sku=sku_to_use
                )

                result = config_service.generate_config(config_request)

                if result.success:
                    # Get server name for display
                    server_name = 'N/A'
                    if hasattr(result, 'server_name') and result.server_name:
                        server_name = result.server_name
                    
                    config_info = {
                        'client_id': result.client_id,
                        'numeric_id': result.numeric_id,
                        'telco': telco,
                        'plan': data['plan'],
                        'server_id': final_server,
                        'server_name': server_name,
                        'created_date': result.created_date,
                        'expired_date': result.expired_date,
                        'active': True,
                        'config_text': str(result.config),
                        'order_sn': data.get('order_sn', user.order_sn),  # Add this line
                    }
                    plugin_instance.order_service.add_generated_config_to_user(user_uuid, config_info)
                    plugin_instance.order_service.update_user_telco_selection(user_uuid, telco)
                    user.configurations_generated += 1
                    user.last_access = datetime.now().isoformat()
                    if not user.order_claimed:
                        user.order_claimed = True
                    plugin_instance.order_service.save_users()

                    return jsonify({
                        'success': True,
                        'config': result.config,
                        'created_date': result.created_date,
                        'expired_date': result.expired_date,
                        'server_name': server_name,
                        'client_id': result.client_id,
                        'numeric_id': result.numeric_id,
                        'message': f'Fallback: Single configuration created on {server_name}'
                    })
                else:
                    return jsonify({'success': False, 'error': result.error}), 400

        except Exception as e:
            logger.error(f"Error in generate_order_config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/order/user-configs', methods=['POST'])
    def get_user_configs():
        """Get all generated configurations for a user by buyer username"""
        try:
            data = request.get_json()
            if not data or 'buyer_username' not in data:
                return jsonify({'success': False, 'error': 'Buyer username is required'}), 400

            buyer_username = data['buyer_username']
            
            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            # Get all configs for this buyer username
            configs = plugin_instance.order_service.get_user_configs_by_buyer_username(buyer_username)
            
            # Enhance configs with server information
            enhanced_configs = []
            for config in configs:
                enhanced_config = config.copy()
                
                # Get server name if server_id is available
                server_id = config.get('server_id')
                if server_id:
                    try:
                        # Get VPN plugin to fetch server info
                        vpn_plugin = plugin_instance.plugin_manager.get_plugin('vpn')
                        if vpn_plugin and hasattr(vpn_plugin, 'api_service'):
                            servers = vpn_plugin.api_service.get_servers()
                            if servers:
                                for server in servers:
                                    if str(server.get('id')) == str(server_id):
                                        enhanced_config['server_name'] = server.get('name', f'Server {server_id}')
                                        enhanced_config['server_location'] = server.get('location', 'Unknown')
                                        break
                                else:
                                    enhanced_config['server_name'] = f'Server {server_id}'
                            else:
                                enhanced_config['server_name'] = f'Server {server_id}'
                        else:
                            enhanced_config['server_name'] = f'Server {server_id}'
                    except Exception as e:
                        logger.warning(f"Could not get server info for server_id {server_id}: {e}")
                        enhanced_config['server_name'] = f'Server {server_id}'
                
                # Calculate validity status
                try:
                    if config.get('expired_date'):
                        exp_date = datetime.strptime(config['expired_date'], '%d-%m-%Y')
                        now = datetime.now()
                        days_remaining = (exp_date - now).days
                        
                        if days_remaining > 0:
                            enhanced_config['validity_status'] = f"{days_remaining} days remaining"
                            enhanced_config['is_expired'] = False
                        elif days_remaining == 0:
                            enhanced_config['validity_status'] = "Expires today"
                            enhanced_config['is_expired'] = False
                        else:
                            enhanced_config['validity_status'] = f"Expired {abs(days_remaining)} days ago"
                            enhanced_config['is_expired'] = True
                    else:
                        enhanced_config['validity_status'] = "Unknown"
                        enhanced_config['is_expired'] = False
                except Exception as e:
                    logger.warning(f"Could not calculate validity status: {e}")
                    enhanced_config['validity_status'] = "Unknown"
                    enhanced_config['is_expired'] = False
                
                enhanced_configs.append(enhanced_config)
            
            return jsonify({
                'success': True,
                'configs': enhanced_configs,
                'total_configs': len(enhanced_configs)
            })

        except Exception as e:
            logger.error(f"Error getting user configs: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/order/renew-config', methods=['POST'])
    def renew_config():
        """Renew VPN configuration by extending expiry date"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            required_fields = ['client_id', 'user_uuid', 'order_sn']
            for field in required_fields:
                if field not in data or data[field] is None:
                    return jsonify({'success': False, 'error': f'Missing or null field: {field}'}), 400

            # Get user first to resolve client_id
            user = plugin_instance.order_service.get_user(data['user_uuid'])
            if not user:
                return jsonify({'success': False, 'error': 'User not found'}), 404

            # Resolve client_id to numeric format
            try:
                client_id = resolve_client_id(data['client_id'], user)
            except ValueError as e:
                return jsonify({'success': False, 'error': str(e)}), 400
            
            user_uuid = data['user_uuid']
            order_sn = data['order_sn']
            
            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            user = plugin_instance.order_service.get_user(user_uuid)
            if not user:
                return jsonify({'success': False, 'error': 'User not found'}), 404

            # Security Check: Verify SKU allows renewing to this server's tag
            can_renew, error_message = plugin_instance.order_service.verify_renewal_permission(user, client_id)
            if not can_renew:
                return jsonify({'success': False, 'error': error_message}), 403

            renewal_days = plugin_instance.order_service.get_renewal_days_from_sku(user.var_sku)
            
            from .models import VPNRenewalRequest
            renewal_request = VPNRenewalRequest(client_id=client_id, days=renewal_days, user_uuid=user_uuid, order_sn=order_sn)

            if not hasattr(plugin_instance.order_service, 'plugin_manager'):
                plugin_instance.order_service.plugin_manager = plugin_instance.plugin_manager

            result = plugin_instance.order_service.renew_vpn_config(renewal_request)

            if result.success:
                return jsonify({
                    'success': True,
                    'client_id': result.client_id,
                    'new_expiry_date': result.new_expiry_date,
                    'days_extended': result.days_extended,
                    'message': result.message
                })
            else:
                return jsonify({'success': False, 'error': result.error}), 400

        except Exception as e:
            logger.error(f"Error renewing config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/order/change-config', methods=['POST'])
    def change_config():
        """Change VPN configuration plan within the same telco"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['client_id', 'user_uuid', 'order_sn', 'telco', 'plan']
            for field in required_fields:
                if field not in data or data[field] is None:
                    return jsonify({'success': False, 'error': f'Missing or null field: {field}'}), 400

            user_uuid = data['user_uuid']
            order_sn = data['order_sn']
            telco = data['telco']
            new_plan = data['plan']

            logger.info(f"Change config request - UUID: {user_uuid}, Order: {order_sn}, Telco: {telco}, Plan: {new_plan}")

            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            # Get user to verify permissions
            user = plugin_instance.order_service.get_user(user_uuid)
            if not user:
                logger.error(f"User not found for UUID: {user_uuid}")
                # Try to find user by order_sn as fallback
                all_users = plugin_instance.order_service._users
                logger.info(f"Available users: {list(all_users.keys())}")
                
                # Find user by order_sn
                user_by_order = None
                for uuid, user_data in all_users.items():
                    if user_data.order_sn == order_sn:
                        user_by_order = user_data
                        logger.info(f"Found user by order_sn: {uuid}")
                        break
                
                if user_by_order:
                    user = user_by_order
                    user_uuid = user.uuid  # Update to correct UUID
                else:
                    return jsonify({'success': False, 'error': f'User not found for UUID: {user_uuid} or Order: {order_sn}'}), 404

            # Resolve client_id to numeric format
            try:
                client_id = resolve_client_id(data['client_id'], user)
            except ValueError as e:
                return jsonify({'success': False, 'error': str(e)}), 400

            # Verify user owns this order
            if user.order_sn != order_sn:
                return jsonify({'success': False, 'error': 'Order does not belong to this user'}), 403

            # Check if user can access the telco
            if user.is_restricted and user.assigned_telco and user.assigned_telco != telco:
                return jsonify({'success': False, 'error': f'User is restricted to {user.assigned_telco} telco'}), 403

            # Find the configuration to change
            config_to_change = None
            for config in user.generated_configs:
                if (config.get('client_id') == str(client_id) or
                    config.get('numeric_id') == client_id):
                    config_to_change = config
                    break

            if not config_to_change:
                return jsonify({'success': False, 'error': 'Configuration not found'}), 404

            # Verify the configuration belongs to the specified telco
            if config_to_change.get('telco') != telco:
                return jsonify({'success': False, 'error': 'Configuration does not belong to the specified telco'}), 400

            # Get telco configuration to validate the new plan
            telcos = config_service.get_all_telcos()
            if telco not in telcos:
                return jsonify({'success': False, 'error': f'Telco {telco} not found'}), 400

            telco_config = telcos[telco]
            if new_plan not in telco_config.plans:
                return jsonify({'success': False, 'error': f'Plan {new_plan} not available for {telco}'}), 400

            # Create a new configuration request with the new plan
            from .models import VPNConfigRequest
            config_request = VPNConfigRequest(
                server=config_to_change.get('server_id', '1'),  # Use existing server
                days=plugin_instance.order_service.get_renewal_days_from_sku(user.var_sku),
                telco=telco,
                plan=new_plan,
                username=user.buyer_username
            )

            # Generate new configuration
            result = config_service.generate_config(config_request)

            if result.success:
                # Update the existing configuration record
                config_to_change.update({
                    'plan': new_plan,
                    'config_text': result.config,
                    'created_date': result.created_date,
                    'expired_date': result.expired_date,
                    'last_modified': datetime.now().isoformat(),
                    'change_history': config_to_change.get('change_history', []) + [{
                        'from_plan': config_to_change.get('plan'),
                        'to_plan': new_plan,
                        'changed_at': datetime.now().isoformat()
                    }]
                })

                # Save updated user data
                plugin_instance.order_service.save_users()

                return jsonify({
                    'success': True,
                    'client_id': client_id,
                    'new_plan': new_plan,
                    'config': result.config,
                    'created_date': result.created_date,
                    'expired_date': result.expired_date,
                    'message': f'Configuration changed successfully to {new_plan} plan'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f'Failed to generate new configuration: {result.error}'
                }), 400

        except Exception as e:
            logger.error(f"Error changing config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/uuid-config')
    def uuid_config_page():
        """UUID-based configuration page for returning customers"""
        try:
            config_service = plugin_instance.config_service
            telcos = config_service.get_all_telcos()
            
            return render_template('vpn_config_generator/uuid_config.html',
                                 telcos=telcos)
        except Exception as e:
            logger.error(f"Error loading UUID config page: {e}")
            return f"Error loading page: {str(e)}", 500

    @bp.route('/api/uuid/generate-config', methods=['POST'])
    def generate_config_by_uuid():
        """Generate VPN configuration using existing UUID"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['user_uuid', 'telco', 'plan']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            user_uuid = data['user_uuid']
            telco = data['telco']

            # Check if user can access the selected telco
            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            can_access, access_message = plugin_instance.order_service.can_user_access_telco(user_uuid, telco)
            if not can_access:
                return jsonify({'success': False, 'error': access_message}), 403

            # Get user information to include SKU data
            user = plugin_instance.order_service.get_user(user_uuid)
            if not user:
                return jsonify({'success': False, 'error': 'User not found'}), 404

            # Auto-populate server and validity based on user's SKU
            try:
                from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
                
                # Get auto-suggestions for the user's SKU
                auto_server = 'auto'
                auto_validity = 30
                
                if user.var_sku:
                    # Get server tags and find matching server
                    server_tags = VPNStrategyFactory.get_server_tags_for_sku(user.var_sku)
                    matching_servers = config_service._get_servers_by_tags(server_tags)
                    
                    if matching_servers:
                        selected_server = matching_servers[0]
                        auto_server = str(selected_server.get('id'))
                    
                    # Get validity days
                    auto_validity = VPNStrategyFactory.get_validity_days_for_sku(user.var_sku)
                    logger.info(f"Auto-populated validity for user {user_uuid} with SKU {user.var_sku}: {auto_validity} days")
                    
            except ImportError:
                logger.warning("VPN strategy factory not available, using defaults")

            # Create VPN config request with auto-populated values
            from .models import VPNConfigRequest
            config_request = VPNConfigRequest(
                server=auto_server,
                days=str(auto_validity),
                telco=telco,
                plan=data['plan'],
                username=user.buyer_username,  # Use actual buyer username from order
                sku=user.sku,
                var_sku=user.var_sku
            )

            # Generate configuration
            result = config_service.generate_config(config_request)

            if result.success:
                # Update user's configuration count and add to generated configs
                user.configurations_generated += 1
                user.last_access = datetime.now().isoformat()

                # Add configuration to user's record
                config_data = {
                    'client_id': result.client_id,
                    'numeric_id': result.numeric_id,
                    'server_id': data.get('server'),
                    'telco': data['telco'],
                    'plan': data['plan'],
                    'created_date': result.created_date,
                    'expired_date': result.expired_date,
                    'order_sn': user.order_sn,
                    'generated_at': datetime.now().isoformat()
                }

                if not user.generated_configs:
                    user.generated_configs = []
                user.generated_configs.append(config_data)

                # Mark order as claimed when configuration is generated
                if not user.order_claimed:
                    user.order_claimed = True
                    logger.info(f"Marked order as claimed for user {user.uuid} after generating configuration via UUID")

                plugin_instance.order_service.save_users()

                return jsonify({
                    'success': True,
                    'config': result.config,
                    'created_date': result.created_date,
                    'expired_date': result.expired_date,
                    'message': result.message,
                    'user_info': {
                        'uuid': user.uuid,
                        'order_sn': user.order_sn,
                        'buyer_username': user.buyer_username,
                        'sku': user.sku,
                        'var_sku': user.var_sku,
                        'configurations_generated': user.configurations_generated
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result.error
                }), 400

        except Exception as e:
            logger.error(f"Error generating config by UUID: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/debug/create-test-order', methods=['POST'])
    def create_test_order():
        """Create a test order for VPN configuration testing"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Generate test order data
            import random
            import string
            from datetime import datetime

            order_sn = data.get('order_sn') or f"VPN{''.join(random.choices(string.ascii_uppercase + string.digits, k=8))}"
            var_sku = data.get('var_sku', 'vpn_service_30')
            buyer_username = data.get('buyer_username', 'test_user')
            restriction_type = data.get('restriction_type', 'none')  # 'none', 'my_restricted', 'custom'

            # Adjust var_sku based on restriction type
            if restriction_type == 'my_restricted':
                var_sku = f"my_{var_sku}" if not var_sku.startswith('my_') else var_sku

            # Create fake order structure
            fake_order = {
                "order_sn": order_sn,
                "var_sku": var_sku,
                "buyer_username": buyer_username,
                "status": "To Ship",
                "created_at": datetime.now().isoformat(),
                "is_fake_order": True,
                "order_type": "vpn_test_order",
                "description": f"VPN test order - {restriction_type} restriction"
            }

            # Load existing fake orders
            import os
            import json
            fake_orders_file = os.path.join(os.path.dirname(os.path.dirname(plugin_instance.plugin_dir)), 'configs', 'data', 'manual_orders.json')

            fake_orders = []
            if os.path.exists(fake_orders_file):
                try:
                    with open(fake_orders_file, 'r', encoding='utf-8') as f:
                        fake_orders = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not load existing fake orders: {e}")

            # Add new fake order
            fake_orders.append(fake_order)

            # Save back to file
            os.makedirs(os.path.dirname(fake_orders_file), exist_ok=True)
            with open(fake_orders_file, 'w', encoding='utf-8') as f:
                json.dump(fake_orders, f, indent=2, ensure_ascii=False)

            logger.info(f"Created VPN test order: {order_sn} with var_sku: {var_sku}")

            return jsonify({
                'success': True,
                'order_sn': order_sn,
                'var_sku': var_sku,
                'buyer_username': buyer_username,
                'restriction_type': restriction_type,
                'test_url': f'/vpn-config-generator/order-config?order_sn={order_sn}',
                'message': f'Test order {order_sn} created successfully'
            })

        except Exception as e:
            logger.error(f"Error creating test order: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/debug/test-scenarios')
    def get_test_scenarios():
        """Get predefined test scenarios for VPN configuration testing"""
        try:
            scenarios = [
                {
                    'name': 'Regular User - No Restrictions',
                    'description': 'Standard user with access to all telcos and plans',
                    'var_sku': 'vpn_service_30',
                    'restriction_type': 'none',
                    'expected_behavior': 'Can select any telco and plan, no restrictions applied'
                },
                {
                    'name': 'Restricted User - My Prefix (New)',
                    'description': 'New user with my_ prefixed SKU, will be locked after first selection',
                    'var_sku': 'my_highspeed_15',
                    'restriction_type': 'my_restricted',
                    'expected_behavior': 'Can select any telco initially, locked to first selection'
                },
                {
                    'name': 'Restricted User - My Premium',
                    'description': 'Premium user with my_ prefix and extended validity',
                    'var_sku': 'my_premium_30',
                    'restriction_type': 'my_restricted',
                    'expected_behavior': 'Single telco lock after first selection'
                },
                {
                    'name': 'Business User - Standard',
                    'description': 'Business account with standard access',
                    'var_sku': 'business_vpn_30',
                    'restriction_type': 'none',
                    'expected_behavior': 'Full access to all services'
                },
                {
                    'name': 'Trial User - Limited',
                    'description': 'Trial account with potential restrictions',
                    'var_sku': 'trial_vpn_7',
                    'restriction_type': 'none',
                    'expected_behavior': 'Standard access for trial period'
                }
            ]

            return jsonify({
                'success': True,
                'scenarios': scenarios,
                'total_scenarios': len(scenarios)
            })

        except Exception as e:
            logger.error(f"Error getting test scenarios: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/sku-restrictions')
    def sku_restrictions():
        """Render SKU restrictions management page"""
        try:
            # TODO: Add proper access control when PluginConfig is implemented
            return render_template('vpn_config_generator/sku_restrictions.html')
        except Exception as e:
            current_app.logger.error(f"Error rendering SKU restrictions page: {str(e)}")
            flash('Error loading SKU restrictions management', 'error')
            return redirect(url_for('index'))

    @bp.route('/api/sku-restrictions', methods=['GET'])
    def get_sku_restrictions():
        """Get all SKU restrictions"""
        try:
            restrictions = SKURestrictionService.get_all_restrictions()
            return jsonify({
                'success': True,
                'restrictions': [r.to_dict() for r in restrictions]
            })
        except Exception as e:
            current_app.logger.error(f"Error getting SKU restrictions: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @bp.route('/api/sku-restrictions', methods=['POST'])
    def create_sku_restriction():
        """Create a new SKU restriction"""
        try:
            data = request.get_json()
            
            # Validate required fields
            required_fields = ['sku_pattern', 'restriction_type', 'description']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'success': False,
                        'error': f'Missing required field: {field}'
                    }), 400
            
            # Create new restriction
            new_restriction = SKURestriction(
                sku_pattern=data['sku_pattern'],
                restriction_type=data['restriction_type'],
                description=data['description'],
                enabled=data.get('enabled', True),
                allowed_telcos=data.get('allowed_telcos', []),
                max_configurations=data.get('max_configurations', -1),
                single_telco_only=data.get('single_telco_only', False)
            )
            
            if SKURestrictionService.add_restriction(new_restriction):
                return jsonify({
                    'success': True,
                    'message': 'SKU restriction created successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to create SKU restriction'
                }), 500
                
        except Exception as e:
            current_app.logger.error(f"Error creating SKU restriction: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @bp.route('/api/sku-restrictions/<int:index>', methods=['PUT'])
    def update_sku_restriction(index):
        """Update an existing SKU restriction"""
        try:
            data = request.get_json()
            
            # Get existing restrictions
            restrictions = SKURestrictionService.get_all_restrictions()
            if index >= len(restrictions):
                return jsonify({
                    'success': False,
                    'error': 'Restriction not found'
                }), 404
            
            # Update the restriction
            restrictions[index].sku_pattern = data.get('sku_pattern', restrictions[index].sku_pattern)
            restrictions[index].restriction_type = data.get('restriction_type', restrictions[index].restriction_type)
            restrictions[index].description = data.get('description', restrictions[index].description)
            restrictions[index].enabled = data.get('enabled', restrictions[index].enabled)
            restrictions[index].allowed_telcos = data.get('allowed_telcos', restrictions[index].allowed_telcos)
            restrictions[index].max_configurations = data.get('max_configurations', restrictions[index].max_configurations)
            restrictions[index].single_telco_only = data.get('single_telco_only', restrictions[index].single_telco_only)
            
            if SKURestrictionService.save_restrictions(restrictions):
                return jsonify({
                    'success': True,
                    'message': 'SKU restriction updated successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to update SKU restriction'
                }), 500
                
        except Exception as e:
            current_app.logger.error(f"Error updating SKU restriction: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @bp.route('/api/sku-restrictions/<int:index>', methods=['DELETE'])
    def delete_sku_restriction(index):
        """Delete an SKU restriction"""
        try:
            if SKURestrictionService.remove_restriction(index):
                return jsonify({
                    'success': True,
                    'message': 'SKU restriction deleted successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to delete SKU restriction or restriction not found'
                }), 404
                
        except Exception as e:
            current_app.logger.error(f"Error deleting SKU restriction: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @bp.route('/api/sku-restrictions/stats', methods=['GET'])
    def get_sku_restrictions_stats():
        """Get statistics about SKU restrictions and their usage"""
        try:
            stats = SKURestrictionService.get_restrictions_stats()
            return jsonify({
                'success': True,
                'stats': stats
            })
        except Exception as e:
            current_app.logger.error(f"Error getting SKU restrictions stats: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @bp.route('/api/sku-restrictions/test', methods=['POST'])
    def test_sku_restriction():
        """Test if a SKU matches any restrictions"""
        try:
            data = request.get_json()
            sku = data.get('sku')
            
            if not sku:
                return jsonify({
                    'success': False,
                    'error': 'SKU is required'
                }), 400
            
            restriction = SKURestrictionService.get_restriction_for_sku(sku)
            
            return jsonify({
                'success': True,
                'sku': sku,
                'has_restriction': restriction is not None,
                'restriction': restriction.to_dict() if restriction else None
            })
            
        except Exception as e:
            current_app.logger.error(f"Error testing SKU restriction: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # ========== SKU Tags Management Routes ==========

    @bp.route('/sku-tags')
    def sku_tags_management():
        """SKU Tags management interface"""
        try:
            return render_template('vpn_config_generator/sku_tags_management.html')
        except Exception as e:
            logger.error(f"Error loading SKU tags management: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/sku-tags', methods=['GET'])
    def get_sku_tags():
        """Get all SKU tags mappings"""
        try:
            sku_tags_service = VPNSKUTagsService()
            config = sku_tags_service.load_config()

            return jsonify({
                'success': True,
                'data': {
                    'mappings': config.get('sku_server_tags_mapping', {}),
                    'fallback_mapping': config.get('fallback_mapping', {}),
                    'tag_definitions': config.get('tag_definitions', {}),
                    'version': config.get('version', 'unknown'),
                    'last_updated': config.get('last_updated', 'unknown')
                }
            })

        except Exception as e:
            logger.error(f"Error getting SKU tags: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/sku-tags', methods=['POST'])
    def add_sku_tag():
        """Add a new SKU tag mapping"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['category', 'sku', 'tags']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            category = data['category']
            sku = data['sku']
            tags = data['tags']
            validity_days = data.get('validity_days')

            # Validate tags
            if not isinstance(tags, list) or not tags:
                return jsonify({'success': False, 'error': 'Tags must be a non-empty list'}), 400

            sku_tags_service = VPNSKUTagsService()
            success = sku_tags_service.add_sku_mapping(category, sku, tags, validity_days)

            if success:
                return jsonify({
                    'success': True,
                    'message': f'Successfully added SKU mapping: {sku}'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to save SKU mapping'}), 500

        except Exception as e:
            logger.error(f"Error adding SKU tag: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/sku-tags/<category>/<sku>', methods=['PUT'])
    def update_sku_tag(category: str, sku: str):
        """Update an existing SKU tag mapping"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            tags = data.get('tags')
            validity_days = data.get('validity_days')

            # Validate tags
            if not isinstance(tags, list) or not tags:
                return jsonify({'success': False, 'error': 'Tags must be a non-empty list'}), 400

            sku_tags_service = VPNSKUTagsService()
            success = sku_tags_service.update_sku_mapping(category, sku, tags, validity_days)

            if success:
                return jsonify({
                    'success': True,
                    'message': f'Successfully updated SKU mapping: {sku}'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to save SKU mapping'}), 500

        except Exception as e:
            logger.error(f"Error updating SKU tag: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/sku-tags/<category>/<sku>', methods=['DELETE'])
    def delete_sku_tag(category: str, sku: str):
        """Delete an existing SKU tag mapping"""
        try:
            sku_tags_service = VPNSKUTagsService()
            success = sku_tags_service.delete_sku_mapping(category, sku)

            if success:
                return jsonify({
                    'success': True,
                    'message': f'Successfully deleted SKU mapping: {sku}'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to delete SKU mapping'}), 500

        except Exception as e:
            logger.error(f"Error deleting SKU tag: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/sku-tags/definitions', methods=['GET'])
    def get_tag_definitions():
        """Get all tag definitions"""
        try:
            sku_tags_service = VPNSKUTagsService()
            config = sku_tags_service.load_config()
            return jsonify({
                'success': True,
                'definitions': config.get('tag_definitions', {})
            })
        except Exception as e:
            logger.error(f"Error getting tag definitions: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/sku-tags/definitions', methods=['POST'])
    def add_tag_definition():
        """Add a new tag definition"""
        try:
            data = request.get_json()
            if not data or 'tag' not in data or 'description' not in data:
                return jsonify({'success': False, 'error': 'Tag and description are required'}), 400

            tag = data['tag']
            description = data['description']

            sku_tags_service = VPNSKUTagsService()
            success = sku_tags_service.add_tag_definition(tag, description)

            if success:
                return jsonify({
                    'success': True,
                    'message': f'Successfully added tag definition: {tag}'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to save tag definition'}), 500

        except Exception as e:
            logger.error(f"Error adding tag definition: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/sku-tags/definitions/<tag>', methods=['DELETE'])
    def delete_tag_definition(tag: str):
        """Delete a tag definition"""
        try:
            sku_tags_service = VPNSKUTagsService()
            success = sku_tags_service.delete_tag_definition(tag)

            if success:
                return jsonify({
                    'success': True,
                    'message': f'Successfully deleted tag definition: {tag}'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to delete tag definition'}), 500

        except Exception as e:
            logger.error(f"Error deleting tag definition: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # ========== Order Management API Routes ==========

    @bp.route('/order-config')
    def order_config_page():
        """Order-based VPN configuration management page"""
        try:
            return render_template('vpn_config_generator/order_config.html')
        except Exception as e:
            logger.error(f"Error loading order config page: {e}")
            return f"Error loading page: {str(e)}", 500

    @bp.route('/api/order/update-template-only', methods=['POST'])
    def update_template_only():
        """Update configuration template while preserving existing UUID and expiration date"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['user_uuid', 'telco', 'plan']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            user_uuid = data['user_uuid']
            telco = data['telco']
            plan = data['plan']

            # Validate user exists
            user = plugin_instance.order_service.get_user(user_uuid)
            if not user:
                return jsonify({'success': False, 'error': 'User not found'}), 404

            # Check telco access permissions
            can_access, access_message = plugin_instance.order_service.can_user_access_telco(user_uuid, telco)
            if not can_access:
                return jsonify({'success': False, 'error': access_message}), 403

            # Get user's existing configuration to preserve UUID and expiry
            existing_configs = user.generated_configs or []
            if not existing_configs:
                return jsonify({'success': False, 'error': 'No existing configuration found to update'}), 404

            # Use the most recent configuration
            latest_config = max(existing_configs, key=lambda c: c.get('generated_at', ''))
            preserve_uuid = latest_config.get('client_id')
            preserve_expiry = latest_config.get('expired_date')

            logger.info(f"Template update request - User: {user.buyer_username}, Telco: {telco}, Plan: {plan}")
            logger.info(f"Preserving UUID: {preserve_uuid}, Expiry: {preserve_expiry}")

            # Create VPN config request for template generation
            from .models import VPNConfigRequest
            config_request = VPNConfigRequest(
                server=data.get('server', 'auto'),  # Server can be specified or auto
                days=data.get('days', '30'),  # Days is used for template vars, not actual expiry
                telco=telco,
                plan=plan,
                username=user.buyer_username,
                sku=user.sku,
                var_sku=user.var_sku
            )

            # Use the new template-only update method
            result = config_service.update_config_template_only(
                config_request, 
                preserve_uuid=preserve_uuid, 
                preserve_expiry_date=preserve_expiry
            )

            if result.success:
                # Update user's configuration record with template change
                for config in user.generated_configs:
                    if config.get('client_id') == preserve_uuid:
                        config.update({
                            'telco': telco,
                            'plan': plan,
                            'template_updated_at': datetime.now().isoformat(),
                            'template_update_reason': f"Template changed to {telco} {plan}"
                        })
                        break

                # Update user metadata but preserve claim status and dates
                user.last_access = datetime.now().isoformat()
                plugin_instance.order_service.save_users()

                return jsonify({
                    'success': True,
                    'config': result.config,
                    'client_id': result.client_id,
                    'numeric_id': result.numeric_id,
                    'created_date': result.created_date,
                    'expired_date': result.expired_date,
                    'message': result.message,
                    'template_change': {
                        'new_telco': telco,
                        'new_plan': plan,
                        'uuid_preserved': preserve_uuid,
                        'expiry_preserved': preserve_expiry
                    }
                })
            else:
                return jsonify({'success': False, 'error': result.error}), 500

        except Exception as e:
            logger.error(f"Error updating template only: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    # ========== Refund Detection Routes ==========
    
    @bp.route('/api/refund/check-and-remove', methods=['POST'])
    def check_and_remove_refunded_configs():
        """Check all existing VPN configs for refunded orders and remove VPN clients"""
        try:
            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500
                
            result = plugin_instance.order_service.check_and_remove_refunded_configs()
            
            if result['success']:
                return jsonify({
                    'success': True,
                    'message': f"Refund detection completed. {result['removed_count']} VPN clients removed.",
                    'removed_clients': result['removed_clients'],
                    'checked_orders_count': result['checked_orders_count'],
                    'removed_count': result['removed_count'],
                    'errors': result['errors']
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['error'],
                    'errors': result.get('errors', [])
                }), 500
                
        except Exception as e:
            logger.error(f"Error in refund detection API: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/refund/delete-client', methods=['POST'])
    def delete_vpn_client():
        """Manually delete a VPN client by ID"""
        try:
            data = request.get_json()
            if not data or 'client_id' not in data:
                return jsonify({'success': False, 'error': 'client_id is required'}), 400
                
            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500
                
            client_id = data['client_id']
            result = plugin_instance.order_service._delete_vpn_client(client_id)
            
            if result['success']:
                return jsonify({
                    'success': True,
                    'message': f"VPN client {client_id} deleted successfully",
                    'method': result.get('method', 'unknown')
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['error']
                }), 400
                
        except Exception as e:
            logger.error(f"Error deleting VPN client: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # ========== Redemption Link Management Routes ==========

    @bp.route('/admin/redemption-links')
    def redemption_links_admin():
        """Admin interface for managing redemption links"""
        try:
            return render_template('vpn_config_generator/redemption_links_admin.html')
        except Exception as e:
            logger.error(f"Error loading redemption links admin: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/redeem/<link_id>')
    def redeem_link(link_id: str):
        """Redemption interface for customers"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            # Validate the redemption link
            is_valid, message, link = redemption_service.validate_redemption_link(link_id)

            if not is_valid:
                return render_template('vpn_config_generator/redemption_error.html',
                                     error=message, link_id=link_id)

            return render_template('vpn_config_generator/redemption_interface.html',
                                 link=link.to_dict(), link_id=link_id)
        except Exception as e:
            logger.error(f"Error loading redemption interface: {e}")
            return render_template('vpn_config_generator/redemption_error.html',
                                 error=str(e), link_id=link_id)

    @bp.route('/api/redemption-links', methods=['GET'])
    def get_redemption_links():
        """Get all redemption links"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            links = redemption_service.get_all_redemption_links()
            return jsonify({
                'success': True,
                'links': {link_id: link.to_dict() for link_id, link in links.items()}
            })
        except Exception as e:
            logger.error(f"Error getting redemption links: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links', methods=['POST'])
    def create_redemption_link():
        """Create a new redemption link"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['customer_username', 'validity_days']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            # Get redemption service
            redemption_service = get_redemption_service()

            # Create request object
            from .models import RedemptionLinkRequest
            request_obj = RedemptionLinkRequest.from_dict(data)

            # Create the redemption link
            result = redemption_service.create_redemption_link(request_obj)

            if result.success:
                return jsonify({
                    'success': True,
                    'link': result.redemption_link.to_dict(),
                    'redemption_url': result.redemption_url,
                    'message': result.message
                })
            else:
                return jsonify({'success': False, 'error': result.error}), 400

        except Exception as e:
            logger.error(f"Error creating redemption link: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/<link_id>', methods=['GET'])
    def get_redemption_link(link_id: str):
        """Get a specific redemption link"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            link = redemption_service.get_redemption_link(link_id)
            if link:
                return jsonify({
                    'success': True,
                    'link': link.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Redemption link not found'}), 404
        except Exception as e:
            logger.error(f"Error getting redemption link: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/<link_id>/validate', methods=['GET'])
    def validate_redemption_link(link_id: str):
        """Validate a redemption link"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            is_valid, message, link = redemption_service.validate_redemption_link(link_id)

            return jsonify({
                'success': True,
                'is_valid': is_valid,
                'message': message,
                'link': link.to_dict() if link else None
            })
        except Exception as e:
            logger.error(f"Error validating redemption link: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/<link_id>/deactivate', methods=['POST'])
    def deactivate_redemption_link(link_id: str):
        """Deactivate a redemption link"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            success = redemption_service.deactivate_redemption_link(link_id)
            if success:
                return jsonify({
                    'success': True,
                    'message': 'Redemption link deactivated successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Redemption link not found'}), 404
        except Exception as e:
            logger.error(f"Error deactivating redemption link: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/<link_id>', methods=['DELETE'])
    def delete_redemption_link(link_id: str):
        """Delete a redemption link"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            success = redemption_service.delete_redemption_link(link_id)
            if success:
                return jsonify({
                    'success': True,
                    'message': 'Redemption link deleted successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Redemption link not found'}), 404
        except Exception as e:
            logger.error(f"Error deleting redemption link: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/process', methods=['POST'])
    def process_redemption_link():
        """Process a redemption link to generate VPN configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            link_id = data.get('link_id')
            if not link_id:
                return jsonify({'success': False, 'error': 'Link ID is required'}), 400

            # Get redemption service
            redemption_service = get_redemption_service()

            # Validate the redemption link
            is_valid, message, link = redemption_service.validate_redemption_link(link_id)
            if not is_valid:
                return jsonify({'success': False, 'error': message}), 400

            # Create a fake order for the redemption link
            fake_order_sn = f"REDEEM-{link_id[:8]}-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Create fake order data that mimics a real Shopee order
            fake_order_data = {
                'order_sn': fake_order_sn,
                'buyer_username': link.customer_username,
                'sku': 'REDEMPTION_LINK',
                'var_sku': link.var_sku or 'REDEMPTION_30D',  # Default to 30 days
                'status': 'To Ship',  # Use a status that allows processing
                'items': [{
                    'item_name': f'VPN Configuration - {link.validity_days} Days',
                    'variation_name': link.var_sku or 'Standard',
                    'quantity': 1
                }]
            }

            # Add the fake order to the order service
            if not hasattr(plugin_instance, 'order_service'):
                return jsonify({'success': False, 'error': 'Order service not available'}), 500

            # Store the fake order
            plugin_instance.order_service._orders.append(fake_order_data)
            plugin_instance.order_service.save_orders()

            # Process the fake order using existing order processing logic
            from .models import VPNOrderRequest
            order_request = VPNOrderRequest(
                order_sn=fake_order_sn,
                buyer_username=link.customer_username
            )

            result = plugin_instance.order_service.process_order(order_request)

            if result.success:
                # Mark the redemption link as used
                redemption_service.use_redemption_link(link_id, link.customer_username)

                # Generate VPN configuration using the existing flow
                user = plugin_instance.order_service.get_user(result.user_uuid)
                if user:
                    # Use link parameters or defaults
                    telco = link.telco or data.get('telco', 'digi')  # Allow override from request
                    plan = link.plan or data.get('plan', 'unlimited')  # Allow override from request
                    server = link.server_id or data.get('server', 'auto')  # Allow override from request

                    # Create VPN config request
                    from .models import VPNConfigRequest
                    config_request = VPNConfigRequest(
                        server=server,
                        days=str(link.validity_days),
                        telco=telco,
                        plan=plan,
                        username=link.customer_username,
                        sku='REDEMPTION_LINK',
                        var_sku=link.var_sku
                    )

                    # Generate the configuration
                    config_result = config_service.generate_config(config_request)

                    if config_result.success:
                        return jsonify({
                            'success': True,
                            'config': config_result.config,
                            'created_date': config_result.created_date,
                            'expired_date': config_result.expired_date,
                            'message': f'VPN configuration generated successfully from redemption link',
                            'order_sn': fake_order_sn,
                            'link_id': link_id
                        })
                    else:
                        return jsonify({'success': False, 'error': config_result.error}), 500
                else:
                    return jsonify({'success': False, 'error': 'User not found after order processing'}), 500
            else:
                return jsonify({'success': False, 'error': result.error}), 400

        except Exception as e:
            logger.error(f"Error processing redemption link: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/cleanup', methods=['POST'])
    def cleanup_expired_redemption_links():
        """Clean up expired redemption links"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            cleaned_count = redemption_service.cleanup_expired_links()
            return jsonify({
                'success': True,
                'message': f'Cleaned up {cleaned_count} expired redemption links',
                'cleaned_count': cleaned_count
            })
        except Exception as e:
            logger.error(f"Error cleaning up redemption links: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # ========== Chat Integration Routes ==========

    @bp.route('/api/redemption-links/<link_id>/send-chat', methods=['POST'])
    def send_redemption_link_via_chat(link_id: str):
        """Send a redemption link via chat to the customer"""
        try:
            data = request.get_json() or {}
            base_url = data.get('base_url', request.host_url.rstrip('/'))

            # Get redemption service
            redemption_service = get_redemption_service()

            success, message = redemption_service.send_redemption_link_via_chat(link_id, base_url)

            if success:
                return jsonify({
                    'success': True,
                    'message': message
                })
            else:
                return jsonify({'success': False, 'error': message}), 400

        except Exception as e:
            logger.error(f"Error sending redemption link via chat: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/chat-template-config', methods=['GET'])
    def get_chat_template_config():
        """Get chat template configuration"""
        try:
            # Get redemption service
            redemption_service = get_redemption_service()

            config = redemption_service.get_chat_template_config()
            return jsonify({
                'success': True,
                'config': config.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting chat template config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/chat-template-config', methods=['PUT'])
    def update_chat_template_config():
        """Update chat template configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Get redemption service
            redemption_service = get_redemption_service()

            # Create config object
            from .models import ChatTemplateConfig
            config = ChatTemplateConfig.from_dict(data)

            # Update config
            success = redemption_service.update_chat_template_config(config)
            if success:
                return jsonify({
                    'success': True,
                    'message': 'Chat template configuration updated successfully',
                    'config': config.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update chat template configuration'}), 500

        except Exception as e:
            logger.error(f"Error updating chat template config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # ========== Bulk Creation Routes ==========

    @bp.route('/admin/redemption-links/bulk')
    def bulk_redemption_links():
        """Bulk creation interface for redemption links"""
        try:
            return render_template('vpn_config_generator/bulk_redemption_links.html')
        except Exception as e:
            logger.error(f"Error loading bulk redemption links page: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/redemption-links/bulk', methods=['POST'])
    def create_bulk_redemption_links():
        """Create multiple redemption links in bulk"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Validate required fields
            required_fields = ['customer_usernames', 'validity_days']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

            # Get redemption service
            redemption_service = get_redemption_service()

            # Create request object
            from .models import BulkCreationRequest
            request_obj = BulkCreationRequest.from_dict(data)

            # Get base URL for chat messages
            base_url = data.get('base_url', request.host_url.rstrip('/'))

            # Create bulk redemption links
            result = redemption_service.create_bulk_redemption_links(request_obj, base_url)

            return jsonify({
                'success': result.success,
                'result': result.to_dict()
            })

        except Exception as e:
            logger.error(f"Error creating bulk redemption links: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/bulk/parse-csv', methods=['POST'])
    def parse_csv_usernames():
        """Parse CSV data to extract customer usernames"""
        try:
            data = request.get_json()
            if not data or 'csv_data' not in data:
                return jsonify({'success': False, 'error': 'No CSV data provided'}), 400

            csv_data = data['csv_data'].strip()
            if not csv_data:
                return jsonify({'success': False, 'error': 'Empty CSV data'}), 400

            # Parse CSV data
            import csv
            import io

            usernames = []
            errors = []

            try:
                csv_reader = csv.reader(io.StringIO(csv_data))
                for row_num, row in enumerate(csv_reader, 1):
                    if row and len(row) > 0:
                        username = row[0].strip()
                        if username:
                            usernames.append(username)
                        else:
                            errors.append(f"Row {row_num}: Empty username")
                    else:
                        errors.append(f"Row {row_num}: Empty row")
            except Exception as parse_error:
                return jsonify({
                    'success': False,
                    'error': f'CSV parsing error: {str(parse_error)}'
                }), 400

            return jsonify({
                'success': True,
                'usernames': usernames,
                'total_count': len(usernames),
                'errors': errors
            })

        except Exception as e:
            logger.error(f"Error parsing CSV usernames: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # ========== Analytics Routes ==========

    @bp.route('/admin/redemption-links/analytics')
    def redemption_links_analytics():
        """Analytics interface for redemption links"""
        try:
            return render_template('vpn_config_generator/redemption_analytics.html')
        except Exception as e:
            logger.error(f"Error loading redemption analytics page: {e}")
            return jsonify({'error': str(e)}), 500

    @bp.route('/api/redemption-links/analytics', methods=['GET'])
    def get_redemption_analytics():
        """Get analytics data for redemption links"""
        try:
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')

            # Get redemption service
            redemption_service = get_redemption_service()

            analytics = redemption_service.get_analytics(start_date, end_date)

            return jsonify({
                'success': True,
                'analytics': analytics.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting redemption analytics: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/redemption-links/analytics/export', methods=['GET'])
    def export_redemption_analytics():
        """Export analytics data"""
        try:
            format_type = request.args.get('format', 'json').lower()
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')

            # Get redemption service
            redemption_service = get_redemption_service()

            success, message, data = redemption_service.export_analytics_data(format_type, start_date, end_date)

            if success:
                if format_type == 'csv':
                    from flask import Response
                    return Response(
                        data,
                        mimetype='text/csv',
                        headers={'Content-Disposition': 'attachment; filename=redemption_links_export.csv'}
                    )
                else:
                    return jsonify({
                        'success': True,
                        'data': data,
                        'message': message
                    })
            else:
                return jsonify({'success': False, 'error': message}), 400

        except Exception as e:
            logger.error(f"Error exporting redemption analytics: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    return bp


def send_responses_to_shopee(responses, original_message, webhook_config):
    """Send responses back to ShopeeAPI"""
    try:
        import requests
        
        if not webhook_config.response_endpoint:
            logger.warning("No response endpoint configured for ShopeeAPI")
            return False
        
        for response in responses:
            payload = {
                'chat_id': original_message.chat_id,
                'message': response.message,
                'order_sn': original_message.order_sn
            }
            
            # Send to ShopeeAPI
            resp = requests.post(webhook_config.response_endpoint, json=payload, timeout=10)
            if resp.status_code != 200:
                logger.error(f"Failed to send response to ShopeeAPI: {resp.status_code}")
                return False
        
        return True
    except Exception as e:
        logger.error(f"Error sending responses to ShopeeAPI: {e}")
        return False