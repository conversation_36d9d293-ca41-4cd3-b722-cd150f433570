{"AUTHORIZATION_CODE": "Bearer your_auth_token", "COOKIE": "your_cookie_string", "SHOP_ID": 123456789, "REGION_ID": "MY", "PAGE_SIZE": 40, "REQUEST_TIMEOUT": 60, "CACHE": {"ENABLED": true, "USERNAME_TO_CONVERSATION_ID": {"ENABLED": true, "EXPIRY_SECONDS": 86400, "MAX_SIZE": 1000}, "CONVERSATION_MESSAGES": {"ENABLED": true, "EXPIRY_SECONDS": 3600, "MAX_SIZE": 50, "WEBSOCKET_ONLY": true}}, "IMAGE_CACHE": {"MAX_AGE_HOURS": 24, "MAX_CACHE_SIZE_MB": 100, "CLEANUP_INTERVAL_HOURS": 4}, "WEBHOOK": {"ENABLED": true, "ERROR_RATE_LIMITING": {"ENABLED": true, "LOG_INTERVAL_SECONDS": 300, "MAX_ERRORS_PER_INTERVAL": 3}, "MESSAGE_RECEIVED": {"ENABLED": true, "URL": "http://your-webhook-url/message-received", "RETRY_COUNT": 3, "RETRY_DELAY": 5}, "MESSAGE_SENT": {"ENABLED": true, "URL": "http://your-webhook-url/message-sent", "RETRY_COUNT": 3, "RETRY_DELAY": 5}}, "WEBSOCKET": {"ENABLED": true, "RECONNECT_INTERVAL": 30, "MAX_RECONNECT_ATTEMPTS": 10, "PING_INTERVAL": 25, "PING_TIMEOUT": 5, "CLIENT_MAX_SIZE": 100}, "URLS": {"initial_order_list": "https://seller.shopee.com.my/api/v3/order/search_order_list_index", "order_details": "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list", "order_details_specific": "https://seller.shopee.com.my/api/v3/order/get_one_order", "init_order": "https://seller.shopee.com.my/api/v3/shipment/init_order", "conversation": "https://seller.shopee.com.my/webchat/api/v1.2/mini/conversation/redirection", "conversation_search": "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/search/combined_search", "chat_message": "https://seller.shopee.com.my/webchat/api/v1.2/messages", "recent_conversations": "https://seller.shopee.com.my/webchat/api/v1.2/conversations", "buyer_orders": "https://seller.shopee.com.my/webchat/api/v1.2/orders/orders_by_buyer", "conversation_messages": "https://seller.shopee.com.my/webchat/api/v1.2/conversations", "conversation_unread": "https://seller.shopee.com.my/webchat/api/v1.2/conversations", "chat_image_upload": "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/images", "websocket": "wss://seller-push-ws.shopee.com.my/socket.io/?region=my&_v=new&EIO=3&transport=websocket"}, "EMAIL_SENDER": {"FROM_EMAIL": "<EMAIL>", "FROM_PASSWORD": "your_sender_app_password", "DEFAULT_TO_EMAIL": "<EMAIL>"}, "AUTOMATED_CHECKS": {"COOKIE_VALIDITY": {"ENABLED": true, "INTERVAL_MINUTES": 60, "NOTIFICATION_EMAIL": "<EMAIL>"}}}