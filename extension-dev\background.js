let bearerToken = '';
let fullCookieText = '';
let cookieJsonArray = [];
let isTokenCaptured = false;
let isCookieCaptured = false;

// Store the listener function reference to remove it later
const headerListener = (details) => {
  if (isTokenCaptured) return;

  for (let header of details.requestHeaders) {
    if (header.name.toLowerCase() === 'authorization' && header.value.startsWith('Bearer ')) {
      bearerToken = header.value;
      chrome.storage.local.set({ bearerToken });
      isTokenCaptured = true;
      chrome.webRequest.onBeforeSendHeaders.removeListener(headerListener);
      console.log('Bearer token captured, listener removed.');
      checkAndRemoveListeners();
      break;
    }
  }
};

chrome.webRequest.onBeforeSendHeaders.addListener(
  headerListener,
  { urls: ["*://seller.shopee.com.my/*"] },
  ["requestHeaders", "extraHeaders"]
);

function updateCookies(domain) {
  // Always allow recapture to keep cookies fresh

  // Get cookies from multiple sources to ensure we capture everything
  const cookieSources = [
    { domain: domain },
    { url: `https://${domain}` },
    { url: `https://seller.shopee.com.my` },
    { url: `https://shopee.com.my` }
  ];

  let allCookies = [];
  let completedRequests = 0;

  cookieSources.forEach(source => {
    chrome.cookies.getAll(source, (cookies) => {
      // Add cookies to the collection, avoiding duplicates
      cookies.forEach(cookie => {
        const exists = allCookies.some(existing =>
          existing.name === cookie.name &&
          existing.domain === cookie.domain &&
          existing.path === cookie.path
        );
        if (!exists) {
          allCookies.push(cookie);
        }
      });

      completedRequests++;

      // Process when all requests are complete
      if (completedRequests === cookieSources.length) {
        processCookies(allCookies);
      }
    });
  });
}

function processCookies(cookies) {
  console.log(`Processing ${cookies.length} total cookies`);

  // Prefer the latest/most-valid cookie per name to avoid stale duplicates
  const getDomainPriority = (domain) => {
    // Highest priority to base domain .shopee.com.my (typical for CTOKEN & SPC_*)
    if (domain === '.shopee.com.my') return 3;
    if (domain === 'shopee.com.my') return 2;
    if (domain === 'seller.shopee.com.my') return 2; // treat as same tier as apex
    if (domain.endsWith('.shopee.com.my')) return 1; // any other subdomains
    return 0;
  };

  const sortByPreference = (a, b) => {
    // Domain priority desc
    const dpA = getDomainPriority(a.domain || '');
    const dpB = getDomainPriority(b.domain || '');
    if (dpA !== dpB) return dpB - dpA;
    // Prefer root path
    const pathA = (a.path || '/').length;
    const pathB = (b.path || '/').length;
    if (pathA !== pathB) return pathA - pathB;
    // Prefer secure cookies
    if (a.secure !== b.secure) return (a.secure ? -1 : 1);
    // Prefer httpOnly cookies
    if (a.httpOnly !== b.httpOnly) return (a.httpOnly ? -1 : 1);
    // Prefer newer expirationDate
    const expA = a.expirationDate || 0;
    const expB = b.expirationDate || 0;
    if (expA !== expB) return expB - expA;
    return 0;
  };

  // Group by cookie name and pick the best candidate per name
  const grouped = cookies.reduce((map, c) => {
    if (!map[c.name]) map[c.name] = [];
    map[c.name].push(c);
    return map;
  }, {});

  const preferredCookies = Object.entries(grouped).map(([name, list]) => {
    // Special handling for high-impact cookies to reduce staleness risk
    const critical = ['CTOKEN', 'SPC_STK', 'SPC_EC', 'SPC_SC_SESSION', 'SPC_CDS_CHAT'];
    const sorted = [...list].sort((a, b) => {
      if (critical.includes(name)) {
        // For critical cookies, weigh expiration most, then domain
        const expA = a.expirationDate || 0;
        const expB = b.expirationDate || 0;
        if (expA !== expB) return expB - expA;
        const dpA = getDomainPriority(a.domain || '');
        const dpB = getDomainPriority(b.domain || '');
        if (dpA !== dpB) return dpB - dpA;
      }
      return sortByPreference(a, b);
    });
    if (sorted.length > 1) {
      console.log(`Dedup: ${name} had ${sorted.length} variants. Chose domain=${sorted[0].domain} path=${sorted[0].path}`);
    }
    return sorted[0];
  });

  // Check if we have the important SPC_CDS_CHAT cookie
  const spcCdsChatCookie = preferredCookies.find(cookie => cookie.name === "SPC_CDS_CHAT");
  if (spcCdsChatCookie) {
    console.log("✅ SPC_CDS_CHAT cookie found:", spcCdsChatCookie.value.substring(0, 20) + "...");
  } else {
    console.log("❌ SPC_CDS_CHAT cookie NOT found");
  }

  // Store as string format for backward compatibility (order: stable by name)
  fullCookieText = preferredCookies
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(cookie => `${cookie.name}=${cookie.value}`)
    .join('; ');

  // Store as JSON array format with all available properties
  cookieJsonArray = preferredCookies.map(cookie => {
    const cookieObj = {
      name: cookie.name,
      value: cookie.value,
      domain: cookie.domain,
      path: cookie.path,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      sameSite: cookie.sameSite,
      hostOnly: cookie.hostOnly
    };
    if (cookie.expirationDate) cookieObj.expirationDate = cookie.expirationDate;
    if (cookie.storeId) cookieObj.storeId = cookie.storeId;
    if (cookie.session) cookieObj.session = cookie.session;
    return cookieObj;
  });

  // Save both formats to storage
  chrome.storage.local.set({
    fullCookieText,
    cookieJsonArray: JSON.stringify(cookieJsonArray)
  });

  console.log("Cookies saved to storage. String length:", fullCookieText.length);
  console.log("JSON array length:", cookieJsonArray.length);

  isCookieCaptured = true;
  checkAndRemoveListeners();
}

// Initialize cookie capture from primary domain
updateCookies("seller.shopee.com.my");


const cookieListener = (changeInfo) => {
  // Always react to cookie changes to keep storage up to date

  const domain = changeInfo.cookie.domain;
  if (domain.includes("seller.shopee.com.my") || domain.includes("shopee.com.my")) {
    updateCookies(domain);
  }
};

chrome.cookies.onChanged.addListener(cookieListener);

function checkAndRemoveListeners() {
  if (isTokenCaptured && isCookieCaptured) {
    // Only remove header listener; keep cookie listener to auto-refresh cookies
    if (chrome.webRequest.onBeforeSendHeaders.hasListener(headerListener)) {
      chrome.webRequest.onBeforeSendHeaders.removeListener(headerListener);
    }
    console.log('Bearer token captured. Cookie listener remains active for updates.');
  }
}

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'refreshCookies') {
    console.log('Received refresh cookies request');

    // Reset the capture flag to allow recapture
    isCookieCaptured = false;

    // Clear existing cookie data
    fullCookieText = '';
    cookieJsonArray = [];

    // Recapture cookies
    updateCookies("seller.shopee.com.my");

    // Send response
    sendResponse({ success: true });

    return true; // Keep the message channel open for async response
  }
});