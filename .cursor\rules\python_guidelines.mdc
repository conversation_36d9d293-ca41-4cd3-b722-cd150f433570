---
globs: *.py
description: "Python Coding Guidelines"
---
Always adhere to PEP 8, include type hints for functions and method signatures, and write descriptive docstrings for all public modules, classes, and functions.

Code Style Checklist:
1. Use absolute imports within the project.
2. Avoid wildcard imports.
3. Prefer dependency injection over global states.
4. Keep functions small and focused on a single responsibility.
5. Leverage utils.audit_logger for consistent logging.
6. Add unit tests for critical logic.
