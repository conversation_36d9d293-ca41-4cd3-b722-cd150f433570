{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <form method="POST" action="">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="server_id">Server <span class="text-danger">*</span></label>
                            <select class="form-control" id="server_id" name="server_id" required>
                                <option value="">Select a server...</option>
                                {% for server in servers %}
                                <option value="{{ server.id }}"
                                        {% if request.args.get('server_id') == server.id|string %}selected{% endif %}>
                                    {{ server.name }} ({{ server.host }})
                                </option>
                                {% endfor %}
                            </select>
                            {% if request.args.get('server_id') %}
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i>
                                Pre-selected server from context. Xray service will be automatically restarted after bulk creation.
                            </small>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="clients_data">Clients Data <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="clients_data" name="clients_data" rows="15" required
                                      placeholder="email,expired_date,shopee_username,description&#10;<EMAIL>,31-12-2025,shopee_user1,Premium user&#10;<EMAIL>,lifetime,,Basic user&#10;<EMAIL>,15-01-2026"></textarea>
                            <small class="form-text text-muted">
                                <strong>Format:</strong> email,expired_date,shopee_username,description<br>
                                - One client per line<br>
                                - Email and expired_date are required<br>
                                - Shopee username and description are optional<br>
                                - Expired date format: DD-MM-YYYY or 'lifetime'<br>
                                - Use commas to separate fields, leave empty for optional fields
                            </small>
                        </div>
                        
                        <div class="alert alert-info">
                            <h5>Example:</h5>
                            <pre><EMAIL>,31-12-2025,john_shopee,Premium account
<EMAIL>,lifetime,,Lifetime member
<EMAIL>,15-01-2026
<EMAIL>,28-02-2026,alice123,Trial account</pre>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Quick Templates</h5>
                                    </div>
                                    <div class="card-body">
                                        <button type="button" class="btn btn-sm btn-outline-primary mb-2" onclick="generateTemplate('30days')">
                                            Generate 30-day accounts
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-primary mb-2" onclick="generateTemplate('lifetime')">
                                            Generate lifetime accounts
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-primary mb-2" onclick="generateTemplate('custom')">
                                            Generate custom template
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Import Options</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="custom-control custom-checkbox mb-2">
                                            <input type="checkbox" class="custom-control-input" id="skip_duplicates" name="skip_duplicates">
                                            <label class="custom-control-label" for="skip_duplicates">Skip duplicate emails</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="validate_format" name="validate_format" checked>
                                            <label class="custom-control-label" for="validate_format">Validate date format</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary" onclick="return validateBulkData()">
                            <i class="fas fa-upload"></i> Create Clients
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearData()">
                            <i class="fas fa-eraser"></i> Clear
                        </button>
                        <a href="{{ url_for('vpn.clients') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function generateTemplate(type) {
    var textarea = $('#clients_data');
    var template = '';
    
    if (type === '30days') {
        var date = new Date();
        date.setDate(date.getDate() + 30);
        var expiry = formatDate(date);
        
        template = `<EMAIL>,${expiry},,30-day trial
<EMAIL>,${expiry},,30-day trial
<EMAIL>,${expiry},,30-day trial
<EMAIL>,${expiry},,30-day trial
<EMAIL>,${expiry},,30-day trial`;
    } else if (type === 'lifetime') {
        template = `<EMAIL>,lifetime,,VIP lifetime member
<EMAIL>,lifetime,,VIP lifetime member
<EMAIL>,lifetime,,VIP lifetime member`;
    } else if (type === 'custom') {
        var days = prompt('Enter number of days for expiry:', '90');
        if (days) {
            var date = new Date();
            date.setDate(date.getDate() + parseInt(days));
            var expiry = formatDate(date);
            
            template = `<EMAIL>,${expiry},,${days}-day custom plan
<EMAIL>,${expiry},,${days}-day custom plan`;
        }
    }
    
    if (template) {
        textarea.val(template);
    }
}

function formatDate(date) {
    var dd = String(date.getDate()).padStart(2, '0');
    var mm = String(date.getMonth() + 1).padStart(2, '0');
    var yyyy = date.getFullYear();
    return dd + '-' + mm + '-' + yyyy;
}

function clearData() {
    $('#clients_data').val('');
}

function validateBulkData() {
    var data = $('#clients_data').val().trim();
    if (!data) {
        alert('Please enter client data');
        return false;
    }
    
    var lines = data.split('\n');
    var validLines = 0;
    var errors = [];
    
    for (var i = 0; i < lines.length; i++) {
        var line = lines[i].trim();
        if (!line) continue;
        
        var parts = line.split(',');
        if (parts.length < 2) {
            errors.push(`Line ${i + 1}: Missing required fields (need at least email and expired_date)`);
            continue;
        }
        
        var email = parts[0].trim();
        var expiredDate = parts[1].trim();
        
        // Validate email
        if (!email || !email.includes('@')) {
            errors.push(`Line ${i + 1}: Invalid email format`);
            continue;
        }
        
        // Validate date
        if (!expiredDate) {
            errors.push(`Line ${i + 1}: Missing expiry date`);
            continue;
        }
        
        if (expiredDate !== 'lifetime') {
            var dateRegex = /^\d{2}-\d{2}-\d{4}$/;
            if (!dateRegex.test(expiredDate)) {
                errors.push(`Line ${i + 1}: Invalid date format (use DD-MM-YYYY or 'lifetime')`);
                continue;
            }
        }
        
        validLines++;
    }
    
    if (errors.length > 0) {
        var message = `Found ${errors.length} error(s):\n\n` + errors.join('\n') + 
                      `\n\nValid lines: ${validLines}\n\nDo you want to continue anyway?`;
        return confirm(message);
    }
    
    return confirm(`Ready to create ${validLines} client(s). Continue?`);
}
</script>
{% endblock %}