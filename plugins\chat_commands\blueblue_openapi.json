{"openapi": "3.1.0", "info": {"title": "VPSScriptHelper-BlueBlue", "description": "Centralized management system for BlueBlue VPN servers", "version": "1.0.0"}, "paths": {"/api/v1/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register", "description": "Register a new user.", "operationId": "register_api_v1_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["Authentication"], "summary": "<PERSON><PERSON>", "description": "Authenticate user and return access token.", "operationId": "login_api_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/me": {"get": {"tags": ["Authentication"], "summary": "Get Current User Info", "description": "Get current user information.", "operationId": "get_current_user_info_api_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/logout": {"post": {"tags": ["Authentication"], "summary": "Logout", "description": "Logout user (client should discard the token).", "operationId": "logout_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/refresh": {"post": {"tags": ["Authentication"], "summary": "Refresh <PERSON>", "description": "Refresh access token.", "operationId": "refresh_token_api_v1_auth_refresh_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/servers/": {"get": {"tags": ["Servers"], "summary": "Get Servers", "description": "Get list of servers.", "operationId": "get_servers_api_v1_servers__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ServerResponse"}, "title": "Response Get Servers Api V1 Servers  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Servers"], "summary": "Create Server", "description": "Create a new server.", "operationId": "create_server_api_v1_servers__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/servers/test-credentials": {"post": {"tags": ["Servers"], "summary": "Test Server Credentials", "description": "Test server credentials before creating a server.", "operationId": "test_server_credentials_api_v1_servers_test_credentials_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerCredentialsTest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerConnectionTest"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/servers/{server_id}": {"get": {"tags": ["Servers"], "summary": "Get Server", "description": "Get server by ID.", "operationId": "get_server_api_v1_servers__server_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Servers"], "summary": "Update Server", "description": "Update server information.", "operationId": "update_server_api_v1_servers__server_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Servers"], "summary": "Delete Server", "description": "Delete server.", "operationId": "delete_server_api_v1_servers__server_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/servers/{server_id}/test-connection": {"post": {"tags": ["Servers"], "summary": "Test Server Connection", "description": "Test SSH connection to server.", "operationId": "test_server_connection_api_v1_servers__server_id__test_connection_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/servers/{server_id}/restart-xray": {"post": {"tags": ["Servers"], "summary": "Restart Xray Service", "description": "Restart Xray service on server.", "operationId": "restart_xray_service_api_v1_servers__server_id__restart_xray_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/servers/{server_id}/service-status": {"get": {"tags": ["Servers"], "summary": "Get Service Status", "description": "Get Xray service status.", "operationId": "get_service_status_api_v1_servers__server_id__service_status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/servers/{server_id}/config": {"get": {"tags": ["Servers"], "summary": "Get Server Config", "description": "Get Xray configuration from server.", "operationId": "get_server_config_api_v1_servers__server_id__config_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/servers/{server_id}/clients": {"get": {"tags": ["Servers"], "summary": "Get Server Clients", "description": "Get clients from server configuration.", "operationId": "get_server_clients_api_v1_servers__server_id__clients_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/servers/{server_id}/remove-expired": {"post": {"tags": ["Servers"], "summary": "Remove Expired Clients", "description": "Remove expired clients from server.", "operationId": "remove_expired_clients_api_v1_servers__server_id__remove_expired_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/": {"get": {"tags": ["Clients"], "summary": "Get Clients", "description": "Get list of clients with filtering and pagination.", "operationId": "get_clients_api_v1_clients__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}, {"name": "server_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Server Id"}}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, {"name": "is_expired", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Expired"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Clients"], "summary": "Create Client", "description": "Create a new client and add to server configuration.", "operationId": "create_client_api_v1_clients__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/by-email/{email}": {"get": {"tags": ["Clients"], "summary": "Get Client By Email", "description": "Get client by email address.", "operationId": "get_client_by_email_api_v1_clients_by_email__email__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string", "title": "Email"}}, {"name": "server_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by server ID. If not provided, returns first match across all servers.", "title": "Server Id"}, "description": "Filter by server ID. If not provided, returns first match across all servers."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/{client_id}": {"get": {"tags": ["Clients"], "summary": "Get Client", "description": "Get client by ID.", "operationId": "get_client_api_v1_clients__client_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Clients"], "summary": "Update Client", "description": "Update client information.", "operationId": "update_client_api_v1_clients__client_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Clients"], "summary": "Delete Client", "description": "Delete client from database and server configuration.", "operationId": "delete_client_api_v1_clients__client_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/bulk": {"post": {"tags": ["Clients"], "summary": "Create Clients Bulk", "description": "Create multiple clients in bulk.", "operationId": "create_clients_bulk_api_v1_clients_bulk_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientBulkCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientBulkResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/clients/server/{server_id}/expiry": {"get": {"tags": ["Clients"], "summary": "Get Server Client Expiry", "description": "Get expiry information for all clients on a server.", "operationId": "get_server_client_expiry_api_v1_clients_server__server_id__expiry_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientExpiryInfo"}, "title": "Response Get Server Client Expiry Api V1 Clients Server  Server Id  Expiry Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/{client_id}/extend": {"post": {"tags": ["Clients"], "summary": "Extend Client Expiry", "description": "Extend client expiry by specified number of days.", "operationId": "extend_client_expiry_api_v1_clients__client_id__extend_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Client Id"}}, {"name": "days", "in": "query", "required": true, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "title": "Days"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/sync/server/{server_id}": {"post": {"tags": ["Clients"], "summary": "Sync Server Clients", "description": "Sync clients from server Xray configuration to local database.", "operationId": "sync_server_clients_api_v1_clients_sync_server__server_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/sync/all": {"post": {"tags": ["Clients"], "summary": "Sync All Servers Clients", "description": "Sync clients from all active servers to local database.", "operationId": "sync_all_servers_clients_api_v1_clients_sync_all_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/clients/sync/compare/{server_id}": {"get": {"tags": ["Clients"], "summary": "Compare Server Clients", "description": "Compare clients between server configuration and local database.", "operationId": "compare_server_clients_api_v1_clients_sync_compare__server_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/sync/analyze/{server_id}": {"get": {"tags": ["Clients"], "summary": "Analyze Invalid Clients", "description": "Analyze invalid clients in server configuration.", "operationId": "analyze_invalid_clients_api_v1_clients_sync_analyze__server_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/sync/cleanup/{server_id}": {"post": {"tags": ["Clients"], "summary": "Cleanup Orphaned Clients", "description": "Remove clients from database that don't exist in server configuration.", "operationId": "cleanup_orphaned_clients_api_v1_clients_sync_cleanup__server_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/": {"get": {"tags": ["Configuration"], "summary": "Get Configurations", "description": "Get configuration overview for all servers.", "operationId": "get_configurations_api_v1_config__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigOverviewResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/config/server/{server_id}": {"get": {"tags": ["Configuration"], "summary": "Get Server Config", "description": "Get configuration for a specific server.", "operationId": "get_server_config_api_v1_config_server__server_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Configuration"], "summary": "Update Server Config", "description": "Update configuration for a specific server.", "operationId": "update_server_config_api_v1_config_server__server_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/backup": {"post": {"tags": ["Configuration"], "summary": "Backup Configurations", "description": "Backup server configurations.", "operationId": "backup_configurations_api_v1_config_backup_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigBackupRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigBackupResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/config/restore": {"post": {"tags": ["Configuration"], "summary": "Restore Configuration", "description": "Restore server configuration from a backup.", "operationId": "restore_configuration_api_v1_config_restore_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigRestoreRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigRestoreResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/config/validate/server/{server_id}": {"get": {"tags": ["Configuration"], "summary": "Validate Server Config", "description": "Validate configuration for a specific server.", "operationId": "validate_server_config_api_v1_config_validate_server__server_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigValidationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/backups": {"get": {"tags": ["Configuration"], "summary": "List Config Backups", "description": "List available configuration backups.", "operationId": "list_config_backups_api_v1_config_backups_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by server ID", "title": "Server Id"}, "description": "Filter by server ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigBackupListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/compare/server/{server_id}": {"get": {"tags": ["Configuration"], "summary": "Compare Server Config", "description": "Compare server configuration with database state.", "operationId": "compare_server_config_api_v1_config_compare_server__server_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigComparisonResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/sync": {"post": {"tags": ["Configuration"], "summary": "Sync Configurations", "description": "Synchronize configurations between database and servers.", "operationId": "sync_configurations_api_v1_config_sync_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigSyncRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigSyncResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/tasks/": {"get": {"tags": ["Task Manager"], "summary": "Get Tasks", "description": "Get list of tasks.", "operationId": "get_tasks_api_v1_tasks__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}": {"get": {"tags": ["Task Manager"], "summary": "Get Task", "description": "Get task by ID.", "operationId": "get_task_api_v1_tasks__task_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/expiry-check": {"post": {"tags": ["Task Manager"], "summary": "<PERSON>gger Expiry Check", "description": "Trigger manual expiry check.", "operationId": "trigger_expiry_check_api_v1_tasks_expiry_check_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "query", "required": false, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/expiry/summary": {"get": {"tags": ["Task Manager"], "summary": "Get Expiry Summary", "description": "Get expiry summary for all servers.", "operationId": "get_expiry_summary_api_v1_tasks_expiry_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/background-tasks/enqueue-task": {"post": {"tags": ["Background Tasks"], "summary": "Enqueue Task", "description": "Enqueues a sample background task.", "operationId": "enqueue_task_api_v1_background_tasks_enqueue_task_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/health/": {"get": {"tags": ["Health"], "summary": "Comprehensive Health Dashboard", "description": "Comprehensive health dashboard endpoint matching frontend HealthDashboard interface.", "operationId": "comprehensive_health_dashboard_api_v1_health__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/health/detailed": {"get": {"tags": ["Health"], "summary": "Detailed Health Check", "description": "Detailed health check including database and services.", "operationId": "detailed_health_check_api_v1_health_detailed_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/health/expiry-summary": {"get": {"tags": ["Health"], "summary": "Expiry Health Check", "description": "Health check focused on client expiry status.", "operationId": "expiry_health_check_api_v1_health_expiry_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/health/servers/{server_id}": {"get": {"tags": ["Health"], "summary": "Server Health Check", "description": "Health check for a specific server matching frontend ServerHealth interface.", "operationId": "server_health_check_api_v1_health_servers__server_id__get", "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/health/servers/{server_id}/refresh": {"post": {"tags": ["Health"], "summary": "Refresh Server Health", "description": "Manually refresh health status for a specific server matching frontend HealthCheckResult interface.", "operationId": "refresh_server_health_api_v1_health_servers__server_id__refresh_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "server_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Server Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/health/refresh-all-servers": {"post": {"tags": ["Health"], "summary": "Refresh All Servers Health", "description": "Manually refresh health status for all servers in parallel matching frontend AllServersHealthCheckResult interface.", "operationId": "refresh_all_servers_health_api_v1_health_refresh_all_servers_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/health/ssh-pool": {"get": {"tags": ["Health"], "summary": "Ssh Connection Pool Status", "description": "Get SSH connection pool status and statistics.", "operationId": "ssh_connection_pool_status_api_v1_health_ssh_pool_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ws/stats": {"get": {"tags": ["WebSocket Health"], "summary": "Get Websocket Stats", "description": "Get WebSocket connection statistics.", "operationId": "get_websocket_stats_api_v1_ws_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/": {"get": {"summary": "Root", "description": "Root endpoint with basic application information.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1": {"get": {"summary": "Api Info", "description": "API version information.", "operationId": "api_info_api_v1_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"ClientBase": {"properties": {"email": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Email", "description": "Email or custom identifier"}, "shopee_username": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expired_date": {"type": "string", "title": "Expired Date", "description": "Expiry date in DD-MM-YYYY format or 'lifetime'"}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["email", "expired_date"], "title": "ClientBase", "description": "Base schema for client information."}, "ClientBulkCreate": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "clients": {"items": {"$ref": "#/components/schemas/ClientBase"}, "type": "array", "title": "Clients"}}, "type": "object", "required": ["server_id", "clients"], "title": "ClientBulkCreate", "description": "Schema for bulk client creation."}, "ClientBulkResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "created_count": {"type": "integer", "title": "Created Count"}, "failed_count": {"type": "integer", "title": "Failed Count"}, "created_clients": {"items": {"$ref": "#/components/schemas/ClientResponse"}, "type": "array", "title": "Created Clients"}, "errors": {"items": {"type": "string"}, "type": "array", "title": "Errors"}}, "type": "object", "required": ["success", "created_count", "failed_count", "created_clients", "errors"], "title": "ClientBulkResponse", "description": "Schema for bulk operation response."}, "ClientCreate": {"properties": {"email": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Email", "description": "Email or custom identifier"}, "shopee_username": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expired_date": {"type": "string", "title": "Expired Date", "description": "Expiry date in DD-MM-YYYY format or 'lifetime'"}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes"}, "server_id": {"type": "integer", "title": "Server Id"}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}}, "type": "object", "required": ["email", "expired_date", "server_id"], "title": "ClientCreate", "description": "<PERSON><PERSON><PERSON> for creating a new client."}, "ClientExpiryInfo": {"properties": {"client_id": {"type": "string", "title": "Client Id"}, "email": {"type": "string", "title": "Email"}, "expired_date": {"type": "string", "title": "Expired Date"}, "is_expired": {"type": "boolean", "title": "Is Expired"}, "days_until_expiry": {"type": "integer", "title": "Days Until Expiry"}, "warning_level": {"type": "string", "title": "Warning Level"}}, "type": "object", "required": ["client_id", "email", "expired_date", "is_expired", "days_until_expiry", "warning_level"], "title": "ClientExpiryInfo", "description": "Schema for client expiry information."}, "ClientListResponse": {"properties": {"clients": {"items": {"$ref": "#/components/schemas/ClientResponse"}, "type": "array", "title": "Clients"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "per_page": {"type": "integer", "title": "Per Page"}, "has_next": {"type": "boolean", "title": "Has Next"}, "has_prev": {"type": "boolean", "title": "<PERSON>"}}, "type": "object", "required": ["clients", "total", "page", "per_page", "has_next", "has_prev"], "title": "ClientListResponse", "description": "Schema for client list response with pagination."}, "ClientResponse": {"properties": {"email": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Email", "description": "Email or custom identifier"}, "shopee_username": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expired_date": {"type": "string", "title": "Expired Date", "description": "Expiry date in DD-MM-YYYY format or 'lifetime'"}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes"}, "id": {"type": "integer", "title": "Id"}, "server_id": {"type": "integer", "title": "Server Id"}, "client_id": {"type": "string", "title": "Client Id"}, "is_active": {"type": "boolean", "title": "Is Active"}, "is_expired": {"type": "boolean", "title": "Is Expired"}, "days_until_expiry": {"type": "integer", "title": "Days Until Expiry"}, "last_connected": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Connected"}, "data_usage": {"type": "integer", "title": "Data Usage"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["email", "expired_date", "id", "server_id", "client_id", "is_active", "is_expired", "days_until_expiry", "data_usage", "created_at", "updated_at"], "title": "ClientResponse", "description": "Schema for client response."}, "ClientUpdate": {"properties": {"email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "shopee_username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expired_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Expired Date"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "type": "object", "title": "ClientUpdate", "description": "Schema for updating client information."}, "ConfigBackupInfo": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "server_name": {"type": "string", "title": "Server Name"}, "backup_path": {"type": "string", "title": "Backup Path"}, "backup_size": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Backup Size"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "success": {"type": "boolean", "title": "Success"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}}, "type": "object", "required": ["server_id", "server_name", "backup_path", "created_at", "success"], "title": "ConfigBackupInfo", "description": "<PERSON><PERSON><PERSON> for backup information."}, "ConfigBackupListItem": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "server_name": {"type": "string", "title": "Server Name"}, "backup_path": {"type": "string", "title": "Backup Path"}, "backup_name": {"type": "string", "title": "Backup Name"}, "file_size": {"type": "integer", "title": "File Size"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "is_accessible": {"type": "boolean", "title": "Is Accessible"}}, "type": "object", "required": ["server_id", "server_name", "backup_path", "backup_name", "file_size", "created_at", "is_accessible"], "title": "ConfigBackupListItem", "description": "<PERSON><PERSON><PERSON> for backup list item."}, "ConfigBackupListResponse": {"properties": {"backups": {"items": {"$ref": "#/components/schemas/ConfigBackupListItem"}, "type": "array", "title": "Backups"}, "total_backups": {"type": "integer", "title": "Total Backups"}, "total_size": {"type": "integer", "title": "Total Size"}}, "type": "object", "required": ["backups", "total_backups", "total_size"], "title": "ConfigBackupListResponse", "description": "<PERSON><PERSON><PERSON> for backup list response."}, "ConfigBackupRequest": {"properties": {"server_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Server Ids", "description": "Specific server IDs to backup, or None for all"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Backup description"}}, "type": "object", "title": "ConfigBackupRequest", "description": "Schema for configuration backup request."}, "ConfigBackupResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "backups": {"items": {"$ref": "#/components/schemas/ConfigBackupInfo"}, "type": "array", "title": "Backups"}, "total_backups": {"type": "integer", "title": "Total Backups"}, "failed_backups": {"type": "integer", "title": "Failed Backups"}}, "type": "object", "required": ["success", "message", "backups", "total_backups", "failed_backups"], "title": "ConfigBackupResponse", "description": "Schema for configuration backup response."}, "ConfigClientSummary": {"properties": {"client_id": {"type": "string", "title": "Client Id"}, "email": {"type": "string", "title": "Email"}, "shopee_username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expired_date": {"type": "string", "title": "Expired Date"}, "is_expired": {"type": "boolean", "title": "Is Expired"}}, "type": "object", "required": ["client_id", "email", "expired_date", "is_expired"], "title": "ConfigClientSummary", "description": "Schema for client summary in config."}, "ConfigComparisonResult": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "server_name": {"type": "string", "title": "Server Name"}, "config_clients": {"items": {"$ref": "#/components/schemas/ConfigClientSummary"}, "type": "array", "title": "Config Clients"}, "database_clients": {"items": {"$ref": "#/components/schemas/ConfigClientSummary"}, "type": "array", "title": "Database Clients"}, "only_in_config": {"items": {"$ref": "#/components/schemas/ConfigClientSummary"}, "type": "array", "title": "Only In Config"}, "only_in_database": {"items": {"$ref": "#/components/schemas/ConfigClientSummary"}, "type": "array", "title": "Only In Database"}, "mismatched": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Mismatched"}, "in_sync": {"type": "boolean", "title": "In Sync"}}, "type": "object", "required": ["server_id", "server_name", "config_clients", "database_clients", "only_in_config", "only_in_database", "mismatched", "in_sync"], "title": "ConfigComparisonResult", "description": "Schema for configuration comparison result."}, "ConfigOverview": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "server_name": {"type": "string", "title": "Server Name"}, "server_host": {"type": "string", "title": "Server Host"}, "config_path": {"type": "string", "title": "Config Path"}, "total_clients": {"type": "integer", "title": "Total Clients"}, "active_clients": {"type": "integer", "title": "Active Clients"}, "expired_clients": {"type": "integer", "title": "Expired Clients"}, "last_backup": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Backup"}, "config_valid": {"type": "boolean", "title": "Config <PERSON>"}, "last_updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Updated"}}, "type": "object", "required": ["server_id", "server_name", "server_host", "config_path", "total_clients", "active_clients", "expired_clients", "config_valid"], "title": "ConfigOverview", "description": "Schema for configuration overview."}, "ConfigOverviewResponse": {"properties": {"servers": {"items": {"$ref": "#/components/schemas/ConfigOverview"}, "type": "array", "title": "Servers"}, "total_servers": {"type": "integer", "title": "Total Servers"}, "total_clients": {"type": "integer", "title": "Total Clients"}, "total_active_clients": {"type": "integer", "title": "Total Active Clients"}, "total_expired_clients": {"type": "integer", "title": "Total Expired Clients"}}, "type": "object", "required": ["servers", "total_servers", "total_clients", "total_active_clients", "total_expired_clients"], "title": "ConfigOverviewResponse", "description": "Schema for configuration overview response."}, "ConfigRestoreRequest": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "backup_path": {"type": "string", "title": "Backup Path"}, "create_backup_before_restore": {"type": "boolean", "title": "Create Backup Before Restore", "description": "Create backup before restore", "default": true}}, "type": "object", "required": ["server_id", "backup_path"], "title": "ConfigRestoreRequest", "description": "Schema for configuration restore request."}, "ConfigRestoreResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "backup_created": {"type": "boolean", "title": "Backup Created"}, "restored_at": {"type": "string", "format": "date-time", "title": "Restored At"}}, "type": "object", "required": ["success", "message", "backup_created", "restored_at"], "title": "ConfigRestoreResponse", "description": "Schema for configuration restore response."}, "ConfigSyncRequest": {"properties": {"server_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Server Ids", "description": "Specific server IDs to sync, or None for all"}, "sync_direction": {"type": "string", "title": "Sync Direction", "description": "Direction: 'config_to_db' or 'db_to_config'", "default": "config_to_db"}, "remove_orphaned": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "description": "Remove orphaned entries", "default": false}}, "type": "object", "title": "ConfigSyncRequest", "description": "Schema for configuration sync request."}, "ConfigSyncResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "synced_servers": {"items": {"type": "integer"}, "type": "array", "title": "Synced Servers"}, "failed_servers": {"items": {"type": "integer"}, "type": "array", "title": "Failed Servers"}, "total_synced_clients": {"type": "integer", "title": "Total Synced Clients"}, "total_removed_clients": {"type": "integer", "title": "Total Removed Clients"}, "sync_details": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Sync Details"}}, "type": "object", "required": ["success", "message", "synced_servers", "failed_servers", "total_synced_clients", "total_removed_clients", "sync_details"], "title": "ConfigSyncResponse", "description": "Schema for configuration sync response."}, "ConfigUpdateRequest": {"properties": {"config": {"additionalProperties": true, "type": "object", "title": "Config", "description": "Xray configuration JSON"}, "create_backup": {"type": "boolean", "title": "Create Backup", "description": "Create backup before update", "default": true}}, "type": "object", "required": ["config"], "title": "ConfigUpdateRequest", "description": "Schema for configuration update request."}, "ConfigUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "backup_created": {"type": "boolean", "title": "Backup Created"}, "backup_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Backup Path"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["success", "message", "backup_created", "updated_at"], "title": "ConfigUpdateResponse", "description": "Schema for configuration update response."}, "ConfigValidationResponse": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "server_name": {"type": "string", "title": "Server Name"}, "is_valid": {"type": "boolean", "title": "Is <PERSON>"}, "validation_errors": {"items": {"type": "string"}, "type": "array", "title": "Validation Errors"}, "warnings": {"items": {"type": "string"}, "type": "array", "title": "Warnings"}, "clients_count": {"type": "integer", "title": "Clients Count"}, "inbounds_count": {"type": "integer", "title": "Inbounds Count"}, "validated_at": {"type": "string", "format": "date-time", "title": "Validated At"}}, "type": "object", "required": ["server_id", "server_name", "is_valid", "validation_errors", "warnings", "clients_count", "inbounds_count", "validated_at"], "title": "ConfigValidationResponse", "description": "Schema for configuration validation response."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ServerConfigResponse": {"properties": {"server_id": {"type": "integer", "title": "Server Id"}, "server_name": {"type": "string", "title": "Server Name"}, "config": {"additionalProperties": true, "type": "object", "title": "Config"}, "clients_count": {"type": "integer", "title": "Clients Count"}, "last_updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Updated"}, "is_valid": {"type": "boolean", "title": "Is <PERSON>"}}, "type": "object", "required": ["server_id", "server_name", "config", "clients_count", "is_valid"], "title": "ServerConfigResponse", "description": "Schema for server configuration response."}, "ServerConnectionTest": {"properties": {"success": {"type": "boolean", "title": "Success"}, "connection_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Connection Time"}, "message": {"type": "string", "title": "Message"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["success", "message"], "title": "ServerConnectionTest", "description": "Schema for connection test results."}, "ServerCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "host": {"type": "string", "title": "Host"}, "port": {"type": "integer", "title": "Port", "default": 22}, "username": {"type": "string", "title": "Username"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "xray_config_path": {"type": "string", "title": "Xray Config Path", "default": "/etc/xray/config.json"}, "xray_service_name": {"type": "string", "title": "Xray Service Name", "default": "xray"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "default": []}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}, "private_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Private Key"}, "private_key_passphrase": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Private Key Passphrase"}}, "type": "object", "required": ["name", "host", "username"], "title": "ServerCreate", "description": "Schema for creating a new server."}, "ServerCredentialsTest": {"properties": {"host": {"type": "string", "title": "Host"}, "port": {"type": "integer", "title": "Port", "default": 22}, "username": {"type": "string", "title": "Username"}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}, "private_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Private Key"}, "private_key_passphrase": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Private Key Passphrase"}}, "type": "object", "required": ["host", "username"], "title": "ServerCredentialsTest", "description": "Schema for testing server credentials before creation."}, "ServerResponse": {"properties": {"name": {"type": "string", "title": "Name"}, "host": {"type": "string", "title": "Host"}, "port": {"type": "integer", "title": "Port", "default": 22}, "username": {"type": "string", "title": "Username"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "xray_config_path": {"type": "string", "title": "Xray Config Path", "default": "/etc/xray/config.json"}, "xray_service_name": {"type": "string", "title": "Xray Service Name", "default": "xray"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "default": []}, "id": {"type": "integer", "title": "Id"}, "last_connected": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Connected"}, "last_health_check": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Health Check"}, "health_status": {"type": "string", "title": "Health Status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "host", "username", "id", "health_status", "created_at", "updated_at"], "title": "ServerResponse", "description": "Schema for server response."}, "ServerUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "host": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Host"}, "port": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Port"}, "username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}, "private_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Private Key"}, "private_key_passphrase": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Private Key Passphrase"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "xray_config_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Xray Config Path"}, "xray_service_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Xray Service Name"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}}, "type": "object", "title": "ServerUpdate", "description": "Schema for updating server information."}, "Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type"}, "expires_in": {"type": "integer", "title": "Expires In"}}, "type": "object", "required": ["access_token", "token_type", "expires_in"], "title": "Token", "description": "Schema for authentication token."}, "UserCreate": {"properties": {"username": {"type": "string", "title": "Username"}, "email": {"type": "string", "format": "email", "title": "Email"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["username", "email", "password"], "title": "UserCreate", "description": "Schema for creating a new user."}, "UserLogin": {"properties": {"username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["username", "password"], "title": "UserLogin", "description": "Schema for user login."}, "UserResponse": {"properties": {"username": {"type": "string", "title": "Username"}, "email": {"type": "string", "format": "email", "title": "Email"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name"}, "id": {"type": "integer", "title": "Id"}, "is_active": {"type": "boolean", "title": "Is Active"}, "is_superuser": {"type": "boolean", "title": "Is Superuser"}, "last_login": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login"}, "login_count": {"type": "integer", "title": "Login <PERSON>"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["username", "email", "id", "is_active", "is_superuser", "login_count", "created_at", "updated_at"], "title": "UserResponse", "description": "Schema for user response (excluding sensitive data)."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}