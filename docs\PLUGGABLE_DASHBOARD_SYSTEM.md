# Pluggable Dashboard System

## Overview

The SteamCodeTool now features a fully pluggable dashboard system where widgets are dynamically loaded based on enabled plugins. When a plugin is disabled through the plugin manager, its corresponding widgets automatically disappear from the dashboard.

## Architecture

### Core Components

1. **Plugin Manager (`core/plugin_manager.py`)**
   - `DashboardWidget` class for widget configuration
   - `get_dashboard_widgets()` method in PluginInterface
   - `get_widget_data()` method for widget data fetching
   - Widget management methods in PluginManager

2. **Widget Loader (`static/js/widget-loader.js`)**
   - `DashboardWidgetLoader` class for dynamic widget loading
   - `DashboardWidget` base class for all widgets
   - Auto-refresh functionality
   - Widget lifecycle management

3. **Dashboard Template (`templates/dashboard.html`)**
   - Dynamic widget containers
   - Widget position management (header, main, sidebar, footer)
   - Global event handlers

4. **API Endpoints (`api/admin_routes.py`)**
   - `/admin/api/dashboard/widgets` - Get all dashboard widgets
   - `/admin/api/widget/<plugin_name>/<widget_id>` - Get widget data

## Creating a Dashboard Widget

### 1. Plugin Implementation

In your plugin's `plugin.py`:

```python
from core.plugin_manager import PluginInterface, DashboardWidget

class Plugin(PluginInterface):
    def get_dashboard_widgets(self) -> list:
        """Return list of dashboard widgets"""
        widgets = []
        
        # Create a widget
        my_widget = DashboardWidget(
            widget_id="my-widget",
            title="My Widget Title",
            position="main",  # header, main, sidebar, footer
            order=30,  # Lower = higher priority
            size="medium"  # small, medium, large, full
        )
        my_widget.data_endpoint = "/api/myplugin/widget/data"
        my_widget.refresh_interval = 60  # Seconds (None = no auto-refresh)
        widgets.append(my_widget)
        
        return widgets
    
    def get_widget_data(self, widget_id: str) -> dict:
        """Return data for a specific widget"""
        if widget_id == "my-widget":
            return {
                "stats": self._calculate_stats(),
                "last_update": datetime.now().isoformat()
            }
        return {"error": "Unknown widget"}
```

### 2. JavaScript Widget Implementation

Create a widget file in `static/js/widgets/<widget-id>.js`:

```javascript
class MyWidget extends window.DashboardWidget {
    constructor(config) {
        super(config);
        this.myData = null;
    }

    async loadData() {
        try {
            const response = await fetch(this.config.data_endpoint);
            const result = await response.json();
            
            if (result.success) {
                this.myData = result.data;
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load data');
            }
        } catch (error) {
            console.error('Error loading widget data:', error);
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container || !this.myData) return;

        this.container.innerHTML = `
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <h3 class="text-lg font-medium text-gray-900">${this.config.title}</h3>
                    <div class="mt-3">
                        <!-- Your widget content here -->
                        <p>Stats: ${this.myData.stats}</p>
                    </div>
                </div>
            </div>
        `;
    }

    shouldRefresh() {
        // Optional: Custom refresh logic
        if (!this.lastRefresh) return true;
        const now = new Date();
        const diff = now - this.lastRefresh;
        return diff > 60 * 1000; // Refresh every minute
    }

    destroy() {
        // Optional: Cleanup when widget is removed
    }
}

// Register widget
window.DashboardWidgets['my-widget'] = MyWidget;
```

### 3. Plugin Routes

Add widget data endpoints to your plugin's routes:

```python
@self.blueprint.route('/widget/data')
def widget_data():
    data = self._get_widget_data()
    return jsonify({"success": True, "data": data})
```

## Widget Positions and Sizes

### Positions
- `header`: Top of dashboard
- `main`: Main content area (grid layout)
- `sidebar`: Right sidebar
- `footer`: Bottom of dashboard

### Sizes
- `small`: 1 column
- `medium`: 2 columns
- `large`: 3 columns
- `full`: Full width

## Widget Lifecycle

1. **Initialization**
   - Widget loader fetches available widgets from API
   - Widget JavaScript files are dynamically loaded
   - Widget instances are created

2. **Data Loading**
   - Initial data load via `loadData()`
   - Auto-refresh based on `refresh_interval`
   - Manual refresh via `refresh()` method

3. **Rendering**
   - `render()` method updates DOM
   - Event handlers attached after rendering

4. **Destruction**
   - `destroy()` called when widget removed
   - Cleanup of event handlers and resources

## Best Practices

1. **Performance**
   - Implement efficient data fetching
   - Use appropriate refresh intervals
   - Clean up resources in `destroy()`

2. **Error Handling**
   - Always catch and display errors
   - Use `showError()` for user-friendly messages
   - Log errors to console for debugging

3. **Responsive Design**
   - Use Tailwind CSS classes
   - Test on different screen sizes
   - Consider mobile experience

4. **Data Management**
   - Cache data when appropriate
   - Implement loading states
   - Handle empty states gracefully

## Example Widgets

### Core Plugin Widgets
- **Core Statistics**: Email configs, Steam credentials, daily redeems
- **Health Status**: API service health monitoring
- **Redeem Statistics**: Account and order redeem counts
- **Auth Code Records**: Steam authentication code history

### Steam Plugin Widget
- **Steam Cooldowns**: Active session cooldowns with reset functionality

### Netflix Plugin Widget
- **Netflix Cooldowns**: Session cooldowns management

## Testing

Use the test script to verify your widget implementation:

```bash
python test_pluggable_dashboard.py
```

This will test:
- Widget API endpoints
- Plugin status
- Widget data fetching

## Troubleshooting

### Widget Not Appearing
1. Check plugin is enabled in plugin manager
2. Verify widget JavaScript file exists
3. Check browser console for errors
4. Ensure widget is registered: `window.DashboardWidgets['widget-id']`

### Data Not Loading
1. Check API endpoint is correct
2. Verify authentication/permissions
3. Check network tab for API errors
4. Ensure data format matches expectations

### Auto-refresh Not Working
1. Verify `refresh_interval` is set
2. Check `shouldRefresh()` implementation
3. Look for JavaScript errors in console

## Security Considerations

1. **Authentication**: All widget endpoints should require authentication
2. **Permissions**: Implement widget-level permissions if needed
3. **Data Validation**: Validate all data before rendering
4. **XSS Prevention**: Escape user-generated content 