<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        .btn-loading {
            position: relative;
            pointer-events: none;
        }
        
        .btn-loading .fas.fa-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .connection-testing-card {
            border: 2px solid #17a2b8;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .test-button {
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .test-button:disabled {
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h3>VPN Button Functionality Test</h3>
                    </div>
                    <div class="card-body">
                        <!-- Test Form Fields -->
                        <div class="form-group">
                            <label for="host">Host/IP Address</label>
                            <input type="text" class="form-control" id="host" value="*************">
                        </div>
                        <div class="form-group">
                            <label for="username">SSH Username</label>
                            <input type="text" class="form-control" id="username" value="root">
                        </div>
                        <div class="form-group">
                            <label for="password">SSH Password</label>
                            <input type="password" class="form-control" id="password" value="test123">
                        </div>
                        
                        <!-- Connection Testing Section -->
                        <div class="card mt-3 mb-3 connection-testing-card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-network-wired"></i> Connection Testing</h5>
                                <small class="text-light">Test your server connectivity before creating</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-primary btn-block test-button" id="testSSHBtn">
                                            <i class="fas fa-plug"></i> Test SSH Connection
                                        </button>
                                        <small class="text-muted d-block mt-1">Verify SSH credentials and connectivity</small>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-success btn-block test-button" id="testXrayBtn">
                                            <i class="fas fa-cogs"></i> Test Xray Service
                                        </button>
                                        <small class="text-muted d-block mt-1">Check Xray configuration and service status</small>
                                    </div>
                                </div>
                                <div id="testResults" class="mt-3" style="display: none;">
                                    <div class="alert" id="testAlert" role="alert"></div>
                                    <div id="testDetails" class="small text-muted"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            console.log('Document ready, jQuery version:', $.fn.jquery);
            console.log('SSH button element:', $('#testSSHBtn').length);
            console.log('Xray button element:', $('#testXrayBtn').length);

            // Test SSH Connection
            $('#testSSHBtn').click(function () {
                console.log('SSH Test button clicked!');
                alert('SSH Test button clicked! Check console for details.');
                
                const btn = $(this);
                const originalText = btn.html();

                // Show loading state
                btn.html('<i class="fas fa-spinner fa-spin"></i> Testing SSH Connection...');
                btn.prop('disabled', true);
                btn.addClass('btn-loading');

                // Simulate API call
                setTimeout(function() {
                    showTestResult('success', 'SSH Connection Test Successful!', {
                        host: $('#host').val(),
                        username: $('#username').val(),
                        status: 'Connected'
                    });
                    
                    btn.html(originalText);
                    btn.prop('disabled', false);
                    btn.removeClass('btn-loading');
                }, 2000);
            });

            // Test Xray Service
            $('#testXrayBtn').click(function () {
                console.log('Xray Test button clicked!');
                alert('Xray Test button clicked! Check console for details.');
                
                const btn = $(this);
                const originalText = btn.html();

                // Show loading state
                btn.html('<i class="fas fa-spinner fa-spin"></i> Testing Xray Service...');
                btn.prop('disabled', true);
                btn.addClass('btn-loading');

                // Simulate API call
                setTimeout(function() {
                    showTestResult('warning', 'Xray Service Issues Found', {
                        config_exists: false,
                        service_active: false,
                        message: 'This is a simulated test result'
                    });
                    
                    btn.html(originalText);
                    btn.prop('disabled', false);
                    btn.removeClass('btn-loading');
                }, 2000);
            });

            function showTestResult(type, message, details) {
                console.log('Showing test result:', type, message, details);
                
                const alertClass = type === 'success' ? 'alert-success' :
                    type === 'warning' ? 'alert-warning' : 'alert-danger';

                const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-times-circle';

                $('#testAlert').removeClass('alert-success alert-warning alert-danger')
                    .addClass(alertClass)
                    .html(`<i class="${icon}"></i> ${message}`);

                if (details) {
                    let detailsHtml = '<strong>Details:</strong><br>';
                    if (typeof details === 'object') {
                        for (const [key, value] of Object.entries(details)) {
                            detailsHtml += `<strong>${key}:</strong> ${value}<br>`;
                        }
                    } else {
                        detailsHtml += details;
                    }
                    $('#testDetails').html(detailsHtml);
                } else {
                    $('#testDetails').empty();
                }

                $('#testResults').show();

                // Auto-hide success messages after 5 seconds
                if (type === 'success') {
                    setTimeout(() => {
                        $('#testResults').fadeOut();
                    }, 5000);
                }
            }
        });
    </script>
</body>
</html>