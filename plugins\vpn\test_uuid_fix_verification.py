#!/usr/bin/env python3
"""
Verification script to test if the VPN server creation template buttons are working
"""

import os
import sys

def check_template_inline_handlers():
    """Check if inline onclick handlers are present in the template"""
    template_path = os.path.join(os.path.dirname(__file__), 'templates', 'vpn_server_form.html')
    
    if not os.path.exists(template_path):
        print("❌ Template file not found")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for inline handlers
    ssh_inline = 'onclick="console.log(\'SSH INLINE CLICK!\')' in content
    xray_inline = 'onclick="console.log(\'XRAY INLINE CLICK!\')' in content
    
    print(f"✅ SSH inline handler present: {ssh_inline}")
    print(f"✅ Xray inline handler present: {xray_inline}")
    
    # Check for immediate script test
    immediate_test = 'IMMEDIATE SCRIPT TEST' in content
    print(f"✅ Immediate script test present: {immediate_test}")
    
    # Check for button IDs
    ssh_id = 'id="testSSHBtn"' in content
    xray_id = 'id="testXrayBtn"' in content
    
    print(f"✅ SSH button ID present: {ssh_id}")
    print(f"✅ Xray button ID present: {xray_id}")
    
    return ssh_inline and xray_inline and immediate_test and ssh_id and xray_id

def print_troubleshooting_steps():
    """Print troubleshooting steps for the user"""
    print("\n" + "="*60)
    print("🔧 TROUBLESHOOTING STEPS")
    print("="*60)
    
    print("\n1. 📋 When you load the VPN server creation page, you should see:")
    print("   - An alert popup: 'JavaScript is working! Check console for details.'")
    print("   - Console logs showing script execution")
    
    print("\n2. 🖱️ When you click the SSH or Xray buttons, you should see:")
    print("   - Alert popup: 'SSH Button Clicked (inline)!' or 'Xray Button Clicked (inline)!'")
    print("   - Console log: 'SSH INLINE CLICK!' or 'XRAY INLINE CLICK!'")
    
    print("\n3. 🔍 If nothing happens when clicking buttons:")
    print("   - Open browser Developer Tools (F12)")
    print("   - Go to Console tab")
    print("   - Look for any JavaScript errors (red text)")
    print("   - Check if the page is loading completely")
    
    print("\n4. 🌐 If no JavaScript runs at all:")
    print("   - Check if JavaScript is enabled in your browser")
    print("   - Try refreshing the page (Ctrl+F5)")
    print("   - Try a different browser")
    print("   - Check if there are any browser extensions blocking JavaScript")
    
    print("\n5. 🔗 Test the standalone version:")
    print("   - Open plugins/vpn/test_standalone_buttons.html in your browser")
    print("   - This should work independently of the Flask application")
    
    print("\n6. 📝 Check Flask template rendering:")
    print("   - View page source in browser")
    print("   - Look for the onclick handlers in the button elements")
    print("   - Verify the JavaScript code is present in the <script> tags")

def main():
    """Main verification function"""
    print("🔍 VPN Server Creation Template - Button Functionality Verification")
    print("="*70)
    
    # Check template content
    if check_template_inline_handlers():
        print("\n✅ Template verification PASSED - All required elements are present")
    else:
        print("\n❌ Template verification FAILED - Some elements are missing")
        return False
    
    # Print troubleshooting steps
    print_troubleshooting_steps()
    
    print("\n" + "="*60)
    print("📍 CURRENT STATUS")
    print("="*60)
    print("✅ Template has inline onclick handlers")
    print("✅ Template has immediate JavaScript test")
    print("✅ Template has proper button IDs")
    print("✅ Template has comprehensive debugging")
    
    print("\n🎯 EXPECTED BEHAVIOR:")
    print("- Page load → Alert popup + console logs")
    print("- Button click → Alert popup + console logs")
    print("- If this doesn't happen, follow troubleshooting steps above")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)