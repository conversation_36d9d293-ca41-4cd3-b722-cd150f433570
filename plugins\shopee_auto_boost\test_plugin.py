"""
Test script for Shopee Auto Boost Plugin

This script tests the plugin functionality without running the full application.
"""

import sys
import os
import json
import logging
from unittest.mock import Mock, MagicMock

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_mock_shopee_plugin():
    """Create a mock Shopee plugin for testing"""
    mock_plugin = Mock()
    mock_plugin.session = Mock()
    
    # Mock the common params method
    mock_plugin._get_common_params.return_value = {
        "SPC_CDS": "test-spc-cds",
        "SPC_CDS_VER": "2"
    }
    
    return mock_plugin


def create_mock_product_data():
    """Create mock product data for testing"""
    return [
        {
            "id": 29577128498,
            "name": "Test Product 1",
            "status": 1,
            "tag": {"unlist": False},
            "boost_info": {"boost_entry_status": 1},
            "stock_detail": {"total_available_stock": 100},
            "statistics": {"sold_count": 50},
            "price_detail": {"price_min": "10.00", "price_max": "15.00"}
        },
        {
            "id": 29577128499,
            "name": "Test Product 2",
            "status": 1,
            "tag": {"unlist": False},
            "boost_info": {"boost_entry_status": 1},
            "stock_detail": {"total_available_stock": 200},
            "statistics": {"sold_count": 75},
            "price_detail": {"price_min": "20.00", "price_max": "25.00"}
        },
        {
            "id": 29577128500,
            "name": "Test Product 3 (Unlisted)",
            "status": 1,
            "tag": {"unlist": True},
            "boost_info": {"boost_entry_status": 1},
            "stock_detail": {"total_available_stock": 50},
            "statistics": {"sold_count": 25},
            "price_detail": {"price_min": "5.00", "price_max": "8.00"}
        }
    ]


def test_product_service():
    """Test the ProductService functionality"""
    logger.info("Testing ProductService...")
    
    try:
        from plugins.shopee_auto_boost.services.product_service import ProductService
        
        # Create mock shopee plugin
        mock_shopee = create_mock_shopee_plugin()
        
        # Mock API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "code": 0,
            "data": {
                "products": create_mock_product_data(),
                "page_info": {"total": 3}
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_shopee.session.get.return_value = mock_response
        
        # Test configuration
        config = {
            "product_filters": {
                "min_stock": 1,
                "exclude_unlisted": True,
                "exclude_inactive": True
            }
        }
        
        # Create service
        service = ProductService(mock_shopee, config)
        
        # Test filtering
        all_products = create_mock_product_data()
        boostable = service._filter_boostable_products(all_products)
        
        # Should filter out the unlisted product
        assert len(boostable) == 2, f"Expected 2 boostable products, got {len(boostable)}"
        
        # Test product summary
        summary = service.get_product_summary(all_products[0])
        assert summary["id"] == 29577128498
        assert "Test Product 1" in summary["name"]
        
        logger.info("✅ ProductService tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ ProductService test failed: {e}")
        return False


def test_boost_service():
    """Test the BoostService functionality"""
    logger.info("Testing BoostService...")
    
    try:
        from plugins.shopee_auto_boost.services.boost_service import BoostService
        
        # Create mock shopee plugin
        mock_shopee = create_mock_shopee_plugin()
        
        # Mock successful boost response
        mock_response = Mock()
        mock_response.json.return_value = {"code": 0, "message": "success"}
        mock_response.raise_for_status.return_value = None
        mock_shopee.session.post.return_value = mock_response
        
        # Test configuration
        config = {
            "products_per_boost": 2,
            "rotation_strategy": "least_recently_boosted"
        }
        
        # Create service
        service = BoostService(mock_shopee, config)
        
        # Test single product boost
        success = service.boost_single_product(29577128498)
        assert success, "Single product boost should succeed"
        
        # Test product selection
        products = create_mock_product_data()[:2]  # Use first 2 products
        history = {}
        selected = service.select_products_for_boost(products, history)
        assert len(selected) == 2, f"Expected 2 selected products, got {len(selected)}"
        
        logger.info("✅ BoostService tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ BoostService test failed: {e}")
        return False


def test_plugin_initialization():
    """Test plugin initialization"""
    logger.info("Testing Plugin initialization...")
    
    try:
        from plugins.shopee_auto_boost.plugin import Plugin
        from core.plugin_manager import PluginManager
        
        # Create mock plugin manager
        mock_manager = Mock()
        mock_manager.get_plugin.return_value = create_mock_shopee_plugin()
        
        # Create plugin instance
        plugin = Plugin(mock_manager)
        
        # Test basic properties
        assert plugin.name == "shopee_auto_boost"
        assert plugin.version == "1.0.0"
        assert "shopee" in plugin.dependencies
        
        # Test config schema
        schema = plugin.get_config_schema()
        assert "boost_interval_hours" in schema["properties"]
        assert "products_per_boost" in schema["properties"]
        
        logger.info("✅ Plugin initialization tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Plugin initialization test failed: {e}")
        return False


def main():
    """Run all tests"""
    logger.info("Starting Shopee Auto Boost Plugin tests...")
    
    tests = [
        test_product_service,
        test_boost_service,
        test_plugin_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    logger.info(f"\n{'='*50}")
    logger.info(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
