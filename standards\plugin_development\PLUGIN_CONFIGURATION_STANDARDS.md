# Plugin Configuration Standards

This document defines the standards for plugin configuration management, schema definition, and configuration validation in the SteamCodeTool system.

## 📋 Configuration Schema Standards

### 1. Standard Schema Structure

All plugins must provide a JSON schema that follows this structure:

```python
def get_config_schema(self) -> Dict[str, Any]:
    """Standard configuration schema structure"""
    return {
        "type": "object",
        "properties": {
            # Core plugin settings (REQUIRED)
            "enabled": {
                "type": "boolean",
                "default": True,
                "description": "Enable/disable the plugin"
            },
            "debug": {
                "type": "boolean", 
                "default": False,
                "description": "Enable debug logging for this plugin"
            },
            
            # Service-specific configuration (OPTIONAL)
            "service_config": {
                "type": "object",
                "properties": {
                    "timeout": {
                        "type": "integer",
                        "default": 30,
                        "minimum": 1,
                        "maximum": 300,
                        "description": "Service timeout in seconds"
                    },
                    "retry_attempts": {
                        "type": "integer",
                        "default": 3,
                        "minimum": 0,
                        "maximum": 10,
                        "description": "Number of retry attempts for failed operations"
                    },
                    "retry_delay": {
                        "type": "number",
                        "default": 1.0,
                        "minimum": 0.1,
                        "maximum": 60.0,
                        "description": "Delay between retry attempts in seconds"
                    }
                }
            },
            
            # API configuration (OPTIONAL)
            "api_config": {
                "type": "object",
                "properties": {
                    "base_url": {
                        "type": "string",
                        "format": "uri",
                        "description": "Base URL for external API"
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for authentication"
                    },
                    "rate_limit": {
                        "type": "integer",
                        "default": 100,
                        "minimum": 1,
                        "description": "API rate limit per minute"
                    }
                }
            },
            
            # Database configuration (OPTIONAL)
            "database_config": {
                "type": "object",
                "properties": {
                    "connection_string": {
                        "type": "string",
                        "description": "Database connection string"
                    },
                    "pool_size": {
                        "type": "integer",
                        "default": 5,
                        "minimum": 1,
                        "maximum": 50,
                        "description": "Database connection pool size"
                    }
                }
            },
            
            # Feature flags (OPTIONAL)
            "features": {
                "type": "object",
                "properties": {
                    "feature_name": {
                        "type": "boolean",
                        "default": False,
                        "description": "Enable/disable specific feature"
                    }
                }
            }
        },
        "required": ["enabled"],
        "additionalProperties": False
    }
```

### 2. Configuration Validation

#### Schema Validation Implementation
```python
import jsonschema
from typing import Dict, Any

class ConfigurationValidator:
    """Validates plugin configuration against schema"""
    
    def __init__(self, schema: Dict[str, Any]):
        self.schema = schema
        self.validator = jsonschema.Draft7Validator(schema)
    
    def validate(self, config: Dict[str, Any]) -> tuple[bool, list]:
        """Validate configuration and return (is_valid, errors)"""
        errors = list(self.validator.iter_errors(config))
        return len(errors) == 0, [error.message for error in errors]
    
    def validate_and_apply_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate config and apply default values"""
        # Apply defaults
        config_with_defaults = self._apply_defaults(config, self.schema)
        
        # Validate
        is_valid, errors = self.validate(config_with_defaults)
        if not is_valid:
            raise ConfigurationError(f"Invalid configuration: {errors}")
        
        return config_with_defaults
    
    def _apply_defaults(self, config: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively apply default values from schema"""
        result = config.copy()
        
        if schema.get("type") == "object" and "properties" in schema:
            for prop, prop_schema in schema["properties"].items():
                if prop not in result and "default" in prop_schema:
                    result[prop] = prop_schema["default"]
                elif prop in result and prop_schema.get("type") == "object":
                    result[prop] = self._apply_defaults(result[prop], prop_schema)
        
        return result
```

#### Plugin Configuration Loading
```python
class Plugin(PluginInterface):
    def load_config(self, config: Dict[str, Any]):
        """Load and validate plugin configuration"""
        try:
            # Get schema
            schema = self.get_config_schema()
            
            # Validate and apply defaults
            validator = ConfigurationValidator(schema)
            self.config = validator.validate_and_apply_defaults(config)
            
            # Update services with new config
            self._update_service_configs()
            
            self.logger.info(f"Configuration loaded successfully for {self.name}")
            
        except ConfigurationError as e:
            self.logger.error(f"Configuration error in {self.name}: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error loading config for {self.name}: {e}")
            raise ConfigurationError(f"Failed to load configuration: {e}")
```

## 🔧 Configuration Management Patterns

### 1. Environment-Specific Configuration

```python
def get_config_schema(self) -> Dict[str, Any]:
    """Schema with environment-specific settings"""
    return {
        "type": "object",
        "properties": {
            "environment": {
                "type": "string",
                "enum": ["development", "staging", "production"],
                "default": "development",
                "description": "Deployment environment"
            },
            "development": {
                "type": "object",
                "properties": {
                    "debug": {"type": "boolean", "default": True},
                    "log_level": {"type": "string", "default": "DEBUG"}
                }
            },
            "production": {
                "type": "object", 
                "properties": {
                    "debug": {"type": "boolean", "default": False},
                    "log_level": {"type": "string", "default": "INFO"}
                }
            }
        }
    }

def _get_environment_config(self) -> Dict[str, Any]:
    """Get configuration for current environment"""
    env = self.config.get("environment", "development")
    return self.config.get(env, {})
```

### 2. Sensitive Configuration Handling

```python
import os
from typing import Optional

class SecureConfigManager:
    """Handles sensitive configuration data"""
    
    @staticmethod
    def get_secret(key: str, default: Optional[str] = None) -> Optional[str]:
        """Get secret from environment variables or config"""
        # Try environment variable first
        env_key = f"PLUGIN_{key.upper()}"
        value = os.getenv(env_key)
        
        if value:
            return value
        
        # Fallback to default
        return default
    
    @staticmethod
    def mask_sensitive_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Mask sensitive values in config for logging"""
        sensitive_keys = ['password', 'api_key', 'secret', 'token']
        masked_config = config.copy()
        
        def mask_dict(d: Dict[str, Any]):
            for key, value in d.items():
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    d[key] = "***MASKED***"
                elif isinstance(value, dict):
                    mask_dict(value)
        
        mask_dict(masked_config)
        return masked_config

# Usage in plugin
class Plugin(PluginInterface):
    def load_config(self, config: Dict[str, Any]):
        # Load sensitive values from environment
        if 'api_config' in config:
            config['api_config']['api_key'] = SecureConfigManager.get_secret(
                'api_key', 
                config['api_config'].get('api_key')
            )
        
        # Log masked config
        masked_config = SecureConfigManager.mask_sensitive_config(config)
        self.logger.info(f"Loading config: {masked_config}")
        
        super().load_config(config)
```

### 3. Dynamic Configuration Updates

```python
class Plugin(PluginInterface):
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """Update plugin configuration at runtime"""
        try:
            # Validate new configuration
            schema = self.get_config_schema()
            validator = ConfigurationValidator(schema)
            validated_config = validator.validate_and_apply_defaults(new_config)
            
            # Store old config for rollback
            old_config = self.config.copy()
            
            try:
                # Apply new configuration
                self.config = validated_config
                self._update_service_configs()
                
                # Test configuration
                if not self._test_configuration():
                    raise ConfigurationError("Configuration test failed")
                
                self.logger.info(f"Configuration updated successfully for {self.name}")
                return True
                
            except Exception as e:
                # Rollback on failure
                self.config = old_config
                self._update_service_configs()
                raise ConfigurationError(f"Failed to apply new configuration: {e}")
                
        except Exception as e:
            self.logger.error(f"Error updating configuration for {self.name}: {e}")
            return False
    
    def _test_configuration(self) -> bool:
        """Test if current configuration is working"""
        try:
            # Perform configuration-specific tests
            for service_name, service in self.services.items():
                if hasattr(service, 'test_configuration'):
                    if not service.test_configuration():
                        return False
            return True
        except Exception:
            return False
```

## 📁 Configuration File Management

### 1. Configuration File Structure

```
configs/
├── core/
│   └── plugin_config.json          # Main plugin configuration
├── plugins/
│   ├── plugin_name/
│   │   ├── config.json             # Plugin-specific config
│   │   ├── config.development.json # Development overrides
│   │   ├── config.staging.json     # Staging overrides
│   │   └── config.production.json  # Production overrides
│   └── shared/
│       └── common_config.json      # Shared configuration
└── secrets/
    └── plugin_secrets.json         # Sensitive configuration (gitignored)
```

### 2. Configuration Loading Priority

```python
class ConfigurationLoader:
    """Loads configuration with proper priority"""
    
    def load_plugin_config(self, plugin_name: str, environment: str = "development") -> Dict[str, Any]:
        """Load plugin configuration with priority order"""
        config = {}
        
        # 1. Load base configuration
        base_config = self._load_config_file(f"configs/plugins/{plugin_name}/config.json")
        if base_config:
            config.update(base_config)
        
        # 2. Load environment-specific overrides
        env_config = self._load_config_file(f"configs/plugins/{plugin_name}/config.{environment}.json")
        if env_config:
            config.update(env_config)
        
        # 3. Load secrets
        secrets = self._load_config_file(f"configs/secrets/{plugin_name}_secrets.json")
        if secrets:
            config.update(secrets)
        
        # 4. Apply environment variable overrides
        config = self._apply_env_overrides(config, plugin_name)
        
        return config
    
    def _apply_env_overrides(self, config: Dict[str, Any], plugin_name: str) -> Dict[str, Any]:
        """Apply environment variable overrides"""
        prefix = f"{plugin_name.upper()}_"
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                # Convert string values to appropriate types
                config[config_key] = self._convert_env_value(value)
        
        return config
```

### 3. Configuration Backup and Versioning

```python
import json
import shutil
from datetime import datetime
from pathlib import Path

class ConfigurationBackupManager:
    """Manages configuration backups and versioning"""
    
    def __init__(self, backup_dir: str = "backups/configs"):
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    def backup_config(self, plugin_name: str, config: Dict[str, Any]) -> str:
        """Create a backup of plugin configuration"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"{plugin_name}_{timestamp}.json"
        
        with open(backup_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Keep only last 10 backups
        self._cleanup_old_backups(plugin_name)
        
        return str(backup_file)
    
    def restore_config(self, plugin_name: str, backup_file: str) -> Dict[str, Any]:
        """Restore configuration from backup"""
        backup_path = Path(backup_file)
        if not backup_path.exists():
            raise FileNotFoundError(f"Backup file not found: {backup_file}")
        
        with open(backup_path, 'r') as f:
            return json.load(f)
    
    def _cleanup_old_backups(self, plugin_name: str, keep_count: int = 10):
        """Keep only the most recent backups"""
        pattern = f"{plugin_name}_*.json"
        backups = sorted(self.backup_dir.glob(pattern), key=lambda x: x.stat().st_mtime, reverse=True)
        
        for backup in backups[keep_count:]:
            backup.unlink()
```

## 🔍 Configuration Validation Examples

### Complex Validation Rules
```python
def get_config_schema(self) -> Dict[str, Any]:
    """Schema with complex validation rules"""
    return {
        "type": "object",
        "properties": {
            "email_config": {
                "type": "object",
                "properties": {
                    "smtp_host": {"type": "string", "minLength": 1},
                    "smtp_port": {"type": "integer", "minimum": 1, "maximum": 65535},
                    "use_tls": {"type": "boolean", "default": True},
                    "username": {"type": "string", "format": "email"},
                    "password": {"type": "string", "minLength": 8}
                },
                "required": ["smtp_host", "smtp_port", "username", "password"],
                "additionalProperties": False
            },
            "rate_limits": {
                "type": "object",
                "patternProperties": {
                    "^[a-zA-Z_][a-zA-Z0-9_]*$": {
                        "type": "object",
                        "properties": {
                            "requests_per_minute": {"type": "integer", "minimum": 1},
                            "burst_limit": {"type": "integer", "minimum": 1}
                        },
                        "required": ["requests_per_minute"]
                    }
                }
            }
        }
    }
```

This configuration standard ensures consistency and reliability across all plugins while providing flexibility for plugin-specific needs.
