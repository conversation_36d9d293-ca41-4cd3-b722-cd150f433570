#!/usr/bin/env python3
"""
Production Backup and Recovery Manager

This module provides automated backup and recovery capabilities
for the OpenAI Plus Redeem Plugin data and configuration.
"""

import os
import sys
import json
import shutil
import gzip
import tarfile
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import threading
import time
import hashlib

class BackupManager:
    """Automated backup and recovery manager"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "production_config.json"
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.backup_active = False
        
        # Backup configuration
        self.data_config = self.config.get('data_config', {})
        self.data_directory = self.data_config.get('data_directory', 'configs/data/openai_plus_redeem')
        self.backup_directory = os.path.join(self.data_directory, 'backups')
        self.backup_enabled = self.data_config.get('backup_enabled', True)
        self.backup_interval_hours = self.data_config.get('backup_interval_hours', 6)
        self.backup_retention_days = self.data_config.get('backup_retention_days', 30)
        self.max_backup_files = self.data_config.get('max_backup_files', 120)
        self.compression_enabled = self.data_config.get('compression_enabled', True)
        
        # Ensure directories exist
        os.makedirs(self.data_directory, exist_ok=True)
        os.makedirs(self.backup_directory, exist_ok=True)
    
    def _load_config(self) -> Dict[str, Any]:
        """Load production configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Setup backup logging"""
        logger = logging.getLogger('openai_plus_redeem_backup')
        logger.setLevel(logging.INFO)
        
        # File handler
        log_file = "logs/backup.log"
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def start_automated_backup(self):
        """Start automated backup process"""
        if not self.backup_enabled:
            self.logger.info("Automated backup is disabled")
            return
        
        self.backup_active = True
        self.logger.info("Starting automated backup process...")
        
        # Start backup thread
        backup_thread = threading.Thread(target=self._backup_loop, daemon=True)
        backup_thread.start()
        
        self.logger.info("Automated backup started successfully")
    
    def stop_automated_backup(self):
        """Stop automated backup process"""
        self.backup_active = False
        self.logger.info("Automated backup stopped")
    
    def _backup_loop(self):
        """Main backup loop"""
        while self.backup_active:
            try:
                # Create backup
                backup_result = self.create_backup()
                
                if backup_result['success']:
                    self.logger.info(f"Automated backup completed: {backup_result['backup_file']}")
                else:
                    self.logger.error(f"Automated backup failed: {backup_result['error']}")
                
                # Clean up old backups
                self.cleanup_old_backups()
                
                # Sleep until next backup
                sleep_seconds = self.backup_interval_hours * 3600
                time.sleep(sleep_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in backup loop: {e}")
                time.sleep(3600)  # Wait 1 hour before retrying
    
    def create_backup(self, backup_name: str = None) -> Dict[str, Any]:
        """Create a backup of all plugin data"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = backup_name or f"backup_{timestamp}"
            
            if self.compression_enabled:
                backup_file = os.path.join(self.backup_directory, f"{backup_name}.tar.gz")
            else:
                backup_file = os.path.join(self.backup_directory, f"{backup_name}.tar")
            
            self.logger.info(f"Creating backup: {backup_file}")
            
            # Create backup archive
            with tarfile.open(backup_file, 'w:gz' if self.compression_enabled else 'w') as tar:
                # Backup data files
                data_files = [
                    'chatgpt_accounts.json',
                    'order_redemptions.json',
                    'email_verifications.json',
                    'account_cooldowns.json'
                ]
                
                for data_file in data_files:
                    file_path = os.path.join(self.data_directory, data_file)
                    if os.path.exists(file_path):
                        tar.add(file_path, arcname=data_file)
                        self.logger.debug(f"Added to backup: {data_file}")
                
                # Backup configuration
                if os.path.exists(self.config_path):
                    tar.add(self.config_path, arcname='config.json')
                    self.logger.debug("Added configuration to backup")
                
                # Backup plugin files (optional)
                plugin_files = [
                    'plugin.py',
                    'README.md',
                    'requirements.txt'
                ]
                
                plugin_dir = os.path.dirname(os.path.abspath(__file__))
                for plugin_file in plugin_files:
                    file_path = os.path.join(plugin_dir, plugin_file)
                    if os.path.exists(file_path):
                        tar.add(file_path, arcname=f"plugin/{plugin_file}")
                        self.logger.debug(f"Added plugin file to backup: {plugin_file}")
            
            # Calculate backup file hash
            backup_hash = self._calculate_file_hash(backup_file)
            
            # Create backup metadata
            metadata = {
                'backup_name': backup_name,
                'backup_file': backup_file,
                'timestamp': timestamp,
                'size_bytes': os.path.getsize(backup_file),
                'hash': backup_hash,
                'compression': self.compression_enabled,
                'files_included': data_files + ['config.json'] + [f"plugin/{f}" for f in plugin_files]
            }
            
            # Save metadata
            metadata_file = backup_file + '.meta'
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            self.logger.info(f"Backup created successfully: {backup_file} ({metadata['size_bytes']} bytes)")
            
            return {
                'success': True,
                'backup_file': backup_file,
                'metadata': metadata
            }
            
        except Exception as e:
            error_msg = f"Backup creation failed: {e}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def restore_backup(self, backup_file: str, confirm: bool = False) -> Dict[str, Any]:
        """Restore from a backup file"""
        try:
            if not os.path.exists(backup_file):
                raise FileNotFoundError(f"Backup file not found: {backup_file}")
            
            if not confirm:
                return {
                    'success': False,
                    'error': 'Restore operation requires explicit confirmation (confirm=True)'
                }
            
            self.logger.info(f"Starting restore from backup: {backup_file}")
            
            # Create restore point before restoring
            restore_point = self.create_backup(f"restore_point_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            if not restore_point['success']:
                raise Exception(f"Failed to create restore point: {restore_point['error']}")
            
            # Extract backup
            with tarfile.open(backup_file, 'r:gz' if backup_file.endswith('.gz') else 'r') as tar:
                # Extract data files
                data_files = [
                    'chatgpt_accounts.json',
                    'order_redemptions.json', 
                    'email_verifications.json',
                    'account_cooldowns.json'
                ]
                
                restored_files = []
                
                for data_file in data_files:
                    try:
                        tar.extract(data_file, path=self.data_directory)
                        restored_files.append(data_file)
                        self.logger.debug(f"Restored: {data_file}")
                    except KeyError:
                        self.logger.warning(f"File not found in backup: {data_file}")
                
                # Extract configuration if present
                try:
                    tar.extract('config.json', path=os.path.dirname(self.config_path))
                    restored_files.append('config.json')
                    self.logger.debug("Restored: config.json")
                except KeyError:
                    self.logger.warning("Configuration not found in backup")
            
            self.logger.info(f"Restore completed successfully. Restored files: {restored_files}")
            
            return {
                'success': True,
                'restored_files': restored_files,
                'restore_point': restore_point['backup_file']
            }
            
        except Exception as e:
            error_msg = f"Restore failed: {e}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """List all available backups"""
        backups = []
        
        try:
            for file in os.listdir(self.backup_directory):
                if file.endswith(('.tar', '.tar.gz')) and not file.endswith('.meta'):
                    backup_file = os.path.join(self.backup_directory, file)
                    metadata_file = backup_file + '.meta'
                    
                    backup_info = {
                        'file': backup_file,
                        'name': file,
                        'size_bytes': os.path.getsize(backup_file),
                        'created': datetime.fromtimestamp(os.path.getctime(backup_file)).isoformat()
                    }
                    
                    # Load metadata if available
                    if os.path.exists(metadata_file):
                        try:
                            with open(metadata_file, 'r') as f:
                                metadata = json.load(f)
                                backup_info.update(metadata)
                        except Exception as e:
                            self.logger.warning(f"Could not load metadata for {file}: {e}")
                    
                    backups.append(backup_info)
            
            # Sort by creation time (newest first)
            backups.sort(key=lambda x: x['created'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"Error listing backups: {e}")
        
        return backups
    
    def cleanup_old_backups(self):
        """Clean up old backup files"""
        try:
            backups = self.list_backups()
            
            # Remove backups older than retention period
            cutoff_date = datetime.now() - timedelta(days=self.backup_retention_days)
            
            old_backups = [b for b in backups 
                          if datetime.fromisoformat(b['created']) < cutoff_date]
            
            # Remove excess backups (keep only max_backup_files)
            if len(backups) > self.max_backup_files:
                excess_backups = backups[self.max_backup_files:]
                old_backups.extend(excess_backups)
            
            # Remove duplicates
            old_backups = list({b['file']: b for b in old_backups}.values())
            
            for backup in old_backups:
                try:
                    os.remove(backup['file'])
                    
                    # Remove metadata file if exists
                    metadata_file = backup['file'] + '.meta'
                    if os.path.exists(metadata_file):
                        os.remove(metadata_file)
                    
                    self.logger.info(f"Removed old backup: {backup['name']}")
                    
                except Exception as e:
                    self.logger.error(f"Error removing backup {backup['name']}: {e}")
            
            if old_backups:
                self.logger.info(f"Cleaned up {len(old_backups)} old backup(s)")
                
        except Exception as e:
            self.logger.error(f"Error during backup cleanup: {e}")
    
    def verify_backup(self, backup_file: str) -> Dict[str, Any]:
        """Verify backup integrity"""
        try:
            if not os.path.exists(backup_file):
                raise FileNotFoundError(f"Backup file not found: {backup_file}")
            
            # Check if file can be opened
            with tarfile.open(backup_file, 'r:gz' if backup_file.endswith('.gz') else 'r') as tar:
                members = tar.getmembers()
            
            # Verify hash if metadata exists
            metadata_file = backup_file + '.meta'
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                expected_hash = metadata.get('hash')
                if expected_hash:
                    actual_hash = self._calculate_file_hash(backup_file)
                    if actual_hash != expected_hash:
                        raise Exception(f"Hash mismatch: expected {expected_hash}, got {actual_hash}")
            
            return {
                'success': True,
                'message': 'Backup verification successful',
                'files_count': len(members)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Backup verification failed: {e}"
            }
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of a file"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def get_backup_status(self) -> Dict[str, Any]:
        """Get backup system status"""
        backups = self.list_backups()
        
        return {
            'backup_enabled': self.backup_enabled,
            'backup_active': self.backup_active,
            'backup_directory': self.backup_directory,
            'backup_interval_hours': self.backup_interval_hours,
            'backup_retention_days': self.backup_retention_days,
            'total_backups': len(backups),
            'latest_backup': backups[0] if backups else None,
            'total_backup_size_mb': sum(b['size_bytes'] for b in backups) / (1024 * 1024),
            'timestamp': datetime.now().isoformat()
        }

def main():
    """Main backup function"""
    if len(sys.argv) < 2:
        print("Usage: python backup_manager.py <command> [options]")
        print("Commands:")
        print("  create [name]     - Create a backup")
        print("  restore <file>    - Restore from backup (requires --confirm)")
        print("  list              - List all backups")
        print("  cleanup           - Clean up old backups")
        print("  verify <file>     - Verify backup integrity")
        print("  status            - Show backup system status")
        print("  start-auto        - Start automated backup")
        sys.exit(1)
    
    backup_manager = BackupManager()
    command = sys.argv[1]
    
    try:
        if command == 'create':
            backup_name = sys.argv[2] if len(sys.argv) > 2 else None
            result = backup_manager.create_backup(backup_name)
            print(json.dumps(result, indent=2))
            
        elif command == 'restore':
            if len(sys.argv) < 3:
                print("Error: Backup file required")
                sys.exit(1)
            
            backup_file = sys.argv[2]
            confirm = '--confirm' in sys.argv
            result = backup_manager.restore_backup(backup_file, confirm)
            print(json.dumps(result, indent=2))
            
        elif command == 'list':
            backups = backup_manager.list_backups()
            print(json.dumps(backups, indent=2))
            
        elif command == 'cleanup':
            backup_manager.cleanup_old_backups()
            print("Cleanup completed")
            
        elif command == 'verify':
            if len(sys.argv) < 3:
                print("Error: Backup file required")
                sys.exit(1)
            
            backup_file = sys.argv[2]
            result = backup_manager.verify_backup(backup_file)
            print(json.dumps(result, indent=2))
            
        elif command == 'status':
            status = backup_manager.get_backup_status()
            print(json.dumps(status, indent=2))
            
        elif command == 'start-auto':
            backup_manager.start_automated_backup()
            print("Automated backup started. Press Ctrl+C to stop.")
            try:
                while True:
                    time.sleep(60)
            except KeyboardInterrupt:
                backup_manager.stop_automated_backup()
                print("Automated backup stopped")
                
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
