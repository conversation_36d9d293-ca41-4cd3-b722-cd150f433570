# 自动图片尺寸检测功能

## 概述

ShopeeAPI 现在支持自动检测图片的真实尺寸，无需手动指定 width、height、thumb_width 和 thumb_height 参数。系统会自动从图片URL获取真实尺寸并计算合适的缩略图尺寸。

## 功能特性

### 🔍 自动尺寸检测
- **智能检测**: 自动从图片URL获取真实的宽度和高度
- **多格式支持**: 支持 JPEG、PNG、GIF、WebP 等常见图片格式
- **高效下载**: 只下载图片头部信息，不会完整下载大文件
- **安全限制**: 设置10MB下载限制，防止恶意大文件

### 📐 智能缩略图计算
- **比例保持**: 自动计算缩略图尺寸，保持原图宽高比
- **尺寸限制**: 默认最大缩略图尺寸为 330x330 像素
- **智能适配**: 根据图片方向（横向/纵向/正方形）智能调整

### 🛡️ 容错机制
- **优雅降级**: 如果检测失败，自动使用默认尺寸
- **可选功能**: 可以通过参数禁用自动检测
- **手动覆盖**: 手动指定的尺寸会覆盖自动检测结果

## API 使用方法

### 基本用法（推荐）

```json
{
  "username": "buyer_username",
  "image_url": "https://example.com/image.jpg"
}
```

系统会自动：
1. 检测图片真实尺寸（例如：800x600）
2. 计算合适的缩略图尺寸（例如：330x248）
3. 发送包含正确尺寸信息的消息

### 禁用自动检测

```json
{
  "username": "buyer_username", 
  "image_url": "https://example.com/image.jpg",
  "auto_detect_dimensions": false,
  "width": 800,
  "height": 600,
  "thumb_width": 244,
  "thumb_height": 183
}
```

### 部分手动指定

```json
{
  "username": "buyer_username",
  "image_url": "https://example.com/image.jpg", 
  "width": 1200,
  "height": 800
}
```

在这种情况下：
- 使用手动指定的 width 和 height
- 自动计算 thumb_width 和 thumb_height

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `username` | string | 必填 | 接收者用户名 |
| `image_url` | string | 必填 | 图片URL |
| `auto_detect_dimensions` | boolean | `true` | 是否启用自动尺寸检测 |
| `width` | integer | `null` | 图片宽度（自动检测或手动指定） |
| `height` | integer | `null` | 图片高度（自动检测或手动指定） |
| `thumb_width` | integer | `null` | 缩略图宽度（自动计算或手动指定） |
| `thumb_height` | integer | `null` | 缩略图高度（自动计算或手动指定） |
| `url_hash` | string | `""` | 图片哈希值 |
| `thumb_url` | string | `image_url` | 缩略图URL |
| `file_server_id` | integer | `0` | 文件服务器ID |

## 工作流程

```mermaid
graph TD
    A[发送图片消息] --> B{auto_detect_dimensions?}
    B -->|true| C{width/height 已指定?}
    B -->|false| H[使用手动指定的尺寸]
    C -->|否| D[从URL检测图片尺寸]
    C -->|是| I[使用指定的尺寸]
    D --> E{检测成功?}
    E -->|是| F[使用检测到的尺寸]
    E -->|否| G[使用默认尺寸 299x404]
    F --> J{thumb_width/height 已指定?}
    G --> J
    H --> J
    I --> J
    J -->|否| K[自动计算缩略图尺寸]
    J -->|是| L[使用指定的缩略图尺寸]
    K --> M[发送消息到 Shopee]
    L --> M
```

## 默认值

- **图片尺寸**: 299x404 像素（如果检测失败）
- **缩略图尺寸**: 244x330 像素（如果检测失败）
- **最大缩略图**: 330x330 像素
- **下载超时**: 10秒
- **文件大小限制**: 10MB

## 示例场景

### 场景1: 发送商品图片
```json
{
  "username": "customer123",
  "image_url": "https://shop.example.com/product/12345.jpg"
}
```
**结果**: 自动检测商品图片的真实尺寸，确保在聊天中正确显示

### 场景2: 发送高分辨率图片
```json
{
  "username": "customer123", 
  "image_url": "https://example.com/high-res-image.jpg"
}
```
**结果**: 检测到高分辨率（如2000x1500），自动计算合适的缩略图尺寸（330x248）

### 场景3: 处理检测失败的情况
```json
{
  "username": "customer123",
  "image_url": "https://broken-link.com/image.jpg"
}
```
**结果**: 检测失败，自动使用默认尺寸，消息仍能正常发送

## 性能优化

1. **头部检测**: 优先尝试只读取图片头部信息获取尺寸
2. **流式下载**: 使用流式下载，一旦获取到尺寸信息立即停止
3. **缓存机制**: 可以考虑添加尺寸缓存（未来功能）
4. **并发限制**: 避免同时检测过多图片导致性能问题

## 错误处理

- **网络错误**: 自动降级到默认尺寸
- **格式不支持**: 自动降级到默认尺寸  
- **文件过大**: 停止下载，使用默认尺寸
- **超时**: 10秒超时后使用默认尺寸

## 兼容性

- ✅ 向后兼容：现有代码无需修改
- ✅ 可选功能：可以禁用自动检测
- ✅ 手动覆盖：手动指定的值优先级更高

## 依赖

- `Pillow>=9.0.0`: 用于图片处理和尺寸检测
- `requests>=2.28.0`: 用于HTTP请求

## 注意事项

1. **网络依赖**: 需要能够访问图片URL
2. **性能影响**: 首次发送图片时可能有轻微延迟（通常<1秒）
3. **隐私考虑**: 系统会访问图片URL来获取尺寸信息
4. **格式限制**: 仅支持常见的图片格式

---

这个功能大大简化了图片发送流程，确保图片在 Shopee 聊天中以正确的尺寸显示，提升用户体验。
