{% extends "base.html" %}

{% block title %}Shopee Auto Boost - History{% endblock %}

{% block header %}Boost History{% endblock %}

{% block content %}
<div class="container-fluid" x-data="shopeeAutoBoostHistory()">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 x-text="globalStats.total_boost_sessions || 0"></h4>
                    <p class="mb-0">Total Boost Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4 x-text="globalStats.total_products_boosted || 0"></h4>
                    <p class="mb-0">Products Boosted</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4 x-text="globalStats.total_failures || 0"></h4>
                    <p class="mb-0">Failed Boosts</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4 x-text="calculateSuccessRate()"></h4>
                    <p class="mb-0">Success Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Timeline</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <strong>First Boost Session:</strong>
                            <span x-text="formatDateTime(globalStats.first_boost_session) || 'Never'"></span>
                        </li>
                        <li class="mb-2">
                            <strong>Last Boost Session:</strong>
                            <span x-text="formatDateTime(globalStats.last_boost_session) || 'Never'"></span>
                        </li>
                        <li class="mb-2">
                            <strong>Days Active:</strong>
                            <span x-text="calculateDaysActive()"></span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" @click="loadHistory()" :disabled="loading">
                            <i class="fas fa-sync me-2"></i>
                            <span x-show="!loading">Refresh History</span>
                            <span x-show="loading">Loading...</span>
                        </button>
                        <button class="btn btn-success" @click="exportHistory()">
                            <i class="fas fa-download me-2"></i>Export History
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product History Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Product Boost History</h5>
                    <div class="d-flex gap-2">
                        <input type="text" 
                               class="form-control form-control-sm" 
                               x-model="searchTerm" 
                               @input="filterProducts()"
                               placeholder="Search products..."
                               style="width: 200px;">
                        <select class="form-select form-select-sm" 
                                x-model="sortBy" 
                                @change="filterProducts()"
                                style="width: 150px;">
                            <option value="total_boosts">Most Boosted</option>
                            <option value="last_boosted">Recently Boosted</option>
                            <option value="first_boosted">First Boosted</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div x-show="loading" class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading history...</p>
                    </div>

                    <div x-show="!loading && filteredProducts.length === 0" class="text-center text-muted">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <p>No boost history found</p>
                    </div>

                    <div x-show="!loading && filteredProducts.length > 0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Product ID</th>
                                        <th>Total Boosts</th>
                                        <th>First Boosted</th>
                                        <th>Last Boosted</th>
                                        <th>Days Since Last Boost</th>
                                        <th>Boost Frequency</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="product in paginatedProducts" :key="product.product_id">
                                        <tr>
                                            <td>
                                                <code x-text="product.product_id"></code>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary" x-text="product.total_boosts"></span>
                                            </td>
                                            <td x-text="formatDateTime(product.first_boosted)"></td>
                                            <td x-text="formatDateTime(product.last_boosted)"></td>
                                            <td>
                                                <span :class="getDaysSinceClass(product.days_since_last)" 
                                                      x-text="product.days_since_last + ' days'"></span>
                                            </td>
                                            <td>
                                                <small class="text-muted" x-text="product.frequency"></small>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <nav x-show="totalPages > 1">
                            <ul class="pagination justify-content-center">
                                <li class="page-item" :class="{ 'disabled': currentPage === 1 }">
                                    <button class="page-link" @click="currentPage = Math.max(1, currentPage - 1)">Previous</button>
                                </li>
                                
                                <template x-for="page in visiblePages" :key="page">
                                    <li class="page-item" :class="{ 'active': page === currentPage }">
                                        <button class="page-link" @click="currentPage = page" x-text="page"></button>
                                    </li>
                                </template>
                                
                                <li class="page-item" :class="{ 'disabled': currentPage === totalPages }">
                                    <button class="page-link" @click="currentPage = Math.min(totalPages, currentPage + 1)">Next</button>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div x-show="alertMessage" 
         :class="'alert alert-' + alertType + ' alert-dismissible fade show'" 
         role="alert">
        <span x-text="alertMessage"></span>
        <button type="button" class="btn-close" @click="alertMessage = ''" aria-label="Close"></button>
    </div>
</div>

<script>
function shopeeAutoBoostHistory() {
    return {
        globalStats: {},
        recentProducts: [],
        filteredProducts: [],
        searchTerm: '',
        sortBy: 'total_boosts',
        loading: false,
        alertMessage: '',
        alertType: 'info',
        currentPage: 1,
        itemsPerPage: 20,
        
        init() {
            this.loadHistory();
        },
        
        get paginatedProducts() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredProducts.slice(start, end);
        },
        
        get totalPages() {
            return Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        },
        
        get visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },
        
        async loadHistory() {
            this.loading = true;
            try {
                const response = await fetch('/api/shopee_auto_boost/history');
                if (response.ok) {
                    const data = await response.json();
                    this.globalStats = data.global_stats || {};
                    this.recentProducts = data.recent_products || [];
                    this.processProductData();
                    this.filterProducts();
                    this.showAlert('History loaded successfully', 'success');
                } else {
                    this.showAlert('Failed to load history', 'danger');
                }
            } catch (error) {
                this.showAlert('Error loading history: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },
        
        processProductData() {
            this.recentProducts = this.recentProducts.map(product => {
                const lastBoosted = new Date(product.last_boosted);
                const firstBoosted = new Date(product.first_boosted);
                const now = new Date();
                
                const daysSinceLast = Math.floor((now - lastBoosted) / (1000 * 60 * 60 * 24));
                const totalDays = Math.floor((lastBoosted - firstBoosted) / (1000 * 60 * 60 * 24)) + 1;
                const frequency = totalDays > 0 ? (product.total_boosts / totalDays).toFixed(2) + ' boosts/day' : 'N/A';
                
                return {
                    ...product,
                    days_since_last: daysSinceLast,
                    frequency: frequency
                };
            });
        },
        
        filterProducts() {
            let filtered = [...this.recentProducts];
            
            // Apply search filter
            if (this.searchTerm) {
                const term = this.searchTerm.toLowerCase();
                filtered = filtered.filter(product => 
                    product.product_id.toString().includes(term)
                );
            }
            
            // Apply sorting
            filtered.sort((a, b) => {
                switch (this.sortBy) {
                    case 'total_boosts':
                        return b.total_boosts - a.total_boosts;
                    case 'last_boosted':
                        return new Date(b.last_boosted) - new Date(a.last_boosted);
                    case 'first_boosted':
                        return new Date(b.first_boosted) - new Date(a.first_boosted);
                    default:
                        return 0;
                }
            });
            
            this.filteredProducts = filtered;
            this.currentPage = 1;
        },
        
        calculateSuccessRate() {
            const total = (this.globalStats.total_products_boosted || 0) + (this.globalStats.total_failures || 0);
            if (total === 0) return '0%';
            const rate = ((this.globalStats.total_products_boosted || 0) / total * 100).toFixed(1);
            return rate + '%';
        },
        
        calculateDaysActive() {
            if (!this.globalStats.first_boost_session || !this.globalStats.last_boost_session) {
                return '0';
            }
            
            const first = new Date(this.globalStats.first_boost_session);
            const last = new Date(this.globalStats.last_boost_session);
            const days = Math.floor((last - first) / (1000 * 60 * 60 * 24)) + 1;
            return days.toString();
        },
        
        getDaysSinceClass(days) {
            if (days <= 1) return 'badge bg-success';
            if (days <= 7) return 'badge bg-warning';
            return 'badge bg-danger';
        },
        
        formatDateTime(dateString) {
            if (!dateString) return 'Never';
            return new Date(dateString).toLocaleString();
        },
        
        exportHistory() {
            const data = {
                global_stats: this.globalStats,
                product_history: this.recentProducts,
                exported_at: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `shopee_boost_history_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.showAlert('History exported successfully', 'success');
        },
        
        showAlert(message, type) {
            this.alertMessage = message;
            this.alertType = type;
            setTimeout(() => this.alertMessage = '', 5000);
        }
    }
}
</script>
{% endblock %}
