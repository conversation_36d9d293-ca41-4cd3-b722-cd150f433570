"""
Security Audit Logger for SteamCodeTool
Comprehensive logging system for security monitoring and compliance
"""

import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from functools import wraps
from flask import request, g, session
import threading

class SecurityAuditLogger:
    """Enhanced security audit logging system"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.setup_loggers()
        self.lock = threading.Lock()
    
    def setup_loggers(self):
        """Setup different loggers for different types of events"""
        # Ensure logs directory exists
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Security audit logger
        self.security_logger = logging.getLogger('security_audit')
        self.security_logger.setLevel(logging.INFO)
        
        if not self.security_logger.handlers:
            security_handler = logging.FileHandler(
                os.path.join(self.log_dir, 'security_audit.log')
            )
            security_formatter = logging.Formatter(
                '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
            )
            security_handler.setFormatter(security_formatter)
            self.security_logger.addHandler(security_handler)
        
        # Access logger
        self.access_logger = logging.getLogger('access_audit')
        self.access_logger.setLevel(logging.INFO)
        
        if not self.access_logger.handlers:
            access_handler = logging.FileHandler(
                os.path.join(self.log_dir, 'access_audit.log')
            )
            access_formatter = logging.Formatter(
                '%(asctime)s - ACCESS - %(message)s'
            )
            access_handler.setFormatter(access_formatter)
            self.access_logger.addHandler(access_handler)
        
        # Admin actions logger
        self.admin_logger = logging.getLogger('admin_audit')
        self.admin_logger.setLevel(logging.INFO)
        
        if not self.admin_logger.handlers:
            admin_handler = logging.FileHandler(
                os.path.join(self.log_dir, 'admin_audit.log')
            )
            admin_formatter = logging.Formatter(
                '%(asctime)s - ADMIN - %(levelname)s - %(message)s'
            )
            admin_handler.setFormatter(admin_formatter)
            self.admin_logger.addHandler(admin_handler)
        
        # API usage logger
        self.api_logger = logging.getLogger('api_audit')
        self.api_logger.setLevel(logging.INFO)
        
        if not self.api_logger.handlers:
            api_handler = logging.FileHandler(
                os.path.join(self.log_dir, 'api_audit.log')
            )
            api_formatter = logging.Formatter(
                '%(asctime)s - API - %(message)s'
            )
            api_handler.setFormatter(api_formatter)
            self.api_logger.addHandler(api_handler)
    
    def get_client_info(self) -> Dict[str, Any]:
        """Extract client information from request"""
        if not request:
            return {}
        
        # Get IP address
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR')
        if client_ip:
            client_ip = client_ip.split(',')[0].strip()
        else:
            client_ip = request.environ.get('HTTP_X_REAL_IP', 
                                          request.environ.get('REMOTE_ADDR', 'unknown'))
        
        return {
            'ip_address': client_ip,
            'user_agent': request.headers.get('User-Agent', 'unknown'),
            'method': request.method,
            'url': request.url,
            'endpoint': request.endpoint,
            'referrer': request.headers.get('Referer'),
            'timestamp': datetime.utcnow().isoformat(),
            'session_id': session.get('session_id', 'anonymous'),
            'user_id': getattr(g, 'user_id', 'anonymous')
        }
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          level: str = 'INFO'):
        """Log security-related events"""
        client_info = self.get_client_info()
        
        log_entry = {
            'event_type': event_type,
            'client_info': client_info,
            'details': details,
            'severity': level
        }
        
        log_message = json.dumps(log_entry, default=str)
        
        if level.upper() == 'WARNING':
            self.security_logger.warning(log_message)
        elif level.upper() == 'ERROR':
            self.security_logger.error(log_message)
        elif level.upper() == 'CRITICAL':
            self.security_logger.critical(log_message)
        else:
            self.security_logger.info(log_message)
    
    def log_access(self, response_status: int, response_size: int = 0):
        """Log access requests"""
        client_info = self.get_client_info()
        
        log_entry = {
            'client_info': client_info,
            'response_status': response_status,
            'response_size': response_size
        }
        
        log_message = json.dumps(log_entry, default=str)
        self.access_logger.info(log_message)
    
    def log_admin_action(self, action: str, details: Dict[str, Any]):
        """Log administrative actions"""
        client_info = self.get_client_info()
        
        log_entry = {
            'action': action,
            'client_info': client_info,
            'details': details
        }
        
        log_message = json.dumps(log_entry, default=str)
        self.admin_logger.info(log_message)
    
    def log_api_usage(self, endpoint: str, method: str, status_code: int, 
                     response_time: float, details: Optional[Dict[str, Any]] = None):
        """Log API usage"""
        client_info = self.get_client_info()
        
        log_entry = {
            'endpoint': endpoint,
            'method': method,
            'status_code': status_code,
            'response_time_ms': round(response_time * 1000, 2),
            'client_info': client_info,
            'details': details or {}
        }
        
        log_message = json.dumps(log_entry, default=str)
        self.api_logger.info(log_message)
    
    def log_authentication_attempt(self, username: str, success: bool, 
                                  failure_reason: Optional[str] = None):
        """Log authentication attempts"""
        details = {
            'username': username,
            'success': success,
            'failure_reason': failure_reason
        }
        
        event_type = 'authentication_success' if success else 'authentication_failure'
        level = 'INFO' if success else 'WARNING'
        
        self.log_security_event(event_type, details, level)
    
    def log_rate_limit_violation(self, limit_type: str, limit_value: int):
        """Log rate limit violations"""
        details = {
            'limit_type': limit_type,
            'limit_value': limit_value,
            'violation_time': datetime.utcnow().isoformat()
        }
        
        self.log_security_event('rate_limit_violation', details, 'WARNING')
    
    def log_suspicious_activity(self, activity_type: str, details: Dict[str, Any]):
        """Log suspicious activities"""
        self.log_security_event('suspicious_activity', {
            'activity_type': activity_type,
            **details
        }, 'WARNING')
    
    def log_data_access(self, data_type: str, action: str, record_count: int = 1):
        """Log data access events"""
        details = {
            'data_type': data_type,
            'action': action,
            'record_count': record_count
        }
        
        self.log_security_event('data_access', details)
    
    def log_configuration_change(self, config_type: str, old_value: Any, 
                                new_value: Any):
        """Log configuration changes"""
        details = {
            'config_type': config_type,
            'old_value': str(old_value),
            'new_value': str(new_value)
        }
        
        self.log_admin_action('configuration_change', details)

# Global audit logger instance
audit_logger = SecurityAuditLogger()

def audit_access(f):
    """Decorator to audit access to endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = datetime.utcnow()
        
        try:
            result = f(*args, **kwargs)
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            # Get status code
            status_code = getattr(result, 'status_code', 200)
            
            # Log access
            audit_logger.log_access(status_code)
            
            return result
            
        except Exception as e:
            # Log error
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            audit_logger.log_security_event('endpoint_error', {
                'error': str(e),
                'endpoint': request.endpoint,
                'response_time': response_time
            }, 'ERROR')
            
            raise
    
    return decorated_function

def audit_admin_action(action_description: str):
    """Decorator to audit admin actions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                result = f(*args, **kwargs)
                
                # Log successful admin action
                audit_logger.log_admin_action(action_description, {
                    'success': True,
                    'function': f.__name__
                })
                
                return result
                
            except Exception as e:
                # Log failed admin action
                audit_logger.log_admin_action(action_description, {
                    'success': False,
                    'error': str(e),
                    'function': f.__name__
                })
                
                raise
        
        return decorated_function
    return decorator

def audit_api_call(f):
    """Decorator to audit API calls"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = datetime.utcnow()
        
        try:
            result = f(*args, **kwargs)
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            # Get status code
            status_code = getattr(result, 'status_code', 200)
            
            # Log API usage
            audit_logger.log_api_usage(
                endpoint=request.endpoint or 'unknown',
                method=request.method,
                status_code=status_code,
                response_time=response_time
            )
            
            return result
            
        except Exception as e:
            # Log API error
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            audit_logger.log_api_usage(
                endpoint=request.endpoint or 'unknown',
                method=request.method,
                status_code=500,
                response_time=response_time,
                details={'error': str(e)}
            )
            
            raise
    
    return decorated_function