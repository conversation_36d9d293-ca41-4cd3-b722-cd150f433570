from flask import Blueprint, jsonify, request, render_template, session, redirect, url_for
from functools import wraps
import json
import os
import uuid
import random
import string
import requests
import datetime

vpn_bp = Blueprint('vpn', __name__)

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function

@vpn_bp.route('/admin/vpn/servers', methods=['GET'])
@login_required
def vpn_servers_page():
    """Render the VPN servers management page"""
    return render_template('vpn_servers.html')

VPN_SERVERS_FILE = 'configs/services/vpn_servers.json'
CONFIG_TEMPLATES_FILE = 'configs/services/config_templates.json'

def load_vpn_servers():
    if os.path.exists(VPN_SERVERS_FILE):
        with open(VPN_SERVERS_FILE, 'r') as f:
            return json.load(f)
    return []

def save_vpn_servers(servers):
    with open(VPN_SERVERS_FILE, 'w') as f:
        json.dump(servers, f, indent=2)

@vpn_bp.route('/admin/vpn/get_servers', methods=['GET'])
@login_required
def get_servers():
    try:
        servers = load_vpn_servers()
        return jsonify({"success": True, "servers": servers})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@vpn_bp.route('/admin/vpn/servers', methods=['POST'])
@login_required
def add_server():
    try:
        server = request.json
        servers = load_vpn_servers()
        
        # Validate server connection before adding
        if check_server_connection(server):
            servers.append(server)
            save_vpn_servers(servers)
            return jsonify({"success": True, "message": "Server added successfully"})
        else:
            return jsonify({"success": False, "message": "Failed to connect to server"}), 400
            
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@vpn_bp.route('/admin/vpn/servers/<int:index>', methods=['PUT'])
@login_required
def update_server(index):
    try:
        server = request.json
        servers = load_vpn_servers()
        
        if 0 <= index < len(servers):
            # Validate server connection before updating
            if check_server_connection(server):
                servers[index] = server
                save_vpn_servers(servers)
                return jsonify({"success": True, "message": "Server updated successfully"})
            else:
                return jsonify({"success": False, "message": "Failed to connect to server"}), 400
        else:
            return jsonify({"success": False, "message": "Server index out of range"}), 404
            
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@vpn_bp.route('/admin/vpn/servers/<int:index>', methods=['DELETE'])
@login_required
def delete_server(index):
    try:
        servers = load_vpn_servers()
        
        if 0 <= index < len(servers):
            servers.pop(index)
            save_vpn_servers(servers)
            return jsonify({"success": True, "message": "Server deleted successfully"})
        else:
            return jsonify({"success": False, "message": "Server index out of range"}), 404
            
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@vpn_bp.route('/admin/vpn/check-connection', methods=['POST'])
@login_required
def check_connection():
    try:
        server = request.json
        server_index = request.json.get('server_index')  # Add this to receive the index
        success = check_server_connection(server)
        
        # Update and save the server status
        if server_index is not None:
            servers = load_vpn_servers()
            if 0 <= server_index < len(servers):
                servers[server_index]['status'] = 'connected' if success else 'disconnected'
                save_vpn_servers(servers)
        
        return jsonify({"success": success})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

def check_server_connection(server):
    """Check if we can connect to the VPN server using the provided credentials"""
    try:
        url = f"http://{server['domain']}:{server['port']}/login"
        payload = f"username={server['username']}&password={server['password']}"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        
        response = requests.post(url, headers=headers, data=payload, timeout=5)
        data = response.json()
        
        return data.get('success', False)
        
    except Exception as e:
        print(f"Error checking server connection: {str(e)}")
        return False

@vpn_bp.route('/admin/vpn/inbounds', methods=['GET'])
@login_required
def vpn_inbounds_page():
    """Render the VPN inbounds management page"""
    return render_template('vpn_inbounds.html')

def make_vpn_request(server, endpoint, method='GET', data=None):
    """Helper function to make requests to a specific VPN server"""
    try:
        url = f"http://{server['domain']}:{server['port']}/panel/api{endpoint}"
        headers = {'Accept': 'application/json', 'Content-Type': 'application/json'}
        
        # Login first to get session
        login_url = f"http://{server['domain']}:{server['port']}/login"
        login_payload = f"username={server['username']}&password={server['password']}"
        login_headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        
        session = requests.Session()
        # Increase timeout for login
        login_response = session.post(login_url, headers=login_headers, data=login_payload, timeout=10)
        
        if not login_response.json().get('success'):
            return {"success": False, "msg": "Failed to authenticate with VPN server"}
        
        # Make the actual request with increased timeout
        if isinstance(data, str):
            # If data is a string, send it directly
            response = session.request(
                method=method,
                url=url,
                headers=headers,
                data=data,
                timeout=15  # Increased timeout
            )
        else:
            # If data is a dict, send it as JSON
            response = session.request(
                method=method,
                url=url,
                headers=headers,
                json=data,
                timeout=15  # Increased timeout
            )
            
        return response.json()
        
    except requests.RequestException as e:
        return {"success": False, "msg": f"VPN API request failed: {str(e)}"}

@vpn_bp.route('/admin/vpn/inbounds/updateClient/<string:client_id>', methods=['POST'])
@login_required
def update_client(client_id):
    try:
        data = request.json
        server_domain = data.pop('server', None)
        server_port = data.pop('serverPort', None)
        inbound_id = data.pop('inboundId', None)
        
        if not all([server_domain, server_port, inbound_id]):
            return jsonify({"success": False, "msg": "Missing required information"}), 400
        
        # Find the server in our list
        servers = load_vpn_servers()
        server = next((s for s in servers if s['domain'] == server_domain and s['port'] == server_port), None)
        
        if not server:
            return jsonify({"success": False, "msg": "Server not found"}), 404

        # Get the current inbound to ensure we update only the specific client
        current_inbound = make_vpn_request(server, f'/inbounds/get/{inbound_id}', 'GET')
        if not current_inbound.get('success'):
            return jsonify({"success": False, "msg": "Failed to get current inbound data"}), 400

        current_settings = json.loads(current_inbound['obj']['settings'])
        current_clients = current_settings.get('clients', [])

        # Update only the specific client while keeping others unchanged
        updated_clients = []
        client_found = False
        for c in current_clients:
            if c['id'] == client_id:
                client_found = True
                # Convert GB to bytes correctly (1 GB = 1024^3 bytes)
                total_gb = float(data.get('totalGB', 0))
                total_bytes = int(total_gb * (1024 ** 3)) if total_gb > 0 else 0
                
                updated_clients.append({
                    "id": client_id,
                    "alterId": 0,
                    "email": data.get('email'),
                    "limitIp": data.get('limitIp', 0),
                    "totalGB": total_bytes,  # Use the correctly converted value
                    "expiryTime": int(datetime.datetime.fromisoformat(data.get('expiryTime')).timestamp() * 1000) if data.get('expiryTime') else 0,
                    "enable": data.get('enable', True),
                    "tgId": "",
                    "subId": c.get('subId', "")  # Preserve existing subId
                })
            else:
                updated_clients.append(c)

        if not client_found:
            return jsonify({"success": False, "msg": "Client not found in inbound"}), 404

        # Format the update payload
        client_data = {
            "id": int(inbound_id),
            "settings": json.dumps({
                "clients": [{  # Only send the client being updated
                    "id": client_id,
                    "alterId": 0,
                    "email": data.get('email'),
                    "limitIp": data.get('limitIp', 3),
                    "totalGB": total_bytes,
                    "expiryTime": int(datetime.datetime.fromisoformat(data.get('expiryTime')).timestamp() * 1000) if data.get('expiryTime') else 0,
                    "enable": data.get('enable', True),
                    "tgId": "",
                    "subId": next((c['subId'] for c in current_clients if c['id'] == client_id), "")
                }],
                "decryption": "none",
                "fallbacks": []
            })
        }
        
        print(f"Update payload: {client_data}")  # Debug logging
        
        # Make the request to update client
        result = make_vpn_request(server, f'/inbounds/updateClient/{client_id}', 'POST', client_data)
        print(f"Update result: {result}")  # Debug logging
        
        return jsonify(result)
        
    except Exception as e:
        print(f"Error updating client: {str(e)}")  # Debug logging
        return jsonify({"success": False, "msg": str(e)}), 500

@vpn_bp.route('/admin/vpn/inbounds/list', methods=['GET'])
@login_required
def list_inbounds():
    """Get list of all inbounds from all servers"""
    try:
        servers = load_vpn_servers()
        all_inbounds = []
        
        for server in servers:
            result = make_vpn_request(server, '/inbounds/list')
            if result.get('success'):
                # Add server information to each inbound
                for inbound in result.get('obj', []):
                    inbound['server'] = server['domain']
                    inbound['serverPort'] = server['port']
                all_inbounds.extend(result.get('obj', []))
        
        return jsonify({"success": True, "obj": all_inbounds})
    except Exception as e:
        return jsonify({"success": False, "msg": str(e)}), 500

@vpn_bp.route('/admin/vpn/inbounds/add', methods=['POST'])
@login_required
def add_inbound():
    """Add a new inbound to a specific server"""
    try:
        data = request.json
        server_domain = data.pop('server', None)
        server_port = data.pop('serverPort', None)
        
        if not server_domain or not server_port:
            return jsonify({"success": False, "msg": "Server information is required"}), 400
        
        # Find the server in our list
        servers = load_vpn_servers()
        server = next((s for s in servers if s['domain'] == server_domain and s['port'] == server_port), None)
        
        if not server:
            return jsonify({"success": False, "msg": "Server not found"}), 404
            
        # Generate random values
        client_id = str(uuid.uuid4())
        email = f"user_{random.randint(10000, 99999)}"
        sub_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=16))

        # Create the exact payload string as required
        payload = (
            "{\r\n"
            f"\"up\": 0,\r\n"
            f"\"down\": 0,\r\n"
            f"\"total\": 0,\r\n"
            f"\"remark\": \"New\",\r\n"
            f"\"enable\": true,\r\n"
            f"\"expiryTime\": 0,\r\n"
            f"\"listen\": \"\",\r\n"
            f"\"port\": {data.get('port')},\r\n"
            f"\"protocol\": \"{data.get('protocol')}\",\r\n"
            f"\"settings\": \"{{\\\"clients\\\": [{{\\\"id\\\": \\\"{client_id}\\\",\\\"flow\\\": \\\"\\\",\\\"email\\\": \\\"{email}\\\",\\\"limitIp\\\": 0,\\\"totalGB\\\": 0,\\\"expiryTime\\\": 0,\\\"enable\\\": true,\\\"tgId\\\": \\\"\\\",\\\"subId\\\": \\\"{sub_id}\\\",\\\"reset\\\": 0}}],\\\"decryption\\\": \\\"none\\\",\\\"fallbacks\\\": []}}\",\r\n"
            f"\"streamSettings\": \"{{\\\"network\\\": \\\"{data.get('network')}\\\",\\\"security\\\": \\\"{data.get('security')}\\\",\\\"externalProxy\\\": [],\\\"tcpSettings\\\": {{\\\"acceptProxyProtocol\\\": false,\\\"header\\\": {{\\\"type\\\": \\\"none\\\"}}}}}}\"" + ",\r\n"
            f"\"sniffing\": \"{{\\\"enabled\\\": true,\\\"destOverride\\\": [\\\"http\\\",\\\"tls\\\",\\\"quic\\\",\\\"fakedns\\\"],\\\"metadataOnly\\\": false,\\\"routeOnly\\\": false}}\",\r\n"
            f"\"allocate\": \"{{\\\"strategy\\\": \\\"always\\\",\\\"refresh\\\": 5,\\\"concurrency\\\": 3}}\"\r\n"
            "}"
        )

        # Make the request to add inbound
        result = make_vpn_request(server, '/inbounds/add', 'POST', payload)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "msg": str(e)}), 500

@vpn_bp.route('/admin/vpn/inbounds/update/<int:inbound_id>', methods=['POST'])
@login_required
def update_inbound(inbound_id):
    """Update an existing inbound on a specific server"""
    try:
        data = request.json
        server_domain = data.pop('server', None)
        server_port = data.pop('serverPort', None)
        
        if not server_domain or not server_port:
            return jsonify({"success": False, "msg": "Server information is required"}), 400
        
        # Find the server in our list
        servers = load_vpn_servers()
        server = next((s for s in servers if s['domain'] == server_domain and s['port'] == server_port), None)
        
        if not server:
            return jsonify({"success": False, "msg": "Server not found"}), 404
        
        # Prepare the complete inbound data
        inbound_data = {
            "up": 0,
            "down": 0,
            "total": 0,
            "remark": "",
            "enable": True,
            "expiryTime": 0,
            "listen": "",
            "port": int(data.get('port')),
            "protocol": data.get('protocol'),
            "settings": json.dumps({
                "clients": [
                    {
                        "id": data.get('id', str(uuid.uuid4())),
                        "flow": "",
                        "email": f"user_{random.randint(10000, 99999)}",
                        "limitIp": 0,
                        "totalGB": 0,
                        "expiryTime": 0,
                        "enable": True,
                        "tgId": "",
                        "subId": ''.join(random.choices(string.ascii_lowercase + string.digits, k=16)),
                        "reset": 0
                    }
                ],
                "decryption": "none",
                "fallbacks": []
            }, indent=2),
            "streamSettings": json.dumps({
                "network": data.get('network', 'tcp'),
                "security": data.get('security', 'none'),
                "externalProxy": [],
                "tcpSettings": {
                    "acceptProxyProtocol": False,
                    "header": {
                        "type": "none"
                    }
                }
            }, indent=2),
            "sniffing": json.dumps({
                "enabled": False,
                "destOverride": [
                    "http",
                    "tls",
                    "quic",
                    "fakedns"
                ],
                "metadataOnly": False,
                "routeOnly": False
            }, indent=2),
            "allocate": json.dumps({
                "strategy": "always",
                "refresh": 5,
                "concurrency": 3
            }, indent=2)
        }
        
        # Make the request to update inbound
        result = make_vpn_request(server, f'/inbounds/update/{inbound_id}', 'POST', inbound_data)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "msg": str(e)}), 500

@vpn_bp.route('/admin/vpn/inbounds/del/<int:inbound_id>', methods=['POST'])
@login_required
def delete_inbound(inbound_id):
    """Delete an inbound from a specific server"""
    try:
        data = request.json
        server_domain = data.get('server')
        server_port = data.get('serverPort')
        
        if not server_domain or not server_port:
            return jsonify({"success": False, "msg": "Server information is required"}), 400
        
        # Find the server in our list
        servers = load_vpn_servers()
        server = next((s for s in servers if s['domain'] == server_domain and s['port'] == server_port), None)
        
        if not server:
            return jsonify({"success": False, "msg": "Server not found"}), 404
        
        # Make the request to delete inbound - note the endpoint format change
        result = make_vpn_request(server, f'/inbounds/del/{inbound_id}', 'POST')
        
        # If successful, the API returns {"success": true, "msg": "Delete Successfully", "obj": inbound_id}
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "msg": str(e)}), 500

@vpn_bp.route('/admin/vpn/inbounds/<int:inbound_id>', methods=['GET'])
@login_required
def get_inbound(inbound_id):
    """Get details of a specific inbound"""
    try:
        server_domain = request.args.get('server')
        server_port = request.args.get('serverPort')
        
        if not server_domain or not server_port:
            return jsonify({"success": False, "msg": "Server information is required"}), 400
        
        # Find the server in our list
        servers = load_vpn_servers()
        server = next((s for s in servers if s['domain'] == server_domain and s['port'] == server_port), None)
        
        if not server:
            return jsonify({"success": False, "msg": "Server not found"}), 404
        
        # Make the request to get inbound details
        result = make_vpn_request(server, f'/inbounds/get/{inbound_id}')
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "msg": str(e)}), 500
    
@vpn_bp.route('/admin/vpn/clients', methods=['GET'])
@login_required
def vpn_clients_page():
    """Render the VPN clients management page"""
    return render_template('vpn_clients.html')


@vpn_bp.route('/admin/vpn/inbounds/addClient', methods=['POST'])
@login_required
def add_client():
    """Add a new client to an inbound"""
    try:
        data = request.json
        server_domain = data.pop('server', None)
        server_port = data.pop('serverPort', None)
        inbound_id = data.pop('inboundId', None)
        
        if not all([server_domain, server_port, inbound_id]):
            return jsonify({"success": False, "msg": "Missing required information"}), 400
        
        # Find the server in our list
        servers = load_vpn_servers()
        server = next((s for s in servers if s['domain'] == server_domain and s['port'] == server_port), None)
        
        if not server:
            return jsonify({"success": False, "msg": "Server not found"}), 404

        # Generate unique IDs
        client_id = str(uuid.uuid4())
        sub_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=16))

        # Format the client data
        client_data = {
            "id": int(inbound_id),
            "settings": json.dumps({
                "clients": [{
                    "id": client_id,
                    "flow": "",
                    "email": data.get('email', f"user_{random.randint(10000, 99999)}"),
                    "limitIp": data.get('limitIp', 0),
                    "totalGB": data.get('totalGB', 0) * 1024 * 1024 * 1024,  # Convert GB to bytes
                    "expiryTime": int(datetime.datetime.fromisoformat(data.get('expiryTime')).timestamp() * 1000) if data.get('expiryTime') else 0,
                    "enable": data.get('enable', True),
                    "tgId": "",
                    "subId": sub_id,
                    "reset": 0
                }]
            })
        }
        
        # Make the request to add client
        result = make_vpn_request(server, '/inbounds/addClient', 'POST', client_data)
        return jsonify(result)
        
    except Exception as e:
        print(f"Error adding client: {str(e)}")  # Add logging
        return jsonify({"success": False, "msg": str(e)}), 500

def load_config_templates():
    """Load config templates from JSON file"""
    if os.path.exists(CONFIG_TEMPLATES_FILE):
        with open(CONFIG_TEMPLATES_FILE, 'r') as f:
            return json.load(f)
    return []

def save_config_templates(templates):
    """Save config templates to JSON file"""
    with open(CONFIG_TEMPLATES_FILE, 'w') as f:
        json.dump(templates, f, indent=2)

@vpn_bp.route('/admin/vpn/config-templates', methods=['GET'])
@login_required
def get_config_templates():
    """Get all config templates"""
    try:
        templates = load_config_templates()
        return jsonify({"success": True, "templates": templates})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@vpn_bp.route('/admin/vpn/config-templates', methods=['POST'])
@login_required
def add_config_template():
    """Add a new config template"""
    try:
        template = request.json
        if not template.get('name') or not template.get('template'):
            return jsonify({
                "success": False, 
                "message": "Name and template are required"
            }), 400

        templates = load_config_templates()
        
        # Check for duplicate names
        if any(t['name'] == template['name'] for t in templates):
            return jsonify({
                "success": False,
                "message": "Template with this name already exists"
            }), 400

        # Ensure template has an ID
        if 'id' not in template:
            template['id'] = str(uuid.uuid4())

        templates.append(template)
        save_config_templates(templates)
        
        return jsonify({
            "success": True,
            "message": "Template added successfully",
            "template": template
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@vpn_bp.route('/admin/vpn/config-templates', methods=['PUT'])
@login_required
def update_config_template():
    """Update an existing config template"""
    try:
        template = request.json
        if not template.get('id'):
            return jsonify({
                "success": False,
                "message": "Template ID is required"
            }), 400

        templates = load_config_templates()
        
        # Find and update the template
        template_index = next(
            (i for i, t in enumerate(templates) if t['id'] == template['id']), 
            None
        )
        
        if template_index is None:
            return jsonify({
                "success": False,
                "message": "Template not found"
            }), 404

        # Check for duplicate names with other templates
        if any(t['name'] == template['name'] and t['id'] != template['id'] 
               for t in templates):
            return jsonify({
                "success": False,
                "message": "Another template with this name already exists"
            }), 400

        templates[template_index] = template
        save_config_templates(templates)
        
        return jsonify({
            "success": True,
            "message": "Template updated successfully",
            "template": template
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@vpn_bp.route('/admin/vpn/config-templates/<template_id>', methods=['DELETE'])
@login_required
def delete_config_template(template_id):
    """Delete a config template"""
    try:
        templates = load_config_templates()
        
        # Find and remove the template
        templates = [t for t in templates if t['id'] != template_id]
        save_config_templates(templates)
        
        return jsonify({
            "success": True,
            "message": "Template deleted successfully"
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@vpn_bp.route('/admin/vpn/inbounds/<int:inbound_id>/delClient/<string:client_id>', methods=['POST'])
@login_required
def delete_client(inbound_id, client_id):
    """Delete a client from an inbound"""
    try:
        data = request.json
        server_domain = data.get('server')
        server_port = data.get('serverPort')
        
        if not server_domain or not server_port:
            return jsonify({"success": False, "msg": "Server information is required"}), 400
        
        # Find the server in our list
        servers = load_vpn_servers()
        server = next((s for s in servers if s['domain'] == server_domain and s['port'] == server_port), None)
        
        if not server:
            return jsonify({"success": False, "msg": "Server not found"}), 404
        
        # Make the request to delete client
        result = make_vpn_request(server, f'/inbounds/{inbound_id}/delClient/{client_id}', 'POST')
        return jsonify(result)
        
    except Exception as e:
        print(f"Error deleting client: {str(e)}")  # Debug logging
        return jsonify({"success": False, "msg": str(e)}), 500