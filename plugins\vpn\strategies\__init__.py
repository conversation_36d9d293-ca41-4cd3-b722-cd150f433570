"""
VPN Strategies Package
Contains strategy pattern implementations for different VPN product types
"""

from .base_strategy import BaseRedemptionStrategy
from .vpn_base_strategy import VPNBaseStrategy
from .vpn_sg_strategy import VPNSingaporeStrategy
from .vpn_my_basic_strategy import VPNMalaysiaBasicStrategy
from .vpn_my_highspeed_strategy import VPNMalaysiaHighSpeedStrategy

__all__ = [
    'BaseRedemptionStrategy',
    'VPNBaseStrategy',
    'VPNSingaporeStrategy', 
    'VPNMalaysiaBasicStrategy',
    'VPNMalaysiaHighSpeedStrategy'
]
