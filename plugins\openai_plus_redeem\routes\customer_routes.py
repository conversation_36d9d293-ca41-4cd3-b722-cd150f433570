"""
Customer Routes for OpenAI Plus Redeem Plugin

Customer-facing endpoints for redemption process, status checking,
and verification code retrieval.
"""

import logging
from typing import TYPE_CHECKING
from datetime import datetime
from flask import Blueprint, jsonify, request, render_template, abort, url_for, redirect, g
from functools import wraps

if TYPE_CHECKING:
    from ..plugin import Plugin

logger = logging.getLogger(__name__)

# Import rate limiting utilities
try:
    from utils.rate_limiter import rate_limit, get_client_ip
    RATE_LIMITING_AVAILABLE = True
except ImportError:
    logger.warning("Rate limiting utilities not available")
    RATE_LIMITING_AVAILABLE = False

    # Fallback decorators
    def rate_limit(limit=100, window=3600, per='ip'):
        def decorator(func):
            return func
        return decorator

    def get_client_ip():
        return request.remote_addr or 'unknown'


def handle_api_error(func):
    """Decorator to handle API errors consistently"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"Validation error in {func.__name__}: {e}")
            return jsonify({
                'status': 'error',
                'error': str(e),
                'code': 'VALIDATION_ERROR'
            }), 400
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {e}")
            return jsonify({
                'status': 'error',
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }), 500
    return wrapper


def create_customer_blueprint(plugin: 'Plugin') -> Blueprint:
    """Create customer routes blueprint"""
    
    # Specify template folder for plugin templates
    import os
    template_folder = os.path.join(os.path.dirname(__file__), '..', 'templates')
    
    bp = Blueprint('openai_plus_redeem_customer', __name__, 
                   template_folder=template_folder,
                   url_prefix='/openai-plus-redeem')
    
    # ========== Helper Functions ==========
    
    def get_service(service_name: str):
        """Get service from plugin service manager"""
        if not hasattr(plugin, 'service_manager') or not plugin.service_manager:
            raise RuntimeError("Service manager not available")
        
        service = plugin.service_manager.get_service(service_name)
        if not service:
            raise RuntimeError(f"Service {service_name} not available")
        
        return service
    
    def validate_required_params(data: dict, required_fields: list) -> None:
        """Validate required parameters"""
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
    
    # ========== Customer Web Pages ==========
    
    @bp.route('/', methods=['GET'])
    def redeem_page():
        """Main redemption page with Shopee integration support"""
        try:
            # Get URL parameters for Shopee integration
            order_id = request.args.get('order_id', '')
            source = request.args.get('source', '')
            username = request.args.get('username', '')

            # Additional Shopee-specific parameters
            order_sn = request.args.get('order_sn', '')  # Alternative order identifier
            buyer_id = request.args.get('buyer_id', '')

            # Auto-populate form if coming from Shopee
            auto_populate = source.lower() == 'shopee' and (order_id or order_sn) and username

            # If auto-populate is enabled, pre-fill redemption data
            redemption_data = {}
            if auto_populate:
                redemption_data = {
                    'order_id': order_id or order_sn,
                    'buyer_username': username,
                    'source': source,
                    'auto_populate': True
                }

            return render_template('openai_plus_redeem/customer_redeem.html',
                                 order_id=order_id,
                                 order_sn=order_sn,
                                 source=source,
                                 username=username,
                                 buyer_id=buyer_id,
                                 auto_populate=auto_populate,
                                 redemption_data=redemption_data)
        except Exception as e:
            logger.error(f"Error rendering redeem page: {e}")
            abort(500)
    
    @bp.route('/status/<redemption_id>', methods=['GET'])
    def status_page(redemption_id: str):
        """Redemption status page"""
        try:
            return render_template('openai_plus_redeem/redemption_status.html',
                                 redemption_id=redemption_id)
        except Exception as e:
            logger.error(f"Error rendering status page: {e}")
            abort(500)
    
    # ========== Customer API Endpoints ==========
    
    @bp.route('/api/redeem', methods=['POST'])
    @rate_limit(limit=10, window=3600, per='ip')  # 10 redemptions per hour per IP
    @handle_api_error
    def process_redemption():
        """Process order redemption"""
        data = request.get_json() or {}
        
        # Validate required fields
        validate_required_params(data, ['order_id', 'buyer_username', 'sku', 'var_sku'])
        
        # Get order redeem service
        order_redeem_service = get_service('order_redeem')
        
        # Process redemption
        result = order_redeem_service.process_order_redemption(
            order_id=data['order_id'],
            buyer_username=data['buyer_username'],
            sku=data['sku'],
            var_sku=data['var_sku']
        )
        
        if result['success']:
            response_data = {
                'status': 'success',
                'redemption_id': result['redemption_id'],
                'account_assigned': result.get('account_assigned', False),
                'requires_verification': result.get('requires_verification', False),
                'message': result.get('message', 'Redemption processed successfully')
            }
            
            # Add account info if assigned
            if result.get('account_info'):
                response_data['account_info'] = result['account_info']
            
            return jsonify(response_data), 201
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'step': result.get('step', 'unknown'),
                'code': 'REDEMPTION_FAILED'
            }), 400
    
    @bp.route('/api/status/<redemption_id>', methods=['GET'])
    @handle_api_error
    def get_redemption_status(redemption_id: str):
        """Get redemption status"""
        # Get order redeem service
        order_redeem_service = get_service('order_redeem')
        
        # Get status
        status = order_redeem_service.get_redemption_status(redemption_id)
        
        if status['found']:
            return jsonify({
                'status': 'success',
                'data': status
            })
        else:
            return jsonify({
                'status': 'error',
                'error': status.get('error', 'Redemption not found'),
                'code': 'NOT_FOUND'
            }), 404
    
    @bp.route('/api/user/<username>/summary', methods=['GET'])
    @handle_api_error
    def get_user_summary(username: str):
        """Get user redemption summary"""
        # Get order redeem service
        order_redeem_service = get_service('order_redeem')
        
        # Get summary
        summary = order_redeem_service.get_user_redemption_summary(username)
        
        if 'error' in summary:
            return jsonify({
                'status': 'error',
                'error': summary['error'],
                'code': 'SUMMARY_ERROR'
            }), 500
        
        return jsonify({
            'status': 'success',
            'data': summary
        })
    
    @bp.route('/api/verification/<verification_id>/status', methods=['GET'])
    @handle_api_error
    def get_verification_status(verification_id: str):
        """Get email verification status"""
        # Get email service
        email_service = get_service('email')
        
        # Get verification status
        status = email_service.get_verification_status(verification_id)
        
        if status['found']:
            return jsonify({
                'status': 'success',
                'data': status
            })
        else:
            return jsonify({
                'status': 'error',
                'error': status.get('error', 'Verification not found'),
                'code': 'NOT_FOUND'
            }), 404
    
    @bp.route('/api/verification/search', methods=['POST'])
    @rate_limit(limit=20, window=3600, per='ip')  # 20 verification searches per hour per IP
    @handle_api_error
    def search_verification_code():
        """Search for verification code in email"""
        data = request.get_json() or {}
        
        # Validate required fields
        validate_required_params(data, ['account_email', 'redemption_id'])
        
        # Get email service
        email_service = get_service('email')
        
        # Search for verification code
        result = email_service.search_verification_code(
            account_email=data['account_email'],
            redemption_id=data['redemption_id'],
            timeout_minutes=data.get('timeout_minutes')
        )
        
        if result['success']:
            return jsonify({
                'status': 'success',
                'verification_id': result['verification_id'],
                'verification_code': result['verification_code'],
                'search_attempts': result['search_attempts']
            })
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'verification_id': result.get('verification_id'),
                'code': 'VERIFICATION_SEARCH_FAILED'
            }), 400
    
    @bp.route('/api/cooldown/<username>/status', methods=['GET'])
    @handle_api_error
    def get_cooldown_status(username: str):
        """Get user cooldown status"""
        # Get cooldown service
        cooldown_service = get_service('cooldown')
        
        # Get cooldown status
        status = cooldown_service.get_user_cooldown_status(username)
        
        if 'error' in status:
            return jsonify({
                'status': 'error',
                'error': status['error'],
                'code': 'COOLDOWN_ERROR'
            }), 500
        
        return jsonify({
            'status': 'success',
            'data': status
        })
    
    @bp.route('/api/account/test', methods=['POST'])
    @handle_api_error
    def test_account_access():
        """Test ChatGPT account access"""
        data = request.get_json() or {}

        # Validate required fields
        validate_required_params(data, ['email', 'password'])

        # This would test actual ChatGPT login
        # For now, return a mock response
        return jsonify({
            'status': 'success',
            'data': {
                'email': data['email'],
                'access_valid': True,
                'test_timestamp': datetime.now().isoformat(),
                'message': 'Account access test completed'
            }
        })

    @bp.route('/api/shopee/redeem', methods=['POST'])
    @rate_limit(limit=15, window=3600, per='ip')  # 15 Shopee redemptions per hour per IP
    @handle_api_error
    def shopee_redeem_integration():
        """Shopee integration endpoint for direct redemption"""
        data = request.get_json() or {}

        # Support both direct parameters and Shopee webhook format
        if 'order_data' in data:
            # Shopee webhook format
            order_data = data['order_data']
            order_id = order_data.get('order_sn') or order_data.get('order_id')
            buyer_username = order_data.get('buyer_username')
            sku = order_data.get('sku')
            var_sku = order_data.get('var_sku')
        else:
            # Direct parameters
            order_id = data.get('order_id') or data.get('order_sn')
            buyer_username = data.get('buyer_username') or data.get('username')
            sku = data.get('sku')
            var_sku = data.get('var_sku')

        # Validate required fields
        validate_required_params({
            'order_id': order_id,
            'buyer_username': buyer_username,
            'sku': sku,
            'var_sku': var_sku
        }, ['order_id', 'buyer_username', 'sku', 'var_sku'])

        # Get order redeem service
        order_redeem_service = get_service('order_redeem')

        # Process redemption
        result = order_redeem_service.process_order_redemption(
            order_id=order_id,
            buyer_username=buyer_username,
            sku=sku,
            var_sku=var_sku
        )

        if result['success']:
            # Send account details via Shopee messaging if available
            if result.get('account_assigned') and result.get('account_info'):
                try:
                    messaging_service = get_service('shopee_messaging')
                    redemption_service = get_service('order_redemption')

                    # Get redemption details
                    redemption = redemption_service.get_redemption(result['redemption_id'])

                    if redemption:
                        # Get account details
                        account_service = get_service('chatgpt_account')
                        account = account_service.get_account(redemption.assigned_account_id)

                        if account:
                            # Send account details via Shopee
                            messaging_result = messaging_service.send_account_details(
                                order_id=order_id,
                                buyer_username=buyer_username,
                                account=account,
                                redemption=redemption
                            )

                            result['message_sent'] = messaging_result.get('success', False)
                            if messaging_result.get('success'):
                                result['message'] = 'Account details sent via Shopee chat'

                except Exception as e:
                    logger.warning(f"Failed to send Shopee message: {e}")
                    result['message_sent'] = False

            response_data = {
                'status': 'success',
                'redemption_id': result['redemption_id'],
                'account_assigned': result.get('account_assigned', False),
                'message_sent': result.get('message_sent', False),
                'requires_verification': result.get('requires_verification', False),
                'message': result.get('message', 'Redemption processed successfully'),
                'source': 'shopee_integration'
            }

            # Add account info if assigned
            if result.get('account_info'):
                response_data['account_info'] = result['account_info']

            return jsonify(response_data), 201
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'step': result.get('step', 'unknown'),
                'code': 'SHOPEE_REDEMPTION_FAILED',
                'source': 'shopee_integration'
            }), 400
    
    # ========== Error Handlers ==========
    
    @bp.errorhandler(400)
    def bad_request(error):
        """Handle bad request errors"""
        return jsonify({
            'status': 'error',
            'error': 'Bad request',
            'code': 'BAD_REQUEST',
            'details': str(error.description) if hasattr(error, 'description') else None
        }), 400
    
    @bp.errorhandler(404)
    def not_found(error):
        """Handle not found errors"""
        return jsonify({
            'status': 'error',
            'error': 'Resource not found',
            'code': 'NOT_FOUND'
        }), 404
    
    @bp.errorhandler(500)
    def internal_error(error):
        """Handle internal server errors"""
        return jsonify({
            'status': 'error',
            'error': 'Internal server error',
            'code': 'INTERNAL_ERROR'
        }), 500
    
    return bp
