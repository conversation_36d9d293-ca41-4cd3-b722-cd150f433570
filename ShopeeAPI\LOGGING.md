# ShopeeAPI Logging Configuration

## Overview

ShopeeAPI has been optimized to reduce log spam and improve performance. The logging system now uses different log levels for different components and includes log rotation to prevent disk space issues.

## Log Levels

### Default Log Levels (Production)
- **General**: `WARNING` - Only warnings and errors
- **WebSocket**: `ERROR` - Only errors (WebSocket messages are very frequent)
- **Webhook**: `ERROR` - Only errors (Webhook calls are frequent)

### Log Files
- `logs/shopee_api.log` - General application logs (max 5MB, 3 backups)
- `logs/shopee_api_errors.log` - Error-only logs (max 2MB, 2 backups)

## Environment Variables

You can control logging behavior using these environment variables:

```bash
# General log level
LOG_LEVEL=WARNING

# WebSocket service log level (reduces spam from frequent messages)
WEBSOCKET_LOG_LEVEL=ERROR

# Webhook service log level (reduces spam from frequent webhook calls)
WEBHOOK_LOG_LEVEL=ERROR

# Enable debug mode (sets all levels to DEBUG)
DEBUG_MODE=false
```

## Docker Configuration

### Method 1: Environment Variables in Docker Run
```bash
docker run -d \
  -p 8000:8000 \
  -e LOG_LEVEL=INFO \
  -e WEBSOCKET_LOG_LEVEL=WARNING \
  -e WEBHOOK_LOG_LEVEL=WARNING \
  -e DEBUG_MODE=false \
  -v /path/to/config.json:/app/ShopeeAPI/config.json \
  -v /path/to/logs:/app/logs \
  your-shopee-api-image
```

### Method 2: Using .env File
1. Copy `.env.example` to `.env`
2. Modify the values in `.env`
3. Mount the .env file in Docker:
```bash
docker run -d \
  -p 8000:8000 \
  --env-file .env \
  -v /path/to/config.json:/app/ShopeeAPI/config.json \
  -v /path/to/logs:/app/logs \
  your-shopee-api-image
```

## Troubleshooting

### Enable Debug Mode
If you need to troubleshoot issues, enable debug mode:
```bash
# Set environment variable
DEBUG_MODE=true

# Or set individual log levels
LOG_LEVEL=DEBUG
WEBSOCKET_LOG_LEVEL=DEBUG
WEBHOOK_LOG_LEVEL=DEBUG
```

### Log File Rotation
- Log files automatically rotate when they reach the size limit
- Old log files are kept with `.1`, `.2`, `.3` extensions
- This prevents disk space issues

### Performance Impact
- **WARNING/ERROR levels**: Minimal performance impact
- **INFO level**: Moderate impact due to frequent WebSocket/Webhook logs
- **DEBUG level**: High impact, only use for troubleshooting

## Log Reduction Benefits

### Before Optimization
- ~14MB logs per day
- Frequent INFO messages from WebSocket and Webhook operations
- No log rotation (single large file)
- System lag due to excessive logging

### After Optimization
- ~1-2MB logs per day (85-90% reduction)
- Only important warnings and errors logged by default
- Automatic log rotation prevents disk space issues
- Improved system performance

## Monitoring

### Important Logs to Monitor
- **ERROR level**: Authentication failures, connection issues
- **WARNING level**: Configuration problems, retry attempts
- **INFO level**: Service startup, configuration changes (when enabled)

### Log Analysis
```bash
# View recent errors
tail -f logs/shopee_api_errors.log

# Search for specific issues
grep -i "authentication" logs/shopee_api.log

# Monitor log file sizes
ls -lh logs/
```
