#!/usr/bin/env python3
"""
Debug script to test VPN command registration with chat commands plugin
"""

import sys
import os
import json
import logging

# Add the main application directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def debug_registration():
    """Debug the command registration process"""
    print("🔍 Debug: VPN Command Registration")
    print("=" * 50)
    
    # Get plugin directory
    plugin_dir = os.path.dirname(os.path.abspath(__file__))
    configs_dir = os.path.join(os.path.dirname(os.path.dirname(plugin_dir)), 'configs', 'plugins', 'vpn_config_generator')
    
    # Check if command config files exist
    command_config_file = os.path.join(configs_dir, 'command_config.json')
    commands_file = os.path.join(configs_dir, 'commands.json')
    
    print(f"📁 Plugin dir: {plugin_dir}")
    print(f"📁 Configs dir: {configs_dir}")
    print(f"📄 Command config file: {command_config_file}")
    print(f"📄 Commands file: {commands_file}")
    
    # Check if files exist
    config_exists = os.path.exists(command_config_file)
    commands_exists = os.path.exists(commands_file)
    
    print(f"✅ Command config exists: {config_exists}")
    print(f"✅ Commands file exists: {commands_exists}")
    
    if not config_exists or not commands_exists:
        print("❌ Missing required configuration files!")
        return False
    
    # Load configuration
    try:
        with open(command_config_file, 'r', encoding='utf-8') as f:
            command_config = json.load(f)
        
        with open(commands_file, 'r', encoding='utf-8') as f:
            commands = json.load(f)
            
        print(f"📋 Main command name: {command_config['command_name']}")
        print(f"📋 Enabled: {command_config.get('enabled', 'Unknown')}")
        print(f"📋 Commands count: {len(commands)}")
        
        # Check command names
        print("\n🔍 Command Analysis:")
        print("-" * 30)
        
        main_command = command_config['command_name']
        expected_commands = {
            'v': main_command,
            'vlist': f"{main_command}list",
            'vuser': f"{main_command}user", 
            'vdel': f"{main_command}del",
            'vrenew': f"{main_command}renew",
            'vtest': f"{main_command}test",
            'vservers': f"{main_command}servers",
            'vhelp': f"{main_command}help"
        }
        
        registration_data = {}
        for command_key, expected_name in expected_commands.items():
            if command_key in commands:
                actual_name = commands[command_key]['command']
                matches = actual_name == expected_name
                status = "✅" if matches else "❌"
                print(f"{status} {command_key}: '{actual_name}' (expected: '{expected_name}')")
                
                registration_data[command_key] = {
                    'command_name': actual_name,
                    'description': commands[command_key].get('description', ''),
                    'enabled': commands[command_key].get('enabled', True),
                    'required_params': commands[command_key].get('required_params', [])
                }
            else:
                print(f"❌ {command_key}: NOT FOUND")
                
        print(f"\n📊 Registration Summary:")
        print(f"   Main Command: #{main_command}")
        print(f"   Total Commands: {len(registration_data)}")
        print(f"   Expected: {len(expected_commands)}")
        
        # Simulate registration check
        print(f"\n🧪 Simulated Registration Test:")
        print(f"   Plugin Name: vpn_config_generator")
        print(f"   Commands to Register: {list(registration_data.keys())}")
        
        # Check if we can import required models
        try:
            # Try to import ChatCommand from chat_commands plugin
            from plugins.chat_commands.models import ChatCommand
            print("✅ ChatCommand model import successful")
            
            # Try to create a sample command
            sample_command = ChatCommand(
                command=main_command,
                description="Test VPN command",
                response_text="Test response",
                required_params=[],
                enabled=True
            )
            print("✅ ChatCommand object creation successful")
            print(f"   Sample command: {sample_command.command}")
            
        except ImportError as e:
            print(f"❌ Import error: {e}")
            return False
        except Exception as e:
            print(f"❌ Command creation error: {e}")
            return False
            
        print(f"\n💡 Registration Process:")
        print(f"1. Load commands from {commands_file}")
        print(f"2. Create ChatCommand objects for each command")
        print(f"3. Call chat_commands_plugin.register_external_command()")
        print(f"4. Commands should be stored in chat commands plugin")
        
        print(f"\n🔍 Status Check Process:")
        print(f"1. Get chat_commands plugin from plugin_manager")
        print(f"2. Call plugin.command_service.get_command('{main_command}')")
        print(f"3. Check if command.plugin_source == 'vpn_config_generator'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False

def test_direct_registration():
    """Test direct registration without plugin manager"""
    print(f"\n🧪 Direct Registration Test")
    print("-" * 30)
    
    try:
        # Import required components
        from plugins.chat_commands.models import ChatCommand
        from plugins.chat_commands.services import ChatCommandService
        
        # Create a mock app
        class MockApp:
            def __init__(self):
                self.config = {}
        
        # Create command service
        chat_dir = os.path.join(os.path.dirname(__file__), '..', 'chat_commands')
        service = ChatCommandService(chat_dir, MockApp())
        
        # Create test command
        test_command = ChatCommand(
            command="vv",
            description="Test VPN command",
            response_text="Test response",
            required_params=[],
            enabled=True
        )
        
        # Try to register
        success = service.register_external_command(test_command, "vpn_config_generator", None)
        print(f"Registration result: {success}")
        
        # Try to retrieve
        retrieved = service.get_command("vv")
        if retrieved:
            print(f"✅ Command retrieved: {retrieved.command}")
            print(f"   Plugin source: {getattr(retrieved, 'plugin_source', 'None')}")
        else:
            print("❌ Command not found after registration")
            
        return success
        
    except Exception as e:
        print(f"❌ Direct registration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting VPN Command Registration Debug...")
    
    config_ok = debug_registration()
    if config_ok:
        direct_ok = test_direct_registration()
        if direct_ok:
            print(f"\n🎉 All tests passed! Registration should work.")
        else:
            print(f"\n⚠️  Direct registration test failed. Check chat_commands plugin.")
    else:
        print(f"\n❌ Configuration issues found. Fix configuration first.")