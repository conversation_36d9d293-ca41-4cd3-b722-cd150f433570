"""
Scheduler Service for Shopee Auto Boost Plugin

Handles the 4-hour scheduling and automatic boost execution.
"""

import logging
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger

logger = logging.getLogger(__name__)


class SchedulerService:
    """Service for scheduling automatic product boosts"""

    def __init__(self, boost_service, product_service, config: Dict[str, Any], plugin_dir: str):
        self.boost_service = boost_service
        self.product_service = product_service
        self.config = config
        self.plugin_dir = plugin_dir
        self.scheduler = BackgroundScheduler()
        self.history_file = os.path.join(plugin_dir, "boost_history.json")
        self._running = False

    def start_scheduler(self) -> bool:
        """Start the auto-boost scheduler"""
        try:
            if self._running:
                logger.warning("Scheduler is already running")
                return True

            # Get boost interval from config
            interval_hours = self.config.get("boost_interval_hours", 4)
            
            # Add the job to scheduler
            self.scheduler.add_job(
                func=self.execute_auto_boost,
                trigger=IntervalTrigger(hours=interval_hours),
                id="auto_boost_job",
                name="Shopee Auto Boost",
                replace_existing=True
            )
            
            # Start the scheduler
            self.scheduler.start()
            self._running = True
            
            logger.info(f"Auto-boost scheduler started with {interval_hours} hour interval")
            return True
            
        except Exception as e:
            logger.error(f"Error starting scheduler: {e}")
            return False

    def stop_scheduler(self) -> bool:
        """Stop the auto-boost scheduler"""
        try:
            if not self._running:
                logger.warning("Scheduler is not running")
                return True
                
            self.scheduler.shutdown()
            self._running = False
            
            logger.info("Auto-boost scheduler stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping scheduler: {e}")
            return False

    def execute_auto_boost(self) -> Dict[str, Any]:
        """Execute automatic boost process"""
        try:
            # Check if PIN mode is enabled
            pinned_config = self.config.get("pinned_products", {})
            pin_mode_enabled = pinned_config.get("enabled", False)

            if pin_mode_enabled:
                logger.info("Starting automatic boost execution (PIN mode enabled)...")
                pinned_product_ids = pinned_config.get("product_ids", [])
                logger.info(f"PIN mode: {len(pinned_product_ids)} products configured for auto-boost")
            else:
                logger.info("Starting automatic boost execution (standard mode)...")

            # Get boostable products
            products = self.product_service.get_boostable_products()

            if not products:
                logger.warning("No boostable products found")
                return {"success": False, "message": "No boostable products found"}

            # Load boost history
            history = self._load_history()

            # Select products for boosting
            selected_products = self.boost_service.select_products_for_boost(products, history)

            if not selected_products:
                if pin_mode_enabled:
                    logger.warning("No pinned products available for boosting (all may be on cooldown)")
                    return {"success": False, "message": "No pinned products available for boosting"}
                else:
                    logger.warning("No products selected for boosting")
                    return {"success": False, "message": "No products selected for boosting"}

            if pin_mode_enabled:
                logger.info(f"Selected {len(selected_products)} pinned products for boosting")
                for product in selected_products:
                    logger.info(f"  - {product.get('name', 'Unknown')} (ID: {product.get('id')})")
            else:
                logger.info(f"Selected {len(selected_products)} products for boosting")

            # Boost the selected products
            boost_results = self.boost_service.boost_products(selected_products)

            # Update boost history
            self.boost_service.update_boost_history(boost_results, self.history_file)

            # Log results
            success_count = len(boost_results.get("success", []))
            failed_count = len(boost_results.get("failed", []))

            if pin_mode_enabled:
                logger.info(f"PIN mode auto-boost completed: {success_count} successful, {failed_count} failed")
            else:
                logger.info(f"Auto-boost completed: {success_count} successful, {failed_count} failed")

            return {
                "success": True,
                "message": f"Boosted {success_count} products successfully",
                "results": boost_results,
                "timestamp": datetime.now().isoformat(),
                "pin_mode": pin_mode_enabled
            }
            
        except Exception as e:
            logger.error(f"Error during auto-boost execution: {e}")
            return {"success": False, "message": f"Error: {str(e)}"}

    def is_running(self) -> bool:
        """Check if scheduler is running"""
        return self._running and self.scheduler.running

    def get_next_run_time(self) -> Optional[str]:
        """Get next scheduled run time"""
        try:
            if not self._running:
                return None
                
            job = self.scheduler.get_job("auto_boost_job")
            if job and job.next_run_time:
                return job.next_run_time.isoformat()
                
        except Exception as e:
            logger.error(f"Error getting next run time: {e}")
            
        return None

    def get_last_boost_time(self) -> Optional[str]:
        """Get last boost execution time"""
        try:
            history = self._load_history()
            return history.get("global_stats", {}).get("last_boost_session")
        except Exception as e:
            logger.error(f"Error getting last boost time: {e}")
            return None

    def get_total_boosts(self) -> int:
        """Get total number of boost sessions"""
        try:
            history = self._load_history()
            return history.get("global_stats", {}).get("total_boost_sessions", 0)
        except Exception as e:
            logger.error(f"Error getting total boosts: {e}")
            return 0

    def trigger_manual_boost(self) -> Dict[str, Any]:
        """Trigger a manual boost execution"""
        logger.info("Manual boost triggered")
        return self.execute_auto_boost()

    def _load_history(self) -> Dict[str, Any]:
        """Load boost history from file"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading history: {e}")
        
        return {}

    def update_schedule(self, new_interval_hours: int) -> bool:
        """Update the boost schedule interval"""
        try:
            if self._running:
                # Remove existing job
                self.scheduler.remove_job("auto_boost_job")
                
                # Add new job with updated interval
                self.scheduler.add_job(
                    func=self.execute_auto_boost,
                    trigger=IntervalTrigger(hours=new_interval_hours),
                    id="auto_boost_job",
                    name="Shopee Auto Boost",
                    replace_existing=True
                )
                
                logger.info(f"Updated boost interval to {new_interval_hours} hours")
                return True
            else:
                logger.warning("Cannot update schedule - scheduler is not running")
                return False
                
        except Exception as e:
            logger.error(f"Error updating schedule: {e}")
            return False
