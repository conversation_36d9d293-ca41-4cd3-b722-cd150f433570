FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8000
ENV PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    sudo \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create log directory
RUN mkdir -p /app/logs

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional packages
RUN pip install --no-cache-dir watchdog

# Copy the entire project for context
COPY . /app/

# Make run_docker.py executable
RUN chmod +x /app/run_docker.py

# Make sure documentation is available in the container
RUN echo "Documentation is available at /app/DOCKER.md" >> /app/README.md

# Copy our entrypoint script
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# Make the check_env.py script executable if it exists
RUN if [ -f /app/check_env.py ]; then chmod +x /app/check_env.py; fi

# Create a non-root user to run the application
RUN groupadd -r shopeeapi && useradd -r -g shopeeapi shopeeapi
# Add shopeeapi to sudoers for config file management
RUN echo "shopeeapi ALL=(root) NOPASSWD: /usr/bin/chown, /usr/bin/chmod" > /etc/sudoers.d/shopeeapi
RUN chmod 0440 /etc/sudoers.d/shopeeapi

# Set proper ownership
RUN chown -R shopeeapi:shopeeapi /app

# Switch to non-root user
USER shopeeapi

# Expose the port
EXPOSE 8000

# Health check using Python script
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD python /app/health_check.py || exit 1

# Run the application using our entrypoint script
ENTRYPOINT ["/app/docker-entrypoint.sh"]