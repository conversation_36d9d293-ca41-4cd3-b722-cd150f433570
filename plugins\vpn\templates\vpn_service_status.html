{% extends "base.html" %}

{% block title %}Service Status - Server {{ server_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Service Status - Server {{ server_id }}</h2>
                <a href="{{ url_for('vpn.servers') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Servers
                </a>
            </div>
        </div>
    </div>
    
    {% if status %}
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Xray Service Status</h3>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Service:</dt>
                        <dd class="col-sm-8">{{ status.service_name or 'xray' }}</dd>
                        
                        <dt class="col-sm-4">Status:</dt>
                        <dd class="col-sm-8">
                            {% if status.is_running %}
                            <span class="badge badge-success">Running</span>
                            {% else %}
                            <span class="badge badge-danger">Stopped</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-4">PID:</dt>
                        <dd class="col-sm-8">{{ status.pid or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">Uptime:</dt>
                        <dd class="col-sm-8">{{ status.uptime or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">Memory Usage:</dt>
                        <dd class="col-sm-8">{{ status.memory_usage or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">CPU Usage:</dt>
                        <dd class="col-sm-8">{{ status.cpu_usage or 'N/A' }}</dd>
                    </dl>
                    
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="refreshStatus()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        {% if status.is_running %}
                        <button class="btn btn-warning" onclick="restartService()">
                            <i class="fas fa-redo"></i> Restart Service
                        </button>
                        <button class="btn btn-danger" onclick="stopService()">
                            <i class="fas fa-stop"></i> Stop Service
                        </button>
                        {% else %}
                        <button class="btn btn-success" onclick="startService()">
                            <i class="fas fa-play"></i> Start Service
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">System Information</h3>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">OS:</dt>
                        <dd class="col-sm-8">{{ status.os_info or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">Kernel:</dt>
                        <dd class="col-sm-8">{{ status.kernel_version or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">Load Average:</dt>
                        <dd class="col-sm-8">{{ status.load_average or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">Total Memory:</dt>
                        <dd class="col-sm-8">{{ status.total_memory or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">Free Memory:</dt>
                        <dd class="col-sm-8">{{ status.free_memory or 'N/A' }}</dd>
                        
                        <dt class="col-sm-4">Disk Usage:</dt>
                        <dd class="col-sm-8">{{ status.disk_usage or 'N/A' }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
    
    {% if status.recent_logs %}
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Logs</h3>
                </div>
                <div class="card-body">
                    <pre class="bg-dark text-light p-3" style="max-height: 400px; overflow-y: auto;">{{ status.recent_logs }}</pre>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> Unable to retrieve service status.
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshStatus() {
    location.reload();
}

function restartService() {
    if (confirm('Are you sure you want to restart the Xray service?')) {
        toastr.info('Restarting service...');
        $.post(`/admin/vpn/servers/{{ server_id }}/restart-xray`, function() {
            toastr.success('Service restarted successfully!');
            setTimeout(() => location.reload(), 2000);
        }).fail(function() {
            toastr.error('Failed to restart service');
        });
    }
}

function startService() {
    toastr.info('Starting service...');
    // In a real implementation, you would have a start endpoint
    toastr.warning('Start service feature not yet implemented');
}

function stopService() {
    if (confirm('Are you sure you want to stop the Xray service? This will disconnect all clients!')) {
        toastr.info('Stopping service...');
        // In a real implementation, you would have a stop endpoint
        toastr.warning('Stop service feature not yet implemented');
    }
}

// Auto-refresh every 30 seconds
setInterval(function() {
    if (document.visibilityState === 'visible') {
        refreshStatus();
    }
}, 30000);
</script>
{% endblock %}