"""
WebSocket service for Shopee API.

This module provides WebSocket functionality for real-time message delivery.
It connects to Shopee's WebSocket server and forwards messages to connected clients.
It also caches messages when enabled in the configuration.
"""
import asyncio
import json
import logging
import os
import time
import websockets
import socketio
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import WebSocket, WebSocketDisconnect

# Try different import approaches to handle both package and direct imports
try:
    from core.session import ShopeeSession
    from core.config import ShopeeConfig
    from services.chat import ChatService
    from core.cache import CacheManager
    from utils.webhook import WebhookManager
except ImportError as e:
    print(f"Import error in websocket.py: {e}")
    # Try adding current directory to path and importing again
    try:
        import sys
        import os
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        from core.session import ShopeeSession
        from core.config import ShopeeConfig
        from services.chat import ChatService
        from core.cache import Cache<PERSON>anager
        from utils.webhook import WebhookManager
    except ImportError as e2:
        print(f"Path-based import also failed in websocket.py: {e2}")
        # Last resort: try relative imports (only works when run as package)
        try:
            from ..core.session import ShopeeSession
            from ..core.config import ShopeeConfig
            from .chat import ChatService
            from ..core.cache import CacheManager
            from ..utils.webhook import WebhookManager
        except ImportError as e3:
            print(f"Relative import also failed in websocket.py: {e3}")
            raise ImportError(f"Could not import required modules in websocket.py: {e}")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# Set this module's logger to INFO level for production use
logger.setLevel(logging.INFO)


class WebSocketService:
    """
    WebSocket service for Shopee API.

    This service manages WebSocket connections to Shopee and forwards messages to connected clients.
    It also caches messages when enabled in the configuration.
    """

    def __init__(self, session: ShopeeSession, config: ShopeeConfig, chat_service: ChatService):
        """
        Initialize the WebSocket service.

        Args:
            session: ShopeeSession instance for authentication
            config: ShopeeConfig instance for configuration
            chat_service: ChatService instance for chat operations
        """
        self.session = session
        self.config = config
        self.chat_service = chat_service

        # Validate that session has the correct credential_manager
        if not hasattr(self.session, 'credential_manager'):
            raise ValueError("Session object must have a credential_manager attribute")
        
        # Ensure credential_manager has the required methods
        if not hasattr(self.session.credential_manager, 'get_csrf_token'):
            raise ValueError("Session credential_manager must have get_csrf_token method")

        # Store connected clients
        self.connected_clients: Dict[str, WebSocket] = {}

        # Track connection state
        self.is_connected = False
        self.last_message_time: Optional[datetime] = None
        self.last_ping_time: Optional[datetime] = None
        self.last_pong_time: Optional[datetime] = None
        self.reconnect_attempt = 0
        self.ws_connection = None
        self.ws_task = None

        # Add connection failure tracking
        self.max_reconnect_attempts = 10  # Maximum reconnection attempts before giving up
        self.auth_failure_count = 0  # Track consecutive auth failures
        self.max_auth_failures = 3  # Maximum auth failures before longer backoff
        self.last_auth_failure_time: Optional[datetime] = None
        self.auth_backoff_duration = 300  # 5 minutes backoff after repeated auth failures

        # Socket.IO client for Shopee WebSocket
        self.sio_client = None
        self.sio_connected = False
        self.shopee_heartbeat_task = None

        # Cache manager
        self.cache_manager = None

        # Webhook manager
        self.webhook_manager = None

        # Add flag for reconnection after config update
        self.needs_reconnect_after_config_update = False

        # Message deduplication - track processed message IDs
        self.processed_message_ids = set()
        self.max_processed_messages = 1000  # Keep track of last 1000 messages

        # Connection tracking for email notifications
        self.connection_start_time: Optional[datetime] = None
        self.last_disconnect_time: Optional[datetime] = None
        # Note: Email spam prevention is now handled by EmailService rate limiting

        # Initialize email service for disconnect notifications
        self.email_service = None
        try:
            from services.email_service import EmailService
            self.email_service = EmailService()
            logger.debug("Email service initialized for WebSocket disconnect notifications")
        except ImportError as e:
            logger.warning(f"Could not import EmailService: {e}. Disconnect email notifications will be disabled.")
        except Exception as e:
            logger.warning(f"Could not initialize EmailService: {e}. Disconnect email notifications will be disabled.")

        # Initialize cache manager
        cache_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cache')
        # Create cache directory if it doesn't exist
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        self.cache_manager = CacheManager(self.config.cache, cache_dir)

        # Print the raw webhook config from the config object for debugging
        print(f"Raw webhook config from config object: {json.dumps(self.config.webhook, indent=2)}")

        # Convert webhook config to uppercase keys to match config.json format
        webhook_config = {
            "ENABLED": self.config.webhook["enabled"],  # Use the value from config.json
            "MESSAGE_RECEIVED": {
                "ENABLED": self.config.webhook["message_received"]["enabled"],  # Use the value from config.json
                "RETRY_COUNT": self.config.webhook["message_received"]["retry_count"],
                "RETRY_DELAY": self.config.webhook["message_received"]["retry_delay"]
            },
            "MESSAGE_SENT": {
                "ENABLED": self.config.webhook["message_sent"]["enabled"],
                "RETRY_COUNT": self.config.webhook["message_sent"]["retry_count"],
                "RETRY_DELAY": self.config.webhook["message_sent"]["retry_delay"]
            }
        }

        # Add URLs configuration - support both new URLS array and legacy URL
        if self.config.webhook["message_received"]["urls"]:
            webhook_config["MESSAGE_RECEIVED"]["URLS"] = self.config.webhook["message_received"]["urls"]
        elif self.config.webhook["message_received"]["url"]:
            # Backward compatibility: convert single URL to URLS array format
            webhook_config["MESSAGE_RECEIVED"]["URLS"] = [{
                "URL": self.config.webhook["message_received"]["url"],
                "NAME": "Legacy URL",
                "ENABLED": True
            }]

        if self.config.webhook["message_sent"]["urls"]:
            webhook_config["MESSAGE_SENT"]["URLS"] = self.config.webhook["message_sent"]["urls"]
        elif self.config.webhook["message_sent"]["url"]:
            # Backward compatibility: convert single URL to URLS array format
            webhook_config["MESSAGE_SENT"]["URLS"] = [{
                "URL": self.config.webhook["message_sent"]["url"],
                "NAME": "Legacy URL",
                "ENABLED": True
            }]

        # Log webhook configuration for debugging
        logger.debug(f"Webhook configuration from config.json: ENABLED={self.config.webhook['enabled']}")

        # Log MESSAGE_RECEIVED configuration
        msg_received_urls = webhook_config["MESSAGE_RECEIVED"].get("URLS", [])
        logger.debug(f"MESSAGE_RECEIVED: ENABLED={self.config.webhook['message_received']['enabled']}, URLs={len(msg_received_urls)}")
        for i, url_config in enumerate(msg_received_urls):
            logger.debug(f"  URL {i+1}: {url_config.get('NAME', 'Unnamed')} - {url_config.get('URL', 'No URL')} (Enabled: {url_config.get('ENABLED', True)})")

        # Log MESSAGE_SENT configuration
        msg_sent_urls = webhook_config["MESSAGE_SENT"].get("URLS", [])
        logger.debug(f"MESSAGE_SENT: ENABLED={self.config.webhook['message_sent']['enabled']}, URLs={len(msg_sent_urls)}")
        for i, url_config in enumerate(msg_sent_urls):
            logger.debug(f"  URL {i+1}: {url_config.get('NAME', 'Unnamed')} - {url_config.get('URL', 'No URL')} (Enabled: {url_config.get('ENABLED', True)})")

        # Initialize webhook manager with uppercase keys
        self.webhook_manager = WebhookManager(webhook_config)

        # Log websockets library version for debugging
        self._log_websockets_version()

    def _log_websockets_version(self):
        """Log the websockets library version for debugging purposes."""
        try:
            import websockets
            version = getattr(websockets, '__version__', 'unknown')
            logger.debug(f"WebSocket service initialized with websockets library version: {version}")
        except Exception as e:
            logger.debug(f"Could not determine websockets library version: {e}")

    def _is_connection_open(self) -> bool:
        """
        Safely check if the Socket.IO connection is open.

        Returns:
            bool: True if connection is open, False otherwise
        """
        try:
            # Check Socket.IO connection
            if self.sio_client and self.sio_client.connected:
                return True

            # Fallback to old WebSocket connection if Socket.IO is not available
            if self.ws_connection:
                # Try the new way first (websockets 11.x+)
                if hasattr(self.ws_connection, 'closed'):
                    return not self.ws_connection.closed
                # Fallback to old way (websockets 10.x and earlier)
                elif hasattr(self.ws_connection, 'open'):
                    return self.ws_connection.open

            return False
        except Exception as e:
            logger.warning(f"Error checking connection status: {e}")
            return False

    async def get_chat_login_info(self):
        """
        Get chat login information from Shopee API.

        This method calls the mini/login endpoint to get the chat token and other login information.

        Returns:
            Tuple[Dict[str, Any], int]: Tuple with login info data and HTTP status code
        """
        try:
            # Extract CSRF token using credential manager
            csrf_token = self.session.credential_manager.get_csrf_token()

            # Log token info (partial for security)
            if csrf_token and len(csrf_token) > 8:
                logger.info(f"Using CSRF token: {csrf_token[:4]}...{csrf_token[-4:]}")
            elif csrf_token:
                logger.info("Using CSRF token (hidden for security)")

            if not csrf_token:
                logger.warning("Could not extract CSRF token from cookies")
                return {"error": "Could not extract CSRF token from cookies"}, 400

            # Build request URL and data
            login_url = "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/mini/login"
            data = {
                "csrf_token": csrf_token,
                "source": "pcmall",
                "_api_source": "pcmall"
            }

            # Save original headers
            original_headers = {}
            for key in self.session.session.headers:
                original_headers[key] = self.session.session.headers[key]

            # Set specific headers
            headers_to_set = {
                "Cookie": self.session.credential_manager.cookie,
                "Authorization": self.session.credential_manager.authorization_code,
                "User-Agent": self.session.credential_manager.user_agent,
                "Referer": "https://seller.shopee.com.my/webchat/conversations",
                "X-Requested-With": "XMLHttpRequest",
                "Accept": "application/json",
                "Content-Type": "application/x-www-form-urlencoded"
            }

            self.session.session.headers.update(headers_to_set)

            # Send POST request
            try:
                logger.info("Sending POST request to mini/login endpoint")
                response = self.session.post(login_url, data=data)
                logger.info(f"Response status code: {response.status_code}")

                if response.status_code != 200:
                    logger.warning(f"Failed to get chat login info: HTTP {response.status_code}")
                    return {"error": f"Failed to get chat login info: HTTP {response.status_code}"}, response.status_code
            finally:
                # Restore original headers
                self.session.session.headers.clear()
                self.session.session.headers.update(original_headers)

            # Parse response
            try:
                data = response.json()
                logger.info("Successfully obtained chat login info")
                return data, 200
            except Exception as e:
                logger.error(f"Error parsing chat login info response: {e}")
                return {"error": f"Error parsing response: {str(e)}"}, 500

        except Exception as e:
            logger.error(f"Error getting chat login info: {e}")
            return {"error": f"Error getting chat login info: {str(e)}"}, 500

    async def get_chat_token(self):
        """
        Get the chat token from Shopee API.

        Returns:
            str: The chat token (p_token) if successful, None otherwise
        """
        try:
            login_info, status_code = await self.get_chat_login_info()

            if status_code != 200:
                logger.warning("Failed to get chat login info")
                return None

            # Use p_token instead of token for WebSocket authentication
            p_token = login_info.get("p_token")

            if p_token:
                logger.info("Successfully obtained chat token (p_token)")
                return p_token
            else:
                logger.warning("No p_token found in login info response")
                return None

        except Exception as e:
            logger.error(f"Error getting chat token: {e}")
            return None

    async def test_authorization_status(self):
        """Test current authorization status by calling the login API."""
        try:
            logger.info("Testing authorization status...")

            # Check if we have basic credentials
            auth_token = self.session.credential_manager.authorization_code
            cookies = self.session.credential_manager.cookie

            if not auth_token or not cookies:
                logger.error("✗ Missing basic credentials (authorization_code or cookies)")
                self.auth_failure_count += 1
                self.last_auth_failure_time = datetime.now()
                return False

            # Test chat login API
            login_info, status_code = await self.get_chat_login_info()

            if status_code == 200:
                logger.info("✓ Chat login API successful")
                user_info = login_info.get("user", {})
                logger.info(f"User ID: {user_info.get('uid', 'N/A')}")
                logger.info(f"User Name: {user_info.get('name', 'N/A')}")

                # Test token extraction
                p_token = login_info.get("p_token")
                if p_token:
                    logger.info("✓ P-token extracted successfully")
                    logger.info(f"P-token length: {len(p_token)}")
                else:
                    logger.warning("✗ No p_token found in response")

                # Reset auth failure count on success
                self.auth_failure_count = 0
                self.last_auth_failure_time = None
                return True
            else:
                # Track auth failures
                self.auth_failure_count += 1
                self.last_auth_failure_time = datetime.now()

                logger.error(f"✗ Chat login API failed with status {status_code} (failure #{self.auth_failure_count})")
                logger.error(f"Response: {login_info}")

                # Provide specific error guidance
                if status_code == 401:
                    logger.error("✗ Authentication failed - credentials may be expired or invalid")
                elif status_code == 403:
                    logger.error("✗ Access forbidden - account may be restricted or cookies invalid")
                elif status_code == 429:
                    logger.error("✗ Rate limited - too many requests, will backoff")

                # If we have too many auth failures, suggest longer backoff
                if self.auth_failure_count >= self.max_auth_failures:
                    logger.error(f"✗ Too many consecutive auth failures ({self.auth_failure_count}). "
                               f"Will wait {self.auth_backoff_duration} seconds before next attempt.")
                    logger.error("✗ Please check and update your credentials in config.json")

                return False

        except Exception as e:
            # Track auth failures for exceptions too
            self.auth_failure_count += 1
            self.last_auth_failure_time = datetime.now()
            logger.error(f"✗ Authorization test failed: {e} (failure #{self.auth_failure_count})")
            import traceback
            logger.error(f"Authorization test error details: {traceback.format_exc()}")
            return False

    async def connect_to_shopee(self):
        """
        Connect to Shopee's WebSocket server using Socket.IO client.

        Returns:
            bool: True if connection was successful, False otherwise
        """
        if self.is_connected and self.sio_client and self.sio_client.connected:
            logger.info("Already connected to Shopee Socket.IO server")
            return True

        try:
            # Check if we're in auth failure backoff period
            if (self.last_auth_failure_time and
                self.auth_failure_count >= self.max_auth_failures and
                (datetime.now() - self.last_auth_failure_time).total_seconds() < self.auth_backoff_duration):

                remaining_time = self.auth_backoff_duration - (datetime.now() - self.last_auth_failure_time).total_seconds()
                logger.warning(f"In auth failure backoff period. {remaining_time:.0f} seconds remaining.")
                logger.info("Attempting to reset auth backoff and test credentials again...")

                # Reset auth failure tracking to allow immediate retry
                self.auth_failure_count = 0
                self.last_auth_failure_time = None
                logger.info("Auth failure backoff reset - proceeding with connection attempt")

            # Test authorization status first
            logger.info("Testing authorization status before Socket.IO connection...")
            auth_ok = await self.test_authorization_status()
            if not auth_ok:
                logger.error("Authorization test failed - Socket.IO connection may fail")
                return False

            # Get WebSocket URL - ensure it's the Socket.IO URL
            ws_url = self.config.urls.get("websocket")
            if not ws_url:
                logger.error("WebSocket URL not configured")
                return False

            # Ensure the URL is in Socket.IO format
            if "socket.io" not in ws_url:
                # Convert to Socket.IO URL if needed
                if ws_url.startswith("wss://seller-push-ws.shopee.com.my"):
                    ws_url = "wss://seller-push-ws.shopee.com.my/socket.io/?region=my&_v=new&EIO=3&transport=websocket"
                else:
                    logger.error(f"Invalid WebSocket URL format: {ws_url}")
                    return False

            logger.info(f"Using Socket.IO URL: {ws_url}")

            # Get authorization info
            auth_token = self.session.credential_manager.authorization_code
            cookies = self.session.credential_manager.cookie
            csrf_token = self.session.credential_manager.get_csrf_token()

            # Set headers for Socket.IO connection
            headers = {
                "Cookie": cookies,
                "Authorization": auth_token,
                "User-Agent": self.session.credential_manager.user_agent,
                "Origin": "https://shopee.com.my",
                "Referer": "https://seller.shopee.com.my/webchat/conversations",
                "Accept-Language": "en-US,en;q=0.9",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache"
            }

            logger.info("Creating Socket.IO client for Shopee WebSocket server")

            # Create Socket.IO client with correct parameters
            self.sio_client = socketio.AsyncClient(
                logger=False,  # Disable Socket.IO's own logging to avoid spam
                engineio_logger=False,
                # Socket.IO handles ping/pong automatically, no need for manual ping_interval
            )

            # Setup event handlers
            await self._setup_socketio_handlers()

            # Connect to Socket.IO server
            try:
                logger.info(f"Connecting to Socket.IO server: {ws_url}")

                # The python-socketio client handles the /socket.io/ path and transport
                # parameters. We pass the full URL and let it manage the connection details.
                await self.sio_client.connect(
                    ws_url,
                    headers=headers,
                    transports=['websocket'],
                    wait_timeout=30
                )

                # Give Socket.IO a moment to fully establish connection
                await asyncio.sleep(0.5)

                # Verify connection was successful with retry logic
                connection_verified = False
                for attempt in range(3):  # Try 3 times with small delays
                    if self.sio_client.connected:
                        connection_verified = True
                        break
                    logger.debug(f"Connection verification attempt {attempt + 1}/3 - waiting...")
                    await asyncio.sleep(0.5)

                if not connection_verified:
                    logger.error("Socket.IO connection failed - client reports not connected after verification attempts")
                    return False

                logger.info("✓ Socket.IO connected successfully")
                self.sio_connected = True
                self.is_connected = True
                self.reconnect_attempt = 0

                # Track connection start time for disconnect notifications
                self.connection_start_time = datetime.now()
                # Note: Email rate limiting is handled by EmailService

                # Wait for connection to stabilize
                await asyncio.sleep(2)

                # Get chat token and send login message
                logger.info("Sending login message to Socket.IO server...")
                token = await self.get_chat_token()
                if not token:
                    logger.error("Failed to get chat token")
                    return False

                # Get user info
                login_info, _ = await self.get_chat_login_info()
                user_info = login_info.get("user", {})
                account_id = user_info.get("uid", f"0-{self.config.shop_id}")

                # Prepare login data exactly as browser sends
                login_payload = {
                    "token": token,  # This is the p_token
                    "account_id": str(account_id),
                    "app_id": "pmminichat"
                }

                # Send login event using the correct 420 format as seen in browser
                # Browser sends: 420["login",{"token":"xxx","account_id":"0-*********","app_id":"pmminichat"}]
                logger.info("Sending 420 format login message...")

                # Wait a moment for the Socket.IO connection to fully establish
                await asyncio.sleep(1)

                # Verify connection is still active before sending login (with small delay)
                await asyncio.sleep(0.2)  # Give connection time to stabilize
                if not self.sio_client or not self.sio_client.connected:
                    logger.error("Socket.IO connection lost before sending login message")
                    return False

                try:
                    # Use the Socket.IO client's internal websocket to send raw 420 message
                    import json
                    login_message = f'420["login",{json.dumps(login_payload)}]'
                    logger.info(f"Sending raw login message: {login_message}")

                    # Check if we can access the underlying websocket connection
                    raw_ws_available = (hasattr(self.sio_client, 'eio') and
                                      self.sio_client.eio and
                                      hasattr(self.sio_client.eio, 'ws') and
                                      self.sio_client.eio.ws is not None)

                    if raw_ws_available:
                        # Use send_str() for aiohttp ClientWebSocketResponse
                        await self.sio_client.eio.ws.send_str(login_message)
                        logger.info(f"✓ 420 login message sent successfully with account_id={account_id}")
                    else:
                        # Fallback to regular emit if we can't access the raw websocket
                        logger.warning("Cannot access raw websocket, falling back to regular emit")
                        if self.sio_client.connected:
                            await self.sio_client.emit('login', login_payload)
                            logger.info(f"✓ Fallback login message sent with account_id={account_id}")
                        else:
                            logger.error("Socket.IO client not connected, cannot send login message")
                            return False

                except Exception as e:
                    logger.error(f"Error sending 420 login message: {e}")
                    # Fallback to regular emit only if still connected
                    try:
                        if self.sio_client and self.sio_client.connected:
                            await self.sio_client.emit('login', login_payload)
                            logger.info(f"✓ Fallback login message sent with account_id={account_id}")
                        else:
                            logger.error("Socket.IO client not connected for fallback, login failed")
                            return False
                    except Exception as fallback_error:
                        logger.error(f"Fallback login also failed: {fallback_error}")
                        return False

                # Wait a bit to see if we get disconnected immediately
                await asyncio.sleep(3)

                # Check connection status after login attempt with retry logic
                login_connection_verified = False
                for attempt in range(5):  # Try 5 times with delays
                    if self.sio_client and self.sio_client.connected:
                        login_connection_verified = True
                        break
                    logger.debug(f"Post-login connection verification attempt {attempt + 1}/5 - waiting...")
                    await asyncio.sleep(1)

                if not login_connection_verified:
                    logger.error("Connection was dropped after login - possible auth failure")

                    # Track auth failure
                    self.auth_failure_count += 1
                    self.last_auth_failure_time = datetime.now()

                    # Only send email after multiple auth failures to prevent spam
                    if self.auth_failure_count >= 2:
                        asyncio.create_task(self._send_disconnect_email_notification("Connection dropped after login (possible auth failure)"))

                    # Clean up the failed connection
                    if self.sio_client:
                        try:
                            await self.sio_client.disconnect()
                        except:
                            pass
                        self.sio_client = None

                    self.sio_connected = False
                    self.is_connected = False
                    return False

                # Start monitoring and heartbeat
                self._start_socketio_monitoring()

                # Start Shopee-specific heartbeat
                self._start_shopee_heartbeat()

                return True

            except Exception as e:
                logger.error(f"Failed to connect to Shopee Socket.IO server: {e}")
                import traceback
                logger.error(f"Detailed error: {traceback.format_exc()}")
                
                # Clean up on failure
                if self.sio_client:
                    try:
                        await self.sio_client.disconnect()
                    except:
                        pass
                    self.sio_client = None


                
                self.sio_connected = False
                self.is_connected = False
                return False
                
        except Exception as e:
            self.reconnect_attempt += 1
            wait_time = min(60, self.reconnect_attempt * 5)  # Exponential backoff with max 60 seconds
            logger.error(f"Failed to connect to Shopee Socket.IO server: {e}. Retrying in {wait_time}s")
            return False

    async def _setup_socketio_handlers(self):
        """Set up Socket.IO event handlers."""

        @self.sio_client.event
        async def connect():
            logger.info("Socket.IO connected successfully")
            logger.info(f"[WebSocket] Socket.IO connected at {datetime.now()}")
            self.sio_connected = True

        @self.sio_client.event
        async def disconnect():
            disconnect_time = datetime.now()
            logger.warning("Socket.IO disconnected")
            logger.warning(f"[WebSocket] Socket.IO disconnected at {disconnect_time}")
            self.sio_connected = False
            self.is_connected = False
            self.last_disconnect_time = disconnect_time

            # Cancel Shopee heartbeat task when disconnected
            if self.shopee_heartbeat_task and not self.shopee_heartbeat_task.done():
                self.shopee_heartbeat_task.cancel()
                logger.debug("Shopee heartbeat task cancelled due to disconnection")

            # Only send email notification if we've had multiple consecutive failures
            # This prevents spam from rapid connect-disconnect cycles
            if self.reconnect_attempt >= 2:  # Only after 2+ failed reconnection attempts
                asyncio.create_task(self._send_disconnect_email_notification("Socket.IO disconnected after multiple failures"))

            # Broadcast disconnection to clients
            try:
                await self.broadcast_message({
                    "type": "status_update",
                    "message": "Socket.IO disconnected from Shopee",
                    "connected_to_shopee": False,
                    "timestamp": disconnect_time.isoformat()
                })
            except Exception as e:
                logger.error(f"Error broadcasting disconnection status: {e}")



        @self.sio_client.event
        async def connect_error(data):
            logger.error(f"Socket.IO connection error: {data}")

        @self.sio_client.event
        async def message(data):
            """Handle incoming messages from Shopee."""
            try:
                self.last_message_time = datetime.now()

                # Extract message ID for logging
                message_id = data.get('message_id', 'unknown')
                message_type = data.get('message_type', 'unknown')

                # Only log message reception in debug mode to reduce spam
                logger.debug(f"Received Socket.IO message - ID: {message_id}, Type: {message_type}")

                # Only log full data in debug mode to reduce spam
                logger.debug(f"Full message data: {data}")

                # Process the message and forward to connected clients (non-blocking)
                asyncio.create_task(self._process_shopee_message(data))

            except Exception as e:
                logger.error(f"Error processing Socket.IO message: {e}")
                # Don't let message processing errors affect the connection

        @self.sio_client.event
        async def heartbeat(data):
            """Handle heartbeat messages."""
            logger.debug(f"Received heartbeat: {data}")
            self.last_ping_time = datetime.now()

        # Handle any other events that might come from Shopee
        @self.sio_client.event
        async def login_response(data):
            """Handle login response from Shopee."""
            logger.info(f"Login response received: {data}")

        # Add more event handlers for debugging
        @self.sio_client.event
        async def error(data):
            """Handle error events."""
            logger.error(f"Socket.IO error event: {data}")

        @self.sio_client.event
        async def auth_error(data):
            """Handle authentication error events."""
            logger.error(f"Socket.IO auth error: {data}")

        @self.sio_client.event
        async def unauthorized(data):
            """Handle unauthorized events."""
            logger.error(f"Socket.IO unauthorized: {data}")

        # Add a catch-all event handler for debugging
        @self.sio_client.on('*')
        async def catch_all(event, *args):
            """Catch all Socket.IO events for debugging."""
            logger.debug(f"Socket.IO event '{event}' received with args: {args}")

            # Update ping time for any activity
            if event in ['ping', 'pong', 'heartbeat']:
                self.last_ping_time = datetime.now()
                logger.debug(f"Updated last_ping_time for event: {event}")

    async def _send_disconnect_email_notification(self, disconnect_reason: str):
        """
        Send email notification about WebSocket disconnection.
        
        Args:
            disconnect_reason: Reason for the disconnection
        """
        try:
            # Check if email service is available and configured
            if not self.email_service:
                logger.debug("Email service not available, skipping disconnect notification")
                return

            # Check if email is configured in the config
            email_config = getattr(self.config, 'email_sender', {})
            if not email_config.get('from_email') or not email_config.get('from_password') or not email_config.get('default_to_email'):
                logger.debug("Email configuration incomplete, skipping disconnect notification")
                return

            # Note: Email spam prevention is now handled by EmailService rate limiting

            # Prepare email details
            last_message_time = self.last_message_time or datetime.now()
            connection_start_time = self.connection_start_time or datetime.now()
            reconnect_attempt = self.reconnect_attempt
            
            # Try to reconnect once to see if it's successful
            reconnect_success = False
            try:
                logger.info("Attempting reconnection for email notification status...")
                reconnect_success = await self.connect_to_shopee()
                if reconnect_success:
                    logger.info("Reconnection successful during email notification")
                else:
                    logger.warning("Reconnection failed during email notification")
            except Exception as e:
                logger.error(f"Error during reconnection attempt for email: {e}")

            # Gather health metrics
            health_metrics = {
                "reconnect_attempts": reconnect_attempt,
                "auth_failure_count": self.auth_failure_count,
                "connected_clients": len(self.connected_clients),
                "websocket_enabled": True,
                "last_ping_time": self.last_ping_time.isoformat() if self.last_ping_time else None,
                "connection_duration_seconds": (datetime.now() - connection_start_time).total_seconds() if connection_start_time else 0
            }

            # Send the email notification
            logger.info("Sending WebSocket disconnect email notification...")
            email_sent = self.email_service.send_websocket_disconnect_email(
                from_email=email_config['from_email'],
                from_password=email_config['from_password'],
                to_email=email_config['default_to_email'],
                disconnect_reason=disconnect_reason,
                last_message_time=last_message_time,
                connection_start_time=connection_start_time,
                reconnect_attempt=reconnect_attempt,
                reconnect_success=reconnect_success,
                health_metrics=health_metrics,
                error_text=None
            )

            if email_sent:
                logger.info("WebSocket disconnect email notification sent successfully")
            else:
                logger.warning("Failed to send WebSocket disconnect email notification")

        except Exception as e:
            logger.error(f"Error sending WebSocket disconnect email notification: {e}")
            import traceback
            logger.error(f"Email notification error details: {traceback.format_exc()}")

    async def _process_socketio_event(self, event, args):
        """Process Socket.IO events for potential messages."""
        try:
            logger.debug(f"Processing Socket.IO event '{event}' with {len(args)} args")

            # For any event with data, forward to clients only (no webhook processing)
            event_obj = {
                "type": "socketio_event",
                "event": event,
                "data": args,
                "timestamp": time.time(),
                "server_time": datetime.now().isoformat()
            }

            # Broadcast to connected clients
            await self.broadcast_message(event_obj)

        except Exception as e:
            logger.error(f"Error processing Socket.IO event '{event}': {e}")
            import traceback
            logger.error(f"Event processing error details: {traceback.format_exc()}")

    async def _process_chat_message(self, message_data):
        """Process chat messages for webhook notifications and caching."""
        try:
            logger.debug(f"Processing chat message: {type(message_data)}")

            # Check if this looks like a Shopee message
            if isinstance(message_data, dict):
                message_type = message_data.get("message_type", "")
                
                if message_type == "message":
                    # Parse the message content
                    message_content = message_data.get("message_content")
                    if isinstance(message_content, str):
                        try:
                            message_content = json.loads(message_content)
                        except json.JSONDecodeError:
                            logger.warning("Failed to parse message_content as JSON")
                            return
                    
                    conversation_id = message_content.get("conversation_id") if message_content else None
                    logger.debug(f"Processing chat message for conversation: {conversation_id}")
                    
                    if conversation_id and message_content:
                        # Send webhook notification based on message sender
                        try:
                            send_by_yourself = message_content.get("send_by_yourself", False)
                            logger.debug(f"Message sender info - send_by_yourself: {send_by_yourself}")

                            if send_by_yourself:
                                # Message sent by the user
                                logger.debug(f"Sending message_sent webhook for conversation {conversation_id}")
                                webhook_payload = {
                                    "message": message_content,
                                    "conversation_id": conversation_id,
                                    "event": "message_sent"
                                }
                                if self.webhook_manager:
                                    webhook_task = asyncio.create_task(self.webhook_manager.send_message_sent_webhook(webhook_payload))
                                    webhook_task.add_done_callback(
                                        lambda t: logger.debug(f"Message sent webhook task completed: {t.result() if not t.exception() else t.exception()}")
                                    )
                            else:
                                # Message received from another party
                                logger.debug(f"Sending message_received webhook for conversation {conversation_id}")
                                webhook_payload = {
                                    "message": message_content,
                                    "conversation_id": conversation_id,
                                    "event": "message_received"
                                }
                                if self.webhook_manager:
                                    webhook_task = asyncio.create_task(self.webhook_manager.send_message_received_webhook(webhook_payload))
                                    webhook_task.add_done_callback(
                                        lambda t: logger.debug(f"Message received webhook task completed: {t.result() if not t.exception() else t.exception()}")
                                    )
                        except Exception as e:
                            logger.error(f"Error sending webhook notification: {e}")
                            import traceback
                            logger.error(f"Webhook notification error details: {traceback.format_exc()}")
                        
                        # Cache the message if caching is enabled
                        if self.config.cache["enabled"] and self.config.cache.get("conversation_messages", {}).get("enabled", False):
                            try:
                                logger.debug(f"Caching message for conversation {conversation_id}")
                                
                                # Get existing cached messages or create new entry
                                cached_data = self.cache_manager.conversation_messages.get(conversation_id)
                                if not cached_data:
                                    cached_data = {"messages": []}
                                
                                # Add the new message to the cache
                                cached_data["messages"].insert(0, message_content)  # Add at the beginning (newest first)
                                
                                # Update the cache
                                self.cache_manager.conversation_messages.set(conversation_id, cached_data)
                                logger.debug(f"Message cached for conversation {conversation_id}")
                            except Exception as e:
                                logger.error(f"Error caching message: {e}")
                        else:
                            logger.debug("Message caching is disabled")
                
        except Exception as e:
            logger.error(f"Error processing chat message: {e}")
            import traceback
            logger.error(f"Chat message processing error details: {traceback.format_exc()}")

    def _start_socketio_monitoring(self):
        """Start Socket.IO connection monitoring (non-blocking)."""
        logger.info("Starting Socket.IO monitoring")

        # Socket.IO client handles heartbeat automatically
        # The event handlers will automatically process incoming messages

    def _start_shopee_heartbeat(self):
        """Start Shopee-specific heartbeat task."""
        logger.info("Starting Shopee heartbeat task")

        # Start heartbeat task
        if not hasattr(self, 'shopee_heartbeat_task') or self.shopee_heartbeat_task is None or self.shopee_heartbeat_task.done():
            self.shopee_heartbeat_task = asyncio.create_task(self._shopee_heartbeat_loop())
            logger.info("✓ Shopee heartbeat task started")

    async def _shopee_heartbeat_loop(self):
        """Send Shopee-specific heartbeat messages to maintain connection."""
        logger.info("Starting Shopee heartbeat loop")
        consecutive_failures = 0
        max_consecutive_failures = 3

        while True:
            try:
                # Check if Socket.IO client is connected
                if self.sio_client and self.sio_client.connected and self.sio_connected:
                    try:
                        # Send Shopee heartbeat in the exact format they expect
                        heartbeat_data = "pmminichat|0|0"
                        await self.sio_client.emit('heartbeat', heartbeat_data)
                        logger.debug(f"✓ Sent Shopee heartbeat: {heartbeat_data}")

                        # Update last ping time and reset failure counter
                        self.last_ping_time = datetime.now()
                        consecutive_failures = 0

                    except Exception as heartbeat_error:
                        consecutive_failures += 1
                        logger.error(f"Failed to send heartbeat (attempt {consecutive_failures}/{max_consecutive_failures}): {heartbeat_error}")

                        # If heartbeat fails multiple times, the connection might be broken
                        if consecutive_failures >= max_consecutive_failures:
                            logger.warning(f"Heartbeat failed {consecutive_failures} consecutive times - connection may be broken")
                            self.sio_connected = False
                            self.is_connected = False
                            break  # Exit heartbeat loop to trigger reconnection

                        if "not a connected namespace" in str(heartbeat_error):
                            logger.warning("Heartbeat failed due to namespace error - connection may be broken")
                            self.sio_connected = False
                            self.is_connected = False
                            break  # Exit heartbeat loop to trigger reconnection
                else:
                    logger.debug("Socket.IO not connected, skipping Shopee heartbeat")

                # Wait for heartbeat interval (30 seconds for more frequent heartbeat)
                await asyncio.sleep(30)

            except asyncio.CancelledError:
                logger.info("Shopee heartbeat loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error sending Shopee heartbeat: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(5)





    async def _process_shopee_message(self, data):
        """Process messages received from Shopee and forward to clients."""
        try:
            # Extract message ID for deduplication
            message_id = data.get('message_id', '')
            if not message_id:
                logger.warning("Received message without message_id, processing anyway")
                # Generate a unique identifier if no message_id
                import hashlib
                message_content = str(data)
                message_id = hashlib.md5(message_content.encode()).hexdigest()[:16]
            
            # Check if we've already processed this message
            if message_id in self.processed_message_ids:
                logger.debug(f"Skipping duplicate message: {message_id}")
                return
            
            # Add to processed messages set
            self.processed_message_ids.add(message_id)
            
            # Limit the size of processed messages set to prevent memory leaks
            if len(self.processed_message_ids) > self.max_processed_messages:
                # Remove oldest 100 messages
                oldest_messages = list(self.processed_message_ids)[:100]
                for old_id in oldest_messages:
                    self.processed_message_ids.discard(old_id)
            
            # Check message type - skip notification messages that are just status updates
            message_type = data.get('message_type', '')
            if message_type == 'notification':
                # Parse message content to check if it's a status update
                message_content = data.get('message_content', '')
                if isinstance(message_content, str):
                    try:
                        parsed_content = json.loads(message_content)
                        content_type = parsed_content.get('type', '')
                        
                        # Skip chat status updates as they're not actual messages
                        if content_type == 'chat_update_msg_status':
                            logger.debug(f"Skipping chat status update message: {message_id}")
                            return
                            
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse notification message content: {message_content}")
            
            # Only log message processing in debug mode to reduce spam
            logger.debug(f"Processing unique message: {message_id} (type: {message_type})")
            
            # Create message object for broadcasting
            message_obj = {
                "type": "shopee_message",
                "data": data,
                "timestamp": time.time(),
                "server_time": datetime.now().isoformat()
            }

            # Cache the message if caching is enabled
            if self.cache_manager and self.config.cache.get("conversation_messages", {}).get("enabled", False):
                # For now, cache all messages. In the future, we could filter by conversation
                cache_key = f"shopee_message_{int(time.time())}"
                self.cache_manager.conversation_messages.set(cache_key, [message_obj])

            # Send webhook notification if enabled (non-blocking)
            if self.webhook_manager:
                webhook_task = asyncio.create_task(
                    self.webhook_manager.send_message_received_webhook(message_obj)
                )
                webhook_task.add_done_callback(
                    lambda t: logger.debug(f"Webhook task completed: {t.result() if not t.exception() else t.exception()}")
                )

            # Broadcast to all connected clients
            await self.broadcast_message(message_obj)

        except Exception as e:
            logger.error(f"Error processing Shopee message: {e}")

    async def register_client(self, client_id: str, websocket: WebSocket):
        """
        Register a new client connection.

        Args:
            client_id: Unique identifier for the client
            websocket: WebSocket connection for the client
        """
        # Check if we've reached the maximum number of clients
        if len(self.connected_clients) >= self.config.websocket["client_max_size"]:
            logger.warning(f"Maximum number of clients reached ({self.config.websocket['client_max_size']})")
            await websocket.close(code=1008, reason="Maximum number of clients reached")
            return

        # Add client to connected clients
        self.connected_clients[client_id] = websocket
        logger.debug(f"Client {client_id} connected. Total clients: {len(self.connected_clients)}")

    async def unregister_client(self, client_id: str):
        """
        Unregister a client connection.

        Args:
            client_id: Unique identifier for the client
        """
        if client_id in self.connected_clients:
            del self.connected_clients[client_id]
            logger.debug(f"Client {client_id} disconnected. Total clients: {len(self.connected_clients)}")

    def is_caching_enabled(self) -> bool:
        """
        Check if message caching is enabled and WebSocket is connected.

        Returns:
            bool: True if caching is enabled and WebSocket is connected, False otherwise
        """
        # Check if caching is enabled in config
        cache_enabled = self.config.cache.get("enabled", False)
        messages_cache_enabled = self.config.cache.get("conversation_messages", {}).get("enabled", False)
        websocket_only = self.config.cache.get("conversation_messages", {}).get("websocket_only", True)

        # If websocket_only is True, only cache when WebSocket is connected
        if websocket_only and not self.is_connected:
            return False

        return cache_enabled and messages_cache_enabled

    def get_cached_messages(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get cached messages for a conversation.

        Args:
            conversation_id: Conversation ID to get messages for

        Returns:
            Optional[Dict[str, Any]]: Cached messages or None if not found
        """
        if not self.is_caching_enabled():
            return None

        return self.cache_manager.conversation_messages.get(conversation_id)

    async def close(self):
        """
        Close the Socket.IO connection and clean up resources.
        """
        logger.info("Closing Socket.IO connection and cleaning up resources")

        # 取消监听任务
        if self.ws_task and not self.ws_task.done():
            logger.debug("Cancelling WebSocket listener task")
            self.ws_task.cancel()
            try:
                await self.ws_task
            except asyncio.CancelledError:
                pass

        # 取消Shopee心跳任务
        if self.shopee_heartbeat_task and not self.shopee_heartbeat_task.done():
            logger.debug("Cancelling Shopee heartbeat task")
            self.shopee_heartbeat_task.cancel()
            try:
                await self.shopee_heartbeat_task
            except asyncio.CancelledError:
                pass

        # 关闭Socket.IO连接
        if self.sio_client and self.sio_client.connected:
            logger.debug("Closing Socket.IO connection")
            try:
                await self.sio_client.disconnect()
            except Exception as e:
                logger.warning(f"Error closing Socket.IO connection: {e}")

        # 关闭WebSocket连接（如果存在）
        if self.ws_connection and self._is_connection_open():
            logger.debug("Closing WebSocket connection")
            try:
                await self.ws_connection.close()
            except Exception as e:
                logger.warning(f"Error closing WebSocket connection: {e}")

        # 关闭所有客户端连接
        for client_id, websocket in list(self.connected_clients.items()):
            logger.debug(f"Closing client connection: {client_id}")
            try:
                await websocket.close()
            except Exception as e:
                logger.warning(f"Error closing client connection {client_id}: {e}")

        # 关闭webhook管理器
        try:
            await self.webhook_manager.close()
        except Exception as e:
            logger.warning(f"Error closing webhook manager: {e}")

        self.connected_clients.clear()
        self.is_connected = False
        self.sio_connected = False
        self.ws_connection = None
        self.sio_client = None



        logger.info("Socket.IO resources cleaned up")

    async def broadcast_message(self, message: Any):
        """
        Broadcast a message to all connected clients.

        Args:
            message: Message to broadcast
        """
        if not self.connected_clients:
            return

        # Convert message to JSON if it's not already a string
        if not isinstance(message, str):
            try:
                message = json.dumps(message)
            except Exception as e:
                logger.error(f"Failed to serialize message: {e}")
                return

        # Send message to all connected clients
        disconnected_clients = []
        for client_id, websocket in self.connected_clients.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Failed to send message to client {client_id}: {e}")
                disconnected_clients.append(client_id)

        # Remove disconnected clients
        for client_id in disconnected_clients:
            await self.unregister_client(client_id)

    async def handle_client(self, client_id: str, websocket: WebSocket):
        """
        Handle a client connection.

        Args:
            client_id: Unique identifier for the client
            websocket: WebSocket connection for the client
        """
        logger.debug(f"New client connection: {client_id}")

        # 发送欢迎消息
        try:
            welcome_message = {
                "type": "connection_established",
                "client_id": client_id,
                "timestamp": time.time(),
                "server_time": datetime.now().isoformat(),
                "websocket_status": {
                    "connected_to_shopee": self.is_connected,
                    "client_count": len(self.connected_clients)
                }
            }
            await websocket.send_json(welcome_message)
            logger.debug(f"Sent welcome message to client {client_id}")
        except Exception as e:
            logger.error(f"Error sending welcome message to client {client_id}: {e}")

        await self.register_client(client_id, websocket)

        try:
            # 如果连接到 Shopee 的 WebSocket 尚未建立，尝试连接
            if not self.is_connected:
                logger.info(f"Client {client_id} connected but not connected to Shopee yet. Attempting to connect...")
                await self.connect_to_shopee()

            # 发送当前状态信息
            status_message = {
                "type": "status_update",
                "connected_to_shopee": self.is_connected,
                "last_message_time": self.last_message_time.isoformat() if self.last_message_time else None,
                "client_count": len(self.connected_clients),
                "timestamp": time.time()
            }
            await websocket.send_json(status_message)

            # 保持连接并处理客户端消息
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                logger.debug(f"Received message from client {client_id}: {data}")

                # 尝试解析消息
                try:
                    message = json.loads(data)

                    # 处理特殊命令
                    if message.get("command") == "status":
                        # 发送状态更新
                        await websocket.send_json({
                            "type": "status_response",
                            "connected_to_shopee": self.is_connected,
                            "last_message_time": self.last_message_time.isoformat() if self.last_message_time else None,
                            "client_count": len(self.connected_clients),
                            "timestamp": time.time()
                        })
                    elif message.get("command") == "reconnect":
                        # 尝试重新连接到 Shopee
                        logger.info(f"Client {client_id} requested reconnection to Shopee")

                        # 关闭现有连接
                        if self.ws_connection:
                            try:
                                await self.ws_connection.close()
                                self.ws_connection = None
                            except:
                                pass

                        # 取消现有任务
                        if self.ws_task and not self.ws_task.done():
                            try:
                                self.ws_task.cancel()
                                await asyncio.sleep(0.1)  # 给任务一点时间取消
                            except:
                                pass

                        # 重置连接状态
                        self.is_connected = False

                        # 重新连接
                        success = await self.connect_to_shopee()
                        await websocket.send_json({
                            "type": "reconnect_response",
                            "success": success,
                            "connected": self.is_connected,
                            "timestamp": time.time()
                        })
                except json.JSONDecodeError:
                    # 不是 JSON 格式，忽略
                    pass
                except Exception as e:
                    logger.error(f"Error processing message from client {client_id}: {e}")

        except WebSocketDisconnect:
            logger.debug(f"Client {client_id} disconnected")
            await self.unregister_client(client_id)
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
            import traceback
            logger.debug(f"Client handling error details: {traceback.format_exc()}")
            await self.unregister_client(client_id)

    def reset_auth_backoff(self):
        """
        Reset authentication failure backoff to allow immediate reconnection.

        This is useful when credentials have been updated and we want to try
        connecting immediately without waiting for the backoff period.
        """
        logger.info("Resetting authentication failure backoff")
        self.auth_failure_count = 0
        self.last_auth_failure_time = None
        logger.info("✓ Authentication backoff reset - ready for immediate reconnection")

    async def send_test_disconnect_email(self, reason: str = "Manual test"):
        """
        Send a test disconnect email notification.
        
        This method can be used for testing the email notification system.
        
        Args:
            reason: Reason for the test notification
        """
        logger.info(f"Sending test disconnect email notification: {reason}")
        # Note: Rate limiting is handled by EmailService, test emails may be blocked if sent too frequently
        await self._send_disconnect_email_notification(f"TEST: {reason}")

    async def reconnect_after_config_update(self):
        """
        Reconnect to Shopee's Socket.IO server after a config update.

        This method is called when the config.json file is updated.
        It closes the existing connection and reconnects with the new credentials.
        """
        logger.info("Reconnecting Socket.IO after config update")

        # Reset auth backoff since we have new credentials
        self.reset_auth_backoff()

        # Close existing Socket.IO connection
        if self.sio_client and self.sio_client.connected:
            try:
                await self.sio_client.disconnect()
            except Exception as e:
                logger.error(f"Error closing Socket.IO connection: {e}")
            self.sio_client = None



        # Close old WebSocket connection if it exists
        if self.ws_connection:
            try:
                await self.ws_connection.close()
            except Exception as e:
                logger.error(f"Error closing WebSocket connection: {e}")
            self.ws_connection = None

        # Cancel existing task
        if self.ws_task and not self.ws_task.done():
            self.ws_task.cancel()
            try:
                await self.ws_task
            except asyncio.CancelledError:
                pass

        # Mark as disconnected
        self.is_connected = False
        self.sio_connected = False

        # Reset reconnect attempt counter
        self.reconnect_attempt = 0

        # Try to reconnect
        success = await self.connect_to_shopee()

        if success:
            logger.info("Successfully reconnected Socket.IO after config update")
            # Broadcast reconnection status to all clients
            await self.broadcast_message({
                "type": "status_update",
                "message": "Socket.IO reconnected after config update",
                "connected_to_shopee": True,
                "timestamp": datetime.now().isoformat()
            })
        else:
            logger.warning("Failed to reconnect Socket.IO after config update")
            # Broadcast reconnection status to all clients
            await self.broadcast_message({
                "type": "status_update",
                "message": "Failed to reconnect Socket.IO after config update",
                "connected_to_shopee": False,
                "timestamp": datetime.now().isoformat()
            })

        return success

    def reset_connection_state(self):
        """Reset connection state and counters."""
        logger.info("Resetting connection state")
        self.is_connected = False
        self.sio_connected = False
        self.reconnect_attempt = 0
        self.auth_failure_count = 0
        self.last_auth_failure_time = None

        # Cancel any running tasks
        if self.shopee_heartbeat_task and not self.shopee_heartbeat_task.done():
            self.shopee_heartbeat_task.cancel()

        # Clean up client
        if self.sio_client:
            self.sio_client = None

    async def maintain_connection(self):
        """
        Maintain connection to Shopee's Socket.IO server.

        This method runs in a loop and ensures the connection is maintained.
        """
        logger.info("Starting Socket.IO connection maintenance task")

        # Track initial state
        connection_attempts = 0
        last_status_log = datetime.now()
        last_error_log = None  # Track last error to avoid spam
        last_auth_error_log = None  # Track last auth error to avoid spam

        while True:
            try:
                # Log connection status once per minute
                now = datetime.now()
                if (now - last_status_log).total_seconds() > 60:
                    last_status_log = now
                    # Only log status if we have connection issues
                    if not self.is_connected:
                        logger.info(f"Connection status: attempts={connection_attempts}, "
                                  f"auth_failures={self.auth_failure_count}, "
                                  f"connected={self.is_connected}")

                # Check connection status (prioritize Socket.IO)
                connection_active = (self.sio_connected and
                                    self.sio_client and
                                    self.sio_client.connected)

                # Check if we need to reconnect after config update
                if self.needs_reconnect_after_config_update:
                    logger.info("Config update detected, reconnecting Socket.IO...")
                    # Reset the flag
                    self.needs_reconnect_after_config_update = False

                    # Force reconnection
                    if connection_active:
                        # Close existing Socket.IO connection
                        if self.sio_client and self.sio_client.connected:
                            try:
                                await self.sio_client.disconnect()
                            except:
                                pass
                            self.sio_client = None



                        # Mark as disconnected
                        self.is_connected = False
                        self.sio_connected = False
                        connection_active = False

                        # Reset reconnect attempt counter
                        self.reconnect_attempt = 0

                        # Broadcast reconnection status to all clients
                        await self.broadcast_message({
                            "type": "status_update",
                            "message": "Socket.IO reconnecting after config update",
                            "connected_to_shopee": False,
                            "timestamp": datetime.now().isoformat()
                        })

                if not connection_active:
                    # Check if we've exceeded maximum reconnection attempts
                    if connection_attempts >= self.max_reconnect_attempts:
                        logger.error(f"Maximum reconnection attempts ({self.max_reconnect_attempts}) reached. "
                                   f"Stopping reconnection attempts for 5 minutes.")

                        # Wait 5 minutes before resetting attempt counter
                        await asyncio.sleep(300)  # 5 minutes
                        connection_attempts = 0
                        logger.info("Resetting reconnection attempt counter after 5-minute cooldown")
                        continue

                    # Check if we're in auth failure backoff period
                    if (self.last_auth_failure_time and
                        self.auth_failure_count >= self.max_auth_failures and
                        (datetime.now() - self.last_auth_failure_time).total_seconds() < self.auth_backoff_duration):

                        remaining_time = self.auth_backoff_duration - (datetime.now() - self.last_auth_failure_time).total_seconds()
                        if connection_attempts == 0:  # Only log once per cycle
                            logger.warning(f"Skipping reconnection due to auth failure backoff. {remaining_time:.0f} seconds remaining.")

                        # Wait for the reconnect interval and continue
                        interval = self.config.websocket["reconnect_interval"]
                        await asyncio.sleep(interval)
                        continue

                    # Close Socket.IO connection if it exists
                    if self.sio_client and self.sio_client.connected:
                        try:
                            await self.sio_client.disconnect()
                        except:
                            pass
                        self.sio_client = None

                    # Try to reconnect
                    connection_attempts += 1
                    logger.info(f"Socket.IO not connected. Attempting to connect (attempt {connection_attempts}/{self.max_reconnect_attempts})...")
                    success = await self.connect_to_shopee()

                    if success:
                        logger.info(f"Successfully connected to Shopee Socket.IO after {connection_attempts} attempts")
                        connection_attempts = 0
                    else:
                        # Calculate exponential backoff wait time
                        wait_time = min(60, connection_attempts * 5)  # Max 60 seconds
                        logger.warning(f"Failed to connect to Shopee Socket.IO (attempt {connection_attempts}/{self.max_reconnect_attempts}). "
                                     f"Waiting {wait_time} seconds before next attempt.")

                        # Wait with exponential backoff
                        await asyncio.sleep(wait_time)
                        continue
                else:
                    # Reset connection attempt counter
                    connection_attempts = 0

                # Check if connection is still active
                if connection_active:
                    # For Socket.IO connections, check sio_client
                    if self.sio_client and self.sio_connected:
                        if not self.sio_client.connected:
                            logger.warning("Socket.IO connection is no longer open")
                            # Send disconnect email notification for connection loss
                            asyncio.create_task(self._send_disconnect_email_notification("Socket.IO connection lost (detected in maintenance loop)"))
                            self.is_connected = False
                            self.sio_connected = False
                    # If no connection is available, mark as disconnected
                    elif not self.sio_client:
                        logger.warning("No active Socket.IO connection found")
                        # Send disconnect email notification for missing connection
                        asyncio.create_task(self._send_disconnect_email_notification("Socket.IO client not found (detected in maintenance loop)"))
                        self.is_connected = False
                        self.sio_connected = False

                # Wait before next check
                interval = self.config.websocket["reconnect_interval"]
                await asyncio.sleep(interval)
            except Exception as e:
                # Only log error if it's different from the last one or it's been more than 5 minutes
                current_error = str(e)
                now = datetime.now()

                if (last_error_log is None or
                    current_error != last_error_log or
                    (now - last_status_log).total_seconds() > 300):  # 5 minutes

                    logger.error(f"Error maintaining Socket.IO connection: {e}")
                    last_error_log = current_error
                    last_status_log = now

                await asyncio.sleep(5)  # Wait 5 seconds after error
