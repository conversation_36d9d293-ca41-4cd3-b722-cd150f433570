"""
Steam Order Service
Handles Steam order processing and management
"""

import logging
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

class SteamOrderService:
    """Service for managing Steam orders"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.order_config = config.get('order_config', {})
        self.session_config = config.get('session_config', {})
        
        self.auto_ship = self.order_config.get('auto_ship', True)
        self.chat_messages = self.order_config.get('chat_messages', {})
        self.cooldown_time = self.session_config.get('cooldown_time', 600)
        self.request_timeout = self.session_config.get('request_timeout', 60)
        
        # Order tracking
        self.processed_orders = set()
        self.order_sessions = {}
        self.lock = threading.Lock()
        
        # Load processed orders
        self.load_processed_orders()
        
    def update_config(self, config: Dict[str, Any]):
        """Update service configuration"""
        self.config = config
        self.order_config = config.get('order_config', {})
        self.session_config = config.get('session_config', {})
        
        self.auto_ship = self.order_config.get('auto_ship', True)
        self.chat_messages = self.order_config.get('chat_messages', {})
        self.cooldown_time = self.session_config.get('cooldown_time', 600)
        self.request_timeout = self.session_config.get('request_timeout', 60)
        
    def load_processed_orders(self):
        """Load processed orders from file"""
        try:
            orders_file = 'data/steam_processed_orders.json'
            if os.path.exists(orders_file):
                with open(orders_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.processed_orders = set(data.get('orders', []))
                logger.info(f"Loaded {len(self.processed_orders)} processed orders")
            else:
                self.processed_orders = set()
                self.save_processed_orders()
        except Exception as e:
            logger.error(f"Error loading processed orders: {e}")
            self.processed_orders = set()

    def save_processed_orders(self):
        """Save processed orders to file"""
        try:
            os.makedirs('data', exist_ok=True)
            orders_file = 'data/steam_processed_orders.json'
            with open(orders_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'orders': list(self.processed_orders),
                    'last_updated': datetime.now().isoformat()
                }, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving processed orders: {e}")
            
    def is_order_processed(self, order_id: str) -> bool:
        """Check if order has been processed"""
        with self.lock:
            return order_id in self.processed_orders
            
    def mark_order_processed(self, order_id: str):
        """Mark order as processed"""
        with self.lock:
            self.processed_orders.add(order_id)
            self.save_processed_orders()
            
    def process_steam_auth_request(self, order_id: str, username: str, email_service, inventory_service) -> Dict[str, Any]:
        """Process Steam authentication code request"""
        try:
            logger.info(f"Processing Steam auth request for order {order_id}, username {username}")
            
            # Check if order already processed
            if self.is_order_processed(order_id):
                return {
                    "error": "Order already processed",
                    "order_id": order_id
                }, 400
                
            # Check session cooldown
            if self._is_in_cooldown(username):
                remaining = self._get_cooldown_remaining(username)
                return {
                    "error": f"Request in cooldown. Please wait {remaining} seconds.",
                    "cooldown_remaining": remaining
                }, 429
                
            # Get authentication code from email
            auth_code = email_service.get_auth_code_for_user(username)
            
            if not auth_code:
                # Record failed attempt
                self._record_session_attempt(username, False)
                
                return {
                    "error": "Failed to retrieve Steam authentication code",
                    "order_id": order_id,
                    "username": username
                }, 500
                
            # Record successful attempt
            self._record_session_attempt(username, True)
            
            # Mark order as processed
            self.mark_order_processed(order_id)
            
            # Prepare response
            response = {
                "auth_code": auth_code,
                "order_id": order_id,
                "username": username,
                "timestamp": datetime.now().isoformat()
            }
            
            # Send chat message if configured
            if self.chat_messages.get('auth_code_success'):
                chat_message = self.chat_messages['auth_code_success'].format(
                    code=auth_code,
                    username=username,
                    order_id=order_id
                )
                response['chat_message'] = chat_message
                
            logger.info(f"Successfully processed Steam auth request for order {order_id}")
            return response, 200
            
        except Exception as e:
            logger.error(f"Error processing Steam auth request for order {order_id}: {e}")
            return {
                "error": "Internal server error",
                "order_id": order_id
            }, 500
            
    def _is_in_cooldown(self, username: str) -> bool:
        """Check if username is in cooldown"""
        with self.lock:
            if username not in self.order_sessions:
                return False
                
            session = self.order_sessions[username]
            last_request = session.get('last_request', 0)
            return (datetime.now().timestamp() - last_request) < self.cooldown_time
            
    def _get_cooldown_remaining(self, username: str) -> int:
        """Get remaining cooldown time in seconds"""
        with self.lock:
            if username not in self.order_sessions:
                return 0
                
            session = self.order_sessions[username]
            last_request = session.get('last_request', 0)
            elapsed = datetime.now().timestamp() - last_request
            remaining = max(0, self.cooldown_time - elapsed)
            return int(remaining)
            
    def _record_session_attempt(self, username: str, success: bool):
        """Record session attempt"""
        with self.lock:
            if username not in self.order_sessions:
                self.order_sessions[username] = {
                    'total_requests': 0,
                    'successful_requests': 0,
                    'failed_requests': 0,
                    'first_request': datetime.now().timestamp(),
                    'last_request': 0
                }
                
            session = self.order_sessions[username]
            session['total_requests'] += 1
            session['last_request'] = datetime.now().timestamp()
            
            if success:
                session['successful_requests'] += 1
            else:
                session['failed_requests'] += 1
                
    def get_session_stats(self, username: str) -> Optional[Dict[str, Any]]:
        """Get session statistics for username"""
        with self.lock:
            if username not in self.order_sessions:
                return None
                
            session = self.order_sessions[username].copy()
            session['in_cooldown'] = self._is_in_cooldown(username)
            session['cooldown_remaining'] = self._get_cooldown_remaining(username)
            return session
            
    def get_all_session_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get all session statistics"""
        with self.lock:
            stats = {}
            for username, session in self.order_sessions.items():
                stats[username] = session.copy()
                stats[username]['in_cooldown'] = self._is_in_cooldown(username)
                stats[username]['cooldown_remaining'] = self._get_cooldown_remaining(username)
            return stats
            
    def clear_session_data(self, username: str = None):
        """Clear session data for specific user or all users"""
        with self.lock:
            if username:
                if username in self.order_sessions:
                    del self.order_sessions[username]
                    logger.info(f"Cleared session data for {username}")
            else:
                self.order_sessions.clear()
                logger.info("Cleared all session data")
                
    def get_processed_orders_count(self) -> int:
        """Get count of processed orders"""
        with self.lock:
            return len(self.processed_orders)
            
    def cleanup(self):
        """Cleanup service resources"""
        self.save_processed_orders()
        logger.info("Steam order service cleanup completed")
