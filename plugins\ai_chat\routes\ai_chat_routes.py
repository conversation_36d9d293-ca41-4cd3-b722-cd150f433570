"""
AI Chat Routes
Handles AI chat configuration endpoints
"""

import logging
from flask import Blueprint, jsonify, request, render_template, session, redirect, url_for
from functools import wraps

logger = logging.getLogger(__name__)

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function

def create_ai_chat_blueprint(ai_chat_service):
    """Create and configure AI Chat blueprint with services"""
    
    ai_chat_bp = Blueprint('ai_chat', __name__)
    
    @ai_chat_bp.route('/ai_chat', methods=['GET'])
    @login_required
    def ai_chat_settings():
        """Render AI Chat settings page"""
        return render_template('ai_chat.html')
    
    @ai_chat_bp.route('/get_ai_config', methods=['GET'])
    @login_required
    def get_ai_config():
        """Get AI chat configuration"""
        try:
            config = ai_chat_service.get_ai_config()
            return jsonify(config)
        except Exception as e:
            logger.error(f"Error getting AI config: {e}")
            return jsonify({"error": str(e)}), 500

    @ai_chat_bp.route('/update_ai_config', methods=['POST'])
    @login_required
    def update_ai_config():
        """Update AI chat configuration"""
        try:
            new_config = request.get_json()

            if not new_config:
                return jsonify({"error": "No configuration data provided"}), 400

            # Validate configuration
            errors = ai_chat_service.validate_config(new_config)
            if errors:
                return jsonify({"error": "Validation failed", "details": errors}), 400

            # Update configuration
            success = ai_chat_service.update_ai_config(new_config)

            if success:
                return jsonify({"message": "AI configuration updated successfully"}), 200
            else:
                return jsonify({"error": "Failed to update configuration"}), 500

        except Exception as e:
            logger.error(f"Error updating AI config: {e}")
            return jsonify({"error": f"Failed to update configuration: {str(e)}"}), 500
    
    @ai_chat_bp.route('/ai_status', methods=['GET'])
    @login_required
    def get_ai_status():
        """Get AI chat status"""
        try:
            status = {
                "enabled": ai_chat_service.is_ai_enabled(),
                "has_api_key": ai_chat_service.get_api_key() is not None,
                "cooldown_minutes": ai_chat_service.get_cooldown_minutes()
            }
            return jsonify(status)
        except Exception as e:
            logger.error(f"Error getting AI status: {e}")
            return jsonify({"error": str(e)}), 500

    @ai_chat_bp.route('/test_ai_config', methods=['POST'])
    @login_required
    def test_ai_config():
        """Test AI configuration"""
        try:
            # This endpoint can be used to test AI connectivity
            # For now, just validate that required settings are present
            config = ai_chat_service.get_ai_config()

            if not config.get('DEEPSEEK_API_KEY'):
                return jsonify({
                    "success": False,
                    "message": "DeepSeek API key is required"
                }), 400

            if not config.get('AI_SYSTEM_PROMPT'):
                return jsonify({
                    "success": False,
                    "message": "System prompt is required"
                }), 400

            return jsonify({
                "success": True,
                "message": "AI configuration appears valid"
            })

        except Exception as e:
            logger.error(f"Error testing AI config: {e}")
            return jsonify({
                "success": False,
                "message": f"Error testing configuration: {str(e)}"
            }), 500
    
    return ai_chat_bp
