{"android_help": {"command": "android_help", "description": "Provide Android help instructions", "response_text": "🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑯𝒆𝒍𝒑 🤖\n\n1. Download ArmodVPN From Playstore.\n2. Copy config\n3. Buka ArmodVPN dan Click Import Clipboard\n4. Connect\n\nQA:\nIf bypass not working sometimes, try on/off airplane mode.\n\n🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑯𝒆𝒍𝒑 🤖", "image_urls": ["https://down-sg.img.susercontent.com/sg-11134209-7ra39-mb8nh8aksinqfd"], "required_params": [], "enabled": true, "created_at": "2025-06-21T05:09:04.752080", "updated_at": "2025-06-21T05:09:04.752090", "plugin_source": null, "send_images_first": true, "mark_as_unread": false}, "ios_help": {"command": "ios_help", "description": "Provide iOS help instructions", "response_text": "🤖 𝑰𝑶𝑺 𝑯𝒆𝒍𝒑 🤖\n\n1. Download [V2Box] Dari AppStore\n2. <PERSON><PERSON> [V2Box] dan <PERSON> [Config]\n3. <PERSON><PERSON> [Config] Ka<PERSON>\n4. Klik ➕ [Import v2ray uri from clipboard]\n5. Klik Config itu dalam [V2Box]\n6. <PERSON><PERSON> [Home] Klik [Connect] ▶️\n\nQA:\nIf bypass not working sometimes, try on/off airplane mode.\n\n🤖 𝑰𝑶𝑺 𝑯𝒆𝒍𝒑 🤖", "image_urls": ["https://down-my.img.susercontent.com/file/my-11134207-7r98s-lrobk5lv3v1z67"], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "plugin_source": null, "send_images_first": false, "mark_as_unread": true}, "android_digi": {"command": "android_digi", "description": "Provide Android Digi help instructions", "response_text": "🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖\n\n✨Digi Tanpa Langganan Setting\n\n1. Buka APN (Access Point Names) yang baru kat Android Sim Setting\n2. [Nama] APN letak apa2 pun boleh\n3. Edit yang macam image saya bagi\n4. <PERSON> lain tak perlu edit, terus tekan save \n5. <PERSON><PERSON>/<PERSON><PERSON> pergi yang APN kamu buka itu\n6. Connect VPN\n\n🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖", "image_urls": ["https://down-my.img.susercontent.com/file/my-11134207-7r98z-lrrlyk9ukjs440", "https://down-my.img.susercontent.com/file/my-11134207-7r98p-lrrlyk9uj57o53"], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "plugin_source": null}, "ios_digi": {"command": "ios_digi", "description": "Provide iOS Digi help instructions", "response_text": "🤖 𝒊𝑶𝑺 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖\n\n⚠️ Digi-Tanpa Langganan IOS Guide\n🔗 https://ogne.tech/apns/generator/\n\n1. Open the link in Safari\n2. Enter \"hos\"\n3. <PERSON><PERSON> \"Install\"\n4. Pergi iPhone Setting install APN itu\n5. Lepas Install APN siap, connect VPN (akan send you config & tutorial)\n\n🤖 𝒊𝑶𝑺 𝑫𝒊𝒈𝒊 𝑯𝒆𝒍𝒑 🤖", "image_urls": ["https://down-my.img.susercontent.com/file/my-11134207-7r98q-lrrlyk9uhqn852"], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "plugin_source": null}, "help": {"command": "help", "description": "Provide command list", "response_text": "🎖️ᴄᴏᴍᴍᴀɴᴅ ʟɪꜱᴛ🎖️\n----------------------------------\n\n#𝒃𝒚𝒑𝒂𝒔𝒔 >> Dapat semua config yang kita ade jual.\n#𝒊𝒏𝒇𝒐 >> Explain apa vpn itu.\n#𝒂𝒏𝒅𝒓𝒐𝒊𝒅_𝒉𝒆𝒍𝒑 >> Dapat android connect VPN tutorial\n#𝒊𝒐𝒔_𝒉𝒆𝒍𝒑 >> Dapat iOS connect VPN tutorial\n#𝒂𝒏𝒅𝒓𝒐𝒊𝒅_𝒅𝒊𝒈𝒊 >> Dapat Digi APN Setting for android\n#𝒊𝒐𝒔_𝒅𝒊𝒈𝒊 >> Dapat Digi APN setting for iOS", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "plugin_source": null}, "info": {"command": "info", "description": "Explain what VPN configuration is", "response_text": "🔥Apa VPN Config Itu?\n🅰️Untuk specific telco dan plan dapat unlimited quota / bypass speed cap\n\n🔥Example 1\n🅰️ Macam Celcom ade booster config untuk rm35 3mbps plan, lepas guna booster config boleh jadi bypass 3mbps ke full speed ikut area kamu\n\n🔥Example 2\n🅰️ Digi next prepaid sim ade tanpa langganan config, maksud<PERSON> kamu x payah subs internet apa2 pun boleh dapat unlimited quota + speed\n🅰️ Support active/exp sim juga. maksudnya sim kamu expired atau active pun boleh dpat internet.\n\n Config List: https://docs.google.com/document/d/1KV1zhrBawNip3XSaHic29u8NVD3TQNvO-k8sHXZ1wO4/edit", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "plugin_source": null}, "bypass": {"command": "bypass", "description": "Provide bypass configuration links", "response_text": "🅰️ Semua trick config yang kita ade jual:\n🔗https://docs.google.com/document/d/1KV1zhrBawNip3XSaHic29u8NVD3TQNvO-k8sHXZ1wO4/edit", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "plugin_source": null}, "openwrt_help": {"command": "openwrt_help", "description": "Provide OpenWrt Help Instructions", "response_text": "🎖️ Youtube Tutorial:\n🔗 https://www.youtube.com/watch?v=XE4oz-Vg1fA&t=3s&ab_channel=FZ", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "plugin_source": null}, "ez": {"command": "ez", "description": "Easy", "response_text": "Ezpz", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2025-06-15T02:44:10.262734", "updated_at": "2025-06-15T02:44:10.262744", "plugin_source": null}, "test_long": {"command": "test_long", "description": "Test message splitting functionality", "response_text": "🧪 This is a very long test message designed to test the automatic message splitting functionality. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2025-06-25T00:00:00Z", "updated_at": "2025-06-25T00:00:00Z", "plugin_source": null, "send_images_first": false, "mark_as_unread": false}}