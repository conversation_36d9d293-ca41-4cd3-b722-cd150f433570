# SteamCodeTool Docker Deployment Guide

This guide provides instructions for deploying SteamCodeTool using Docker.

## Quick Start

```bash
# Pull the image
docker pull limjianhui789/mtyb-tools:latest

# Create directories for persistent data
mkdir -p /path/to/mtyb-tools-data
mkdir -p /path/to/logs

# Run the container with volume mounts
docker run -d \
  --name mtyb-tools \
  -p 5000:5000 \
  -v /path/to/configs:/app/configs \
  -v /path/to/logs:/app/logs \
  --restart unless-stopped \
  -e PORT=5000 \
  -e ENVIRONMENT=production \
  limjianhui789/mtyb-tools:latest
```

## Critical Volume Mounts

⚠️ **WARNING**: Without proper volume mounts, all data will be lost when the container is updated or recreated!

### Required Configuration Directory
The `configs/` directory contains all configuration and data files organized by category:

#### Core Configuration (`configs/core/`)
- `config.json` - Main application configuration
- `plugin_config.json` - Plugin system configuration

#### Service Configuration (`configs/services/`)
- `canva_config.json` - Canva integration settings
- `config_templates.json` - VPN configuration templates
- `vpn_servers.json` - VPN server configurations

#### Plugin Configuration (`configs/plugins/`)
- `chat_commands/config.json` - Chat commands plugin settings
- `chat_commands/commands.json` - Chat command definitions
- `vpn_config_generator/config.json` - VPN generator plugin settings
- `vpn_config_generator/templates.json` - VPN configuration templates
- `vpn_config_generator/telco_configs.json` - Telco configurations

**重要**: 插件配置文件现在保存在 `configs/plugins/` 目录中，确保 Docker 容器更新时配置持久化。

#### Data Files (`configs/data/`)
- `manual_orders.json` - Manual order tracking data
- `sent_orders.json` - Sent order tracking data
- `redeemed_stock.json` - Redeemed stock tracking data
- `netflix_sessions.json` - Netflix session management data (moved to `data/netflix/`)
- `canva_orders.json` - Canva order tracking data
- `manual_invoice.json` - Manual invoice data
- `dashboard_data.json` - Dashboard state data

#### Cache Files (`configs/cache/`)
- `ai_reply_cooldown.json` - AI reply cooldown tracking
- `auto_reply_cooldown.json` - Auto reply cooldown tracking

### Optional Directories
- `logs/` - Application logs (recommended for debugging)

## Using Docker Compose

### Option 1: Standard Deployment
Use the provided `docker-compose.steamcodetool.yml` file:

```bash
# Start the service
docker-compose -f docker-compose.steamcodetool.yml up -d

# View logs
docker-compose -f docker-compose.steamcodetool.yml logs -f

# Stop the service
docker-compose -f docker-compose.steamcodetool.yml down
```

### Option 2: Pre-configured for Permission Issues
If you encounter permission errors, use the pre-configured compose file:

```bash
# Start with fixed permissions
docker-compose -f docker-compose.steamcodetool-fixed-permissions.yml up -d

# View logs
docker-compose -f docker-compose.steamcodetool-fixed-permissions.yml logs -f

# Stop the service
docker-compose -f docker-compose.steamcodetool-fixed-permissions.yml down
```

⚠️ **Important**: If you see permission errors, please read `DOCKER_PERMISSION_ISSUES.md` for detailed solutions.

## Building and Pushing to Docker Hub

Use the provided script to build and push the image:

```bash
# Build only
docker-hub-push-steamcodetool.bat --build

# Push only (if image already built)
docker-hub-push-steamcodetool.bat --push

# Build and push
docker-hub-push-steamcodetool.bat --all
```

## Environment Variables

- `PORT` - Application port (default: 5000)
- `ENVIRONMENT` - Environment mode (development/production)
- `PYTHONPATH` - Python path (set to /app)

## Permission Issues and Solutions

### Common Permission Error
If you see errors like:
```
PermissionError: [Errno 13] Permission denied: '/app/configs/core'
```

This happens because the Docker container runs as a non-root user but the mounted volumes may not have the correct permissions.

### Solution 1: Use the Permission Fix Script (Recommended)
Run the provided script to automatically fix permissions:
```bash
.\fix-docker-permissions.bat
```

### Solution 2: Manual User Mapping
Edit `docker-compose.steamcodetool.yml` and uncomment the user mapping line:
```yaml
services:
  mtyb-tools:
    # ... other settings ...
    user: "1000:1000"  # Uncomment this line
```

Then restart the container:
```bash
docker-compose -f docker-compose.steamcodetool.yml down
docker-compose -f docker-compose.steamcodetool.yml up -d
```

### Solution 3: Run as Root (Less Secure)
If other solutions don't work, you can run the container as root:
```yaml
services:
  mtyb-tools:
    # ... other settings ...
    user: "0:0"  # Run as root
```

### Solution 4: Fix Host Directory Permissions
On Linux/macOS, ensure the host directories have proper permissions:
```bash
# Create directories if they don't exist
mkdir -p configs/core configs/cache configs/data configs/services logs data

# Set proper permissions
chmod -R 755 configs logs data
```

### Troubleshooting
1. Check container logs: `docker logs mtyb-tools`
2. Test file creation: `docker exec mtyb-tools touch /app/configs/core/test.txt`
3. Check user ID in container: `docker exec mtyb-tools id`

## Health Check

The container includes a health check that verifies the application is running:
- Endpoint: `http://localhost:5000/health`
- Interval: 30 seconds
- Timeout: 10 seconds
- Retries: 3

## Updating the Container

When a new version is available:

```bash
# Pull the latest image
docker pull limjianhui789/mtyb-tools:latest

# Stop and remove the existing container
docker stop mtyb-tools
docker rm mtyb-tools

# Run the new container with the same volume mounts
# (Use the same docker run command as above)
```

Or with Docker Compose:

```bash
docker-compose -f docker-compose.steamcodetool.yml pull
docker-compose -f docker-compose.steamcodetool.yml up -d
```

## Troubleshooting

### Container won't start
- Check if all required JSON files exist on the host
- Verify file permissions
- Check Docker logs: `docker logs mtyb-tools`

### Data not persisting
- Verify volume mounts are correctly specified
- Check file ownership and permissions
- Ensure host directories exist

### Port conflicts
- Change the host port in the docker run command: `-p 5001:5000`
- Update any external references to use the new port

## Security Notes

- The container runs as a non-root user (`steamcodeuser`)
- Sensitive configuration data should be properly secured on the host
- Consider using Docker secrets for sensitive information in production

## BaoTa Panel Deployment

For deployment on BaoTa Panel:

1. Upload the JSON configuration files to your server
2. Create the Docker container through BaoTa's Docker interface
3. Configure the volume mounts in the BaoTa Docker settings
4. Set environment variables through the BaoTa interface

Remember to backup your JSON files regularly to prevent data loss!
