{% extends "base.html" %}

{% block title %}Account Management - OpenAI Plus Redeem{% endblock %}

{% block header %}
<i class="fas fa-users mr-2"></i>ChatGPT Account Management
{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.10.25/css/jquery.dataTables.min.css">
<script src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<style>
    .account-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
    }
    
    .account-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .status-active {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-expired {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .status-suspended {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .capacity-bar {
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .capacity-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .capacity-low { background-color: #10b981; }
    .capacity-medium { background-color: #f59e0b; }
    .capacity-high { background-color: #ef4444; }
</style>
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
            <h2 class="text-xl font-bold text-gray-800">ChatGPT Account Management</h2>
            <p class="text-gray-600">Manage ChatGPT Plus accounts, monitor capacity, and perform bulk operations</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="showAddAccountModal()" 
                    class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>Add Account
            </button>
            <button onclick="showBulkImportModal()" 
                    class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-upload mr-2"></i>Bulk Import
            </button>
            <button onclick="exportAccounts()" 
                    class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors">
                <i class="fas fa-download mr-2"></i>Export
            </button>
            <button onclick="loadAccounts()" 
                    class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Status Filter</label>
            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="expired">Expired</option>
                <option value="suspended">Suspended</option>
            </select>
        </div>
        
        <div>
            <label for="capacityFilter" class="block text-sm font-medium text-gray-700 mb-1">Capacity Filter</label>
            <select id="capacityFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                <option value="">All Capacities</option>
                <option value="available">Available</option>
                <option value="full">Full</option>
                <option value="low">Low Capacity</option>
            </select>
        </div>
        
        <div>
            <label for="expirationFilter" class="block text-sm font-medium text-gray-700 mb-1">Expiration Filter</label>
            <select id="expirationFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                <option value="">All Accounts</option>
                <option value="expiring_soon">Expiring Soon</option>
                <option value="expired">Expired</option>
                <option value="valid">Valid</option>
            </select>
        </div>
        
        <div>
            <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input type="text" id="searchInput" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                   placeholder="Search by email...">
        </div>
    </div>
    
    <div class="mt-4 flex justify-end">
        <button onclick="applyFilters()" 
                class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
            <i class="fas fa-filter mr-2"></i>Apply Filters
        </button>
    </div>
</div>

<!-- Accounts Table -->
<div class="bg-white rounded-lg shadow-lg p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-800">Accounts</h3>
        <div class="text-sm text-gray-600">
            Total: <span id="accountCount">0</span> accounts
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table id="accountsTable" class="w-full">
            <thead>
                <tr class="bg-gray-50">
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capacity</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiration</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody id="accountsTableBody">
                <tr>
                    <td colspan="5" class="text-center py-8 text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading accounts...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Add Account Modal -->
<div id="addAccountModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-800">Add ChatGPT Account</h3>
                <button onclick="hideAddAccountModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="addAccountForm" class="space-y-4">
                <div>
                    <label for="accountEmail" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="email" id="accountEmail" name="email" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           placeholder="<EMAIL>" required>
                </div>
                
                <div>
                    <label for="accountPassword" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <input type="password" id="accountPassword" name="password" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           placeholder="Account password" required>
                </div>
                
                <div>
                    <label for="expirationDate" class="block text-sm font-medium text-gray-700 mb-1">Expiration Date</label>
                    <input type="datetime-local" id="expirationDate" name="expiration_date" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                
                <div>
                    <label for="maxUsers" class="block text-sm font-medium text-gray-700 mb-1">Max Concurrent Users</label>
                    <select id="maxUsers" name="max_concurrent_users" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                            required>
                        <option value="1">1 User</option>
                        <option value="3">3 Users</option>
                        <option value="5" selected>5 Users</option>
                        <option value="10">10 Users</option>
                        <option value="20">20 Users</option>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideAddAccountModal()" 
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                        Add Account
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Account Modal -->
<div id="editAccountModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-800">Edit Account</h3>
                <button onclick="hideEditAccountModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="editAccountForm" class="space-y-4">
                <input type="hidden" id="editAccountId" name="account_id">
                
                <div>
                    <label for="editAccountEmail" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="email" id="editAccountEmail" name="email" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                
                <div>
                    <label for="editAccountPassword" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <input type="password" id="editAccountPassword" name="password" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                
                <div>
                    <label for="editExpirationDate" class="block text-sm font-medium text-gray-700 mb-1">Expiration Date</label>
                    <input type="datetime-local" id="editExpirationDate" name="expiration_date" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                
                <div>
                    <label for="editMaxUsers" class="block text-sm font-medium text-gray-700 mb-1">Max Concurrent Users</label>
                    <select id="editMaxUsers" name="max_concurrent_users" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                            required>
                        <option value="1">1 User</option>
                        <option value="3">3 Users</option>
                        <option value="5">5 Users</option>
                        <option value="10">10 Users</option>
                        <option value="20">20 Users</option>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideEditAccountModal()" 
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                        Update Account
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Global variables
let accountsData = [];
let accountsTable = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeAccountManagement();
    setupEventListeners();
});

function setupEventListeners() {
    document.getElementById('addAccountForm').addEventListener('submit', handleAddAccount);
    document.getElementById('editAccountForm').addEventListener('submit', handleEditAccount);
    
    // Filter change listeners
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
    document.getElementById('capacityFilter').addEventListener('change', applyFilters);
    document.getElementById('expirationFilter').addEventListener('change', applyFilters);
    document.getElementById('searchInput').addEventListener('input', applyFilters);
}

async function initializeAccountManagement() {
    await loadAccounts();
    initializeDataTable();
}

async function loadAccounts() {
    try {
        const response = await makeAuthenticatedRequest('/admin/openai-plus-redeem/api/accounts');
        
        if (response.ok) {
            const data = await response.json();
            accountsData = data.data || [];
            updateAccountsDisplay();
            updateAccountCount();
        } else {
            showAlert('Failed to load accounts', 'error');
        }
    } catch (error) {
        console.error('Load accounts error:', error);
        showAlert('Error loading accounts', 'error');
    }
}

function updateAccountsDisplay() {
    const tbody = document.getElementById('accountsTableBody');
    
    if (accountsData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-8 text-gray-500">
                    <i class="fas fa-inbox text-2xl mb-2"></i>
                    <p>No accounts found</p>
                </td>
            </tr>
        `;
        return;
    }
    
    const rows = accountsData.map(account => {
        const statusClass = `status-${account.status}`;
        const capacityPercent = Math.round((account.current_users / account.max_concurrent_users) * 100);
        const capacityClass = capacityPercent >= 80 ? 'capacity-high' : 
                             capacityPercent >= 60 ? 'capacity-medium' : 'capacity-low';
        
        const isExpired = new Date(account.expiration_date) < new Date();
        const daysUntilExpiration = Math.ceil((new Date(account.expiration_date) - new Date()) / (1000 * 60 * 60 * 24));
        
        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-3">
                    <div>
                        <div class="font-medium text-gray-800">${account.email}</div>
                        <div class="text-sm text-gray-500">ID: ${account.account_id.substring(0, 8)}...</div>
                    </div>
                </td>
                <td class="px-4 py-3">
                    <span class="status-badge ${statusClass}">${account.status}</span>
                </td>
                <td class="px-4 py-3">
                    <div class="text-sm text-gray-800 mb-1">${account.current_users}/${account.max_concurrent_users} users</div>
                    <div class="capacity-bar bg-gray-200">
                        <div class="capacity-fill ${capacityClass}" style="width: ${capacityPercent}%"></div>
                    </div>
                </td>
                <td class="px-4 py-3">
                    <div class="text-sm text-gray-800">${new Date(account.expiration_date).toLocaleDateString()}</div>
                    <div class="text-xs ${isExpired ? 'text-red-500' : daysUntilExpiration <= 7 ? 'text-yellow-500' : 'text-gray-500'}">
                        ${isExpired ? 'Expired' : `${daysUntilExpiration} days left`}
                    </div>
                </td>
                <td class="px-4 py-3">
                    <div class="flex space-x-2">
                        <button onclick="editAccount('${account.account_id}')" 
                                class="text-blue-500 hover:text-blue-700" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="viewAccountDetails('${account.account_id}')" 
                                class="text-green-500 hover:text-green-700" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="deleteAccount('${account.account_id}')" 
                                class="text-red-500 hover:text-red-700" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
    
    tbody.innerHTML = rows;
    
    // Reinitialize DataTable if it exists
    if (accountsTable) {
        accountsTable.destroy();
        initializeDataTable();
    }
}

function initializeDataTable() {
    accountsTable = $('#accountsTable').DataTable({
        pageLength: 25,
        order: [[3, 'asc']], // Sort by expiration date
        columnDefs: [
            { orderable: false, targets: [4] } // Disable sorting for actions column
        ]
    });
}

function updateAccountCount() {
    document.getElementById('accountCount').textContent = accountsData.length;
}

async function handleAddAccount(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await makeAuthenticatedRequest('/admin/openai-plus-redeem/api/accounts', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert('Account added successfully', 'success');
            hideAddAccountModal();
            await loadAccounts(); // Refresh accounts list
        } else {
            const error = await response.json();
            showAlert(error.error || 'Failed to add account', 'error');
        }
    } catch (error) {
        console.error('Add account error:', error);
        showAlert('Error adding account', 'error');
    }
}

async function handleEditAccount(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    const accountId = data.account_id;
    delete data.account_id;
    
    try {
        const response = await makeAuthenticatedRequest(`/admin/openai-plus-redeem/api/accounts/${accountId}`, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert('Account updated successfully', 'success');
            hideEditAccountModal();
            await loadAccounts(); // Refresh accounts list
        } else {
            const error = await response.json();
            showAlert(error.error || 'Failed to update account', 'error');
        }
    } catch (error) {
        console.error('Edit account error:', error);
        showAlert('Error updating account', 'error');
    }
}

function showAddAccountModal() {
    // Set default expiration date (30 days from now)
    const defaultExpiration = new Date();
    defaultExpiration.setDate(defaultExpiration.getDate() + 30);
    document.getElementById('expirationDate').value = defaultExpiration.toISOString().slice(0, 16);
    
    document.getElementById('addAccountModal').classList.remove('hidden');
}

function hideAddAccountModal() {
    document.getElementById('addAccountModal').classList.add('hidden');
    document.getElementById('addAccountForm').reset();
}

function editAccount(accountId) {
    const account = accountsData.find(a => a.account_id === accountId);
    if (!account) {
        showAlert('Account not found', 'error');
        return;
    }
    
    // Populate edit form
    document.getElementById('editAccountId').value = account.account_id;
    document.getElementById('editAccountEmail').value = account.email;
    document.getElementById('editAccountPassword').value = account.password;
    document.getElementById('editExpirationDate').value = new Date(account.expiration_date).toISOString().slice(0, 16);
    document.getElementById('editMaxUsers').value = account.max_concurrent_users;
    
    document.getElementById('editAccountModal').classList.remove('hidden');
}

function hideEditAccountModal() {
    document.getElementById('editAccountModal').classList.add('hidden');
    document.getElementById('editAccountForm').reset();
}

async function deleteAccount(accountId) {
    const account = accountsData.find(a => a.account_id === accountId);
    if (!account) {
        showAlert('Account not found', 'error');
        return;
    }
    
    if (!confirm(`Are you sure you want to delete account ${account.email}? This action cannot be undone.`)) {
        return;
    }
    
    try {
        const response = await makeAuthenticatedRequest(`/admin/openai-plus-redeem/api/accounts/${accountId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showAlert('Account deleted successfully', 'success');
            await loadAccounts(); // Refresh accounts list
        } else {
            const error = await response.json();
            showAlert(error.error || 'Failed to delete account', 'error');
        }
    } catch (error) {
        console.error('Delete account error:', error);
        showAlert('Error deleting account', 'error');
    }
}

function viewAccountDetails(accountId) {
    const account = accountsData.find(a => a.account_id === accountId);
    if (!account) {
        showAlert('Account not found', 'error');
        return;
    }
    
    // Show account details in a modal or navigate to details page
    alert(`Account Details:\n\nEmail: ${account.email}\nStatus: ${account.status}\nUsers: ${account.current_users}/${account.max_concurrent_users}\nExpires: ${new Date(account.expiration_date).toLocaleString()}`);
}

function applyFilters() {
    // This would filter the accounts based on selected criteria
    // For now, just reload the data
    loadAccounts();
}

function showBulkImportModal() {
    showAlert('Bulk import feature coming soon', 'info');
}

function exportAccounts() {
    // Export accounts to CSV
    const csv = convertAccountsToCSV(accountsData);
    downloadCSV(csv, 'chatgpt_accounts.csv');
}

function convertAccountsToCSV(accounts) {
    const headers = ['Email', 'Status', 'Current Users', 'Max Users', 'Expiration Date', 'Created At'];
    const rows = accounts.map(account => [
        account.email,
        account.status,
        account.current_users,
        account.max_concurrent_users,
        account.expiration_date,
        account.created_at
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Helper function for authenticated requests
function makeAuthenticatedRequest(url, options = {}) {
    const defaultHeaders = {
        'Content-Type': 'application/json',
        'X-Admin-Key': 'admin_key_placeholder' // This should be configured properly
    };
    
    return fetch(url, {
        ...options,
        headers: {
            ...defaultHeaders,
            ...options.headers
        }
    });
}

// Alert system
function showAlert(message, type) {
    const alertClass = {
        'success': 'bg-green-100 border-green-500 text-green-700',
        'warning': 'bg-yellow-100 border-yellow-500 text-yellow-700',
        'error': 'bg-red-100 border-red-500 text-red-700',
        'info': 'bg-blue-100 border-blue-500 text-blue-700'
    }[type] || 'bg-blue-100 border-blue-500 text-blue-700';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `fixed top-4 right-4 ${alertClass} border-l-4 p-4 rounded shadow-lg z-50`;
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg">&times;</button>
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
