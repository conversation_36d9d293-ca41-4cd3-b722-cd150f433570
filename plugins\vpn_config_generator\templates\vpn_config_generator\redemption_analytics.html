<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redemption Links Analytics - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        .crypto-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .crypto-button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .crypto-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
        .crypto-button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        .crypto-button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .crypto-input {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.2s ease;
        }
        .crypto-input:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Redemption Links Analytics</h1>
                    <p class="text-gray-600">Track performance and usage statistics</p>
                </div>
                <div class="flex space-x-2">
                    <a href="/vpn-config-generator/admin/redemption-links" class="crypto-button secondary">← Back to Links</a>
                    <button onclick="refreshAnalytics()" class="crypto-button secondary">Refresh Data</button>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="crypto-card p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Filters</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input type="date" id="startDate" class="crypto-input w-full">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                    <input type="date" id="endDate" class="crypto-input w-full">
                </div>
                <div class="flex items-end">
                    <button onclick="applyFilters()" class="crypto-button w-full">Apply Filters</button>
                </div>
                <div class="flex items-end">
                    <button onclick="clearFilters()" class="crypto-button secondary w-full">Clear Filters</button>
                </div>
            </div>
        </div>

        <!-- Statistics Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card">
                <div id="totalLinks" class="stat-number text-blue-600">-</div>
                <div class="stat-label">Total Links</div>
            </div>
            <div class="stat-card">
                <div id="usedLinks" class="stat-number text-green-600">-</div>
                <div class="stat-label">Used Links</div>
            </div>
            <div class="stat-card">
                <div id="activeLinks" class="stat-number text-yellow-600">-</div>
                <div class="stat-label">Active Links</div>
            </div>
            <div class="stat-card">
                <div id="usageRate" class="stat-number text-purple-600">-%</div>
                <div class="stat-label">Usage Rate</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Creation Trends Chart -->
            <div class="crypto-card p-6">
                <h3 class="text-lg font-semibold mb-4">Creation Trends</h3>
                <canvas id="creationTrendsChart" width="400" height="200"></canvas>
            </div>

            <!-- Usage Trends Chart -->
            <div class="crypto-card p-6">
                <h3 class="text-lg font-semibold mb-4">Usage Trends</h3>
                <canvas id="usageTrendsChart" width="400" height="200"></canvas>
            </div>

            <!-- Popular Telcos Chart -->
            <div class="crypto-card p-6">
                <h3 class="text-lg font-semibold mb-4">Popular Telcos</h3>
                <canvas id="telcosChart" width="400" height="200"></canvas>
            </div>

            <!-- Popular Plans Chart -->
            <div class="crypto-card p-6">
                <h3 class="text-lg font-semibold mb-4">Popular Plans</h3>
                <canvas id="plansChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="crypto-card p-6 mb-8">
            <h3 class="text-lg font-semibold mb-4">Additional Statistics</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="text-center">
                    <div id="avgTimeToUse" class="text-2xl font-bold text-blue-600 mb-2">-</div>
                    <div class="text-sm text-gray-600">Average Time to Use (hours)</div>
                </div>
                <div class="text-center">
                    <div id="expiredLinks" class="text-2xl font-bold text-red-600 mb-2">-</div>
                    <div class="text-sm text-gray-600">Expired Links</div>
                </div>
                <div class="text-center">
                    <div id="topCreator" class="text-2xl font-bold text-green-600 mb-2">-</div>
                    <div class="text-sm text-gray-600">Top Creator</div>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="crypto-card p-6">
            <h3 class="text-lg font-semibold mb-4">Export Data</h3>
            <div class="flex space-x-4">
                <button onclick="exportData('json')" class="crypto-button">Export JSON</button>
                <button onclick="exportData('csv')" class="crypto-button success">Export CSV</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading analytics data...</p>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

    <script>
        let currentAnalytics = null;
        let charts = {};

        // Load analytics on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadAnalytics();
        });

        // Load analytics data
        async function loadAnalytics(startDate = null, endDate = null) {
            showLoading(true);
            
            try {
                const params = new URLSearchParams();
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                
                const response = await axios.get(`/vpn-config-generator/api/redemption-links/analytics?${params}`);
                
                if (response.data.success) {
                    currentAnalytics = response.data.analytics;
                    updateStatistics(currentAnalytics);
                    updateCharts(currentAnalytics);
                } else {
                    showMessage('Error loading analytics: ' + response.data.error, 'error');
                }
            } catch (error) {
                showMessage('Error loading analytics: ' + (error.response?.data?.error || error.message), 'error');
            } finally {
                showLoading(false);
            }
        }

        // Update statistics display
        function updateStatistics(analytics) {
            document.getElementById('totalLinks').textContent = analytics.total_links;
            document.getElementById('usedLinks').textContent = analytics.used_links;
            document.getElementById('activeLinks').textContent = analytics.active_links;
            document.getElementById('usageRate').textContent = analytics.usage_rate + '%';
            document.getElementById('expiredLinks').textContent = analytics.expired_links;
            document.getElementById('avgTimeToUse').textContent = analytics.average_time_to_use || 'N/A';
            
            // Find top creator
            const creators = analytics.creator_stats;
            const topCreator = Object.keys(creators).reduce((a, b) => creators[a] > creators[b] ? a : b, 'N/A');
            document.getElementById('topCreator').textContent = topCreator;
        }

        // Update charts
        function updateCharts(analytics) {
            // Destroy existing charts
            Object.values(charts).forEach(chart => chart.destroy());
            charts = {};

            // Creation Trends Chart
            const creationCtx = document.getElementById('creationTrendsChart').getContext('2d');
            const creationDates = Object.keys(analytics.creation_trends).sort();
            const creationCounts = creationDates.map(date => analytics.creation_trends[date]);

            charts.creation = new Chart(creationCtx, {
                type: 'line',
                data: {
                    labels: creationDates,
                    datasets: [{
                        label: 'Links Created',
                        data: creationCounts,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Usage Trends Chart
            const usageCtx = document.getElementById('usageTrendsChart').getContext('2d');
            const usageDates = Object.keys(analytics.usage_trends).sort();
            const usageCounts = usageDates.map(date => analytics.usage_trends[date]);

            charts.usage = new Chart(usageCtx, {
                type: 'line',
                data: {
                    labels: usageDates,
                    datasets: [{
                        label: 'Links Used',
                        data: usageCounts,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Popular Telcos Chart
            const telcosCtx = document.getElementById('telcosChart').getContext('2d');
            const telcoLabels = Object.keys(analytics.popular_telcos);
            const telcoCounts = Object.values(analytics.popular_telcos);

            charts.telcos = new Chart(telcosCtx, {
                type: 'doughnut',
                data: {
                    labels: telcoLabels.map(t => t.charAt(0).toUpperCase() + t.slice(1)),
                    datasets: [{
                        data: telcoCounts,
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Popular Plans Chart
            const plansCtx = document.getElementById('plansChart').getContext('2d');
            const planLabels = Object.keys(analytics.popular_plans);
            const planCounts = Object.values(analytics.popular_plans);

            charts.plans = new Chart(plansCtx, {
                type: 'bar',
                data: {
                    labels: planLabels.map(p => p.charAt(0).toUpperCase() + p.slice(1)),
                    datasets: [{
                        label: 'Usage Count',
                        data: planCounts,
                        backgroundColor: '#8b5cf6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Apply filters
        function applyFilters() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            loadAnalytics(startDate || null, endDate || null);
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            loadAnalytics();
        }

        // Refresh analytics
        function refreshAnalytics() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            loadAnalytics(startDate || null, endDate || null);
        }

        // Export data
        async function exportData(format) {
            try {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                
                const params = new URLSearchParams();
                params.append('format', format);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                
                const response = await axios.get(`/vpn-config-generator/api/redemption-links/analytics/export?${params}`, {
                    responseType: format === 'csv' ? 'blob' : 'json'
                });
                
                if (format === 'csv') {
                    // Handle CSV download
                    const blob = new Blob([response.data], { type: 'text/csv' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `redemption_analytics_${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showMessage('CSV export downloaded successfully!', 'success');
                } else {
                    // Handle JSON download
                    const dataStr = JSON.stringify(response.data.data, null, 2);
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `redemption_analytics_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showMessage('JSON export downloaded successfully!', 'success');
                }
            } catch (error) {
                showMessage('Error exporting data: ' + (error.response?.data?.error || error.message), 'error');
            }
        }

        // Show/hide loading overlay
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        // Show success/error messages
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded-lg mb-2 ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            messageDiv.textContent = message;
            
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
