"""
Data Persistence Validation Tests

Tests JSON file operations, data integrity, concurrent access handling,
and backup/recovery procedures for all data models:
- ChatGPT accounts
- Order redemptions  
- Email verifications
- Account cooldowns
"""

import unittest
import json
import tempfile
import shutil
import threading
import time
import os
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

# Import models and utilities
from ..models.chatgpt_account import ChatGPTAccount, AccountStatus
from ..models.order_redemption import OrderRedemption, RedemptionStatus
from ..models.email_verification import EmailVerification, VerificationStatus
from ..models.account_cooldown import AccountCooldown, CooldownType
from ..models.utils import load_json_data, save_json_data, backup_data_file, get_data_file_path

# Import services
from ..services.chatgpt_account_service import ChatGPTAccountService
from ..services.order_redemption_service import OrderRedemptionService
from ..services.email_service import EmailService
from ..services.cooldown_service import CooldownService


class TestDataPersistence(unittest.TestCase):
    """Test data persistence operations"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_config = {
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            },
            'cooldown_config': {
                'default_cooldown_hours': 24
            }
        }
        self.logger = Mock()
        
        # Mock data file paths to use temp directory
        self.data_file_patcher = patch(
            'plugins.openai_plus_redeem.models.utils.get_data_file_path',
            side_effect=lambda plugin_name, filename: str(Path(self.temp_dir) / filename)
        )
        self.data_file_patcher.start()
    
    def tearDown(self):
        """Clean up test environment"""
        self.data_file_patcher.stop()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_json_file_operations(self):
        """Test basic JSON file operations"""
        test_file = Path(self.temp_dir) / 'test_data.json'
        
        # Test data
        test_data = {
            'accounts': [
                {
                    'account_id': 'TEST_001',
                    'email': '<EMAIL>',
                    'password': 'password123',
                    'status': 'active'
                }
            ],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }
        
        # Test save operation
        save_result = save_json_data(str(test_file), test_data)
        self.assertTrue(save_result)
        self.assertTrue(test_file.exists())
        
        # Test load operation
        loaded_data = load_json_data(str(test_file))
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data['accounts'][0]['account_id'], 'TEST_001')
        self.assertEqual(loaded_data['metadata']['version'], '1.0')
        
        # Test file integrity
        with open(test_file, 'r') as f:
            file_content = f.read()
            self.assertTrue(json.loads(file_content))  # Should be valid JSON
    
    def test_data_integrity_validation(self):
        """Test data integrity validation"""
        # Create account service
        account_service = ChatGPTAccountService(self.test_config, self.logger)
        account_service.initialize()
        
        # Create test account
        test_account = ChatGPTAccount(
            account_id='INTEGRITY_TEST',
            email='<EMAIL>',
            password='test_password',
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        
        # Add account
        result = account_service.add_account(test_account)
        self.assertTrue(result['success'])
        
        # Verify data was saved correctly
        loaded_account = account_service.get_account_by_id('INTEGRITY_TEST')
        self.assertIsNotNone(loaded_account)
        self.assertEqual(loaded_account.email, '<EMAIL>')
        self.assertEqual(loaded_account.max_concurrent_users, 5)
        self.assertEqual(loaded_account.status, AccountStatus.ACTIVE)
        
        # Test data consistency after multiple operations
        test_account.current_users = 3
        update_result = account_service.update_account(test_account)
        self.assertTrue(update_result['success'])
        
        # Reload and verify
        reloaded_account = account_service.get_account_by_id('INTEGRITY_TEST')
        self.assertEqual(reloaded_account.current_users, 3)
    
    def test_concurrent_access_handling(self):
        """Test concurrent access to data files"""
        # Create account service
        account_service = ChatGPTAccountService(self.test_config, self.logger)
        account_service.initialize()
        
        # Function to add accounts concurrently
        def add_account_worker(worker_id):
            account = ChatGPTAccount(
                account_id=f'CONCURRENT_{worker_id}',
                email=f'concurrent{worker_id}@example.com',
                password=f'password{worker_id}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            result = account_service.add_account(account)
            return result['success']
        
        # Run concurrent operations
        num_workers = 10
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [
                executor.submit(add_account_worker, i) 
                for i in range(num_workers)
            ]
            
            results = [future.result() for future in futures]
        
        # Verify all operations succeeded
        self.assertTrue(all(results), "All concurrent operations should succeed")
        
        # Verify all accounts were saved
        all_accounts = account_service.get_all_accounts()
        concurrent_accounts = [
            acc for acc in all_accounts 
            if acc.account_id.startswith('CONCURRENT_')
        ]
        self.assertEqual(len(concurrent_accounts), num_workers)
    
    def test_backup_and_recovery(self):
        """Test backup and recovery procedures"""
        # Create test data file
        test_file = Path(self.temp_dir) / 'backup_test.json'
        test_data = {
            'accounts': [
                {
                    'account_id': 'BACKUP_TEST',
                    'email': '<EMAIL>',
                    'password': 'backup_password'
                }
            ],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }
        
        # Save initial data
        save_json_data(str(test_file), test_data)
        
        # Create backup
        backup_result = backup_data_file(str(test_file))
        self.assertTrue(backup_result['success'])
        
        backup_file = Path(backup_result['backup_path'])
        self.assertTrue(backup_file.exists())
        
        # Verify backup content
        backup_data = load_json_data(str(backup_file))
        self.assertEqual(backup_data['accounts'][0]['account_id'], 'BACKUP_TEST')
        
        # Simulate data corruption
        with open(test_file, 'w') as f:
            f.write('corrupted data')
        
        # Test recovery from backup
        recovery_data = load_json_data(str(backup_file))
        save_json_data(str(test_file), recovery_data)
        
        # Verify recovery
        recovered_data = load_json_data(str(test_file))
        self.assertEqual(recovered_data['accounts'][0]['account_id'], 'BACKUP_TEST')
    
    def test_large_dataset_performance(self):
        """Test performance with large datasets"""
        # Create account service
        account_service = ChatGPTAccountService(self.test_config, self.logger)
        account_service.initialize()
        
        # Create large number of accounts
        num_accounts = 1000
        start_time = time.time()
        
        for i in range(num_accounts):
            account = ChatGPTAccount(
                account_id=f'PERF_TEST_{i:04d}',
                email=f'perf{i}@example.com',
                password=f'password{i}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            result = account_service.add_account(account)
            self.assertTrue(result['success'])
        
        creation_time = time.time() - start_time
        
        # Test query performance
        start_time = time.time()
        all_accounts = account_service.get_all_accounts()
        query_time = time.time() - start_time
        
        # Verify results
        self.assertEqual(len(all_accounts), num_accounts)
        
        # Performance assertions (adjust thresholds as needed)
        self.assertLess(creation_time, 30.0, "Account creation should complete within 30 seconds")
        self.assertLess(query_time, 2.0, "Account query should complete within 2 seconds")
        
        print(f"Performance metrics:")
        print(f"  - Created {num_accounts} accounts in {creation_time:.2f} seconds")
        print(f"  - Queried {num_accounts} accounts in {query_time:.2f} seconds")
    
    def test_data_migration_compatibility(self):
        """Test data migration and version compatibility"""
        # Create old format data
        old_format_data = {
            'accounts': [
                {
                    'id': 'OLD_FORMAT_001',  # Old field name
                    'email': '<EMAIL>',
                    'pwd': 'old_password',  # Old field name
                    'max_users': 5,  # Old field name
                    'active': True  # Old field name
                }
            ],
            'version': '0.9'  # Old version
        }
        
        old_file = Path(self.temp_dir) / 'old_format.json'
        save_json_data(str(old_file), old_format_data)
        
        # Test loading with migration (this would require migration logic in the service)
        # For now, just test that we can detect old format
        loaded_data = load_json_data(str(old_file))
        self.assertEqual(loaded_data['version'], '0.9')
        self.assertIn('id', loaded_data['accounts'][0])  # Old field exists
    
    def test_error_handling_in_persistence(self):
        """Test error handling in persistence operations"""
        # Test invalid file path
        invalid_path = '/invalid/path/that/does/not/exist/data.json'
        result = save_json_data(invalid_path, {'test': 'data'})
        self.assertFalse(result)
        
        # Test loading non-existent file
        non_existent_data = load_json_data('/non/existent/file.json')
        self.assertIsNone(non_existent_data)
        
        # Test loading corrupted JSON
        corrupted_file = Path(self.temp_dir) / 'corrupted.json'
        with open(corrupted_file, 'w') as f:
            f.write('{ invalid json content }')
        
        corrupted_data = load_json_data(str(corrupted_file))
        self.assertIsNone(corrupted_data)
        
        # Test permission errors (simulate)
        test_file = Path(self.temp_dir) / 'permission_test.json'
        save_json_data(str(test_file), {'test': 'data'})
        
        # Make file read-only
        os.chmod(test_file, 0o444)
        
        # Try to write (should fail gracefully)
        result = save_json_data(str(test_file), {'new': 'data'})
        # Note: This might succeed on some systems, so we just ensure it doesn't crash
        self.assertIsInstance(result, bool)
    
    def test_all_model_persistence(self):
        """Test persistence for all data models"""
        # Test ChatGPT Account persistence
        account_service = ChatGPTAccountService(self.test_config, self.logger)
        account_service.initialize()
        
        test_account = ChatGPTAccount(
            account_id='MODEL_TEST_ACCOUNT',
            email='<EMAIL>',
            password='model_password',
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        
        account_result = account_service.add_account(test_account)
        self.assertTrue(account_result['success'])
        
        # Test Order Redemption persistence
        redemption_service = OrderRedemptionService(self.test_config, self.logger)
        redemption_service.initialize()
        
        test_redemption = OrderRedemption(
            redemption_id='MODEL_TEST_REDEMPTION',
            order_id='ORDER_123',
            buyer_username='test_user',
            sku='chatgpt_plus',
            var_sku='chatgpt_5_30',
            account_id='MODEL_TEST_ACCOUNT',
            status=RedemptionStatus.COMPLETED,
            created_date=datetime.now(),
            completed_date=datetime.now()
        )
        
        redemption_result = redemption_service.add_redemption(test_redemption)
        self.assertTrue(redemption_result['success'])
        
        # Test Email Verification persistence
        email_service = EmailService(self.test_config, self.logger)
        email_service.initialize()
        
        email_service._log_verification_attempt(
            '<EMAIL>',
            VerificationStatus.SUCCESS,
            '123456',
            'openai'
        )
        
        verification_logs = email_service.get_verification_logs()
        self.assertGreater(len(verification_logs), 0)
        
        # Test Cooldown persistence
        cooldown_service = CooldownService(self.test_config, self.logger)
        cooldown_service.initialize()
        
        cooldown_result = cooldown_service.set_user_cooldown(
            'test_user',
            hours=24,
            cooldown_type=CooldownType.REDEMPTION_LIMIT
        )
        self.assertTrue(cooldown_result['success'])
        
        # Verify all data persisted correctly
        loaded_account = account_service.get_account_by_id('MODEL_TEST_ACCOUNT')
        self.assertIsNotNone(loaded_account)
        
        loaded_redemption = redemption_service.get_redemption_by_id('MODEL_TEST_REDEMPTION')
        self.assertIsNotNone(loaded_redemption)
        
        cooldown_info = cooldown_service.get_user_cooldown_info('test_user')
        self.assertIsNotNone(cooldown_info)


if __name__ == '__main__':
    unittest.main()
