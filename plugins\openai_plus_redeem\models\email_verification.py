"""
Email Verification Model

Data model for managing email verification processes including
verification code tracking and email search functionality.
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum


class VerificationStatus(Enum):
    """Enumeration of possible verification statuses"""
    PENDING = "pending"
    SEARCHING = "searching"
    FOUND = "found"
    FAILED = "failed"
    EXPIRED = "expired"
    COMPLETED = "completed"


@dataclass
class EmailVerification:
    """
    Data model for email verification processes
    
    Attributes:
        verification_id: Unique verification ID
        redemption_id: Associated redemption ID
        account_email: ChatGPT account email to verify
        verification_code: Retrieved verification code
        verification_status: Current verification status
        search_attempts: Number of search attempts
        max_search_attempts: Maximum allowed search attempts
        search_timeout_minutes: Timeout for each search attempt
        created_at: Verification request timestamp
        completed_at: Verification completion timestamp
        expires_at: Verification expiration timestamp
        error_message: Error message if verification failed
        email_subject: Subject of verification email found
        email_sender: Sender of verification email
        email_received_at: Timestamp when verification email was received
    """
    
    verification_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    redemption_id: str = ""
    account_email: str = ""
    verification_code: str = ""
    verification_status: str = field(default=VerificationStatus.PENDING.value)
    search_attempts: int = 0
    max_search_attempts: int = 5
    search_timeout_minutes: int = 10
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    completed_at: str = ""
    expires_at: str = field(default_factory=lambda: (datetime.now() + timedelta(hours=1)).isoformat())
    error_message: str = ""
    email_subject: str = ""
    email_sender: str = ""
    email_received_at: str = ""
    
    @property
    def status(self) -> VerificationStatus:
        """Get verification status as enum"""
        try:
            return VerificationStatus(self.verification_status)
        except ValueError:
            return VerificationStatus.FAILED
    
    @status.setter
    def status(self, value: VerificationStatus):
        """Set verification status from enum"""
        self.verification_status = value.value
        if value == VerificationStatus.COMPLETED:
            self.completed_at = datetime.now().isoformat()
    
    def is_expired(self) -> bool:
        """Check if the verification has expired"""
        if not self.expires_at:
            return False
        
        try:
            expiration = datetime.fromisoformat(self.expires_at.replace('Z', '+00:00'))
            return datetime.now() > expiration.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return False
    
    def can_search(self) -> bool:
        """Check if another search attempt can be made"""
        return (
            self.search_attempts < self.max_search_attempts and
            not self.is_expired() and
            self.status in [VerificationStatus.PENDING, VerificationStatus.SEARCHING, VerificationStatus.FAILED]
        )
    
    def start_search(self) -> bool:
        """
        Start a new verification search attempt
        
        Returns:
            True if search can be started, False otherwise
        """
        if not self.can_search():
            return False
        
        self.search_attempts += 1
        self.status = VerificationStatus.SEARCHING
        self.error_message = ""
        return True
    
    def complete_search(self, verification_code: str, email_subject: str = "", 
                       email_sender: str = "", email_received_at: str = "") -> None:
        """
        Complete the verification search with found code
        
        Args:
            verification_code: The verification code found
            email_subject: Subject of the verification email
            email_sender: Sender of the verification email
            email_received_at: When the email was received
        """
        self.verification_code = verification_code
        self.email_subject = email_subject
        self.email_sender = email_sender
        self.email_received_at = email_received_at or datetime.now().isoformat()
        self.status = VerificationStatus.FOUND
    
    def fail_search(self, error_message: str) -> None:
        """
        Mark the search as failed
        
        Args:
            error_message: Reason for failure
        """
        self.error_message = error_message
        self.status = VerificationStatus.FAILED
    
    def complete_verification(self) -> None:
        """Mark the verification as completed"""
        self.status = VerificationStatus.COMPLETED
    
    def expire(self) -> None:
        """Mark the verification as expired"""
        self.status = VerificationStatus.EXPIRED
    
    def extend_expiration(self, additional_minutes: int = 30) -> None:
        """
        Extend the verification expiration time
        
        Args:
            additional_minutes: Minutes to extend the expiration
        """
        try:
            if self.expires_at:
                current_expiration = datetime.fromisoformat(self.expires_at.replace('Z', '+00:00'))
            else:
                current_expiration = datetime.now()
            
            new_expiration = current_expiration + timedelta(minutes=additional_minutes)
            self.expires_at = new_expiration.isoformat()
            
        except (ValueError, AttributeError):
            # If current expiration is invalid, set new expiration from now
            new_expiration = datetime.now() + timedelta(minutes=additional_minutes)
            self.expires_at = new_expiration.isoformat()
    
    def get_remaining_attempts(self) -> int:
        """Get remaining search attempts"""
        return max(0, self.max_search_attempts - self.search_attempts)
    
    def get_time_until_expiration(self) -> timedelta:
        """
        Get time remaining until expiration
        
        Returns:
            Timedelta object representing remaining time
        """
        if not self.expires_at:
            return timedelta(0)
        
        try:
            expiration = datetime.fromisoformat(self.expires_at.replace('Z', '+00:00'))
            remaining = expiration.replace(tzinfo=None) - datetime.now()
            return max(timedelta(0), remaining)
        except (ValueError, AttributeError):
            return timedelta(0)
    
    def get_search_duration(self) -> Optional[timedelta]:
        """
        Get duration of the verification search process
        
        Returns:
            Timedelta if completed, None if still in progress
        """
        if not self.completed_at:
            return None
        
        try:
            created = datetime.fromisoformat(self.created_at.replace('Z', '+00:00'))
            completed = datetime.fromisoformat(self.completed_at.replace('Z', '+00:00'))
            return completed.replace(tzinfo=None) - created.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert verification to dictionary for JSON serialization"""
        return {
            'verification_id': self.verification_id,
            'redemption_id': self.redemption_id,
            'account_email': self.account_email,
            'verification_code': self.verification_code,
            'verification_status': self.verification_status,
            'search_attempts': self.search_attempts,
            'max_search_attempts': self.max_search_attempts,
            'search_timeout_minutes': self.search_timeout_minutes,
            'created_at': self.created_at,
            'completed_at': self.completed_at,
            'expires_at': self.expires_at,
            'error_message': self.error_message,
            'email_subject': self.email_subject,
            'email_sender': self.email_sender,
            'email_received_at': self.email_received_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EmailVerification':
        """Create verification from dictionary"""
        return cls(
            verification_id=data.get('verification_id', str(uuid.uuid4())),
            redemption_id=data.get('redemption_id', ''),
            account_email=data.get('account_email', ''),
            verification_code=data.get('verification_code', ''),
            verification_status=data.get('verification_status', VerificationStatus.PENDING.value),
            search_attempts=data.get('search_attempts', 0),
            max_search_attempts=data.get('max_search_attempts', 5),
            search_timeout_minutes=data.get('search_timeout_minutes', 10),
            created_at=data.get('created_at', datetime.now().isoformat()),
            completed_at=data.get('completed_at', ''),
            expires_at=data.get('expires_at', (datetime.now() + timedelta(hours=1)).isoformat()),
            error_message=data.get('error_message', ''),
            email_subject=data.get('email_subject', ''),
            email_sender=data.get('email_sender', ''),
            email_received_at=data.get('email_received_at', '')
        )
    
    @classmethod
    def create_for_redemption(cls, redemption_id: str, account_email: str, 
                            timeout_minutes: int = 10, max_attempts: int = 5) -> 'EmailVerification':
        """
        Create a new email verification for a redemption
        
        Args:
            redemption_id: Associated redemption ID
            account_email: ChatGPT account email to verify
            timeout_minutes: Search timeout in minutes
            max_attempts: Maximum search attempts
            
        Returns:
            New EmailVerification instance
        """
        return cls(
            redemption_id=redemption_id,
            account_email=account_email,
            search_timeout_minutes=timeout_minutes,
            max_search_attempts=max_attempts
        )
    
    def __str__(self) -> str:
        """String representation of the verification"""
        return f"Email Verification {self.account_email} - {self.verification_status} - {self.search_attempts}/{self.max_search_attempts} attempts"
