<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SteamCodeTool - Legitimate Steam Code Management</title>

    <!-- Business Legitimacy Meta Tags -->
    <meta name="description" content="SteamCodeTool - Legitimate Steam authentication code management service for authorized game retailers and verified customers.">
    <meta name="keywords" content="Steam, authentication, game distribution, legitimate business, secure">
    <meta name="author" content="SteamCodeTool">
    <meta name="business-type" content="Digital Game Distribution">
    <meta name="security-verified" content="true">
    <meta name="phishing-site" content="false">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="SteamCodeTool - Legitimate Steam Code Management">
    <meta property="og:description" content="Secure and legitimate Steam authentication code management service">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="SteamCodeTool">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='spinkit.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='shepherd.css') }}">
    <script src="{{ url_for('static', filename='shepherd.js') }}"></script>
    <style>
        .modal-enter {
            opacity: 0;
            transform: scale(0.9);
        }
        .modal-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: opacity 300ms, transform 300ms;
        }
        .modal-exit {
            opacity: 1;
        }
        .modal-exit-active {
            opacity: 0;
            transform: scale(0.9);
            transition: opacity 300ms, transform 300ms;
        }
        .floating-label-input {
            position: relative;
        }
        .floating-label-input input {
            height: 3rem;
            padding-top: 1rem;
        }
        .floating-label-input label {
            position: absolute;
            top: 0.5rem;
            left: 0.75rem;
            transition: all 0.2s ease-out;
            pointer-events: none;
        }
        .floating-label-input input:focus + label,
        .floating-label-input input:not(:placeholder-shown) + label {
            font-size: 0.75rem;
            top: 0;
        }
        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            transform: rotate(180deg);
        }

        .wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 150px;
        }

        .wave .shape-fill {
            fill: #FFFFFF;
        }

        /* Tab Styles */
        .tab-button {
            border-color: transparent;
            color: #6B7280;
            transition: all 0.2s ease;
        }

        .tab-button:hover {
            color: #374151;
            border-color: #D1D5DB;
        }

        .tab-button.active {
            color: #7C3AED;
            border-color: #7C3AED;
        }

        .tab-content {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Service Card Hover Effects */
        .service-card {
            transition: all 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 min-h-screen flex items-center justify-center relative">
    <div class="wave">
        <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
        </svg>
    </div>
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-6xl">
        <!-- Legitimate Business Header -->
        <div class="text-center mb-6">
            <div class="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-2">
                🔒 Verified Legitimate Business
            </div>
            <h1 class="text-3xl font-bold text-purple-800 mb-2">SteamCodeTool</h1>
            <p class="text-gray-600 text-sm">Comprehensive Digital Service Management Platform</p>
        </div>

        <!-- Navigation Tabs -->
        <div class="mb-8">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex justify-center space-x-8 overflow-x-auto">
                    <button onclick="showTab('steam')" class="tab-button active whitespace-nowrap py-3 px-6 border-b-2 font-medium text-base" data-tab="steam">
                        🎮 Steam Authentication
                    </button>
                    <button onclick="showTab('order')" class="tab-button whitespace-nowrap py-3 px-6 border-b-2 font-medium text-base" data-tab="order">
                        📦 Order Redeem
                    </button>
                    <button onclick="showTab('vpn')" class="tab-button whitespace-nowrap py-3 px-6 border-b-2 font-medium text-base" data-tab="vpn">
                        🔐 VPN Configuration
                    </button>
                </nav>
            </div>
        </div>

        <!-- Steam Authentication Tab -->
        <div id="steam-tab" class="tab-content">
            <div class="max-w-md mx-auto">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 text-center">Steam Authentication Code</h2>
                <p class="text-gray-600 text-sm mb-6 text-center">Get your Steam authentication code securely</p>
                <form id="authForm" class="space-y-4">
                    <div class="floating-label-input">
                        <input type="text" id="orderId" name="orderId" required class="px-3 mt-1 block w-full rounded-md border-purple-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 placeholder-transparent">
                        <label for="orderId" class="text-sm font-medium text-purple-700">Order ID</label>
                    </div>
                    <div class="floating-label-input">
                        <input type="text" id="username" name="username" required class="px-3 mt-1 block w-full rounded-md border-purple-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 placeholder-transparent">
                        <label for="username" class="text-sm font-medium text-purple-700">Steam Username</label>
                    </div>
                    <button type="submit" class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 disabled:bg-purple-400 disabled:text-gray-200 disabled:cursor-not-allowed">
                        Get Auth Code
                    </button>
                    <button id="statusButton" type="button" class="mt-4 w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 disabled:bg-green-600 disabled:text-gray-200 disabled:cursor-not-allowed" disabled>
                        Check Get Auth Code Status
                    </button>
                </form>
                <button id="helpButton" class="mt-4 w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50">
                    Help
                </button>
            </div>
        </div>

        <!-- Order Redeem Tab -->
        <div id="order-tab" class="tab-content hidden">
            <div class="max-w-md mx-auto">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 text-center">Order Redemption</h2>
                <p class="text-gray-600 text-sm mb-6 text-center">Redeem your purchased products using your order ID</p>

                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200 text-center">
                    <div class="mb-4">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-500 text-white rounded-full mb-3">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-blue-800 mb-2">📦 Redeem Your Order</h3>
                        <p class="text-blue-600 text-sm mb-4">Enter your order ID to access your purchased products and services</p>
                    </div>

                    <a href="/order" class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition duration-200 font-medium">
                        Start Order Redemption
                    </a>

                    <div class="mt-4 text-xs text-blue-600">
                        <p>💡 You can find your order ID in your purchase confirmation email</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- VPN Configuration Tab -->
        <div id="vpn-tab" class="tab-content hidden">
            <div class="max-w-md mx-auto">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 text-center">VPN Configuration</h2>
                <p class="text-gray-600 text-sm mb-6 text-center">Generate your VPN configuration using your order ID</p>

                <div class="bg-gradient-to-br from-cyan-50 to-cyan-100 p-6 rounded-lg border border-cyan-200 text-center">
                    <div class="mb-4">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-cyan-500 text-white rounded-full mb-3">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-cyan-800 mb-2">🔐 Get Your VPN Config</h3>
                        <p class="text-cyan-600 text-sm mb-4">Secure VPN configuration generation for your purchased VPN service</p>
                    </div>

                    <a href="/vpn-config-generator/order-config" class="inline-block bg-cyan-500 hover:bg-cyan-600 text-white px-6 py-3 rounded-lg transition duration-200 font-medium">
                        Generate VPN Configuration
                    </a>

                    <div class="mt-4 text-xs text-cyan-600">
                        <p>🔒 Secure • 🌍 Global Servers • ⚡ High Speed</p>
                    </div>
                </div>
            </div>
        </div>




    </div>

    <!-- Quick Navigation Floating Button -->
    <div class="fixed bottom-20 right-6 z-30">
        <div class="relative group">
            <button onclick="toggleQuickNav()" class="bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg transition duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>

            <!-- Quick Navigation Menu -->
            <div id="quickNavMenu" class="absolute bottom-16 right-0 bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-64 hidden">
                <h4 class="font-semibold text-gray-800 mb-3">Quick Navigation</h4>
                <div class="space-y-2">
                    <button onclick="showTab('steam')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-purple-50 rounded">🎮 Steam Authentication</button>
                    <button onclick="showTab('order')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-purple-50 rounded">📦 Order Redeem</button>
                    <button onclick="showTab('vpn')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-purple-50 rounded">🔐 VPN Configuration</button>
                    <hr class="my-2">
                    <a href="/vpn-config-generator/order-config" class="block px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded">🔐 Get VPN Config</a>
                    <a href="/order" class="block px-3 py-2 text-sm text-green-600 hover:bg-green-50 rounded">📦 Redeem Order</a>
                </div>
            </div>
        </div>
    </div>

    <footer class="fixed bottom-0 w-full text-center py-2 bg-white bg-opacity-80 text-purple-800 z-20">
        <div class="flex justify-center items-center space-x-4 text-sm">
            <span>&copy; 2024 SteamCodeTool - Legitimate Business</span>
            <a href="/about" class="text-blue-600 hover:underline">About</a>
            <a href="/privacy" class="text-blue-600 hover:underline">Privacy</a>
            <a href="/security" class="text-blue-600 hover:underline">Security</a>
            <span class="text-green-600">🔒 Verified Safe</span>
        </div>
    </footer>

    <!-- Modal -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" onclick="handleModalClick(event)">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div id="modalContent" class="mt-3 text-center">
                <div id="loadingIndicator" class="mx-auto flex items-center justify-center h-12 w-12">
                    <div class="sk-wave sk-primary">
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                    </div>
                </div>
                <div id="successIndicator" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 hidden">
                    <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div id="errorIndicator" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 hidden">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2" id="modalTitle">Loading</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500" id="modalMessage">
                        Please Login Steam Now, System Waiting The Code.
                    </p>
                    <div id="credentialsContainer" class="mt-4 text-left hidden">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-small">Username:</span>
                            <div class="flex items-center">
                                <span id="steamUsername" class="mr-2"></span>
                                <button onclick="copyToClipboard('steamUsername')" class="text-purple-600 hover:text-purple-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="font-small">Password:</span>
                            <div class="flex items-center">
                                <span id="steamPassword" class="mr-2"></span>
                                <button onclick="copyToClipboard('steamPassword')" class="text-purple-600 hover:text-purple-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab Management
        function showTab(tabName) {
            // Only allow valid tabs for customers
            const validTabs = ['steam', 'order', 'vpn'];
            if (!validTabs.includes(tabName)) {
                tabName = 'steam'; // Default to steam tab
            }

            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.remove('hidden');
            }

            // Add active class to selected tab button
            const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            // Store active tab in localStorage
            localStorage.setItem('activeTab', tabName);
        }

        // Quick Navigation Toggle
        function toggleQuickNav() {
            const menu = document.getElementById('quickNavMenu');
            menu.classList.toggle('hidden');
        }

        // Close quick nav when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('quickNavMenu');
            const button = event.target.closest('button[onclick="toggleQuickNav()"]');

            if (!menu.contains(event.target) && !button) {
                menu.classList.add('hidden');
            }
        });

        // Initialize tabs on page load
        window.addEventListener('load', function() {
            // Get saved tab or default to 'steam'
            const savedTab = localStorage.getItem('activeTab') || 'steam';
            showTab(savedTab);

            // Handle URL parameters for Steam tab
            var orderId = getUrlParameter('orderId');
            var username = getUrlParameter('username');

            if (orderId) {
                document.getElementById('orderId').value = orderId;
            }
            if (username) {
                document.getElementById('username').value = username;
            }

            // If both parameters exist, switch to steam tab
            if (orderId && username) {
                showTab('steam');
            }

            // Handle VPN order parameter
            var vpnOrderSn = getUrlParameter('vpn_order_sn');
            if (vpnOrderSn) {
                showTab('vpn');
                // Open VPN config page with order
                setTimeout(() => {
                    window.open(`/vpn-config-generator/order-config?order_sn=${vpnOrderSn}`, '_blank');
                }, 500);
            }
        });

        // Steam Authentication Code functionality
        const form = document.getElementById('authForm');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const successIndicator = document.getElementById('successIndicator');
        const errorIndicator = document.getElementById('errorIndicator');
        const submitButton = form.querySelector('button[type="submit"]');
        const checkStatusButton = document.getElementById('statusButton');
        // Add this at the beginning of your script
        const ongoingRequests = new Set();
        let isRequestOngoing = false;
        
        document.getElementById('statusButton').addEventListener('click', () => {
            if (ongoingRequests.size > 0) {
                showModal();
                loadingIndicator.classList.remove('hidden');
                successIndicator.classList.add('hidden');
                errorIndicator.classList.add('hidden');
                modalTitle.textContent = 'Loading';
                modalMessage.textContent = 'Request is still in progress...';
            } else {
                alert('Currently, there are no ongoing requests.');
            }
        });

        // Helper function to generate a unique key for each request
        function getRequestKey(username, orderId) {
            return `${username}:${orderId}`;
        }


        function showModal() {
            modal.classList.remove('hidden');
            modal.classList.add('modal-enter');
            setTimeout(() => {
                modal.classList.remove('modal-enter');
                modal.classList.add('modal-enter-active');
            }, 10);
        }

        // 在 hideModal() 中重置
        function hideModal() {
            modal.classList.add('modal-exit');
            setTimeout(() => {
                modal.classList.add('modal-exit-active');
                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.classList.remove('modal-exit', 'modal-exit-active');
                }, 300);
            }, 10);
        }
        
        function handleModalClick(event) {
            // 检查点击是否发生在 modal 内容之外
            if (event.target === modal) {
                hideModal();
            }
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const orderId = document.getElementById('orderId').value.trim();
            const username = document.getElementById('username').value.trim();

            // 生成请求的唯一键
            const requestKey = getRequestKey(username, orderId);

            // 添加到进行中请求的 Set
            ongoingRequests.add(requestKey);
            submitButton.disabled = true;
            isRequestOngoing = true; 
            checkStatusButton.disabled = false;

            showModal();
            loadingIndicator.classList.remove('hidden');
            successIndicator.classList.add('hidden');
            errorIndicator.classList.add('hidden');
            modalTitle.textContent = 'Loading';
            modalMessage.textContent = 'Verifying order and fetching credentials...';

            try {
                // 首先获取 Steam 凭据
                const credentialsResponse = await axios.post('/api/get_steam_credentials', {
                    order_id: orderId,
                    username: username
                });

                const steamUsername = credentialsResponse.data.username;
                const steamPassword = credentialsResponse.data.password;

                document.getElementById('steamUsername').textContent = steamUsername;
                document.getElementById('steamPassword').textContent = steamPassword;
                document.getElementById('credentialsContainer').classList.remove('hidden');

                modalMessage.innerHTML = 'Please login now. System is getting the code.';

                // 然后获取 Steam 验证码
                const authCodeResponse = await axios.post('/api/get_steam_auth_code', {
                    order_id: orderId,
                    username: username
                });

                loadingIndicator.classList.add('hidden');
                successIndicator.classList.remove('hidden');
                modalTitle.textContent = 'Success';
                modalMessage.innerHTML += `<br><br>Your auth code is: ${authCodeResponse.data.auth_code}`;
            } catch (error) {
                loadingIndicator.classList.add('hidden');
                errorIndicator.classList.remove('hidden');
                modalTitle.textContent = 'Error';
                modalMessage.innerHTML = error.response?.data?.error || 'An error occurred';
            } finally {
                // 从进行中请求的 Set 中移除
                ongoingRequests.delete(requestKey);
                isRequestOngoing = false;
                submitButton.disabled = false;
                checkStatusButton.disabled = true;
            }
        });

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                // 可以添加一个临时的提示，表示复制成功
                element.classList.add('text-green-600');
                setTimeout(() => {
                    element.classList.remove('text-green-600');
                }, 1000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }



        const tour = new Shepherd.Tour({
        defaultStepOptions: {
            useModalOverlay: true,
            cancelIcon: {
                enabled: true
            },
            classes: 'shadow-md bg-purple-50',
            scrollTo: { behavior: 'smooth', block: 'center' },
            highlightClass: 'highlight-element',
            modalOverlayOpeningPadding: 10,
            modalOverlayOpeningRadius: 4
        }
    });

    tour.addStep({
        id: 'order-id',
        text: `
            <div class="mb-4">
                <div class="bg-gradient-to-r from-orange-400 to-red-500 text-white p-4 rounded-lg shadow-md">
                    <h4 class="font-bold text-lg mb-2">📦 Shopee Order ID</h4>
                    <p class="text-sm">Find your order ID in: My Account → My Purchase → Order Details</p>
                    <div class="mt-2 text-xs bg-white bg-opacity-20 p-2 rounded">
                        Format: Usually starts with numbers (e.g., 2506120...)
                    </div>
                </div>
            </div>
            <p>Enter your Shopee order ID here. You can find this in your Shopee order details.</p>
        `,
        attachTo: {
            element: '#orderId',
            on: 'bottom'
        },
        buttons: [
            {
                text: 'Next',
                action: tour.next
            }
        ]
    });

    tour.addStep({
        id: 'steam-username',
        text: `
            <div class="mb-4">
                
            </div>
            <p>Enter the Steam username you want to log in with. This is the username you use to log into Steam.</p>
        `,
        attachTo: {
            element: '#username',
            on: 'bottom'
        },
        buttons: [
            {
                text: 'Done',
                action: tour.complete
            }
        ]
    });

    document.getElementById('helpButton').addEventListener('click', () => {
        tour.start();
    });
    </script>
</body>
</html>