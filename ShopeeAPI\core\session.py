"""
Session management for Shopee API.
"""
import requests
import time
import json
import re
from typing import Dict, Any, Optional, Union

# Try different import approaches to handle both package and direct imports
try:
    from core.auth import CredentialManager
    from core.exceptions import RequestError, AuthenticationError
    from utils.helpers import rate_limit
except ImportError:
    try:
        from core.auth import Credential<PERSON>anager
        from core.exceptions import RequestError, AuthenticationError
        from utils.helpers import rate_limit
    except ImportError:
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        sys.path.insert(0, parent_dir)
        from core.auth import CredentialManager
        from core.exceptions import RequestError, AuthenticationError
        from utils.helpers import rate_limit


class ShopeeSession:
    """
    Manages HTTP session and requests for Shopee API.
    """

    def __init__(self, credential_manager: CredentialManager, timeout: int = 60, requests_per_minute: int = 30):
        """
        Initialize a new session with provided credentials.

        Args:
            credential_manager: CredentialManager instance that provides authorization credentials
            timeout: Request timeout in seconds
            requests_per_minute: Maximum requests per minute for rate limiting
        """
        self.credential_manager = credential_manager
        self.session = requests.Session()
        self.timeout = timeout
        self.requests_per_minute = requests_per_minute
        self.request_interval = 60.0 / requests_per_minute if requests_per_minute > 0 else 0
        self.last_request_time = 0
        self._update_session_headers()

    def _update_session_headers(self):
        """Update session headers with current credentials."""
        # Clear existing headers to avoid any conflicts
        self.session.headers.clear()

        # Set the essential headers (matching browser exactly)
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "Authorization": self.credential_manager.authorization_code,
            "Content-Type": "application/json",
            "Cookie": self.credential_manager.cookie,
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Origin": "https://seller.shopee.com.my",
            "Referer": "https://seller.shopee.com.my/portal/sale/order/all",
            # Add browser security headers that Shopee might be checking
            "Sec-Ch-Ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        })



    def _rate_limit(self):
        """Apply rate limiting between requests."""
        self.last_request_time = rate_limit(self.last_request_time, self.request_interval)

    def refresh_credentials(self):
        """Refresh session headers with latest credentials."""
        print("Refreshing session credentials...")
        self._update_session_headers()

        # Validate credentials after refresh
        is_valid = self.credential_manager.validate_credentials()
        if is_valid:
            print("Credentials validated successfully after refresh")
        else:
            print("Warning: Credentials may be invalid or incomplete after refresh")

    def _check_auth_error(self, response: requests.Response, url: str) -> None:
        """
        Check if the response indicates an authentication error.

        Args:
            response: Response object to check
            url: URL that was requested

        Raises:
            AuthenticationError: If the response indicates an authentication error
        """
        # Check for common authentication error status codes
        if response.status_code in (401, 403):
            error_msg = f"Authentication failed for {url} with status code {response.status_code}"
            raise AuthenticationError(error_msg, status_code=response.status_code)

        # Try to parse the response as JSON
        try:
            data = response.json()

            # Check for common error patterns in Shopee API responses
            if isinstance(data, dict):
                # Check for error messages that indicate authentication issues
                error_msg = data.get('error', '')
                if isinstance(error_msg, str) and error_msg:
                    error_lower = error_msg.lower()
                    if any(term in error_lower for term in ['unauthorized', 'forbidden', 'authentication', 'login', 'expired']):
                        raise AuthenticationError(f"Authentication error in response: {error_msg}",
                                                status_code=response.status_code,
                                                response=data)

                # Check for empty response with error code
                if 'data' in data and not data['data'] and 'error' in data:
                    error_code = data.get('error_code', '')
                    if error_code in ('unauthorized', 'forbidden', 'auth_error', 'auth_required'):
                        raise AuthenticationError(f"Authentication error: {data.get('error', '')}",
                                                status_code=response.status_code,
                                                response=data)
        except (json.JSONDecodeError, ValueError):
            # Not JSON or couldn't parse, check the text content
            if response.text:
                text_lower = response.text.lower()
                if any(term in text_lower for term in ['unauthorized', 'forbidden', 'authentication', 'login required', 'expired']):
                    raise AuthenticationError(f"Authentication error in response text from {url}",
                                            status_code=response.status_code)

    def get(self, url: str, params: Optional[Dict[str, Any]] = None) -> requests.Response:
        """
        Perform a GET request with the session.

        Args:
            url: The URL to request
            params: Query parameters for the request

        Returns:
            Response object

        Raises:
            AuthenticationError: If authentication fails
            RequestError: If the request fails for other reasons
        """
        self._rate_limit()
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            self._check_auth_error(response, url)
            return response
        except AuthenticationError:
            # Re-raise authentication errors
            raise
        except requests.RequestException as e:
            # Check if the exception message indicates an authentication issue
            error_msg = str(e).lower()
            if any(term in error_msg for term in ['unauthorized', 'forbidden', 'authentication']):
                raise AuthenticationError(f"Authentication failed for GET request to {url}: {str(e)}")
            raise RequestError(f"GET request to {url} failed: {str(e)}", response={"error": str(e)})

    def post(self, url: str, params: Optional[Dict[str, Any]] = None,
             json: Optional[Dict[str, Any]] = None, data: Optional[Dict[str, Any]] = None, 
             files: Optional[Dict[str, Any]] = None) -> requests.Response:
        """
        Perform a POST request with the session.

        Args:
            url: The URL to request
            params: Query parameters for the request
            json: JSON body for the request (for application/json content type)
            data: Form data for the request (for application/x-www-form-urlencoded content type)
            files: Files to upload (for multipart/form-data content type)

        Returns:
            Response object

        Raises:
            AuthenticationError: If authentication fails
            RequestError: If the request fails for other reasons
        """
        self._rate_limit()
        try:
            # If files are being uploaded, temporarily remove Content-Type header
            # to let requests set the correct multipart/form-data with boundary
            if files:
                saved_content_type = self.session.headers.pop('Content-Type', None)
                try:
                    response = self.session.post(url, params=params, json=json, data=data, files=files, timeout=self.timeout)
                finally:
                    # Restore the Content-Type header after the request
                    if saved_content_type:
                        self.session.headers['Content-Type'] = saved_content_type
            else:
                response = self.session.post(url, params=params, json=json, data=data, files=files, timeout=self.timeout)
            
            self._check_auth_error(response, url)
            return response
        except AuthenticationError:
            # Re-raise authentication errors
            raise
        except requests.RequestException as e:
            # Check if the exception message indicates an authentication issue
            error_msg = str(e).lower()
            if any(term in error_msg for term in ['unauthorized', 'forbidden', 'authentication']):
                raise AuthenticationError(f"Authentication failed for POST request to {url}: {str(e)}")
            raise RequestError(f"POST request to {url} failed: {str(e)}", response={"error": str(e)})

    def put(self, url: str, params: Optional[Dict[str, Any]] = None,
            json: Optional[Dict[str, Any]] = None) -> requests.Response:
        """
        Perform a PUT request with the session.

        Args:
            url: The URL to request
            params: Query parameters for the request
            json: JSON body for the request

        Returns:
            Response object

        Raises:
            AuthenticationError: If authentication fails
            RequestError: If the request fails for other reasons
        """
        self._rate_limit()
        try:
            response = self.session.put(url, params=params, json=json, timeout=self.timeout)
            self._check_auth_error(response, url)
            return response
        except AuthenticationError:
            # Re-raise authentication errors
            raise
        except requests.RequestException as e:
            # Check if the exception message indicates an authentication issue
            error_msg = str(e).lower()
            if any(term in error_msg for term in ['unauthorized', 'forbidden', 'authentication']):
                raise AuthenticationError(f"Authentication failed for PUT request to {url}: {str(e)}")
            raise RequestError(f"PUT request to {url} failed: {str(e)}", response={"error": str(e)})

    def delete(self, url: str, params: Optional[Dict[str, Any]] = None) -> requests.Response:
        """
        Perform a DELETE request with the session.

        Args:
            url: The URL to request
            params: Query parameters for the request

        Returns:
            Response object

        Raises:
            AuthenticationError: If authentication fails
            RequestError: If the request fails for other reasons
        """
        self._rate_limit()
        try:
            response = self.session.delete(url, params=params, timeout=self.timeout)
            self._check_auth_error(response, url)
            return response
        except AuthenticationError:
            # Re-raise authentication errors
            raise
        except requests.RequestException as e:
            # Check if the exception message indicates an authentication issue
            error_msg = str(e).lower()
            if any(term in error_msg for term in ['unauthorized', 'forbidden', 'authentication']):
                raise AuthenticationError(f"Authentication failed for DELETE request to {url}: {str(e)}")
            raise RequestError(f"DELETE request to {url} failed: {str(e)}", response={"error": str(e)})

    def get_common_params(self) -> Dict[str, str]:
        """
        Get common parameters for API requests.

        Returns:
            Dictionary of common parameters
        """
        # Use actual SPC_CDS from credentials, not random UUID
        spc_cds = self.credential_manager.get_spc_cds()
        spc_cds_ver = self.credential_manager.get_spc_cds_ver() or '2'

        return {
            'SPC_CDS': spc_cds,
            'SPC_CDS_VER': spc_cds_ver
        }
