"""
Health Monitoring Service
Monitors the health status of external APIs including Shopee API and BlueBlue API.
"""
import requests
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import asyncio
import aiohttp
import json

logger = logging.getLogger(__name__)

@dataclass
class HealthStatus:
    """Health status data class"""
    service_name: str
    status: str  # 'healthy', 'degraded', 'unhealthy', 'unknown'
    response_time: Optional[float] = None
    last_check: Optional[datetime] = None
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    authentication_status: Optional[str] = None

class HealthMonitoringService:
    """Service for monitoring external API health status"""
    
    def __init__(self):
        self.timeout = 10  # seconds
        self.cache_duration = 60  # seconds
        self._cache = {}
        
        # Service configurations
        self.services = {
            'shopee_api': {
                'name': 'Shopee API',
                'base_url': 'https://shop.api.limjianhui.com',
                'health_endpoint': '/',  # Use root endpoint since /health doesn't exist
                'auth_endpoint': '/auth/validate',
                'openapi_endpoint': '/openapi.json',
                'requires_auth': False,  # Disable auth check for now since endpoint structure is unclear
                'custom_health_check': True  # Flag to indicate custom health logic needed
            },
            'blueblue_api': {
                'name': 'BlueBlue API',
                'base_url': 'https://blueblue.api.limjianhui.com',
                'health_endpoint': '/api/v1/health',
                'auth_endpoint': '/api/v1/auth/login',
                'openapi_endpoint': '/openapi.json',
                'requires_auth': True,
                'auth_credentials': {
                    'username': 'admin',
                    'password': 'admin123'
                }
            }
        }
    
    def _is_cache_valid(self, service_key: str) -> bool:
        """Check if cached health status is still valid"""
        if service_key not in self._cache:
            return False
        
        cache_entry = self._cache[service_key]
        if 'timestamp' not in cache_entry:
            return False
            
        cache_age = (datetime.now() - cache_entry['timestamp']).total_seconds()
        return cache_age < self.cache_duration
    
    def _get_cached_status(self, service_key: str) -> Optional[HealthStatus]:
        """Get cached health status if valid"""
        if self._is_cache_valid(service_key):
            return self._cache[service_key]['status']
        return None
    
    def _cache_status(self, service_key: str, status: HealthStatus):
        """Cache health status"""
        self._cache[service_key] = {
            'status': status,
            'timestamp': datetime.now()
        }
    
    def check_service_health(self, service_key: str) -> HealthStatus:
        """Check health status of a specific service"""
        # Check cache first
        cached_status = self._get_cached_status(service_key)
        if cached_status:
            return cached_status
        
        if service_key not in self.services:
            return HealthStatus(
                service_name=service_key,
                status='unknown',
                error_message=f'Unknown service: {service_key}',
                last_check=datetime.now()
            )
        
        service_config = self.services[service_key]
        start_time = time.time()
        
        try:
            # Check basic connectivity
            health_status = self._check_basic_health(service_config)
            
            # Check authentication if required
            if service_config.get('requires_auth', False):
                auth_status = self._check_authentication(service_config)
                health_status.authentication_status = auth_status
                
                # If auth fails, mark as degraded
                if auth_status != 'authenticated':
                    health_status.status = 'degraded'
            
            # Check OpenAPI endpoint
            openapi_status = self._check_openapi_endpoint(service_config)
            if not health_status.details:
                health_status.details = {}
            health_status.details['openapi_available'] = openapi_status
            
            health_status.response_time = time.time() - start_time
            health_status.last_check = datetime.now()
            
            # Cache the result
            self._cache_status(service_key, health_status)
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error checking health for {service_key}: {e}")
            error_status = HealthStatus(
                service_name=service_config['name'],
                status='unhealthy',
                response_time=time.time() - start_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
            
            # Cache the error result too
            self._cache_status(service_key, error_status)
            return error_status
    
    def _check_basic_health(self, service_config: Dict[str, Any]) -> HealthStatus:
        """Check basic health endpoint"""
        base_url = service_config['base_url']
        health_endpoint = service_config['health_endpoint']
        service_name = service_config['name']

        try:
            url = f"{base_url}{health_endpoint}"
            response = requests.get(url, timeout=self.timeout)

            if response.status_code == 200:
                # Handle custom health check logic for different services
                if service_config.get('custom_health_check', False):
                    status, details = self._parse_custom_health_response(service_config, response)
                else:
                    status = 'healthy'
                    details = {}
                    try:
                        response_data = response.json()
                        details = response_data

                        # Enhanced BlueBlue API health checking
                        if service_name == 'BlueBlue API':
                            status, details = self._check_blueblue_enhanced_health(service_config, response_data)
                    except:
                        details = {'raw_response': response.text[:500]}

            elif response.status_code in [503, 500]:
                status = 'degraded'
                details = {'status_code': response.status_code, 'response': response.text[:500]}
            else:
                status = 'unhealthy'
                details = {'status_code': response.status_code, 'response': response.text[:500]}

            return HealthStatus(
                service_name=service_name,
                status=status,
                details=details
            )

        except requests.exceptions.Timeout:
            return HealthStatus(
                service_name=service_name,
                status='unhealthy',
                error_message='Request timeout - service responding slowly or unavailable'
            )
        except requests.exceptions.ConnectionError:
            return HealthStatus(
                service_name=service_name,
                status='unhealthy',
                error_message='Connection failed - service may be down or unreachable'
            )
        except Exception as e:
            return HealthStatus(
                service_name=service_name,
                status='unhealthy',
                error_message=f'Health check failed: {str(e)}'
            )

    def _parse_custom_health_response(self, service_config: Dict[str, Any], response) -> tuple:
        """Parse custom health response for services that don't have standard health endpoints"""
        service_name = service_config['name']

        try:
            response_data = response.json()

            if service_name == 'Shopee API':
                # For Shopee API, check if it returns expected service info
                if 'message' in response_data and 'Shopee API' in response_data['message']:
                    return 'healthy', {
                        'service_info': response_data,
                        'health_note': 'Service responding with expected API info'
                    }
                else:
                    return 'degraded', {
                        'service_info': response_data,
                        'health_note': 'Service responding but with unexpected format'
                    }

        except:
            # If we can't parse JSON, but got 200, consider it degraded
            return 'degraded', {
                'raw_response': response.text[:500],
                'health_note': 'Service responding but not returning JSON'
            }

        return 'healthy', {'raw_response': response.text[:500]}

    def _check_blueblue_enhanced_health(self, service_config: Dict[str, Any], response_data: Dict[str, Any]) -> tuple:
        """Enhanced health checking for BlueBlue API with server details"""
        status = 'healthy'
        details = response_data.copy()

        # Check overall API status
        if 'overall_status' in response_data:
            api_status = response_data['overall_status']
            if api_status == 'critical':
                status = 'degraded'
                details['health_note'] = f"API reports {api_status} status"
            elif api_status == 'warning':
                status = 'degraded'
                details['health_note'] = f"API reports {api_status} status"

        # Enhanced server health checking with authentication
        if service_config.get('requires_auth', False):
            try:
                server_details = self._fetch_blueblue_server_details(service_config)
                if server_details:
                    details['servers'] = server_details

                    # Check if any servers have issues
                    for server in server_details:
                        if server.get('status') in ['critical', 'error']:
                            status = 'degraded'
                            if 'health_note' not in details:
                                details['health_note'] = "One or more servers have issues"
            except Exception as e:
                logger.warning(f"Could not fetch enhanced server details: {e}")
                details['server_check_error'] = str(e)

        return status, details

    def _fetch_blueblue_server_details(self, service_config: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Fetch detailed server health information from BlueBlue API"""
        try:
            # First authenticate to get access token
            auth_url = f"{service_config['base_url']}{service_config['auth_endpoint']}"
            auth_data = service_config.get('auth_credentials', {})

            auth_response = requests.post(auth_url, json=auth_data, timeout=self.timeout)
            if auth_response.status_code != 200:
                return None

            token_data = auth_response.json()
            access_token = token_data.get('access_token')
            if not access_token:
                return None

            # Get list of servers first
            servers_url = f"{service_config['base_url']}/api/v1/servers/"
            headers = {'Authorization': f'Bearer {access_token}'}

            servers_response = requests.get(servers_url, headers=headers, timeout=self.timeout)
            if servers_response.status_code != 200:
                return None

            servers = servers_response.json()
            if not isinstance(servers, list):
                return None

            # Get health details for each server
            server_details = []
            for server in servers:
                server_id = server.get('id')
                if server_id:
                    try:
                        health_url = f"{service_config['base_url']}/api/v1/health/servers/{server_id}"
                        health_response = requests.get(health_url, headers=headers, timeout=self.timeout)

                        if health_response.status_code == 200:
                            health_data = health_response.json()
                            server_details.append({
                                'id': server_id,
                                'name': server.get('name', f'Server {server_id}'),
                                'status': health_data.get('status', 'unknown'),
                                'issues': health_data.get('issues', []),
                                'recommendations': health_data.get('recommendations', []),
                                'last_check': health_data.get('last_check'),
                                'response_time': health_data.get('response_time'),
                                'connection_status': health_data.get('connection_status'),
                                'service_status': health_data.get('service_status')
                            })
                        else:
                            server_details.append({
                                'id': server_id,
                                'name': server.get('name', f'Server {server_id}'),
                                'status': 'error',
                                'issues': [f'Health check failed: HTTP {health_response.status_code}'],
                                'recommendations': ['Check server health endpoint'],
                                'last_check': datetime.now().isoformat()
                            })
                    except Exception as e:
                        server_details.append({
                            'id': server_id,
                            'name': server.get('name', f'Server {server_id}'),
                            'status': 'error',
                            'issues': [f'Health check error: {str(e)}'],
                            'recommendations': ['Check server connectivity'],
                            'last_check': datetime.now().isoformat()
                        })

            return server_details

        except Exception as e:
            logger.error(f"Error fetching BlueBlue server details: {e}")
            return None

    def _check_authentication(self, service_config: Dict[str, Any]) -> str:
        """Check authentication status for a service"""
        try:
            base_url = service_config['base_url']
            auth_endpoint = service_config['auth_endpoint']

            if service_config['name'] == 'BlueBlue API':
                # Test BlueBlue API authentication
                auth_url = f"{base_url}{auth_endpoint}"
                auth_data = service_config.get('auth_credentials', {})

                response = requests.post(auth_url, json=auth_data, timeout=self.timeout)

                if response.status_code == 200:
                    try:
                        token_data = response.json()
                        if token_data.get('access_token'):
                            return 'authenticated'
                        else:
                            return 'authentication_failed - no token in response'
                    except:
                        return 'authentication_failed - invalid response format'
                elif response.status_code == 401:
                    return 'authentication_failed - invalid credentials'
                elif response.status_code == 403:
                    return 'authentication_failed - access forbidden'
                else:
                    return f'authentication_failed - HTTP {response.status_code}'

            elif service_config['name'] == 'Shopee API':
                # For Shopee API, skip auth check since we disabled it
                return 'auth_disabled'

        except requests.exceptions.Timeout:
            return 'authentication_error - timeout'
        except requests.exceptions.ConnectionError:
            return 'authentication_error - connection failed'
        except Exception as e:
            logger.error(f"Authentication check failed: {e}")
            return f'authentication_error - {str(e)}'

        return 'unknown'

    def _check_openapi_endpoint(self, service_config: Dict[str, Any]) -> bool:
        """Check if OpenAPI endpoint is accessible"""
        try:
            base_url = service_config['base_url']
            openapi_endpoint = service_config['openapi_endpoint']

            url = f"{base_url}{openapi_endpoint}"
            response = requests.get(url, timeout=self.timeout)

            if response.status_code == 200:
                try:
                    # Try to parse as JSON to validate it's a proper OpenAPI spec
                    openapi_data = response.json()
                    return 'openapi' in openapi_data or 'swagger' in openapi_data
                except:
                    return False
            return False

        except Exception as e:
            logger.error(f"OpenAPI check failed: {e}")
            return False

    def get_all_health_status(self) -> Dict[str, HealthStatus]:
        """Get health status for all monitored services"""
        results = {}

        for service_key in self.services.keys():
            results[service_key] = self.check_service_health(service_key)

        return results

    def get_overall_health_summary(self) -> Dict[str, Any]:
        """Get overall health summary"""
        all_status = self.get_all_health_status()

        healthy_count = sum(1 for status in all_status.values() if status.status == 'healthy')
        degraded_count = sum(1 for status in all_status.values() if status.status == 'degraded')
        unhealthy_count = sum(1 for status in all_status.values() if status.status == 'unhealthy')
        total_count = len(all_status)

        # Determine overall status
        if unhealthy_count > 0:
            overall_status = 'unhealthy'
        elif degraded_count > 0:
            overall_status = 'degraded'
        elif healthy_count == total_count:
            overall_status = 'healthy'
        else:
            overall_status = 'unknown'

        return {
            'overall_status': overall_status,
            'total_services': total_count,
            'healthy_services': healthy_count,
            'degraded_services': degraded_count,
            'unhealthy_services': unhealthy_count,
            'services': {key: {
                'name': status.service_name,
                'status': status.status,
                'response_time': status.response_time,
                'last_check': status.last_check.isoformat() if status.last_check else None,
                'error_message': status.error_message,
                'authentication_status': status.authentication_status
            } for key, status in all_status.items()},
            'last_updated': datetime.now().isoformat()
        }

    def clear_cache(self):
        """Clear health status cache"""
        self._cache.clear()

    def get_service_details(self, service_key: str) -> Dict[str, Any]:
        """Get detailed information about a specific service"""
        if service_key not in self.services:
            return {'error': f'Unknown service: {service_key}'}

        service_config = self.services[service_key]
        health_status = self.check_service_health(service_key)

        return {
            'service_key': service_key,
            'name': service_config['name'],
            'base_url': service_config['base_url'],
            'endpoints': {
                'health': service_config['health_endpoint'],
                'auth': service_config['auth_endpoint'],
                'openapi': service_config['openapi_endpoint']
            },
            'requires_auth': service_config.get('requires_auth', False),
            'current_status': {
                'status': health_status.status,
                'response_time': health_status.response_time,
                'last_check': health_status.last_check.isoformat() if health_status.last_check else None,
                'error_message': health_status.error_message,
                'authentication_status': health_status.authentication_status,
                'details': health_status.details
            }
        }


# Global instance
health_monitoring_service = HealthMonitoringService()
