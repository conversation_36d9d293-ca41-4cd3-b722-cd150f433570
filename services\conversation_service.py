from services.shopee_api_client import shopee_client
from utils.session import session
import config
import logging

logger = logging.getLogger(__name__)

def get_conversation_info_by_ordersn(order_sn):
    # Use the centralized API to search for the order
    response_data, status_code = shopee_client.search_order(order_sn)

    if status_code != 200:
        logger.error(f"Failed to search order {order_sn}: {response_data}")
        return {"error": "Order not found"}, 404

    order_data = response_data.get('data', {})
    card_list = order_data.get('card_list', [])

    if not card_list:
        return {"error": "Order not found"}, 404

    order = card_list[0]['order_card']
    user_id = order['order_ext_info']['buyer_user_id']

    payload = {
        "user_id": user_id,
        "shop_id": config.SHOP_ID
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": config.CSRF_TOKEN,
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "_api_source": "sc"
    }

    conversation_response = session.post(
        config.CONVERSATION_URL,
        params=params,
        json=payload
    )

    if conversation_response.status_code != 200:
        return {"error": "Failed to retrieve conversation info"}, 500

    return conversation_response.json()

def get_conversation_info_by_username(username):
    """Get conversation info by username using centralized Shopee API."""
    try:
        response_data, status_code = shopee_client.search_conversation_by_username(username, raw=True)

        if status_code == 200:
            return response_data.get('data', {})
        else:
            logger.error(f"Failed to get conversation info for {username}: {response_data}")
            return {"error": response_data.get('error', 'Unknown error')}, 500

    except Exception as e:
        logger.error(f"Exception in get_conversation_info_by_username: {str(e)}")
        return {"error": f"Failed to retrieve conversation info: {str(e)}"}, 500