# Design Document

## Overview

The Universal Fake Order System is designed to create comprehensive fake orders that perfectly mimic real Shopee API orders for testing purposes. The system will generate orders with realistic data structures, support all existing product types (Steam, Netflix, VPN, Canva, etc.), and integrate seamlessly with the existing order processing pipeline without requiring any code changes in plugins or services.

## Architecture

### Core Components

1. **Fake Order Generator Service** - Generates realistic fake orders with proper Shopee API structure
2. **Product Template Engine** - Manages product-specific templates and metadata for different SKUs
3. **Order Data Factory** - Creates complete order data structures matching Shopee API format
4. **Fake Order Storage** - Manages persistence and retrieval of fake orders
5. **Integration Layer** - Ensures fake orders work seamlessly with existing order processing

### System Integration Points

- **Order Service** - Existing `order_service.py` already has basic fake order support that will be enhanced
- **Stock Service** - Will recognize fake orders and handle them appropriately
- **Plugin System** - All plugins will process fake orders without modification
- **Admin Interface** - Enhanced UI for creating and managing fake orders

## Components and Interfaces

### 1. Fake Order Generator Service

```python
class FakeOrderGeneratorService:
    def generate_order(self, config: FakeOrderConfig) -> dict
    def generate_batch_orders(self, configs: List[FakeOrderConfig]) -> List[dict]
    def get_product_templates(self) -> dict
    def validate_order_config(self, config: FakeOrderConfig) -> bool
```

**Responsibilities:**
- Generate complete fake order structures
- Apply product-specific templates and metadata
- Ensure unique order IDs and realistic data
- Validate configuration parameters

### 2. Product Template Engine

```python
class ProductTemplateEngine:
    def get_template_for_sku(self, var_sku: str) -> ProductTemplate
    def register_product_template(self, template: ProductTemplate) -> None
    def get_all_templates(self) -> List[ProductTemplate]
    def generate_product_metadata(self, var_sku: str, config: dict) -> dict
```

**Product Templates Include:**
- Steam codes (`steam_auth_code`, `text_based`)
- Netflix accounts (`netflix_30`, `netflix_60`)
- VPN configurations (`vpn_*` patterns)
- Canva Pro (`canva_30`, `canva_lifetime`)
- Custom/extensible templates for future products

### 3. Order Data Factory

```python
class OrderDataFactory:
    def create_shopee_order_structure(self, base_config: dict) -> dict
    def generate_buyer_info(self, config: dict) -> dict
    def generate_order_items(self, products: List[dict]) -> List[dict]
    def generate_timestamps(self, config: dict) -> dict
    def generate_order_metadata(self, config: dict) -> dict
```

**Generated Order Structure:**
```json
{
  "message": "Order details fetched successfully",
  "data": {
    "order_sn": "FAKE_250612BWY37BUV",
    "buyer_user": {
      "user_name": "test_user_123"
    },
    "order_items": [
      {
        "var_sku": "canva_30",
        "product": {
          "name": "Canva Pro 30 Days Subscription"
        },
        "item_model": {
          "sku": "canva_30"
        },
        "quantity": 1,
        "price": 15.00
      }
    ],
    "total_price": 15.00,
    "buyer_address_name": "Test User",
    "buyer_address_phone": "+60123456789",
    "create_time": "2025-01-15T10:30:00Z",
    "status": "To Ship",
    "is_fake_order": true,
    "fake_order_metadata": {
      "created_by": "fake_order_system",
      "test_scenario": "canva_pro_testing"
    }
  }
}
```

### 4. Enhanced Fake Order API

**New Endpoints:**
- `POST /api/fake-orders/generate` - Generate single fake order
- `POST /api/fake-orders/batch-generate` - Generate multiple fake orders
- `GET /api/fake-orders/templates` - Get available product templates
- `GET /api/fake-orders/list` - List all fake orders with filtering
- `DELETE /api/fake-orders/cleanup` - Bulk delete fake orders
- `POST /api/fake-orders/templates/save` - Save custom order templates

**Enhanced Configuration:**
```json
{
  "order_sn": "FAKE_ORDER_001",
  "product_config": {
    "var_sku": "canva_30",
    "quantity": 1,
    "custom_metadata": {}
  },
  "buyer_config": {
    "username": "test_user",
    "name": "Test Customer",
    "phone": "+60123456789",
    "email": "<EMAIL>"
  },
  "order_config": {
    "status": "To Ship",
    "payment_status": "paid",
    "created_date": "2025-01-15",
    "custom_timestamps": {}
  },
  "test_scenario": "canva_pro_basic_flow"
}
```

## Data Models

### FakeOrderConfig
```python
@dataclass
class FakeOrderConfig:
    order_sn: str
    var_sku: str
    buyer_username: str = "test_user"
    buyer_name: str = "Test Customer"
    buyer_phone: str = "+60123456789"
    buyer_email: str = "<EMAIL>"
    status: str = "To Ship"
    payment_status: str = "paid"
    quantity: int = 1
    custom_metadata: dict = field(default_factory=dict)
    test_scenario: str = "general_testing"
```

### ProductTemplate
```python
@dataclass
class ProductTemplate:
    var_sku: str
    product_name: str
    category: str
    default_price: float
    metadata_schema: dict
    validation_rules: dict
    sample_data: dict
```

### FakeOrder
```python
@dataclass
class FakeOrder:
    order_sn: str
    var_sku: str
    order_data: dict
    created_at: datetime
    test_scenario: str
    is_processed: bool = False
    processing_results: dict = field(default_factory=dict)
```

## Error Handling

### Validation Errors
- Invalid SKU validation with suggestions for similar SKUs
- Order SN conflict detection and auto-generation alternatives
- Configuration validation with detailed error messages

### Processing Errors
- Graceful handling when fake orders encounter processing issues
- Clear error logging with fake order context
- Fallback mechanisms for incomplete configurations

### Integration Errors
- Detection and handling of plugin compatibility issues
- Monitoring for fake order processing failures
- Automatic cleanup of problematic fake orders

## Testing Strategy

### Unit Tests
- Test fake order generation with various configurations
- Validate product template engine functionality
- Test order data factory output structure
- Verify integration with existing services

### Integration Tests
- Test fake orders through complete order processing pipeline
- Verify plugin compatibility with fake orders
- Test batch order generation and processing
- Validate cleanup and management operations

### End-to-End Tests
- Create fake orders through UI and process them
- Test different product types and scenarios
- Verify fake order isolation from real orders
- Test performance with large numbers of fake orders

### Test Scenarios
1. **Basic Product Testing** - Simple fake orders for each product type
2. **Edge Case Testing** - Invalid configurations, missing data, error conditions
3. **Batch Processing** - Multiple fake orders with different configurations
4. **Plugin Integration** - Fake orders processed by each plugin
5. **Performance Testing** - Large volumes of fake orders
6. **Cleanup Testing** - Bulk deletion and data management

## Security Considerations

### Data Isolation
- Fake orders clearly marked and separated from real orders
- Prevent fake orders from affecting real inventory or financial data
- Secure storage of fake order data with appropriate access controls

### Access Control
- Fake order creation restricted to authorized users/developers
- Admin-level permissions required for bulk operations
- Audit logging for all fake order operations

### Production Safety
- Clear visual indicators in all interfaces for fake orders
- Automatic exclusion from production reports and metrics
- Safeguards to prevent accidental processing as real orders

## Performance Considerations

### Generation Performance
- Efficient template-based order generation
- Caching of frequently used product templates
- Optimized batch generation for large test datasets

### Storage Optimization
- Efficient storage format for fake order data
- Automatic cleanup of old fake orders
- Indexing for fast retrieval and filtering

### Processing Impact
- Minimal impact on real order processing performance
- Separate processing queues for fake orders when needed
- Resource monitoring and throttling for batch operations