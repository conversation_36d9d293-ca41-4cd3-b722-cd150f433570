#!/usr/bin/env python3
"""
Test script to verify SSH connectivity with the provided credentials
"""

import socket
import sys
import json

def test_basic_connectivity(host: str, port: int) -> dict:
    """Test basic TCP connectivity to host:port"""
    try:
        print(f"Testing TCP connection to {host}:{port}...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            return {
                'success': True,
                'message': f'TCP connection to {host}:{port} successful',
                'test_method': 'Basic TCP'
            }
        else:
            return {
                'success': False,
                'message': f'TCP connection to {host}:{port} failed (error code: {result})',
                'test_method': 'Basic TCP'
            }
    except Exception as e:
        return {
            'success': False,
            'message': f'Connection test failed: {str(e)}',
            'test_method': 'Basic TCP'
        }

def test_ssh_with_paramiko(credentials: dict) -> dict:
    """Test SSH connection with paramiko if available"""
    try:
        import paramiko
        from datetime import datetime
        from io import StringIO
        
        print("Paramiko available, testing full SSH connection...")
        
        start_time = datetime.now()
        
        # Extract connection details
        host = credentials.get('host')
        port = credentials.get('port', 22)
        username = credentials.get('username')
        password = credentials.get('password')
        private_key = credentials.get('private_key')
        private_key_passphrase = credentials.get('private_key_passphrase')
        
        # Create SSH client
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # Prepare authentication
        auth_kwargs = {
            'hostname': host,
            'port': int(port),
            'username': username,
            'timeout': 10
        }
        
        # Use private key if provided
        if private_key and private_key.strip():
            try:
                key_file = StringIO(private_key)
                
                # Try different key types
                for key_class in [paramiko.RSAKey, paramiko.DSAKey, paramiko.ECDSAKey, paramiko.Ed25519Key]:
                    try:
                        key_file.seek(0)
                        pkey = key_class.from_private_key(key_file, password=private_key_passphrase)
                        auth_kwargs['pkey'] = pkey
                        break
                    except Exception:
                        continue
                else:
                    return {
                        'success': False,
                        'message': 'Invalid private key format',
                        'test_method': 'Paramiko SSH'
                    }
            except Exception as e:
                return {
                    'success': False,
                    'message': f'Private key error: {str(e)}',
                    'test_method': 'Paramiko SSH'
                }
        elif password:
            auth_kwargs['password'] = password
        else:
            return {
                'success': False,
                'message': 'No authentication method provided',
                'test_method': 'Paramiko SSH'
            }
        
        # Attempt connection
        ssh.connect(**auth_kwargs)
        
        # Test with a simple command
        stdin, stdout, stderr = ssh.exec_command('echo "SSH connection test successful"')
        output = stdout.read().decode().strip()
        
        ssh.close()
        
        connection_time = (datetime.now() - start_time).total_seconds()
        
        return {
            'success': True,
            'message': 'SSH connection successful',
            'connection_time': f'{connection_time:.2f}s',
            'test_method': 'Paramiko SSH',
            'test_output': output
        }
        
    except ImportError:
        return {
            'success': False,
            'message': 'Paramiko not available',
            'test_method': 'Paramiko SSH (unavailable)'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'SSH connection failed: {str(e)}',
            'test_method': 'Paramiko SSH'
        }

def main():
    """Main test function"""
    # Test credentials from user
    credentials = {
        "host": "server2.online-mtyb.com",
        "port": 20203,
        "username": "root",
        "password": "whitepaperh0817",
        "private_key": "",
        "private_key_passphrase": ""
    }
    
    print("=== SSH Connectivity Test ===")
    print(f"Testing connection to: {credentials['host']}:{credentials['port']}")
    print(f"Username: {credentials['username']}")
    print()
    
    # Test basic TCP connectivity first
    print("1. Testing basic TCP connectivity...")
    tcp_result = test_basic_connectivity(credentials['host'], credentials['port'])
    print(f"Result: {tcp_result}")
    print()
    
    # Test full SSH connection if TCP works
    if tcp_result.get('success'):
        print("2. Testing SSH authentication...")
        ssh_result = test_ssh_with_paramiko(credentials)
        print(f"Result: {ssh_result}")
    else:
        print("2. Skipping SSH test due to TCP connectivity failure")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
