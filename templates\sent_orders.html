{% extends "base.html" %}

{% block title %}Manage Sent Orders{% endblock %}
{% block header %}Manage Sent Orders{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="sentOrdersData()" x-init="init()">
    <div x-show="isLoaded">
        <div class="section-content">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold">Sent Orders List</h2>
                <button @click="resetAllOrders" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Reset All Orders
                </button>
            </div>

            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order SN</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="order in sentOrders" :key="order">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap" x-text="order"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <button @click="deleteOrder(order)" class="text-red-600 hover:text-red-900">Delete</button>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading sent orders...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function sentOrdersData() {
        return {
            sentOrders: [],
            isLoaded: false,
            init() {
                this.loadSentOrders();
            },
            loadSentOrders() {
                fetch('/admin/get_sent_orders')
                    .then(response => response.json())
                    .then(data => {
                        this.sentOrders = data.sent_orders;
                        this.isLoaded = true;
                    })
                    .catch(error => {
                        console.error('Error loading sent orders:', error);
                        alert('Failed to load sent orders.');
                    });
            },
            deleteOrder(orderSn) {
                if (!confirm(`Are you sure you want to delete order ${orderSn}?`)) return;

                fetch('/admin/delete_sent_order', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ order_sn: orderSn })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.loadSentOrders();
                    } else {
                        alert('Failed to delete order: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error deleting order:', error);
                    alert('An error occurred while deleting the order.');
                });
            },
            resetAllOrders() {
                if (!confirm('Are you sure you want to reset all sent orders? This cannot be undone.')) return;

                fetch('/admin/reset_sent_orders', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    this.loadSentOrders();
                })
                .catch(error => {
                    console.error('Error resetting orders:', error);
                    alert('An error occurred while resetting all orders.');
                });
            }
        }
    }
</script>
{% endblock %}
