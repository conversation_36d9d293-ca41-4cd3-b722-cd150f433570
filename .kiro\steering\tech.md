# Technology Stack & Build System

## Core Framework
- **Backend**: Flask 3.1.0 with Python 3.9+
- **Architecture**: Plugin-based modular system
- **Database**: JSON-based configuration and data storage
- **Containerization**: Docker with Docker Compose

## Key Dependencies
- **Web Framework**: Flask, Flask-CORS, Flask-SocketIO, Flask-WTF
- **HTTP/API**: requests, httpx, aiohttp for external API calls
- **AI Integration**: OpenAI SDK for DeepSeek AI chat automation
- **Security**: cryptography, PyJWT, bcrypt for authentication and encryption
- **Automation**: selenium, playwright, PyAutoG<PERSON> for browser automation
- **Scheduling**: APScheduler for background tasks
- **Data Processing**: BeautifulSoup4, Pillow for content processing

## Build & Deployment Commands

### Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Run application
python main.py

# Run with specific port
python main.py --port 5001
```

### Docker Deployment (Recommended)
```bash
# One-click deployment (Windows)
.\deploy-steamcodetool.bat

# One-click deployment (Linux/macOS)
chmod +x deploy-steamcodetool.sh
./deploy-steamcodetool.sh

# Manual Docker commands
docker-compose -f docker-compose.steamcodetool.yml up -d
docker-compose -f docker-compose.steamcodetool.yml logs -f
docker-compose -f docker-compose.steamcodetool.yml down
```

### Development with Docker
```bash
# Build local image
docker-compose -f docker-compose.steamcodetool.yml build mtyb-tools-local

# Run local development container
docker-compose -f docker-compose.steamcodetool.yml up mtyb-tools-local
```

## Configuration Management
- **Core Config**: `configs/core/config.json` - Main application settings
- **Plugin Config**: `configs/core/plugin_config.json` - Plugin-specific settings
- **Service Config**: `configs/services/` - Service-specific configurations
- **Data Storage**: `configs/data/` - Application data files
- **Cache**: `configs/cache/` - Temporary cache files

## Environment Variables
- `PORT`: Application port (default: 5000)
- `ENVIRONMENT`: production/development
- `PYTHONPATH`: Set to /app in containers

## Testing
```bash
# Run plugin tests
python -m pytest plugins/*/test_*.py

# Run specific plugin test
python plugins/vpn/test_vpn_plugin.py
```

## Security Considerations
- All sensitive data stored in encrypted configuration files
- Rate limiting and IP blocking implemented
- CSRF protection enabled
- Security headers enforced
- Audit logging for all admin actions