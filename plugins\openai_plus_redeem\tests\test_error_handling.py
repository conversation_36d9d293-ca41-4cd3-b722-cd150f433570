"""
Error Handling Validation Tests

Tests all error scenarios, exception handling, graceful degradation,
and ensures proper logging and user feedback:
- Service initialization failures
- Network connectivity issues
- File system errors
- Invalid data scenarios
- Resource exhaustion
- External service failures
- Concurrent access conflicts
"""

import unittest
import tempfile
import shutil
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Import plugin components
from ..plugin import Plugin
from ..services.service_manager import ServiceManager
from ..services.chatgpt_account_service import ChatGPTAccountService
from ..services.order_redemption_service import OrderRedemptionService
from ..services.email_service import EmailService
from ..services.cooldown_service import CooldownService
from ..models.chatgpt_account import ChatGPTAccount, AccountStatus
from ..models.order_redemption import OrderRedemption, RedemptionStatus


class TestErrorHandling(unittest.TestCase):
    """Test error handling and graceful degradation"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_config = {
            'enabled': True,
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            },
            'cooldown_config': {
                'default_cooldown_hours': 24
            }
        }
        
        # Mock logger to capture log messages
        self.mock_logger = Mock()
        self.log_messages = []
        
        def capture_log(level, message):
            self.log_messages.append((level, message))
        
        self.mock_logger.info.side_effect = lambda msg: capture_log('INFO', msg)
        self.mock_logger.warning.side_effect = lambda msg: capture_log('WARNING', msg)
        self.mock_logger.error.side_effect = lambda msg: capture_log('ERROR', msg)
        self.mock_logger.debug.side_effect = lambda msg: capture_log('DEBUG', msg)
        
        # Setup data file mocks
        self.data_file_patches = []
        self._setup_data_file_mocks()
    
    def tearDown(self):
        """Clean up test environment"""
        for patcher in self.data_file_patches:
            patcher.stop()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _setup_data_file_mocks(self):
        """Setup mocks for data file operations"""
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.temp_dir) / filename)
        
        patcher = patch('plugins.openai_plus_redeem.models.utils.get_data_file_path', 
                       side_effect=mock_get_data_file_path)
        patcher.start()
        self.data_file_patches.append(patcher)
    
    def test_service_initialization_failures(self):
        """Test service initialization failure scenarios"""
        print("\n🚨 Testing Service Initialization Failures")
        
        # Test 1: Invalid configuration
        invalid_config = {
            'enabled': True,
            'email_config': {
                # Missing required fields
            }
        }
        
        account_service = ChatGPTAccountService(invalid_config, self.mock_logger)
        result = account_service.initialize()
        
        # Should fail gracefully
        self.assertFalse(result)
        
        # Should log appropriate error
        error_logs = [msg for level, msg in self.log_messages if level == 'ERROR']
        self.assertGreater(len(error_logs), 0, "Should log initialization errors")
        
        # Test 2: File system permission errors
        # Make temp directory read-only
        os.chmod(self.temp_dir, 0o444)
        
        try:
            account_service = ChatGPTAccountService(self.test_config, self.mock_logger)
            result = account_service.initialize()
            
            # Should handle permission errors gracefully
            self.assertFalse(result)
            
        finally:
            # Restore permissions for cleanup
            os.chmod(self.temp_dir, 0o755)
    
    def test_file_system_error_handling(self):
        """Test file system error handling"""
        print("\n💾 Testing File System Error Handling")
        
        account_service = ChatGPTAccountService(self.test_config, self.mock_logger)
        account_service.initialize()
        
        # Test 1: Disk full simulation (mock)
        with patch('plugins.openai_plus_redeem.models.utils.save_json_data', return_value=False):
            test_account = ChatGPTAccount(
                account_id='DISK_FULL_TEST',
                email='<EMAIL>',
                password='password123',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            result = account_service.add_account(test_account)
            
            # Should fail gracefully
            self.assertFalse(result['success'])
            self.assertIn('error', result)
            
            # Should log appropriate error
            error_logs = [msg for level, msg in self.log_messages if level == 'ERROR']
            self.assertGreater(len(error_logs), 0)
        
        # Test 2: Corrupted data file handling
        data_file = Path(self.temp_dir) / 'chatgpt_accounts.json'
        with open(data_file, 'w') as f:
            f.write('{ invalid json content }')
        
        # Should handle corrupted file gracefully
        account_service = ChatGPTAccountService(self.test_config, self.mock_logger)
        result = account_service.initialize()
        
        # Should either recover or fail gracefully
        self.assertIsInstance(result, bool)
    
    def test_network_connectivity_errors(self):
        """Test network connectivity error handling"""
        print("\n🌐 Testing Network Connectivity Errors")
        
        # Test email service with network errors
        email_service = EmailService(self.test_config, self.mock_logger)
        email_service.initialize()
        
        # Mock IMAP connection failure
        with patch('imaplib.IMAP4_SSL') as mock_imap:
            mock_imap.side_effect = ConnectionError("Network unreachable")
            
            result = email_service.retrieve_verification_code(
                '<EMAIL>',
                'openai'
            )
            
            # Should handle network errors gracefully
            self.assertFalse(result['success'])
            self.assertIn('error', result)
            self.assertIn('network', result['error'].lower())
            
            # Should log appropriate error
            error_logs = [msg for level, msg in self.log_messages if level == 'ERROR']
            network_errors = [msg for msg in error_logs if 'network' in msg.lower() or 'connection' in msg.lower()]
            self.assertGreater(len(network_errors), 0)
    
    def test_invalid_data_scenarios(self):
        """Test invalid data scenario handling"""
        print("\n📊 Testing Invalid Data Scenarios")
        
        account_service = ChatGPTAccountService(self.test_config, self.mock_logger)
        account_service.initialize()
        
        # Test 1: Invalid account data
        invalid_accounts = [
            # Missing required fields
            ChatGPTAccount(
                account_id='',  # Empty ID
                email='<EMAIL>',
                password='password123',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            ),
            # Invalid email format
            ChatGPTAccount(
                account_id='INVALID_EMAIL_TEST',
                email='not-an-email',
                password='password123',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            ),
            # Negative values
            ChatGPTAccount(
                account_id='NEGATIVE_VALUES_TEST',
                email='<EMAIL>',
                password='password123',
                max_concurrent_users=-1,  # Invalid
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            ),
            # Past expiration date
            ChatGPTAccount(
                account_id='EXPIRED_TEST',
                email='<EMAIL>',
                password='password123',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() - timedelta(days=30),  # Expired
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
        ]
        
        for invalid_account in invalid_accounts:
            result = account_service.add_account(invalid_account)
            
            # Should reject invalid data
            self.assertFalse(result['success'])
            self.assertIn('error', result)
            
            # Should provide meaningful error message
            self.assertIsInstance(result['error'], str)
            self.assertGreater(len(result['error']), 0)
    
    def test_resource_exhaustion_handling(self):
        """Test resource exhaustion handling"""
        print("\n⚡ Testing Resource Exhaustion Handling")
        
        account_service = ChatGPTAccountService(self.test_config, self.mock_logger)
        account_service.initialize()
        
        # Test 1: Memory exhaustion simulation
        # Create a large number of accounts to test memory handling
        large_accounts = []
        for i in range(1000):  # Large but manageable number
            account = ChatGPTAccount(
                account_id=f'RESOURCE_TEST_{i:04d}',
                email=f'resource{i}@example.com',
                password=f'password{i}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            large_accounts.append(account)
        
        # Add accounts and monitor for graceful handling
        successful_adds = 0
        for account in large_accounts:
            result = account_service.add_account(account)
            if result['success']:
                successful_adds += 1
        
        # Should handle large datasets without crashing
        self.assertGreater(successful_adds, 0, "Should successfully add at least some accounts")
        
        # Test 2: Concurrent access handling
        import threading
        
        def concurrent_add_worker(worker_id):
            account = ChatGPTAccount(
                account_id=f'CONCURRENT_ERROR_{worker_id}',
                email=f'concurrent{worker_id}@example.com',
                password=f'password{worker_id}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            return account_service.add_account(account)
        
        # Run concurrent operations
        threads = []
        results = []
        
        for i in range(10):
            thread = threading.Thread(target=lambda i=i: results.append(concurrent_add_worker(i)))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Should handle concurrent access without corruption
        self.assertEqual(len(results), 10, "All concurrent operations should complete")
    
    def test_external_service_failures(self):
        """Test external service failure handling"""
        print("\n🔌 Testing External Service Failures")
        
        # Test Shopee API integration failures
        order_redeem_service = OrderRedemptionService(self.test_config, self.mock_logger)
        order_redeem_service.initialize()
        
        # Mock Shopee API failure
        with patch.object(order_redeem_service, '_search_order', side_effect=Exception("Shopee API unavailable")):
            result = order_redeem_service.process_order_redemption(
                order_id='EXTERNAL_FAIL_TEST',
                buyer_username='test_user',
                sku='chatgpt_plus',
                var_sku='chatgpt_5_30'
            )
            
            # Should handle external service failures gracefully
            self.assertFalse(result['success'])
            self.assertIn('error', result)
            
            # Should log appropriate error
            error_logs = [msg for level, msg in self.log_messages if level == 'ERROR']
            api_errors = [msg for msg in error_logs if 'api' in msg.lower() or 'shopee' in msg.lower()]
            self.assertGreater(len(api_errors), 0)
    
    def test_graceful_degradation(self):
        """Test graceful degradation scenarios"""
        print("\n🛡️  Testing Graceful Degradation")
        
        # Test service manager with partial service failures
        service_manager = ServiceManager(self.test_config, self.mock_logger)
        
        # Mock one service to fail initialization
        with patch.object(EmailService, 'initialize', return_value=False):
            result = service_manager.initialize_all_services()
            
            # Should continue with other services even if one fails
            self.assertIsInstance(result, bool)
            
            # Should still have some services available
            available_services = service_manager.get_available_services()
            self.assertGreater(len(available_services), 0, "Should have some services available")
    
    def test_logging_and_user_feedback(self):
        """Test proper logging and user feedback"""
        print("\n📝 Testing Logging and User Feedback")
        
        account_service = ChatGPTAccountService(self.test_config, self.mock_logger)
        account_service.initialize()
        
        # Clear previous log messages
        self.log_messages.clear()
        
        # Test successful operation logging
        test_account = ChatGPTAccount(
            account_id='LOGGING_TEST',
            email='<EMAIL>',
            password='password123',
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        
        result = account_service.add_account(test_account)
        
        # Should log successful operations
        info_logs = [msg for level, msg in self.log_messages if level == 'INFO']
        self.assertGreater(len(info_logs), 0, "Should log successful operations")
        
        # Test error operation logging
        duplicate_result = account_service.add_account(test_account)  # Duplicate ID
        
        # Should log errors appropriately
        error_logs = [msg for level, msg in self.log_messages if level == 'ERROR']
        self.assertGreater(len(error_logs), 0, "Should log error operations")
        
        # Test user feedback quality
        self.assertFalse(duplicate_result['success'])
        self.assertIn('error', duplicate_result)
        self.assertIsInstance(duplicate_result['error'], str)
        self.assertGreater(len(duplicate_result['error']), 10, "Error messages should be descriptive")
        
        # Error messages should not expose internal details
        self.assertNotIn('traceback', duplicate_result['error'].lower())
        self.assertNotIn('exception', duplicate_result['error'].lower())


class TestExceptionHandling(unittest.TestCase):
    """Test specific exception handling scenarios"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_logger = Mock()
        self.test_config = {
            'enabled': True,
            'email_config': {
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_json_parsing_exceptions(self):
        """Test JSON parsing exception handling"""
        # Create corrupted JSON file
        corrupted_file = Path(self.temp_dir) / 'corrupted.json'
        with open(corrupted_file, 'w') as f:
            f.write('{ "invalid": json content }')
        
        # Test loading corrupted JSON
        from plugins.openai_plus_redeem.models.utils import load_json_data
        
        result = load_json_data(str(corrupted_file))
        
        # Should handle JSON parsing errors gracefully
        self.assertIsNone(result, "Should return None for corrupted JSON")
    
    def test_file_not_found_exceptions(self):
        """Test file not found exception handling"""
        from plugins.openai_plus_redeem.models.utils import load_json_data
        
        # Test loading non-existent file
        result = load_json_data('/non/existent/file.json')
        
        # Should handle file not found gracefully
        self.assertIsNone(result, "Should return None for non-existent file")
    
    def test_permission_denied_exceptions(self):
        """Test permission denied exception handling"""
        from plugins.openai_plus_redeem.models.utils import save_json_data
        
        # Test saving to protected location
        result = save_json_data('/root/protected.json', {'test': 'data'})
        
        # Should handle permission errors gracefully
        self.assertFalse(result, "Should return False for permission denied")


class TestRecoveryMechanisms(unittest.TestCase):
    """Test recovery and resilience mechanisms"""

    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_logger = Mock()
        self.test_config = {
            'enabled': True,
            'email_config': {
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }

    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_service_recovery_after_failure(self):
        """Test service recovery after failure"""
        account_service = ChatGPTAccountService(self.test_config, self.mock_logger)

        # Simulate initialization failure
        with patch.object(account_service, '_load_accounts', side_effect=Exception("Load failed")):
            result = account_service.initialize()
            self.assertFalse(result)

        # Test recovery - should be able to reinitialize
        result = account_service.initialize()
        self.assertTrue(result, "Service should recover after failure")

    def test_data_backup_and_recovery(self):
        """Test data backup and recovery mechanisms"""
        from plugins.openai_plus_redeem.models.utils import backup_data_file, load_json_data, save_json_data

        # Create test data file
        test_file = Path(self.temp_dir) / 'recovery_test.json'
        test_data = {'test': 'data', 'timestamp': datetime.now().isoformat()}

        save_json_data(str(test_file), test_data)

        # Create backup
        backup_result = backup_data_file(str(test_file))
        self.assertTrue(backup_result['success'], "Backup should succeed")

        # Simulate data corruption
        with open(test_file, 'w') as f:
            f.write('corrupted data')

        # Verify corruption
        corrupted_data = load_json_data(str(test_file))
        self.assertIsNone(corrupted_data, "Corrupted file should return None")

        # Test recovery from backup
        backup_file = Path(backup_result['backup_path'])
        self.assertTrue(backup_file.exists(), "Backup file should exist")

        backup_data = load_json_data(str(backup_file))
        self.assertIsNotNone(backup_data, "Should be able to load from backup")
        self.assertEqual(backup_data['test'], 'data', "Backup data should be intact")


if __name__ == '__main__':
    unittest.main()
