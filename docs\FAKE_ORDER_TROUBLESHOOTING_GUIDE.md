# Fake Order System - Troubleshooting Guide & Best Practices

## Table of Contents

1. [Common Issues and Solutions](#common-issues-and-solutions)
2. [Error Codes Reference](#error-codes-reference)
3. [Performance Troubleshooting](#performance-troubleshooting)
4. [Security Issues](#security-issues)
5. [Maintenance Problems](#maintenance-problems)
6. [Best Practices](#best-practices)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [FAQ](#frequently-asked-questions)

## Common Issues and Solutions

### Issue: Order Generation Fails with "MISSING_VAR_SKU" Error

**Symptoms:**
```json
{
  "success": false,
  "error": "var_sku is required in product_config",
  "error_code": "MISSING_VAR_SKU"
}
```

**Cause:** The `var_sku` field is missing from the `product_config` section.

**Solution:**
```json
{
  "product_config": {
    "var_sku": "canva_30",  // ← This field is required
    "quantity": 1
  }
}
```

**Prevention:** Always validate your request payload includes required fields before sending.

### Issue: Batch Generation Exceeds Size Limit

**Symptoms:**
```json
{
  "success": false,
  "error": "Batch size exceeds maximum limit of 50",
  "error_code": "BATCH_SIZE_EXCEEDED"
}
```

**Cause:** Trying to create more than 50 orders in a single batch request.

**Solution:**
```python
# Split large batches into smaller chunks
def create_large_batch(orders, batch_size=50):
    results = []
    for i in range(0, len(orders), batch_size):
        batch = orders[i:i + batch_size]
        batch_data = {
            "orders": batch,
            "batch_config": {"continue_on_error": True}
        }
        result = requests.post(url, json=batch_data, headers=headers)
        results.append(result.json())
    return results
```

### Issue: Orders Not Found After Creation

**Symptoms:** Order creation succeeds but subsequent queries return empty results.

**Possible Causes:**
1. **Indexing delay:** The order index hasn't been updated yet
2. **Storage corruption:** The storage file is corrupted
3. **Permission issues:** Insufficient permissions to read orders

**Solutions:**

1. **Wait and retry:**
```python
import time

# Create order
order_response = create_order(order_data)
order_sn = order_response["order_sn"]

# Wait for indexing
time.sleep(1)

# Query order
order = get_order(order_sn)
```

2. **Force index rebuild:**
```bash
curl -X POST "https://your-domain/api/fake-orders/maintenance/run" \
  -H "X-API-Key: your-api-key" \
  -d '{"maintenance_type": "optimization"}'
```

3. **Check storage integrity:**
```python
# Check maintenance status
response = requests.get(
    "https://your-domain/api/fake-orders/maintenance/status",
    headers={"X-API-Key": "your-api-key"}
)
print(response.json())
```

### Issue: High Memory Usage

**Symptoms:** Application consuming excessive memory, slow response times.

**Causes:**
- Large number of cached orders
- Memory leaks in order processing
- Insufficient cleanup

**Solutions:**

1. **Run storage optimization:**
```bash
curl -X POST "https://your-domain/api/fake-orders/maintenance/run" \
  -H "X-API-Key: your-api-key" \
  -d '{"maintenance_type": "optimization"}'
```

2. **Adjust cache settings:**
```python
# In your configuration
FAKE_ORDER_CACHE_SIZE = 500  # Reduce from default 1000
FAKE_ORDER_CACHE_TTL = 300   # 5 minutes
```

3. **Enable automatic cleanup:**
```bash
curl -X POST "https://your-domain/api/fake-orders/scheduler/task/daily_cleanup/toggle" \
  -H "X-API-Key: your-api-key" \
  -d '{"enable": true}'
```

### Issue: Slow Order Processing

**Symptoms:** Order processing takes longer than expected.

**Diagnostic Steps:**

1. **Check system health:**
```bash
curl -H "X-API-Key: your-api-key" \
  "https://your-domain/api/fake-orders/health"
```

2. **Monitor processing times:**
```python
import time

start_time = time.time()
result = process_order(order_sn)
processing_time = time.time() - start_time

print(f"Processing took {processing_time:.2f} seconds")
```

3. **Check for storage issues:**
```bash
# Check storage statistics
curl -H "X-API-Key: your-api-key" \
  "https://your-domain/api/fake-orders/maintenance/status"
```

**Solutions:**

1. **Optimize storage:**
```json
{
  "maintenance_type": "optimization",
  "config": {
    "rebuild_indexes": true,
    "compact_storage": true
  }
}
```

2. **Reduce concurrent processing:**
```python
# Limit concurrent requests
import threading
semaphore = threading.Semaphore(5)  # Max 5 concurrent

def process_with_limit(order_sn):
    with semaphore:
        return process_order(order_sn)
```

## Error Codes Reference

### Generation Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `MISSING_PAYLOAD` | No JSON payload provided | Include valid JSON in request body |
| `MISSING_PRODUCT_CONFIG` | product_config section missing | Add product_config with var_sku |
| `MISSING_VAR_SKU` | var_sku field missing | Specify valid product SKU |
| `VALIDATION_FAILED` | Order configuration invalid | Check validation_errors in response |
| `GENERATION_FAILED` | Order generation failed | Check logs for detailed error |

### Batch Operation Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `MISSING_ORDERS_ARRAY` | orders array missing | Provide orders array in payload |
| `EMPTY_ORDERS_ARRAY` | orders array is empty | Include at least one order config |
| `BATCH_SIZE_EXCEEDED` | Too many orders in batch | Split into smaller batches (≤50) |
| `BATCH_VALIDATION_FAILED` | Batch validation failed | Fix validation errors or enable continue_on_error |

### Cleanup Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `CONFIRMATION_REQUIRED` | Missing confirmation parameter | Add ?confirm=true or "confirm": true |
| `INVALID_ORDER_SNS` | Invalid order_sns array | Provide valid array of order SNs |
| `CLEANUP_FAILED` | Cleanup operation failed | Check permissions and storage integrity |

### Maintenance Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `INVALID_MAINTENANCE_TYPE` | Invalid maintenance type | Use: cleanup, optimization, archive, or full |
| `MAINTENANCE_FAILED` | Maintenance operation failed | Check logs and system resources |
| `CONFIG_UPDATE_FAILED` | Configuration update failed | Verify configuration format |

### Authentication/Authorization Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `UNAUTHORIZED` | Missing or invalid API key | Provide valid X-API-Key header |
| `PERMISSION_DENIED` | Insufficient permissions | Check user permissions for operation |
| `RATE_LIMITED` | Too many requests | Implement rate limiting in client |

## Performance Troubleshooting

### Identifying Performance Bottlenecks

1. **Monitor API Response Times:**
```python
import time
import statistics

def benchmark_api_calls(endpoint, data, iterations=10):
    times = []
    for _ in range(iterations):
        start = time.time()
        response = requests.post(endpoint, json=data, headers=headers)
        end = time.time()
        times.append(end - start)
    
    return {
        'average': statistics.mean(times),
        'median': statistics.median(times),
        'min': min(times),
        'max': max(times)
    }

# Benchmark order generation
stats = benchmark_api_calls('/api/fake-orders/generate', order_data)
print(f"Average response time: {stats['average']:.2f}s")
```

2. **Check Storage Performance:**
```bash
# Get storage statistics
curl -H "X-API-Key: your-api-key" \
  "https://your-domain/api/fake-orders/maintenance/status" | \
  jq '.maintenance_status.current_storage_stats'
```

3. **Monitor System Resources:**
```python
import psutil

def check_system_resources():
    return {
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_usage': psutil.disk_usage('/').percent
    }

resources = check_system_resources()
print(f"System resources: {resources}")
```

### Performance Optimization Strategies

1. **Batch Operations:**
```python
# Instead of individual requests
for order_config in order_configs:
    create_order(order_config)  # Slow

# Use batch operations
create_batch_orders(order_configs)  # Fast
```

2. **Implement Caching:**
```python
import functools
import time

@functools.lru_cache(maxsize=100)
def get_template_cached(var_sku):
    return get_template(var_sku)

# Cache with TTL
def cache_with_ttl(ttl_seconds):
    def decorator(func):
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            key = str(args) + str(kwargs)
            now = time.time()
            
            if key in cache:
                result, timestamp = cache[key]
                if now - timestamp < ttl_seconds:
                    return result
            
            result = func(*args, **kwargs)
            cache[key] = (result, now)
            return result
        
        return wrapper
    return decorator

@cache_with_ttl(300)  # 5 minutes
def get_order_details(order_sn):
    return fetch_order_details(order_sn)
```

3. **Optimize Database Queries:**
```python
# Use specific filters to reduce data transfer
orders = list_orders(
    test_scenario="specific_test",
    limit=10,
    created_after="2025-01-15T00:00:00Z"
)

# Instead of fetching all orders
all_orders = list_orders()  # Slow for large datasets
```

## Security Issues

### Common Security Problems

1. **API Key Exposure:**
```python
# ❌ Bad: API key in code
API_KEY = "sk-1234567890abcdef"

# ✅ Good: API key from environment
import os
API_KEY = os.getenv('FAKE_ORDER_API_KEY')

# ✅ Better: Use secure key management
from your_key_manager import get_secret
API_KEY = get_secret('fake_order_api_key')
```

2. **Insufficient Input Validation:**
```python
# ✅ Validate inputs before sending
def validate_order_data(order_data):
    required_fields = ['product_config', 'buyer_config']
    for field in required_fields:
        if field not in order_data:
            raise ValueError(f"Missing required field: {field}")
    
    if 'var_sku' not in order_data['product_config']:
        raise ValueError("var_sku is required in product_config")
    
    return True

# Use validation
validate_order_data(order_data)
response = create_order(order_data)
```

3. **Fake Order Leakage:**
```python
# ✅ Always verify fake order markers
def verify_fake_order(order_data):
    fake_markers = [
        'FAKE_' in order_data.get('order_sn', ''),
        order_data.get('_fake_order_marker') == True,
        'test' in order_data.get('test_scenario', '').lower()
    ]
    
    if not any(fake_markers):
        raise SecurityError("Order does not appear to be a fake order")
    
    return True
```

### Security Best Practices

1. **Use HTTPS Only:**
```python
# ✅ Always use HTTPS
BASE_URL = "https://your-domain.com"  # Not http://

# ✅ Verify SSL certificates
import requests
requests.get(url, verify=True)  # Default, but be explicit
```

2. **Implement Rate Limiting:**
```python
import time
from collections import defaultdict

class RateLimiter:
    def __init__(self, max_requests=100, time_window=60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = defaultdict(list)
    
    def allow_request(self, client_id):
        now = time.time()
        client_requests = self.requests[client_id]
        
        # Remove old requests
        client_requests[:] = [req_time for req_time in client_requests 
                             if now - req_time < self.time_window]
        
        if len(client_requests) >= self.max_requests:
            return False
        
        client_requests.append(now)
        return True

rate_limiter = RateLimiter()

def make_api_request(endpoint, data, client_id="default"):
    if not rate_limiter.allow_request(client_id):
        raise Exception("Rate limit exceeded")
    
    return requests.post(endpoint, json=data, headers=headers)
```

3. **Secure Configuration:**
```python
# ✅ Secure configuration management
import json
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self, key_file='config.key'):
        with open(key_file, 'rb') as f:
            self.key = f.read()
        self.cipher = Fernet(self.key)
    
    def encrypt_config(self, config_data):
        json_data = json.dumps(config_data).encode()
        return self.cipher.encrypt(json_data)
    
    def decrypt_config(self, encrypted_data):
        decrypted_data = self.cipher.decrypt(encrypted_data)
        return json.loads(decrypted_data.decode())

# Usage
config = SecureConfig()
api_key = config.decrypt_config(encrypted_api_key)
```

## Maintenance Problems

### Scheduled Maintenance Not Running

**Symptoms:** Scheduled tasks not executing, old orders accumulating.

**Diagnostic Steps:**
```bash
# Check scheduler status
curl -H "X-API-Key: your-api-key" \
  "https://your-domain/api/fake-orders/scheduler/status"

# Check specific task
curl -H "X-API-Key: your-api-key" \
  "https://your-domain/api/fake-orders/scheduler/status?task_name=daily_cleanup"
```

**Solutions:**

1. **Enable disabled tasks:**
```bash
curl -X POST "https://your-domain/api/fake-orders/scheduler/task/daily_cleanup/toggle" \
  -H "X-API-Key: your-api-key" \
  -d '{"enable": true}'
```

2. **Check for errors:**
```python
# Get task status with error information
response = requests.get(
    "https://your-domain/api/fake-orders/scheduler/status",
    headers={"X-API-Key": "your-api-key"}
)

tasks = response.json()["scheduler_status"]["tasks"]
for task_name, task_info in tasks.items():
    if task_info["error_count"] > 0:
        print(f"Task {task_name} has {task_info['error_count']} errors")
```

3. **Manual maintenance run:**
```bash
# Run maintenance manually to check for issues
curl -X POST "https://your-domain/api/fake-orders/maintenance/run" \
  -H "X-API-Key: your-api-key" \
  -d '{"maintenance_type": "full"}'
```

### Storage Corruption Issues

**Symptoms:** Orders disappearing, index errors, maintenance failures.

**Recovery Steps:**

1. **Backup current data:**
```bash
# Create backup before attempting recovery
cp -r configs/data/fake_orders configs/data/fake_orders_backup_$(date +%Y%m%d_%H%M%S)
```

2. **Rebuild indexes:**
```bash
curl -X POST "https://your-domain/api/fake-orders/maintenance/run" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "maintenance_type": "optimization",
    "config": {
      "rebuild_indexes": true,
      "compact_storage": true
    }
  }'
```

3. **Validate data integrity:**
```python
import json

def validate_orders_file(file_path):
    try:
        with open(file_path, 'r') as f:
            orders = json.load(f)
        
        if not isinstance(orders, list):
            return False, "Orders file is not a list"
        
        for i, order in enumerate(orders):
            if not isinstance(order, dict):
                return False, f"Order {i} is not a dictionary"
            
            required_fields = ['order_sn', 'var_sku', 'created_at']
            for field in required_fields:
                if field not in order:
                    return False, f"Order {i} missing field: {field}"
        
        return True, f"Validated {len(orders)} orders"
    
    except Exception as e:
        return False, f"Validation error: {e}"

is_valid, message = validate_orders_file('configs/data/fake_orders/orders.json')
print(f"Validation result: {message}")
```

## Best Practices

### Development Best Practices

1. **Use Meaningful Test Scenarios:**
```python
# ✅ Good: Descriptive test scenarios
test_scenarios = [
    "user_registration_flow",
    "payment_processing_test",
    "order_cancellation_workflow",
    "bulk_order_processing"
]

# ❌ Bad: Generic test scenarios
test_scenarios = ["test1", "test2", "general_test"]
```

2. **Implement Proper Error Handling:**
```python
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError

def create_order_with_retry(order_data, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = requests.post(
                url, 
                json=order_data, 
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
            
        except Timeout:
            print(f"Timeout on attempt {attempt + 1}")
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # Exponential backoff
            
        except ConnectionError:
            print(f"Connection error on attempt {attempt + 1}")
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)
            
        except RequestException as e:
            if e.response and e.response.status_code >= 500:
                # Server error, retry
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)
            else:
                # Client error, don't retry
                raise
```

3. **Clean Up Test Data:**
```python
import atexit

class TestDataManager:
    def __init__(self):
        self.created_orders = []
        atexit.register(self.cleanup)
    
    def create_order(self, order_data):
        response = requests.post(url, json=order_data, headers=headers)
        if response.status_code == 201:
            order_sn = response.json()["order_sn"]
            self.created_orders.append(order_sn)
        return response
    
    def cleanup(self):
        if self.created_orders:
            cleanup_data = {
                "order_sns": self.created_orders,
                "confirm": True
            }
            requests.delete(cleanup_url, json=cleanup_data, headers=headers)
            print(f"Cleaned up {len(self.created_orders)} test orders")

# Usage
test_manager = TestDataManager()
test_manager.create_order(order_data)
# Cleanup happens automatically on exit
```

### Production Best Practices

1. **Monitor System Health:**
```python
import time
import logging

def monitor_fake_order_system():
    while True:
        try:
            response = requests.get(
                "https://your-domain/api/fake-orders/health",
                headers={"X-API-Key": api_key},
                timeout=10
            )
            
            if response.status_code == 200:
                health_data = response.json()
                if health_data["status"] != "healthy":
                    logging.warning(f"System unhealthy: {health_data}")
            else:
                logging.error(f"Health check failed: {response.status_code}")
                
        except Exception as e:
            logging.error(f"Health check error: {e}")
        
        time.sleep(60)  # Check every minute

# Run in background thread
import threading
health_thread = threading.Thread(target=monitor_fake_order_system, daemon=True)
health_thread.start()
```

2. **Implement Circuit Breaker:**
```python
import time
from enum import Enum

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class CircuitBreaker:
    def __init__(self, failure_threshold=5, timeout=60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
    
    def call(self, func, *args, **kwargs):
        if self.state == CircuitState.OPEN:
            if time.time() - self.last_failure_time > self.timeout:
                self.state = CircuitState.HALF_OPEN
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self.on_success()
            return result
        except Exception as e:
            self.on_failure()
            raise
    
    def on_success(self):
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def on_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN

# Usage
circuit_breaker = CircuitBreaker()

def create_order_with_circuit_breaker(order_data):
    return circuit_breaker.call(create_order, order_data)
```

3. **Log Important Operations:**
```python
import logging
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fake_order_operations.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('fake_order_client')

def create_order_with_logging(order_data):
    logger.info(f"Creating order with SKU: {order_data['product_config']['var_sku']}")
    
    try:
        response = requests.post(url, json=order_data, headers=headers)
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"Order created successfully: {result['order_sn']}")
        return result
        
    except Exception as e:
        logger.error(f"Order creation failed: {e}")
        logger.debug(f"Order data: {json.dumps(order_data, indent=2)}")
        raise
```

## Monitoring and Logging

### Setting Up Monitoring

1. **Health Check Monitoring:**
```python
import requests
import time
import smtplib
from email.mime.text import MimeText

class HealthMonitor:
    def __init__(self, api_url, api_key, alert_email=None):
        self.api_url = api_url
        self.headers = {"X-API-Key": api_key}
        self.alert_email = alert_email
        self.last_alert_time = 0
        self.alert_cooldown = 300  # 5 minutes
    
    def check_health(self):
        try:
            response = requests.get(
                f"{self.api_url}/api/fake-orders/health",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                health_data = response.json()
                return health_data["status"] == "healthy", health_data
            else:
                return False, {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return False, {"error": str(e)}
    
    def send_alert(self, message):
        if not self.alert_email:
            return
        
        current_time = time.time()
        if current_time - self.last_alert_time < self.alert_cooldown:
            return  # Skip alert due to cooldown
        
        # Send email alert (implement based on your email setup)
        print(f"ALERT: {message}")
        self.last_alert_time = current_time
    
    def monitor_loop(self):
        while True:
            is_healthy, health_data = self.check_health()
            
            if not is_healthy:
                self.send_alert(f"Fake Order System unhealthy: {health_data}")
            
            time.sleep(60)  # Check every minute

# Usage
monitor = HealthMonitor("https://your-domain", "your-api-key", "<EMAIL>")
monitor.monitor_loop()
```

2. **Performance Metrics:**
```python
import time
import statistics
from collections import deque

class PerformanceMetrics:
    def __init__(self, window_size=100):
        self.response_times = deque(maxlen=window_size)
        self.error_count = 0
        self.total_requests = 0
    
    def record_request(self, response_time, success=True):
        self.response_times.append(response_time)
        self.total_requests += 1
        if not success:
            self.error_count += 1
    
    def get_metrics(self):
        if not self.response_times:
            return {}
        
        return {
            "avg_response_time": statistics.mean(self.response_times),
            "median_response_time": statistics.median(self.response_times),
            "p95_response_time": sorted(self.response_times)[int(len(self.response_times) * 0.95)],
            "error_rate": self.error_count / self.total_requests if self.total_requests > 0 else 0,
            "total_requests": self.total_requests
        }

# Usage
metrics = PerformanceMetrics()

def create_order_with_metrics(order_data):
    start_time = time.time()
    success = False
    
    try:
        response = requests.post(url, json=order_data, headers=headers)
        response.raise_for_status()
        success = True
        return response.json()
    finally:
        response_time = time.time() - start_time
        metrics.record_request(response_time, success)

# Print metrics periodically
print(metrics.get_metrics())
```

### Log Analysis

1. **Parse API Logs:**
```python
import re
from collections import defaultdict
from datetime import datetime

def analyze_api_logs(log_file):
    error_counts = defaultdict(int)
    response_times = []
    endpoints = defaultdict(int)
    
    with open(log_file, 'r') as f:
        for line in f:
            # Parse log line (adjust regex based on your log format)
            match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*"(POST|GET|DELETE) ([^"]+)".*(\d{3}) (\d+)', line)
            
            if match:
                timestamp, method, endpoint, status_code, response_time = match.groups()
                
                endpoints[f"{method} {endpoint}"] += 1
                response_times.append(int(response_time))
                
                if int(status_code) >= 400:
                    error_counts[status_code] += 1
    
    return {
        "total_requests": len(response_times),
        "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
        "error_counts": dict(error_counts),
        "top_endpoints": sorted(endpoints.items(), key=lambda x: x[1], reverse=True)[:10]
    }

# Usage
log_analysis = analyze_api_logs('fake_order_api.log')
print(f"Analysis: {log_analysis}")
```

## Frequently Asked Questions

### Q: How do I know if an order is fake or real?

**A:** Fake orders have several identifying markers:
- Order SN contains "FAKE_" prefix
- `_fake_order_marker` field set to `true`
- `test_scenario` field is present
- Created through fake order endpoints

```python
def is_fake_order(order_data):
    markers = [
        'FAKE_' in order_data.get('order_sn', ''),
        order_data.get('_fake_order_marker') == True,
        'test_scenario' in order_data,
        order_data.get('buyer_username', '').startswith('test_')
    ]
    return any(markers)
```

### Q: Can fake orders interfere with real orders?

**A:** No, fake orders are designed with multiple safeguards:
- Separate storage system
- Security validation prevents mixing
- Clear identification markers
- Isolated processing workflows

### Q: How often should I run cleanup?

**A:** Recommended cleanup schedule:
- **Daily**: Light cleanup (orders > 7 days old)
- **Weekly**: Deep cleanup (orders > 14 days old, templates)
- **Monthly**: Archive cleanup (very old archives)

### Q: What's the maximum number of fake orders I can create?

**A:** Limits depend on your system resources:
- **Single request**: No hard limit, but validate reasonably
- **Batch request**: 50 orders per batch
- **Total storage**: Limited by disk space and memory
- **Rate limits**: 100 requests/minute for standard operations

### Q: How do I integrate with CI/CD pipelines?

**A:** Example CI/CD integration:

```yaml
# .github/workflows/test.yml
name: Test with Fake Orders
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup test environment
        run: |
          # Start fake order system
          docker-compose up -d fake-order-system
          
      - name: Run tests with fake orders
        env:
          FAKE_ORDER_API_KEY: ${{ secrets.FAKE_ORDER_API_KEY }}
        run: |
          python -m pytest tests/test_with_fake_orders.py
          
      - name: Cleanup fake orders
        if: always()
        run: |
          curl -X DELETE "$FAKE_ORDER_URL/api/fake-orders/cleanup?older_than_days=0&confirm=true" \
            -H "X-API-Key: $FAKE_ORDER_API_KEY"
```

### Q: How do I backup fake order data?

**A:** Backup strategies:

```bash
# Manual backup
tar -czf fake_orders_backup_$(date +%Y%m%d).tar.gz configs/data/fake_orders/

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups/fake_orders"
SOURCE_DIR="configs/data/fake_orders"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"
tar -czf "$BACKUP_DIR/fake_orders_$DATE.tar.gz" "$SOURCE_DIR"

# Keep only last 7 days of backups
find "$BACKUP_DIR" -name "fake_orders_*.tar.gz" -mtime +7 -delete
```

### Q: Can I use fake orders in production?

**A:** Fake orders should **never** be used in production environments:
- Use separate staging/testing environments
- Implement environment checks in your code
- Use feature flags to disable fake order creation in production

```python
import os

def create_fake_order(order_data):
    if os.getenv('ENVIRONMENT') == 'production':
        raise Exception("Fake orders not allowed in production")
    
    return create_order(order_data)
```

This troubleshooting guide covers the most common issues and provides practical solutions for maintaining a healthy fake order system. Regular monitoring and following best practices will help prevent most issues from occurring.