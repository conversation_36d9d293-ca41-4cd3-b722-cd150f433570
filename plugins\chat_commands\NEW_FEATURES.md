# Chat Commands Plugin - New Features

This document describes the new features added to the Chat Commands plugin.

## 1. Per-Command Image Sending Order

### Description
You can now configure each command individually to send images before or after text messages.

### Configuration
- **Global Setting**: In the Command Config section, there's a "Send Images First" checkbox that sets the default behavior for all commands.
- **Per-Command Setting**: When creating or editing a command, you can check "Send images before text for this command" to override the global setting for that specific command.

### Example
- `android_help` command is configured to send images first, then text
- Other commands follow the global setting (text first, then images)

### Usage in Interface
1. Go to Chat Commands admin page
2. Click "Add" or "Edit" a command
3. Check the "Send images before text for this command" option
4. Save the command

## 2. Mark Conversation as Unread

### Description
Commands can now automatically mark the conversation as unread after sending a response, ensuring the conversation appears at the top of the chat list.

### Configuration
- **Per-Command Setting**: When creating or editing a command, you can check "Mark conversation as unread after sending response"

### Example
- `ios_help` command is configured to mark conversations as unread after responding
- This helps ensure important help messages don't get missed

### Usage in Interface
1. Go to Chat Commands admin page
2. Click "Add" or "Edit" a command
3. Check the "Mark conversation as unread after sending response" option
4. Save the command

### API Endpoint
The plugin will call the ShopeeAPI endpoint:
```
POST /api/chat/mark-unread
{
  "username": "customer_username"
}
```

## 3. Configurable Message Splitting

### Description
Long messages are now automatically split into multiple messages when they exceed a configurable character limit.

### Configuration
- **Global Setting**: In the Command Config section, set the "Message Split Limit" (default: 600 characters)
- The system intelligently splits messages at sentence boundaries when possible
- If a sentence is too long, it splits at word boundaries
- If a word is too long, it splits at character boundaries

### Example
- A 1252-character message gets split into 3 messages of 576, 599, and 74 characters respectively
- The `test_long` command demonstrates this functionality

### Usage in Interface
1. Go to Chat Commands admin page
2. In the Command Config section, adjust the "Message Split Limit" field
3. Click "Save Command Config"

### Technical Details
- Previous limit was hardcoded at 500 characters
- New configurable limit defaults to 600 characters
- Splitting algorithm preserves message readability by respecting sentence and word boundaries

## 4. Updated Data Models

### ChatCommand Model
New fields added:
- `send_images_first: bool = False` - Whether to send images before text for this command
- `mark_as_unread: bool = False` - Whether to mark conversation as unread after response

### CommandConfig Model
New field added:
- `message_split_limit: int = 600` - Character limit for auto-splitting messages

### CommandResponse Model
New field added:
- `mark_as_unread: bool = False` - Whether to mark conversation as unread

## 5. Backward Compatibility

- All existing commands will have the new fields set to `False` by default
- Existing configurations will continue to work without modification
- The global "Send Images First" setting remains available for setting default behavior

## 6. Testing

The implementation includes comprehensive testing for:
- Message splitting with various text lengths
- Command-specific configuration loading
- Response generation with new features
- Configuration persistence

## 7. Usage Examples

### Example 1: Help Command with Images First
```json
{
  "command": "android_help",
  "description": "Provide Android help instructions",
  "response_text": "Help text here...",
  "image_urls": ["https://example.com/image.jpg"],
  "send_images_first": true,
  "mark_as_unread": false
}
```

### Example 2: Important Command with Unread Marking
```json
{
  "command": "ios_help", 
  "description": "Provide iOS help instructions",
  "response_text": "Important help text...",
  "image_urls": ["https://example.com/image.jpg"],
  "send_images_first": false,
  "mark_as_unread": true
}
```

### Example 3: Long Message Command
```json
{
  "command": "test_long",
  "description": "Test message splitting",
  "response_text": "Very long text that will be automatically split...",
  "send_images_first": false,
  "mark_as_unread": false
}
```

## 8. Configuration Files

The new features are stored in:
- `configs/plugins/chat_commands/commands.json` - Individual command configurations
- `configs/plugins/chat_commands/config.json` - Global plugin configuration

These files are automatically managed by the plugin and persist across Docker container updates.
