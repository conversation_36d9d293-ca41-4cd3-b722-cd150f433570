"""
Tests for the chat functionality of the ShopeeAPI.
"""
import unittest
import requests
import json
import sys
import os
import time
from unittest.mock import patch, MagicMock

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from ShopeeAPI.client import ShopeeAPI
except ImportError:
    from client import ShopeeAPI


class TestChatAPI(unittest.TestCase):
    """Test cases for the chat API endpoints."""
    
    API_URL = "http://localhost:8000"
    
    def setUp(self):
        """Set up test environment."""
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        # Default test order number
        self.test_order_sn = "2412064GY1714Y"
    
    def test_send_order_message_endpoint(self):
        """Test the send_order_message endpoint."""
        # Skip this test if the API server is not running
        try:
            requests.get(f"{self.API_URL}/status", timeout=1)
        except requests.RequestException:
            self.skipTest("API server is not running")
            
        url = f"{self.API_URL}/chat/send_order_message"
        payload = {
            "order_sn": self.test_order_sn
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            
            # Print response for debugging
            print(f"Response status code: {response.status_code}")
            try:
                response_json = response.json()
                print(f"Response JSON: {json.dumps(response_json, indent=2)}")
            except json.JSONDecodeError:
                print(f"Response text (not JSON): {response.text}")
                
            # Check if the request was successful
            self.assertIn(response.status_code, [200, 201, 202])
        except Exception as e:
            self.fail(f"Exception during test: {str(e)}")


class TestChatClient(unittest.TestCase):
    """Test cases for the chat functionality in the ShopeeAPI client."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock ShopeeAPI instance
        self.api = MagicMock(spec=ShopeeAPI)

        # Default test order number
        self.test_order_sn = "2412064GY1714Y"

    @patch('ShopeeAPI.client.ShopeeAPI')
    def test_send_order_message_client(self, mock_api):
        """Test sending an order message directly using the ShopeeAPI client."""
        # Configure the mock
        mock_api.return_value.send_order_message.return_value = ({"success": True}, 200)

        # Create an instance of the mocked API
        api = mock_api()

        # Call the method
        response, status_code = api.send_order_message(self.test_order_sn)

        # Assertions
        self.assertEqual(status_code, 200)
        self.assertTrue(response.get("success"))

        # Verify the method was called with the correct parameters
        api.send_order_message.assert_called_once_with(self.test_order_sn)


class TestMessageSplitting(unittest.TestCase):
    """Test cases for the message splitting functionality."""

    def test_split_message_short(self):
        """Test that short messages are not split."""
        from services.chat import ChatService

        # Create a mock chat service
        chat_service = ChatService(None, None)

        short_message = "Hello, this is a short message."
        parts = chat_service._split_message(short_message)

        self.assertEqual(len(parts), 1)
        self.assertEqual(parts[0], short_message)

    def test_split_message_long(self):
        """Test that long messages are split correctly."""
        from services.chat import ChatService

        # Create a mock chat service
        chat_service = ChatService(None, None)

        # Create a message longer than 500 characters
        long_message = "This is a very long message that should be split into multiple parts. " * 10
        parts = chat_service._split_message(long_message)

        # Should be split into multiple parts
        self.assertGreater(len(parts), 1)

        # Each part should be within the limit
        for part in parts:
            self.assertLessEqual(len(part), 500)

        # Total content should be preserved (approximately)
        reconstructed = " ".join(parts)
        original_words = long_message.split()
        reconstructed_words = reconstructed.split()

        # Allow for some variation due to trimming
        self.assertGreaterEqual(len(reconstructed_words), len(original_words) - 5)

    def test_split_message_exactly_500(self):
        """Test message exactly at the 500 character limit."""
        from services.chat import ChatService

        # Create a mock chat service
        chat_service = ChatService(None, None)

        # Create a message exactly 500 characters
        message_500 = "A" * 500
        parts = chat_service._split_message(message_500)

        self.assertEqual(len(parts), 1)
        self.assertEqual(len(parts[0]), 500)

    def test_split_message_just_over_500(self):
        """Test message just over the 500 character limit."""
        from services.chat import ChatService

        # Create a mock chat service
        chat_service = ChatService(None, None)

        # Create a message just over 500 characters
        message_501 = "A" * 501
        parts = chat_service._split_message(message_501)

        self.assertEqual(len(parts), 2)
        self.assertEqual(len(parts[0]), 500)
        self.assertEqual(len(parts[1]), 1)


if __name__ == "__main__":
    unittest.main()
