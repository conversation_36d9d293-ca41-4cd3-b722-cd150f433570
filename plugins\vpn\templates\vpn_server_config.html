{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Server Configuration - {{ config.server_name if config else 'Unknown' }}</h2>
                <div>
                    <a href="{{ url_for('vpn.edit_server_configuration', server_id=server_id) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <button class="btn btn-info" onclick="validateConfig()">
                        <i class="fas fa-check"></i> Validate
                    </button>
                    <button class="btn btn-warning" onclick="backupConfig()">
                        <i class="fas fa-save"></i> Backup
                    </button>
                    <a href="{{ url_for('vpn.configurations') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    {% if config %}
    <!-- Configuration Info -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Configuration Information</h3>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">Server ID:</dt>
                        <dd class="col-sm-9">{{ config.server_id }}</dd>
                        
                        <dt class="col-sm-3">Server Name:</dt>
                        <dd class="col-sm-9">{{ config.server_name }}</dd>
                        
                        <dt class="col-sm-3">Clients Count:</dt>
                        <dd class="col-sm-9">{{ config.clients_count }}</dd>
                        
                        <dt class="col-sm-3">Last Updated:</dt>
                        <dd class="col-sm-9">{{ config.last_updated or 'Never' }}</dd>
                        
                        <dt class="col-sm-3">Configuration Valid:</dt>
                        <dd class="col-sm-9">
                            {% if config.is_valid %}
                            <span class="badge badge-success">Valid</span>
                            {% else %}
                            <span class="badge badge-danger">Invalid</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Validation Results -->
    {% if validation %}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card {{ 'border-success' if validation.is_valid else 'border-danger' }}">
                <div class="card-header {{ 'bg-success text-white' if validation.is_valid else 'bg-danger text-white' }}">
                    <h3 class="card-title">Validation Results</h3>
                </div>
                <div class="card-body">
                    {% if validation.validation_errors %}
                    <h5>Errors:</h5>
                    <ul class="list-unstyled">
                        {% for error in validation.validation_errors %}
                        <li><i class="fas fa-times text-danger"></i> {{ error }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                    
                    {% if validation.warnings %}
                    <h5>Warnings:</h5>
                    <ul class="list-unstyled">
                        {% for warning in validation.warnings %}
                        <li><i class="fas fa-exclamation-triangle text-warning"></i> {{ warning }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                    
                    <dl class="row mt-3">
                        <dt class="col-sm-3">Inbounds Count:</dt>
                        <dd class="col-sm-9">{{ validation.inbounds_count }}</dd>
                        
                        <dt class="col-sm-3">Clients Count:</dt>
                        <dd class="col-sm-9">{{ validation.clients_count }}</dd>
                        
                        <dt class="col-sm-3">Validated At:</dt>
                        <dd class="col-sm-9">{{ validation.validated_at }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Configuration JSON -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Configuration JSON</h3>
                    <div class="card-tools">
                        <button class="btn btn-sm btn-secondary" onclick="copyConfig()">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <button class="btn btn-sm btn-info" onclick="downloadConfig()">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <pre id="config-json" class="language-json"><code>{{ config.config | tojson(indent=2) }}</code></pre>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> Configuration not found or could not be loaded.
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-json.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">

<script>
function validateConfig() {
    toastr.info('Validating configuration...');
    $.get(`/admin/vpn/api/config/validate/server/{{ server_id }}`, function(data) {
        if (data.is_valid) {
            toastr.success('Configuration is valid!');
        } else {
            var errors = data.validation_errors.join('\n');
            toastr.error('Configuration validation failed:\n' + errors);
        }
        // Reload to show validation results
        setTimeout(() => location.reload(), 2000);
    }).fail(function() {
        toastr.error('Failed to validate configuration');
    });
}

function backupConfig() {
    toastr.info('Creating backup...');
    $.post('/api/v1/config/backup', {
        server_ids: [{{ server_id }}],
        description: 'Manual backup from configuration view'
    }, function(data) {
        if (data.success) {
            toastr.success('Backup created successfully!');
        } else {
            toastr.error('Backup failed: ' + data.message);
        }
    }).fail(function() {
        toastr.error('Failed to create backup');
    });
}

function copyConfig() {
    var configText = document.getElementById('config-json').innerText;
    navigator.clipboard.writeText(configText).then(function() {
        toastr.success('Configuration copied to clipboard!');
    }, function() {
        toastr.error('Failed to copy configuration');
    });
}

function downloadConfig() {
    var configText = document.getElementById('config-json').innerText;
    var blob = new Blob([configText], { type: 'application/json' });
    var url = window.URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = 'server-{{ server_id }}-config.json';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    toastr.success('Configuration downloaded!');
}
</script>
{% endblock %}