"""
Services for managing Shopee products.
"""
from typing import Dict, Any, List, Optional
import logging
import time

from core.session import ShopeeSession
from core.config import ShopeeConfig
from core.exceptions import RequestError, ResourceNotFoundError

logger = logging.getLogger(__name__)


class ProductService:
    """
    Service for interacting with Shopee product APIs.
    """

    def __init__(self, session: ShopeeSession, config: ShopeeConfig):
        """
        Initialize with session and config.

        Args:
            session: ShopeeSession instance
            config: ShopeeConfig instance
        """
        self.session = session
        self.config = config

    def get_all_products(self) -> List[Dict[str, Any]]:
        """
        Fetch all products from Shopee API.

        Returns:
            List of all products

        Raises:
            RequestError: If API request fails
        """
        all_products = []
        page_number = 1
        page_size = 10  # Try smaller page size to avoid limit error

        try:
            while True:
                # Build API URL and parameters (GET request like the working example)
                url = self.config.urls.get("product_list", "https://seller.shopee.com.my/api/v3/mpsku/list/v2/get_product_list")

                # Use GET with query parameters exactly like the working example
                params = {
                    **self.session.get_common_params(),
                    "page_number": page_number,
                    "page_size": page_size,
                    "list_type": "all",
                    "need_ads": "true"  # Keep as string to match working request
                }

                response = self.session.get(url, params=params)

                # Check for 403 Forbidden specifically
                if response.status_code == 403:
                    logger.error(f"403 Forbidden when fetching products. This usually indicates authentication issues.")
                    logger.error(f"Response: {response.text[:500]}")
                    raise RequestError("Authentication failed for product API")

                response.raise_for_status()

                data = response.json()

                if data.get("code") != 0:
                    logger.error(f"API error: {data.get('message', 'Unknown error')}")
                    raise RequestError(f"API error: {data.get('message', 'Unknown error')}")

                products = data.get("data", {}).get("products", [])
                if not products:
                    break

                all_products.extend(products)

                # Check if we've reached the last page
                page_info = data.get("data", {}).get("page_info", {})
                total_pages = (page_info.get("total", 0) + page_size - 1) // page_size

                if page_number >= total_pages:
                    break

                page_number += 1

        except Exception as e:
            logger.error(f"Error fetching products: {e}")
            raise RequestError(f"Error fetching products: {e}")

        return all_products

    def get_boostable_products(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Get all products that can be boosted.
        
        Args:
            filters: Optional filters to apply to products
            
        Returns:
            List of boostable products
        """
        try:
            # Get all products from Shopee API
            all_products = self.get_all_products()
            
            # Filter products that can be boosted
            boostable_products = self._filter_boostable_products(all_products, filters or {})
            
            logger.info(f"Found {len(boostable_products)} boostable products out of {len(all_products)} total")
            return boostable_products

        except Exception as e:
            logger.error(f"Error getting boostable products: {e}")
            raise RequestError(f"Error getting boostable products: {e}")

    def _filter_boostable_products(self, products: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter products that can be boosted.
        
        Args:
            products: List of all products
            filters: Filtering criteria
            
        Returns:
            List of filtered boostable products
        """
        boostable = []
        
        for product in products:
            # Check if product is active
            if filters.get("exclude_inactive", True) and product.get("status") != 1:
                continue
                
            # Check if product is not unlisted
            if filters.get("exclude_unlisted", True) and product.get("tag", {}).get("unlist", False):
                continue
                
            # Check minimum stock
            min_stock = filters.get("min_stock", 1)
            stock_detail = product.get("stock_detail", {})
            total_stock = stock_detail.get("total_available_stock", 0)
            if total_stock < min_stock:
                continue
                
            boostable.append(product)
            
        return boostable

    def get_product_summary(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a summary of product information.
        
        Args:
            product: Product data from API
            
        Returns:
            Summarized product information
        """
        return {
            "id": product.get("id"),
            "name": product.get("name", "")[:50] + "..." if len(product.get("name", "")) > 50 else product.get("name", ""),
            "status": product.get("status"),
            "stock": product.get("stock_detail", {}).get("total_available_stock", 0),
            "sold_count": product.get("statistics", {}).get("sold_count", 0),
            "boost_status": product.get("boost_info", {}).get("boost_entry_status"),
            "price_min": product.get("price_detail", {}).get("price_min", "0"),
            "price_max": product.get("price_detail", {}).get("price_max", "0")
        }

    def boost_product(self, product_id: int) -> bool:
        """
        Boost a single product.
        
        Args:
            product_id: ID of the product to boost
            
        Returns:
            True if boost was successful, False otherwise
            
        Raises:
            RequestError: If API request fails
        """
        try:
            # Build boost API URL and parameters
            url = self.config.urls.get("boost_product", "https://seller.shopee.com.my/api/v3/product/boost_product/")
            params = {
                "version": "3.1.0",
                **self.session.get_common_params()
            }
            
            # Payload for boost request
            payload = {"id": product_id}
            
            # Make the boost request
            response = self.session.post(url, params=params, json=payload)

            # Check for 403 Forbidden specifically
            if response.status_code == 403:
                logger.error(f"403 Forbidden when boosting product {product_id}. This usually indicates authentication issues.")
                logger.error(f"Response: {response.text[:500]}")
                raise RequestError("Authentication failed for boost API")

            response.raise_for_status()
            
            data = response.json()
            
            # Check if boost was successful
            if data.get("code") == 0 and data.get("message") == "success":
                logger.info(f"Successfully boosted product {product_id}")
                return True
            else:
                logger.warning(f"Boost API returned error for product {product_id}: {data}")
                return False
                
        except Exception as e:
            logger.error(f"Error boosting product {product_id}: {e}")
            raise RequestError(f"Error boosting product {product_id}: {e}")

    def boost_multiple_products(self, product_ids: List[int]) -> Dict[str, Any]:
        """
        Boost multiple products.
        
        Args:
            product_ids: List of product IDs to boost
            
        Returns:
            Dictionary with success and failure results
        """
        results = {
            "success": [],
            "failed": [],
            "total_attempted": len(product_ids)
        }

        for product_id in product_ids:
            try:
                success = self.boost_product(product_id)
                if success:
                    results["success"].append({
                        "id": product_id,
                        "boosted_at": time.time()
                    })
                else:
                    results["failed"].append({
                        "id": product_id,
                        "error": "Boost API returned failure"
                    })
                    
            except Exception as e:
                results["failed"].append({
                    "id": product_id,
                    "error": str(e)
                })

        return results
