"""
Helper functions for the Shopee API.
"""
import os
import json
import time
import uuid
import requests
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from PIL import Image
from io import BytesIO


def generate_request_id() -> str:
    """
    Generate a unique request ID.
    
    Returns:
        Unique request ID string
    """
    return str(uuid.uuid4())


def timestamp_to_datetime(timestamp: int) -> datetime:
    """
    Convert a Unix timestamp to a datetime object.
    
    Args:
        timestamp: Unix timestamp in seconds
        
    Returns:
        Datetime object
    """
    return datetime.fromtimestamp(timestamp)


def datetime_to_timestamp(dt: datetime) -> int:
    """
    Convert a datetime object to a Unix timestamp.
    
    Args:
        dt: Datetime object
        
    Returns:
        Unix timestamp in seconds
    """
    return int(dt.timestamp())


def load_json_file(file_path: str) -> Dict[str, Any]:
    """
    Load a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        Dictionary with the JSON data

    Raises:
        FileNotFoundError: If the file doesn't exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def save_json_file(file_path: str, data: Dict[str, Any]) -> None:
    """
    Save data to a JSON file.

    Args:
        file_path: Path to save the JSON file
        data: Dictionary to save

    Raises:
        IOError: If the file can't be written
    """
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def get_file_modification_time(file_path: str) -> Optional[float]:
    """
    Get the last modification time of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Modification time as a float, or None if the file doesn't exist
    """
    if os.path.exists(file_path):
        return os.path.getmtime(file_path)
    return None


def rate_limit(last_request_time: float, request_interval: float) -> float:
    """
    Apply rate limiting between requests.

    Args:
        last_request_time: Time of the last request
        request_interval: Minimum interval between requests

    Returns:
        Current time after rate limiting
    """
    if request_interval > 0:
        current_time = time.time()
        elapsed = current_time - last_request_time
        if elapsed < request_interval:
            time.sleep(request_interval - elapsed)
    return time.time()


def get_image_dimensions(image_url: str, timeout: int = 10) -> Tuple[Optional[int], Optional[int]]:
    """
    Get the dimensions of an image from a URL.

    Args:
        image_url: URL of the image
        timeout: Request timeout in seconds

    Returns:
        Tuple of (width, height) or (None, None) if failed
    """
    try:
        # Send a HEAD request first to check if the URL is accessible
        head_response = requests.head(image_url, timeout=timeout, allow_redirects=True)

        # If HEAD request fails, try GET request
        if head_response.status_code != 200:
            print(f"HEAD request failed for {image_url}, trying GET request")

        # Download the image with a reasonable size limit (10MB)
        response = requests.get(
            image_url,
            timeout=timeout,
            stream=True,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        response.raise_for_status()

        # Read only the first chunk to get image dimensions
        # Most image formats store dimension info in the header
        image_data = BytesIO()
        downloaded_size = 0
        max_size = 10 * 1024 * 1024  # 10MB limit

        for chunk in response.iter_content(chunk_size=8192):
            if downloaded_size + len(chunk) > max_size:
                print(f"Image too large, stopping download at {downloaded_size} bytes")
                break
            image_data.write(chunk)
            downloaded_size += len(chunk)

            # Try to get dimensions early if we have enough data
            if downloaded_size > 1024:  # After 1KB, try to get dimensions
                try:
                    image_data.seek(0)
                    with Image.open(image_data) as img:
                        width, height = img.size
                        print(f"Successfully got image dimensions: {width}x{height} from {image_url}")
                        return width, height
                except Exception:
                    # Not enough data yet, continue downloading
                    image_data.seek(0, 2)  # Seek to end
                    continue

        # Final attempt with all downloaded data
        image_data.seek(0)
        with Image.open(image_data) as img:
            width, height = img.size
            print(f"Successfully got image dimensions: {width}x{height} from {image_url}")
            return width, height

    except requests.exceptions.RequestException as e:
        print(f"Network error getting image dimensions from {image_url}: {e}")
        return None, None
    except Exception as e:
        print(f"Error getting image dimensions from {image_url}: {e}")
        return None, None


def calculate_thumbnail_dimensions(original_width: int, original_height: int, max_width: int = 500, max_height: int = 500) -> Tuple[int, int]:
    """
    Calculate thumbnail dimensions while maintaining aspect ratio.

    Args:
        original_width: Original image width
        original_height: Original image height
        max_width: Maximum thumbnail width
        max_height: Maximum thumbnail height

    Returns:
        Tuple of (thumbnail_width, thumbnail_height)
    """
    if original_width <= 0 or original_height <= 0:
        return max_width, max_height

    # Calculate aspect ratio
    aspect_ratio = original_width / original_height

    # Calculate thumbnail dimensions
    if aspect_ratio > 1:  # Landscape
        thumb_width = min(max_width, original_width)
        thumb_height = int(thumb_width / aspect_ratio)
        if thumb_height > max_height:
            thumb_height = max_height
            thumb_width = int(thumb_height * aspect_ratio)
    else:  # Portrait or square
        thumb_height = min(max_height, original_height)
        thumb_width = int(thumb_height * aspect_ratio)
        if thumb_width > max_width:
            thumb_width = max_width
            thumb_height = int(thumb_width / aspect_ratio)

    return max(1, thumb_width), max(1, thumb_height)
