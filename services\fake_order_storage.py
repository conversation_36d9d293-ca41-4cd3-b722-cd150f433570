"""
Fake Order Storage System

This module provides comprehensive storage and persistence for fake orders with
efficient indexing, retrieval methods, and data management capabilities.
"""

import json
import os
import threading
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Any, Optional, Set, Tuple
from pathlib import Path
import logging
from collections import defaultdict
import hashlib
from utils.fake_order_security import fake_order_security

logger = logging.getLogger(__name__)

@dataclass
class FakeOrder:
    """Complete fake order data model with all order information"""
    order_sn: str
    var_sku: str
    order_data: Dict[str, Any]
    created_at: datetime
    test_scenario: str
    is_processed: bool = False
    processing_results: Dict[str, Any] = field(default_factory=dict)
    
    # Additional metadata fields
    buyer_username: str = "test_user"
    buyer_name: str = "Test Customer"
    buyer_phone: str = "+60123456789"
    buyer_email: str = "<EMAIL>"
    status: str = "To Ship"
    payment_status: str = "paid"
    quantity: int = 1
    total_price: float = 0.0
    
    # Storage and tracking metadata
    storage_version: str = "1.0"
    last_accessed: Optional[datetime] = None
    access_count: int = 0
    tags: List[str] = field(default_factory=list)
    custom_metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Processing tracking
    processing_history: List[Dict[str, Any]] = field(default_factory=list)
    error_history: List[Dict[str, Any]] = field(default_factory=list)
    
    def __post_init__(self):
        """Post-initialization processing"""
        if isinstance(self.created_at, str):
            self.created_at = datetime.fromisoformat(self.created_at)
        if self.last_accessed and isinstance(self.last_accessed, str):
            self.last_accessed = datetime.fromisoformat(self.last_accessed)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        data['created_at'] = self.created_at.isoformat()
        if self.last_accessed:
            data['last_accessed'] = self.last_accessed.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FakeOrder':
        """Create FakeOrder from dictionary"""
        # Handle datetime conversion
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'last_accessed' in data and isinstance(data['last_accessed'], str):
            data['last_accessed'] = datetime.fromisoformat(data['last_accessed'])
        
        return cls(**data)
    
    def update_access_tracking(self):
        """Update access tracking information"""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def add_processing_result(self, result: Dict[str, Any]):
        """Add processing result to history"""
        self.processing_results = result
        self.processing_history.append({
            'timestamp': datetime.now().isoformat(),
            'result': result,
            'success': result.get('success', False)
        })
        if result.get('success'):
            self.is_processed = True
    
    def add_error(self, error: str, context: Optional[Dict[str, Any]] = None):
        """Add error to error history"""
        self.error_history.append({
            'timestamp': datetime.now().isoformat(),
            'error': error,
            'context': context or {}
        })
    
    def get_age_days(self) -> int:
        """Get age of order in days"""
        return (datetime.now() - self.created_at).days
    
    def is_stale(self, days: int = 7) -> bool:
        """Check if order is stale (older than specified days)"""
        return self.get_age_days() > days

@dataclass
class FakeOrderQuery:
    """Query parameters for fake order retrieval"""
    order_sn: Optional[str] = None
    var_sku: Optional[str] = None
    buyer_username: Optional[str] = None
    status: Optional[str] = None
    payment_status: Optional[str] = None
    test_scenario: Optional[str] = None
    is_processed: Optional[bool] = None
    tags: Optional[List[str]] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    limit: Optional[int] = None
    offset: int = 0
    sort_by: str = "created_at"
    sort_order: str = "desc"  # "asc" or "desc"

class FakeOrderIndex:
    """Indexing system for efficient fake order retrieval"""
    
    def __init__(self):
        self.order_sn_index: Dict[str, str] = {}  # order_sn -> file_key
        self.sku_index: Dict[str, Set[str]] = defaultdict(set)  # var_sku -> set of file_keys
        self.buyer_index: Dict[str, Set[str]] = defaultdict(set)  # buyer_username -> set of file_keys
        self.status_index: Dict[str, Set[str]] = defaultdict(set)  # status -> set of file_keys
        self.scenario_index: Dict[str, Set[str]] = defaultdict(set)  # test_scenario -> set of file_keys
        self.tag_index: Dict[str, Set[str]] = defaultdict(set)  # tag -> set of file_keys
        self.date_index: List[Tuple[datetime, str]] = []  # (created_at, file_key) sorted by date
        self._lock = threading.RLock()
    
    def add_order(self, order: FakeOrder, file_key: str):
        """Add order to all relevant indexes"""
        with self._lock:
            self.order_sn_index[order.order_sn] = file_key
            self.sku_index[order.var_sku].add(file_key)
            self.buyer_index[order.buyer_username].add(file_key)
            self.status_index[order.status].add(file_key)
            self.scenario_index[order.test_scenario].add(file_key)
            
            for tag in order.tags:
                self.tag_index[tag].add(file_key)
            
            # Insert into date index maintaining sort order
            self._insert_date_index(order.created_at, file_key)
    
    def remove_order(self, order: FakeOrder, file_key: str):
        """Remove order from all indexes"""
        with self._lock:
            self.order_sn_index.pop(order.order_sn, None)
            self.sku_index[order.var_sku].discard(file_key)
            self.buyer_index[order.buyer_username].discard(file_key)
            self.status_index[order.status].discard(file_key)
            self.scenario_index[order.test_scenario].discard(file_key)
            
            for tag in order.tags:
                self.tag_index[tag].discard(file_key)
            
            # Remove from date index
            self.date_index = [(date, key) for date, key in self.date_index if key != file_key]
    
    def _insert_date_index(self, created_at: datetime, file_key: str):
        """Insert into date index maintaining sort order"""
        # Simple insertion sort for now - could optimize with bisect for large datasets
        inserted = False
        for i, (date, key) in enumerate(self.date_index):
            if created_at > date:  # Newer dates first
                self.date_index.insert(i, (created_at, file_key))
                inserted = True
                break
        
        if not inserted:
            self.date_index.append((created_at, file_key))
    
    def find_by_order_sn(self, order_sn: str) -> Optional[str]:
        """Find file key by order SN"""
        return self.order_sn_index.get(order_sn)
    
    def find_by_criteria(self, query: FakeOrderQuery) -> Set[str]:
        """Find file keys matching query criteria"""
        with self._lock:
            result_sets = []
            
            if query.var_sku:
                result_sets.append(self.sku_index[query.var_sku])
            
            if query.buyer_username:
                result_sets.append(self.buyer_index[query.buyer_username])
            
            if query.status:
                result_sets.append(self.status_index[query.status])
            
            if query.test_scenario:
                result_sets.append(self.scenario_index[query.test_scenario])
            
            if query.tags:
                for tag in query.tags:
                    result_sets.append(self.tag_index[tag])
            
            # Intersect all result sets
            if result_sets:
                result = result_sets[0].copy()
                for result_set in result_sets[1:]:
                    result &= result_set
            else:
                # No specific criteria, return all
                result = set(key for key in self.order_sn_index.values())
            
            return result
    
    def get_stats(self) -> Dict[str, Any]:
        """Get index statistics"""
        with self._lock:
            return {
                'total_orders': len(self.order_sn_index),
                'unique_skus': len(self.sku_index),
                'unique_buyers': len(self.buyer_index),
                'unique_statuses': len(self.status_index),
                'unique_scenarios': len(self.scenario_index),
                'unique_tags': len(self.tag_index),
                'date_range': {
                    'oldest': self.date_index[-1][0].isoformat() if self.date_index else None,
                    'newest': self.date_index[0][0].isoformat() if self.date_index else None
                }
            }

class FakeOrderStorage:
    """Comprehensive storage system for fake orders"""
    
    def __init__(self, storage_dir: str = 'configs/data/fake_orders'):
        self.storage_dir = Path(storage_dir)
        self.orders_file = self.storage_dir / 'orders.json'
        self.index_file = self.storage_dir / 'index.json'
        self.metadata_file = self.storage_dir / 'metadata.json'
        
        self.index = FakeOrderIndex()
        self._lock = threading.RLock()
        self._cache: Dict[str, FakeOrder] = {}
        self._cache_max_size = 1000
        
        self._ensure_storage_structure()
        self._load_index()
        self._load_metadata()
    
    def _ensure_storage_structure(self):
        """Ensure storage directory and files exist"""
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        if not self.orders_file.exists():
            with open(self.orders_file, 'w', encoding='utf-8') as f:
                json.dump([], f, indent=2)
        
        if not self.metadata_file.exists():
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'created_at': datetime.now().isoformat(),
                    'version': '1.0',
                    'total_orders': 0,
                    'last_cleanup': None
                }, f, indent=2)
    
    def _load_index(self):
        """Load index from file or rebuild if necessary"""
        try:
            if self.index_file.exists():
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                self._rebuild_index_from_data(index_data)
            else:
                self._rebuild_index()
        except Exception as e:
            logger.warning(f"Failed to load index, rebuilding: {e}")
            self._rebuild_index()
    
    def _rebuild_index(self):
        """Rebuild index from stored orders"""
        logger.info("Rebuilding fake order index...")
        self.index = FakeOrderIndex()
        
        try:
            with open(self.orders_file, 'r', encoding='utf-8') as f:
                orders_data = json.load(f)
            
            for i, order_data in enumerate(orders_data):
                try:
                    order = FakeOrder.from_dict(order_data)
                    file_key = f"order_{i}"
                    self.index.add_order(order, file_key)
                except Exception as e:
                    logger.error(f"Failed to index order {i}: {e}")
            
            self._save_index()
            logger.info(f"Index rebuilt with {len(orders_data)} orders")
            
        except Exception as e:
            logger.error(f"Failed to rebuild index: {e}")
    
    def _rebuild_index_from_data(self, index_data: Dict[str, Any]):
        """Rebuild index from saved index data"""
        # This is a simplified version - in production, you'd want more robust deserialization
        self.index = FakeOrderIndex()
        # For now, just rebuild from orders file
        self._rebuild_index()
    
    def _save_index(self):
        """Save index to file"""
        try:
            index_data = {
                'order_sn_index': self.index.order_sn_index,
                'stats': self.index.get_stats(),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save index: {e}")
    
    def _load_metadata(self):
        """Load storage metadata"""
        try:
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
        except Exception as e:
            logger.error(f"Failed to load metadata: {e}")
            self.metadata = {
                'created_at': datetime.now().isoformat(),
                'version': '1.0',
                'total_orders': 0,
                'last_cleanup': None
            }
    
    def _save_metadata(self):
        """Save storage metadata"""
        try:
            self.metadata['last_updated'] = datetime.now().isoformat()
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
    
    def _generate_file_key(self, order: FakeOrder) -> str:
        """Generate unique file key for order"""
        # Use hash of order_sn and timestamp for uniqueness
        key_data = f"{order.order_sn}_{order.created_at.isoformat()}"
        return hashlib.md5(key_data.encode()).hexdigest()[:16]
    
    def store_order(self, order: FakeOrder) -> bool:
        """Store a fake order with indexing and security validation"""
        with self._lock:
            try:
                # Security validation - ensure order is properly marked as fake
                order_dict = order.to_dict()
                order_dict = fake_order_security.add_fake_order_markers(order_dict)
                
                # Validate fake order safety
                safety_validation = fake_order_security.validate_fake_order_safety(order_dict)
                if not safety_validation['is_safe']:
                    logger.error(f"Fake order safety validation failed for {order.order_sn}: {safety_validation['errors']}")
                    fake_order_security.log_fake_order_operation(
                        'store_order_failed_safety_validation',
                        order_dict,
                        {'validation_errors': safety_validation['errors']}
                    )
                    return False
                
                # Log security warnings if any
                if safety_validation['warnings']:
                    logger.warning(f"Fake order safety warnings for {order.order_sn}: {safety_validation['warnings']}")
                
                # Load existing orders
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    orders_data = json.load(f)
                
                # Check for duplicates
                if any(o.get('order_sn') == order.order_sn for o in orders_data):
                    logger.warning(f"{fake_order_security.get_log_prefix(order.order_sn)} Order {order.order_sn} already exists")
                    return False
                
                # Add new order with security markers
                orders_data.append(order_dict)
                
                # Save updated orders
                with open(self.orders_file, 'w', encoding='utf-8') as f:
                    json.dump(orders_data, f, indent=2, ensure_ascii=False)
                
                # Update index
                file_key = f"order_{len(orders_data) - 1}"
                self.index.add_order(order, file_key)
                
                # Update cache
                self._cache[order.order_sn] = order
                self._manage_cache_size()
                
                # Update metadata
                self.metadata['total_orders'] = len(orders_data)
                self._save_metadata()
                self._save_index()
                
                # Log successful storage with security context
                fake_order_security.log_fake_order_operation(
                    'store_order_success',
                    order_dict,
                    {
                        'safety_validation': safety_validation,
                        'storage_location': str(self.orders_file)
                    }
                )
                
                logger.info(f"{fake_order_security.get_log_prefix(order.order_sn)} Stored fake order: {order.order_sn}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to store order {order.order_sn}: {e}")
                fake_order_security.log_fake_order_operation(
                    'store_order_error',
                    {'order_sn': order.order_sn},
                    {'error': str(e)}
                )
                return False
    
    def get_order(self, order_sn: str) -> Optional[FakeOrder]:
        """Retrieve a fake order by order SN with security logging"""
        # Check cache first
        if order_sn in self._cache:
            order = self._cache[order_sn]
            order.update_access_tracking()
            
            # Log access for security audit
            fake_order_security.log_fake_order_operation(
                'access_order_from_cache',
                {'order_sn': order_sn},
                {'access_count': order.access_count}
            )
            
            return order
        
        # Find in index
        file_key = self.index.find_by_order_sn(order_sn)
        if not file_key:
            # Log failed access attempt
            if fake_order_security.is_fake_order(order_sn):
                fake_order_security.log_fake_order_operation(
                    'access_order_not_found',
                    {'order_sn': order_sn},
                    {'search_location': 'index'}
                )
            return None
        
        # Load from file
        try:
            with open(self.orders_file, 'r', encoding='utf-8') as f:
                orders_data = json.load(f)
            
            # Extract index from file_key
            order_index = int(file_key.split('_')[1])
            if 0 <= order_index < len(orders_data):
                order = FakeOrder.from_dict(orders_data[order_index])
                order.update_access_tracking()
                
                # Add to cache
                self._cache[order_sn] = order
                self._manage_cache_size()
                
                # Log successful access
                fake_order_security.log_fake_order_operation(
                    'access_order_from_storage',
                    {'order_sn': order_sn},
                    {
                        'access_count': order.access_count,
                        'storage_location': str(self.orders_file),
                        'file_key': file_key
                    }
                )
                
                return order
            
        except Exception as e:
            logger.error(f"{fake_order_security.get_log_prefix(order_sn)} Failed to retrieve order {order_sn}: {e}")
            fake_order_security.log_fake_order_operation(
                'access_order_error',
                {'order_sn': order_sn},
                {'error': str(e)}
            )
        
        return None
    
    def query_orders(self, query: FakeOrderQuery) -> List[FakeOrder]:
        """Query orders with filtering and sorting"""
        with self._lock:
            try:
                # Handle single order lookup
                if query.order_sn:
                    order = self.get_order(query.order_sn)
                    return [order] if order else []
                
                # Find matching file keys
                matching_keys = self.index.find_by_criteria(query)
                
                # Load orders from file
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    orders_data = json.load(f)
                
                orders = []
                for key in matching_keys:
                    try:
                        order_index = int(key.split('_')[1])
                        if 0 <= order_index < len(orders_data):
                            order = FakeOrder.from_dict(orders_data[order_index])
                            
                            # Apply additional filters
                            if self._matches_query(order, query):
                                orders.append(order)
                    except Exception as e:
                        logger.error(f"Failed to load order from key {key}: {e}")
                
                # Sort orders
                orders = self._sort_orders(orders, query.sort_by, query.sort_order)
                
                # Apply pagination
                start = query.offset
                end = start + query.limit if query.limit else len(orders)
                
                return orders[start:end]
                
            except Exception as e:
                logger.error(f"Failed to query orders: {e}")
                return []
    
    def _matches_query(self, order: FakeOrder, query: FakeOrderQuery) -> bool:
        """Check if order matches additional query criteria"""
        if query.is_processed is not None and order.is_processed != query.is_processed:
            return False
        
        if query.payment_status and order.payment_status != query.payment_status:
            return False
        
        if query.created_after and order.created_at < query.created_after:
            return False
        
        if query.created_before and order.created_at > query.created_before:
            return False
        
        return True
    
    def _sort_orders(self, orders: List[FakeOrder], sort_by: str, sort_order: str) -> List[FakeOrder]:
        """Sort orders by specified criteria"""
        reverse = sort_order.lower() == 'desc'
        
        if sort_by == 'created_at':
            return sorted(orders, key=lambda o: o.created_at, reverse=reverse)
        elif sort_by == 'order_sn':
            return sorted(orders, key=lambda o: o.order_sn, reverse=reverse)
        elif sort_by == 'total_price':
            return sorted(orders, key=lambda o: o.total_price, reverse=reverse)
        elif sort_by == 'access_count':
            return sorted(orders, key=lambda o: o.access_count, reverse=reverse)
        else:
            return orders
    
    def _manage_cache_size(self):
        """Manage cache size to prevent memory issues"""
        if len(self._cache) > self._cache_max_size:
            # Remove least recently accessed items
            sorted_items = sorted(
                self._cache.items(),
                key=lambda x: x[1].last_accessed or datetime.min
            )
            
            # Remove oldest 20% of items
            remove_count = len(sorted_items) // 5
            for order_sn, _ in sorted_items[:remove_count]:
                del self._cache[order_sn]
    
    def delete_order(self, order_sn: str) -> bool:
        """Delete a fake order"""
        with self._lock:
            try:
                # Load existing orders
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    orders_data = json.load(f)
                
                # Find and remove order
                order_index = None
                order_to_remove = None
                
                for i, order_data in enumerate(orders_data):
                    if order_data.get('order_sn') == order_sn:
                        order_index = i
                        order_to_remove = FakeOrder.from_dict(order_data)
                        break
                
                if order_index is None:
                    return False
                
                # Remove from data
                orders_data.pop(order_index)
                
                # Save updated orders
                with open(self.orders_file, 'w', encoding='utf-8') as f:
                    json.dump(orders_data, f, indent=2, ensure_ascii=False)
                
                # Update index
                file_key = f"order_{order_index}"
                self.index.remove_order(order_to_remove, file_key)
                
                # Remove from cache
                self._cache.pop(order_sn, None)
                
                # Update metadata
                self.metadata['total_orders'] = len(orders_data)
                self._save_metadata()
                self._save_index()
                
                # Rebuild index to handle index shifts
                self._rebuild_index()
                
                logger.info(f"Deleted fake order: {order_sn}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to delete order {order_sn}: {e}")
                return False
    
    def cleanup_orders(self, older_than_days: int = 7, 
                      processed_only: bool = False) -> Dict[str, int]:
        """Clean up old fake orders"""
        with self._lock:
            try:
                cutoff_date = datetime.now() - timedelta(days=older_than_days)
                
                # Load existing orders
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    orders_data = json.load(f)
                
                original_count = len(orders_data)
                
                # Filter orders
                filtered_orders = []
                for order_data in orders_data:
                    try:
                        order = FakeOrder.from_dict(order_data)
                        
                        # Check age
                        if order.created_at > cutoff_date:
                            filtered_orders.append(order_data)
                            continue
                        
                        # Check processing status if specified
                        if processed_only and not order.is_processed:
                            filtered_orders.append(order_data)
                            continue
                        
                        # Order will be removed
                        logger.debug(f"Cleaning up order: {order.order_sn}")
                        
                    except Exception as e:
                        logger.error(f"Error processing order for cleanup: {e}")
                        # Keep problematic orders to avoid data loss
                        filtered_orders.append(order_data)
                
                # Save filtered orders
                with open(self.orders_file, 'w', encoding='utf-8') as f:
                    json.dump(filtered_orders, f, indent=2, ensure_ascii=False)
                
                # Rebuild index
                self._rebuild_index()
                
                # Clear cache
                self._cache.clear()
                
                # Update metadata
                removed_count = original_count - len(filtered_orders)
                self.metadata['total_orders'] = len(filtered_orders)
                self.metadata['last_cleanup'] = datetime.now().isoformat()
                self._save_metadata()
                
                logger.info(f"Cleaned up {removed_count} fake orders")
                
                return {
                    'original_count': original_count,
                    'removed_count': removed_count,
                    'remaining_count': len(filtered_orders)
                }
                
            except Exception as e:
                logger.error(f"Failed to cleanup orders: {e}")
                raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive storage statistics"""
        with self._lock:
            index_stats = self.index.get_stats()
            
            # Calculate additional stats
            try:
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    orders_data = json.load(f)
                
                processed_count = sum(1 for o in orders_data if o.get('is_processed', False))
                total_price = sum(o.get('total_price', 0) for o in orders_data)
                
                # Age distribution
                now = datetime.now()
                age_distribution = {'0-1d': 0, '1-7d': 0, '7-30d': 0, '30d+': 0}
                
                for order_data in orders_data:
                    try:
                        created_at = datetime.fromisoformat(order_data.get('created_at', ''))
                        age_days = (now - created_at).days
                        
                        if age_days <= 1:
                            age_distribution['0-1d'] += 1
                        elif age_days <= 7:
                            age_distribution['1-7d'] += 1
                        elif age_days <= 30:
                            age_distribution['7-30d'] += 1
                        else:
                            age_distribution['30d+'] += 1
                    except:
                        pass
                
            except Exception as e:
                logger.error(f"Failed to calculate additional stats: {e}")
                processed_count = 0
                total_price = 0
                age_distribution = {}
            
            return {
                **index_stats,
                'processed_orders': processed_count,
                'unprocessed_orders': index_stats['total_orders'] - processed_count,
                'total_value': total_price,
                'age_distribution': age_distribution,
                'cache_size': len(self._cache),
                'storage_metadata': self.metadata
            }


# Global storage instance
fake_order_storage = FakeOrderStorage()