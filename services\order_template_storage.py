"""
Order Template Storage System

This module provides persistent storage for custom order templates with versioning,
management, and import/export functionality for the fake order system.
"""

import json
import os
import shutil
import threading
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Any, Optional, Set
from pathlib import Path
import logging
import hashlib
import zipfile
import tempfile

logger = logging.getLogger(__name__)

@dataclass
class OrderTemplate:
    """Complete order template with configuration and metadata"""
    template_id: str
    name: str
    description: str
    category: str
    version: str = "1.0"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    
    # Order configuration
    order_config: Dict[str, Any] = field(default_factory=dict)
    buyer_config: Dict[str, Any] = field(default_factory=dict)
    product_config: Dict[str, Any] = field(default_factory=dict)
    
    # Template metadata
    tags: List[str] = field(default_factory=list)
    use_count: int = 0
    last_used: Optional[datetime] = None
    is_public: bool = False
    is_built_in: bool = False
    
    # Validation and compatibility
    compatible_skus: List[str] = field(default_factory=list)
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization processing"""
        if isinstance(self.created_at, str):
            self.created_at = datetime.fromisoformat(self.created_at)
        if isinstance(self.updated_at, str):
            self.updated_at = datetime.fromisoformat(self.updated_at)
        if self.last_used and isinstance(self.last_used, str):
            self.last_used = datetime.fromisoformat(self.last_used)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        if self.last_used:
            data['last_used'] = self.last_used.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderTemplate':
        """Create OrderTemplate from dictionary"""
        return cls(**data)
    
    def update_usage(self):
        """Update usage tracking"""
        self.use_count += 1
        self.last_used = datetime.now()
        self.updated_at = datetime.now()
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate template configuration"""
        errors = []
        warnings = []
        
        # Validate required fields
        if not self.name:
            errors.append("Template name is required")
        
        if not self.template_id:
            errors.append("Template ID is required")
        
        # Validate order configuration
        if not self.order_config:
            warnings.append("Order configuration is empty")
        
        # Validate product configuration
        if self.product_config and 'var_sku' in self.product_config:
            var_sku = self.product_config['var_sku']
            if self.compatible_skus and var_sku not in self.compatible_skus:
                warnings.append(f"SKU {var_sku} not in compatible SKUs list")
        
        # Validate buyer configuration
        if self.buyer_config:
            required_buyer_fields = ['buyer_username', 'buyer_name']
            for field in required_buyer_fields:
                if field not in self.buyer_config:
                    warnings.append(f"Buyer field '{field}' is recommended")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def get_hash(self) -> str:
        """Get hash of template content for change detection"""
        content = {
            'order_config': self.order_config,
            'buyer_config': self.buyer_config,
            'product_config': self.product_config,
            'validation_rules': self.validation_rules
        }
        content_str = json.dumps(content, sort_keys=True)
        return hashlib.md5(content_str.encode()).hexdigest()

@dataclass
class TemplateVersion:
    """Version information for template versioning"""
    version: str
    created_at: datetime
    changes: str
    template_hash: str
    created_by: str = "system"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'version': self.version,
            'created_at': self.created_at.isoformat(),
            'changes': self.changes,
            'template_hash': self.template_hash,
            'created_by': self.created_by
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TemplateVersion':
        """Create from dictionary"""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)

class OrderTemplateStorage:
    """Storage system for order templates with versioning and management"""
    
    def __init__(self, storage_dir: str = 'configs/data/order_templates'):
        self.storage_dir = Path(storage_dir)
        self.templates_file = self.storage_dir / 'templates.json'
        self.versions_file = self.storage_dir / 'versions.json'
        self.metadata_file = self.storage_dir / 'metadata.json'
        self.exports_dir = self.storage_dir / 'exports'
        self.imports_dir = self.storage_dir / 'imports'
        
        self._lock = threading.RLock()
        self._templates: Dict[str, OrderTemplate] = {}
        self._versions: Dict[str, List[TemplateVersion]] = {}
        self._metadata: Dict[str, Any] = {}
        
        self._ensure_storage_structure()
        self._load_templates()
        self._load_versions()
        self._load_metadata()
        self._create_built_in_templates()
    
    def _ensure_storage_structure(self):
        """Ensure storage directory structure exists"""
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.exports_dir.mkdir(exist_ok=True)
        self.imports_dir.mkdir(exist_ok=True)
        
        # Create empty files if they don't exist
        for file_path in [self.templates_file, self.versions_file, self.metadata_file]:
            if not file_path.exists():
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump({}, f, indent=2)
    
    def _load_templates(self):
        """Load templates from storage"""
        try:
            with open(self.templates_file, 'r', encoding='utf-8') as f:
                templates_data = json.load(f)
            
            for template_id, template_data in templates_data.items():
                try:
                    template = OrderTemplate.from_dict(template_data)
                    self._templates[template_id] = template
                except Exception as e:
                    logger.error(f"Failed to load template {template_id}: {e}")
            
            logger.info(f"Loaded {len(self._templates)} order templates")
            
        except Exception as e:
            logger.error(f"Failed to load templates: {e}")
            self._templates = {}
    
    def _load_versions(self):
        """Load version history from storage"""
        try:
            with open(self.versions_file, 'r', encoding='utf-8') as f:
                versions_data = json.load(f)
            
            for template_id, versions_list in versions_data.items():
                self._versions[template_id] = [
                    TemplateVersion.from_dict(version_data)
                    for version_data in versions_list
                ]
            
            logger.info(f"Loaded version history for {len(self._versions)} templates")
            
        except Exception as e:
            logger.error(f"Failed to load versions: {e}")
            self._versions = {}
    
    def _load_metadata(self):
        """Load storage metadata"""
        try:
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                self._metadata = json.load(f)
        except Exception as e:
            logger.error(f"Failed to load metadata: {e}")
            self._metadata = {
                'created_at': datetime.now().isoformat(),
                'version': '1.0',
                'total_templates': 0,
                'last_cleanup': None
            }
    
    def _save_templates(self):
        """Save templates to storage"""
        try:
            templates_data = {
                template_id: template.to_dict()
                for template_id, template in self._templates.items()
            }
            
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save templates: {e}")
            raise
    
    def _save_versions(self):
        """Save version history to storage"""
        try:
            versions_data = {
                template_id: [version.to_dict() for version in versions]
                for template_id, versions in self._versions.items()
            }
            
            with open(self.versions_file, 'w', encoding='utf-8') as f:
                json.dump(versions_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save versions: {e}")
            raise
    
    def _save_metadata(self):
        """Save metadata to storage"""
        try:
            self._metadata['last_updated'] = datetime.now().isoformat()
            self._metadata['total_templates'] = len(self._templates)
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self._metadata, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
    
    def _create_built_in_templates(self):
        """Create built-in order templates"""
        if self.get_template('basic_steam_order'):
            return  # Built-in templates already exist
        
        # Basic Steam order template
        steam_template = OrderTemplate(
            template_id='basic_steam_order',
            name='Basic Steam Order',
            description='Standard Steam authentication code order template',
            category='gaming',
            created_by='system',
            is_built_in=True,
            is_public=True,
            order_config={
                'status': 'To Ship',
                'payment_status': 'paid',
                'test_scenario': 'steam_basic_flow'
            },
            buyer_config={
                'buyer_username': 'steam_tester',
                'buyer_name': 'Steam Test User',
                'buyer_phone': '+60123456789',
                'buyer_email': '<EMAIL>'
            },
            product_config={
                'var_sku': 'steam_auth_code',
                'quantity': 1
            },
            compatible_skus=['steam_auth_code', 'text_based'],
            tags=['gaming', 'steam', 'basic', 'authentication']
        )
        
        # Netflix subscription template
        netflix_template = OrderTemplate(
            template_id='netflix_premium_30d',
            name='Netflix Premium 30 Days',
            description='Netflix premium subscription for 30 days testing',
            category='streaming',
            created_by='system',
            is_built_in=True,
            is_public=True,
            order_config={
                'status': 'To Ship',
                'payment_status': 'paid',
                'test_scenario': 'netflix_subscription_flow'
            },
            buyer_config={
                'buyer_username': 'netflix_user',
                'buyer_name': 'Netflix Test User',
                'buyer_phone': '+60123456789',
                'buyer_email': '<EMAIL>'
            },
            product_config={
                'var_sku': 'netflix_30',
                'quantity': 1
            },
            compatible_skus=['netflix_30', 'netflix_60'],
            tags=['streaming', 'netflix', 'premium', 'subscription']
        )
        
        # Canva Pro template
        canva_template = OrderTemplate(
            template_id='canva_pro_monthly',
            name='Canva Pro Monthly',
            description='Canva Pro subscription monthly template',
            category='design',
            created_by='system',
            is_built_in=True,
            is_public=True,
            order_config={
                'status': 'To Ship',
                'payment_status': 'paid',
                'test_scenario': 'canva_pro_flow'
            },
            buyer_config={
                'buyer_username': 'canva_designer',
                'buyer_name': 'Canva Test Designer',
                'buyer_phone': '+60123456789',
                'buyer_email': '<EMAIL>'
            },
            product_config={
                'var_sku': 'canva_30',
                'quantity': 1
            },
            compatible_skus=['canva_30', 'canva_lifetime'],
            tags=['design', 'canva', 'pro', 'creative']
        )
        
        # VPN service template
        vpn_template = OrderTemplate(
            template_id='vpn_monthly_basic',
            name='VPN Monthly Basic',
            description='Basic VPN service monthly subscription',
            category='security',
            created_by='system',
            is_built_in=True,
            is_public=True,
            order_config={
                'status': 'To Ship',
                'payment_status': 'paid',
                'test_scenario': 'vpn_subscription_flow'
            },
            buyer_config={
                'buyer_username': 'vpn_user',
                'buyer_name': 'VPN Test User',
                'buyer_phone': '+60123456789',
                'buyer_email': '<EMAIL>'
            },
            product_config={
                'var_sku': 'vpn_monthly',
                'quantity': 1
            },
            compatible_skus=['vpn_monthly', 'vpn_yearly'],
            tags=['security', 'vpn', 'privacy', 'monthly']
        )
        
        # Batch order template
        batch_template = OrderTemplate(
            template_id='mixed_products_batch',
            name='Mixed Products Batch',
            description='Template for testing multiple different products in batch',
            category='testing',
            created_by='system',
            is_built_in=True,
            is_public=True,
            order_config={
                'status': 'To Ship',
                'payment_status': 'paid',
                'test_scenario': 'batch_processing_test'
            },
            buyer_config={
                'buyer_username': 'batch_tester',
                'buyer_name': 'Batch Test User',
                'buyer_phone': '+60123456789',
                'buyer_email': '<EMAIL>'
            },
            product_config={
                'var_sku': 'steam_auth_code',  # Default, can be overridden
                'quantity': 1
            },
            compatible_skus=['steam_auth_code', 'netflix_30', 'canva_30', 'vpn_monthly'],
            tags=['testing', 'batch', 'mixed', 'integration']
        )
        
        # Store built-in templates
        built_in_templates = [
            steam_template, netflix_template, canva_template, 
            vpn_template, batch_template
        ]
        
        for template in built_in_templates:
            self._templates[template.template_id] = template
            self._create_version(template, "Initial built-in template creation")
        
        self._save_templates()
        self._save_versions()
        self._save_metadata()
        
        logger.info(f"Created {len(built_in_templates)} built-in order templates")
    
    def save_template(self, template: OrderTemplate, changes: str = "Template saved") -> bool:
        """Save order template with versioning"""
        with self._lock:
            try:
                # Validate template
                validation = template.validate_config()
                if not validation['is_valid']:
                    logger.error(f"Template validation failed: {validation['errors']}")
                    return False
                
                # Check if template exists and has changed
                existing_template = self._templates.get(template.template_id)
                if existing_template:
                    if existing_template.get_hash() == template.get_hash():
                        logger.info(f"Template {template.template_id} unchanged, skipping save")
                        return True
                    
                    # Update version
                    template.version = self._increment_version(existing_template.version)
                    template.updated_at = datetime.now()
                else:
                    # New template
                    template.created_at = datetime.now()
                    template.updated_at = datetime.now()
                
                # Save template
                self._templates[template.template_id] = template
                
                # Create version entry
                self._create_version(template, changes)
                
                # Save to storage
                self._save_templates()
                self._save_versions()
                self._save_metadata()
                
                logger.info(f"Saved template: {template.template_id} v{template.version}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to save template {template.template_id}: {e}")
                return False
    
    def _increment_version(self, current_version: str) -> str:
        """Increment version number"""
        try:
            parts = current_version.split('.')
            if len(parts) >= 2:
                major, minor = int(parts[0]), int(parts[1])
                return f"{major}.{minor + 1}"
            else:
                return "1.1"
        except:
            return "1.1"
    
    def _create_version(self, template: OrderTemplate, changes: str):
        """Create version entry for template"""
        version = TemplateVersion(
            version=template.version,
            created_at=template.updated_at,
            changes=changes,
            template_hash=template.get_hash(),
            created_by=template.created_by
        )
        
        if template.template_id not in self._versions:
            self._versions[template.template_id] = []
        
        self._versions[template.template_id].append(version)
        
        # Keep only last 10 versions to prevent storage bloat
        if len(self._versions[template.template_id]) > 10:
            self._versions[template.template_id] = self._versions[template.template_id][-10:]
    
    def get_template(self, template_id: str) -> Optional[OrderTemplate]:
        """Get template by ID"""
        template = self._templates.get(template_id)
        if template:
            template.update_usage()
            self._save_templates()  # Save usage update
        return template
    
    def list_templates(self, category: Optional[str] = None, 
                      public_only: bool = False,
                      tags: Optional[List[str]] = None) -> List[OrderTemplate]:
        """List templates with optional filtering"""
        templates = list(self._templates.values())
        
        if category:
            templates = [t for t in templates if t.category == category]
        
        if public_only:
            templates = [t for t in templates if t.is_public]
        
        if tags:
            templates = [
                t for t in templates 
                if any(tag in t.tags for tag in tags)
            ]
        
        # Sort by usage and creation date
        templates.sort(key=lambda t: (t.use_count, t.created_at), reverse=True)
        
        return templates
    
    def delete_template(self, template_id: str) -> bool:
        """Delete template and its versions"""
        with self._lock:
            try:
                if template_id not in self._templates:
                    return False
                
                template = self._templates[template_id]
                
                # Don't delete built-in templates
                if template.is_built_in:
                    logger.warning(f"Cannot delete built-in template: {template_id}")
                    return False
                
                # Remove template and versions
                del self._templates[template_id]
                self._versions.pop(template_id, None)
                
                # Save changes
                self._save_templates()
                self._save_versions()
                self._save_metadata()
                
                logger.info(f"Deleted template: {template_id}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to delete template {template_id}: {e}")
                return False
    
    def get_template_versions(self, template_id: str) -> List[TemplateVersion]:
        """Get version history for template"""
        return self._versions.get(template_id, [])
    
    def export_template(self, template_id: str, export_path: Optional[str] = None) -> str:
        """Export template to file"""
        template = self._templates.get(template_id)
        if not template:
            raise ValueError(f"Template {template_id} not found")
        
        if not export_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_path = self.exports_dir / f"{template_id}_{timestamp}.json"
        
        export_data = {
            'template': template.to_dict(),
            'versions': [v.to_dict() for v in self._versions.get(template_id, [])],
            'exported_at': datetime.now().isoformat(),
            'export_version': '1.0'
        }
        
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Exported template {template_id} to {export_path}")
        return str(export_path)
    
    def import_template(self, import_path: str, overwrite: bool = False) -> bool:
        """Import template from file"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            template_data = import_data.get('template')
            if not template_data:
                raise ValueError("Invalid template file format")
            
            template = OrderTemplate.from_dict(template_data)
            
            # Check if template exists
            if template.template_id in self._templates and not overwrite:
                logger.warning(f"Template {template.template_id} already exists, use overwrite=True")
                return False
            
            # Import template
            template.created_by = f"imported_from_{Path(import_path).name}"
            template.updated_at = datetime.now()
            
            # Save template
            changes = f"Imported from {Path(import_path).name}"
            return self.save_template(template, changes)
            
        except Exception as e:
            logger.error(f"Failed to import template from {import_path}: {e}")
            return False
    
    def export_all_templates(self, export_path: Optional[str] = None) -> str:
        """Export all templates to a zip file"""
        if not export_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_path = self.exports_dir / f"all_templates_{timestamp}.zip"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Export each template
            for template_id, template in self._templates.items():
                template_file = temp_path / f"{template_id}.json"
                export_data = {
                    'template': template.to_dict(),
                    'versions': [v.to_dict() for v in self._versions.get(template_id, [])],
                    'exported_at': datetime.now().isoformat()
                }
                
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            # Create zip file
            with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for template_file in temp_path.glob('*.json'):
                    zipf.write(template_file, template_file.name)
        
        logger.info(f"Exported all templates to {export_path}")
        return str(export_path)
    
    def import_templates_from_zip(self, zip_path: str, overwrite: bool = False) -> Dict[str, Any]:
        """Import templates from zip file"""
        results = {'successful': [], 'failed': [], 'skipped': []}
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Extract zip file
                with zipfile.ZipFile(zip_path, 'r') as zipf:
                    zipf.extractall(temp_path)
                
                # Import each template file
                for template_file in temp_path.glob('*.json'):
                    try:
                        if self.import_template(str(template_file), overwrite):
                            results['successful'].append(template_file.stem)
                        else:
                            results['skipped'].append(template_file.stem)
                    except Exception as e:
                        results['failed'].append({
                            'template': template_file.stem,
                            'error': str(e)
                        })
            
            logger.info(f"Import results: {len(results['successful'])} successful, "
                       f"{len(results['failed'])} failed, {len(results['skipped'])} skipped")
            
        except Exception as e:
            logger.error(f"Failed to import templates from zip {zip_path}: {e}")
            results['failed'].append({'error': str(e)})
        
        return results
    
    def cleanup_old_versions(self, keep_versions: int = 5) -> Dict[str, int]:
        """Clean up old template versions"""
        with self._lock:
            cleaned_count = 0
            
            for template_id, versions in self._versions.items():
                if len(versions) > keep_versions:
                    # Keep the most recent versions
                    versions.sort(key=lambda v: v.created_at, reverse=True)
                    self._versions[template_id] = versions[:keep_versions]
                    cleaned_count += len(versions) - keep_versions
            
            if cleaned_count > 0:
                self._save_versions()
                logger.info(f"Cleaned up {cleaned_count} old template versions")
            
            return {'cleaned_versions': cleaned_count}
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive storage statistics"""
        with self._lock:
            categories = {}
            total_usage = 0
            
            for template in self._templates.values():
                if template.category not in categories:
                    categories[template.category] = 0
                categories[template.category] += 1
                total_usage += template.use_count
            
            return {
                'total_templates': len(self._templates),
                'categories': categories,
                'built_in_templates': sum(1 for t in self._templates.values() if t.is_built_in),
                'custom_templates': sum(1 for t in self._templates.values() if not t.is_built_in),
                'public_templates': sum(1 for t in self._templates.values() if t.is_public),
                'total_usage': total_usage,
                'total_versions': sum(len(versions) for versions in self._versions.values()),
                'storage_metadata': self._metadata
            }


# Global template storage instance
order_template_storage = OrderTemplateStorage()