# Server Tags Configuration Guide

This guide explains how to configure server tags for the VPN plugin to enable SKU-based server selection.

## Overview

The VPN plugin now supports server tag-based routing, allowing different product SKUs to automatically select appropriate servers based on their tags. This provides more flexible and maintainable server management.

## How It Works

1. **Server Tags**: Each VPN server can have multiple tags (e.g., `malaysia`, `singapore`, `shinjiru`, `digitalocean`, `highspeed`, `premium`)
2. **SKU Mapping**: Each product SKU is mapped to specific server tags in the strategy factory
3. **Automatic Selection**: When processing an order, the system automatically selects servers that match the required tags

## Server Tags Configuration

### Adding Tags to Servers

When creating or editing servers through the VPN management interface, you can add tags in the "Tags" field (comma-separated):

```
malaysia, shinjiru, basic
```

### Recommended Tag Categories

#### Location Tags
- `malaysia` - Servers located in Malaysia
- `singapore` - Servers located in Singapore
- `thailand` - Servers located in Thailand
- `indonesia` - Servers located in Indonesia

#### Provider Tags
- `shinjiru` - Shinjiru hosting provider
- `digitalocean` - Digital Ocean hosting provider
- `gbnetwork` - GBNetwork hosting provider
- `vultr` - Vultr hosting provider

#### Performance Tags
- `basic` - Basic performance servers
- `standard` - Standard performance servers
- `highspeed` - High-speed servers
- `premium` - Premium performance servers
- `business` - Business-grade servers

#### Feature Tags
- `unlimited` - Unlimited bandwidth servers
- `dedicated` - Dedicated IP servers
- `shared` - Shared IP servers

## SKU to Server Tags Mapping

The mapping is configured in `plugins/vpn/strategies/strategy_factory.py`:

```python
SKU_SERVER_TAGS_MAPPING = {
    # Malaysia Basic Products - use Shinjiru servers only
    'my_': ['malaysia', 'shinjiru', 'basic'],
    'my_standard': ['malaysia', 'shinjiru', 'standard'],
    'my_premium': ['malaysia', 'shinjiru', 'premium'],
    
    # Malaysia High-Speed Products - use all available servers
    'my_highspeed': ['malaysia', 'singapore', 'highspeed', 'premium'],
    'my_highspeed_premium': ['malaysia', 'singapore', 'highspeed', 'premium', 'business'],
    
    # Singapore Products - use Digital Ocean and premium servers
    'sg_': ['singapore', 'digitalocean', 'basic'],
    'sg_highspeed': ['singapore', 'digitalocean', 'highspeed'],
    'sg_premium': ['singapore', 'digitalocean', 'premium'],
    'sg_business': ['singapore', 'digitalocean', 'business', 'premium'],
}
```

## Example Server Configurations

### Malaysia Shinjiru Server
```json
{
    "name": "MY-Shinjiru-01",
    "host": "my1.example.com",
    "tags": ["malaysia", "shinjiru", "basic", "standard"]
}
```

### Singapore Digital Ocean Server
```json
{
    "name": "SG-DO-01",
    "host": "sg1.example.com", 
    "tags": ["singapore", "digitalocean", "highspeed", "premium"]
}
```

### Multi-Region High-Speed Server
```json
{
    "name": "Premium-Global-01",
    "host": "global1.example.com",
    "tags": ["malaysia", "singapore", "highspeed", "premium", "business", "unlimited"]
}
```

## Order Processing Flow

1. **Order Received**: System receives order with specific var_sku (e.g., `my_highspeed_30`)
2. **Tag Lookup**: Strategy factory determines required tags: `['malaysia', 'singapore', 'highspeed', 'premium']`
3. **Server Filtering**: System filters all active servers to find those with matching tags
4. **Server Selection**: Strategy selects appropriate server(s) based on product type:
   - Basic products: Single random server
   - High-speed products: Multiple servers with shared UUID
5. **User Creation**: VPN user is created on selected server(s)

## Benefits

### For Administrators
- **Flexible Configuration**: Easy to reassign servers to different product tiers
- **Scalable Management**: Add new servers and assign appropriate tags
- **Clear Organization**: Servers are logically grouped by capabilities

### For Customers
- **Optimal Performance**: Automatically get servers best suited for their product tier
- **Geographic Optimization**: Servers selected based on location preferences
- **Service Consistency**: Consistent experience within each product tier

## Troubleshooting

### No Servers Found
If no servers match the required tags:
1. Check server tags are correctly configured
2. Verify servers are marked as active
3. Review SKU mapping in strategy factory
4. System will fall back to all active servers as last resort

### Performance Issues
- Ensure high-speed products have servers tagged with `highspeed` or `premium`
- Consider geographic distribution for better latency
- Monitor server load and add more servers with appropriate tags

## Migration from Old System

The new tag-based system is backward compatible. Existing strategies will continue to work, but you can gradually migrate by:

1. Adding tags to existing servers
2. Testing with specific SKUs
3. Updating SKU mappings as needed
4. Monitoring performance and adjusting tags

## Future Enhancements

Planned improvements include:
- Dynamic load balancing based on server metrics
- Geographic latency optimization
- Automatic server health-based selection
- Customer preference-based server selection
