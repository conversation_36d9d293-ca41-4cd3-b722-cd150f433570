{% extends "base.html" %}

{% block title %}SKU Restrictions Management{% endblock %}

{% block extra_head %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<style>
    .restriction-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .restriction-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .restriction-header {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        padding: 1.5rem;
    }
    
    .restriction-header.telco-lock {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .restriction-header.plan-limit {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 600;
    }
    
    .status-enabled {
        background-color: #dcfce7;
        color: #166534;
    }
    
    .status-disabled {
        background-color: #f3f4f6;
        color: #6b7280;
    }
    
    .action-btn {
        padding: 0.5rem;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        margin: 0 0.25rem;
    }
    
    .action-btn:hover {
        transform: scale(1.05);
    }
    
    .btn-edit {
        background-color: #fbbf24;
        color: white;
    }
    
    .btn-edit:hover {
        background-color: #f59e0b;
    }
    
    .btn-delete {
        background-color: #ef4444;
        color: white;
    }
    
    .btn-delete:hover {
        background-color: #dc2626;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
        border: none;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #d1d5db;
    }
    
    .pattern-test {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .test-result {
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
    }
    
    .test-match {
        background-color: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
    }
    
    .test-no-match {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #fecaca;
    }
</style>
{% endblock %}

{% block header %}SKU Restrictions Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">SKU Restrictions Management</h2>
                <p class="text-gray-600 mt-1">Configure access control rules based on SKU patterns</p>
            </div>
            <button type="button" class="bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" onclick="showCreateRestrictionModal()">
                <i class="fas fa-plus mr-2"></i>Add Restriction
            </button>
        </div>

        <!-- Statistics Section -->
        <div id="stats-section" class="stats-card">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-bar text-blue-500 text-xl"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">Current Statistics</h4>
                    <div id="stats-content" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Stats will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h4 class="text-lg font-semibold text-blue-800 mb-2">How SKU Restrictions Work</h4>
                    <div class="text-blue-700 space-y-2">
                        <p><strong>SKU Pattern Matching:</strong> Use wildcards like <code class="bg-white px-2 py-1 rounded">my_*</code> to match SKUs starting with "my_"</p>
                        <p><strong>Telco Lock:</strong> Users are locked to their first selected telco</p>
                        <p><strong>Plan Limits:</strong> Restrict maximum number of configurations per user</p>
                        <p><strong>Priority:</strong> First matching restriction applies</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Restrictions List -->
    <div id="restrictions-list" class="space-y-6">
        <!-- Restriction cards will be loaded here -->
    </div>
</div>

<!-- Create/Edit Restriction Modal -->
<div class="modal fade" id="restrictionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
            <div class="modal-header bg-gradient-to-r from-orange-500 to-red-600 text-white" style="border-radius: 16px 16px 0 0;">
                <h4 class="modal-title font-semibold" id="restrictionModalTitle">Add SKU Restriction</h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-6">
                <form id="restrictionForm">
                    <div class="mb-4">
                        <label for="skuPattern" class="form-label font-semibold text-gray-700">SKU Pattern</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all" id="skuPattern" required placeholder="e.g., my_*, premium_*, trial_*">
                        <small class="text-gray-500">Use * as wildcard. Examples: my_*, *premium*, exact_sku_name</small>
                        
                        <!-- Pattern Test Section -->
                        <div class="pattern-test">
                            <label class="font-medium text-gray-700 mb-2 block">Test Pattern:</label>
                            <div class="flex gap-2">
                                <input type="text" id="testSkuInput" class="form-control border border-gray-300 rounded px-3 py-2 flex-1" placeholder="Enter SKU to test">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="testSkuPattern()">Test</button>
                            </div>
                            <div id="testResult" class="test-result hidden"></div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="restrictionType" class="form-label font-semibold text-gray-700">Restriction Type</label>
                        <select class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all" id="restrictionType" required>
                            <option value="">Select restriction type</option>
                            <option value="telco_lock">Telco Lock</option>
                            <option value="plan_limit">Plan Limit</option>
                            <option value="access_deny">Access Deny</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="form-label font-semibold text-gray-700">Description</label>
                        <textarea class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all" id="description" rows="3" required placeholder="Describe what this restriction does"></textarea>
                    </div>
                    
                    <!-- Telco Lock Options -->
                    <div id="telcoLockOptions" class="mb-4 hidden">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h6 class="font-semibold text-red-800 mb-3">Telco Lock Settings</h6>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="singleTelcoOnly">
                                <label class="form-check-label font-medium text-red-700" for="singleTelcoOnly">
                                    Lock to single telco after first selection
                                </label>
                            </div>
                            <div>
                                <label for="allowedTelcos" class="form-label font-medium text-red-700">Allowed Telcos (optional)</label>
                                <input type="text" class="form-control border border-red-300 rounded px-3 py-2" id="allowedTelcos" placeholder="digi,maxis,celcom (comma-separated)">
                                <small class="text-red-600">Leave empty to allow all telcos initially</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Plan Limit Options -->
                    <div id="planLimitOptions" class="mb-4 hidden">
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h6 class="font-semibold text-purple-800 mb-3">Plan Limit Settings</h6>
                            <div>
                                <label for="maxConfigurations" class="form-label font-medium text-purple-700">Maximum Configurations</label>
                                <input type="number" class="form-control border border-purple-300 rounded px-3 py-2" id="maxConfigurations" min="-1" value="1" placeholder="1">
                                <small class="text-purple-600">Set -1 for unlimited configurations</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="restrictionEnabled" checked>
                            <label class="form-check-label font-semibold text-gray-700" for="restrictionEnabled">
                                Enabled
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-6 pt-0">
                <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all" onclick="saveRestriction()">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
let editMode = false;
let currentRestrictionIndex = null;

// Configure toastr
toastr.options = {
    "closeButton": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "timeOut": "3000"
};

// Load data on page load
$(document).ready(function() {
    loadRestrictions();
    loadStats();
    setupFormHandlers();
});

function setupFormHandlers() {
    // Show/hide options based on restriction type
    $('#restrictionType').change(function() {
        const type = $(this).val();
        $('.mb-4[id$="Options"]').addClass('hidden');
        
        if (type === 'telco_lock') {
            $('#telcoLockOptions').removeClass('hidden');
        } else if (type === 'plan_limit') {
            $('#planLimitOptions').removeClass('hidden');
        }
    });
    
    // Test pattern as user types
    $('#skuPattern, #testSkuInput').on('input', function() {
        if ($('#skuPattern').val() && $('#testSkuInput').val()) {
            testSkuPattern();
        }
    });
}

function loadRestrictions() {
    $.get('/vpn-config-generator/api/sku-restrictions')
        .done(function(response) {
            if (response.success) {
                displayRestrictions(response.restrictions);
            } else {
                toastr.error('Failed to load restrictions: ' + response.error);
            }
        })
        .fail(function() {
            toastr.error('Failed to load restrictions');
        });
}

function loadStats() {
    $.get('/vpn-config-generator/api/sku-restrictions/stats')
        .done(function(response) {
            if (response.success) {
                displayStats(response.stats);
            } else {
                console.error('Failed to load stats: ' + response.error);
            }
        })
        .fail(function() {
            console.error('Failed to load stats');
        });
}

function displayStats(stats) {
    const container = $('#stats-content');
    container.html(`
        <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">${stats.total_users || 0}</div>
            <div class="text-sm text-gray-600">Total Users</div>
        </div>
        <div class="text-center">
            <div class="text-2xl font-bold text-red-600">${stats.restricted_users || 0}</div>
            <div class="text-sm text-gray-600">Restricted Users</div>
        </div>
        <div class="text-center">
            <div class="text-2xl font-bold text-green-600">${stats.total_configurations || 0}</div>
            <div class="text-sm text-gray-600">Total Configs</div>
        </div>
        <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">${stats.active_restrictions || 0}</div>
            <div class="text-sm text-gray-600">Active Rules</div>
        </div>
    `);
}

function displayRestrictions(restrictions) {
    const container = $('#restrictions-list');
    container.empty();

    if (restrictions.length === 0) {
        container.html(`
            <div class="empty-state">
                <i class="fas fa-shield-alt"></i>
                <h3 class="text-xl font-semibold mb-2">No SKU Restrictions Configured</h3>
                <p class="text-gray-500 mb-4">All users have unrestricted access to VPN services</p>
                <button class="bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all" onclick="showCreateRestrictionModal()">
                    <i class="fas fa-plus mr-2"></i>Add Your First Restriction
                </button>
            </div>
        `);
        return;
    }

    restrictions.forEach((restriction, index) => {
        const restrictionCard = createRestrictionCard(restriction, index);
        container.append(restrictionCard);
    });
}

function createRestrictionCard(restriction, index) {
    const statusBadge = restriction.enabled ? 
        '<span class="status-badge status-enabled">Enabled</span>' : 
        '<span class="status-badge status-disabled">Disabled</span>';
    
    const typeClass = restriction.restriction_type.replace('_', '-');
    const typeIcon = {
        'telco_lock': 'fas fa-lock',
        'plan_limit': 'fas fa-list-ol',
        'access_deny': 'fas fa-ban'
    }[restriction.restriction_type] || 'fas fa-shield-alt';
    
    const allowedTelcos = restriction.allowed_telcos && restriction.allowed_telcos.length > 0 
        ? restriction.allowed_telcos.join(', ') 
        : 'All telcos';
    
    const maxConfigs = restriction.max_configurations === -1 
        ? 'Unlimited' 
        : restriction.max_configurations;

    return `
        <div class="restriction-card bg-white shadow-lg">
            <div class="restriction-header ${typeClass}">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                            <i class="${typeIcon}"></i>
                            <h3 class="text-xl font-bold">${restriction.sku_pattern}</h3>
                            ${statusBadge}
                        </div>
                        <p class="text-white text-opacity-90 text-sm mb-2">${restriction.description}</p>
                        <div class="text-white text-opacity-75 text-xs">
                            <strong>Type:</strong> ${restriction.restriction_type.replace('_', ' ').toUpperCase()}
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button class="action-btn btn-edit" onclick="editRestriction(${index})" title="Edit Restriction">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn btn-delete" onclick="deleteRestriction(${index})" title="Delete Restriction">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-semibold text-gray-800 mb-2">Configuration</h5>
                        <div class="space-y-1 text-sm">
                            <div><strong>Pattern:</strong> <code class="bg-gray-100 px-2 py-1 rounded">${restriction.sku_pattern}</code></div>
                            <div><strong>Type:</strong> ${restriction.restriction_type.replace('_', ' ')}</div>
                            ${restriction.single_telco_only ? '<div><strong>Single Telco:</strong> Yes</div>' : ''}
                            ${restriction.restriction_type === 'plan_limit' ? `<div><strong>Max Configs:</strong> ${maxConfigs}</div>` : ''}
                        </div>
                    </div>
                    <div>
                        <h5 class="font-semibold text-gray-800 mb-2">Access Control</h5>
                        <div class="space-y-1 text-sm">
                            <div><strong>Allowed Telcos:</strong> ${allowedTelcos}</div>
                            <div><strong>Status:</strong> ${restriction.enabled ? 'Active' : 'Inactive'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function testSkuPattern() {
    const pattern = $('#skuPattern').val();
    const testSku = $('#testSkuInput').val();
    const resultDiv = $('#testResult');
    
    if (!pattern || !testSku) {
        resultDiv.addClass('hidden');
        return;
    }
    
    // Simple wildcard matching
    const regexPattern = pattern.replace(/\*/g, '.*');
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    const matches = regex.test(testSku);
    
    resultDiv.removeClass('hidden test-match test-no-match');
    if (matches) {
        resultDiv.addClass('test-match');
        resultDiv.html(`<i class="fas fa-check mr-1"></i>Match: "${testSku}" matches pattern "${pattern}"`);
    } else {
        resultDiv.addClass('test-no-match');
        resultDiv.html(`<i class="fas fa-times mr-1"></i>No Match: "${testSku}" does not match pattern "${pattern}"`);
    }
}

function showCreateRestrictionModal() {
    editMode = false;
    currentRestrictionIndex = null;
    $('#restrictionModalTitle').text('Add SKU Restriction');
    $('#restrictionForm')[0].reset();
    $('#restrictionEnabled').prop('checked', true);
    $('.mb-4[id$="Options"]').addClass('hidden');
    $('#testResult').addClass('hidden');
    $('#restrictionModal').modal('show');
}

function editRestriction(index) {
    $.get('/vpn-config-generator/api/sku-restrictions')
        .done(function(response) {
            if (response.success && response.restrictions[index]) {
                const restriction = response.restrictions[index];
                editMode = true;
                currentRestrictionIndex = index;
                
                $('#restrictionModalTitle').text('Edit SKU Restriction');
                $('#skuPattern').val(restriction.sku_pattern);
                $('#restrictionType').val(restriction.restriction_type).trigger('change');
                $('#description').val(restriction.description);
                $('#restrictionEnabled').prop('checked', restriction.enabled);
                
                // Set type-specific options
                if (restriction.restriction_type === 'telco_lock') {
                    $('#singleTelcoOnly').prop('checked', restriction.single_telco_only);
                    $('#allowedTelcos').val(restriction.allowed_telcos ? restriction.allowed_telcos.join(',') : '');
                } else if (restriction.restriction_type === 'plan_limit') {
                    $('#maxConfigurations').val(restriction.max_configurations);
                }
                
                $('#restrictionModal').modal('show');
            } else {
                toastr.error('Restriction not found');
            }
        })
        .fail(function() {
            toastr.error('Failed to load restriction details');
        });
}

function saveRestriction() {
    const data = {
        sku_pattern: $('#skuPattern').val(),
        restriction_type: $('#restrictionType').val(),
        description: $('#description').val(),
        enabled: $('#restrictionEnabled').is(':checked'),
        allowed_telcos: $('#allowedTelcos').val() ? $('#allowedTelcos').val().split(',').map(t => t.trim()) : [],
        max_configurations: parseInt($('#maxConfigurations').val()) || -1,
        single_telco_only: $('#singleTelcoOnly').is(':checked')
    };

    const url = editMode ? 
        `/vpn-config-generator/api/sku-restrictions/${currentRestrictionIndex}` : 
        '/vpn-config-generator/api/sku-restrictions';
    const method = editMode ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data)
    })
    .done(function(response) {
        if (response.success) {
            toastr.success(editMode ? 'Restriction updated successfully' : 'Restriction created successfully');
            $('#restrictionModal').modal('hide');
            loadRestrictions();
            loadStats();
        } else {
            toastr.error('Failed to save restriction: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to save restriction');
    });
}

function deleteRestriction(index) {
    const confirmHtml = `
        <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
                    <div class="modal-header bg-gradient-to-r from-red-500 to-pink-600 text-white" style="border-radius: 16px 16px 0 0;">
                        <h5 class="modal-title font-semibold">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-6 text-center">
                        <i class="fas fa-shield-alt text-red-500 text-4xl mb-4"></i>
                        <h4 class="font-semibold text-gray-800 mb-2">Delete SKU Restriction</h4>
                        <p class="text-gray-600">Are you sure you want to delete this restriction? This will remove access controls for matching SKUs.</p>
                    </div>
                    <div class="modal-footer border-0 p-6 pt-0">
                        <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn bg-gradient-to-r from-red-500 to-pink-600 text-white px-6 py-2 rounded-lg font-semibold" onclick="confirmDeleteRestriction(${index})">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('body').append(confirmHtml);
    $('#confirmDeleteModal').modal('show');
    $('#confirmDeleteModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

function confirmDeleteRestriction(index) {
    $('#confirmDeleteModal').modal('hide');
    $.ajax({
        url: `/vpn-config-generator/api/sku-restrictions/${index}`,
        method: 'DELETE'
    })
    .done(function(response) {
        if (response.success) {
            toastr.success('Restriction deleted successfully');
            loadRestrictions();
            loadStats();
        } else {
            toastr.error('Failed to delete restriction: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to delete restriction');
    });
}
</script>
{% endblock %} 