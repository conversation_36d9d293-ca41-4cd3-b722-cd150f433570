# SSH User Management - VPN Plugin

## Overview

The SSH User Management feature provides comprehensive tools for managing SSH users across your VPN servers. This functionality integrates with the `https://blueblue.api.online-mtyb.com` API to provide real-time user management, session monitoring, and automated maintenance tasks.

## Features

### 1. User Management
- **List SSH Users**: View all SSH users on each server with their status, expiry dates, and lock status
- **Renew Users**: Extend user expiry dates and unlock accounts
- **Auto-Delete Expired**: Automatically remove expired user accounts

### 2. Session Monitoring
- **Active Sessions**: Monitor active SSH sessions across different services:
  - Dropbear SSH sessions
  - OpenSSH sessions
  - OpenVPN TCP connections
  - OpenVPN UDP connections

### 3. Security Features
- **Multi-Login Violations**: Check for users violating multi-login limits
- **Auto-Kill Configuration**: Set up automatic session termination for users exceeding session limits

## API Endpoints

### User Management
- `GET /api/v1/servers/{server_id}/ssh-users/members` - List SSH users
- `POST /api/v1/servers/{server_id}/ssh-users/renew` - Renew user account
- `POST /api/v1/servers/{server_id}/ssh-users/auto-delete-expired` - Delete expired users

### Session Monitoring
- `GET /api/v1/servers/{server_id}/ssh-users/sessions` - Get active sessions
- `GET /api/v1/servers/{server_id}/ssh-users/limit-violations` - Check violations

### Auto-Kill Configuration
- `GET /api/v1/servers/{server_id}/ssh-users/autokill/status` - Get auto-kill status
- `POST /api/v1/servers/{server_id}/ssh-users/autokill/configure` - Configure auto-kill

## Web Interface

### Accessing SSH User Management
1. Navigate to the VPN Plugin admin interface
2. Click on "SSH User Management" in the navigation menu
3. Select a server to manage its SSH users

### Interface Features

#### Users Tab
- View all SSH users with their details
- Renew user accounts with custom expiry extensions
- Delete expired users in bulk

#### Sessions Tab
- Monitor active SSH and VPN sessions
- View connection details including IP addresses and connection times

#### Auto-Kill Tab
- Configure automatic session termination
- Set check intervals (5, 10, or 15 minutes)
- Define maximum sessions per user

#### Violations Tab
- View multi-login violations
- Monitor users exceeding connection limits

## Usage Examples

### Renewing a User Account
```python
from plugins.vpn.services.vpn_api_service import VPNAPIService

# Initialize API service
api_service = VPNAPIService(config)

# Renew user for 30 days and unlock account
result = api_service.renew_ssh_user(
    server_id=1,
    username="testuser",
    days=30,
    unlock=True
)

if result and result.get('success'):
    print(f"User renewed. New expiry: {result['new_expires_on']}")
```

### Getting Active Sessions
```python
# Get active sessions for a server
sessions = api_service.get_ssh_sessions(server_id=1)

if sessions:
    print(f"Dropbear sessions: {len(sessions.get('dropbear', []))}")
    print(f"OpenSSH sessions: {len(sessions.get('openssh', []))}")
    print(f"OpenVPN TCP: {len(sessions.get('openvpn_tcp', []))}")
    print(f"OpenVPN UDP: {len(sessions.get('openvpn_udp', []))}")
```

### Configuring Auto-Kill
```python
# Configure auto-kill to check every 10 minutes with max 2 sessions per user
result = api_service.configure_ssh_autokill(
    server_id=1,
    interval_minutes=10,
    max_sessions=2
)

if result and result.get('enabled'):
    print("Auto-kill configured successfully")
```

## Configuration

### Plugin Configuration
The SSH user management functionality uses the existing VPN plugin configuration:

```json
{
  "vpn_api": {
    "base_url": "https://blueblue.api.online-mtyb.com",
    "username": "admin",
    "password": "admin123",
    "timeout": 30
  }
}
```

### Auto-Kill Settings
- **Interval Options**: 5, 10, or 15 minutes
- **Max Sessions**: 1-10 sessions per user
- **Service**: Requires `/usr/bin/tendang` script on the server

## Error Handling

The system provides comprehensive error handling:

- **Authentication Errors**: Automatic token refresh and re-authentication
- **Network Errors**: Timeout and connection error handling
- **API Errors**: Detailed error messages with status codes
- **Validation Errors**: Input validation for all parameters

## Security Considerations

1. **Authentication**: All API calls require valid authentication tokens
2. **Authorization**: Server access is controlled by the API service
3. **Input Validation**: All user inputs are validated before API calls
4. **Audit Trail**: All actions are logged for security auditing

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check API credentials in plugin configuration
   - Verify API server is accessible

2. **No Users Found**
   - Ensure server has SSH users with UID >= 1000
   - Check server connectivity

3. **Auto-Kill Not Working**
   - Verify `/usr/bin/tendang` script exists on server
   - Check cron service is running

### Testing

Run the test script to verify functionality:

```bash
python plugins/vpn/test_ssh_user_management.py
```

## Integration

The SSH User Management feature is fully integrated with the VPN plugin:

- **Admin Routes**: Accessible via `/admin/vpn/ssh-users`
- **API Endpoints**: Available under `/admin/vpn/api/servers/{server_id}/ssh-users/`
- **Templates**: Uses Bootstrap-based responsive design
- **JavaScript**: Real-time updates and interactive interface

## Future Enhancements

Planned improvements include:

1. **Bulk User Operations**: Create, modify, and delete multiple users
2. **User Templates**: Predefined user configurations
3. **Advanced Monitoring**: Real-time session alerts and notifications
4. **Reporting**: Usage statistics and user activity reports
5. **Integration**: Connection with external user management systems
