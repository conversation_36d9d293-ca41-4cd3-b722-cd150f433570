"""
Image cache service for Shopee API.
Provides caching functionality for uploaded images to avoid re-downloading and re-uploading.
"""
import os
import json
import time
import hashlib
import threading
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta


class ImageCache:
    """
    Manages image upload caching to avoid re-uploading the same images.
    
    Cache entry structure:
    {
        "image_url": str,
        "upload_result": dict,  # Shopee upload response
        "timestamp": float,     # Unix timestamp
        "conversation_id": str, # Original conversation used for upload
        "file_hash": str,       # Hash of the image content
        "content_type": str,    # Image content type
        "file_size": int        # Size in bytes
    }
    """
    
    def __init__(self, cache_dir: str = "cache", max_age_hours: int = 24, max_cache_size_mb: int = 100):
        """
        Initialize the image cache.
        
        Args:
            cache_dir: Directory to store cache files
            max_age_hours: Maximum age of cache entries in hours (default: 24)
            max_cache_size_mb: Maximum total cache size in MB (default: 100)
        """
        self.cache_dir = cache_dir
        self.max_age_seconds = max_age_hours * 3600
        self.max_cache_size_bytes = max_cache_size_mb * 1024 * 1024
        self.cache_file = os.path.join(cache_dir, "image_cache.json")
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
        # Load existing cache
        self._load_cache()
        
        # Clean up on initialization
        self._cleanup_expired_entries()
    
    def _load_cache(self) -> None:
        """Load cache from disk."""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self._cache = json.load(f)
                print(f"Loaded {len(self._cache)} image cache entries")
            else:
                self._cache = {}
                print("No existing image cache found, starting with empty cache")
        except Exception as e:
            print(f"Error loading image cache: {e}")
            self._cache = {}
    
    def _save_cache(self) -> None:
        """Save cache to disk."""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self._cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving image cache: {e}")
    
    def _generate_cache_key(self, image_url: str) -> str:
        """Generate a cache key from image URL."""
        return hashlib.md5(image_url.encode('utf-8')).hexdigest()
    
    def _generate_file_hash(self, image_data: bytes) -> str:
        """Generate a hash from image file content."""
        return hashlib.sha256(image_data).hexdigest()[:16]  # First 16 chars for brevity
    
    def get_cached_upload(self, image_url: str, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get cached upload result for an image URL.
        
        Args:
            image_url: URL of the image
            conversation_id: Conversation ID (for validation)
        
        Returns:
            Cached upload result or None if not found/expired
        """
        with self._lock:
            cache_key = self._generate_cache_key(image_url)
            
            if cache_key not in self._cache:
                return None
            
            entry = self._cache[cache_key]
            current_time = time.time()
            
            # Check if entry has expired
            if current_time - entry["timestamp"] > self.max_age_seconds:
                print(f"Cache entry for {image_url} has expired, removing")
                del self._cache[cache_key]
                self._save_cache()
                return None
            
            print(f"Found cached upload result for {image_url}")
            print(f"Cache entry age: {(current_time - entry['timestamp'])/3600:.1f} hours")
            
            return entry["upload_result"]
    
    def cache_upload_result(self, image_url: str, image_data: bytes, content_type: str, 
                          upload_result: Dict[str, Any], conversation_id: str) -> None:
        """
        Cache an upload result.
        
        Args:
            image_url: URL of the image
            image_data: Raw image data
            content_type: Image content type
            upload_result: Result from Shopee upload API
            conversation_id: Conversation ID used for upload
        """
        with self._lock:
            cache_key = self._generate_cache_key(image_url)
            file_hash = self._generate_file_hash(image_data)
            
            entry = {
                "image_url": image_url,
                "upload_result": upload_result,
                "timestamp": time.time(),
                "conversation_id": conversation_id,
                "file_hash": file_hash,
                "content_type": content_type,
                "file_size": len(image_data)
            }
            
            self._cache[cache_key] = entry
            
            print(f"Cached upload result for {image_url}")
            print(f"File hash: {file_hash}, Size: {len(image_data)} bytes")
            
            # Check cache size and cleanup if necessary
            self._enforce_cache_size_limit()
            
            # Save to disk
            self._save_cache()
    
    def _enforce_cache_size_limit(self) -> None:
        """Remove oldest entries if cache size exceeds limit."""
        total_size = sum(entry.get("file_size", 0) for entry in self._cache.values())
        
        if total_size <= self.max_cache_size_bytes:
            return
        
        print(f"Cache size ({total_size/1024/1024:.1f}MB) exceeds limit ({self.max_cache_size_bytes/1024/1024:.1f}MB)")
        
        # Sort entries by timestamp (oldest first)
        sorted_entries = sorted(self._cache.items(), key=lambda x: x[1]["timestamp"])
        
        # Remove oldest entries until we're under the limit
        for cache_key, entry in sorted_entries:
            if total_size <= self.max_cache_size_bytes:
                break
            
            file_size = entry.get("file_size", 0)
            del self._cache[cache_key]
            total_size -= file_size
            print(f"Removed cache entry for {entry['image_url']} ({file_size} bytes)")
        
        print(f"Cache size after cleanup: {total_size/1024/1024:.1f}MB")
    
    def _cleanup_expired_entries(self) -> None:
        """Remove expired cache entries."""
        current_time = time.time()
        expired_keys = []
        
        for cache_key, entry in self._cache.items():
            if current_time - entry["timestamp"] > self.max_age_seconds:
                expired_keys.append(cache_key)
        
        if expired_keys:
            print(f"Removing {len(expired_keys)} expired cache entries")
            for key in expired_keys:
                del self._cache[key]
            self._save_cache()
    
    def periodic_cleanup(self) -> None:
        """Perform periodic cleanup of expired entries and size enforcement."""
        print("Performing periodic image cache cleanup...")
        with self._lock:
            old_count = len(self._cache)
            self._cleanup_expired_entries()
            self._enforce_cache_size_limit()
            new_count = len(self._cache)
            
            if old_count != new_count:
                print(f"Cache cleanup completed: {old_count} -> {new_count} entries")
            else:
                print("Cache cleanup completed: no changes needed")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_size = sum(entry.get("file_size", 0) for entry in self._cache.values())
            current_time = time.time()
            
            # Count entries by age
            fresh_count = 0  # < 1 hour
            medium_count = 0  # 1-6 hours
            old_count = 0    # > 6 hours
            
            for entry in self._cache.values():
                age_hours = (current_time - entry["timestamp"]) / 3600
                if age_hours < 1:
                    fresh_count += 1
                elif age_hours < 6:
                    medium_count += 1
                else:
                    old_count += 1
            
            return {
                "total_entries": len(self._cache),
                "total_size_mb": round(total_size / 1024 / 1024, 2),
                "max_size_mb": round(self.max_cache_size_bytes / 1024 / 1024, 2),
                "max_age_hours": self.max_age_seconds / 3600,
                "age_distribution": {
                    "fresh_1h": fresh_count,
                    "medium_1_6h": medium_count,
                    "old_6h_plus": old_count
                },
                "cache_file": self.cache_file
            }
    
    def clear_cache(self) -> int:
        """Clear all cache entries. Returns number of entries cleared."""
        with self._lock:
            count = len(self._cache)
            self._cache.clear()
            self._save_cache()
            print(f"Cleared {count} cache entries")
            return count