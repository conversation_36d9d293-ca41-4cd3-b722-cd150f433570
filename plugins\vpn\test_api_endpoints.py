#!/usr/bin/env python3
"""
Test script to verify VPN API endpoints are working correctly
"""

import requests
import json
import sys
import os

def test_ssh_credentials_endpoint():
    """Test the SSH credentials endpoint"""
    print("Testing SSH credentials endpoint...")
    
    # Test data
    test_data = {
        "host": "127.0.0.1",
        "port": 22,
        "username": "test",
        "password": "test123"
    }
    
    # Make request
    url = "http://localhost:5000/admin/vpn/api/test-ssh-credentials"
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Message: {data.get('message')}")
            return True
        else:
            print(f"Error: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return False

def test_xray_service_endpoint():
    """Test the Xray service endpoint"""
    print("\nTesting Xray service endpoint...")
    
    # Test data
    test_data = {
        "host": "127.0.0.1",
        "port": 22,
        "username": "test",
        "password": "test123",
        "xray_config_path": "/etc/xray/config.json",
        "xray_service_name": "xray"
    }
    
    # Make request
    url = "http://localhost:5000/admin/vpn/api/test-xray-service"
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Message: {data.get('message')}")
            return True
        else:
            print(f"Error: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return False

def test_server_create_page():
    """Test if the server create page loads"""
    print("\nTesting server create page...")
    
    url = "http://localhost:5000/admin/vpn/servers/create"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("Page loaded successfully")
            # Check if the buttons are in the HTML
            if 'testSSHConnection' in response.text:
                print("✅ testSSHConnection function found in HTML")
            else:
                print("❌ testSSHConnection function NOT found in HTML")
                
            if 'testXrayService' in response.text:
                print("✅ testXrayService function found in HTML")
            else:
                print("❌ testXrayService function NOT found in HTML")
                
            return True
        else:
            print(f"Error: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return False

def main():
    """Main test function"""
    print("=== VPN API Endpoints Test ===")
    
    # Test endpoints
    ssh_test = test_ssh_credentials_endpoint()
    xray_test = test_xray_service_endpoint()
    page_test = test_server_create_page()
    
    print("\n=== Test Results ===")
    print(f"SSH Credentials Endpoint: {'✅ PASS' if ssh_test else '❌ FAIL'}")
    print(f"Xray Service Endpoint: {'✅ PASS' if xray_test else '❌ FAIL'}")
    print(f"Server Create Page: {'✅ PASS' if page_test else '❌ FAIL'}")
    
    if all([ssh_test, xray_test, page_test]):
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
