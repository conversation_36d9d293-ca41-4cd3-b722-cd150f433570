#!/usr/bin/env python3
"""
Setup script for Chat Commands Plugin

This script helps with the initial setup and configuration of the Chat Commands plugin.
"""

import os
import sys
import json
import requests
from typing import Dict, Any

def check_plugin_files():
    """Check if all required plugin files exist"""
    print("🔍 Checking plugin files...")
    
    plugin_dir = os.path.dirname(__file__)
    required_files = [
        '__init__.py',
        'plugin.py',
        'models.py',
        'services.py',
        'routes.py',
        'config.json',
        'commands.json'
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(plugin_dir, file)
        if not os.path.exists(file_path):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("✅ All plugin files present")
        return True

def check_steamcodetool_integration():
    """Check if SteamCodeTool can load the plugin"""
    print("\n🔍 Checking SteamCodeTool integration...")
    
    try:
        # Try to import the plugin
        sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
        from chat_commands.plugin import Plugin
        print("✅ Plugin can be imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Plugin import failed: {e}")
        return False

def check_shopee_api_connectivity():
    """Check if ShopeeAPI is accessible"""
    print("\n🔍 Checking ShopeeAPI connectivity...")
    
    possible_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://localhost:5000",
        "http://127.0.0.1:5000"
    ]
    
    for url in possible_urls:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ ShopeeAPI found at: {url}")
                return url
        except:
            continue
    
    print("⚠️  ShopeeAPI not detected (this is optional for setup)")
    return None

def check_vpn_api_connectivity():
    """Check if VPN API is accessible"""
    print("\n🔍 Checking VPN API connectivity...")
    
    try:
        response = requests.get("https://blueblue.api.limjianhui.com/openapi.json", timeout=10)
        if response.status_code == 200:
            print("✅ VPN API is accessible")
            return True
        else:
            print(f"⚠️  VPN API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"⚠️  VPN API not accessible: {e}")
        return False

def test_plugin_functionality():
    """Test basic plugin functionality"""
    print("\n🔍 Testing plugin functionality...")
    
    try:
        # Import and test the plugin components
        sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
        from chat_commands.services import ChatCommandService, MessageProcessor
        
        # Create a temporary test directory
        test_dir = os.path.join(os.path.dirname(__file__), 'test_setup')
        os.makedirs(test_dir, exist_ok=True)
        
        try:
            # Test service creation
            service = ChatCommandService(test_dir)
            commands = service.get_all_commands()
            print(f"✅ Service created with {len(commands)} default commands")
            
            # Test message processing
            processor = MessageProcessor(service)
            test_webhook = {
                'message': {
                    'content': {'text': '#help'},
                    'from_name': 'test-user',
                    'send_by_yourself': False
                },
                'conversation_id': 'test-conv'
            }
            
            responses = processor.process_webhook_message(test_webhook)
            if responses:
                print(f"✅ Message processing works ({len(responses)} responses generated)")
            else:
                print("❌ Message processing failed")
                return False
            
            return True
            
        finally:
            # Clean up test directory
            import shutil
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
                
    except Exception as e:
        print(f"❌ Plugin functionality test failed: {e}")
        return False

def generate_webhook_config(steamcodetool_url: str = "http://localhost:5000"):
    """Generate webhook configuration for ShopeeAPI"""
    print(f"\n📝 Generating webhook configuration...")
    
    webhook_config = {
        "webhook": {
            "enabled": True,
            "message_received": {
                "enabled": True,
                "url": f"{steamcodetool_url}/chat-commands/api/webhook",
                "retry_count": 3,
                "retry_delay": 5
            },
            "message_sent": {
                "enabled": False,
                "url": "",
                "retry_count": 3,
                "retry_delay": 5
            }
        }
    }
    
    print("📋 Add this to your ShopeeAPI config.json:")
    print(json.dumps(webhook_config, indent=2))
    
    return webhook_config

def show_next_steps():
    """Show next steps for completing the setup"""
    print("\n🚀 Next Steps:")
    print("1. Enable the Chat Commands plugin in SteamCodeTool admin interface")
    print("2. Configure ShopeeAPI webhooks (see configuration above)")
    print("3. Visit /chat-commands/ to manage commands and settings")
    print("4. Test the integration by sending a #help message")
    print("\n📚 Documentation:")
    print("- README.md - Plugin overview and features")
    print("- INTEGRATION_GUIDE.md - Detailed integration instructions")
    print("- CHAT_COMMANDS_PLUGIN_SUMMARY.md - Complete implementation summary")

def main():
    """Main setup function"""
    print("=" * 60)
    print("🔧 Chat Commands Plugin Setup")
    print("=" * 60)
    
    # Check plugin files
    if not check_plugin_files():
        print("\n❌ Setup failed: Missing plugin files")
        return 1
    
    # Check SteamCodeTool integration
    if not check_steamcodetool_integration():
        print("\n❌ Setup failed: Plugin integration issues")
        return 1
    
    # Check external services
    shopee_api_url = check_shopee_api_connectivity()
    vpn_api_ok = check_vpn_api_connectivity()
    
    # Test plugin functionality
    if not test_plugin_functionality():
        print("\n❌ Setup failed: Plugin functionality issues")
        return 1
    
    # Generate configuration
    steamcodetool_url = "http://localhost:5000"  # Default SteamCodeTool URL
    generate_webhook_config(steamcodetool_url)
    
    # Show next steps
    show_next_steps()
    
    print("\n" + "=" * 60)
    print("✅ Chat Commands Plugin setup completed successfully!")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    exit(main())
