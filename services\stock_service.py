import json
import os
import config

REDEEMED_STOCK_FILE = 'configs/data/redeemed_stock.json'

def get_all_stock():
    stock_items = []
    
    # Get limited stock items
    for item in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK:
        stock_items.append({
            'var_sku': item['sku'],
            'quantity': len(item['stock']),
            'is_unlimited': False
        })
    
    # Get unlimited stock items
    for item in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY:
        if item['sku'] not in [i['var_sku'] for i in stock_items]:
            stock_items.append({
                'var_sku': item['sku'],
                'quantity': 'Unlimited',
                'is_unlimited': True
            })
    
    return stock_items

def get_stock_item(var_sku, order_sn=None):
    # First, check if the order has already been redeemed
    if order_sn:
        redeemed_stock = load_redeemed_stock()
        if order_sn in redeemed_stock:
            # 修改这里，返回完整的 item 信息
            for item in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY:
                if item['sku'] == var_sku:
                    return {
                        **item,  # 包含原始商品信息（message 等）
                        'item': redeemed_stock[order_sn]['item'],
                        'status': 'already_redeemed'
                    }
            return None

    # If not already redeemed, proceed with finding the stock item
    for item in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY:
        if item['sku'] == var_sku:
            return item
    return None

def update_stock(var_sku, order_sn):
    redeemed_stock = load_redeemed_stock()
    
    # 检查是否已经兑换过
    if order_sn in redeemed_stock:
        return {'item': redeemed_stock[order_sn]['item'], 'status': 'already_redeemed'}

    for item in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK:
        if item['sku'] == var_sku:
            if item['stock']:
                redeemed_item = item['stock'].pop(0)
                redeemed_stock[order_sn] = {
                    'var_sku': var_sku,
                    'item': redeemed_item
                }
                save_redeemed_stock(redeemed_stock)
                save_updated_config()
                return {'item': redeemed_item, 'status': 'success'}
            else:
                return {'status': 'out_of_stock'}
    return {'status': 'sku_not_found'}

def load_redeemed_stock():
    if os.path.exists(REDEEMED_STOCK_FILE):
        try:
            with open(REDEEMED_STOCK_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            # 如果文件为空或格式不正确，返回空字典
            return {}
    return {}

def save_redeemed_stock(redeemed_stock):
    with open(REDEEMED_STOCK_FILE, 'w', encoding='utf-8') as f:
        json.dump(redeemed_stock, f, indent=2, ensure_ascii=False)

def save_updated_config():
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)

    config_data['AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK'] = config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK

    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)