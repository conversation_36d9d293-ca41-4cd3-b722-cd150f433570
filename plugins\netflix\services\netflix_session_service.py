"""
Netflix Session Service
Handles Netflix session management and cooldowns
"""

import logging
import time
import json
import os
from typing import Dict, Any, Optional
from threading import Lock
from datetime import datetime

logger = logging.getLogger(__name__)

class NetflixSessionService:
    """Service for managing Netflix sessions and cooldowns"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.session_config = config.get('session_config', {})
        
        self.cooldown_time = self.session_config.get('cooldown_time', 600)
        self.request_timeout = self.session_config.get('request_timeout', 30)
        self.max_concurrent = self.session_config.get('max_concurrent_requests', 3)
        
        self.sessions = {}
        self.active_requests = set()
        self.lock = Lock()
        
        # Load existing sessions
        self.sessions_file = 'data/netflix/netflix_sessions.json'
        self.load_sessions()
        
    def update_config(self, config: Dict[str, Any]):
        """Update service configuration"""
        self.config = config
        self.session_config = config.get('session_config', {})
        
        self.cooldown_time = self.session_config.get('cooldown_time', 600)
        self.request_timeout = self.session_config.get('request_timeout', 30)
        self.max_concurrent = self.session_config.get('max_concurrent_requests', 3)
        
    def load_sessions(self):
        """Load sessions from file"""
        try:
            os.makedirs(os.path.dirname(self.sessions_file), exist_ok=True)
            if os.path.exists(self.sessions_file):
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    self.sessions = json.load(f)
                logger.info(f"Loaded {len(self.sessions)} Netflix sessions")
            else:
                self.sessions = {}
                self.save_sessions()
        except Exception as e:
            logger.error(f"Error loading Netflix sessions: {e}")
            self.sessions = {}

    def save_sessions(self):
        """Save sessions to file"""
        try:
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump(self.sessions, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving Netflix sessions: {e}")
            
    def create_session(self, order_id: str, account_email: str, signin_code: str):
        """Create a new Netflix session"""
        with self.lock:
            session_data = {
                'order_id': order_id,
                'account_email': account_email,
                'signin_code': signin_code,
                'created_at': datetime.now().isoformat(),
                'last_accessed': datetime.now().isoformat(),
                'status': 'active'
            }
            
            self.sessions[order_id] = session_data
            self.save_sessions()
            logger.info(f"Created Netflix session for order {order_id}")
            
    def get_session(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get session by order ID"""
        with self.lock:
            session = self.sessions.get(order_id)
            if session:
                # Update last accessed time
                session['last_accessed'] = datetime.now().isoformat()
                self.save_sessions()
            return session
            
    def is_in_cooldown(self, order_id: str) -> bool:
        """Check if order is in cooldown"""
        with self.lock:
            session = self.sessions.get(order_id)
            if not session:
                return False
                
            last_request = session.get('last_request_time', 0)
            return (time.time() - last_request) < self.cooldown_time
            
    def get_cooldown_remaining(self, order_id: str) -> int:
        """Get remaining cooldown time in seconds"""
        with self.lock:
            session = self.sessions.get(order_id)
            if not session:
                return 0
                
            last_request = session.get('last_request_time', 0)
            elapsed = time.time() - last_request
            remaining = max(0, self.cooldown_time - elapsed)
            return int(remaining)
            
    def set_cooldown(self, order_id: str):
        """Set cooldown for order"""
        with self.lock:
            if order_id in self.sessions:
                self.sessions[order_id]['last_request_time'] = time.time()
                self.save_sessions()
                
    def can_process_request(self, order_id: str) -> bool:
        """Check if request can be processed"""
        with self.lock:
            # Check cooldown
            if self.is_in_cooldown(order_id):
                return False
                
            # Check concurrent requests limit
            if len(self.active_requests) >= self.max_concurrent:
                return False
                
            # Check if already processing
            if order_id in self.active_requests:
                return False
                
            return True
            
    def start_request(self, order_id: str) -> bool:
        """Start processing a request"""
        with self.lock:
            if not self.can_process_request(order_id):
                return False
                
            self.active_requests.add(order_id)
            return True
            
    def finish_request(self, order_id: str):
        """Finish processing a request"""
        with self.lock:
            self.active_requests.discard(order_id)
            self.set_cooldown(order_id)
            
    def get_all_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all sessions"""
        with self.lock:
            return self.sessions.copy()
            
    def clear_session(self, order_id: str):
        """Clear a specific session"""
        with self.lock:
            if order_id in self.sessions:
                del self.sessions[order_id]
                self.save_sessions()
                logger.info(f"Cleared Netflix session for order {order_id}")
                
    def clear_all_sessions(self):
        """Clear all sessions"""
        with self.lock:
            self.sessions.clear()
            self.active_requests.clear()
            self.save_sessions()
            logger.info("Cleared all Netflix sessions")
            
    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Cleanup old sessions"""
        with self.lock:
            current_time = datetime.now()
            to_remove = []
            
            for order_id, session in self.sessions.items():
                try:
                    created_at = datetime.fromisoformat(session['created_at'])
                    age_hours = (current_time - created_at).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        to_remove.append(order_id)
                except Exception as e:
                    logger.error(f"Error checking session age for {order_id}: {e}")
                    to_remove.append(order_id)
                    
            for order_id in to_remove:
                del self.sessions[order_id]
                
            if to_remove:
                self.save_sessions()
                logger.info(f"Cleaned up {len(to_remove)} old Netflix sessions")
                
    def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics"""
        with self.lock:
            return {
                'total_sessions': len(self.sessions),
                'active_requests': len(self.active_requests),
                'cooldown_time': self.cooldown_time,
                'max_concurrent': self.max_concurrent
            }
            
    def cleanup(self):
        """Cleanup service resources"""
        self.save_sessions()
        logger.info("Netflix session service cleanup completed")
