"""
Order Data Factory

This module provides the OrderDataFactory class for generating complete Shopee API
order structures with realistic buyer information, order items, and metadata.
"""

import json
import os
import random
import string
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Set
import logging

logger = logging.getLogger(__name__)

@dataclass
class BuyerConfig:
    """Configuration for buyer information generation"""
    username: str = ""
    name: str = ""
    phone: str = ""
    email: str = ""
    address: str = ""
    
@dataclass
class OrderItemConfig:
    """Configuration for order item generation"""
    var_sku: str
    product_name: str
    quantity: int = 1
    price: float = 0.0
    category: str = "digital"
    metadata: Dict[str, Any] = field(default_factory=dict)

class OrderDataFactory:
    """Factory for creating complete Shopee API order structures"""
    
    def __init__(self):
        self.malaysian_names = [
            "<PERSON>", "<PERSON><PERSON>ur<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"
        ]
        
        self.malaysian_addresses = [
            "Jalan Bukit Bintang, 55100 Kuala Lumpur",
            "Lorong Maarof, 59000 Kuala Lumpur", 
            "Jalan Telawi, 59100 Bangsar, Kuala Lumpur",
            "Persiaran Gurney, 10250 George Town, Pulau Pinang",
            "Jalan Tun Razak, 50400 Kuala Lumpur",
            "Jalan Sultan Ismail, 50250 Kuala Lumpur",
            "Jalan Ampang, 50450 Kuala Lumpur",
            "Jalan Petaling, 50000 Kuala Lumpur"
        ]
        
        self.phone_prefixes = ["+6012", "+6013", "+6014", "+6016", "+6017", "+6018", "+6019"]
        
        # Order ID tracking for conflict prevention
        self.fake_orders_file = 'configs/data/fake_orders.json'
        self.order_id_cache: Set[str] = set()
        self._ensure_data_files()
        self._load_existing_order_ids()
    
    def _ensure_data_files(self):
        """Ensure required data files exist"""
        os.makedirs('configs/data', exist_ok=True)
        
        if not os.path.exists(self.fake_orders_file):
            with open(self.fake_orders_file, 'w', encoding='utf-8') as f:
                json.dump([], f, indent=2)
    
    def _load_existing_order_ids(self):
        """Load existing order IDs into cache for conflict detection"""
        try:
            with open(self.fake_orders_file, 'r', encoding='utf-8') as f:
                fake_orders = json.load(f)
            
            # Extract order SNs from existing fake orders
            for order in fake_orders:
                if isinstance(order, dict):
                    order_sn = order.get('order_sn')
                    if order_sn:
                        self.order_id_cache.add(order_sn)
                    
                    # Also check full order data if available
                    full_data = order.get('full_order_data', {})
                    if isinstance(full_data, dict) and 'data' in full_data:
                        nested_order_sn = full_data['data'].get('order_sn')
                        if nested_order_sn:
                            self.order_id_cache.add(nested_order_sn)
            
            logger.info(f"Loaded {len(self.order_id_cache)} existing order IDs for conflict detection")
            
        except (FileNotFoundError, json.JSONDecodeError, Exception) as e:
            logger.warning(f"Could not load existing order IDs: {e}")
            self.order_id_cache = set()
    
    def generate_unique_order_id(self, prefix: str = "FAKE_", max_attempts: int = 100) -> str:
        """Generate unique order ID with "FAKE_" prefix and conflict prevention"""
        
        for attempt in range(max_attempts):
            # Generate different patterns for variety
            if attempt < 30:
                # Pattern 1: FAKE_ + date + random alphanumeric (similar to Shopee format)
                date_part = datetime.now().strftime("%y%m%d")
                random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
                order_id = f"{prefix}{date_part}{random_part}"
            elif attempt < 60:
                # Pattern 2: FAKE_ + timestamp + random
                timestamp_part = str(int(datetime.now().timestamp()))[-8:]  # Last 8 digits
                random_part = ''.join(random.choices(string.ascii_uppercase, k=6))
                order_id = f"{prefix}{timestamp_part}{random_part}"
            else:
                # Pattern 3: FAKE_ + full timestamp (fallback for high conflict scenarios)
                timestamp_part = str(int(datetime.now().timestamp() * 1000))  # Include milliseconds
                random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
                order_id = f"{prefix}{timestamp_part}{random_part}"
            
            # Check for conflicts
            if not self._order_id_exists(order_id):
                # Add to cache to prevent future conflicts
                self.order_id_cache.add(order_id)
                logger.debug(f"Generated unique order ID: {order_id} (attempt {attempt + 1})")
                return order_id
        
        # Ultimate fallback with microsecond precision
        fallback_id = f"{prefix}{int(datetime.now().timestamp() * 1000000)}"
        logger.warning(f"Used fallback order ID generation: {fallback_id}")
        self.order_id_cache.add(fallback_id)
        return fallback_id
    
    def _order_id_exists(self, order_id: str) -> bool:
        """Check if order ID already exists (including real orders check)"""
        # Check in our cache first (fastest)
        if order_id in self.order_id_cache:
            return True
        
        # Check against real orders if order service is available
        try:
            # Import here to avoid circular imports
            from .order_service import order_service
            
            # Check if this looks like a real Shopee order ID that might exist
            if not order_id.startswith("FAKE_"):
                # For non-fake IDs, do a more thorough check
                existing_order = order_service.get_order_details(order_id)
                if existing_order and existing_order.get('data'):
                    logger.warning(f"Order ID {order_id} conflicts with existing real order")
                    return True
        except ImportError:
            logger.debug("Order service not available for conflict checking")
        except Exception as e:
            logger.debug(f"Could not check real orders for conflicts: {e}")
        
        return False
    
    def generate_alternative_order_id(self, base_id: str, conflict_count: int = 0) -> str:
        """Generate alternative order ID when conflicts are detected"""
        if conflict_count == 0:
            # First alternative: add suffix
            alternative = f"{base_id}_ALT"
        elif conflict_count < 5:
            # Subsequent alternatives: add incremental suffix
            alternative = f"{base_id}_ALT{conflict_count}"
        else:
            # Many conflicts: generate completely new ID
            return self.generate_unique_order_id()
        
        # Check if alternative also conflicts
        if self._order_id_exists(alternative):
            return self.generate_alternative_order_id(base_id, conflict_count + 1)
        
        self.order_id_cache.add(alternative)
        return alternative
    
    def validate_order_id_format(self, order_id: str) -> Dict[str, Any]:
        """Validate order ID format and provide suggestions"""
        errors = []
        warnings = []
        suggestions = []
        
        # Check if it's a fake order ID
        if not order_id.startswith("FAKE_"):
            warnings.append("Order ID does not start with 'FAKE_' prefix")
            suggestions.append("Consider using 'FAKE_' prefix for test orders")
        
        # Check length (Shopee order IDs are typically 12-15 characters)
        if len(order_id) < 8:
            errors.append("Order ID is too short (minimum 8 characters)")
        elif len(order_id) > 25:
            warnings.append("Order ID is quite long (consider shorter format)")
        
        # Check for invalid characters
        valid_chars = set(string.ascii_letters + string.digits + "_-")
        invalid_chars = set(order_id) - valid_chars
        if invalid_chars:
            errors.append(f"Order ID contains invalid characters: {invalid_chars}")
        
        # Check for conflicts
        conflict_exists = self._order_id_exists(order_id)
        if conflict_exists:
            errors.append("Order ID already exists")
            suggestions.append("Use generate_unique_order_id() to avoid conflicts")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "suggestions": suggestions,
            "has_conflicts": conflict_exists,
            "is_fake_order": order_id.startswith("FAKE_")
        }
        
    def create_shopee_order_structure(self, order_sn: str, order_items: List[OrderItemConfig], 
                                    buyer_config: Optional[BuyerConfig] = None,
                                    order_status: str = "To Ship",
                                    payment_status: str = "paid",
                                    custom_timestamps: Optional[Dict[str, str]] = None,
                                    custom_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create complete Shopee API order structure"""
        
        # Generate buyer information
        buyer_info = self.generate_buyer_info(buyer_config)
        
        # Generate order items data
        items_data = self.generate_order_items(order_items)
        
        # Calculate total price
        total_price = sum(item["total_price"] for item in items_data)
        
        # Generate timestamps
        timestamps = self.generate_timestamps(custom_timestamps, order_status)
        
        # Generate order metadata
        metadata = self.generate_order_metadata(custom_metadata or {}, order_items)
        
        # Create complete order structure matching Shopee API format
        order_structure = {
            "message": "Order details fetched successfully",
            "data": {
                "order_sn": order_sn,
                "buyer_user": {
                    "user_name": buyer_info["username"]
                },
                "order_items": items_data,
                "total_price": total_price,
                "buyer_address_name": buyer_info["name"],
                "buyer_address_phone": buyer_info["phone"],
                "buyer_address_email": buyer_info["email"],
                "buyer_address_full": buyer_info["address"],
                "create_time": timestamps["create_time"],
                "update_time": timestamps["update_time"],
                "pay_time": timestamps["pay_time"],
                "ship_time": timestamps.get("ship_time"),
                "status": order_status,
                "payment_status": payment_status,
                "order_status": order_status,
                "is_fake_order": True,
                "fake_order_metadata": {
                    "created_by": "order_data_factory",
                    "factory_version": "1.0",
                    "generated_at": datetime.now().isoformat(),
                    "buyer_generation_method": "realistic_malaysian_data",
                    "order_type": "fake_test_order",
                    **metadata
                }
            }
        }
        
        logger.info(f"Generated Shopee order structure for order_sn: {order_sn}")
        return order_structure
    
    def generate_buyer_info(self, config: Optional[BuyerConfig] = None) -> Dict[str, str]:
        """Generate realistic buyer information with Malaysian context"""
        
        if config and all([config.username, config.name, config.phone, config.email, config.address]):
            # Use provided configuration
            return {
                "username": config.username,
                "name": config.name,
                "phone": config.phone,
                "email": config.email,
                "address": config.address
            }
        
        # Generate realistic Malaysian buyer data
        name = config.name if config and config.name else random.choice(self.malaysian_names)
        username = config.username if config and config.username else self._generate_username(name)
        phone = config.phone if config and config.phone else self._generate_malaysian_phone()
        email = config.email if config and config.email else self._generate_email(username)
        address = config.address if config and config.address else random.choice(self.malaysian_addresses)
        
        return {
            "username": username,
            "name": name,
            "phone": phone,
            "email": email,
            "address": address
        }
    
    def _generate_username(self, name: str) -> str:
        """Generate realistic username from name"""
        # Convert name to username format
        name_parts = name.lower().replace(" ", "").replace(".", "")
        # Add some random numbers
        random_suffix = ''.join(random.choices(string.digits, k=random.randint(2, 4)))
        return f"{name_parts}{random_suffix}"
    
    def _generate_malaysian_phone(self) -> str:
        """Generate realistic Malaysian phone number"""
        prefix = random.choice(self.phone_prefixes)
        # Generate 7-8 digit number
        number_length = random.choice([7, 8])
        number = ''.join(random.choices(string.digits, k=number_length))
        return f"{prefix}{number}"
    
    def _generate_email(self, username: str) -> str:
        """Generate realistic email address"""
        domains = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "live.com"]
        domain = random.choice(domains)
        return f"{username}@{domain}"
    
    def generate_order_items(self, items_config: List[OrderItemConfig]) -> List[Dict[str, Any]]:
        """Generate order items with proper product details"""
        items_data = []
        
        for item_config in items_config:
            # Calculate item total price
            item_total = item_config.price * item_config.quantity
            
            item_data = {
                "var_sku": item_config.var_sku,
                "product": {
                    "name": item_config.product_name,
                    "category": item_config.category
                },
                "item_model": {
                    "sku": item_config.var_sku,
                    "name": item_config.product_name
                },
                "quantity": item_config.quantity,
                "price": item_config.price,
                "total_price": item_total,
                "currency": "MYR",
                "item_status": "normal",
                "product_metadata": item_config.metadata
            }
            
            items_data.append(item_data)
        
        return items_data
    
    def generate_timestamps(self, custom_timestamps: Optional[Dict[str, str]] = None,
                          order_status: str = "To Ship") -> Dict[str, str]:
        """Generate realistic timestamps for order lifecycle with status-aware progression"""
        now = datetime.now()
        
        # Generate base timestamps with realistic intervals
        create_time = now - timedelta(hours=random.randint(1, 72))  # Order created 1-72 hours ago
        
        timestamps = {
            "create_time": create_time.isoformat(),
            "order_created_timestamp": int(create_time.timestamp()),
        }
        
        # Generate payment timestamp (usually within minutes to hours after creation)
        if random.choice([True, True, True, False]):  # 75% chance of payment
            pay_time = create_time + timedelta(minutes=random.randint(5, 480))  # 5 mins to 8 hours
            timestamps.update({
                "pay_time": pay_time.isoformat(),
                "payment_timestamp": int(pay_time.timestamp()),
                "payment_confirmed_time": (pay_time + timedelta(minutes=random.randint(1, 15))).isoformat()
            })
        else:
            pay_time = create_time  # Use create_time as fallback
        
        # Generate update timestamp (usually after payment)
        update_time = pay_time + timedelta(minutes=random.randint(1, 60))
        timestamps.update({
            "update_time": update_time.isoformat(),
            "last_update_timestamp": int(update_time.timestamp())
        })
        
        # Status-specific timestamps
        if order_status in ["Shipped", "To Receive", "Completed"]:
            ship_time = pay_time + timedelta(hours=random.randint(2, 48))
            timestamps.update({
                "ship_time": ship_time.isoformat(),
                "shipping_timestamp": int(ship_time.timestamp()),
                "tracking_updated_time": (ship_time + timedelta(hours=random.randint(1, 12))).isoformat()
            })
            
            if order_status in ["To Receive", "Completed"]:
                delivery_time = ship_time + timedelta(days=random.randint(1, 7))
                timestamps.update({
                    "delivery_time": delivery_time.isoformat(),
                    "delivery_timestamp": int(delivery_time.timestamp())
                })
                
                if order_status == "Completed":
                    complete_time = delivery_time + timedelta(hours=random.randint(1, 24))
                    timestamps.update({
                        "complete_time": complete_time.isoformat(),
                        "completion_timestamp": int(complete_time.timestamp()),
                        "auto_complete_time": (complete_time + timedelta(days=7)).isoformat()
                    })
        
        # Add processing timestamps for digital products
        if random.choice([True, False]):  # 50% chance of processing timestamps
            processing_start = pay_time + timedelta(minutes=random.randint(1, 30))
            processing_complete = processing_start + timedelta(minutes=random.randint(1, 15))
            timestamps.update({
                "processing_start_time": processing_start.isoformat(),
                "processing_complete_time": processing_complete.isoformat(),
                "fulfillment_timestamp": int(processing_complete.timestamp())
            })
        
        # Override with custom timestamps if provided
        if custom_timestamps:
            timestamps.update(custom_timestamps)
        
        return timestamps
    
    def create_custom_timestamp_config(self, base_time: Optional[datetime] = None,
                                     order_age_hours: int = 24,
                                     payment_delay_minutes: int = 30,
                                     processing_delay_minutes: int = 15,
                                     shipping_delay_hours: int = 12) -> Dict[str, str]:
        """Create custom timestamp configuration for specific testing scenarios"""
        if base_time is None:
            base_time = datetime.now() - timedelta(hours=order_age_hours)
        
        create_time = base_time
        pay_time = create_time + timedelta(minutes=payment_delay_minutes)
        processing_time = pay_time + timedelta(minutes=processing_delay_minutes)
        ship_time = processing_time + timedelta(hours=shipping_delay_hours)
        
        return {
            "create_time": create_time.isoformat(),
            "pay_time": pay_time.isoformat(),
            "update_time": processing_time.isoformat(),
            "processing_start_time": (pay_time + timedelta(minutes=5)).isoformat(),
            "processing_complete_time": processing_time.isoformat(),
            "ship_time": ship_time.isoformat(),
            "order_created_timestamp": int(create_time.timestamp()),
            "payment_timestamp": int(pay_time.timestamp()),
            "fulfillment_timestamp": int(processing_time.timestamp()),
            "shipping_timestamp": int(ship_time.timestamp())
        }
    
    def generate_order_metadata(self, custom_metadata: Dict[str, Any], 
                              order_items: Optional[List[OrderItemConfig]] = None) -> Dict[str, Any]:
        """Generate comprehensive order metadata with fake order markers and enhanced tracking"""
        
        # Base metadata with fake order identification
        base_metadata = {
            # Fake order identification
            "test_scenario": "general_testing",
            "generation_method": "order_data_factory",
            "data_source": "synthetic",
            "order_type": "fake_test_order",
            "is_test_data": True,
            
            # Generation details
            "factory_version": "1.0",
            "generated_at": datetime.now().isoformat(),
            "generation_timestamp": int(datetime.now().timestamp()),
            
            # Data characteristics
            "buyer_data_type": "realistic_malaysian",
            "order_complexity": "standard",
            "validation_status": "generated",
            "data_quality": "synthetic_high_fidelity",
            
            # Testing context
            "testing_purpose": "plugin_integration_testing",
            "environment": "development",
            "automation_friendly": True,
            
            # Tracking and identification
            "fake_order_marker": "FAKE_ORDER_SYSTEM_V1",
            "system_generated": True,
            "requires_cleanup": True,
            "cleanup_eligible_after": (datetime.now() + timedelta(days=7)).isoformat()
        }
        
        # Add product-specific metadata if order items provided
        if order_items:
            product_categories = list(set(item.category for item in order_items))
            product_skus = [item.var_sku for item in order_items]
            total_items = sum(item.quantity for item in order_items)
            
            base_metadata.update({
                "product_categories": product_categories,
                "product_skus": product_skus,
                "total_item_count": total_items,
                "multi_product_order": len(product_skus) > 1,
                "digital_products_only": all(item.category in ["gaming", "streaming", "design", "security"] for item in order_items)
            })
        
        # Add randomized testing metadata
        test_scenarios = [
            "basic_order_processing", "payment_flow_testing", "plugin_integration",
            "error_handling_test", "performance_testing", "ui_testing",
            "api_endpoint_testing", "webhook_testing", "notification_testing"
        ]
        
        base_metadata.update({
            "suggested_test_scenarios": random.sample(test_scenarios, random.randint(2, 4)),
            "test_priority": random.choice(["low", "medium", "high"]),
            "expected_processing_time_seconds": random.randint(5, 300),
            "complexity_score": random.randint(1, 10)
        })
        
        # Add system integration metadata
        base_metadata.update({
            "shopee_api_compatible": True,
            "plugin_system_ready": True,
            "webhook_safe": True,
            "audit_log_safe": True,
            "metrics_excluded": True,  # Should be excluded from production metrics
            "reporting_excluded": True  # Should be excluded from business reports
        })
        
        # Merge with custom metadata (custom metadata takes precedence)
        base_metadata.update(custom_metadata)
        
        return base_metadata
    
    def create_batch_orders(self, base_configs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create multiple order structures efficiently"""
        orders = []
        
        for i, config in enumerate(base_configs):
            try:
                # Extract configuration
                order_sn = config.get("order_sn", f"FAKE_BATCH_{i:04d}")
                items_config = []
                
                # Convert items configuration
                for item in config.get("items", []):
                    items_config.append(OrderItemConfig(
                        var_sku=item["var_sku"],
                        product_name=item["product_name"],
                        quantity=item.get("quantity", 1),
                        price=item.get("price", 0.0),
                        category=item.get("category", "digital"),
                        metadata=item.get("metadata", {})
                    ))
                
                # Create buyer config if provided
                buyer_config = None
                if "buyer" in config:
                    buyer_data = config["buyer"]
                    buyer_config = BuyerConfig(
                        username=buyer_data.get("username", ""),
                        name=buyer_data.get("name", ""),
                        phone=buyer_data.get("phone", ""),
                        email=buyer_data.get("email", ""),
                        address=buyer_data.get("address", "")
                    )
                
                # Generate order structure
                order_structure = self.create_shopee_order_structure(
                    order_sn=order_sn,
                    order_items=items_config,
                    buyer_config=buyer_config,
                    order_status=config.get("status", "To Ship"),
                    payment_status=config.get("payment_status", "paid"),
                    custom_timestamps=config.get("timestamps"),
                    custom_metadata=config.get("metadata", {})
                )
                
                orders.append(order_structure)
                
            except Exception as e:
                logger.error(f"Failed to create order structure for batch item {i}: {e}")
                # Continue with other orders instead of failing completely
                continue
        
        logger.info(f"Generated {len(orders)} order structures from {len(base_configs)} configurations")
        return orders
    
    def validate_order_structure(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate generated order structure against Shopee API format"""
        errors = []
        warnings = []
        
        # Check required top-level fields
        required_fields = ["message", "data"]
        for field in required_fields:
            if field not in order_data:
                errors.append(f"Missing required field: {field}")
        
        if "data" in order_data:
            data = order_data["data"]
            
            # Check required data fields
            required_data_fields = [
                "order_sn", "buyer_user", "order_items", "total_price",
                "buyer_address_name", "buyer_address_phone", "create_time", "status"
            ]
            
            for field in required_data_fields:
                if field not in data:
                    errors.append(f"Missing required data field: {field}")
            
            # Validate order items structure
            if "order_items" in data and isinstance(data["order_items"], list):
                for i, item in enumerate(data["order_items"]):
                    required_item_fields = ["var_sku", "product", "quantity", "price"]
                    for field in required_item_fields:
                        if field not in item:
                            errors.append(f"Missing required field '{field}' in order item {i}")
            
            # Check fake order markers
            if not data.get("is_fake_order"):
                warnings.append("Order is not marked as fake order")
            
            if "fake_order_metadata" not in data:
                warnings.append("Missing fake order metadata")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }


# Global factory instance
order_data_factory = OrderDataFactory()