"""
Example usage of the ShopeeAPI client.
"""
import json
import os

# Use relative import when run from inside the package
try:
    from . import ShopeeAPI
# Use absolute import when run as a script
except ImportError:
    from ShopeeAPI import ShopeeAP<PERSON>


def print_json(data):
    """Pretty print JSON data"""
    print(json.dumps(data, indent=2))


def main():
    """Example of using the ShopeeAPI client."""

    # Get credentials from config file if it exists, or use environment variables
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')

    authorization_code = os.environ.get('SHOPEE_AUTHORIZATION_CODE', '')
    cookie = os.environ.get('SHOPEE_COOKIE', '')
    shop_id = os.environ.get('SHOPEE_SHOP_ID', '')
    region_id = os.environ.get('SHOPEE_REGION_ID', 'MY')

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                authorization_code = config.get('AUTHORIZATION_CODE', authorization_code)
                cookie = config.get('COOKIE', cookie)
                shop_id = config.get('SHOP_ID', shop_id)
                region_id = config.get('REGION_ID', region_id)
        except json.JSONDecodeError:
            print(f"Error: Could not parse {config_path}")

    # Initialize API
    shop_id = int(shop_id) if shop_id else None
    api = ShopeeAPI(
        authorization_code=authorization_code,
        cookie=cookie,
        shop_id=shop_id,
        region_id=region_id
    )

    # Example: Get to-ship orders
    print("Getting to-ship orders...")
    to_ship_orders = api.get_to_ship_orders()
    print(f"Found {len(to_ship_orders.get('data', {}).get('card_list', []))} to-ship orders")

    # Example: Get shipped orders
    print("\nGetting shipped orders...")
    shipped_orders = api.get_shipped_orders()
    print(f"Found {len(shipped_orders.get('data', {}).get('card_list', []))} shipped orders")

    # Example: Get completed orders
    print("\nGetting completed orders...")
    completed_orders = api.get_completed_orders()
    print(f"Found {len(completed_orders.get('data', {}).get('card_list', []))} completed orders")

    # Example: Get recent conversations
    print("\nGetting recent conversations...")
    conversations_response, status_code = api.get_recent_conversations()
    if status_code == 200:
        conversations = conversations_response
        print(f"Found {len(conversations)} recent conversations")
    else:
        print(f"Error getting conversations: {conversations_response.get('error')}")

    # Example: Get recent messages
    print("\nGetting recent messages...")
    messages = api.get_recent_latest_messages()
    print(f"Found {len(messages)} recent messages")

    # Print the first message if available
    if messages:
        print("\nFirst message:")
        print(f"From: {messages[0].get('to_name')}")
        print(f"Content: {messages[0].get('latest_message_content')}")

    # Example: Send a chat message (uncomment to test)
    """
    print("\nSending a chat message...")
    message_payload = {
        "text": "Hello from ShopeeAPI!",
        "username": "customer_username",  # Replace with actual username
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    response, status_code = api.send_chat_message(message_payload)
    if status_code == 200:
        print("Message sent successfully!")
        if response.get('message_parts_sent', 1) > 1:
            print(f"Message was split into {response['message_parts_sent']} parts")
    else:
        print(f"Error sending message: {response.get('error')}")
    """

    # Example: Send a long chat message (automatically split)
    """
    print("\nSending a long chat message...")
    long_message = "Dear customer, thank you for your order. " * 20  # Creates a message > 600 chars
    message_payload = {
        "text": long_message,
        "username": "customer_username",  # Replace with actual username
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    response, status_code = api.send_chat_message(message_payload)
    if status_code == 200:
        print("Long message sent successfully!")
        if response.get('message_parts_sent', 1) > 1:
            print(f"Message was automatically split into {response['message_parts_sent']} parts")
    else:
        print(f"Error sending long message: {response.get('error')}")
    """


if __name__ == "__main__":
    main()