#!/usr/bin/env python3
"""
Test script for credential expiration email notification functionality.
"""
import json
import time
from datetime import datetime, timedelta
from core.auth import Credential<PERSON>anager

def test_credential_expiration_email():
    """Test the email notification when credentials expire."""
    
    print("🧪 Testing Credential Expiration Email Notification")
    print("=" * 60)
    
    # Test email configuration
    email_config = {
        'from_email': '<EMAIL>',
        'from_password': 'test_password',
        'to_email': '<EMAIL>'
    }
    
    # Create a mock cookie with an expired SPC_STK token
    expired_time = time.time() - 3600  # 1 hour ago
    mock_cookie_json = [
        {
            'name': 'SPC_STK',
            'value': 'mock_expired_token',
            'expirationDate': expired_time
        },
        {
            'name': 'SPC_EC',
            'value': 'mock_ec_token'
        }
    ]
    
    # Test 1: Credential manager with expired cookie and email config
    print("\n1️⃣ Testing with expired cookie and email config...")
    credential_manager = CredentialManager(
        authorization_code="Bearer mock_token",
        cookie=mock_cookie_json,
        email_config=email_config
    )
    
    # This should detect expired credentials and attempt to send email
    # (Note: Email won't actually send with test credentials)
    is_valid = credential_manager.validate_credentials()
    print(f"   ✅ Credentials valid: {is_valid}")
    print(f"   📧 Email config set: {credential_manager.email_service is not None}")
    
    # Test 2: Credential manager without email config
    print("\n2️⃣ Testing with expired cookie but no email config...")
    credential_manager_no_email = CredentialManager(
        authorization_code="Bearer mock_token",
        cookie=mock_cookie_json
    )
    
    is_valid = credential_manager_no_email.validate_credentials()
    print(f"   ✅ Credentials valid: {is_valid}")
    print(f"   📧 Email service: {credential_manager_no_email.email_service is not None}")
    
    # Test 3: Valid credentials
    print("\n3️⃣ Testing with valid (non-expired) credentials...")
    future_time = time.time() + 3600  # 1 hour from now
    valid_cookie_json = [
        {
            'name': 'SPC_STK',
            'value': 'mock_valid_token',
            'expirationDate': future_time
        },
        {
            'name': 'SPC_EC',
            'value': 'mock_ec_token'
        }
    ]
    
    credential_manager_valid = CredentialManager(
        authorization_code="Bearer mock_token",
        cookie=valid_cookie_json,
        email_config=email_config
    )
    
    is_valid = credential_manager_valid.validate_credentials()
    print(f"   ✅ Credentials valid: {is_valid}")
    print(f"   📧 Email config set: {credential_manager_valid.email_service is not None}")
    
    # Test 4: Email cooldown functionality
    print("\n4️⃣ Testing email cooldown functionality...")
    credential_manager._last_expiration_email_time = datetime.now() - timedelta(minutes=30)
    
    # This should be blocked by cooldown
    is_valid = credential_manager.validate_credentials()
    print(f"   ✅ Credentials valid: {is_valid}")
    print(f"   ⏰ Last email time: {credential_manager._last_expiration_email_time}")
    
    print("\n" + "=" * 60)
    print("✅ Test completed! Check the output above for results.")
    print("\n📝 Next steps:")
    print("   1. Configure EMAIL_SENDER in your config.json file")
    print("   2. Update your credentials when they expire")
    print("   3. Monitor email notifications for expiration alerts")

if __name__ == "__main__":
    test_credential_expiration_email()