/**
 * Core Statistics Widget
 * Displays email configs, steam credentials, and successful redeems count
 */

class CoreStatsWidget extends window.DashboardWidget {
    constructor(config) {
        super(config);
        this.stats = {
            email_config_count: 0,
            steam_credential_count: 0,
            successful_redeems_today: 0
        };
    }

    async loadData() {
        try {
            const response = await fetch('/admin/api/core/stats');
            const result = await response.json();
            
            if (result.success) {
                this.stats = result.data;
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load core statistics');
            }
        } catch (error) {
            console.error('Error loading core stats:', error);
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <!-- Email Configs -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                                <i class="fas fa-envelope text-white text-2xl"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">
                                        Total Email Configs
                                    </dt>
                                    <dd class="text-3xl font-semibold text-gray-900">
                                        ${this.stats.email_config_count || 0}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Steam Credentials -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                                <i class="fab fa-steam text-white text-2xl"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">
                                        Total Steam Credentials
                                    </dt>
                                    <dd class="text-3xl font-semibold text-gray-900">
                                        ${this.stats.steam_credential_count || 0}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Successful Redeems Today -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                                <i class="fas fa-check-circle text-white text-2xl"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">
                                        Successful Redeems Today
                                    </dt>
                                    <dd class="text-3xl font-semibold text-gray-900">
                                        ${this.stats.successful_redeems_today || 0}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    shouldRefresh() {
        // Refresh every 5 minutes
        if (!this.lastRefresh) return true;
        const now = new Date();
        const diff = now - this.lastRefresh;
        return diff > 5 * 60 * 1000;
    }
}

// Register widget
window.DashboardWidgets['core-stats'] = CoreStatsWidget; 