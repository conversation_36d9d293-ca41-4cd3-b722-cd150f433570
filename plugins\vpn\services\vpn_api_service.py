"""
VPN API Service - Complete implementation for blueblue.api.limjianhui.com
Handles all API interactions including authentication, servers, clients, and configurations
"""

import logging
import requests
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class VPNAPIService:
    """Service for interacting with the BlueBlue VPN API"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize VPN API Service"""
        self.config = config
        self.api_config = config.get('vpn_api', {})
        self.base_url = self.api_config.get('base_url', 'https://blueblue.api.limjianhui.com')
        self.username = self.api_config.get('username', 'admin')
        self.password = self.api_config.get('password', 'admin123')
        self.timeout = self.api_config.get('timeout', 30)
        
        # Authentication token
        self.access_token = None
        self.token_expires_at = None
        
        # Session for connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
    def _ensure_authenticated(self) -> bool:
        """Ensure we have a valid authentication token"""
        if self.access_token and self.token_expires_at:
            if datetime.now() < self.token_expires_at:
                return True
                
        # Need to authenticate
        return self.authenticate()
        
    def authenticate(self) -> bool:
        """Authenticate with the API and get access token"""
        try:
            auth_url = f"{self.base_url}/api/v1/auth/login"
            logger.info(f"Attempting authentication to: {auth_url}")

            response = self.session.post(
                auth_url,
                json={
                    "username": self.username,
                    "password": self.password
                },
                timeout=self.timeout
            )

            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get('access_token')
                expires_in = data.get('expires_in', 3600)  # Default 1 hour
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)

                # Update session headers with token
                self.session.headers.update({
                    'Authorization': f'Bearer {self.access_token}'
                })

                logger.info(f"Successfully authenticated with VPN API: {self.base_url}")
                return True
            else:
                error_msg = f"Authentication failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return False

        except requests.exceptions.ConnectTimeout as e:
            logger.error(f"Connection timeout while authenticating with VPN API {self.base_url}: {e}")
            return False
        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error while authenticating with VPN API {self.base_url}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error authenticating with VPN API {self.base_url}: {e}")
            return False
            
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Make an authenticated request to the API"""
        if not self._ensure_authenticated():
            logger.error("Failed to authenticate with VPN API")
            return None
            
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            if response.status_code == 401:
                # Token expired, try to re-authenticate
                logger.info("Token expired, re-authenticating...")
                self.access_token = None
                if self._ensure_authenticated():
                    # Retry the request
                    response = self.session.request(
                        method=method,
                        url=url,
                        timeout=self.timeout,
                        **kwargs
                    )
                else:
                    return None
                    
            response.raise_for_status()
            
            # Return JSON response if available
            if response.content:
                return response.json()
            return {}
            
        except requests.exceptions.HTTPError as e:
            # Handle HTTP errors with detailed logging
            if hasattr(e, 'response') and e.response is not None:
                status_code = e.response.status_code
                try:
                    error_detail = e.response.json() if e.response.content else {}
                    logger.error(f"API HTTP error: {method} {endpoint} - {status_code} - {error_detail}")

                    # For 422 errors, return the detailed error information
                    if status_code == 422:
                        return {
                            'error': f"Validation error: {error_detail.get('detail', 'Invalid request data')}",
                            'status_code': status_code,
                            'detail': error_detail
                        }

                    # For 500 errors, check for specific server-side issues and return error info
                    elif status_code == 500:
                        detail = error_detail.get('detail', 'Internal server error')

                        # Check for JSON configuration errors
                        if any(pattern in detail.lower() for pattern in [
                            'invalid json', 'expecting value', 'config file'
                        ]):
                            return {
                                'error': f"Server configuration error: {detail}",
                                'status_code': status_code,
                                'detail': error_detail,
                                'server_issue': True
                            }
                        else:
                            return {
                                'error': f"Server error: {detail}",
                                'status_code': status_code,
                                'detail': error_detail
                            }

                except:
                    logger.error(f"API HTTP error: {method} {endpoint} - {status_code} - {e.response.text}")

                    # For 500 errors without parseable JSON, still return error info
                    if status_code == 500:
                        return {
                            'error': f"Server error: HTTP {status_code}",
                            'status_code': status_code,
                            'raw_response': e.response.text if hasattr(e.response, 'text') else 'Unknown error'
                        }
            else:
                logger.error(f"API HTTP error: {method} {endpoint} - {e}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {method} {endpoint} - {e}")
            return None
            
    # ========== Server Management ==========
    
    def get_servers(self, skip: int = 0, limit: int = 100) -> Optional[List[Dict[str, Any]]]:
        """Get list of VPN servers"""
        return self._make_request('GET', '/api/v1/servers/', params={'skip': skip, 'limit': limit})
        
    def get_server(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server by ID"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}')
        
    def create_server(self, server_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new server"""
        return self._make_request('POST', '/api/v1/servers/', json=server_data)
        
    def update_server(self, server_id: int, server_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update server information"""
        return self._make_request('PUT', f'/api/v1/servers/{server_id}', json=server_data)
        
    def delete_server(self, server_id: int) -> bool:
        """Delete a server"""
        result = self._make_request('DELETE', f'/api/v1/servers/{server_id}')
        return result is not None
        
    def test_server_credentials(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Test server credentials before creating"""
        return self._make_request('POST', '/api/v1/servers/test-credentials', json=credentials)
        
    def test_server_connection(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Test SSH connection to server"""
        return self._make_request('POST', f'/api/v1/servers/{server_id}/test-connection')
        
    def restart_xray_service(self, server_id: int) -> bool:
        """Restart Xray service on server"""
        result = self._make_request('POST', f'/api/v1/servers/{server_id}/restart-xray')
        return result is not None
        
    def get_service_status(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get Xray service status"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}/service-status')
        
    def get_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get Xray configuration from server"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}/config')
        
    def get_server_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get clients from server configuration"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}/clients')
        
    def remove_expired_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Remove expired clients from server"""
        return self._make_request('POST', f'/api/v1/servers/{server_id}/remove-expired')

    def get_server_clients_detailed(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed client information for a specific server with enhanced data"""
        try:
            # Get server info first
            server_info = self.get_server(server_id)
            if not server_info:
                return None

            # Get clients for this server using the existing filtered method
            clients_response = self.get_clients(server_id=server_id, limit=1000)
            if not clients_response:
                return None

            # Get server-specific client data from server configuration
            server_clients = self.get_server_clients(server_id)

            return {
                'server': server_info,
                'clients': clients_response.get('clients', []),
                'total_clients': clients_response.get('total', 0),
                'server_config_clients': server_clients.get('clients', []) if server_clients else [],
                'success': True
            }
        except Exception as e:
            logger.error(f"Error getting detailed server clients for server {server_id}: {e}")
            return None

    def reset_client_traffic(self, server_id: int, client_email: str) -> Optional[Dict[str, Any]]:
        """Reset client traffic for specific server (if supported by API)"""
        # Note: This endpoint may not exist in current API, but we prepare for it
        return self._make_request('POST', f'/api/v1/servers/{server_id}/clients/{client_email}/reset')

    def get_server_client_stats(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get client statistics for a specific server"""
        try:
            clients_response = self.get_clients(server_id=server_id, limit=1000)
            if not clients_response:
                return None

            clients = clients_response.get('clients', [])

            stats = {
                'total_clients': len(clients),
                'active_clients': len([c for c in clients if c.get('is_active', False)]),
                'expired_clients': len([c for c in clients if c.get('is_expired', False)]),
                'expiring_soon': len([c for c in clients if c.get('days_until_expiry', 999) <= 7 and not c.get('is_expired', False)]),
                'lifetime_clients': len([c for c in clients if c.get('expired_date', '').lower() == 'lifetime'])
            }

            return stats
        except Exception as e:
            logger.error(f"Error getting server client stats for server {server_id}: {e}")
            return None
        
    # ========== Client Management ==========
    
    def get_clients(self, skip: int = 0, limit: int = 100, server_id: Optional[int] = None,
                   is_active: Optional[bool] = None, is_expired: Optional[bool] = None,
                   search: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get list of clients with filtering"""
        params = {
            'skip': skip,
            'limit': limit
        }
        if server_id is not None:
            params['server_id'] = server_id
        if is_active is not None:
            params['is_active'] = is_active
        if is_expired is not None:
            params['is_expired'] = is_expired
        if search:
            params['search'] = search
            
        return self._make_request('GET', '/api/v1/clients/', params=params)
        
    def get_client(self, client_id: int) -> Optional[Dict[str, Any]]:
        """Get client by ID"""
        return self._make_request('GET', f'/api/v1/clients/{client_id}')
        
    def get_client_by_email(self, email: str, server_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """Get client by email address"""
        params = {}
        if server_id is not None:
            params['server_id'] = server_id
        return self._make_request('GET', f'/api/v1/clients/by-email/{email}', params=params)
        
    def create_client(self, client_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new client"""
        return self._make_request('POST', '/api/v1/clients/', json=client_data)
        
    def create_clients_bulk(self, server_id: int, clients: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Create multiple clients in bulk"""
        return self._make_request('POST', '/api/v1/clients/bulk', json={
            'server_id': server_id,
            'clients': clients
        })
        
    def update_client(self, client_id: int, client_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update client information"""
        return self._make_request('PUT', f'/api/v1/clients/{client_id}', json=client_data)
        
    def delete_client(self, client_id: int) -> bool:
        """Delete client"""
        result = self._make_request('DELETE', f'/api/v1/clients/{client_id}')
        return result is not None
        
    def extend_client_expiry(self, client_id: int, days: int) -> bool:
        """Extend client expiry by specified days"""
        result = self._make_request('POST', f'/api/v1/clients/{client_id}/extend', params={'days': days})
        return result is not None
        
    def get_server_client_expiry(self, server_id: int) -> Optional[List[Dict[str, Any]]]:
        """Get expiry information for all clients on a server"""
        return self._make_request('GET', f'/api/v1/clients/server/{server_id}/expiry')
        
    # ========== Sync Operations ==========
    
    def sync_server_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Sync clients from server to database"""
        return self._make_request('POST', f'/api/v1/clients/sync/server/{server_id}')
        
    def sync_all_servers_clients(self) -> Optional[Dict[str, Any]]:
        """Sync clients from all servers"""
        return self._make_request('POST', '/api/v1/clients/sync/all')
        
    def compare_server_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Compare clients between server and database"""
        return self._make_request('GET', f'/api/v1/clients/sync/compare/{server_id}')
        
    def analyze_invalid_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Analyze invalid clients in server configuration"""
        return self._make_request('GET', f'/api/v1/clients/sync/analyze/{server_id}')
        
    def cleanup_orphaned_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Remove orphaned clients from database"""
        return self._make_request('POST', f'/api/v1/clients/sync/cleanup/{server_id}')
        
    # ========== Configuration Management ==========
    
    def get_configurations(self) -> Optional[Dict[str, Any]]:
        """Get configuration overview for all servers"""
        return self._make_request('GET', '/api/v1/config/')
        
    def get_server_configuration(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific server"""
        return self._make_request('GET', f'/api/v1/config/server/{server_id}')
        
    def update_server_configuration(self, server_id: int, config: Dict[str, Any], 
                                  create_backup: bool = True) -> Optional[Dict[str, Any]]:
        """Update server configuration"""
        return self._make_request('PUT', f'/api/v1/config/server/{server_id}', json={
            'config': config,
            'create_backup': create_backup
        })
        
    def backup_configurations(self, server_ids: Optional[List[int]] = None, 
                            description: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Backup server configurations"""
        data = {}
        if server_ids is not None:
            data['server_ids'] = server_ids
        if description:
            data['description'] = description
        return self._make_request('POST', '/api/v1/config/backup', json=data)
        
    def restore_configuration(self, server_id: int, backup_path: str, 
                            create_backup_before_restore: bool = True) -> Optional[Dict[str, Any]]:
        """Restore server configuration from backup"""
        return self._make_request('POST', '/api/v1/config/restore', json={
            'server_id': server_id,
            'backup_path': backup_path,
            'create_backup_before_restore': create_backup_before_restore
        })
        
    def validate_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Validate server configuration"""
        return self._make_request('GET', f'/api/v1/config/validate/server/{server_id}')

    def check_server_config_format(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Check server config format"""
        return self._make_request('POST', f'/api/v1/config/check-format/server/{server_id}')
        
    def list_config_backups(self, server_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """List available configuration backups"""
        params = {}
        if server_id is not None:
            params['server_id'] = server_id
        return self._make_request('GET', '/api/v1/config/backups', params=params)
        
    def compare_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Compare server configuration with database"""
        return self._make_request('GET', f'/api/v1/config/compare/server/{server_id}')
        
    def sync_configurations(self, server_ids: Optional[List[int]] = None,
                          sync_direction: str = 'config_to_db',
                          remove_orphaned: bool = False) -> Optional[Dict[str, Any]]:
        """Sync configurations between database and servers"""
        data = {
            'sync_direction': sync_direction,
            'remove_orphaned': remove_orphaned
        }
        if server_ids is not None:
            data['server_ids'] = server_ids
        return self._make_request('POST', '/api/v1/config/sync', json=data)
        
    # ========== Health Monitoring ==========
    
    def get_health_dashboard(self) -> Optional[Dict[str, Any]]:
        """Get comprehensive health dashboard"""
        return self._make_request('GET', '/api/v1/health/')

    def test_connection(self) -> Dict[str, Any]:
        """Test API connection and authentication"""
        try:
            logger.info(f"Testing connection to VPN API: {self.base_url}")

            # Try to authenticate first
            if not self._ensure_authenticated():
                error_msg = f"Authentication failed for {self.base_url} with username {self.username}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": "Authentication failed",
                    "details": error_msg,
                    "api_url": self.base_url
                }

            # Try to make a simple API call to test connection
            result = self._make_request('GET', '/api/v1/health/')
            if result is not None:
                logger.info(f"VPN API connection test successful: {self.base_url}")
                return {
                    "success": True,
                    "message": "Connection successful",
                    "api_url": self.base_url,
                    "response": result
                }
            else:
                error_msg = f"Failed to connect to API health endpoint: {self.base_url}/api/v1/health/"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": "Failed to connect to API",
                    "details": error_msg,
                    "api_url": self.base_url
                }

        except Exception as e:
            error_msg = f"Connection test failed for {self.base_url}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": f"Connection test failed: {str(e)}",
                "details": error_msg,
                "api_url": self.base_url
            }
        
    def get_detailed_health(self) -> Optional[Dict[str, Any]]:
        """Get detailed health check"""
        return self._make_request('GET', '/api/v1/health/detailed')
        
    def get_expiry_health(self) -> Optional[Dict[str, Any]]:
        """Get expiry health check"""
        return self._make_request('GET', '/api/v1/health/expiry-summary')

    def get_server_health(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get health check for specific server"""
        return self._make_request('GET', f'/api/v1/health/servers/{server_id}')

    def refresh_server_health(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Refresh health status for specific server"""
        return self._make_request('POST', f'/api/v1/health/servers/{server_id}/refresh')

    def refresh_all_servers_health(self) -> Optional[Dict[str, Any]]:
        """Refresh health status for all servers"""
        return self._make_request('POST', '/api/v1/health/refresh-all-servers')

    def get_ssh_pool_status(self) -> Optional[Dict[str, Any]]:
        """Get SSH connection pool status"""
        return self._make_request('GET', '/api/v1/health/ssh-pool')

    def get_detailed_health(self) -> Optional[Dict[str, Any]]:
        """Get detailed health check"""
        return self._make_request('GET', '/api/v1/health/detailed')

    # ========== Missing Server Management Methods ==========

    def get_server(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server by ID"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}')

    def update_server(self, server_id: int, server_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update server"""
        return self._make_request('PUT', f'/api/v1/servers/{server_id}', json=server_data)

    def delete_server(self, server_id: int) -> bool:
        """Delete server"""
        result = self._make_request('DELETE', f'/api/v1/servers/{server_id}')
        return result is not None

    def test_server_connection(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Test server connection by ID"""
        return self._make_request('POST', f'/api/v1/servers/{server_id}/test-connection')

    def restart_xray_service(self, server_id: int) -> bool:
        """Restart Xray service"""
        result = self._make_request('POST', f'/api/v1/servers/{server_id}/restart-xray')
        return result is not None

    def get_service_status(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get service status"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}/service-status')

    def get_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server configuration"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}/config')

    def get_server_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server clients"""
        return self._make_request('GET', f'/api/v1/servers/{server_id}/clients')

    def remove_expired_clients(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Remove expired clients"""
        return self._make_request('POST', f'/api/v1/servers/{server_id}/remove-expired')

    # ========== Missing Client Management Methods ==========

    def get_client(self, client_id: int) -> Optional[Dict[str, Any]]:
        """Get client by ID"""
        return self._make_request('GET', f'/api/v1/clients/{client_id}')

    def update_client(self, client_id: int, client_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update client"""
        return self._make_request('PUT', f'/api/v1/clients/{client_id}', json=client_data)

    def delete_client(self, client_id: int) -> bool:
        """Delete client"""
        result = self._make_request('DELETE', f'/api/v1/clients/{client_id}')
        return result is not None

    def get_client_by_email(self, email: str, server_id: int = None) -> Optional[Dict[str, Any]]:
        """Get client by email"""
        params = {'server_id': server_id} if server_id else None
        return self._make_request('GET', f'/api/v1/clients/by-email/{email}', params=params)

    def create_clients_bulk(self, server_id: int, clients: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Create clients in bulk"""
        data = {'server_id': server_id, 'clients': clients}
        return self._make_request('POST', '/api/v1/clients/bulk', json=data)

    def extend_client_expiry(self, client_id: int, days: int) -> bool:
        """Extend client expiry"""
        result = self._make_request('POST', f'/api/v1/clients/{client_id}/extend', params={'days': days})
        return result is not None

    def get_server_client_expiry(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server client expiry information"""
        return self._make_request('GET', f'/api/v1/clients/server/{server_id}/expiry')

    # ========== Missing Configuration Management Methods ==========

    def get_configurations(self) -> Optional[Dict[str, Any]]:
        """Get configurations overview"""
        return self._make_request('GET', '/api/v1/config')

    def get_server_configuration(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server configuration"""
        return self._make_request('GET', f'/api/v1/config/server/{server_id}')

    def update_server_configuration(self, server_id: int, config: str, create_backup: bool = True) -> Optional[Dict[str, Any]]:
        """Update server configuration"""
        data = {'config': config, 'create_backup': create_backup}
        return self._make_request('PUT', f'/api/v1/config/server/{server_id}', json=data)

    def backup_configurations(self, server_ids: List[int] = None, description: str = None) -> Optional[Dict[str, Any]]:
        """Backup configurations"""
        data = {}
        if server_ids:
            data['server_ids'] = server_ids
        if description:
            data['description'] = description
        return self._make_request('POST', '/api/v1/config/backup', json=data)

    def restore_configuration(self, server_id: int, backup_path: str, create_backup_before_restore: bool = True) -> Optional[Dict[str, Any]]:
        """Restore configuration"""
        data = {
            'server_id': server_id,
            'backup_path': backup_path,
            'create_backup_before_restore': create_backup_before_restore
        }
        return self._make_request('POST', '/api/v1/config/restore', json=data)

    def validate_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Validate server configuration"""
        return self._make_request('GET', f'/api/v1/config/validate/server/{server_id}')

    def list_config_backups(self, server_id: int = None) -> Optional[Dict[str, Any]]:
        """List configuration backups"""
        params = {'server_id': server_id} if server_id else None
        return self._make_request('GET', '/api/v1/config/backups', params=params)

    def compare_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Compare server configuration"""
        return self._make_request('GET', f'/api/v1/config/compare/server/{server_id}')

    def sync_configurations(self, server_ids: List[int] = None, sync_direction: str = 'config_to_db', remove_orphaned: bool = False) -> Optional[Dict[str, Any]]:
        """Sync configurations"""
        data = {
            'sync_direction': sync_direction,
            'remove_orphaned': remove_orphaned
        }
        if server_ids:
            data['server_ids'] = server_ids
        return self._make_request('POST', '/api/v1/config/sync', json=data)

    # ========== Missing Task Management Methods ==========

    def get_tasks(self, skip: int = 0, limit: int = 100) -> Optional[Dict[str, Any]]:
        """Get tasks"""
        params = {'skip': skip, 'limit': limit}
        return self._make_request('GET', '/api/v1/tasks', params=params)

    def get_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        return self._make_request('GET', f'/api/v1/tasks/{task_id}')

    def trigger_expiry_check(self, server_id: int = None) -> Optional[Dict[str, Any]]:
        """Trigger expiry check"""
        params = {'server_id': server_id} if server_id else None
        return self._make_request('POST', '/api/v1/tasks/expiry-check', params=params)

    def get_expiry_summary(self) -> Optional[Dict[str, Any]]:
        """Get expiry summary"""
        return self._make_request('GET', '/api/v1/tasks/expiry/summary')
        
    # ========== Task Management ==========
    
    def get_tasks(self, skip: int = 0, limit: int = 100) -> Optional[Dict[str, Any]]:
        """Get list of tasks"""
        return self._make_request('GET', '/api/v1/tasks/', params={'skip': skip, 'limit': limit})
        
    def get_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        return self._make_request('GET', f'/api/v1/tasks/{task_id}')
        
    def trigger_expiry_check(self, server_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """Trigger manual expiry check"""
        params = {}
        if server_id is not None:
            params['server_id'] = server_id
        return self._make_request('POST', '/api/v1/tasks/expiry-check', params=params)
        
    def get_expiry_summary(self) -> Optional[Dict[str, Any]]:
        """Get expiry summary for all servers"""
        return self._make_request('GET', '/api/v1/tasks/expiry/summary')
        
    # ========== User Authentication Management ==========

    def register_user(self, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Register a new user"""
        return self._make_request('POST', '/api/v1/auth/register', json=user_data)

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current user information"""
        return self._make_request('GET', '/api/v1/auth/me')

    def logout(self) -> bool:
        """Logout user (client should discard the token)"""
        result = self._make_request('POST', '/api/v1/auth/logout')
        if result is not None:
            # Clear local token
            self.access_token = None
            self.token_expires_at = None
            # Remove Authorization header
            if 'Authorization' in self.session.headers:
                del self.session.headers['Authorization']
            return True
        return False

    def refresh_token(self) -> bool:
        """Refresh access token"""
        result = self._make_request('POST', '/api/v1/auth/refresh')
        if result:
            self.access_token = result.get('access_token')
            expires_in = result.get('expires_in', 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)

            # Update session headers with new token
            self.session.headers.update({
                'Authorization': f'Bearer {self.access_token}'
            })
            return True
        return False

    # ========== Background Tasks ==========

    def enqueue_background_task(self) -> Optional[Dict[str, Any]]:
        """Enqueue a sample background task"""
        return self._make_request('POST', '/api/v1/background-tasks/enqueue-task')

    # ========== WebSocket Health ==========

    def get_websocket_stats(self) -> Optional[Dict[str, Any]]:
        """Get WebSocket connection statistics"""
        return self._make_request('GET', '/api/v1/ws/stats')

    # ========== Root Information ==========

    def get_root_info(self) -> Optional[Dict[str, Any]]:
        """Get root endpoint information"""
        # This endpoint doesn't require authentication
        try:
            response = self.session.get(
                f"{self.base_url}/",
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json() if response.content else {}
        except Exception as e:
            logger.error(f"Failed to get root info: {e}")
            return None

    def get_api_info(self) -> Optional[Dict[str, Any]]:
        """Get API version information"""
        # This endpoint doesn't require authentication
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1",
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json() if response.content else {}
        except Exception as e:
            logger.error(f"Failed to get API info: {e}")
            return None

    # ========== Utility Methods ==========

    def format_expiry_date(self, date_str: str) -> str:
        """Format expiry date to DD-MM-YYYY format"""
        if date_str.lower() == 'lifetime':
            return 'lifetime'
            
        try:
            # Try parsing different date formats
            for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%Y/%m/%d', '%d/%m/%Y']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d-%m-%Y')
                except ValueError:
                    continue
                    
            # If no format matches, return as is
            return date_str
        except Exception as e:
            logger.error(f"Error formatting date {date_str}: {e}")
            return date_str
            
    def is_client_expired(self, expired_date: str) -> bool:
        """Check if client is expired"""
        if expired_date.lower() == 'lifetime':
            return False
            
        try:
            expiry = datetime.strptime(expired_date, '%d-%m-%Y')
            return datetime.now() > expiry
        except Exception:
            return False
            
    def days_until_expiry(self, expired_date: str) -> int:
        """Calculate days until expiry"""
        if expired_date.lower() == 'lifetime':
            return 9999  # Large number for lifetime
            
        try:
            expiry = datetime.strptime(expired_date, '%d-%m-%Y')
            delta = expiry - datetime.now()
            return delta.days
        except Exception:
            return 0

    def test_credentials(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Test SSH credentials without creating a server"""
        return self.test_server_credentials(credentials)

    def test_xray_service_config(self, server_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Test Xray service configuration and status on a server"""
        return self._make_request('POST', '/api/v1/servers/test-xray-config', json=server_data)


# Global instance for shared access
_vpn_api_service_instance = None


def initialize_vpn_api_service(config: Dict[str, Any]) -> VPNAPIService:
    """Initialize the global VPN API service instance"""
    global _vpn_api_service_instance
    _vpn_api_service_instance = VPNAPIService(config)
    return _vpn_api_service_instance


def get_vpn_api_service() -> Optional[VPNAPIService]:
    """Get the global VPN API service instance"""
    return _vpn_api_service_instance
