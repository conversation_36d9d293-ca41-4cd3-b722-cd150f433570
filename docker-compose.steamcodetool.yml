version: '3.8'

services:
  mtyb-tools:
    image: limjianhui789/mtyb-tools:latest
    container_name: mtyb-tools
    ports:
      - "5000:5000"
    volumes:
      # Use named volumes to avoid permission issues
      - mtyb-configs:/app/configs
      - mtyb-logs:/app/logs
      - mtyb-data:/app/data
    restart: unless-stopped
    environment:
      - PORT=5000
      - ENVIRONMENT=production
      - PYTHONPATH=/app
    # Run as root to avoid permission issues
    user: "0:0"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    init: true

  # Local development service for testing
  mtyb-tools-local:
    build: .
    container_name: mtyb-tools-local
    ports:
      - "5001:5000"
    volumes:
      # Configuration files (REQUIRED)
      - ./configs:/app/configs

      # Logs directory (OPTIONAL)
      - ./logs:/app/logs

      # Data directory (OPTIONAL - for future use)
      - ./data:/app/data
    restart: unless-stopped
    environment:
      - PORT=5000
      - ENVIRONMENT=development
      - PYTHONPATH=/app
    # Run as root for automatic deployment (no permission issues)
    user: "0:0"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # Add init: true to properly handle signals and zombie processes
    init: true

# Named volumes for automatic creation and persistence
volumes:
  mtyb-configs:
    driver: local
  mtyb-logs:
    driver: local
  mtyb-data:
    driver: local

# Alternative configuration using a single data directory
# Uncomment this section if you prefer to mount a single data directory
# and move all JSON files into it

# services:
#   mtyb-tools-alt:
#     image: limjianhui789/mtyb-tools:latest
#     container_name: mtyb-tools-alt
#     ports:
#       - "5000:5000"
#     volumes:
#       # Single data directory approach
#       - ./steamcodetool-data:/app/data
#       - ./logs:/app/logs
#     restart: unless-stopped
#     environment:
#       - PORT=5000
#       - ENVIRONMENT=production
#       - DATA_DIR=/app/data
#     healthcheck:
#       test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
#       interval: 30s
#       timeout: 10s
#       retries: 3
#       start_period: 10s
#     init: true
