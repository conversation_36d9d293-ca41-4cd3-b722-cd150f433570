import os

# Create cache directory if it doesn't exist
cache_dir = os.path.join(os.path.dirname(__file__), 'cache')
if not os.path.exists(cache_dir):
    os.makedirs(cache_dir)

# Try different import approaches to handle both package and direct imports
try:
    from .client import ShopeeAPI
except ImportError:
    try:
        from client import ShopeeAP<PERSON>
    except ImportError:
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        from client import ShopeeAPI

__all__ = ["ShopeeAPI"]