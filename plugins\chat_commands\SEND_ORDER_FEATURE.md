# 发送顺序配置功能

## 概述

Chat Commands 插件现在支持配置消息发送顺序，可以选择先发送图片还是先发送文本。这个功能让回复看起来更自然，特别是当你希望图片先于说明文字出现时。

## 功能特性

### 🔄 **可配置的发送顺序**
- **默认模式**: 先发送文本，然后发送图片
- **图片优先模式**: 先发送图片，然后发送文本
- **智能处理**: 只发送存在的内容（如果只有文本或只有图片）

### ⚙️ **界面配置**
- 在 `/chat-commands/` 管理界面中可以轻松切换
- 实时生效，无需重启服务
- 配置保存在 `config.json` 中

## 使用方法

### 1. 访问配置界面
1. 打开浏览器，访问 `/chat-commands/`
2. 找到 **Command Config** 部分
3. 查看 **"Send Images First (before text)"** 选项

### 2. 配置发送顺序

#### 默认模式（文本优先）
```
☐ Send Images First (before text)
```
**发送顺序**: 文本 → 图片

#### 图片优先模式
```
☑ Send Images First (before text)
```
**发送顺序**: 图片 → 文本

### 3. 保存配置
点击 **"Save Command"** 按钮保存设置

## 配置文件

配置保存在 `plugins/chat_commands/config.json`:

```json
{
  "command_config": {
    "command_prefix": "#",
    "case_sensitive": false,
    "max_response_length": 4000,
    "max_images_per_response": 5,
    "send_images_first": false  // true = 图片优先，false = 文本优先
  }
}
```

## 实际效果示例

### 场景：用户发送 `#android_help`

#### 默认模式（文本优先）
1. 📝 发送帮助文本："🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑯𝒆𝒍𝒑 🤖..."
2. 🖼️ 发送教程图片

#### 图片优先模式
1. 🖼️ 发送教程图片
2. 📝 发送帮助文本："🤖 𝑨𝒏𝒅𝒓𝒐𝒊𝒅 𝑯𝒆𝒍𝒑 🤖..."

## 技术实现

### 代码逻辑
```python
# 获取配置
command_config = command_service.get_command_config()
send_images_first = command_config.send_images_first if command_config else False

# 根据配置决定发送顺序
if send_images_first:
    send_images()  # 先发送图片
    send_text()    # 后发送文本
else:
    send_text()    # 先发送文本
    send_images()  # 后发送图片
```

### 影响的文件
- `plugins/chat_commands/models.py` - 添加 `send_images_first` 字段
- `plugins/chat_commands/config.json` - 配置存储
- `plugins/chat_commands/routes.py` - Webhook 处理逻辑
- `plugins/chat_commands/plugin.py` - 插件内部处理逻辑
- `templates/chat_commands.html` - 界面控制

## 使用场景

### 适合图片优先的情况
- **教程类命令**: 先显示截图，再显示说明
- **产品展示**: 先显示产品图片，再显示描述
- **步骤指导**: 先显示操作界面，再显示文字说明

### 适合文本优先的情况
- **信息查询**: 先显示结果文本，图片作为补充
- **状态报告**: 先显示文字状态，图表作为辅助
- **简单回复**: 主要是文字内容，图片是可选的

## 调试和日志

当启用调试模式时，系统会记录发送顺序：

```
INFO: Sending response to user123 (images first, then text)
INFO: Successfully sent image response to user123
INFO: Successfully sent text response to user123
```

或

```
INFO: Sending response to user123 (text first, then images)
INFO: Successfully sent text response to user123
INFO: Successfully sent image response to user123
```

## 兼容性

- ✅ **向后兼容**: 现有配置默认为文本优先模式
- ✅ **热更新**: 配置更改立即生效
- ✅ **错误处理**: 如果配置加载失败，默认使用文本优先模式

## 注意事项

1. **网络延迟**: 图片发送通常比文本慢，选择图片优先可能会增加总响应时间
2. **用户体验**: 考虑你的用户群体习惯，选择最合适的发送顺序
3. **内容相关性**: 确保图片和文本内容相关，避免用户困惑

---

这个功能让你可以根据不同的使用场景灵活调整消息发送顺序，提供更好的用户体验！
