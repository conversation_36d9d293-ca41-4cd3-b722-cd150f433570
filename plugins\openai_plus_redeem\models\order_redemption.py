"""
Order Redemption Model

Data model for managing order redemptions including status tracking,
account assignment, and cooldown management.
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from enum import Enum


class RedemptionStatus(Enum):
    """Enumeration of possible redemption statuses"""
    PENDING = "pending"
    ACTIVE = "active"
    EXPIRED = "expired"
    COOLDOWN = "cooldown"
    CANCELLED = "cancelled"
    ERROR = "error"


@dataclass
class OrderRedemption:
    """
    Data model for order redemptions
    
    Attributes:
        redemption_id: Unique redemption ID
        order_id: Shopee order ID
        buyer_username: Customer username
        sku: Product SKU
        var_sku: Product variant SKU
        assigned_account_id: Assigned ChatGPT account ID
        redemption_status: Current status
        cooldown_until: Cooldown expiration (ISO format)
        validity_until: Access validity expiration
        verification_code_retrieved: Whether code was retrieved
        created_at: Redemption timestamp
        last_accessed: Last access timestamp
        error_message: Error message if status is ERROR
        retry_count: Number of retry attempts
    """
    
    redemption_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    order_id: str = ""
    buyer_username: str = ""
    sku: str = ""
    var_sku: str = ""
    assigned_account_id: str = ""
    redemption_status: str = field(default=RedemptionStatus.PENDING.value)
    cooldown_until: str = ""
    validity_until: str = ""
    verification_code_retrieved: bool = False
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    last_accessed: str = field(default_factory=lambda: datetime.now().isoformat())
    error_message: str = ""
    retry_count: int = 0
    
    def __post_init__(self):
        """Post-initialization validation and setup"""
        if not self.validity_until and self.var_sku:
            # Set validity based on SKU
            from .chatgpt_account import ChatGPTAccount
            _, validity_days = ChatGPTAccount.parse_capacity_from_sku(self.var_sku)
            validity_end = datetime.now() + timedelta(days=validity_days)
            self.validity_until = validity_end.isoformat()
    
    @property
    def status(self) -> RedemptionStatus:
        """Get redemption status as enum"""
        try:
            return RedemptionStatus(self.redemption_status)
        except ValueError:
            return RedemptionStatus.ERROR
    
    @status.setter
    def status(self, value: RedemptionStatus):
        """Set redemption status from enum"""
        self.redemption_status = value.value
        self.last_accessed = datetime.now().isoformat()
    
    def is_expired(self) -> bool:
        """Check if the redemption has expired"""
        if not self.validity_until:
            return False
        
        try:
            validity_end = datetime.fromisoformat(self.validity_until.replace('Z', '+00:00'))
            return datetime.now() > validity_end.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return False
    
    def is_in_cooldown(self) -> bool:
        """Check if the redemption is in cooldown period"""
        if not self.cooldown_until:
            return False
        
        try:
            cooldown_end = datetime.fromisoformat(self.cooldown_until.replace('Z', '+00:00'))
            return datetime.now() < cooldown_end.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return False
    
    def can_redeem(self) -> bool:
        """Check if the order can be redeemed"""
        return (
            self.status == RedemptionStatus.PENDING and
            not self.is_expired() and
            not self.is_in_cooldown()
        )
    
    def can_access(self) -> bool:
        """Check if the redemption can be accessed"""
        return (
            self.status == RedemptionStatus.ACTIVE and
            not self.is_expired() and
            bool(self.assigned_account_id)
        )
    
    def activate(self, account_id: str) -> bool:
        """
        Activate the redemption with assigned account
        
        Args:
            account_id: ID of the assigned ChatGPT account
            
        Returns:
            True if activation was successful
        """
        if not self.can_redeem():
            return False
        
        self.assigned_account_id = account_id
        self.status = RedemptionStatus.ACTIVE
        self.error_message = ""
        return True
    
    def set_cooldown(self, cooldown_hours: int = 24) -> None:
        """
        Set cooldown period for the redemption
        
        Args:
            cooldown_hours: Number of hours for cooldown
        """
        cooldown_end = datetime.now() + timedelta(hours=cooldown_hours)
        self.cooldown_until = cooldown_end.isoformat()
        self.status = RedemptionStatus.COOLDOWN
    
    def clear_cooldown(self) -> None:
        """Clear the cooldown period"""
        self.cooldown_until = ""
        if self.status == RedemptionStatus.COOLDOWN:
            self.status = RedemptionStatus.PENDING
    
    def expire(self) -> None:
        """Mark the redemption as expired"""
        self.status = RedemptionStatus.EXPIRED
    
    def cancel(self, reason: str = "") -> None:
        """
        Cancel the redemption
        
        Args:
            reason: Reason for cancellation
        """
        self.status = RedemptionStatus.CANCELLED
        if reason:
            self.error_message = reason
    
    def set_error(self, error_message: str) -> None:
        """
        Set error status with message
        
        Args:
            error_message: Error description
        """
        self.status = RedemptionStatus.ERROR
        self.error_message = error_message
        self.retry_count += 1
    
    def update_access_time(self) -> None:
        """Update the last accessed timestamp"""
        self.last_accessed = datetime.now().isoformat()
    
    def get_remaining_validity_days(self) -> int:
        """
        Get remaining validity days
        
        Returns:
            Number of days remaining, 0 if expired
        """
        if not self.validity_until:
            return 0
        
        try:
            validity_end = datetime.fromisoformat(self.validity_until.replace('Z', '+00:00'))
            remaining = validity_end.replace(tzinfo=None) - datetime.now()
            return max(0, remaining.days)
        except (ValueError, AttributeError):
            return 0
    
    def get_cooldown_remaining_hours(self) -> int:
        """
        Get remaining cooldown hours
        
        Returns:
            Number of hours remaining in cooldown, 0 if not in cooldown
        """
        if not self.is_in_cooldown():
            return 0
        
        try:
            cooldown_end = datetime.fromisoformat(self.cooldown_until.replace('Z', '+00:00'))
            remaining = cooldown_end.replace(tzinfo=None) - datetime.now()
            return max(0, int(remaining.total_seconds() / 3600))
        except (ValueError, AttributeError):
            return 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert redemption to dictionary for JSON serialization"""
        return {
            'redemption_id': self.redemption_id,
            'order_id': self.order_id,
            'buyer_username': self.buyer_username,
            'sku': self.sku,
            'var_sku': self.var_sku,
            'assigned_account_id': self.assigned_account_id,
            'redemption_status': self.redemption_status,
            'cooldown_until': self.cooldown_until,
            'validity_until': self.validity_until,
            'verification_code_retrieved': self.verification_code_retrieved,
            'created_at': self.created_at,
            'last_accessed': self.last_accessed,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderRedemption':
        """Create redemption from dictionary"""
        return cls(
            redemption_id=data.get('redemption_id', str(uuid.uuid4())),
            order_id=data.get('order_id', ''),
            buyer_username=data.get('buyer_username', ''),
            sku=data.get('sku', ''),
            var_sku=data.get('var_sku', ''),
            assigned_account_id=data.get('assigned_account_id', ''),
            redemption_status=data.get('redemption_status', RedemptionStatus.PENDING.value),
            cooldown_until=data.get('cooldown_until', ''),
            validity_until=data.get('validity_until', ''),
            verification_code_retrieved=data.get('verification_code_retrieved', False),
            created_at=data.get('created_at', datetime.now().isoformat()),
            last_accessed=data.get('last_accessed', datetime.now().isoformat()),
            error_message=data.get('error_message', ''),
            retry_count=data.get('retry_count', 0)
        )
    
    @classmethod
    def create_from_order(cls, order_id: str, buyer_username: str, sku: str, var_sku: str) -> 'OrderRedemption':
        """
        Create a new redemption from order information
        
        Args:
            order_id: Shopee order ID
            buyer_username: Customer username
            sku: Product SKU
            var_sku: Product variant SKU
            
        Returns:
            New OrderRedemption instance
        """
        return cls(
            order_id=order_id,
            buyer_username=buyer_username,
            sku=sku,
            var_sku=var_sku
        )
    
    def __str__(self) -> str:
        """String representation of the redemption"""
        return f"Redemption {self.order_id} - {self.redemption_status} - {self.buyer_username}"
