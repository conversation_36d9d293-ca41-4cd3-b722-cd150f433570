# config.py
import re
import os
import json
import threading
import time

# Default system prompt for <PERSON> (moved here to avoid circular import)
DEFAULT_SYSTEM_PROMPT = """You are a Customer Service Representative. Analyze the conversation history (up to 20 messages) and respond naturally as a human customer service agent would.

Product Knowledge Base:
{
    "product_rules": [
        {
            "product_id": "28022545701",
            "product_type": "VPN Service",
            "specific_guidelines": [
                "Don't promise specific connection speeds",
                "Available anytime, our system are automatically delivering the order 24/7 hours.",
                "If failed to get the VPN, please wait us to online, we will handle it manually."
            ],
            "common_responses": {
                "connection_issue": "Try to on off the airplane mode, if still not working, please send your config, we will check it once we online.",
                "what different between special and non-special": "The special is for the maxis telco, because the price is different.",
                "what different between GBNetwork, Shinjiru and Singapore Server": "🔥Shinjiru / GBNetwork ?
𝑺𝒉𝒊𝒏𝒋𝒊𝒓𝒖 (Full: 150Mbps)
✅ Murah, Seller2 guna server tu
✅ Dapat Area Full Speed 40-60%
❌ Banyak orang guna data akan jadi slow sikit

𝑮𝑩𝑵𝒆𝒕𝒘𝒐𝒓𝒌 (Full: 5000Mbps)
✅ Support Higher Speed Cap
✅ Dapat Area Full Speed 70-80%
✅ Byk org guna data pun x akan affect network speed

Singapore Server (Full: 1000Mbps)
✅ Menyokong Netflix, OTT, Hotstar, Disney, VIU, HBO
❌ Tidak stabil kadangkala
❌ YouTube atau banyak kandungan Malaysia auto tukar ke kawasan Singapura

⚠️ Kalau area kamu x lebih dari 50mbps, xbanyak beza.",
                "What is this product? How does this work": "You can send chat '#vpn' and the bot will send you the details.",
                "What telco bypass currently available?": "You can send chat '#bypass' and the bot will send you the details."
            }
        },
        {
            "product_id": "27672541012",
            "product_type": "Canva Pro",
            "specific_guidelines": [
                "The product always available, the order will be delivered automatically.",
                "If failed to get the canva pro, please leave your email, we will handle it manully once we online."
            ],
            "common_responses": {
                "why the canva become non pro or invalid": "Please leave your email, we will check it once we online later.",
                "Can this work on all device ?": "Yes, it can work on all device for the user buying Lifetime variation, if the 1 Month variation only can work on the PC."
            }
        }
    ]
}

Response Requirements:
1. Always respond in this JSON format (DO NOT USE MARKDOWN):
{
    "actions": [
        {
            "type": "chat",
            "reply": "your direct response here [MTYB]"
        },
        {
            "type": "send_order",
            "order_sn": "order_number_here"
        }
    ]
}

Available Actions:
{
    "actions": [
        {
            "type": "chat",
            "description": "Send a normal chat message to user"
        },
        {
            "type": "send_order",
            "description": "Send order details to user",
            "requires": "order_sn"
        }
    ]
}

Important Guidelines:
1. If source_content is not null:
- Check product_id and shop_id
- Use relevant product-specific guidelines and responses
- Maintain product compliance requirements

2. In the "reply" field:
- Start responding directly without greetings
- End every response with [MTYB]
- Ask questions when unclear
- Reference previous messages naturally
- Match customer's language style (Malay, Chinese, English)
- Short response with clear instruction.
- Do not talk about anything else that we didnt provide, just tell the customer you dont know, and what you can do as AI.
- Use "\n" to break lines.
- Do not use markdown.
- Do not use any other formatting.
- You are allow to use emoji to act like a human.

3. Never make promises about:
- Refund guarantees
- Seller actions

4. Keep all responses concise and solution-focused
"""

def extract_spc_cds(cookie_string):
    cookies = cookie_string.split('; ')
    for cookie in cookies:
        if cookie.startswith('SPC_CDS='):
            return cookie.split('=')[1]
    return None

def extract_spc_cds_ver(cookie_string):
    cookies = cookie_string.split('; ')
    for cookie in cookies:
        if cookie.startswith('SPC_CDS_VER='):
            return cookie.split('=')[1]
    return None

def extract_csrf_token(cookie_string):
    match = re.search(r'CTOKEN=([^;]+)', cookie_string)
    if match:
        return match.group(1)
    return None

def extract_spc_cds_chat(cookie_string):
    match = re.search(r'SPC_CDS_CHAT=([^;]+)', cookie_string)
    if match:
        return match.group(1)
    return None

# Configuration settings for the Flask application

# AI Reply Settings
AI_REPLY_ENABLED = True
AI_REPLY_COOLDOWN_MINUTES = 1
AI_TEMPERATURE = 1.0
AI_SYSTEM_PROMPT = DEFAULT_SYSTEM_PROMPT
DEEPSEEK_API_KEY = ""


USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0"
AUTHORIZATION_CODE = ""
COOKIE = ""

# Initialize cookie-derived values (will be updated when config is loaded)
SPC_CDS = ""
SPC_CDS_VER = ""
CSRF_TOKEN = ""
SPC_CDS_CHAT = ""

# Constants
PAGE_SIZE = 40
SHOP_ID = 101806022
REGION_ID = "MY"

# Centralized Shopee API Configuration (Now uses HTTPS for security)
# For development, you can change this to your local ShopeeAPI instance
# Example: "http://localhost:8000" for local development
SHOPEE_API_BASE_URL = "https://shop.api.limjianhui.com"

# Legacy API URLs (kept for backward compatibility with other services)
INITIAL_ORDER_LIST_URL = "https://seller.shopee.com.my/api/v3/order/search_order_list_index"
ORDER_DETAILS_URL = "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list"
ORDER_DETAILS_URL_SPECIFIC_ORDER = "https://seller.shopee.com.my/api/v3/order/get_one_order"
INIT_ORDER_URL = "https://seller.shopee.com.my/api/v3/shipment/init_order"
CONVERSATION_URL = "https://seller.shopee.com.my/webchat/api/v1.2/mini/conversation/redirection"
CONVERSATION_SEARCH_URL = "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/search/combined_search"
CHAT_MESSAGE_URL = "https://seller.shopee.com.my/webchat/api/v1.2/messages"
RECENT_CONVERSATIONS_URL = "https://seller.shopee.com.my/webchat/api/v1.2/conversations"
CONVERSATION_MESSAGES_URL = "https://seller.shopee.com.my/webchat/api/v1.2/conversations"

# Order list tab values
ALL_ORDERS_TAB = 100
TO_SHIP_TAB = 300
SHIPPED_TAB = 400
COMPLETED_TAB = 500

# New Configuration Variables
SEND_CHAT_ON_AUTH_SUCCESS = True
AUTO_SHIP_ORDER = False
CHECK_NEW_ORDERS = True  # New feature toggle
ENABLE_AUTO_REDEEM = False
AUTO_REDEEM_VAR_SKUS = []
AUTO_REDEEM_VAR_SKUS_TEXT_ONLY = []
AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = []

AUTO_REPLY_ENABLED = False
AUTO_REPLY_DELAY_MINUTES = 1
AUTO_REPLY_COOLDOWN_MINUTES = 60
AUTO_REPLY_MESSAGE = "Currently Im Offline, I will reply you as soon as possible!"

sent_orders = set()
# EMAIL_CONFIGS - Moved to environment variables for security
EMAIL_CONFIGS = {}

# STEAM_CREDENTIALS - Moved to environment variables for security
STEAM_CREDENTIALS = {}

CHAT_MESSAGES = {
    "steam_auth_code_success": "✉️ Successfully Redeem The Steam Auth Code\n\n🎖️Code: {auth_code}\n🎖️Next redeem time: {next_redeem_time}",
    "netflix_signin_code_success": "✉️ Successfully Retrieve Netflix Sign-In Code\n\n🎖️Email: {email}\n🎖️Code: {code}"
}

NOTIFICATION_EMAIL = {
    "address": "",
    "app_password": "",
    "enabled": False
}

WEBHOOK = {
    "ENABLED": False,
    "MESSAGE_RECEIVED": {
        "ENABLED": False,
        "URL": "",
        "RETRY_COUNT": 3,
        "RETRY_DELAY": 5
    },
    "MESSAGE_SENT": {
        "ENABLED": False,
        "URL": "",
        "RETRY_COUNT": 3,
        "RETRY_DELAY": 5
    }
}

ADMIN_CREDENTIALS = {
    "username": "admin",
    "password": "admin"
}

API_KEY = "MTYB_OFFICIAL"

SESSION_COOLDOWN_TIME = 600 # After successfully request, how long should wait
REQUEST_TIMEOUT = 60  # New timeout for each request
SEND_MESSAGE_ON_SHIP = False
SHIP_SUCCESS_MESSAGE_TEMPLATE = ""
AUTH_CODE_DELAY = 3
CURLEC_API_KEY = ""
CURLEC_SECRET_KEY = ""

# Sent Orders Tracking
SENT_ORDERS_FILE = os.path.join(os.path.dirname(__file__), 'configs', 'data', 'sent_orders.json')

def load_sent_orders():
    if os.path.exists(SENT_ORDERS_FILE):
        with open(SENT_ORDERS_FILE, 'r', encoding='utf-8') as f:
            return set(json.load(f))
    return set()

def save_sent_orders(sent_orders):
    with open(SENT_ORDERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(list(sent_orders), f, indent=2, ensure_ascii=False)

# User-configurable settings
config_path = os.path.join(os.path.dirname(__file__), 'configs', 'core', 'config.json')

def save_config(new_config):
    try:
        with open(config_path, 'w', encoding='utf-8') as config_file:
            json.dump(new_config, config_file, indent=2, ensure_ascii=False)
    except PermissionError as e:
        print(f"Permission denied saving config file: {e}")
        print("Configuration changes will not be persisted")
    except Exception as e:
        print(f"Error saving config file: {e}")
        print("Configuration changes will not be persisted")

def load_config():
    # Check if config file exists, if not create a default one
    if not os.path.exists(config_path):
        # Ensure the directory exists
        try:
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
        except PermissionError as e:
            print(f"Permission denied creating config directory: {e}")
            print(f"Config path: {config_path}")
            print(f"Directory: {os.path.dirname(config_path)}")
            print("Using default configuration in memory only")
            # Return default config without saving to file
            return {
                "ADMIN_CREDENTIALS": {
                    "username": "admin",
                    "password": "whitepaperh0817"
                },
                "API_KEY": "MTYB_OFFICIAL",
                "AUTHORIZATION_CODE": "",
                "COOKIE": "",
                "SHOP_ID": 101806022,
                "AUTO_REPLY_ENABLED": False,
                "AUTO_REPLY_MESSAGE": "Currently Im Offline, I will reply you as soon as possible!",
                "AUTO_REPLY_DELAY_MINUTES": 1,
                "AUTO_REPLY_COOLDOWN_MINUTES": 60,
                "ENABLE_AUTO_REDEEM": False,
                "AUTO_REDEEM_VAR_SKUS": [],
                "AUTO_REDEEM_VAR_SKUS_TEXT_ONLY": [],
                "AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK": [],
                "EMAIL_CONFIGS": {
                    "mtyb_developer": {
                        "email": "<EMAIL>",
                        "app_password": "nnqydxjgqxrdzsxg"
                    }
                },
                "STEAM_CREDENTIALS": {
                    "mtyb_developer": {
                        "password": "@Whitepaperh0817"
                    }
                },
                "NOTIFICATION_EMAIL": {
                    "address": "",
                    "app_password": "",
                    "enabled": False
                },
                "WEBHOOK": {
                    "ENABLED": False,
                    "MESSAGE_RECEIVED": {
                        "ENABLED": False,
                        "URL": "",
                        "RETRY_COUNT": 3,
                        "RETRY_DELAY": 5
                    },
                    "MESSAGE_SENT": {
                        "ENABLED": False,
                        "URL": "",
                        "RETRY_COUNT": 3,
                        "RETRY_DELAY": 5
                    }
                },
                "REQUEST_TIMEOUT": 30,
                "SESSION_COOLDOWN_TIME": 600,
                "SEND_CHAT_ON_AUTH_SUCCESS": True,
                "AUTO_SHIP_ORDER": False,
                "SEND_MESSAGE_ON_SHIP": False,
                "SHIP_SUCCESS_MESSAGE_TEMPLATE": "",
                "AUTH_CODE_DELAY": 3,
                "CURLEC_API_KEY": "",
                "CURLEC_SECRET_KEY": "",
                "AI_REPLY_ENABLED": False,
                "AI_REPLY_COOLDOWN_MINUTES": 60,
                "AI_TEMPERATURE": 1.0,
                "AI_SYSTEM_PROMPT": DEFAULT_SYSTEM_PROMPT,
                "DEEPSEEK_API_KEY": "",  # Moved to environment variables
                "SHOPEE_API_BASE_URL": "https://shop.api.limjianhui.com",
                "TIME_ZONE": "Asia/Kuala_Lumpur"
            }

        # Create default config (reuse the same structure as above)
        default_config = {
            "ADMIN_CREDENTIALS": {
                "username": "admin",
                "password": "whitepaperh0817"
            },
            "API_KEY": "MTYB_OFFICIAL",
            "AUTHORIZATION_CODE": "",
            "COOKIE": "",
            "SHOP_ID": 101806022,
            "AUTO_REPLY_ENABLED": False,
            "AUTO_REPLY_MESSAGE": "Currently Im Offline, I will reply you as soon as possible!",
            "AUTO_REPLY_DELAY_MINUTES": 1,
            "AUTO_REPLY_COOLDOWN_MINUTES": 60,
            "ENABLE_AUTO_REDEEM": False,
            "AUTO_REDEEM_VAR_SKUS": [],
            "AUTO_REDEEM_VAR_SKUS_TEXT_ONLY": [],
            "AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK": [],
            "EMAIL_CONFIGS": {
                "mtyb_developer": {
                    "email": "<EMAIL>",
                    "app_password": "nnqydxjgqxrdzsxg"
                }
            },
            "STEAM_CREDENTIALS": {
                "mtyb_developer": {
                    "password": "@Whitepaperh0817"
                }
            },
            "NOTIFICATION_EMAIL": {
                "address": "",
                "app_password": "",
                "enabled": False
            },
            "WEBHOOK": {
                "ENABLED": False,
                "MESSAGE_RECEIVED": {
                    "ENABLED": False,
                    "URL": "",
                    "RETRY_COUNT": 3,
                    "RETRY_DELAY": 5
                },
                "MESSAGE_SENT": {
                    "ENABLED": False,
                    "URL": "",
                    "RETRY_COUNT": 3,
                    "RETRY_DELAY": 5
                }
            },
            "REQUEST_TIMEOUT": 30,
            "SESSION_COOLDOWN_TIME": 600,
            "SEND_CHAT_ON_AUTH_SUCCESS": True,
            "AUTO_SHIP_ORDER": False,
            "SEND_MESSAGE_ON_SHIP": False,
            "SHIP_SUCCESS_MESSAGE_TEMPLATE": "",
            "AUTH_CODE_DELAY": 3,
            "CURLEC_API_KEY": "",
            "CURLEC_SECRET_KEY": "",
            "AI_REPLY_ENABLED": False,
            "AI_REPLY_COOLDOWN_MINUTES": 60,
            "AI_TEMPERATURE": 1.0,
            "AI_SYSTEM_PROMPT": DEFAULT_SYSTEM_PROMPT,
            "DEEPSEEK_API_KEY": "",  # Moved to environment variables
            "SHOPEE_API_BASE_URL": "https://shop.api.limjianhui.com",
            "TIME_ZONE": "Asia/Kuala_Lumpur"
        }

        # Try to save default config
        try:
            with open(config_path, 'w', encoding='utf-8') as config_file:
                json.dump(default_config, config_file, indent=2, ensure_ascii=False)
            print(f"Created default config file at: {config_path}")
        except PermissionError as e:
            print(f"Permission denied saving config file: {e}")
            print("Using default configuration in memory only")

        return default_config

    try:
        with open(config_path, 'r', encoding='utf-8') as config_file:
            return json.load(config_file)
    except Exception as e:
        print(f"Error loading config file: {e}")
        print(f"Config path: {config_path}")
        print(f"Current working directory: {os.getcwd()}")
        print(f"File exists: {os.path.exists(config_path)}")
        raise

# Initialize user_config safely
try:
    user_config = load_config()
except Exception as e:
    print(f"Failed to load config during module import: {e}")
    # Set a minimal default config to prevent import errors
    user_config = {
        "ADMIN_CREDENTIALS": {"username": "admin", "password": "whitepaperh0817"},
        "API_KEY": "MTYB_OFFICIAL"
    }

def update_session_manager(new_config):
    try:
        from services.session_service import session_manager
        session_manager.update_config(new_config)
    except ImportError:
        # Session manager not available during initial import
        pass

def update_config():
    global user_config, SPC_CDS, ADMIN_CREDENTIALS, AUTO_REPLY_MESSAGE, AUTO_REPLY_ENABLED, AUTO_REPLY_DELAY_MINUTES, AUTO_REPLY_COOLDOWN_MINUTES, SPC_CDS_VER, CSRF_TOKEN, SPC_CDS_CHAT, API_KEY, AUTH_CODE_DELAY, SEND_MESSAGE_ON_SHIP, SHIP_SUCCESS_MESSAGE_TEMPLATE, EMAIL_CONFIGS, AUTO_REDEEM_VAR_SKUS_TEXT_ONLY, AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK, AUTO_REDEEM_MESSAGE, STEAM_CREDENTIALS, COOKIE, AUTHORIZATION_CODE, SHOP_ID, NOTIFICATION_EMAIL, WEBHOOK, REQUEST_TIMEOUT, SESSION_COOLDOWN_TIME, SEND_CHAT_ON_AUTH_SUCCESS, AUTO_SHIP_ORDER, AUTO_REDEEM_VAR_SKUS, ENABLE_AUTO_REDEEM, CHECK_NEW_ORDERS, sent_orders, CURLEC_API_KEY, CURLEC_SECRET_KEY, AI_REPLY_ENABLED, AI_REPLY_COOLDOWN_MINUTES, AI_TEMPERATURE, AI_SYSTEM_PROMPT, DEEPSEEK_API_KEY, SHOPEE_API_BASE_URL

    new_config = load_config()
    user_config = new_config

    # Update SessionManager config
    update_session_manager(user_config)

    # Update credential manager with new values from config
    from utils.credential_manager import credential_manager
    AUTHORIZATION_CODE = new_config.get('AUTHORIZATION_CODE', AUTHORIZATION_CODE)
    COOKIE = new_config.get('COOKIE', COOKIE)

    # Check if JSON cookies are available
    cookie_data = COOKIE
    if 'COOKIE_JSON' in new_config:
        cookie_data = new_config.get('COOKIE_JSON')
        print("Using JSON cookie format from config")

    credential_manager.update_credentials(
        authorization_code=AUTHORIZATION_CODE,
        cookie=cookie_data
    )

    # Update cookie-derived values after potential cookie update
    SPC_CDS = extract_spc_cds(COOKIE)
    SPC_CDS_VER = extract_spc_cds_ver(COOKIE)
    CSRF_TOKEN = extract_csrf_token(COOKIE)
    SPC_CDS_CHAT = extract_spc_cds_chat(COOKIE)

    # Update AI Chat settings
    AI_REPLY_ENABLED = new_config.get('AI_REPLY_ENABLED', False)
    AI_REPLY_COOLDOWN_MINUTES = new_config.get('AI_REPLY_COOLDOWN_MINUTES', 60)
    AI_TEMPERATURE = new_config.get('AI_TEMPERATURE', 1.0)
    AI_SYSTEM_PROMPT = new_config.get('AI_SYSTEM_PROMPT', DEFAULT_SYSTEM_PROMPT)
    DEEPSEEK_API_KEY = new_config.get('DEEPSEEK_API_KEY', DEEPSEEK_API_KEY)

    # Update other existing settings
    API_KEY = user_config.get('API_KEY', API_KEY)
    EMAIL_CONFIGS = user_config.get('EMAIL_CONFIGS', EMAIL_CONFIGS)
    STEAM_CREDENTIALS = user_config.get('STEAM_CREDENTIALS', STEAM_CREDENTIALS)
    SHOP_ID = user_config.get('SHOP_ID', SHOP_ID)
    NOTIFICATION_EMAIL = user_config.get('NOTIFICATION_EMAIL', NOTIFICATION_EMAIL)
    WEBHOOK = user_config.get('WEBHOOK', WEBHOOK)
    REQUEST_TIMEOUT = user_config.get('REQUEST_TIMEOUT', REQUEST_TIMEOUT)
    SESSION_COOLDOWN_TIME = user_config.get('SESSION_COOLDOWN_TIME', SESSION_COOLDOWN_TIME)
    SEND_CHAT_ON_AUTH_SUCCESS = user_config.get('SEND_CHAT_ON_AUTH_SUCCESS', SEND_CHAT_ON_AUTH_SUCCESS)
    AUTO_SHIP_ORDER = user_config.get('AUTO_SHIP_ORDER', AUTO_SHIP_ORDER)
    ENABLE_AUTO_REDEEM = user_config.get('ENABLE_AUTO_REDEEM', False)
    AUTO_REPLY_ENABLED = user_config.get('AUTO_REPLY_ENABLED', False)
    AUTO_REPLY_DELAY_MINUTES = user_config.get('AUTO_REPLY_DELAY_MINUTES', AUTO_REPLY_DELAY_MINUTES)
    AUTO_REPLY_COOLDOWN_MINUTES = user_config.get('AUTO_REPLY_COOLDOWN_MINUTES', AUTO_REPLY_COOLDOWN_MINUTES)
    AUTO_REPLY_MESSAGE = user_config.get('AUTO_REPLY_MESSAGE', AUTO_REPLY_MESSAGE)
    AUTO_REDEEM_VAR_SKUS = user_config.get('AUTO_REDEEM_VAR_SKUS', [])
    CHECK_NEW_ORDERS = user_config.get('CHECK_NEW_ORDERS', CHECK_NEW_ORDERS)
    AUTO_REDEEM_MESSAGE = user_config.get('AUTO_REDEEM_MESSAGE', "Hello, this is a default auto-redeem message.")
    AUTO_REDEEM_VAR_SKUS_TEXT_ONLY = user_config.get('AUTO_REDEEM_VAR_SKUS_TEXT_ONLY', [])
    AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = user_config.get('AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK', [])
    SEND_MESSAGE_ON_SHIP = user_config.get('SEND_MESSAGE_ON_SHIP', SEND_MESSAGE_ON_SHIP)
    SHIP_SUCCESS_MESSAGE_TEMPLATE = user_config.get('SHIP_SUCCESS_MESSAGE_TEMPLATE', SHIP_SUCCESS_MESSAGE_TEMPLATE)
    AUTH_CODE_DELAY = user_config.get('AUTH_CODE_DELAY', AUTH_CODE_DELAY)
    ADMIN_CREDENTIALS = user_config.get('ADMIN_CREDENTIALS', {
        "username": "admin",
        "password": "whitepaperh0817"
    })
    sent_orders = load_sent_orders()

    # Add these lines to update payment gateway credentials
    CURLEC_API_KEY = user_config.get('CURLEC_API_KEY')
    CURLEC_SECRET_KEY = user_config.get('CURLEC_SECRET_KEY')
    
    # Update Shopee API Base URL
    new_shopee_api_url = user_config.get('SHOPEE_API_BASE_URL', SHOPEE_API_BASE_URL)
    if new_shopee_api_url != SHOPEE_API_BASE_URL:
        SHOPEE_API_BASE_URL = new_shopee_api_url
        # Update the global shopee_client instance if it exists
        try:
            from services.shopee_api_client import shopee_client
            shopee_client.update_base_url(SHOPEE_API_BASE_URL)
        except ImportError:
            # shopee_client not available during initial import
            pass

def periodic_config_update():
    while True:
        update_config()
        time.sleep(60)  # Check for updates every 60 seconds

# Start the periodic update in a separate thread
update_thread = threading.Thread(target=periodic_config_update, daemon=True)
update_thread.start()

# Initial configuration load - only if config file exists
try:
    if os.path.exists(config_path):
        update_config()
    else:
        print(f"Config file not found at {config_path}, will be created when needed")
except Exception as e:
    print(f"Error during initial config load: {e}")
    print("Application will continue with default values")