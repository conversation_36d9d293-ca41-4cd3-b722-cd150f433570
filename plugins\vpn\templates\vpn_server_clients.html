{% extends "admin_base.html" %}

{% block title %}{{ server.name }} - Client Management{% endblock %}

{% block extra_head %}
<style>
    .server-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .server-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .server-info-item {
        background: rgba(255,255,255,0.1);
        padding: 1rem;
        border-radius: 8px;
        backdrop-filter: blur(10px);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #667eea;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #666;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .client-filters {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .filter-row {
        display: grid;
        grid-template-columns: 1fr auto auto auto;
        gap: 1rem;
        align-items: end;
    }
    
    .client-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .table-header {
        background: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .client-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-active { background: #d4edda; color: #155724; }
    .status-expired { background: #f8d7da; color: #721c24; }
    .status-expiring { background: #fff3cd; color: #856404; }
    .status-lifetime { background: #d1ecf1; color: #0c5460; }
    
    .pagination-wrapper {
        padding: 1rem;
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .back-button {
        background: #6c757d;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 5px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
        transition: background-color 0.2s;
    }
    
    .back-button:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Back Button -->
    <a href="{{ url_for('vpn.servers') }}" class="back-button">
        <i class="fas fa-arrow-left"></i>
        Back to Servers
    </a>
    
    <!-- Server Header -->
    <div class="server-header">
        <h1><i class="fas fa-server"></i> {{ server.name }}</h1>
        <p class="mb-3">{{ server.description or 'No description available' }}</p>
        
        <div class="server-info">
            <div class="server-info-item">
                <strong>Host:</strong><br>
                {{ server.host }}:{{ server.port or 22 }}
            </div>
            <div class="server-info-item">
                <strong>Status:</strong><br>
                <span class="badge badge-{{ 'success' if server.is_active else 'secondary' }}">
                    {{ 'Active' if server.is_active else 'Inactive' }}
                </span>
            </div>
            <div class="server-info-item">
                <strong>Config Path:</strong><br>
                {{ server.xray_config_path or '/etc/xray/config.json' }}
            </div>
            <div class="server-info-item">
                <strong>Service:</strong><br>
                {{ server.xray_service_name or 'xray' }}
            </div>
        </div>
    </div>
    
    <!-- Client Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ client_stats.total_clients or 0 }}</div>
            <div class="stat-label">Total Clients</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ client_stats.active_clients or 0 }}</div>
            <div class="stat-label">Active Clients</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ client_stats.expired_clients or 0 }}</div>
            <div class="stat-label">Expired Clients</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ client_stats.expiring_soon or 0 }}</div>
            <div class="stat-label">Expiring Soon</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ client_stats.lifetime_clients or 0 }}</div>
            <div class="stat-label">Lifetime Clients</div>
        </div>
    </div>
    
    <!-- Client Filters -->
    <div class="client-filters">
        <form method="GET" class="filter-form">
            <div class="filter-row">
                <div class="form-group">
                    <label for="search">Search Clients</label>
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           value="{{ search }}" 
                           placeholder="Search by email or username...">
                </div>
                <div class="form-group">
                    <label for="status">Status Filter</label>
                    <select class="form-control" id="status" name="status">
                        <option value="all" {{ 'selected' if status_filter == 'all' else '' }}>All Clients</option>
                        <option value="active" {{ 'selected' if status_filter == 'active' else '' }}>Active Only</option>
                        <option value="expired" {{ 'selected' if status_filter == 'expired' else '' }}>Expired Only</option>
                        <option value="expiring_soon" {{ 'selected' if status_filter == 'expiring_soon' else '' }}>Expiring Soon</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="per_page">Per Page</label>
                    <select class="form-control" id="per_page" name="per_page">
                        <option value="25" {{ 'selected' if per_page == 25 else '' }}>25</option>
                        <option value="50" {{ 'selected' if per_page == 50 else '' }}>50</option>
                        <option value="100" {{ 'selected' if per_page == 100 else '' }}>100</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Client Table -->
    <div class="client-table">
        <div class="table-header">
            <h5 class="mb-0">
                <i class="fas fa-users"></i> 
                Server Clients ({{ total_clients }} total)
            </h5>
            <div>
                <a href="{{ url_for('vpn.create_client') }}?server_id={{ server.id }}&redirect_to=server_clients"
                   class="btn btn-success btn-sm">
                    <i class="fas fa-plus"></i> Add Client
                </a>
                <a href="{{ url_for('vpn.bulk_create_clients') }}?server_id={{ server.id }}&redirect_to=server_clients"
                   class="btn btn-info btn-sm">
                    <i class="fas fa-plus-circle"></i> Bulk Add
                </a>
            </div>
        </div>
        
        {% if clients %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="thead-light">
                    <tr>
                        <th>Email/ID</th>
                        <th>Username</th>
                        <th>Status</th>
                        <th>Expiry Date</th>
                        <th>Days Left</th>
                        <th>Data Usage</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr>
                        <td>
                            <strong>{{ client.email }}</strong>
                            {% if client.description %}
                            <br><small class="text-muted">{{ client.description }}</small>
                            {% endif %}
                        </td>
                        <td>{{ client.shopee_username or '-' }}</td>
                        <td>
                            {% if client.expired_date.lower() == 'lifetime' %}
                                <span class="status-badge status-lifetime">Lifetime</span>
                            {% elif client.is_expired %}
                                <span class="status-badge status-expired">Expired</span>
                            {% elif client.days_until_expiry <= 7 %}
                                <span class="status-badge status-expiring">Expiring Soon</span>
                            {% else %}
                                <span class="status-badge status-active">Active</span>
                            {% endif %}
                        </td>
                        <td>{{ client.expired_date }}</td>
                        <td>
                            {% if client.expired_date.lower() == 'lifetime' %}
                                ∞
                            {% else %}
                                {{ client.days_until_expiry }} days
                            {% endif %}
                        </td>
                        <td>{{ (client.data_usage / 1024 / 1024 / 1024) | round(2) }} GB</td>
                        <td>
                            <div class="client-actions">
                                <a href="{{ url_for('vpn.edit_client', client_id=client.id) }}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   title="Edit Client">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-success" 
                                        onclick="extendClient({{ client.id }})" 
                                        title="Extend Expiry">
                                    <i class="fas fa-calendar-plus"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteClient({{ client.id }}, '{{ client.email }}')" 
                                        title="Delete Client">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if has_prev or has_next %}
        <div class="pagination-wrapper">
            <div>
                Showing {{ ((page - 1) * per_page + 1) }} to {{ (page * per_page) if (page * per_page) < total_clients else total_clients }} of {{ total_clients }} clients
            </div>
            <div>
                {% if has_prev %}
                <a href="{{ url_for('vpn.manage_server_clients', server_id=server.id, page=page-1, per_page=per_page, search=search, status=status_filter) }}" 
                   class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
                {% endif %}
                {% if has_next %}
                <a href="{{ url_for('vpn.manage_server_clients', server_id=server.id, page=page+1, per_page=per_page, search=search, status=status_filter) }}" 
                   class="btn btn-sm btn-outline-secondary">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No clients found for this server</h5>
            <p class="text-muted">
                {% if search or status_filter != 'all' %}
                    Try adjusting your search or filter criteria.
                {% else %}
                    Get started by adding your first client to this server.
                {% endif %}
            </p>
            <a href="{{ url_for('vpn.create_client') }}?server_id={{ server.id }}&redirect_to=server_clients"
               class="btn btn-primary">
                <i class="fas fa-plus"></i> Add First Client
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function extendClient(clientId) {
    const days = prompt('Enter number of days to extend:', '30');
    if (days && !isNaN(days) && parseInt(days) > 0) {
        fetch(`/admin/vpn/clients/${clientId}/extend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `days=${days}`
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Failed to extend client expiry');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error extending client expiry');
        });
    }
}

function deleteClient(clientId, email) {
    if (confirm(`Are you sure you want to delete client "${email}"?`)) {
        fetch(`/admin/vpn/clients/${clientId}/delete`, {
            method: 'POST'
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Failed to delete client');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting client');
        });
    }
}

// Auto-submit form on filter changes
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('per_page').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
