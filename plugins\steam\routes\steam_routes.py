"""
Steam Plugin Routes
Defines Flask routes for Steam functionality
"""

import logging
from flask import Blueprint, jsonify, request
from typing import Any

logger = logging.getLogger(__name__)

def create_steam_blueprint(email_service, inventory_service, order_service) -> Blueprint:
    """Create Flask blueprint for Steam routes"""
    
    steam_bp = Blueprint('steam', __name__)
    
    @steam_bp.route('/auth_code', methods=['POST'])
    def get_auth_code():
        """Get Steam authentication code for user"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400
                
            username = data.get('username')
            order_id = data.get('order_id')
            
            if not username or not order_id:
                return jsonify({"error": "Username and order_id are required"}), 400
                
            # Process the request
            result, status_code = order_service.process_steam_auth_request(
                order_id, username, email_service, inventory_service
            )
            
            return jsonify(result), status_code
            
        except Exception as e:
            logger.error(f"Error in get_auth_code: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/inventory', methods=['GET'])
    def get_inventory():
        """Get all inventory items"""
        try:
            inventory = inventory_service.get_all_stock()
            return jsonify(inventory)
        except Exception as e:
            logger.error(f"Error getting inventory: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/inventory/<sku>', methods=['GET'])
    def get_inventory_item(sku: str):
        """Get specific inventory item"""
        try:
            item = inventory_service.get_stock_item(sku)
            if not item:
                return jsonify({"error": "Item not found"}), 404
            return jsonify(item)
        except Exception as e:
            logger.error(f"Error getting inventory item {sku}: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/inventory/<sku>', methods=['PUT'])
    def update_inventory_item(sku: str):
        """Update inventory item quantity"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400
                
            quantity = data.get('quantity')
            operation = data.get('operation', 'set')
            
            if quantity is None:
                return jsonify({"error": "Quantity is required"}), 400
                
            success = inventory_service.update_stock(sku, quantity, operation)
            if success:
                return jsonify({"message": "Inventory updated successfully"})
            else:
                return jsonify({"error": "Failed to update inventory"}), 500
                
        except Exception as e:
            logger.error(f"Error updating inventory item {sku}: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/inventory', methods=['POST'])
    def add_inventory_item():
        """Add new inventory item"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400
                
            sku = data.get('sku')
            name = data.get('name')
            quantity = data.get('quantity', 0)
            
            if not sku or not name:
                return jsonify({"error": "SKU and name are required"}), 400
                
            success = inventory_service.add_new_item(sku, name, quantity)
            if success:
                return jsonify({"message": "Item added successfully"})
            else:
                return jsonify({"error": "Failed to add item"}), 500
                
        except Exception as e:
            logger.error(f"Error adding inventory item: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/inventory/<sku>', methods=['DELETE'])
    def remove_inventory_item(sku: str):
        """Remove inventory item"""
        try:
            success = inventory_service.remove_item(sku)
            if success:
                return jsonify({"message": "Item removed successfully"})
            else:
                return jsonify({"error": "Failed to remove item"}), 500
                
        except Exception as e:
            logger.error(f"Error removing inventory item {sku}: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/inventory/low_stock', methods=['GET'])
    def get_low_stock():
        """Get items with low stock"""
        try:
            threshold = request.args.get('threshold', 5, type=int)
            low_stock = inventory_service.get_low_stock_items(threshold)
            return jsonify(low_stock)
        except Exception as e:
            logger.error(f"Error getting low stock items: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/sessions', methods=['GET'])
    def get_sessions():
        """Get all session statistics"""
        try:
            stats = order_service.get_all_session_stats()
            return jsonify(stats)
        except Exception as e:
            logger.error(f"Error getting session stats: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/sessions/<username>', methods=['GET'])
    def get_user_session(username: str):
        """Get session statistics for specific user"""
        try:
            stats = order_service.get_session_stats(username)
            if not stats:
                return jsonify({"error": "User session not found"}), 404
            return jsonify(stats)
        except Exception as e:
            logger.error(f"Error getting session stats for {username}: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/sessions/<username>', methods=['DELETE'])
    def clear_user_session(username: str):
        """Clear session data for specific user"""
        try:
            order_service.clear_session_data(username)
            return jsonify({"message": "Session data cleared"})
        except Exception as e:
            logger.error(f"Error clearing session for {username}: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/sessions', methods=['DELETE'])
    def clear_all_sessions():
        """Clear all session data"""
        try:
            order_service.clear_session_data()
            return jsonify({"message": "All session data cleared"})
        except Exception as e:
            logger.error(f"Error clearing all sessions: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/test_email/<username>', methods=['POST'])
    def test_email_connection(username: str):
        """Test email connection for specific user"""
        try:
            # Get user credentials
            credentials = email_service.credentials
            user_creds = None
            for cred in credentials:
                if cred.get('username') == username:
                    user_creds = cred
                    break
                    
            if not user_creds:
                return jsonify({"error": "User credentials not found"}), 404
                
            # Test connection
            success = email_service.test_connection(
                user_creds['email'], 
                user_creds['password']
            )
            
            if success:
                return jsonify({"message": "Email connection successful"})
            else:
                return jsonify({"error": "Email connection failed"}), 500
                
        except Exception as e:
            logger.error(f"Error testing email connection for {username}: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/users', methods=['GET'])
    def get_available_users():
        """Get list of available Steam users"""
        try:
            users = email_service.get_available_usernames()
            return jsonify({"users": users})
        except Exception as e:
            logger.error(f"Error getting available users: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    @steam_bp.route('/stats', methods=['GET'])
    def get_plugin_stats():
        """Get Steam plugin statistics"""
        try:
            stats = {
                "processed_orders": order_service.get_processed_orders_count(),
                "inventory_items": inventory_service.get_item_count(),
                "available_users": len(email_service.get_available_usernames()),
                "active_sessions": len(order_service.get_all_session_stats())
            }
            return jsonify(stats)
        except Exception as e:
            logger.error(f"Error getting plugin stats: {e}")
            return jsonify({"error": "Internal server error"}), 500
            
    return steam_bp
