#!/usr/bin/env python3
"""
Test script for Chat Commands Plugin

This script tests the basic functionality of the chat commands plugin
without requiring the full SteamCodeTool environment.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Add the plugin directory to Python path
plugin_dir = os.path.dirname(__file__)
sys.path.insert(0, plugin_dir)

# Add the plugins directory to handle imports
plugins_dir = os.path.dirname(plugin_dir)
sys.path.insert(0, plugins_dir)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_models():
    """Test the data models"""
    print("Testing models...")
    
    from chat_commands.models import ChatCommand, WebhookConfig, WebhookMessage, CommandResponse
    
    # Test ChatCommand
    command = ChatCommand(
        command="test",
        description="Test command",
        response_text="Test response",
        image_urls=["http://example.com/image.jpg"],
        enabled=True
    )
    
    print(f"ChatCommand created: {command.command}")
    print(f"ChatCommand dict: {command.to_dict()}")

    # Test WebhookConfig
    webhook_config = WebhookConfig(
        shopee_api_base_url="http://localhost:8000",
        steamcodetool_base_url="http://localhost:5000",
        enabled=True
    )

    print(f"WebhookConfig created: {webhook_config.get_full_webhook_url()}")

    # Test WebhookMessage
    webhook_data = {
        'message': {
            'message_id': 'test-123',
            'content': {'text': '#help'},
            'from_id': 'user-123',
            'from_name': 'test-user',
            'timestamp': '2024-01-01T00:00:00Z'
        },
        'conversation_id': 'conv-123'
    }
    
    webhook_msg = WebhookMessage.from_webhook_data(webhook_data)
    print(f"WebhookMessage created: {webhook_msg.content}")
    
    print("✓ Models test passed\n")

def test_services():
    """Test the services"""
    print("Testing services...")
    
    from chat_commands.services import ChatCommandService, MessageProcessor
    
    # Create a temporary directory for testing
    test_dir = os.path.join(plugin_dir, 'test_data')
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        # Test ChatCommandService
        service = ChatCommandService(test_dir)
        
        # Test getting commands
        commands = service.get_all_commands()
        print(f"Loaded {len(commands)} default commands")
        
        # Test getting a specific command
        help_command = service.get_command('help')
        if help_command:
            print(f"Help command: {help_command.description}")
        
        # Test MessageProcessor
        processor = MessageProcessor(service)
        
        # Test processing a help command
        test_webhook = {
            'message': {
                'message_id': 'test-123',
                'content': {'text': '#help'},
                'from_id': 'user-123',
                'from_name': 'test-user',
                'timestamp': '2024-01-01T00:00:00Z',
                'send_by_yourself': False
            },
            'conversation_id': 'conv-123'
        }
        
        responses = processor.process_webhook_message(test_webhook)
        if responses:
            print(f"Generated {len(responses)} responses for #help command")
            for i, response in enumerate(responses):
                print(f"  Response {i+1}: {response.text[:50]}...")
        
        # Test config command
        test_config_webhook = {
            'message': {
                'message_id': 'test-124',
                'content': {'text': '#config sg1 30 digi unlimited'},
                'from_id': 'user-123',
                'from_name': 'test-user',
                'timestamp': '2024-01-01T00:00:00Z',
                'send_by_yourself': False
            },
            'conversation_id': 'conv-123'
        }
        
        config_responses = processor.process_webhook_message(test_config_webhook)
        if config_responses:
            print(f"Generated {len(config_responses)} responses for #config command")
        
        print("✓ Services test passed\n")
        
    finally:
        # Clean up test directory
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def test_webhook_data_processing():
    """Test webhook data processing with various scenarios"""
    print("Testing webhook data processing...")
    
    from chat_commands.services import ChatCommandService, MessageProcessor
    
    # Create a temporary directory for testing
    test_dir = os.path.join(plugin_dir, 'test_data2')
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        service = ChatCommandService(test_dir)
        processor = MessageProcessor(service)
        
        test_cases = [
            {
                'name': 'Valid help command',
                'webhook': {
                    'message': {
                        'content': {'text': '#help'},
                        'from_name': 'user1',
                        'send_by_yourself': False
                    },
                    'conversation_id': 'conv1'
                },
                'should_respond': True
            },
            {
                'name': 'Message from ourselves',
                'webhook': {
                    'message': {
                        'content': {'text': '#help'},
                        'from_name': 'user1',
                        'send_by_yourself': True
                    },
                    'conversation_id': 'conv1'
                },
                'should_respond': False
            },
            {
                'name': 'Non-command message',
                'webhook': {
                    'message': {
                        'content': {'text': 'Hello there'},
                        'from_name': 'user1',
                        'send_by_yourself': False
                    },
                    'conversation_id': 'conv1'
                },
                'should_respond': False
            },
            {
                'name': 'Unknown command',
                'webhook': {
                    'message': {
                        'content': {'text': '#unknown_command'},
                        'from_name': 'user1',
                        'send_by_yourself': False
                    },
                    'conversation_id': 'conv1'
                },
                'should_respond': False
            },
            {
                'name': 'Android help command',
                'webhook': {
                    'message': {
                        'content': {'text': '#android_help'},
                        'from_name': 'user1',
                        'send_by_yourself': False
                    },
                    'conversation_id': 'conv1'
                },
                'should_respond': True
            }
        ]
        
        for test_case in test_cases:
            print(f"  Testing: {test_case['name']}")
            responses = processor.process_webhook_message(test_case['webhook'])
            
            if test_case['should_respond']:
                if responses and len(responses) > 0:
                    print(f"    ✓ Generated {len(responses)} responses as expected")
                else:
                    print(f"    ✗ Expected responses but got none")
            else:
                if not responses or len(responses) == 0:
                    print(f"    ✓ No responses generated as expected")
                else:
                    print(f"    ✗ Unexpected responses generated: {len(responses)}")
        
        print("✓ Webhook data processing test completed\n")
        
    finally:
        # Clean up test directory
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def test_command_management():
    """Test command CRUD operations"""
    print("Testing command management...")
    
    from chat_commands.services import ChatCommandService
    from chat_commands.models import ChatCommand
    
    # Create a temporary directory for testing
    test_dir = os.path.join(plugin_dir, 'test_data3')
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        service = ChatCommandService(test_dir)
        
        # Test adding a new command
        new_command = ChatCommand(
            command="test_cmd",
            description="Test command for testing",
            response_text="This is a test response",
            image_urls=["http://example.com/test.jpg"],
            enabled=True
        )
        
        success = service.add_command(new_command)
        if success:
            print("  ✓ Successfully added new command")
        else:
            print("  ✗ Failed to add new command")
        
        # Test getting the command
        retrieved = service.get_command("test_cmd")
        if retrieved and retrieved.command == "test_cmd":
            print("  ✓ Successfully retrieved command")
        else:
            print("  ✗ Failed to retrieve command")
        
        # Test updating the command
        new_command.description = "Updated description"
        success = service.update_command("test_cmd", new_command)
        if success:
            print("  ✓ Successfully updated command")
        else:
            print("  ✗ Failed to update command")
        
        # Test deleting the command
        success = service.delete_command("test_cmd")
        if success:
            print("  ✓ Successfully deleted command")
        else:
            print("  ✗ Failed to delete command")
        
        # Verify deletion
        retrieved = service.get_command("test_cmd")
        if not retrieved:
            print("  ✓ Command successfully removed")
        else:
            print("  ✗ Command still exists after deletion")
        
        print("✓ Command management test passed\n")

    finally:
        # Clean up test directory
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def test_webhook_configuration():
    """Test webhook configuration management"""
    print("Testing webhook configuration...")

    from chat_commands.services import ChatCommandService, WebhookManager
    from chat_commands.models import WebhookConfig

    # Create a temporary directory for testing
    test_dir = os.path.join(plugin_dir, 'test_data4')
    os.makedirs(test_dir, exist_ok=True)

    try:
        service = ChatCommandService(test_dir)

        # Test getting default webhook config
        webhook_config = service.get_webhook_config()
        print(f"  ✓ Default webhook config loaded: {webhook_config.shopee_api_base_url}")

        # Test updating webhook config
        new_config = WebhookConfig(
            enabled=True,
            shopee_api_base_url="http://test.example.com:8000",
            steamcodetool_base_url="http://test.example.com:5000",
            retry_count=5,
            timeout=60
        )

        success = service.update_webhook_config(new_config)
        if success:
            print("  ✓ Successfully updated webhook config")
        else:
            print("  ✗ Failed to update webhook config")

        # Test webhook manager
        webhook_manager = WebhookManager(service)

        # Test connectivity check (will fail but should not crash)
        connectivity = webhook_manager.test_shopee_api_connectivity()
        print(f"  ✓ Connectivity test completed: {connectivity['status']}")

        # Test webhook status
        status = webhook_manager.get_webhook_status()
        if status and 'webhook_config' in status:
            print("  ✓ Webhook status check completed")
        else:
            print("  ✗ Webhook status check failed")

        print("✓ Webhook configuration test passed\n")

    finally:
        # Clean up test directory
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def test_mark_as_unreplied_functionality():
    """Test the mark as unreplied functionality"""
    print("Testing mark as unreplied functionality...")
    
    from chat_commands.services import ChatCommandService, MessageProcessor
    from unittest.mock import Mock, MagicMock
    
    # Create a temporary directory for testing
    test_dir = os.path.join(plugin_dir, 'test_data5')
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        service = ChatCommandService(test_dir)
        processor = MessageProcessor(service)
        
        # Mock the ShopeeAPI client
        mock_client = Mock()
        mock_client.send_chat_message = MagicMock(return_value=({'success': True}, 200))
        mock_client.send_image_message = MagicMock(return_value=({'success': True}, 200))
        mock_client.set_conversation_unread_by_username = MagicMock(return_value=({'success': True}, 200))
        
        processor.shopee_api_client = mock_client
        
        # Test webhook data that should trigger a response
        test_webhook = {
            'type': 'shopee_message',
            'data': {
                'message_type': 'text',
                'message_content': json.dumps({
                    'text': '#help',
                    'from_user_name': 'test_user',
                    'send_by_yourself': False
                })
            }
        }
        
        # Process the webhook message
        responses = processor.process_webhook_message(test_webhook)
        
        if responses and len(responses) > 0:
            print(f"  ✓ Generated {len(responses)} responses for #help command")
            
            # Simulate the message sending and unread marking logic from routes.py
            success_count = 0
            sender_name = 'test_user'
            
            # Mock successful message sending
            for response in responses:
                if response.text:
                    result = mock_client.send_chat_message({
                        'text': response.text,
                        'username': sender_name
                    })
                    if result[1] == 200:
                        success_count += 1
                
                for image_url in response.image_urls:
                    result = mock_client.send_image_message({
                        'username': sender_name,
                        'image_url': image_url
                    })
                    if result[1] == 200:
                        success_count += 1
            
            # Test mark as unreplied functionality
            if success_count > 0 and sender_name and sender_name != 'unknown_user':
                result = mock_client.set_conversation_unread_by_username(sender_name)
                if result[1] == 200:
                    print(f"  ✓ Successfully marked conversation with {sender_name} as unread after sending {success_count} responses")
                else:
                    print(f"  ✗ Failed to mark conversation as unread")
            
            # Verify the API calls were made correctly
            mock_client.send_chat_message.assert_called()
            mock_client.set_conversation_unread_by_username.assert_called_with('test_user')
            
            print("  ✓ Mark as unreplied functionality working correctly")
        else:
            print("  ✗ No responses generated for test webhook")
        
        # Test with different webhook formats
        print("  Testing with legacy webhook format...")
        
        legacy_webhook = {
            'message': {
                'content': {'text': '#android_help'},
                'from_user_name': 'legacy_user',
                'send_by_yourself': False
            },
            'conversation_id': 'conv123'
        }
        
        # Reset mocks
        mock_client.reset_mock()
        mock_client.send_chat_message = MagicMock(return_value=({'success': True}, 200))
        mock_client.send_image_message = MagicMock(return_value=({'success': True}, 200))
        mock_client.set_conversation_unread_by_username = MagicMock(return_value=({'success': True}, 200))
        
        legacy_responses = processor.process_webhook_message(legacy_webhook)
        
        if legacy_responses and len(legacy_responses) > 0:
            print(f"  ✓ Generated {len(legacy_responses)} responses for legacy format")
            
            # Simulate successful sending and unread marking
            success_count = len(legacy_responses)  # Assume all successful
            sender_name = 'legacy_user'
            
            result = mock_client.set_conversation_unread_by_username(sender_name)
            if result[1] == 200:
                print(f"  ✓ Successfully handled legacy webhook format and marked as unread")
        
        # Test error handling
        print("  Testing error handling...")
        
        # Mock API failure
        mock_client.set_conversation_unread_by_username = MagicMock(return_value=({'error': 'API Error'}, 500))
        
        try:
            result = mock_client.set_conversation_unread_by_username('test_user')
            if result[1] != 200:
                print("  ✓ Error handling works correctly - API failure detected")
        except Exception as e:
            print(f"  ✓ Exception handling works: {e}")
        
        print("✓ Mark as unreplied functionality test passed\n")
        
    finally:
        # Clean up test directory
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def main():
    """Run all tests"""
    print("=" * 50)
    print("Chat Commands Plugin Test Suite")
    print("=" * 50)
    
    try:
        test_models()
        test_services()
        test_webhook_data_processing()
        test_command_management()
        test_webhook_configuration()
        test_mark_as_unreplied_functionality()

        print("=" * 50)
        print("✓ All tests passed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
