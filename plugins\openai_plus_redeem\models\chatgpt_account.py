"""
ChatGPT Account Model

Data model for managing ChatGPT Plus accounts including capacity tracking,
expiration management, and SKU parsing functionality.
"""

import re
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Tuple, Dict, Any, Optional
import json


@dataclass
class ChatGPTAccount:
    """
    Data model for ChatGPT Plus accounts
    
    Attributes:
        account_id: Unique account identifier
        email: ChatGPT account email
        password: ChatGPT account password (encrypted)
        expiration_date: Account expiration (ISO format)
        max_concurrent_users: Max users per account (from var_sku)
        validity_days: Validity period (from var_sku)
        current_users: Current active users
        is_active: Account status
        created_at: Creation timestamp
        updated_at: Last update timestamp
    """
    
    account_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    email: str = ""
    password: str = ""
    expiration_date: str = ""
    max_concurrent_users: int = 1
    validity_days: int = 30
    current_users: int = 0
    is_active: bool = True
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def __post_init__(self):
        """Post-initialization validation and setup"""
        if not self.expiration_date and self.validity_days > 0:
            # Set expiration date based on validity days
            expiration = datetime.now() + timedelta(days=self.validity_days)
            self.expiration_date = expiration.isoformat()
    
    @classmethod
    def parse_capacity_from_sku(cls, var_sku: str) -> Tuple[int, int]:
        """
        Parse capacity and validity from var_sku
        
        Expected format: "chatgpt_<users>_<days>" or "my_<product>_<days>"
        Examples:
            - "chatgpt_5_30" -> (5 users, 30 days)
            - "chatgpt_1_15" -> (1 user, 15 days)
            - "my_premium_30" -> (1 user, 30 days) - default for my_ prefix
        
        Args:
            var_sku: Product variant SKU string
            
        Returns:
            Tuple of (max_concurrent_users, validity_days)
        """
        if not var_sku:
            return (1, 30)  # Default values
        
        # Handle my_ prefixed SKUs (default to 1 user)
        if var_sku.startswith('my_'):
            # Extract days from my_<product>_<days>
            match = re.search(r'my_\w+_(\d+)', var_sku)
            if match:
                days = int(match.group(1))
                return (1, days)
            return (1, 30)  # Default for my_ prefix
        
        # Handle standard chatgpt SKUs
        if var_sku.startswith('chatgpt_'):
            # Extract users and days from chatgpt_<users>_<days>
            match = re.search(r'chatgpt_(\d+)_(\d+)', var_sku)
            if match:
                users = int(match.group(1))
                days = int(match.group(2))
                return (users, days)
        
        # Try to extract any numbers from the SKU as fallback
        numbers = re.findall(r'\d+', var_sku)
        if len(numbers) >= 2:
            return (int(numbers[0]), int(numbers[1]))
        elif len(numbers) == 1:
            # Assume it's days, default users to 1
            return (1, int(numbers[0]))
        
        # Default fallback
        return (1, 30)
    
    @classmethod
    def from_sku(cls, email: str, password: str, var_sku: str) -> 'ChatGPTAccount':
        """
        Create a ChatGPT account from SKU information
        
        Args:
            email: Account email
            password: Account password
            var_sku: Product variant SKU
            
        Returns:
            ChatGPTAccount instance
        """
        max_users, validity_days = cls.parse_capacity_from_sku(var_sku)
        
        return cls(
            email=email,
            password=password,
            max_concurrent_users=max_users,
            validity_days=validity_days
        )
    
    def is_expired(self) -> bool:
        """Check if the account has expired"""
        if not self.expiration_date:
            return False
        
        try:
            expiration = datetime.fromisoformat(self.expiration_date.replace('Z', '+00:00'))
            return datetime.now() > expiration.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return False
    
    def is_at_capacity(self) -> bool:
        """Check if the account is at maximum capacity"""
        return self.current_users >= self.max_concurrent_users
    
    def can_assign_user(self) -> bool:
        """Check if a new user can be assigned to this account"""
        return (
            self.is_active and 
            not self.is_expired() and 
            not self.is_at_capacity()
        )
    
    def assign_user(self) -> bool:
        """
        Assign a user to this account
        
        Returns:
            True if user was assigned successfully, False otherwise
        """
        if not self.can_assign_user():
            return False
        
        self.current_users += 1
        self.updated_at = datetime.now().isoformat()
        return True
    
    def release_user(self) -> bool:
        """
        Release a user from this account
        
        Returns:
            True if user was released successfully, False otherwise
        """
        if self.current_users <= 0:
            return False
        
        self.current_users -= 1
        self.updated_at = datetime.now().isoformat()
        return True
    
    def extend_expiration(self, additional_days: int) -> None:
        """
        Extend the account expiration by additional days
        
        Args:
            additional_days: Number of days to extend
        """
        try:
            if self.expiration_date:
                current_expiration = datetime.fromisoformat(self.expiration_date.replace('Z', '+00:00'))
            else:
                current_expiration = datetime.now()
            
            new_expiration = current_expiration + timedelta(days=additional_days)
            self.expiration_date = new_expiration.isoformat()
            self.updated_at = datetime.now().isoformat()
            
        except (ValueError, AttributeError):
            # If current expiration is invalid, set new expiration from now
            new_expiration = datetime.now() + timedelta(days=additional_days)
            self.expiration_date = new_expiration.isoformat()
            self.updated_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert account to dictionary for JSON serialization"""
        return {
            'account_id': self.account_id,
            'email': self.email,
            'password': self.password,
            'expiration_date': self.expiration_date,
            'max_concurrent_users': self.max_concurrent_users,
            'validity_days': self.validity_days,
            'current_users': self.current_users,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatGPTAccount':
        """Create account from dictionary"""
        return cls(
            account_id=data.get('account_id', str(uuid.uuid4())),
            email=data.get('email', ''),
            password=data.get('password', ''),
            expiration_date=data.get('expiration_date', ''),
            max_concurrent_users=data.get('max_concurrent_users', 1),
            validity_days=data.get('validity_days', 30),
            current_users=data.get('current_users', 0),
            is_active=data.get('is_active', True),
            created_at=data.get('created_at', datetime.now().isoformat()),
            updated_at=data.get('updated_at', datetime.now().isoformat())
        )
    
    def __str__(self) -> str:
        """String representation of the account"""
        status = "Active" if self.is_active else "Inactive"
        expired = " (Expired)" if self.is_expired() else ""
        return f"ChatGPT Account {self.email} - {status}{expired} - {self.current_users}/{self.max_concurrent_users} users"
