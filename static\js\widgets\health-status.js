/**
 * API Health Status Widget
 * Displays health status of all integrated services
 */

class HealthStatusWidget extends window.DashboardWidget {
    constructor(config) {
        super(config);
        this.healthData = null;
    }

    async loadData() {
        try {
            const response = await fetch('/admin/api/health/summary');
            const result = await response.json();
            
            if (result.success) {
                this.healthData = result.data;
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load health status');
            }
        } catch (error) {
            console.error('Error loading health status:', error);
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container || !this.healthData) return;

        const { overall_status, services, issues } = this.healthData;

        // Determine overall status color and icon
        let statusColor = 'green';
        let statusIcon = 'check-circle';
        let statusText = 'All Systems Operational';

        if (overall_status === 'degraded') {
            statusColor = 'yellow';
            statusIcon = 'exclamation-triangle';
            statusText = 'Some Systems Degraded';
        } else if (overall_status === 'unhealthy') {
            statusColor = 'red';
            statusIcon = 'times-circle';
            statusText = 'System Issues Detected';
        }

        // Build services HTML
        const servicesHtml = Object.keys(services || {}).map(key => {
            const service = services[key];
            let serviceStatusColor = 'green';
            let serviceStatusIcon = 'check-circle';

            // Check if current_status exists before accessing its properties
            const currentStatus = service.current_status || {};
            const status = currentStatus.status || 'unknown';

            if (status === 'degraded') {
                serviceStatusColor = 'yellow';
                serviceStatusIcon = 'exclamation-triangle';
            } else if (status === 'unhealthy') {
                serviceStatusColor = 'red';
                serviceStatusIcon = 'times-circle';
            } else if (status === 'unknown') {
                serviceStatusColor = 'gray';
                serviceStatusIcon = 'question-circle';
            }

            return `
                <div class="p-4 border-b last:border-b-0">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-${serviceStatusIcon} text-${serviceStatusColor}-500"></i>
                            <div>
                                <p class="font-medium">${service.name}</p>
                                <p class="text-sm text-gray-500">${service.description || service.base_url}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-${serviceStatusColor}-600">
                                ${status.charAt(0).toUpperCase() + status.slice(1)}
                            </p>
                            ${currentStatus.response_time ?
                                `<p class="text-xs text-gray-500">${(currentStatus.response_time * 1000).toFixed(0)}ms</p>` :
                                ''}
                            <button class="mt-1 text-xs text-blue-600 hover:text-blue-800 service-details-btn"
                                    data-service="${key}">
                                View Details
                            </button>
                        </div>
                    </div>
                    ${currentStatus.error_message ?
                        `<p class="mt-2 text-sm text-red-600">${currentStatus.error_message}</p>` :
                        ''}
                </div>
            `;
        }).join('');

        // Build issues HTML
        const issuesHtml = (issues || []).map(issue => `
            <div class="p-3 bg-${issue.severity === 'critical' ? 'red' : issue.severity === 'warning' ? 'yellow' : 'blue'}-50 
                        border border-${issue.severity === 'critical' ? 'red' : issue.severity === 'warning' ? 'yellow' : 'blue'}-200 
                        rounded-md mb-2">
                <p class="text-sm font-medium text-${issue.severity === 'critical' ? 'red' : issue.severity === 'warning' ? 'yellow' : 'blue'}-800">
                    ${issue.service}: ${issue.message}
                </p>
                ${issue.details ? `<p class="text-xs text-gray-600 mt-1">${issue.details}</p>` : ''}
            </div>
        `).join('');

        this.container.innerHTML = `
            <div class="bg-white overflow-hidden shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">API Health Status</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                Last updated: ${new Date(this.healthData.last_check).toLocaleString()}
                            </p>
                        </div>
                        <button id="refreshHealthBtn" class="inline-flex items-center px-3 py-2 border border-gray-300 
                                shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white 
                                hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Refresh
                        </button>
                    </div>

                    <!-- Overall Status -->
                    <div class="mb-6 p-4 bg-${statusColor}-50 border border-${statusColor}-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-${statusIcon} text-${statusColor}-500 text-2xl mr-3"></i>
                            <div>
                                <p class="text-lg font-medium text-${statusColor}-800">${statusText}</p>
                                <p class="text-sm text-${statusColor}-600">
                                    ${Object.keys(services).filter(k => {
                                        const svc = services[k];
                                        return svc.current_status && svc.current_status.status === 'healthy';
                                    }).length} of
                                    ${Object.keys(services).length} services operational
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Issues/Alerts -->
                    ${issues.length > 0 ? `
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">Active Issues</h4>
                            ${issuesHtml}
                        </div>
                    ` : ''}

                    <!-- Service Details -->
                    <div class="border rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 p-4 border-b">Service Status</h4>
                        ${servicesHtml}
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        document.getElementById('refreshHealthBtn')?.addEventListener('click', () => {
            this.refresh();
        });

        // Service details button handlers are handled by the main dashboard
    }

    shouldRefresh() {
        // Refresh every minute
        if (!this.lastRefresh) return true;
        const now = new Date();
        const diff = now - this.lastRefresh;
        return diff > 60 * 1000;
    }
}

// Register widget
window.DashboardWidgets['health-status'] = HealthStatusWidget; 