"""
Demo script showing how to use the Shopee Auto Boost Plugin API

This script demonstrates the various API endpoints and their usage.
"""

import requests
import json
import time
from datetime import datetime

# Base URL for the plugin API
BASE_URL = "http://localhost:5000/api/shopee_auto_boost"


def demo_get_status():
    """Demo: Get boost status"""
    print("📊 Getting boost status...")
    try:
        response = requests.get(f"{BASE_URL}/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status retrieved successfully")
            print(f"   Scheduler running: {data.get('scheduler_running', False)}")
            print(f"   Next boost time: {data.get('next_boost_time', 'Not scheduled')}")
            print(f"   Last boost time: {data.get('last_boost_time', 'Never')}")
            print(f"   Total boost sessions: {data.get('total_boost_sessions', 0)}")
            return True
        else:
            print(f"❌ Failed to get status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting status: {e}")
        return False


def demo_get_products():
    """Demo: Get boostable products"""
    print("\n📦 Getting boostable products...")
    try:
        response = requests.get(f"{BASE_URL}/products")
        if response.status_code == 200:
            data = response.json()
            total_count = data.get('total_count', 0)
            products = data.get('products', [])
            
            print(f"✅ Found {total_count} boostable products")
            
            # Show first 3 products as examples
            for i, product in enumerate(products[:3]):
                print(f"   {i+1}. {product.get('name', 'Unknown')} (ID: {product.get('id')})")
                print(f"      Stock: {product.get('stock', 0)}, Sold: {product.get('sold_count', 0)}")
                print(f"      Price: {product.get('price_min', '0')} - {product.get('price_max', '0')}")
            
            if len(products) > 3:
                print(f"   ... and {len(products) - 3} more products")
                
            return products
        else:
            print(f"❌ Failed to get products: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error getting products: {e}")
        return []


def demo_manual_boost():
    """Demo: Trigger manual boost"""
    print("\n🚀 Triggering manual boost...")
    try:
        response = requests.post(f"{BASE_URL}/boost/manual")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('results', {})
                success_count = len(results.get('success', []))
                failed_count = len(results.get('failed', []))
                
                print(f"✅ Manual boost completed")
                print(f"   Successfully boosted: {success_count} products")
                print(f"   Failed to boost: {failed_count} products")
                
                # Show boosted products
                for product in results.get('success', []):
                    print(f"   ✅ {product.get('name', 'Unknown')} (ID: {product.get('id')})")
                
                # Show failed products
                for product in results.get('failed', []):
                    print(f"   ❌ {product.get('name', 'Unknown')} (ID: {product.get('id')}) - {product.get('error', 'Unknown error')}")
                
                return True
            else:
                print(f"❌ Manual boost failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Failed to trigger manual boost: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error triggering manual boost: {e}")
        return False


def demo_single_product_boost(product_id):
    """Demo: Boost a single product"""
    print(f"\n🎯 Boosting single product (ID: {product_id})...")
    try:
        payload = {"product_id": product_id}
        response = requests.post(f"{BASE_URL}/boost/single", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Product {product_id} boosted successfully")
                return True
            else:
                print(f"❌ Failed to boost product {product_id}: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Failed to boost single product: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error boosting single product: {e}")
        return False


def demo_scheduler_control():
    """Demo: Start and stop scheduler"""
    print("\n⏰ Testing scheduler control...")
    
    # Stop scheduler
    print("   Stopping scheduler...")
    try:
        response = requests.post(f"{BASE_URL}/scheduler/stop")
        if response.status_code == 200:
            print("   ✅ Scheduler stopped")
        else:
            print(f"   ❌ Failed to stop scheduler: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error stopping scheduler: {e}")
    
    time.sleep(1)
    
    # Start scheduler
    print("   Starting scheduler...")
    try:
        response = requests.post(f"{BASE_URL}/scheduler/start")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ Scheduler started")
                print(f"   Next run time: {data.get('next_run_time', 'Unknown')}")
                return True
            else:
                print(f"   ❌ Failed to start scheduler: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ Failed to start scheduler: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error starting scheduler: {e}")
        return False


def demo_get_history():
    """Demo: Get boost history"""
    print("\n📈 Getting boost history...")
    try:
        response = requests.get(f"{BASE_URL}/history")
        if response.status_code == 200:
            data = response.json()
            global_stats = data.get('global_stats', {})
            recent_products = data.get('recent_products', [])
            
            print(f"✅ History retrieved successfully")
            print(f"   Total boost sessions: {global_stats.get('total_boost_sessions', 0)}")
            print(f"   Total products boosted: {global_stats.get('total_products_boosted', 0)}")
            print(f"   Total failures: {global_stats.get('total_failures', 0)}")
            print(f"   Last boost session: {global_stats.get('last_boost_session', 'Never')}")
            
            if recent_products:
                print(f"   Recent boosted products:")
                for product in recent_products[:5]:  # Show first 5
                    print(f"     Product {product.get('product_id')} - {product.get('total_boosts')} boosts")
            
            return True
        else:
            print(f"❌ Failed to get history: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting history: {e}")
        return False


def demo_update_config():
    """Demo: Update configuration"""
    print("\n⚙️ Updating configuration...")
    try:
        new_config = {
            "boost_interval_hours": 6,  # Change from 4 to 6 hours
            "products_per_boost": 3     # Change from 5 to 3 products
        }
        
        response = requests.put(f"{BASE_URL}/config", json=new_config)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Configuration updated successfully")
                print(f"   New boost interval: {new_config['boost_interval_hours']} hours")
                print(f"   New products per boost: {new_config['products_per_boost']}")
                return True
            else:
                print(f"❌ Failed to update config: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Failed to update config: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error updating config: {e}")
        return False


def main():
    """Run the demo"""
    print("🎉 Shopee Auto Boost Plugin Demo")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/status", timeout=5)
        if response.status_code != 200:
            print("❌ Plugin API is not responding. Make sure the main application is running.")
            return
    except Exception as e:
        print(f"❌ Cannot connect to plugin API: {e}")
        print("   Make sure the main application is running on http://localhost:5000")
        return
    
    # Run demos
    demo_get_status()
    products = demo_get_products()
    demo_get_history()
    demo_scheduler_control()
    
    # If we have products, demo single product boost
    if products:
        first_product_id = products[0].get('id')
        if first_product_id:
            demo_single_product_boost(first_product_id)
    
    # Demo manual boost (comment out if you don't want to actually boost)
    # demo_manual_boost()
    
    demo_update_config()
    
    print("\n🎉 Demo completed!")
    print("\nNote: Some demos (like manual boost) are commented out to avoid")
    print("      accidentally boosting products during testing.")


if __name__ == "__main__":
    main()
