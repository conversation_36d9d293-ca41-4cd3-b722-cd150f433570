# Docker Hub deployment script for SteamCodeTool
# This script builds and pushes the SteamCodeTool Docker image to a private Docker Hub repository

param(
    [switch]$Build,
    [switch]$Push,
    [switch]$All,
    [switch]$Help
)

# Configuration
$DOCKER_HUB_USERNAME = "limjianhui789"
$DOCKER_HUB_REPO = "mtyb-tools"
$IMAGE_NAME = "$DOCKER_HUB_USERNAME/$DOCKER_HUB_REPO"

function Show-Help {
    Write-Host "Usage: .\docker-hub-push-mtyb-tools.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Build    Build the Docker image"
    Write-Host "  -Push     Push the Docker image to Docker Hub"
    Write-Host "  -All      Build and push the Docker image"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\docker-hub-push-mtyb-tools.ps1 -Build"
    Write-Host "  .\docker-hub-push-mtyb-tools.ps1 -Push"
    Write-Host "  .\docker-hub-push-mtyb-tools.ps1 -All"
}

function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        Write-Error "Docker is not running. Please start Docker Desktop and try again."
        return $false
    }
}

function Get-Version {
    $datetime = Get-Date -Format "yyyy.MM.dd.HHmm"
    return $datetime
}

function Build-DockerImage {
    param($Version)
    
    Write-Host "Building Docker image..." -ForegroundColor Green
    $currentDir = Get-Location
    
    # Check if Dockerfile exists
    if (-not (Test-Path "$currentDir\Dockerfile")) {
        Write-Error "Dockerfile not found at $currentDir\Dockerfile"
        Write-Error "Make sure you're running this script from the root of your project."
        return $false
    }
    
    Write-Host "Using Dockerfile at: $currentDir\Dockerfile"
    
    # Build the image
    $buildResult = docker build -t "$IMAGE_NAME`:$Version" -t "$IMAGE_NAME`:latest" -f "$currentDir\Dockerfile" "$currentDir"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to build Docker image. Please check Docker Desktop and try again."
        return $false
    }
    
    Write-Host "Docker image built: $IMAGE_NAME`:$Version" -ForegroundColor Green
    return $true
}

function Test-ImageExists {
    param($Version)
    
    Write-Host "Checking if image exists locally..."
    docker image inspect "$IMAGE_NAME`:$Version" | Out-Null
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Image $IMAGE_NAME`:$Version does not exist locally. Build the image first."
        Write-Error "Run: .\docker-hub-push-mtyb-tools.ps1 -Build"
        return $false
    }
    
    return $true
}

function Push-DockerImage {
    param($Version)
    
    Write-Host "Logging in to Docker Hub..." -ForegroundColor Yellow
    docker login
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to log in to Docker Hub. Please check your credentials and try again."
        return $false
    }
    
    Write-Host "Pushing Docker image to Docker Hub..." -ForegroundColor Green
    
    # Push versioned tag
    docker push "$IMAGE_NAME`:$Version"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to push image tag $Version. Please check Docker Desktop connection and try again."
        return $false
    }
    
    # Push latest tag
    docker push "$IMAGE_NAME`:latest"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to push latest tag. Please check Docker Desktop connection and try again."
        return $false
    }
    
    Write-Host "Docker image successfully pushed to Docker Hub: $IMAGE_NAME`:$Version" -ForegroundColor Green
    return $true
}

function Show-DeploymentInstructions {
    param($Version)
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "DEPLOYMENT INSTRUCTIONS" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "The image has been pushed to Docker Hub: $IMAGE_NAME`:$Version"
    Write-Host ""
    Write-Host "RECOMMENDED DEPLOYMENT METHOD:" -ForegroundColor Yellow
    Write-Host "Use the production deployment script for automatic setup:"
    Write-Host ""
    Write-Host "Windows:"
    Write-Host "  .\deploy-steamcodetool-fixed.bat"
    Write-Host ""
    Write-Host "Linux/macOS:"
    Write-Host "  chmod +x deploy-steamcodetool-fixed.sh"
    Write-Host "  ./deploy-steamcodetool-fixed.sh"
    Write-Host ""
    Write-Host "ALTERNATIVE: Manual Docker Compose:"
    Write-Host "  docker-compose -f docker-compose.steamcodetool-production.yml up -d"
    Write-Host ""
    Write-Host "FEATURES OF NEW DEPLOYMENT:" -ForegroundColor Green
    Write-Host "✅ No permission issues (runs as root)"
    Write-Host "✅ Uses Docker named volumes for persistence"
    Write-Host "✅ Automatic configuration initialization"
    Write-Host "✅ Built-in health checks"
    Write-Host "✅ Easy troubleshooting with test scripts"
    Write-Host ""
    Write-Host "DATA PERSISTENCE:" -ForegroundColor Yellow
    Write-Host "Your data is stored in Docker named volumes:"
    Write-Host "- mtyb-configs (configuration files)"
    Write-Host "- mtyb-logs (application logs)"
    Write-Host "- mtyb-data (application data)"
    Write-Host ""
    Write-Host "TESTING DEPLOYMENT:" -ForegroundColor Yellow
    Write-Host "After deployment, run the test script:"
    Write-Host "Windows: .\test-deployment.bat"
    Write-Host "Linux/macOS: ./test-deployment.sh"
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
}

# Main script logic
if ($Help -or (-not $Build -and -not $Push -and -not $All)) {
    Show-Help
    exit 0
}

# Check if Docker is running
if (-not (Test-DockerRunning)) {
    exit 1
}

# Get version
$version = Get-Version
Write-Host "Using version: $version" -ForegroundColor Cyan

$buildSuccess = $false

# Handle build
if ($Build -or $All) {
    $buildSuccess = Build-DockerImage -Version $version
    if (-not $buildSuccess -and $All) {
        Write-Error "Build failed, not proceeding to push."
        exit 1
    }
}

# Handle push
if ($Push -or ($All -and $buildSuccess)) {
    if (-not (Test-ImageExists -Version $version)) {
        exit 1
    }
    
    if (-not (Push-DockerImage -Version $version)) {
        exit 1
    }
    
    Show-DeploymentInstructions -Version $version
}

Write-Host "Process completed!" -ForegroundColor Green
