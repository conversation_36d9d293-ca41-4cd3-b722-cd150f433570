"""
API Routes for Shopee Auto Boost Plugin

Provides REST API endpoints for controlling the auto-boost functionality.
"""

import logging
from flask import Blueprint, jsonify, request
from typing import Dict, Any

logger = logging.getLogger(__name__)


def create_boost_routes(plugin) -> Blueprint:
    """Create and configure the boost routes blueprint"""
    
    blueprint = Blueprint('shopee_auto_boost', __name__)

    @blueprint.route('/status', methods=['GET'])
    def get_boost_status():
        """Get current boost status and statistics"""
        try:
            # Get PIN status if enabled
            pinned_config = plugin.config.get("pinned_products", {})
            pin_status = None
            if pinned_config.get("enabled", False):
                history = plugin.scheduler_service._load_history()
                pin_status = plugin.boost_service.get_pinned_products_status(history)

            status = {
                "plugin_status": plugin.get_status(),
                "scheduler_running": plugin.scheduler_service.is_running(),
                "next_boost_time": plugin.scheduler_service.get_next_run_time(),
                "last_boost_time": plugin.scheduler_service.get_last_boost_time(),
                "total_boost_sessions": plugin.scheduler_service.get_total_boosts(),
                "config": plugin.config,  # Return full config instead of subset
                "pin_status": pin_status
            }
            return jsonify(status)

        except Exception as e:
            logger.error(f"Error getting boost status: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/products', methods=['GET'])
    def get_boostable_products():
        """Get list of products that can be boosted"""
        try:
            # Check if force refresh is requested
            force_refresh = request.args.get('refresh', 'false').lower() == 'true'
            use_cache = not force_refresh
            
            products = plugin.product_service.get_boostable_products(use_cache=use_cache)
            
            # Return summary information for each product
            product_summaries = [
                plugin.product_service.get_product_summary(product) 
                for product in products
            ]
            
            # Get cache information
            cache_info = plugin.product_service.get_cache_info()
            
            return jsonify({
                "total_count": len(product_summaries),
                "products": product_summaries,
                "cache_info": cache_info,
                "from_cache": use_cache and cache_info.get("cache_valid", False)
            })
            
        except Exception as e:
            logger.error(f"Error getting boostable products: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/boost/manual', methods=['POST'])
    def trigger_manual_boost():
        """Trigger a manual boost execution"""
        try:
            result = plugin.scheduler_service.trigger_manual_boost()
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"Error triggering manual boost: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/boost/single', methods=['POST'])
    def boost_single_product():
        """Boost a single product by ID"""
        try:
            data = request.get_json()
            if not data or 'product_id' not in data:
                return jsonify({"error": "product_id is required"}), 400
            
            product_id = data['product_id']
            success = plugin.boost_service.boost_single_product(product_id)
            
            if success:
                return jsonify({
                    "success": True,
                    "message": f"Product {product_id} boosted successfully"
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"Failed to boost product {product_id}"
                }), 400
                
        except Exception as e:
            logger.error(f"Error boosting single product: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/scheduler/start', methods=['POST'])
    def start_scheduler():
        """Start the auto-boost scheduler"""
        try:
            success = plugin.scheduler_service.start_scheduler()
            
            if success:
                return jsonify({
                    "success": True,
                    "message": "Auto-boost scheduler started",
                    "next_run_time": plugin.scheduler_service.get_next_run_time()
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "Failed to start scheduler"
                }), 500
                
        except Exception as e:
            logger.error(f"Error starting scheduler: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/scheduler/stop', methods=['POST'])
    def stop_scheduler():
        """Stop the auto-boost scheduler"""
        try:
            success = plugin.scheduler_service.stop_scheduler()
            
            if success:
                return jsonify({
                    "success": True,
                    "message": "Auto-boost scheduler stopped"
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "Failed to stop scheduler"
                }), 500
                
        except Exception as e:
            logger.error(f"Error stopping scheduler: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/history', methods=['GET'])
    def get_boost_history():
        """Get boost history and statistics"""
        try:
            history = plugin.boost_service._load_boost_history(plugin.scheduler_service.history_file)
            
            # Format response
            response = {
                "global_stats": history.get("global_stats", {}),
                "recent_products": []
            }
            
            # Get recent product boost info
            product_history = history.get("product_history", {})
            for product_id, info in product_history.items():
                response["recent_products"].append({
                    "product_id": product_id,
                    "total_boosts": info.get("total_boosts", 0),
                    "last_boosted": info.get("last_boosted"),
                    "first_boosted": info.get("first_boosted")
                })
            
            # Sort by last boosted time
            response["recent_products"].sort(
                key=lambda x: x.get("last_boosted", ""), 
                reverse=True
            )
            
            return jsonify(response)
            
        except Exception as e:
            logger.error(f"Error getting boost history: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/config', methods=['PUT'])
    def update_config():
        """Update boost configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No configuration data provided"}), 400
            
            # Update plugin config in memory
            plugin.config.update(data)
            
            # Persist config to file through plugin manager
            if hasattr(plugin, 'plugin_manager') and plugin.plugin_manager:
                plugin.plugin_manager.update_plugin_config(plugin.name, plugin.config)
            
            # Update service URLs if shopee_api_url changed
            if 'shopee_api_url' in data:
                new_api_url = data['shopee_api_url']
                if plugin.product_service:
                    plugin.product_service.api_base_url = new_api_url
                    logger.info(f"Updated product_service API URL to: {new_api_url}")
                if plugin.boost_service:
                    plugin.boost_service.api_base_url = new_api_url  
                    logger.info(f"Updated boost_service API URL to: {new_api_url}")
            
            # Update scheduler interval if changed
            if plugin.scheduler_service.is_running():
                if 'boost_check_interval_minutes' in data:
                    plugin.scheduler_service.update_schedule(new_interval_minutes=data['boost_check_interval_minutes'])
                elif 'boost_interval_hours' in data:
                    plugin.scheduler_service.update_schedule(new_interval_hours=data['boost_interval_hours'])
            
            return jsonify({
                "success": True,
                "message": "Configuration updated successfully",
                "config": plugin.config
            })
            
        except Exception as e:
            logger.error(f"Error updating config: {e}")
            return jsonify({"error": str(e)}), 500

    @blueprint.route('/pinned', methods=['GET'])
    def get_pinned_products():
        """Get list of pinned products with their information"""
        try:
            pinned_info = plugin.product_service.get_pinned_products_info()
            return jsonify({
                "success": True,
                "data": pinned_info
            })

        except Exception as e:
            logger.error(f"Error getting pinned products: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @blueprint.route('/pin/<int:product_id>', methods=['POST'])
    def pin_product(product_id):
        """Pin a product for automatic boosting"""
        try:
            success = plugin.product_service.pin_product(product_id)

            if success:
                return jsonify({
                    "success": True,
                    "message": f"Product {product_id} pinned successfully"
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"Product {product_id} is already pinned"
                }), 400

        except Exception as e:
            logger.error(f"Error pinning product {product_id}: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @blueprint.route('/pin/<int:product_id>', methods=['DELETE'])
    def unpin_product(product_id):
        """Unpin a product from automatic boosting"""
        try:
            success = plugin.product_service.unpin_product(product_id)

            if success:
                return jsonify({
                    "success": True,
                    "message": f"Product {product_id} unpinned successfully"
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"Product {product_id} is not pinned"
                }), 400

        except Exception as e:
            logger.error(f"Error unpinning product {product_id}: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @blueprint.route('/cooldowns', methods=['GET'])
    def get_cooldowns():
        """Get cooldown status of all pinned products"""
        try:
            history = plugin.scheduler_service._load_history()
            cooldown_status = plugin.boost_service.get_pinned_products_status(history)

            return jsonify({
                "success": True,
                "data": cooldown_status
            })

        except Exception as e:
            logger.error(f"Error getting cooldowns: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @blueprint.route('/cache/info', methods=['GET'])
    def get_cache_info():
        """Get cache information"""
        try:
            cache_info = plugin.product_service.get_cache_info()
            return jsonify({
                "success": True,
                "data": cache_info
            })

        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @blueprint.route('/cache/clear', methods=['DELETE'])
    def clear_cache():
        """Clear the products cache"""
        try:
            success = plugin.product_service.clear_cache()
            
            if success:
                return jsonify({
                    "success": True,
                    "message": "Products cache cleared successfully"
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "Failed to clear cache"
                }), 500

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @blueprint.route('/bumped', methods=['GET'])
    def get_bumped_products():
        """Get list of bumped products with cooldown information"""
        try:
            bumped_data = plugin.product_service.get_bumped_products()
            return jsonify(bumped_data)

        except Exception as e:
            logger.error(f"Error getting bumped products: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    return blueprint
