# Shopee Auto Boost Plugin

This plugin automatically boosts 5 products every 4 hours on Shopee to improve product visibility and sales. The plugin uses the local ShopeeAPI service for all Shopee interactions, following proper architectural separation.

## Features

- **Automatic Boosting**: Boosts 5 products every 4 hours automatically
- **PIN Mode**: Pin specific products for automatic boosting with per-product cooldown tracking
- **Smart Product Selection**: Uses rotation strategies to ensure fair boosting
- **Product Filtering**: Only boosts active, listed products with sufficient stock
- **Per-Product Cooldowns**: Each pinned product has individual 4-hour cooldown after successful boost
- **Boost History**: Tracks boost history and statistics
- **Manual Control**: API endpoints for manual boost control
- **Configurable**: Customizable boost intervals, product count, and filters

## Configuration

The plugin can be configured in `configs/core/plugin_config.json`:

```json
{
  "shopee_auto_boost": {
    "enabled": true,
    "boost_interval_hours": 4,
    "products_per_boost": 5,
    "auto_start": true,
    "shopee_api_url": "http://localhost:8000",
    "product_filters": {
      "min_stock": 1,
      "exclude_unlisted": true,
      "exclude_inactive": true
    },
    "rotation_strategy": "least_recently_boosted"
  }
}
```

### Configuration Options

- `boost_interval_hours`: Hours between automatic boost sessions (default: 4)
- `products_per_boost`: Number of products to boost per session (default: 5)
- `auto_start`: Whether to start the scheduler automatically (default: true)
- `shopee_api_url`: URL of the local ShopeeAPI service (default: "http://localhost:8000")
- `product_filters`: Filters for product selection
  - `min_stock`: Minimum stock required (default: 1)
  - `exclude_unlisted`: Skip unlisted products (default: true)
  - `exclude_inactive`: Skip inactive products (default: true)
- `rotation_strategy`: Product selection strategy (only used when PIN mode is disabled)
  - `least_recently_boosted`: Boost products that haven't been boosted recently
  - `random`: Random selection
  - `highest_sold`: Prioritize products with highest sales
- `pinned_products`: PIN mode configuration
  - `enabled`: Enable PIN mode for automatic boosting (default: true)
  - `product_ids`: Array of product IDs to automatically boost (default: [])
  - `cooldown_hours`: Hours to wait after successful boost before boosting again (default: 4)

## PIN Mode

When PIN mode is enabled, the plugin will only boost the specific products you've pinned. Each pinned product has its own 4-hour cooldown timer that starts only after a successful boost.

### Key Features:
- **Individual Cooldowns**: Each product has its own 4-hour cooldown timer
- **Success-Only Cooldowns**: Failed boosts don't trigger cooldown, so the product remains available
- **Automatic Selection**: The plugin automatically selects available pinned products (not on cooldown)
- **Flexible Count**: If fewer than 5 pinned products are available, it boosts only the available ones

### Example Workflow:
1. Pin 5 products for auto-boosting
2. Plugin runs every 4 hours and checks which pinned products are available
3. Boosts available products (those not on cooldown)
4. Successful boosts start a 4-hour cooldown for that specific product
5. Failed boosts don't start cooldown, keeping the product available for next cycle

## API Endpoints

### Status and Information

- `GET /api/shopee_auto_boost/status` - Get boost status and statistics
- `GET /api/shopee_auto_boost/products` - Get list of boostable products
- `GET /api/shopee_auto_boost/history` - Get boost history

### Manual Control

- `POST /api/shopee_auto_boost/boost/manual` - Trigger manual boost
- `POST /api/shopee_auto_boost/boost/single` - Boost a single product
  ```json
  {
    "product_id": 29577128498
  }
  ```

### Scheduler Control

- `POST /api/shopee_auto_boost/scheduler/start` - Start auto-boost scheduler
- `POST /api/shopee_auto_boost/scheduler/stop` - Stop auto-boost scheduler

### PIN Management

- `GET /api/shopee_auto_boost/pinned` - Get list of pinned products with their information
- `POST /api/shopee_auto_boost/pin/{product_id}` - Pin a product for automatic boosting
- `DELETE /api/shopee_auto_boost/pin/{product_id}` - Unpin a product from automatic boosting
- `GET /api/shopee_auto_boost/cooldowns` - Get cooldown status of all pinned products

### Configuration

- `PUT /api/shopee_auto_boost/config` - Update configuration
  ```json
  {
    "boost_interval_hours": 6,
    "products_per_boost": 3,
    "pinned_products": {
      "enabled": true,
      "product_ids": [123456789, 987654321],
      "cooldown_hours": 4
    }
  }
  ```

## How It Works

### Standard Mode (PIN disabled):
1. **Product Discovery**: Fetches all products via local ShopeeAPI service
2. **Filtering**: Applies filters to find boostable products (done by ShopeeAPI service)
3. **Selection**: Uses rotation strategy to select products for boosting
4. **Boosting**: Calls local ShopeeAPI service to boost products (which then calls Shopee's API)
5. **History**: Updates boost history and statistics
6. **Scheduling**: Schedules next boost session

### PIN Mode (PIN enabled):
1. **Product Discovery**: Fetches all products via local ShopeeAPI service
2. **PIN Filtering**: Filters products to only include pinned product IDs
3. **Cooldown Check**: Removes products that are still on cooldown from previous successful boosts
4. **Selection**: Selects all available pinned products (up to configured limit)
5. **Boosting**: Calls local ShopeeAPI service to boost selected products
6. **Cooldown Update**: Updates individual cooldown timers for successfully boosted products
7. **History**: Updates boost history and per-product statistics
8. **Scheduling**: Schedules next boost session

## Architecture

The plugin follows a proper architectural separation:

```
Plugin → Local ShopeeAPI Service (port 8000) → Shopee's External API
```

- **Plugin**: Handles scheduling, product selection logic, and boost history
- **ShopeeAPI Service**: Manages all external API calls, authentication, and credentials
- **Shopee API**: External Shopee seller API endpoints

This architecture ensures:
- No direct external API calls from plugins
- Centralized credential management
- Better error handling and retry logic
- Consistent API interaction patterns

## Product Selection Logic

The plugin filters products based on:
- Product status (must be active)
- Listing status (must not be unlisted)
- Boost eligibility (`boost_entry_status` must be 1)
- Stock level (must meet minimum stock requirement)

## Boost History

The plugin maintains boost history in `plugins/shopee_auto_boost/boost_history.json`:

```json
{
  "global_stats": {
    "total_boost_sessions": 10,
    "total_products_boosted": 50,
    "total_failures": 2,
    "first_boost_session": "2025-01-18T10:00:00",
    "last_boost_session": "2025-01-18T14:00:00"
  },
  "product_history": {
    "29577128498": {
      "total_boosts": 3,
      "first_boosted": "2025-01-18T10:00:00",
      "last_boosted": "2025-01-18T14:00:00"
    }
  },
  "product_cooldowns": {
    "29577128498": {
      "last_successful_boost": "2025-01-18T14:00:00",
      "next_available_boost": "2025-01-18T18:00:00",
      "total_successful_boosts": 3,
      "total_failed_attempts": 1
    }
  }
}
```

### Cooldown Tracking (PIN Mode)
- `last_successful_boost`: Timestamp of the last successful boost
- `next_available_boost`: When the product will be available for boosting again
- `total_successful_boosts`: Count of successful boosts for this product
- `total_failed_attempts`: Count of failed boost attempts (doesn't affect cooldown)

## Dependencies

- **ShopeeAPI Service**: Must be running on port 8000 (or configured URL)
- **APScheduler**: For scheduling automatic boosts
- **Requests**: For HTTP communication with ShopeeAPI service

### Prerequisites

1. **ShopeeAPI Service**: Ensure the ShopeeAPI service is running and accessible
   ```bash
   cd ShopeeAPI
   python main.py
   ```

2. **Valid Credentials**: The ShopeeAPI service must have valid Shopee credentials configured

3. **Network Connectivity**: Plugin must be able to reach the ShopeeAPI service URL

## Error Handling

- API failures are logged but don't stop the scheduler
- Invalid products are skipped gracefully
- Network issues trigger retry logic
- Failed boosts are tracked in history

## Logging

The plugin logs all boost activities:
- Boost session start/end
- Product selection details
- API call results
- Error conditions

## Installation

The plugin is automatically loaded when the main application starts if enabled in the configuration.

## Troubleshooting

1. **Plugin initialization fails**:
   - Check if ShopeeAPI service is running on the configured URL
   - Verify network connectivity to the ShopeeAPI service
   - Check ShopeeAPI service logs for errors

2. **No products found**:
   - Verify ShopeeAPI service has valid credentials
   - Check product filters configuration
   - Test ShopeeAPI endpoints directly: `GET http://localhost:8000/products/boostable`

3. **Boost failures**:
   - Check ShopeeAPI service credentials and authentication
   - Verify products are eligible for boosting
   - Check ShopeeAPI service logs for detailed error messages

4. **History not saving**:
   - Verify file permissions in plugin directory
   - Check disk space availability

5. **Connection errors**:
   - Ensure ShopeeAPI service is running: `GET http://localhost:8000/status`
   - Check firewall settings
   - Verify `shopee_api_url` configuration is correct
