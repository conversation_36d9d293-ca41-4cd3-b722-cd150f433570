#!/usr/bin/env python3
"""
Test script for VPN Server Creation Template with SSH and Xray testing functionality
"""

import os
import sys
import json
import requests
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

def test_template_rendering():
    """Test that the VPN server form template renders correctly"""
    try:
        template_path = os.path.join(os.path.dirname(__file__), 'templates', 'vpn_server_form.html')
        
        if not os.path.exists(template_path):
            print("❌ Template file not found")
            return False
            
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for key elements
        required_elements = [
            'Test SSH Connection',
            'Test Xray Service', 
            'testSSHBtn',
            'testXrayBtn',
            'testResults',
            'Connection Testing'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
                
        if missing_elements:
            print(f"❌ Missing template elements: {', '.join(missing_elements)}")
            return False
            
        print("✅ Template contains all required elements")
        return True
        
    except Exception as e:
        print(f"❌ Template test failed: {e}")
        return False

def test_javascript_functionality():
    """Test that JavaScript functionality is properly implemented"""
    try:
        template_path = os.path.join(os.path.dirname(__file__), 'templates', 'vpn_server_form.html')
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for JavaScript functions
        js_functions = [
            'function testSSHConnection',
            'function testXrayService',
            'showTestResult',
            '/vpn/api/test-ssh-credentials',
            '/vpn/api/test-xray-service'
        ]
        
        missing_functions = []
        for func in js_functions:
            if func not in content:
                missing_functions.append(func)
                
        if missing_functions:
            print(f"❌ Missing JavaScript functions: {', '.join(missing_functions)}")
            return False
            
        print("✅ JavaScript functionality is properly implemented")
        return True
        
    except Exception as e:
        print(f"❌ JavaScript test failed: {e}")
        return False

def test_api_endpoints_exist():
    """Test that the required API endpoints are defined in routes"""
    try:
        routes_path = os.path.join(os.path.dirname(__file__), 'routes', 'vpn_routes.py')
        
        with open(routes_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for API endpoints
        required_endpoints = [
            '/api/test-ssh-credentials',
            '/api/test-xray-service',
            'def test_ssh_credentials',
            'def test_xray_service'
        ]
        
        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint not in content:
                missing_endpoints.append(endpoint)
                
        if missing_endpoints:
            print(f"❌ Missing API endpoints: {', '.join(missing_endpoints)}")
            return False
            
        print("✅ All required API endpoints are defined")
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test failed: {e}")
        return False

def test_api_service_methods():
    """Test that the required methods exist in the API service"""
    try:
        service_path = os.path.join(os.path.dirname(__file__), 'services', 'vpn_api_service.py')
        
        with open(service_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for service methods
        required_methods = [
            'def test_server_credentials',
            'def test_xray_service_config',
            'def test_credentials'
        ]
        
        missing_methods = []
        for method in required_methods:
            if method not in content:
                missing_methods.append(method)
                
        if missing_methods:
            print(f"❌ Missing API service methods: {', '.join(missing_methods)}")
            return False
            
        print("✅ All required API service methods are defined")
        return True
        
    except Exception as e:
        print(f"❌ API service methods test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("🧪 Testing VPN Server Creation Template with SSH and Xray Testing")
    print("=" * 70)
    
    tests = [
        ("Template Rendering", test_template_rendering),
        ("JavaScript Functionality", test_javascript_functionality),
        ("API Endpoints", test_api_endpoints_exist),
        ("API Service Methods", test_api_service_methods)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"   Test failed for {test_name}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The VPN server creation template is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)