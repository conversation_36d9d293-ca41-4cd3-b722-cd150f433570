#!/usr/bin/env python3
"""
Test script for SSH User Management functionality in VPN Plugin
"""

import sys
import os
import json
import logging

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from plugins.vpn.services.vpn_api_service import VPNAPIService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_ssh_user_management():
    """Test SSH user management functionality"""
    
    # Test configuration
    config = {
        'vpn_api': {
            'base_url': 'https://blueblue.api.online-mtyb.com',
            'username': 'admin',
            'password': 'admin123',
            'timeout': 30
        }
    }
    
    # Initialize API service
    api_service = VPNAPIService(config)
    
    print("🔧 Testing SSH User Management Functionality")
    print("=" * 50)
    
    # Test authentication
    print("\n1. Testing API Authentication...")
    if api_service.authenticate():
        print("✅ Authentication successful")
    else:
        print("❌ Authentication failed")
        return False
    
    # Get servers first
    print("\n2. Getting available servers...")
    servers = api_service.get_servers()
    if not servers:
        print("❌ No servers found. Please add servers first.")
        return False
    
    print(f"✅ Found {len(servers)} servers")
    
    # Test with the first server
    test_server = servers[0]
    server_id = test_server['id']
    server_name = test_server.get('name', 'Unknown')
    
    print(f"\n3. Testing SSH user management on server: {server_name} (ID: {server_id})")
    
    # Test getting SSH users
    print("\n   3.1. Getting SSH users...")
    try:
        users_result = api_service.get_ssh_users(server_id)
        if users_result:
            if 'error' in users_result:
                print(f"⚠️  API returned error: {users_result['error']}")
            else:
                user_count = users_result.get('count', 0)
                print(f"✅ Found {user_count} SSH users")
                
                if users_result.get('members'):
                    print("   Users:")
                    for user in users_result['members'][:3]:  # Show first 3 users
                        print(f"     - {user.get('username')} (UID: {user.get('uid')}, Expiry: {user.get('expiry')}, Status: {user.get('status')})")
        else:
            print("❌ Failed to get SSH users")
    except Exception as e:
        print(f"❌ Error getting SSH users: {e}")
    
    # Test getting SSH sessions
    print("\n   3.2. Getting SSH sessions...")
    try:
        sessions_result = api_service.get_ssh_sessions(server_id)
        if sessions_result:
            if 'error' in sessions_result:
                print(f"⚠️  API returned error: {sessions_result['error']}")
            else:
                dropbear_count = len(sessions_result.get('dropbear', []))
                openssh_count = len(sessions_result.get('openssh', []))
                openvpn_tcp_count = len(sessions_result.get('openvpn_tcp', []))
                openvpn_udp_count = len(sessions_result.get('openvpn_udp', []))
                
                print(f"✅ Sessions found:")
                print(f"     - Dropbear: {dropbear_count}")
                print(f"     - OpenSSH: {openssh_count}")
                print(f"     - OpenVPN TCP: {openvpn_tcp_count}")
                print(f"     - OpenVPN UDP: {openvpn_udp_count}")
        else:
            print("❌ Failed to get SSH sessions")
    except Exception as e:
        print(f"❌ Error getting SSH sessions: {e}")
    
    # Test getting auto-kill status
    print("\n   3.3. Getting auto-kill status...")
    try:
        autokill_result = api_service.get_ssh_autokill_status(server_id)
        if autokill_result:
            if 'error' in autokill_result:
                print(f"⚠️  API returned error: {autokill_result['error']}")
            else:
                enabled = autokill_result.get('enabled', False)
                if enabled:
                    interval = autokill_result.get('interval_minutes', 'N/A')
                    max_sessions = autokill_result.get('max_sessions', 'N/A')
                    print(f"✅ Auto-kill enabled: {interval} min intervals, max {max_sessions} sessions")
                else:
                    print("✅ Auto-kill disabled")
        else:
            print("❌ Failed to get auto-kill status")
    except Exception as e:
        print(f"❌ Error getting auto-kill status: {e}")
    
    # Test getting limit violations
    print("\n   3.4. Getting limit violations...")
    try:
        violations_result = api_service.get_ssh_limit_violations(server_id)
        if violations_result:
            if 'error' in violations_result:
                print(f"⚠️  API returned error: {violations_result['error']}")
            else:
                exists = violations_result.get('exists', False)
                if exists:
                    lines = violations_result.get('lines', [])
                    print(f"⚠️  Found {len(lines)} violation entries")
                else:
                    print("✅ No limit violations found")
        else:
            print("❌ Failed to get limit violations")
    except Exception as e:
        print(f"❌ Error getting limit violations: {e}")
    
    print("\n4. Testing API methods...")
    
    # Test renew user method (without actually calling it)
    print("   4.1. Testing renew user method structure...")
    try:
        # This will test the method structure without making the actual call
        method_exists = hasattr(api_service, 'renew_ssh_user')
        print(f"✅ renew_ssh_user method exists: {method_exists}")
    except Exception as e:
        print(f"❌ Error testing renew user method: {e}")
    
    # Test auto delete method structure
    print("   4.2. Testing auto delete method structure...")
    try:
        method_exists = hasattr(api_service, 'auto_delete_expired_ssh_users')
        print(f"✅ auto_delete_expired_ssh_users method exists: {method_exists}")
    except Exception as e:
        print(f"❌ Error testing auto delete method: {e}")
    
    # Test configure autokill method structure
    print("   4.3. Testing configure autokill method structure...")
    try:
        method_exists = hasattr(api_service, 'configure_ssh_autokill')
        print(f"✅ configure_ssh_autokill method exists: {method_exists}")
    except Exception as e:
        print(f"❌ Error testing configure autokill method: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 SSH User Management test completed!")
    print("\nNote: This test verifies the API integration and method availability.")
    print("For full functionality testing, ensure your API server is running and accessible.")
    
    return True

if __name__ == "__main__":
    try:
        test_ssh_user_management()
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
