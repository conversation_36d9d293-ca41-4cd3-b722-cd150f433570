#!/usr/bin/env python3
"""
Test script for order-based conversation creation functionality.
This tests the fallback mechanism for users who haven't chatted before.
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"
TEST_USERNAMES = ["effansyiddiq", "si_lun"]  # Users from the order data

def test_conversation_search_with_fallback(username):
    """Test the main conversation search endpoint with order fallback"""
    print(f"\n=== Testing conversation search with fallback for: {username} ===")
    
    url = f"{BASE_URL}/chat/search_conversation"
    params = {"username": username}
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Successfully found/created conversation")
            print(f"Conversation ID: {data['data'].get('id')}")
            print(f"User ID: {data['data'].get('to_id')}")
            print(f"Username: {data['data'].get('to_name')}")
            return data['data']
        else:
            print(f"❌ Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None

def test_order_based_search_directly(username):
    """Test the direct order-based search endpoint"""
    print(f"\n=== Testing direct order-based search for: {username} ===")
    
    url = f"{BASE_URL}/chat/search_conversation_from_orders"
    params = {"username": username}
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Successfully found/created conversation via orders")
            print(f"Conversation ID: {data['data'].get('id')}")
            print(f"User ID: {data['data'].get('to_id')}")
            print(f"Username: {data['data'].get('to_name')}")
            return data['data']
        else:
            print(f"❌ Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None

def test_redirection_endpoint_directly(user_id, username):
    """Test the direct redirection endpoint"""
    print(f"\n=== Testing direct redirection endpoint for user_id: {user_id}, username: {username} ===")
    
    url = f"{BASE_URL}/chat/create_conversation_redirection"
    payload = {
        "user_id": user_id,
        "username": username
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Successfully created conversation via redirection")
            print(f"Conversation ID: {data['data'].get('id')}")
            print(f"User ID: {data['data'].get('to_id')}")
            print(f"Username: {data['data'].get('to_name')}")
            return data['data']
        else:
            print(f"❌ Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return None

def test_get_to_ship_orders():
    """Test getting to_ship orders to see available users"""
    print(f"\n=== Testing to_ship orders endpoint ===")
    
    url = f"{BASE_URL}/orders/to_ship"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            orders = data.get('data', {}).get('card_list', [])
            print(f"✅ Found {len(orders)} orders")
            
            # Extract usernames and user_ids
            users = []
            for order in orders:
                order_card = order.get('order_card', {})
                buyer_info = order_card.get('card_header', {}).get('buyer_info', {})
                order_ext_info = order_card.get('order_ext_info', {})
                
                username = buyer_info.get('username')
                user_id = order_ext_info.get('buyer_user_id')
                order_sn = order_card.get('card_header', {}).get('order_sn')
                
                if username and user_id:
                    users.append({
                        'username': username,
                        'user_id': user_id,
                        'order_sn': order_sn
                    })
                    print(f"  - {username} (ID: {user_id}, Order: {order_sn})")
            
            return users
        else:
            print(f"❌ Error: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return []

def main():
    """Main test function"""
    print("🚀 Starting Order-Based Conversation Test")
    print("=" * 50)
    
    # First, get available users from orders
    users = test_get_to_ship_orders()
    
    if not users:
        print("❌ No users found in to_ship orders. Cannot proceed with tests.")
        return
    
    # Test with the first available user
    test_user = users[0]
    username = test_user['username']
    user_id = test_user['user_id']
    
    print(f"\n🎯 Testing with user: {username} (ID: {user_id})")
    
    # Test 1: Main conversation search (should work with fallback)
    conversation_data = test_conversation_search_with_fallback(username)
    
    # Test 2: Direct order-based search
    order_conversation_data = test_order_based_search_directly(username)
    
    # Test 3: Direct redirection endpoint
    redirection_data = test_redirection_endpoint_directly(user_id, username)
    
    # Test 4: Test with a non-existent user
    print(f"\n=== Testing with non-existent user ===")
    test_conversation_search_with_fallback("nonexistent_user_12345")
    
    print(f"\n🏁 Test completed!")
    print("=" * 50)
    
    # Summary
    print("\n📊 Test Results Summary:")
    print(f"✅ Main search (with fallback): {'SUCCESS' if conversation_data else 'FAILED'}")
    print(f"✅ Direct order search: {'SUCCESS' if order_conversation_data else 'FAILED'}")
    print(f"✅ Direct redirection: {'SUCCESS' if redirection_data else 'FAILED'}")

if __name__ == "__main__":
    main() 