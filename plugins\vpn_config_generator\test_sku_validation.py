#!/usr/bin/env python3
"""
Test script to verify SKU validation in VPN Config Generator
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def test_sku_validation():
    """Test SKU validation for VPN products"""
    print("Testing SKU validation for VPN Config Generator...")
    
    # Test cases
    test_cases = [
        # Valid VPN SKUs
        ("my_basic", True, "Malaysia basic VPN"),
        ("sg_premium", True, "Singapore premium VPN"),
        ("vpn_service_30", True, "Generic VPN service"),
        ("vpn-speed", True, "VPN speed service"),
        
        # Invalid non-VPN SKUs
        ("canva_30", False, "Canva subscription"),
        ("office_365", False, "Office 365 subscription"),
        ("spotify_premium", False, "Spotify premium"),
        ("netflix_basic", False, "Netflix basic"),
        ("", False, "Empty SKU"),
        (None, False, "None SKU"),
    ]
    
    try:
        from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
        vpn_available = True
    except ImportError:
        print("❌ VPN strategy factory not available - cannot test SKU validation")
        return False
    
    print(f"✅ VPN strategy factory available")
    
    all_passed = True
    
    for sku, expected_valid, description in test_cases:
        try:
            is_valid = VPNStrategyFactory.is_vpn_product(sku)
            
            if is_valid == expected_valid:
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
                all_passed = False
            
            print(f"{status} SKU: '{sku}' -> {is_valid} (expected: {expected_valid}) - {description}")
            
        except Exception as e:
            print(f"❌ ERROR testing SKU '{sku}': {e}")
            all_passed = False
    
    return all_passed

def test_order_validation():
    """Test order validation with mock data"""
    print("\nTesting order validation with mock data...")

    # Mock order data for testing
    mock_orders = [
        {
            "order_sn": "VPN_TEST_001",
            "sku": "my_basic",
            "var_sku": "my_basic_30",
            "expected_success": True,
            "description": "Valid VPN order"
        },
        {
            "order_sn": "CANVA_TEST_001",
            "sku": "canva_30",
            "var_sku": "canva_30",
            "expected_success": False,
            "description": "Invalid Canva order"
        }
    ]

    try:
        from plugins.vpn_config_generator.services import VPNOrderService
        from plugins.vpn_config_generator.models import VPNOrderRequest

        # Create a mock order service (this would need proper initialization in real test)
        print("✅ VPN order service classes available")

        for mock_order in mock_orders:
            print(f"📝 Test case: {mock_order['description']}")
            print(f"   SKU: {mock_order['sku']}, var_sku: {mock_order['var_sku']}")
            print(f"   Expected success: {mock_order['expected_success']}")

    except ImportError as e:
        print(f"❌ Cannot import VPN order service: {e}")
        return False

    return True

def test_api_sku_validation():
    """Test SKU validation in direct API calls"""
    print("\nTesting API SKU validation...")

    # Test cases for direct API calls
    api_test_cases = [
        {
            "sku": "my_basic",
            "expected_valid": True,
            "description": "Valid VPN SKU in API call"
        },
        {
            "sku": "canva_30",
            "expected_valid": False,
            "description": "Invalid non-VPN SKU in API call"
        },
        {
            "sku": None,
            "expected_valid": True,  # No SKU provided, should pass (optional validation)
            "description": "No SKU provided (optional validation)"
        }
    ]

    try:
        from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory

        for test_case in api_test_cases:
            sku = test_case["sku"]
            expected_valid = test_case["expected_valid"]
            description = test_case["description"]

            if sku is None:
                # No SKU provided - should pass
                result = True
                status = "✅ PASS" if result == expected_valid else "❌ FAIL"
                print(f"{status} {description}")
            else:
                # SKU provided - validate it
                is_valid = VPNStrategyFactory.is_vpn_product(sku)
                status = "✅ PASS" if is_valid == expected_valid else "❌ FAIL"
                print(f"{status} API SKU: '{sku}' -> {is_valid} (expected: {expected_valid}) - {description}")

        return True

    except ImportError:
        print("❌ VPN strategy factory not available for API SKU validation test")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("VPN Config Generator SKU Validation Test")
    print("=" * 60)

    # Test SKU validation logic
    sku_test_passed = test_sku_validation()

    # Test order validation (basic import test)
    order_test_passed = test_order_validation()

    # Test API SKU validation
    api_test_passed = test_api_sku_validation()

    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"SKU Validation: {'✅ PASSED' if sku_test_passed else '❌ FAILED'}")
    print(f"Order Service: {'✅ AVAILABLE' if order_test_passed else '❌ UNAVAILABLE'}")
    print(f"API Validation: {'✅ PASSED' if api_test_passed else '❌ FAILED'}")

    if sku_test_passed and order_test_passed and api_test_passed:
        print("\n🎉 All tests passed! SKU validation is working correctly.")
        print("\nThe VPN Config Generator will now:")
        print("✅ Accept VPN products (my_*, sg_*, vpn_*, vpn-*)")
        print("❌ Reject non-VPN products (canva_*, office_*, etc.)")
        print("✅ Validate SKUs in both order verification and direct API calls")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")

    print("=" * 60)
