[{"id": "digi-booster", "name": "<PERSON><PERSON>", "description": "Digi booster plan configuration", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#DU_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "digi", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "digi-booster2", "name": "Digi Booster 2", "description": "Digi booster plan configuration variant 2", "template": "vless://{uuid}@opensignal.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=#DU2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "server_domain": "Server domain", "port": "Server port", "path": "WebSocket path", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "digi", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "digi-social", "name": "Digi Social", "description": "Digi social plan configuration", "template": "vless://{uuid}@ssl.google-analytics.com.api.digi.com.my.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=ssl.google-analytics.com#DS_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "server_domain": "Server domain", "port": "Server port", "path": "WebSocket path", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "digi", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "digi-social2", "name": "Digi Social 2", "description": "Digi social plan configuration variant 2", "template": "vless://{uuid}@m.facebook.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.facebook.com#DS2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "server_domain": "Server domain", "port": "Server port", "path": "WebSocket path", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "digi", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "umobile-funz", "name": "U Mobile Funz", "description": "U Mobile Funz plan configuration", "template": "vless://{uuid}@www.pubgmobile.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.pubgmobile.com#UN_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "server_domain": "Server domain", "port": "Server port", "path": "WebSocket path", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "umobile", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "umobile-no-plan4", "name": "U Mobile No Plan 4", "description": "U Mobile no plan 4 configuration", "template": "vless://{uuid}@***********:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cityofpalacios.org.{server_domain}#UNP4_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "umobile", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "celcom-booster", "name": "Celcom Bo<PERSON>er", "description": "Celcom booster plan configuration", "template": "vless://{uuid}@www.speedtest.net.cdn.cloudflare.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#CB_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "celcom", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "celcom-booster2", "name": "Celcom Booster 2", "description": "Celcom booster plan configuration variant 2", "template": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#CB2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "celcom", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "maxis-freeze", "name": "<PERSON><PERSON>", "description": "Maxis freeze plan configuration", "template": "vless://{uuid}@cdn.opensignal.com:80?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cdn.opensignal.com.{server_domain}#MF_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "maxis", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "maxis-booster", "name": "<PERSON><PERSON>", "description": "Maxis booster plan configuration", "template": "vless://{uuid}@************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cdn.opensignal.com.{server_domain}#MF_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "maxis", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "tunetalk-booster", "name": "<PERSON>neT<PERSON>", "description": "TuneTalk booster plan configuration", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#TB_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "tunetalk", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "tunetalk-booster2", "name": "TuneTalk Booster 2", "description": "TuneTalk booster plan configuration variant 2", "template": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#TB2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "tunetalk", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "tunetalk-booster3", "name": "TuneTalk Booster 3", "description": "TuneTalk booster plan configuration variant 3", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=api.fast.com.{server_domain}#TB3_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "tunetalk", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "yes-no-plan2", "name": "Yes No Plan 2", "description": "Yes no plan 2 configuration", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=tap-database.who.int.{server_domain}#YN2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "yes", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "yoodo-booster", "name": "<PERSON><PERSON><PERSON>", "description": "Yoodo booster plan configuration", "template": "vless://{uuid}@*************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#YoB_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "yoodo", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "yoodo-booster2", "name": "Yoodo Booster 2", "description": "Yoodo booster plan configuration variant 2", "template": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#YoB2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "yoodo", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "yoodo-booster3", "name": "Yoodo Booster 3", "description": "Yoodo booster plan configuration variant 3", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=api.fast.com.{server_domain}#YoB3_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "yoodo", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "yoodo-pubg", "name": "Yoodo PUBG", "description": "Yoodo PUBG plan configuration", "template": "vless://{uuid}@m.pubgmobile.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.pubgmobile.com#YoP_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "server_domain": "Server domain", "port": "Server port", "path": "WebSocket path", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "yoodo", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "yoodo-mobilelegend", "name": "Yoodo Mobile Legend", "description": "Yoodo Mobile Legend plan configuration", "template": "vless://{uuid}@m.mobilelegends.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.mobilelegends.com#YoM_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "server_domain": "Server domain", "port": "Server port", "path": "WebSocket path", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "yoodo", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "unifi-bebas", "name": "Un<PERSON><PERSON>", "description": "Unifi bebas plan configuration", "template": "vless://{uuid}@************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#UnN_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "unifi", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}, {"id": "unifi-wow", "name": "Unifi Wow", "description": "Unifi wow plan configuration", "template": "vless://{uuid}@covidnow.pages.dev:{port}?security=none&encryption=none&type=ws&headerType=none&path=ws://{server_domain}{path}&host=www.speedtest.net#UnN2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "server_domain": "Server domain", "path": "WebSocket path", "server_name": "Server name", "expired_date": "Expiration date"}, "category": "unifi", "enabled": true, "created_at": "2025-07-25T00:00:00.000000", "updated_at": "2025-07-25T00:00:00.000000"}]