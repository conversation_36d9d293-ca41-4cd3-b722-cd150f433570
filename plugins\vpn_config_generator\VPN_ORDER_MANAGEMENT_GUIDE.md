# VPN Configuration Management System

## Overview

The VPN Configuration Management System provides a comprehensive web interface for customers to verify their orders and generate VPN configurations with built-in access control and user tracking.

## Features

### 🔐 Order Status Verification & Processing
- **Order Lookup Interface**: Customers can enter their order ID for verification
- **Status Checking**: Only orders with "To Ship" status can proceed to VPN configuration
- **Automatic Shipping**: Orders are automatically shipped upon successful verification
- **Order Integration**: Seamlessly integrates with existing Shopee order management system

### 👤 User UUID Management
- **UUID Generation**: Each verified order creates a unique user UUID for tracking
- **Repeat Customer Recognition**: Returning customers are identified by their UUID
- **Session Management**: User data persists across multiple configuration generations
- **Access History**: Tracks user's last access time and configuration count

### 🏢 SKU-Based Access Control
- **Configurable Restrictions**: Define access rules based on SKU patterns
- **Telco Locking**: Users with "my_" prefixed SKUs are locked to their first selected telco
- **Pattern Matching**: Flexible SKU pattern matching using wildcards
- **Restriction Management**: Add, update, and delete access restrictions through configuration

### 📱 Service Selection System
- **Interactive Modal**: User-friendly interface for selecting telco and plan
- **Dynamic Options**: Available options based on user's access permissions
- **Visual Feedback**: Clear indication of selected services and restrictions
- **Validation**: Ensures all required fields are completed before generation

## Architecture

### Models

#### VPNUser
```python
@dataclass
class VPNUser:
    uuid: str                           # Unique user identifier
    order_sn: str                      # Order number
    buyer_username: str                # Customer username
    sku: str                          # Product SKU
    var_sku: str                      # Variation SKU
    assigned_telco: Optional[str]      # Locked telco (for restricted users)
    allowed_telcos: List[str]          # List of allowed telcos
    is_restricted: bool                # Whether user has restrictions
    created_at: Optional[str]          # Account creation timestamp
    last_access: Optional[str]         # Last access timestamp
    configurations_generated: int      # Number of configs generated
```

#### SKURestriction
```python
@dataclass
class SKURestriction:
    sku_pattern: str                   # SKU pattern to match (e.g., "my_*")
    restriction_type: str              # Type of restriction
    allowed_telcos: List[str]          # Allowed telcos for this SKU
    max_configurations: int            # Maximum configs (-1 for unlimited)
    single_telco_only: bool           # Lock to single telco after first selection
    description: str                   # Human-readable description
    enabled: bool                     # Whether restriction is active
```

### Services

#### VPNOrderService
- **Order Processing**: Verifies order status and creates/retrieves users
- **User Management**: Handles user creation, updates, and access control
- **Restriction Management**: Applies and manages SKU-based restrictions
- **Statistics**: Provides user and usage statistics

### API Endpoints

#### `/vpn-config-generator/order-config`
- **Method**: GET
- **Purpose**: Serves the main VPN configuration management webpage
- **Returns**: HTML page with telco selection interface

#### `/vpn-config-generator/api/order/verify`
- **Method**: POST
- **Purpose**: Verify order status and create/retrieve user
- **Payload**: `{"order_sn": "string", "buyer_username": "string"}`
- **Returns**: User data with access permissions and restrictions

#### `/vpn-config-generator/api/order/generate-config`
- **Method**: POST
- **Purpose**: Generate VPN configuration for verified user
- **Payload**: 
  ```json
  {
    "user_uuid": "string",
    "order_sn": "string", 
    "server": "string",
    "days": "number",
    "telco": "string",
    "plan": "string"
  }
  ```
- **Returns**: Generated VPN configuration with metadata

## Configuration

### SKU Restrictions

Default restrictions are automatically created in `configs/plugins/vpn_config_generator/sku_restrictions.json`:

```json
[
  {
    "sku_pattern": "my_*",
    "restriction_type": "telco_lock",
    "single_telco_only": true,
    "description": "Users with 'my_' prefixed SKUs are locked to their first selected telco",
    "enabled": true
  }
]
```

### User Data Storage

User data is stored in `configs/plugins/vpn_config_generator/vpn_users.json`:

```json
{
  "user-uuid-here": {
    "uuid": "user-uuid-here",
    "order_sn": "ORDER123",
    "buyer_username": "customer1",
    "sku": "vpn_service",
    "var_sku": "my_highspeed_15",
    "assigned_telco": "digi",
    "allowed_telcos": ["digi"],
    "is_restricted": true,
    "created_at": "2024-01-01T12:00:00",
    "last_access": "2024-01-01T12:30:00",
    "configurations_generated": 2
  }
}
```

## Usage Workflow

### For New Customers

1. **Order Verification**
   - Customer enters their order ID
   - System verifies order status is "To Ship"
   - Order is automatically shipped
   - User UUID is generated and stored

2. **Service Selection**
   - Modal displays available telcos and plans
   - Customer selects preferred telco and plan
   - For restricted SKUs, telco selection is locked after first choice

3. **Configuration Generation**
   - System generates VPN configuration
   - Configuration is displayed with copy functionality
   - User's telco assignment and config count are updated

### For Returning Customers

1. **Recognition**
   - System identifies returning customer by order number
   - Displays welcome back message
   - Shows current restrictions and assigned telco

2. **Restricted Access**
   - If user has assigned telco, only that telco is available
   - Other telcos are grayed out with restriction message
   - Plans within assigned telco remain selectable

3. **Configuration Generation**
   - Same process as new customers
   - Additional configurations increment the user's count

## Security Features

- **Order Status Validation**: Only "To Ship" orders can proceed
- **UUID-Based Tracking**: Secure user identification without exposing personal data
- **Access Control**: SKU-based restrictions prevent unauthorized access
- **Telco Locking**: Prevents users from switching telcos after initial selection
- **Input Validation**: All user inputs are validated before processing

## Integration Points

- **Order Service**: Integrates with existing Shopee order management
- **VPN Plugin**: Uses existing VPN configuration generation service
- **Chat Commands**: Compatible with existing chat command system
- **Admin Panel**: Accessible through VPN config generator dashboard

## Monitoring & Analytics

- **User Statistics**: Track total users, restricted users, and configurations
- **SKU Breakdown**: Monitor usage by SKU patterns
- **Access Patterns**: Track telco assignments and restrictions
- **Error Logging**: Comprehensive logging for troubleshooting

## Troubleshooting

### Common Issues

1. **Order Not Found**
   - Verify order number is correct
   - Check if order exists in system
   - Ensure order status is "To Ship"

2. **Telco Access Denied**
   - Check user's SKU restrictions
   - Verify assigned telco for restricted users
   - Review restriction configuration

3. **Configuration Generation Failed**
   - Check VPN service availability
   - Verify telco and plan configuration
   - Review server availability

### Debug Information

- User data is logged during order verification
- Access control decisions are logged with reasons
- Configuration generation attempts are tracked
- Error details are provided in API responses

## Future Enhancements

- **Admin Interface**: Web interface for managing restrictions
- **Usage Analytics**: Detailed reporting and analytics dashboard
- **Bulk Operations**: Support for bulk user management
- **API Extensions**: Additional endpoints for advanced functionality
- **Mobile Optimization**: Enhanced mobile user experience
