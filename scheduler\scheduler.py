import importlib
import config
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from services.order_service import get_to_ship_orders, get_order_details
from services.chat_service import send_chat_message, get_recent_conversations, get_recent_latest_messages, process_and_reply_to_conversation
from utils.var_sku_extractor import extract_var_sku
from flask import jsonify
import os
from services.send_auto_redeem_message import auto_redeem_orders
from services.auto_reply_service import check_and_send_auto_reply
from services.chat_service import get_recent_conversations
from concurrent.futures import ThreadPoolExecutor
from services.netflix_service import remove_expired_orders
import time
import requests
from datetime import datetime
import json
from services.curlec_service import CurlecService
import logging
from datetime import datetime, timedelta

# Global scheduler instance for shutdown
_scheduler_instance = None

# Rate limiting for email notifications
_email_error_cache = {}
_EMAIL_ERROR_COOLDOWN = 900  # 15 minutes for email errors

def _should_send_email_error(error_message):
    """Rate limit email error notifications"""
    cache_key = f"email_error:{hash(error_message)}"
    now = datetime.now()
    
    if cache_key in _email_error_cache:
        last_sent = _email_error_cache[cache_key]
        if now - last_sent < timedelta(seconds=_EMAIL_ERROR_COOLDOWN):
            return False
    
    _email_error_cache[cache_key] = now
    return True


def check_expired_netflix_orders():
    """Check and remove expired Netflix orders"""
    try:
        removed_orders = remove_expired_orders()  # Modify remove_expired_orders to return list of removed orders
        if removed_orders and len(removed_orders) > 0:
            # Create table format for email
            table = "Order ID | Email | Order Date | Expiry Date\n"
            table += "-" * 50 + "\n"

            for order in removed_orders:
                table += f"{order['order_id']} | {order['email']} | {order['order_date']} | {order['expiry_date']}\n"

            email_body = f"Removed {len(removed_orders)} expired orders from data/netflix/netflix_sessions.json\n\n"
            email_body += table

            send_notification_email(
                "Netflix Orders Cleanup",
                email_body
            )
    except Exception as e:
        print(f"Error checking expired Netflix orders: {str(e)}")


def send_notification_email(subject, body):
    sender_email = config.NOTIFICATION_EMAIL['address']
    sender_password = config.NOTIFICATION_EMAIL['app_password']
    receiver_email = config.NOTIFICATION_EMAIL['address']

    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = receiver_email
    message["Subject"] = subject

    message.attach(MIMEText(body, "plain"))

    try:
        with smtplib.SMTP_SSL("smtp.gmail.com", 465) as server:
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, receiver_email, message.as_string())
        print("Notification email sent successfully")
    except Exception as e:
        error_msg = f"Failed to send notification email: {str(e)}"
        if _should_send_email_error(error_msg):
            print(error_msg)


def check_credentials():
    try:
        # Directly call the get_to_ship_orders function
        result = get_to_ship_orders()
        # Check if the result contains error or is empty
        if (isinstance(result, dict) and
                'data' in result and
                'card_list' in result['data'] and
                len(result['data']['card_list']) == 0 and
                'error' in result):
            # This likely means invalid credentials
            send_notification_email(
                "Shopee Credentials Invalid",
                "Your Shopee credentials (COOKIE and/or AUTHORIZATION_CODE) are no longer valid. Please update them in the config.json file."
            )
        else:
            print("🔐 Credentials are valid")

    except Exception as e:
        print(f"Error checking credentials: {str(e)}")
        send_notification_email(
            "Shopee Credentials Check Failed",
            f"An error occurred while checking Shopee credentials: {str(e)}. Please check your application and network connection."
        )


def check_stock_levels():
    """Check stock levels and send notifications if below threshold"""
    try:
        for sku in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY:
            if not sku.get('is_unlimited_stock') and sku.get('stock_level', 0) > 0:
                stock_entry = next(
                    (item for item in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK if item['sku'] == sku['sku']),
                    None
                )

                if stock_entry:
                    current_stock = len(stock_entry['stock'])
                    if current_stock <= sku['stock_level']:
                        send_notification_email(
                            f"Low Stock Alert - {sku['sku']}",
                            f"Stock level for SKU {sku['sku']} is low!\n"
                            f"Current stock: {current_stock}\n"
                            f"Alert threshold: {sku['stock_level']}"
                        )
    except Exception as e:
        print(f"Error checking stock levels: {str(e)}")


def check_auto_reply():
    """Check recent conversations and send auto replies if needed"""
    try:
        # Get AUTO_REPLY_ENABLED from config, default to False if not found
        auto_reply_enabled = getattr(config, 'AUTO_REPLY_ENABLED', False)
        if not auto_reply_enabled:
            # print("💬 Auto reply is disabled")
            return

        recent_conversations = get_recent_conversations()
        print(f"Retrieved conversations data type: {type(recent_conversations)}")

        # The function now consistently returns a list or empty list
        if isinstance(recent_conversations, list):
            print(f"Processing {len(recent_conversations)} conversations for auto reply")
            for conversation in recent_conversations:
                if isinstance(conversation, dict):  # Only process dictionary objects
                    try:
                        check_and_send_auto_reply(conversation)
                    except Exception as e:
                        print(f"Error processing conversation for auto reply: {str(e)}")
                else:
                    print(f"Skipping non-dict conversation: {type(conversation)}")
        else:
            print(f"Unexpected conversations data format: {type(recent_conversations)}")

    except Exception as e:
        print(f"Error in auto reply check: {str(e)}")
        import traceback
        print(f"Auto reply check traceback: {traceback.format_exc()}")


def check_payment_status():
    """Check payment status for non-expired payment links and send auto redeem message if payment is completed"""
    try:
        current_timestamp = int(time.time())
        curlec_service = CurlecService()

        # Load existing payment links
        try:
            with open('configs/data/manual_invoice.json', 'r') as f:
                payment_links = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            print("No payment links found or invalid JSON")
            return

        # Filter and check non-expired payment links
        for payment_id, payment_data in payment_links.items():
            try:
                # Skip if already paid or expired
                if payment_data.get('payment_status') == 'paid' or payment_data.get('expire_by', 0) < current_timestamp:
                    continue

                # Get real-time payment status
                response = requests.get(
                    f"{curlec_service.base_url}/payment_links/{payment_id}",
                    headers={"Content-Type": "application/json"},
                    auth=curlec_service.auth
                )

                if response.status_code == 200:
                    payment_info = response.json()

                    # Update payment data with latest info
                    payment_links[payment_id].update({
                        'status': payment_info['status'],
                        'amount_paid': payment_info['amount_paid'],
                        'payments': payment_info['payments'],
                        'updated_at': payment_info['updated_at']
                    })

                    # If payment is completed
                    if payment_info['status'] == 'paid' and payment_data.get('payment_status') != 'paid':

                        # Update payment status
                        payment_links[payment_id]['payment_status'] = 'paid'

                        # Get var_sku message template
                        var_sku = payment_data.get('var_sku')
                        matching_sku = next(
                            (sku for sku in config.AUTO_REDEEM_VAR_SKUS
                             if sku['sku'] == var_sku),
                            None
                        )

                        if matching_sku and matching_sku.get('message'):
                            # Format and send auto redeem message
                            formatted_message = matching_sku['message'].format(
                                order_sn=payment_data.get('reference_id'),
                                buyer_username=payment_data['customer']['name'],
                                item_name=payment_data.get('description'),
                                item_price=f"{payment_data['amount'] / 100:.2f}",
                                payment_method="Curlec"
                            )

                            # Send message to customer
                            chat_payload = {
                                'username': payment_data['customer']['name'],
                                'text': formatted_message
                            }
                            send_chat_message(chat_payload)

                            print(f"Auto redeem message sent for payment {payment_id}")

            except Exception as e:
                print(f"Error processing payment {payment_id}: {str(e)}")
                continue

        # Save updated payment statuses
        with open('configs/data/manual_invoice.json', 'w') as f:
            json.dump(payment_links, f, indent=2)

    except Exception as e:
        print(f"Error in check_payment_status: {str(e)}")


def check_expired_canva_orders():
    """Check and send notification for expired Canva orders"""
    try:
        # Load canva orders
        with open('configs/data/canva_orders.json', 'r') as f:
            canva_orders = json.load(f)

        current_time = datetime.now()
        expired_orders = []

        # Check for expired orders
        for order_sn, order_data in canva_orders.items():
            try:
                # Try to parse ISO format first (with microseconds)
                if 'T' in order_data['expiry_date']:
                    expiry_date = datetime.fromisoformat(order_data['expiry_date'].replace('Z', '+00:00'))
                else:
                    # Fall back to old format
                    expiry_date = datetime.strptime(order_data['expiry_date'], '%Y-%m-%d %H:%M:%S')
            except ValueError as e:
                print(f"Error parsing date for order {order_sn}: {order_data['expiry_date']} - {str(e)}")
                continue

            if current_time >= expiry_date:
                expired_orders.append({
                    'order_sn': order_sn,
                    'user_email': order_data.get('user_email'),
                    'expiry_date': expiry_date.strftime('%Y-%m-%d')
                })

        # If there are expired orders, send email notification
        if expired_orders:
            # Create table format for email
            table = "Order SN | User Email | Expiry Date\n"
            table += "-" * 60 + "\n"

            for order in expired_orders:
                table += f"{order['order_sn']} | {order['user_email']} | {order['expiry_date']}\n"

            email_body = f"Found {len(expired_orders)} expired Canva orders\n\n"
            email_body += table

            send_notification_email(
                "Canva Orders Expiry Notice",
                email_body
            )
            
            logging.info(f"Sent notification for {len(expired_orders)} expired Canva orders")
    except Exception as e:
        logging.error(f"Error checking expired Canva orders: {str(e)}")


def check_and_reply_to_unreplied_messages():
    """Check for unreplied messages and send AI responses"""
    try:
        # Check if ai_chat plugin is enabled and AI reply is enabled
        from core.plugin_manager import PluginManager
        import json
        import os

        # Load plugin configuration
        plugin_config_file = 'configs/core/plugin_config.json'
        ai_reply_enabled = False

        try:
            if os.path.exists(plugin_config_file):
                with open(plugin_config_file, 'r', encoding='utf-8') as f:
                    plugin_config = json.load(f)
                    ai_chat_config = plugin_config.get('ai_chat', {})
                    plugin_enabled = ai_chat_config.get('enabled', False)
                    ai_config = ai_chat_config.get('ai_config', {})
                    ai_reply_enabled = plugin_enabled and ai_config.get('ai_reply_enabled', False)
        except Exception as e:
            print(f"🤖 Error loading AI chat plugin config: {e}")

        if not ai_reply_enabled:
            # print("🤖 AI reply is disabled (plugin disabled or ai_reply_enabled is false)")
            return

        recent_conversations = get_recent_conversations()

        # The function now consistently returns a list or empty list
        if isinstance(recent_conversations, list):
            print(f"🤖 Processing {len(recent_conversations)} conversations for AI reply")
            for conversation in recent_conversations:
                if isinstance(conversation, dict):  # Only process dictionary objects
                    # Skip if the latest message is from the shop
                    if conversation.get('unread_count') == 0:
                        continue

                    # Process and reply to the conversation
                    conversation_id = conversation.get('id')
                    if conversation_id:
                        try:
                            print(f"🤖 Processing AI reply for conversation {conversation_id}")
                            process_and_reply_to_conversation(conversation_id, conversation.get('to_name'))
                        except Exception as e:
                            print(f"❌ Failed to process and reply to conversation {conversation_id}: {str(e)}")
                else:
                    print(f"Skipping non-dict conversation for AI reply: {type(conversation)}")
        else:
            print(f"Unexpected conversations data format for AI reply: {type(recent_conversations)}")

    except Exception as e:
        print(f"Error in AI reply check: {str(e)}")
        import traceback
        print(f"AI reply check traceback: {traceback.format_exc()}")


def start_scheduler():
    global _scheduler_instance
    # Create scheduler with built-in ThreadPoolExecutor
    scheduler = BackgroundScheduler(
        job_defaults={
            'coalesce': True,
            'max_instances': 1,
            'misfire_grace_time': 60
        },
        executors={
            'default': {
                'type': 'threadpool',
                'max_workers': 5  # Adjust this number based on your needs
            }
        }
    )

    # Add jobs with increased intervals to reduce resource usage
    scheduler.add_job(
        func=check_expired_netflix_orders,
        trigger='cron',
        hour=0,  # Run at midnight
        minute=0,
        id='check_expired_netflix_orders_job',
        name='Check and remove expired Netflix orders',
        replace_existing=True
    )

    scheduler.add_job(
        func=check_credentials,
        trigger=IntervalTrigger(minutes=5),  # Increased interval
        id='check_credentials_job',
        name='Check if cookie and authorization are valid',
        replace_existing=True)

    scheduler.add_job(
        func=auto_redeem_orders,
        trigger=IntervalTrigger(minutes=1),
        id='auto_redeem_orders_job',
        name='Auto Redeem To Ship Orders',
        replace_existing=True)

    scheduler.add_job(
        func=check_stock_levels,
        trigger=IntervalTrigger(minutes=30),
        id='check_stock_levels_job',
        name='Check stock levels and send notifications',
        replace_existing=True)

    scheduler.add_job(
        func=check_auto_reply,
        trigger=IntervalTrigger(minutes=1), 
        id='check_auto_reply_job',
        name='Check conversations and send auto replies',
        replace_existing=True)

    # Add payment status check job
    scheduler.add_job(
        func=check_payment_status,
        trigger=IntervalTrigger(minutes=1),
        id='check_payment_status_job',
        name='Check payment status and send auto redeem message',
        replace_existing=True
    )

    # Add Canva orders cleanup job
    scheduler.add_job(
        func=check_expired_canva_orders,
        trigger='cron',
        hour=0,  # Run at midnight
        minute=0,
        id='check_expired_canva_orders_job',
        name='Check and notify expired Canva orders',
        replace_existing=True
    )

    # Add unreplied messages check job
    scheduler.add_job(
        func=check_and_reply_to_unreplied_messages,
        trigger=IntervalTrigger(seconds=60),
        id='check_unreplied_messages_job',
        name='Check and reply to unreplied messages',
        replace_existing=True
    )

    try:
        scheduler.start()
        _scheduler_instance = scheduler
        print("📅 Scheduler started")
        # Run initial checks with delays to avoid overwhelming the system
        with ThreadPoolExecutor(max_workers=5) as executor:
            executor.submit(check_credentials)
            executor.submit(auto_redeem_orders)
            executor.submit(check_auto_reply)
            executor.submit(check_expired_netflix_orders)
            executor.submit(check_expired_canva_orders)
            executor.submit(check_and_reply_to_unreplied_messages)
    except Exception as e:
        print(f"Error starting scheduler: {str(e)}")
        try:
            scheduler.shutdown()
        except:
            pass


def shutdown_scheduler():
    """Shutdown the scheduler gracefully"""
    global _scheduler_instance
    if _scheduler_instance:
        try:
            print("📅 Shutting down scheduler...")
            _scheduler_instance.shutdown(wait=True)
            _scheduler_instance = None
            print("📅 Scheduler shutdown successfully")
        except Exception as e:
            print(f"❌ Error shutting down scheduler: {str(e)}")
    else:
        print("📅 No scheduler instance to shutdown")
