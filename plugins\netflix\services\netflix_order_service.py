"""
Netflix Order Service
Handles Netflix order processing, account assignment, and order management
"""

import logging
import json
import os
import random
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class NetflixOrderService:
    """Service for managing Netflix orders and account assignments"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.order_config = config.get('order_config', {})
        
        # Configuration
        self.account_limit = self.order_config.get('account_limit', 5)
        self.redeem_period_days = self.order_config.get('redeem_period_days', 30)
        self.auto_ship = self.order_config.get('auto_ship', True)
        
        # Data storage
        self.data_dir = 'data/netflix'
        self.orders_file = os.path.join(self.data_dir, 'netflix_orders.json')
        self.accounts_file = os.path.join(self.data_dir, 'netflix_accounts.json')
        
        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Load existing data
        self.orders_data = self._load_orders()
        self.accounts_data = self._load_accounts()
        
    def _load_orders(self) -> Dict[str, Any]:
        """Load orders data from file"""
        try:
            if os.path.exists(self.orders_file):
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading orders data: {e}")
        
        return {'orders': {}, 'accounts': {}}
    
    def _save_orders(self):
        """Save orders data to file"""
        try:
            with open(self.orders_file, 'w', encoding='utf-8') as f:
                json.dump(self.orders_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving orders data: {e}")
    
    def _load_accounts(self) -> List[str]:
        """Load available Netflix accounts from file"""
        try:
            if os.path.exists(self.accounts_file):
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('accounts', [])
        except Exception as e:
            logger.error(f"Error loading accounts data: {e}")
        
        return []
    
    def get_or_assign_account(self, order_sn: str) -> Optional[str]:
        """Get or assign a Netflix account to an order"""
        try:
            # Initialize data structures if needed
            if 'orders' not in self.orders_data:
                self.orders_data['orders'] = {}
            if 'accounts' not in self.orders_data:
                self.orders_data['accounts'] = {}
            
            # Check if order already has an assigned account
            if order_sn in self.orders_data['orders']:
                assigned_account = self.orders_data['orders'][order_sn]['account']
                if assigned_account in self.accounts_data:
                    return assigned_account
            
            # Find available accounts
            available_accounts = []
            for account in self.accounts_data:
                current_orders = len(self.orders_data['accounts'].get(account, []))
                if current_orders < self.account_limit:
                    available_accounts.append(account)
            
            if not available_accounts:
                logger.warning("No available Netflix accounts for assignment")
                return None
            
            # Assign random available account
            new_account = random.choice(available_accounts)
            
            # Update data structures
            if new_account not in self.orders_data['accounts']:
                self.orders_data['accounts'][new_account] = []
            
            self.orders_data['accounts'][new_account].append(order_sn)
            self.orders_data['orders'][order_sn] = {
                'account': new_account,
                'assigned_at': datetime.now().isoformat(),
                'last_redeem': None,
                'status': 'assigned'
            }
            
            self._save_orders()
            logger.info(f"Assigned Netflix account {new_account} to order {order_sn}")
            return new_account
            
        except Exception as e:
            logger.error(f"Error assigning Netflix account to order {order_sn}: {e}")
            return None
    
    def process_order(self, order_sn: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a Netflix order"""
        try:
            # Get or assign account
            netflix_account = self.get_or_assign_account(order_sn)
            if not netflix_account:
                return {
                    'success': False,
                    'error': 'No available Netflix accounts'
                }
            
            # Calculate redeem period
            redeem_until = datetime.now() + timedelta(days=self.redeem_period_days)
            
            # Update order status
            if order_sn in self.orders_data['orders']:
                self.orders_data['orders'][order_sn].update({
                    'processed_at': datetime.now().isoformat(),
                    'redeem_until': redeem_until.isoformat(),
                    'status': 'processed'
                })
                self._save_orders()
            
            return {
                'success': True,
                'netflix_account': netflix_account,
                'redeem_until': redeem_until.isoformat(),
                'order_sn': order_sn
            }
            
        except Exception as e:
            logger.error(f"Error processing Netflix order {order_sn}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_order_status(self, order_sn: str) -> Dict[str, Any]:
        """Get status of a Netflix order"""
        try:
            if order_sn in self.orders_data.get('orders', {}):
                return {
                    'success': True,
                    'order': self.orders_data['orders'][order_sn]
                }
            else:
                return {
                    'success': False,
                    'error': 'Order not found'
                }
        except Exception as e:
            logger.error(f"Error getting order status for {order_sn}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_account_orders(self, account: str) -> List[str]:
        """Get all orders assigned to a specific account"""
        return self.orders_data.get('accounts', {}).get(account, [])
    
    def release_account(self, order_sn: str) -> bool:
        """Release Netflix account from an order"""
        try:
            if order_sn in self.orders_data.get('orders', {}):
                account = self.orders_data['orders'][order_sn]['account']
                
                # Remove from account's order list
                if account in self.orders_data.get('accounts', {}):
                    if order_sn in self.orders_data['accounts'][account]:
                        self.orders_data['accounts'][account].remove(order_sn)
                
                # Remove order record
                del self.orders_data['orders'][order_sn]
                self._save_orders()
                
                logger.info(f"Released Netflix account {account} from order {order_sn}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error releasing account for order {order_sn}: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get Netflix order statistics"""
        try:
            total_accounts = len(self.accounts_data)
            total_orders = len(self.orders_data.get('orders', {}))
            
            # Account utilization
            account_usage = {}
            for account in self.accounts_data:
                orders_count = len(self.orders_data.get('accounts', {}).get(account, []))
                account_usage[account] = {
                    'orders_count': orders_count,
                    'utilization': f"{orders_count}/{self.account_limit}"
                }
            
            return {
                'total_accounts': total_accounts,
                'total_orders': total_orders,
                'account_usage': account_usage,
                'account_limit': self.account_limit
            }
            
        except Exception as e:
            logger.error(f"Error getting Netflix statistics: {e}")
            return {}
    
    def update_config(self, config: Dict[str, Any]):
        """Update service configuration"""
        self.config = config
        self.order_config = config.get('order_config', {})
        self.account_limit = self.order_config.get('account_limit', 5)
        self.redeem_period_days = self.order_config.get('redeem_period_days', 30)
        self.auto_ship = self.order_config.get('auto_ship', True)
        
        logger.info("Netflix order service configuration updated")
    
    def cleanup(self):
        """Cleanup service resources"""
        try:
            # Save any pending data
            self._save_orders()
            logger.info("Netflix order service cleanup completed")
        except Exception as e:
            logger.error(f"Error during Netflix order service cleanup: {e}")
