# WebSocket 420 Format Fix - Real-time Message Reception

## 🚨 Critical Issue Identified

After the initial WebSocket connection fix, the connection was stable but **NOT receiving real-time messages**. The user reported that despite successful connection, no chat messages were being received.

## 🔍 Root Cause Analysis

### User's Correct Observation
The user provided the exact browser behavior:

1. **Login API Call**:
   ```
   URL: https://seller.shopee.com.my/webchat/api/coreapi/v1.2/mini/login
   Method: POST
   Data: csrf_token, source=pcmall, _api_source=pcmall
   ```

2. **Login Response**:
   ```json
   {
     "token": "eyJhbG.......",
     "p_token": "M0pM.....",
     "user": {
       "id": *********,
       "uid": "0-*********",
       "name": "mtyb_official",
       ...
     }
   }
   ```

3. **WebSocket Connection**:
   ```
   URL: wss://seller-push-ws.shopee.com.my/socket.io/?region=my&_v=new&EIO=3&transport=websocket
   Handshake: 0{"sid":"q8zV2HvN9VzUYGMwF-uv","upgrades":["polling"],"pingInterval":10000,"pingTimeout":60000}
   ```

4. **Critical Discovery - Login Message Format**:
   ```javascript
   // Browser sends THIS format:
   420["login",{"token":"M0pMTEVNQnV1bDhpbDlERwKmrq.....","account_id":"0-*********","app_id":"pmminichat"}]
   
   // NOT this format:
   42["login",{"token":"...","account_id":"...","app_id":"..."}]
   ```

## 🛠️ The Fix Applied

### 1. Correct Socket.IO Message Format
- **Problem**: Using `42["login",...]` (regular Socket.IO event)
- **Solution**: Use `420["login",...]` (Socket.IO login event with callback)

### 2. Raw WebSocket Implementation
Instead of relying on Socket.IO client library, implemented raw WebSocket with manual Socket.IO protocol handling:

```python
# Connect using raw websockets
self.ws_connection = await websockets.connect(ws_url, extra_headers=headers)

# Handle handshake
handshake = await self.ws_connection.recv()
# Parse: 0{"sid":"...","pingInterval":10000,"pingTimeout":60000}

# Send login with 420 format
login_payload = {
    "token": p_token,  # Use p_token from login API
    "account_id": str(account_id),  # Use uid from user info
    "app_id": "pmminichat"
}
login_message = f'420["login",{json.dumps(login_payload)}]'
await self.ws_connection.send(login_message)
```

### 3. Proper Token Usage
- **Extract p_token**: Use `p_token` field from login response (not `token`)
- **Account ID**: Use `uid` from user info, fallback to `0-{shop_id}`
- **CSRF Token**: Extract from `CTOKEN` cookie

### 4. Message Processing
Handle Socket.IO protocol messages correctly:
- `0{...}` - Handshake
- `2` - Ping (respond with `3` pong)
- `3` - Pong
- `40` - Namespace connection
- `42[...]` - Event messages (chat messages!)
- `430[...]` - Response messages

## 📁 Files Modified

### `services/websocket.py`
- **Replaced**: Socket.IO client with raw websockets
- **Added**: Manual Socket.IO protocol handling
- **Fixed**: Login message format to use `420["login",...]`
- **Updated**: Token extraction to use `p_token`
- **Enhanced**: Message parsing for different Socket.IO message types

### Test Scripts Created
- `test_fixed_websocket.py` - Comprehensive test
- `test_correct_websocket.py` - Raw WebSocket format test
- `websocket_fix.py` - Diagnostic tool

## 🎯 Expected Results

After this fix, you should see:

1. **✅ Successful Connection**: WebSocket connects and handshake completes
2. **✅ Proper Authentication**: `420["login",...]` message sent
3. **✅ Real-time Messages**: Chat messages received as `42["message",...]`
4. **✅ Webhook Delivery**: Messages forwarded to configured webhooks
5. **✅ Message Caching**: Messages cached if enabled

## 🧪 Testing

Run the test to verify the fix:
```bash
cd ShopeeAPI
python test_fixed_websocket.py
```

Expected output:
```
✅ WebSocket connected successfully!
   Using raw websockets with 420["login",...] format
   Connection established and login message sent
   
📨 Message #1 received:
Raw: 42["message",{"message_type":"message","message_content":"{...}"}]
Type: Event message
Event: message
🎉 CHAT MESSAGE RECEIVED!
```

## 🔧 Key Technical Details

### Socket.IO Protocol Differences
- `42["event",data]` - Regular event
- `420["event",data]` - Event with callback/acknowledgment
- `421["event",data]` - Event response with callback ID

### Why 420 Format is Required
The `420` format tells the Socket.IO server that this is a login event that requires acknowledgment. Regular `42` events are treated as general messages and may not trigger the authentication flow.

### Message Flow
```
1. Connect to WebSocket
2. Receive handshake: 0{"sid":"..."}
3. Send login: 420["login",{"token":"...","account_id":"...","app_id":"pmminichat"}]
4. Receive chat messages: 42["message",{...}]
5. Process and forward to webhooks
```

## 🚀 Deployment

1. **Update Code**: The fix is already applied to `services/websocket.py`
2. **Test Connection**: Run `python test_fixed_websocket.py`
3. **Start Service**: Run `python main.py`
4. **Monitor Logs**: Watch for real-time message reception

## 📊 Monitoring

Look for these log messages indicating success:
```
✓ P-token: M0pMTEVNQnV1bDhpbDlERwKmrq...
✓ Account ID: 0-*********
✓ WebSocket connected!
Sending: 420["login",{"token":"...","account_id":"0-*********","app_id":"pmminichat"}]
✓ Login message sent!
📨 Message #1 received: 42["message",...]
🎉 CHAT MESSAGE RECEIVED!
```

## 🔗 Related Issues

- **Previous Fix**: Socket.IO client connection (stable but no messages)
- **This Fix**: Correct 420 format for authentication (enables message reception)
- **Next**: Monitor webhook delivery and message caching

---

**Status**: ✅ Fixed and Ready for Testing  
**Date**: 2025-01-15  
**Critical**: This fix enables real-time message reception  
**Test**: `python test_fixed_websocket.py`
