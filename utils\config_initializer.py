"""
Configuration Initializer for SteamCodeTool
This module ensures all necessary configuration directories and files exist
with proper default values, especially for Docker deployments.
"""

import os
import json
import logging

logger = logging.getLogger(__name__)

def ensure_directory_exists(directory_path):
    """Ensure a directory exists, create it if it doesn't"""
    try:
        os.makedirs(directory_path, exist_ok=True)
        logger.info(f"Directory ensured: {directory_path}")
        return True
    except PermissionError:
        logger.warning(f"Permission denied creating directory: {directory_path}")
        return False
    except Exception as e:
        logger.error(f"Error creating directory {directory_path}: {e}")
        return False

def create_default_config_file(file_path, default_config, description=""):
    """Create a default configuration file if it doesn't exist"""
    try:
        if not os.path.exists(file_path):
            # Ensure parent directory exists
            parent_dir = os.path.dirname(file_path)
            if parent_dir:
                ensure_directory_exists(parent_dir)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            logger.info(f"Created default config file: {file_path} - {description}")
            return True
        else:
            logger.debug(f"Config file already exists: {file_path}")
            return True
    except PermissionError:
        logger.warning(f"Permission denied creating config file: {file_path}")
        return False
    except Exception as e:
        logger.error(f"Error creating config file {file_path}: {e}")
        return False

def initialize_all_configs():
    """Initialize all necessary configuration directories and files"""
    logger.info("Starting configuration initialization...")
    
    # Define all necessary directories
    directories = [
        'configs',
        'configs/core',
        'configs/cache',
        'configs/data',
        'configs/services',
        'logs',
        'data',
        'data/netflix'
    ]
    
    # Create directories
    for directory in directories:
        ensure_directory_exists(directory)
    
    # Define default configuration files
    config_files = {
        'configs/core/config.json': {
            "ADMIN_CREDENTIALS": {
                "username": "admin",
                "password": "whitepaperh0817"
            },
            "API_KEY": "MTYB_OFFICIAL",
            "AUTHORIZATION_CODE": "",
            "COOKIE": "",
            "SHOP_ID": 101806022,
            "AUTO_REPLY_ENABLED": False,
            "AUTO_REPLY_MESSAGE": "Currently Im Offline, I will reply you as soon as possible!",
            "AUTO_REPLY_DELAY_MINUTES": 1,
            "AUTO_REPLY_COOLDOWN_MINUTES": 60,
            "ENABLE_AUTO_REDEEM": False,
            "AUTO_REDEEM_VAR_SKUS": [],
            "AUTO_REDEEM_VAR_SKUS_TEXT_ONLY": [],
            "AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK": [],
            "EMAIL_CONFIGS": {
                "mtyb_developer": {
                    "email": "<EMAIL>",
                    "app_password": "nnqydxjgqxrdzsxg"
                }
            },
            "STEAM_CREDENTIALS": {
                "mtyb_developer": {
                    "password": "@Whitepaperh0817"
                }
            },
            "NOTIFICATION_EMAIL": {
                "address": "",
                "app_password": ""
            },
            "REQUEST_TIMEOUT": 30,
            "SESSION_COOLDOWN_TIME": 600,
            "SEND_CHAT_ON_AUTH_SUCCESS": True,
            "AUTO_SHIP_ORDER": False,
            "SEND_MESSAGE_ON_SHIP": False,
            "SHIP_SUCCESS_MESSAGE_TEMPLATE": "",
            "AUTH_CODE_DELAY": 3,
            "CURLEC_API_KEY": "",
            "CURLEC_SECRET_KEY": "",
            "AI_REPLY_ENABLED": False,
            "AI_REPLY_COOLDOWN_MINUTES": 60,
            "AI_TEMPERATURE": 1.0,
            "AI_SYSTEM_PROMPT": "You are a helpful customer service assistant.",
            "DEEPSEEK_API_KEY": "***********************************"
        },
        
        'configs/core/plugin_config.json': {
            "enabled_plugins": ["chat_commands", "vpn", "canva", "netflix"],
            "plugin_settings": {
                "chat_commands": {
                    "enabled": True,
                    "auto_reply": True
                },
                "vpn": {
                    "enabled": True,
                    "auto_config": True
                },
                "canva": {
                    "enabled": True,
                    "auto_redeem": False
                },
                "netflix": {
                    "enabled": True,
                    "session_management": True
                }
            }
        },
        
        'configs/services/canva_config.json': {
            "SKU_VALIDITY": {
                "canva_30": {
                    "type": "lifetime"
                },
                "canva_1m": {
                    "type": "1month"
                }
            },
            "types": {
                "lifetime": {
                    "invitation_links": []
                },
                "1month": {
                    "invitation_links": []
                }
            }
        },
        
        'configs/services/config_templates.json': [],
        
        'configs/services/vpn_servers.json': {
            "servers": [],
            "default_config": {
                "protocol": "vless",
                "port": 443,
                "security": "tls"
            }
        },
        
        'configs/data/canva_orders.json': {},
        
        'configs/data/manual_orders.json': [],
        
        'configs/data/sent_orders.json': [],
        
        'configs/data/redeemed_stock.json': {},
        
        'configs/data/manual_invoice.json': {},
        
        'configs/data/dashboard_data.json': {
            "auth_code_records": [],
            "daily_stats": {},
            "last_update": None
        },
        
        'configs/cache/ai_reply_cooldown.json': {},
        
        'configs/cache/auto_reply_cooldown.json': {},
        
        'data/netflix/netflix_sessions.json': {}
    }
    
    # Create configuration files
    success_count = 0
    total_count = len(config_files)
    
    for file_path, default_config in config_files.items():
        description = file_path.split('/')[-1].replace('.json', '').replace('_', ' ').title()
        if create_default_config_file(file_path, default_config, description):
            success_count += 1
    
    logger.info(f"Configuration initialization completed: {success_count}/{total_count} files processed")
    
    if success_count < total_count:
        logger.warning(f"Some configuration files could not be created due to permission issues")
        logger.warning("The application will use in-memory defaults for missing files")
    
    return success_count == total_count

def check_config_integrity():
    """Check if all required configuration files exist and are valid"""
    required_files = [
        'configs/core/config.json',
        'configs/services/canva_config.json'
    ]
    
    missing_files = []
    invalid_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json.load(f)
            except json.JSONDecodeError:
                invalid_files.append(file_path)
            except Exception as e:
                logger.error(f"Error checking {file_path}: {e}")
                invalid_files.append(file_path)
    
    if missing_files:
        logger.warning(f"Missing configuration files: {missing_files}")
    
    if invalid_files:
        logger.warning(f"Invalid configuration files: {invalid_files}")
    
    return len(missing_files) == 0 and len(invalid_files) == 0

if __name__ == "__main__":
    # Setup logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    print("Initializing SteamCodeTool configuration...")
    success = initialize_all_configs()
    
    if success:
        print("✅ All configuration files initialized successfully!")
    else:
        print("⚠️ Some configuration files could not be created due to permission issues")
        print("The application will use in-memory defaults for missing files")
    
    print("\nChecking configuration integrity...")
    if check_config_integrity():
        print("✅ All required configuration files are present and valid!")
    else:
        print("⚠️ Some configuration files are missing or invalid")
