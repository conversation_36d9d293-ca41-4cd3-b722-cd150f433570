#!/usr/bin/env python3
"""
VPN SKU Tags Configuration Manager
Tool for managing VPN SKU to server tags mapping configuration
"""

import sys
import os
import json
import argparse
from typing import Dict, List, Any

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory

class SKUTagsConfigManager:
    """Manager for VPN SKU tags configuration"""
    
    def __init__(self):
        self.config_path = self._get_config_path()
        
    def _get_config_path(self) -> str:
        """Get the configuration file path"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        return os.path.join(project_root, 'configs', 'services', 'vpn_sku_tags.json')
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Configuration file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in configuration file: {e}")
            return {}
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """Save configuration to file"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"✅ Configuration saved to: {self.config_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to save configuration: {e}")
            return False
    
    def show_config(self):
        """Display current configuration"""
        config = self.load_config()
        if not config:
            return
            
        print("=" * 80)
        print("VPN SKU TAGS CONFIGURATION")
        print("=" * 80)
        print(f"File: {self.config_path}")
        print(f"Version: {config.get('version', 'unknown')}")
        print(f"Last Updated: {config.get('last_updated', 'unknown')}")
        print()
        
        # Show SKU mappings by category
        sku_mapping = config.get('sku_server_tags_mapping', {})
        for category, skus in sku_mapping.items():
            print(f"{category.upper().replace('_', ' ')}:")
            print("-" * 50)
            for sku, sku_config in skus.items():
                if isinstance(sku_config, dict):
                    tags = sku_config.get('tags', [])
                    validity = sku_config.get('validity_days')
                    validity_info = f" ({validity} days)" if validity else ""
                    print(f"  {sku:25} -> {tags}{validity_info}")
                else:
                    # Old format compatibility
                    print(f"  {sku:25} -> {sku_config}")
            print()
        
        # Show fallback mappings
        fallback = config.get('fallback_mapping', {})
        if fallback:
            print("FALLBACK MAPPINGS:")
            print("-" * 50)
            for pattern, tags in fallback.items():
                print(f"  {pattern:25} -> {tags}")
            print()
    
    def add_sku_mapping(self, category: str, sku: str, tags: List[str], validity_days: int = None):
        """Add a new SKU mapping with optional validity days"""
        config = self.load_config()
        if not config:
            config = {"sku_server_tags_mapping": {}}

        if 'sku_server_tags_mapping' not in config:
            config['sku_server_tags_mapping'] = {}

        if category not in config['sku_server_tags_mapping']:
            config['sku_server_tags_mapping'][category] = {}

        # Create SKU configuration with tags and optional validity
        sku_config = {"tags": tags}
        if validity_days is not None:
            sku_config["validity_days"] = validity_days

        config['sku_server_tags_mapping'][category][sku] = sku_config

        if self.save_config(config):
            validity_info = f" (validity: {validity_days} days)" if validity_days else ""
            print(f"✅ Added SKU mapping: {sku} -> {tags}{validity_info} (category: {category})")
    
    def remove_sku_mapping(self, sku: str):
        """Remove a SKU mapping"""
        config = self.load_config()
        if not config:
            return
        
        found = False
        sku_mapping = config.get('sku_server_tags_mapping', {})
        
        for category, skus in sku_mapping.items():
            if sku in skus:
                del skus[sku]
                found = True
                print(f"✅ Removed SKU mapping: {sku} from category {category}")
                break
        
        if found:
            self.save_config(config)
        else:
            print(f"❌ SKU mapping not found: {sku}")
    
    def test_sku_resolution(self, test_skus: List[str] = None):
        """Test SKU to tags resolution"""
        if test_skus is None:
            test_skus = [
                'my_30', 'my_highspeed_30', 'sg_15', 'sg_premium_60',
                'my_standard_30', 'sg_business_90', 'unknown_sku'
            ]
        
        print("=" * 80)
        print("SKU TAGS RESOLUTION TEST")
        print("=" * 80)
        
        for sku in test_skus:
            tags = VPNStrategyFactory.get_server_tags_for_sku(sku)
            print(f"SKU: {sku:25} -> Tags: {tags}")
        print()
    
    def validate_config(self):
        """Validate configuration file"""
        config = self.load_config()
        if not config:
            return False
        
        print("=" * 80)
        print("CONFIGURATION VALIDATION")
        print("=" * 80)
        
        errors = []
        warnings = []
        
        # Check required sections
        required_sections = ['sku_server_tags_mapping', 'fallback_mapping']
        for section in required_sections:
            if section not in config:
                errors.append(f"Missing required section: {section}")
        
        # Validate SKU mappings
        sku_mapping = config.get('sku_server_tags_mapping', {})
        total_skus = 0
        for category, skus in sku_mapping.items():
            if not isinstance(skus, dict):
                errors.append(f"Category '{category}' should contain a dictionary of SKUs")
                continue
                
            for sku, tags in skus.items():
                total_skus += 1
                if not isinstance(tags, list):
                    errors.append(f"Tags for SKU '{sku}' should be a list")
                elif not tags:
                    warnings.append(f"SKU '{sku}' has empty tags list")
        
        # Report results
        if errors:
            print("❌ ERRORS FOUND:")
            for error in errors:
                print(f"  - {error}")
        
        if warnings:
            print("⚠️  WARNINGS:")
            for warning in warnings:
                print(f"  - {warning}")
        
        if not errors and not warnings:
            print("✅ Configuration is valid!")
        
        print(f"\nSummary:")
        print(f"  Total categories: {len(sku_mapping)}")
        print(f"  Total SKU mappings: {total_skus}")
        print(f"  Errors: {len(errors)}")
        print(f"  Warnings: {len(warnings)}")
        
        return len(errors) == 0

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='VPN SKU Tags Configuration Manager')
    parser.add_argument('action', choices=['show', 'add', 'remove', 'test', 'validate'],
                       help='Action to perform')
    parser.add_argument('--category', help='Category for add action')
    parser.add_argument('--sku', help='SKU for add/remove actions')
    parser.add_argument('--tags', help='Comma-separated tags for add action')
    parser.add_argument('--validity', type=int, help='Validity days for add action')
    parser.add_argument('--test-skus', help='Comma-separated SKUs for test action')

    args = parser.parse_args()
    manager = SKUTagsConfigManager()

    if args.action == 'show':
        manager.show_config()

    elif args.action == 'add':
        if not all([args.category, args.sku, args.tags]):
            print("❌ For add action, --category, --sku, and --tags are required")
            return 1
        tags = [tag.strip() for tag in args.tags.split(',')]
        manager.add_sku_mapping(args.category, args.sku, tags, args.validity)
        
    elif args.action == 'remove':
        if not args.sku:
            print("❌ For remove action, --sku is required")
            return 1
        manager.remove_sku_mapping(args.sku)
        
    elif args.action == 'test':
        test_skus = None
        if args.test_skus:
            test_skus = [sku.strip() for sku in args.test_skus.split(',')]
        manager.test_sku_resolution(test_skus)
        
    elif args.action == 'validate':
        if not manager.validate_config():
            return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
