"""
AI Chat Service
Handles AI chat configuration and management
"""

import logging
import json
import os
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# Default system prompt
DEFAULT_SYSTEM_PROMPT = """You are a helpful customer service assistant for an online store. 
Your role is to assist customers with their orders, answer questions about products, and provide support.

Guidelines:
1. Be polite, professional, and helpful
2. Keep responses concise but informative
3. If you don't know something, admit it and offer to help find the answer
4. For order-related issues, ask for order numbers when needed
5. Always maintain a friendly and supportive tone

Remember: You are representing the company, so maintain professionalism at all times."""

class AIChatService:
    """Service for managing AI chat configuration and operations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.config_file = 'configs/core/config.json'
        
    def get_ai_config(self) -> Dict[str, Any]:
        """Get current AI chat configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
                
            ai_settings = {
                'AI_REPLY_ENABLED': config.get('AI_REPLY_ENABLED', False),
                'AI_REPLY_COOLDOWN_MINUTES': config.get('AI_REPLY_COOLDOWN_MINUTES', 60),
                'AI_SYSTEM_PROMPT': config.get('AI_SYSTEM_PROMPT', DEFAULT_SYSTEM_PROMPT),
                'AI_TEMPERATURE': config.get('AI_TEMPERATURE', 1.0),
                'DEEPSEEK_API_KEY': config.get('DEEPSEEK_API_KEY', '')
            }
            
            return ai_settings
            
        except Exception as e:
            logger.error(f"Error loading AI config: {e}")
            return {
                'AI_REPLY_ENABLED': False,
                'AI_REPLY_COOLDOWN_MINUTES': 60,
                'AI_SYSTEM_PROMPT': DEFAULT_SYSTEM_PROMPT,
                'AI_TEMPERATURE': 1.0,
                'DEEPSEEK_API_KEY': ''
            }
    
    def update_ai_config(self, new_config: Dict[str, Any]) -> bool:
        """Update AI chat configuration"""
        try:
            # Load existing config
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # Update AI settings
            config['AI_REPLY_ENABLED'] = new_config.get('AI_REPLY_ENABLED', False)
            config['AI_REPLY_COOLDOWN_MINUTES'] = new_config.get('AI_REPLY_COOLDOWN_MINUTES', 60)
            config['AI_SYSTEM_PROMPT'] = new_config.get('AI_SYSTEM_PROMPT', DEFAULT_SYSTEM_PROMPT)
            config['AI_TEMPERATURE'] = new_config.get('AI_TEMPERATURE', 1.0)
            config['DEEPSEEK_API_KEY'] = new_config.get('DEEPSEEK_API_KEY', config.get('DEEPSEEK_API_KEY', ''))
            
            # Save updated config
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            logger.info("AI chat configuration updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error updating AI config: {e}")
            return False
    
    def is_ai_enabled(self) -> bool:
        """Check if AI chat is enabled"""
        config = self.get_ai_config()
        return config.get('AI_REPLY_ENABLED', False)
    
    def get_api_key(self) -> Optional[str]:
        """Get DeepSeek API key"""
        config = self.get_ai_config()
        api_key = config.get('DEEPSEEK_API_KEY', '')
        return api_key if api_key else None
    
    def get_system_prompt(self) -> str:
        """Get AI system prompt"""
        config = self.get_ai_config()
        return config.get('AI_SYSTEM_PROMPT', DEFAULT_SYSTEM_PROMPT)
    
    def get_temperature(self) -> float:
        """Get AI temperature setting"""
        config = self.get_ai_config()
        return config.get('AI_TEMPERATURE', 1.0)
    
    def get_cooldown_minutes(self) -> int:
        """Get AI reply cooldown in minutes"""
        config = self.get_ai_config()
        return config.get('AI_REPLY_COOLDOWN_MINUTES', 60)
    
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Validate AI chat configuration"""
        errors = {}
        
        # Validate temperature
        temperature = config.get('AI_TEMPERATURE', 1.0)
        if not isinstance(temperature, (int, float)) or temperature < 0 or temperature > 2:
            errors['AI_TEMPERATURE'] = 'Temperature must be a number between 0 and 2'
        
        # Validate cooldown
        cooldown = config.get('AI_REPLY_COOLDOWN_MINUTES', 60)
        if not isinstance(cooldown, int) or cooldown < 1:
            errors['AI_REPLY_COOLDOWN_MINUTES'] = 'Cooldown must be a positive integer'
        
        # Validate system prompt
        prompt = config.get('AI_SYSTEM_PROMPT', '')
        if not prompt or len(prompt.strip()) < 10:
            errors['AI_SYSTEM_PROMPT'] = 'System prompt must be at least 10 characters long'
        
        return errors
