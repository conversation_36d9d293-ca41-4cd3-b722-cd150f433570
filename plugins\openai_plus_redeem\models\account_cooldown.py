"""
Account Cooldown Model

Data model for managing account cooldown periods including
reset tracking, admin controls, and abuse prevention.
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum


class CooldownType(Enum):
    """Enumeration of cooldown types"""
    REDEMPTION = "redemption"
    ABUSE = "abuse"
    MANUAL = "manual"
    SYSTEM = "system"


class CooldownReason(Enum):
    """Enumeration of cooldown reasons"""
    REDEMPTION_LIMIT = "redemption_limit"
    ABUSE_PREVENTION = "abuse_prevention"
    MANUAL_ADMIN = "manual_admin"
    SYSTEM_ERROR = "system_error"
    ACCOUNT_EXPIRED = "account_expired"


class CooldownStatus(Enum):
    """Enumeration of cooldown statuses"""
    ACTIVE = "active"
    EXPIRED = "expired"
    RESET = "reset"
    CANCELLED = "cancelled"


@dataclass
class AccountCooldown:
    """
    Data model for account cooldown management

    Attributes:
        cooldown_id: Unique cooldown ID
        buyer_username: Customer username
        order_id: Associated order ID (optional)
        redemption_id: Associated redemption ID (optional)
        cooldown_type: Type of cooldown
        cooldown_reason: Reason for cooldown
        cooldown_status: Current cooldown status
        cooldown_start: Cooldown start timestamp
        cooldown_end: Cooldown end timestamp
        original_duration_hours: Original cooldown duration
        reset_count: Number of times cooldown was reset
        max_resets: Maximum allowed resets
        reset_by_admin: Admin who performed reset (if any)
        reset_reason: Reason for reset
        created_at: Cooldown creation timestamp
        updated_at: Last update timestamp
        notes: Additional notes or comments
    """

    cooldown_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    buyer_username: str = ""
    order_id: str = ""
    redemption_id: str = ""
    cooldown_type: str = field(default=CooldownType.REDEMPTION.value)
    cooldown_reason: str = field(default=CooldownReason.REDEMPTION_LIMIT.value)
    cooldown_status: str = field(default=CooldownStatus.ACTIVE.value)
    cooldown_start: str = field(default_factory=lambda: datetime.now().isoformat())
    cooldown_end: str = ""
    original_duration_hours: int = 24
    reset_count: int = 0
    max_resets: int = 3
    reset_by_admin: str = ""
    reset_reason: str = ""
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    notes: str = ""
    
    def __post_init__(self):
        """Post-initialization setup"""
        if not self.cooldown_end and self.original_duration_hours > 0:
            # Set cooldown end based on duration
            start_time = datetime.fromisoformat(self.cooldown_start.replace('Z', '+00:00'))
            end_time = start_time + timedelta(hours=self.original_duration_hours)
            self.cooldown_end = end_time.isoformat()
    
    @property
    def type(self) -> CooldownType:
        """Get cooldown type as enum"""
        try:
            return CooldownType(self.cooldown_type)
        except ValueError:
            return CooldownType.REDEMPTION

    @type.setter
    def type(self, value: CooldownType):
        """Set cooldown type from enum"""
        self.cooldown_type = value.value
        self.updated_at = datetime.now().isoformat()

    @property
    def reason(self) -> CooldownReason:
        """Get cooldown reason as enum"""
        try:
            return CooldownReason(self.cooldown_reason)
        except ValueError:
            return CooldownReason.REDEMPTION_LIMIT

    @reason.setter
    def reason(self, value: CooldownReason):
        """Set cooldown reason from enum"""
        self.cooldown_reason = value.value
        self.updated_at = datetime.now().isoformat()

    @property
    def status(self) -> CooldownStatus:
        """Get cooldown status as enum"""
        try:
            return CooldownStatus(self.cooldown_status)
        except ValueError:
            return CooldownStatus.ACTIVE

    @status.setter
    def status(self, value: CooldownStatus):
        """Set cooldown status from enum"""
        self.cooldown_status = value.value
        self.updated_at = datetime.now().isoformat()
    
    def is_active(self) -> bool:
        """Check if the cooldown is currently active"""
        if self.status != CooldownStatus.ACTIVE:
            return False
        
        if not self.cooldown_end:
            return True  # Indefinite cooldown
        
        try:
            end_time = datetime.fromisoformat(self.cooldown_end.replace('Z', '+00:00'))
            return datetime.now() < end_time.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return False
    
    def is_expired(self) -> bool:
        """Check if the cooldown has naturally expired"""
        if not self.cooldown_end:
            return False  # Indefinite cooldown
        
        try:
            end_time = datetime.fromisoformat(self.cooldown_end.replace('Z', '+00:00'))
            return datetime.now() >= end_time.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return False
    
    def can_reset(self) -> bool:
        """Check if the cooldown can be reset"""
        return (
            self.status == CooldownStatus.ACTIVE and
            self.reset_count < self.max_resets
        )
    
    def get_remaining_time(self) -> timedelta:
        """
        Get remaining cooldown time
        
        Returns:
            Timedelta object representing remaining time
        """
        if not self.is_active() or not self.cooldown_end:
            return timedelta(0)
        
        try:
            end_time = datetime.fromisoformat(self.cooldown_end.replace('Z', '+00:00'))
            remaining = end_time.replace(tzinfo=None) - datetime.now()
            return max(timedelta(0), remaining)
        except (ValueError, AttributeError):
            return timedelta(0)
    
    def get_remaining_hours(self) -> int:
        """Get remaining cooldown hours"""
        remaining = self.get_remaining_time()
        return int(remaining.total_seconds() / 3600)
    
    def get_remaining_resets(self) -> int:
        """Get remaining reset attempts"""
        return max(0, self.max_resets - self.reset_count)
    
    def extend_cooldown(self, additional_hours: int, reason: str = "") -> None:
        """
        Extend the cooldown period
        
        Args:
            additional_hours: Hours to extend the cooldown
            reason: Reason for extension
        """
        try:
            if self.cooldown_end:
                current_end = datetime.fromisoformat(self.cooldown_end.replace('Z', '+00:00'))
            else:
                current_end = datetime.now()
            
            new_end = current_end + timedelta(hours=additional_hours)
            self.cooldown_end = new_end.isoformat()
            self.updated_at = datetime.now().isoformat()
            
            if reason:
                self.notes = f"{self.notes}\nExtended by {additional_hours}h: {reason}".strip()
                
        except (ValueError, AttributeError):
            # If current end is invalid, extend from now
            new_end = datetime.now() + timedelta(hours=additional_hours)
            self.cooldown_end = new_end.isoformat()
            self.updated_at = datetime.now().isoformat()
    
    def reset_cooldown(self, admin_username: str = "", reason: str = "") -> bool:
        """
        Reset the cooldown (admin action)
        
        Args:
            admin_username: Username of admin performing reset
            reason: Reason for reset
            
        Returns:
            True if reset was successful, False otherwise
        """
        if not self.can_reset():
            return False
        
        self.reset_count += 1
        self.reset_by_admin = admin_username
        self.reset_reason = reason
        self.status = CooldownStatus.RESET
        self.updated_at = datetime.now().isoformat()
        
        if reason:
            self.notes = f"{self.notes}\nReset #{self.reset_count} by {admin_username}: {reason}".strip()
        
        return True
    
    def cancel_cooldown(self, admin_username: str = "", reason: str = "") -> None:
        """
        Cancel the cooldown (admin action)
        
        Args:
            admin_username: Username of admin performing cancellation
            reason: Reason for cancellation
        """
        self.status = CooldownStatus.CANCELLED
        self.reset_by_admin = admin_username
        self.reset_reason = reason
        self.updated_at = datetime.now().isoformat()
        
        if reason:
            self.notes = f"{self.notes}\nCancelled by {admin_username}: {reason}".strip()
    
    def expire_cooldown(self) -> None:
        """Mark the cooldown as naturally expired"""
        if self.is_expired():
            self.status = CooldownStatus.EXPIRED
            self.updated_at = datetime.now().isoformat()
    
    def get_total_duration(self) -> timedelta:
        """
        Get total cooldown duration
        
        Returns:
            Timedelta representing total duration
        """
        if not self.cooldown_end:
            return timedelta(0)
        
        try:
            start_time = datetime.fromisoformat(self.cooldown_start.replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(self.cooldown_end.replace('Z', '+00:00'))
            return end_time.replace(tzinfo=None) - start_time.replace(tzinfo=None)
        except (ValueError, AttributeError):
            return timedelta(hours=self.original_duration_hours)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert cooldown to dictionary for JSON serialization"""
        return {
            'cooldown_id': self.cooldown_id,
            'buyer_username': self.buyer_username,
            'order_id': self.order_id,
            'redemption_id': self.redemption_id,
            'cooldown_type': self.cooldown_type,
            'cooldown_reason': self.cooldown_reason,
            'cooldown_status': self.cooldown_status,
            'cooldown_start': self.cooldown_start,
            'cooldown_end': self.cooldown_end,
            'original_duration_hours': self.original_duration_hours,
            'reset_count': self.reset_count,
            'max_resets': self.max_resets,
            'reset_by_admin': self.reset_by_admin,
            'reset_reason': self.reset_reason,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'notes': self.notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccountCooldown':
        """Create cooldown from dictionary"""
        return cls(
            cooldown_id=data.get('cooldown_id', str(uuid.uuid4())),
            buyer_username=data.get('buyer_username', ''),
            order_id=data.get('order_id', ''),
            redemption_id=data.get('redemption_id', ''),
            cooldown_type=data.get('cooldown_type', CooldownType.REDEMPTION.value),
            cooldown_reason=data.get('cooldown_reason', CooldownReason.REDEMPTION_LIMIT.value),
            cooldown_status=data.get('cooldown_status', CooldownStatus.ACTIVE.value),
            cooldown_start=data.get('cooldown_start', datetime.now().isoformat()),
            cooldown_end=data.get('cooldown_end', ''),
            original_duration_hours=data.get('original_duration_hours', 24),
            reset_count=data.get('reset_count', 0),
            max_resets=data.get('max_resets', 3),
            reset_by_admin=data.get('reset_by_admin', ''),
            reset_reason=data.get('reset_reason', ''),
            created_at=data.get('created_at', datetime.now().isoformat()),
            updated_at=data.get('updated_at', datetime.now().isoformat()),
            notes=data.get('notes', '')
        )
    
    @classmethod
    def create_for_user(cls, buyer_username: str, duration_hours: int = 24,
                       cooldown_type: CooldownType = CooldownType.REDEMPTION,
                       reason: CooldownReason = CooldownReason.REDEMPTION_LIMIT,
                       order_id: str = "", redemption_id: str = "") -> 'AccountCooldown':
        """
        Create a new cooldown for a user

        Args:
            buyer_username: Customer username
            duration_hours: Cooldown duration in hours
            cooldown_type: Type of cooldown
            reason: Reason for cooldown
            order_id: Associated order ID
            redemption_id: Associated redemption ID

        Returns:
            New AccountCooldown instance
        """
        return cls(
            buyer_username=buyer_username,
            order_id=order_id,
            redemption_id=redemption_id,
            cooldown_type=cooldown_type.value,
            cooldown_reason=reason.value,
            original_duration_hours=duration_hours
        )
    
    def __str__(self) -> str:
        """String representation of the cooldown"""
        remaining = self.get_remaining_hours()
        return f"Cooldown {self.buyer_username} - {self.cooldown_status} - {remaining}h remaining"
