"""
Product Template Engine

This module provides the product template system for fake order generation,
including data models, validation, and template management functionality.
"""

import json
import os
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class ProductTemplate:
    """Template for product-specific fake order data with comprehensive validation"""
    var_sku: str
    product_name: str
    category: str
    default_price: float
    metadata_schema: Dict[str, Any] = field(default_factory=dict)
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    sample_data: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate template data after initialization"""
        self._validate_template()
    
    def _validate_template(self):
        """Validate template data integrity"""
        errors = []
        
        # Validate required fields
        if not self.var_sku or not isinstance(self.var_sku, str):
            errors.append("var_sku must be a non-empty string")
        
        if not self.product_name or not isinstance(self.product_name, str):
            errors.append("product_name must be a non-empty string")
        
        if not self.category or not isinstance(self.category, str):
            errors.append("category must be a non-empty string")
        
        if not isinstance(self.default_price, (int, float)) or self.default_price < 0:
            errors.append("default_price must be a non-negative number")
        
        # Validate metadata schema structure
        if self.metadata_schema and not isinstance(self.metadata_schema, dict):
            errors.append("metadata_schema must be a dictionary")
        
        # Validate validation rules structure
        if self.validation_rules and not isinstance(self.validation_rules, dict):
            errors.append("validation_rules must be a dictionary")
        
        # Validate sample data structure
        if self.sample_data and not isinstance(self.sample_data, dict):
            errors.append("sample_data must be a dictionary")
        
        if errors:
            raise ValueError(f"Template validation failed for {self.var_sku}: {'; '.join(errors)}")
    
    def validate_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Validate metadata against template schema"""
        errors = []
        warnings = []
        
        # Check required fields from validation rules
        required_fields = self.validation_rules.get('required_fields', [])
        for field in required_fields:
            if field not in metadata:
                errors.append(f"Required field '{field}' is missing")
        
        # Check field types from metadata schema
        for field, schema in self.metadata_schema.items():
            if field in metadata:
                expected_type = schema.get('type')
                if expected_type and not isinstance(metadata[field], eval(expected_type)):
                    errors.append(f"Field '{field}' should be of type {expected_type}")
                
                # Check allowed values
                allowed_values = schema.get('allowed_values')
                if allowed_values and metadata[field] not in allowed_values:
                    errors.append(f"Field '{field}' value '{metadata[field]}' not in allowed values: {allowed_values}")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert template to dictionary for serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProductTemplate':
        """Create template from dictionary"""
        return cls(**data)
    
    def serialize(self) -> str:
        """Serialize template to JSON string"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)
    
    @classmethod
    def deserialize(cls, json_str: str) -> 'ProductTemplate':
        """Deserialize template from JSON string"""
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {e}")
        except Exception as e:
            raise ValueError(f"Failed to deserialize template: {e}")


class ProductTemplateEngine:
    """Engine for managing product templates and metadata generation"""
    
    def __init__(self, templates_file: str = 'configs/data/product_templates.json'):
        self.templates_file = templates_file
        self.templates: Dict[str, ProductTemplate] = {}
        self._ensure_templates_file()
        self._load_built_in_templates()
        self._load_custom_templates()
    
    def _ensure_templates_file(self):
        """Ensure templates file exists"""
        os.makedirs(os.path.dirname(self.templates_file), exist_ok=True)
        if not os.path.exists(self.templates_file):
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, indent=2)
    
    def _load_built_in_templates(self):
        """Load built-in product templates"""
        # Steam products
        self.templates["steam_auth_code"] = ProductTemplate(
            var_sku="steam_auth_code",
            product_name="Steam Authentication Code",
            category="gaming",
            default_price=25.00,
            metadata_schema={
                "auth_method": {"type": "str", "allowed_values": ["mobile", "email", "sms"]},
                "region": {"type": "str", "allowed_values": ["global", "us", "eu", "asia"]},
                "validity_days": {"type": "int", "min": 1, "max": 365}
            },
            validation_rules={
                "required_fields": ["auth_method", "region"],
                "optional_fields": ["validity_days"]
            },
            sample_data={
                "auth_method": "mobile",
                "region": "global",
                "validity_days": 30,
                "code_format": "alphanumeric",
                "delivery_method": "instant"
            }
        )
        
        self.templates["text_based"] = ProductTemplate(
            var_sku="text_based",
            product_name="Steam Account (Text Based)",
            category="gaming",
            default_price=20.00,
            metadata_schema={
                "account_type": {"type": "str", "allowed_values": ["standard", "premium", "family"]},
                "region": {"type": "str", "allowed_values": ["global", "us", "eu", "asia"]},
                "games_included": {"type": "list"}
            },
            validation_rules={
                "required_fields": ["account_type", "region"],
                "optional_fields": ["games_included"]
            },
            sample_data={
                "account_type": "standard",
                "region": "global",
                "games_included": [],
                "account_level": 1,
                "creation_date": "2024-01-01"
            }
        )
        
        # Netflix products
        self.templates["netflix_30"] = ProductTemplate(
            var_sku="netflix_30",
            product_name="Netflix Premium 30 Days",
            category="streaming",
            default_price=35.00,
            metadata_schema={
                "subscription_type": {"type": "str", "allowed_values": ["basic", "standard", "premium"]},
                "duration_days": {"type": "int", "min": 1, "max": 365},
                "region": {"type": "str", "allowed_values": ["malaysia", "singapore", "thailand", "global"]}
            },
            validation_rules={
                "required_fields": ["subscription_type", "duration_days", "region"],
                "optional_fields": []
            },
            sample_data={
                "subscription_type": "premium",
                "duration_days": 30,
                "region": "malaysia",
                "max_screens": 4,
                "hd_available": True,
                "uhd_available": True
            }
        )
        
        self.templates["netflix_60"] = ProductTemplate(
            var_sku="netflix_60",
            product_name="Netflix Premium 60 Days",
            category="streaming",
            default_price=65.00,
            metadata_schema={
                "subscription_type": {"type": "str", "allowed_values": ["basic", "standard", "premium"]},
                "duration_days": {"type": "int", "min": 1, "max": 365},
                "region": {"type": "str", "allowed_values": ["malaysia", "singapore", "thailand", "global"]}
            },
            validation_rules={
                "required_fields": ["subscription_type", "duration_days", "region"],
                "optional_fields": []
            },
            sample_data={
                "subscription_type": "premium",
                "duration_days": 60,
                "region": "malaysia",
                "max_screens": 4,
                "hd_available": True,
                "uhd_available": True
            }
        )
        
        # Canva products
        self.templates["canva_30"] = ProductTemplate(
            var_sku="canva_30",
            product_name="Canva Pro 30 Days",
            category="design",
            default_price=15.00,
            metadata_schema={
                "subscription_type": {"type": "str", "allowed_values": ["pro", "teams", "enterprise"]},
                "duration_days": {"type": "int", "min": 1, "max": 365},
                "features": {"type": "list"}
            },
            validation_rules={
                "required_fields": ["subscription_type", "duration_days"],
                "optional_fields": ["features"]
            },
            sample_data={
                "subscription_type": "pro",
                "duration_days": 30,
                "features": ["premium_templates", "background_remover", "brand_kit", "resize_magic"],
                "storage_gb": 100,
                "team_members": 1
            }
        )
        
        self.templates["canva_lifetime"] = ProductTemplate(
            var_sku="canva_lifetime",
            product_name="Canva Pro Lifetime",
            category="design",
            default_price=199.00,
            metadata_schema={
                "subscription_type": {"type": "str", "allowed_values": ["pro", "teams", "enterprise"]},
                "duration_days": {"type": "int"},
                "features": {"type": "list"}
            },
            validation_rules={
                "required_fields": ["subscription_type"],
                "optional_fields": ["features"]
            },
            sample_data={
                "subscription_type": "pro",
                "duration_days": -1,  # -1 indicates lifetime
                "features": ["premium_templates", "background_remover", "brand_kit", "resize_magic", "lifetime_access"],
                "storage_gb": 1000,
                "team_members": 1
            }
        )
        
        # VPN products
        self.templates["vpn_monthly"] = ProductTemplate(
            var_sku="vpn_monthly",
            product_name="VPN Service Monthly",
            category="security",
            default_price=12.00,
            metadata_schema={
                "duration_days": {"type": "int", "min": 1, "max": 365},
                "server_locations": {"type": "list"},
                "protocol": {"type": "str", "allowed_values": ["wireguard", "openvpn", "ikev2"]}
            },
            validation_rules={
                "required_fields": ["duration_days", "protocol"],
                "optional_fields": ["server_locations"]
            },
            sample_data={
                "duration_days": 30,
                "server_locations": ["singapore", "malaysia", "thailand", "japan"],
                "protocol": "wireguard",
                "max_devices": 5,
                "bandwidth_limit": "unlimited"
            }
        )
        
        self.templates["vpn_yearly"] = ProductTemplate(
            var_sku="vpn_yearly",
            product_name="VPN Service Yearly",
            category="security",
            default_price=120.00,
            metadata_schema={
                "duration_days": {"type": "int", "min": 1, "max": 365},
                "server_locations": {"type": "list"},
                "protocol": {"type": "str", "allowed_values": ["wireguard", "openvpn", "ikev2"]}
            },
            validation_rules={
                "required_fields": ["duration_days", "protocol"],
                "optional_fields": ["server_locations"]
            },
            sample_data={
                "duration_days": 365,
                "server_locations": ["singapore", "malaysia", "thailand", "japan", "usa", "uk", "germany"],
                "protocol": "wireguard",
                "max_devices": 10,
                "bandwidth_limit": "unlimited"
            }
        )
        
        # Hulu products
        self.templates["hulu_10"] = ProductTemplate(
            var_sku="hulu_10",
            product_name="Hulu Account 10 Days",
            category="streaming",
            default_price=8.00,
            metadata_schema={
                "subscription_type": {"type": "str", "allowed_values": ["basic", "premium", "live_tv"]},
                "duration_days": {"type": "int", "min": 1, "max": 365},
                "region": {"type": "str", "allowed_values": ["usa", "japan"]}
            },
            validation_rules={
                "required_fields": ["subscription_type", "duration_days", "region"],
                "optional_fields": []
            },
            sample_data={
                "subscription_type": "basic",
                "duration_days": 10,
                "region": "usa",
                "ads_included": True,
                "offline_downloads": False
            }
        )
    
    def _load_custom_templates(self):
        """Load custom templates from file"""
        try:
            with open(self.templates_file, 'r', encoding='utf-8') as f:
                custom_templates_data = json.load(f)
            
            for sku, template_data in custom_templates_data.items():
                try:
                    template = ProductTemplate.from_dict(template_data)
                    self.templates[sku] = template
                    logger.info(f"Loaded custom template: {sku}")
                except Exception as e:
                    logger.error(f"Failed to load custom template {sku}: {e}")
                    
        except (FileNotFoundError, json.JSONDecodeError):
            logger.info("No custom templates file found or file is empty")
    
    def register_product_template(self, template: ProductTemplate, save_to_file: bool = True):
        """Register a new product template"""
        # Validate template
        template._validate_template()
        
        # Add to memory
        self.templates[template.var_sku] = template
        
        # Save to file if requested
        if save_to_file:
            self._save_custom_template(template)
        
        logger.info(f"Registered product template: {template.var_sku}")
    
    def _save_custom_template(self, template: ProductTemplate):
        """Save custom template to file"""
        try:
            # Load existing custom templates
            try:
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    custom_templates = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                custom_templates = {}
            
            # Add new template
            custom_templates[template.var_sku] = template.to_dict()
            
            # Save updated templates
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(custom_templates, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save custom template {template.var_sku}: {e}")
            raise
    
    def get_template_for_sku(self, var_sku: str) -> Optional[ProductTemplate]:
        """Get template for specific SKU"""
        return self.templates.get(var_sku)
    
    def get_all_templates(self) -> Dict[str, ProductTemplate]:
        """Get all available templates"""
        return self.templates.copy()
    
    def get_templates_by_category(self, category: str) -> Dict[str, ProductTemplate]:
        """Get templates filtered by category"""
        return {
            sku: template for sku, template in self.templates.items()
            if template.category == category
        }
    
    def generate_product_metadata(self, var_sku: str, custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate product-specific metadata using template with enhanced realistic data"""
        template = self.get_template_for_sku(var_sku)
        if not template:
            raise ValueError(f"No template found for SKU: {var_sku}")
        
        # Start with sample data
        metadata = template.sample_data.copy()
        
        # Apply custom configuration if provided (merge first, then validate)
        if custom_config:
            metadata.update(custom_config)
        
        # Import metadata generator here to avoid circular imports
        try:
            from .metadata_generator import metadata_generator
            # Generate enhanced metadata using the metadata generator
            metadata = metadata_generator.generate_custom_metadata(
                template.category, var_sku, metadata
            )
        except ImportError:
            logger.warning("Metadata generator not available, using basic metadata")
            # Add basic generation timestamp
            metadata["generated_at"] = datetime.now().isoformat()
            metadata["template_version"] = "1.0"
        
        # Validate final metadata (after enhancement) - only log warnings for validation issues
        if custom_config:
            validation = template.validate_metadata(metadata)
            if not validation["is_valid"]:
                logger.warning(f"Metadata validation warnings for {var_sku}: {validation['errors']}")
        
        return metadata
    
    def validate_sku(self, var_sku: str) -> Dict[str, Any]:
        """Validate if SKU exists and return suggestions if not"""
        if var_sku in self.templates:
            return {
                "is_valid": True,
                "template": self.templates[var_sku].to_dict()
            }
        
        # Find similar SKUs
        similar_skus = []
        for sku in self.templates.keys():
            if var_sku.lower() in sku.lower() or sku.lower() in var_sku.lower():
                similar_skus.append(sku)
        
        return {
            "is_valid": False,
            "error": f"SKU '{var_sku}' not found",
            "available_skus": list(self.templates.keys()),
            "similar_skus": similar_skus
        }
    
    def get_template_summary(self) -> Dict[str, Any]:
        """Get summary of all templates"""
        categories = {}
        for template in self.templates.values():
            if template.category not in categories:
                categories[template.category] = []
            categories[template.category].append({
                "var_sku": template.var_sku,
                "product_name": template.product_name,
                "default_price": template.default_price
            })
        
        return {
            "total_templates": len(self.templates),
            "categories": categories,
            "all_skus": list(self.templates.keys())
        }


# Global template engine instance
product_template_engine = ProductTemplateEngine()