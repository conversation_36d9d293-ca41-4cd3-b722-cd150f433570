# ShopeeAPI 文件迁移总结

## 迁移概述

将 ShopeeAPI 相关的运行脚本从根目录移动到 `ShopeeAPI/` 目录中，以便更好地组织项目结构。

## 迁移的文件

### 1. 从根目录移动到 ShopeeAPI/

- `run_shopee_api.py` → `ShopeeAPI/run_shopee_api.py`
- `docker-hub-push.bat` → `ShopeeAPI/docker-hub-push.bat`

## 修改的文件和路径

### 1. ShopeeAPI/run_shopee_api.py
**修改内容**:
- 更新路径检测逻辑，现在检测当前目录而不是子目录
- 修改 uvicorn 启动命令从 `"ShopeeAPI.api:app"` 改为 `"api:app"`

**原因**: 脚本现在在 ShopeeAPI 目录内，可以直接导入 api 模块

### 2. ShopeeAPI/docker-hub-push.bat
**修改内容**:
- 更新 Dockerfile 路径检测，现在检查当前目录的 Dockerfile
- 修改 Docker 构建上下文为当前目录
- 添加了详细的使用说明

**原因**: 脚本现在在 ShopeeAPI 目录内，应该使用当前目录作为构建上下文

### 3. ShopeeAPI/run.bat
**修改内容**:
- 将 `ShopeeAPI.api:app` 改为 `api:app`

### 4. ShopeeAPI/run.sh
**修改内容**:
- 将 `ShopeeAPI.api:app` 改为 `api:app`

### 5. ShopeeAPI/run_in_baota.sh
**修改内容**:
- 将 `ShopeeAPI.api:app` 改为 `api:app`
- 更新注释说明

### 6. ShopeeAPI/docker-compose.yml
**修改内容**:
- 将构建上下文从 `context: ..` 改为 `context: .`
- 将 Dockerfile 路径从 `dockerfile: ShopeeAPI/Dockerfile` 改为 `dockerfile: Dockerfile`
- 将卷挂载路径从 `/app/ShopeeAPI/config.json` 改为 `/app/config.json`

### 7. ShopeeAPI/docker-compose.prod.yml
**修改内容**:
- 将卷挂载路径从 `/app/ShopeeAPI/config.json` 改为 `/app/config.json`

### 8. ShopeeAPI/README.md
**修改内容**:
- 更新运行命令说明，现在需要先进入 ShopeeAPI 目录
- 更新测试运行命令
- 更新生产环境运行命令

## 使用方法更新

### 开发模式运行

**之前**:
```bash
# 从根目录运行
python run_shopee_api.py
# 或
ShopeeAPI\run.bat
```

**现在**:
```bash
# 需要先进入 ShopeeAPI 目录
cd ShopeeAPI

# 然后运行
python run_shopee_api.py
# 或
run.bat
# 或
python main.py
```

### Docker 构建

**之前**:
```bash
# 从根目录运行
docker-hub-push.bat --build
```

**现在**:
```bash
# 需要先进入 ShopeeAPI 目录
cd ShopeeAPI

# 然后运行
docker-hub-push.bat --build
```

### Docker Compose

**之前**:
```bash
# 从根目录运行，需要指定路径
cd ShopeeAPI
docker-compose up -d
```

**现在**:
```bash
# 进入 ShopeeAPI 目录
cd ShopeeAPI

# 直接运行
docker-compose up -d
```

## 优势

1. **更清晰的项目结构** - ShopeeAPI 相关的所有文件都在同一个目录中
2. **简化的路径管理** - 不需要处理复杂的相对路径
3. **独立的服务管理** - ShopeeAPI 可以作为独立的服务进行管理
4. **更好的 Docker 支持** - Docker 构建和运行更加直观

## 注意事项

1. **工作目录要求** - 现在所有 ShopeeAPI 相关的命令都需要在 `ShopeeAPI/` 目录中运行
2. **导入路径变化** - Python 导入从 `ShopeeAPI.api:app` 变为 `api:app`
3. **配置文件路径** - Docker 容器中的配置文件路径从 `/app/ShopeeAPI/config.json` 变为 `/app/config.json`

## 向后兼容性

- 所有 API 端点保持不变
- 配置文件格式保持不变
- 功能特性保持不变
- 只是运行方式和文件组织发生了变化

## 测试建议

1. 测试开发模式运行: `cd ShopeeAPI && python run_shopee_api.py`
2. 测试 Docker 构建: `cd ShopeeAPI && docker-hub-push.bat --build`
3. 测试 Docker Compose: `cd ShopeeAPI && docker-compose up -d`
4. 验证所有 API 端点正常工作
5. 验证 WebSocket 连接正常

---

**迁移完成日期**: 2025-01-22
**状态**: ✅ 完成
