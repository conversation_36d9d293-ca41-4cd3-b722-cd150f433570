"""
Rate Limiting Utilities for SteamCodeTool
Implements various rate limiting strategies for enhanced security
"""

import time
import threading
from collections import defaultdict, deque
from typing import Dict, Optional, Tuple
from functools import wraps
from flask import request, jsonify, g
import logging

logger = logging.getLogger(__name__)

class RateLimiter:
    """Simple in-memory rate limiter using sliding window"""
    
    def __init__(self):
        self.requests = defaultdict(deque)
        self.lock = threading.Lock()
    
    def is_allowed(self, key: str, limit: int, window: int) -> Tuple[bool, Dict[str, int]]:
        """
        Check if request is allowed under rate limit
        
        Args:
            key: Unique identifier (IP, user, etc.)
            limit: Maximum requests allowed
            window: Time window in seconds
        
        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        current_time = time.time()
        cutoff_time = current_time - window
        
        with self.lock:
            # Clean old requests
            while self.requests[key] and self.requests[key][0] <= cutoff_time:
                self.requests[key].popleft()
            
            # Check if under limit
            current_count = len(self.requests[key])
            is_allowed = current_count < limit
            
            if is_allowed:
                self.requests[key].append(current_time)
            
            # Calculate reset time
            if self.requests[key]:
                reset_time = int(self.requests[key][0] + window)
            else:
                reset_time = int(current_time + window)
            
            return is_allowed, {
                'limit': limit,
                'remaining': max(0, limit - current_count - (1 if is_allowed else 0)),
                'reset': reset_time,
                'retry_after': max(0, reset_time - int(current_time)) if not is_allowed else 0
            }

class SecurityRateLimiter:
    """Enhanced rate limiter with security features"""
    
    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.blocked_ips = set()
        self.login_attempts = defaultdict(list)
        self.lock = threading.Lock()
    
    def check_login_attempts(self, ip: str, max_attempts: int = 5, window: int = 900) -> bool:
        """
        Check login attempts for potential brute force attacks
        
        Args:
            ip: IP address
            max_attempts: Maximum failed attempts allowed
            window: Time window in seconds (default 15 minutes)
        
        Returns:
            True if allowed, False if blocked
        """
        current_time = time.time()
        cutoff_time = current_time - window
        
        with self.lock:
            # Clean old attempts
            self.login_attempts[ip] = [
                attempt_time for attempt_time in self.login_attempts[ip]
                if attempt_time > cutoff_time
            ]
            
            # Check if IP should be blocked
            if len(self.login_attempts[ip]) >= max_attempts:
                self.blocked_ips.add(ip)
                logger.warning(f"IP {ip} blocked due to excessive login attempts")
                return False
            
            return ip not in self.blocked_ips
    
    def record_failed_login(self, ip: str):
        """Record a failed login attempt"""
        with self.lock:
            self.login_attempts[ip].append(time.time())
    
    def is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is blocked"""
        return ip in self.blocked_ips
    
    def unblock_ip(self, ip: str):
        """Manually unblock an IP"""
        with self.lock:
            self.blocked_ips.discard(ip)
            if ip in self.login_attempts:
                del self.login_attempts[ip]

# Global rate limiter instance
security_rate_limiter = SecurityRateLimiter()

def get_client_ip() -> str:
    """Get client IP address from request"""
    # Check for X-Forwarded-For header (for proxies)
    if request.environ.get('HTTP_X_FORWARDED_FOR'):
        return request.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()
    
    # Check for X-Real-IP header (for nginx)
    if request.environ.get('HTTP_X_REAL_IP'):
        return request.environ['HTTP_X_REAL_IP']
    
    # Fall back to remote address
    return request.environ.get('REMOTE_ADDR', '127.0.0.1')

def rate_limit(limit: int = 100, window: int = 3600, per: str = 'ip', 
               key_func: Optional[callable] = None):
    """
    Decorator for rate limiting endpoints
    
    Args:
        limit: Maximum requests allowed
        window: Time window in seconds
        per: Rate limit per ('ip', 'user', 'custom')
        key_func: Custom function to generate rate limit key
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Generate rate limit key
            if key_func:
                key = key_func()
            elif per == 'ip':
                key = f"ip:{get_client_ip()}"
            elif per == 'user':
                # Assume session-based user identification
                user_id = getattr(g, 'user_id', get_client_ip())
                key = f"user:{user_id}"
            else:
                key = f"custom:{get_client_ip()}"
            
            # Check rate limit
            is_allowed, rate_info = security_rate_limiter.rate_limiter.is_allowed(
                key, limit, window
            )
            
            # Add rate limit headers
            response_headers = {
                'X-RateLimit-Limit': str(rate_info['limit']),
                'X-RateLimit-Remaining': str(rate_info['remaining']),
                'X-RateLimit-Reset': str(rate_info['reset'])
            }
            
            if not is_allowed:
                response = jsonify({
                    'error': 'Rate limit exceeded',
                    'message': f'Too many requests. Try again in {rate_info["retry_after"]} seconds.',
                    'retry_after': rate_info['retry_after']
                })
                response.status_code = 429
                response.headers.update(response_headers)
                response.headers['Retry-After'] = str(rate_info['retry_after'])
                return response
            
            # Execute the function
            result = f(*args, **kwargs)
            
            # Add headers to successful response
            if hasattr(result, 'headers'):
                result.headers.update(response_headers)
            
            return result
        
        return decorated_function
    return decorator

def admin_rate_limit(limit: int = 50, window: int = 3600):
    """Rate limiter specifically for admin endpoints"""
    return rate_limit(limit=limit, window=window, per='ip')

def api_rate_limit(limit: int = 200, window: int = 3600):
    """Rate limiter for API endpoints"""
    return rate_limit(limit=limit, window=window, per='ip')

def auth_rate_limit(limit: int = 10, window: int = 900):
    """Rate limiter for authentication endpoints"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = get_client_ip()
            
            # Check if IP is blocked
            if security_rate_limiter.is_ip_blocked(client_ip):
                return jsonify({
                    'error': 'IP blocked',
                    'message': 'Your IP has been temporarily blocked due to suspicious activity.'
                }), 403
            
            # Check login attempts
            if not security_rate_limiter.check_login_attempts(client_ip):
                return jsonify({
                    'error': 'Too many failed attempts',
                    'message': 'Too many failed login attempts. Please try again later.'
                }), 429
            
            # Apply regular rate limiting
            @rate_limit(limit=limit, window=window, per='ip')
            def rate_limited_func():
                return f(*args, **kwargs)
            
            return rate_limited_func()
        
        return decorated_function
    return decorator

def record_failed_login():
    """Record a failed login attempt for the current IP"""
    client_ip = get_client_ip()
    security_rate_limiter.record_failed_login(client_ip)

def check_security_headers(response):
    """Add security-related rate limiting headers"""
    client_ip = get_client_ip()
    
    # Add security headers
    if security_rate_limiter.is_ip_blocked(client_ip):
        response.headers['X-Security-Status'] = 'blocked'
    
    return response