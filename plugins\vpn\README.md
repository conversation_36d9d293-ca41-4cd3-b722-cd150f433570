# VPN Plugin

A comprehensive VPN management plugin for managing VPN servers, clients, and configurations using the BlueBlue API (blueblue.api.limjianhui.com).

## Features

### 1. Server Management
- **CRUD Operations**: Create, read, update, and delete VPN servers
- **SSH Connection**: Support for both password and SSH key authentication
- **Connection Testing**: Test server credentials before saving
- **Service Control**: Start, stop, and restart Xray service remotely
- **Health Monitoring**: Real-time server health status and monitoring
- **Batch Operations**: Manage multiple servers simultaneously

### 2. Client Management
- **Client CRUD**: Create, read, update, and delete VPN clients
- **Server-Specific Management**: Dedicated interface for managing clients by server
- **Bulk Import**: Import multiple clients at once via CSV format
- **Expiry Management**: Track and manage client expiration dates
- **Auto-Extension**: Extend client expiry dates with one click
- **Search & Filter**: Advanced filtering by server, status, expiry, etc.
- **Data Usage Tracking**: Monitor client data usage
- **Sync Operations**: Sync clients between database and server configs
- **Client Statistics**: Real-time statistics for each server's clients

### 3. Configuration Management
- **Backup & Restore**: Create backups and restore configurations
- **Configuration Editor**: Edit Xray configurations with JSON validation
- **Sync Management**: Sync configurations between database and servers
- **Validation**: Validate configurations before applying
- **Version Control**: Track configuration changes over time
- **Bulk Operations**: Backup/restore multiple servers at once

### 4. Health Monitoring
- **Dashboard**: Comprehensive health dashboard for all servers
- **Real-time Status**: Monitor server and service status in real-time
- **SSH Pool Monitoring**: Track SSH connection pool status
- **Resource Monitoring**: Monitor CPU, memory, and disk usage
- **Expiry Alerts**: Get alerts for expiring clients
- **Auto Health Checks**: Automatic periodic health checks

### 5. Server-Client Management (NEW)
- **Server-Specific Views**: Dedicated page for each server's clients
- **Contextual Management**: Always know which server you're managing
- **Client Statistics Dashboard**: Visual overview of client counts and status
- **Advanced Filtering**: Search and filter clients by status, email, username
- **Quick Actions**: Edit, extend, delete clients with one click
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Direct Navigation**: Easy access from server list with "Manage Clients" button

### 6. Redemption System
- **Code Generation**: Generate redemption codes for VPN access
- **Code Management**: Track and manage redemption codes
- **Auto-provisioning**: Automatically create clients on redemption
- **Expiry Control**: Set expiry dates for redemption codes

## API Endpoints

The plugin integrates with the BlueBlue VPN API and provides the following endpoints:

### Authentication
- `POST /api/v1/auth/login` - Login to get access token
- `POST /api/v1/auth/logout` - Logout
- `POST /api/v1/auth/refresh` - Refresh access token
- `GET /api/v1/auth/me` - Get current user info

### Servers
- `GET /api/v1/servers/` - List all servers
- `POST /api/v1/servers/` - Create new server
- `GET /api/v1/servers/{id}` - Get server details
- `PUT /api/v1/servers/{id}` - Update server
- `DELETE /api/v1/servers/{id}` - Delete server
- `POST /api/v1/servers/test-credentials` - Test server credentials
- `POST /api/v1/servers/{id}/test-connection` - Test server connection
- `POST /api/v1/servers/{id}/restart-xray` - Restart Xray service
- `GET /api/v1/servers/{id}/service-status` - Get service status
- `GET /api/v1/servers/{id}/config` - Get server configuration
- `GET /api/v1/servers/{id}/clients` - Get server clients
- `POST /api/v1/servers/{id}/remove-expired` - Remove expired clients

### Clients
- `GET /api/v1/clients/` - List clients with filtering
- `POST /api/v1/clients/` - Create new client
- `GET /api/v1/clients/{id}` - Get client details
- `PUT /api/v1/clients/{id}` - Update client
- `DELETE /api/v1/clients/{id}` - Delete client
- `GET /api/v1/clients/by-email/{email}` - Get client by email
- `POST /api/v1/clients/bulk` - Create multiple clients
- `POST /api/v1/clients/{id}/extend` - Extend client expiry
- `GET /api/v1/clients/server/{id}/expiry` - Get expiry info for server clients
- `POST /api/v1/clients/sync/server/{id}` - Sync server clients
- `POST /api/v1/clients/sync/all` - Sync all servers
- `GET /api/v1/clients/sync/compare/{id}` - Compare server clients
- `GET /api/v1/clients/sync/analyze/{id}` - Analyze invalid clients
- `POST /api/v1/clients/sync/cleanup/{id}` - Cleanup orphaned clients

### Configuration
- `GET /api/v1/config/` - Get configurations overview
- `GET /api/v1/config/server/{id}` - Get server configuration
- `PUT /api/v1/config/server/{id}` - Update server configuration
- `POST /api/v1/config/backup` - Backup configurations
- `POST /api/v1/config/restore` - Restore configuration
- `GET /api/v1/config/validate/server/{id}` - Validate server config
- `GET /api/v1/config/backups` - List configuration backups
- `GET /api/v1/config/compare/server/{id}` - Compare server config
- `POST /api/v1/config/sync` - Sync configurations

### Health
- `GET /api/v1/health/` - Get health dashboard
- `GET /api/v1/health/detailed` - Get detailed health check
- `GET /api/v1/health/expiry-summary` - Get expiry health check
- `GET /api/v1/health/servers/{id}` - Get server health
- `POST /api/v1/health/servers/{id}/refresh` - Refresh server health
- `POST /api/v1/health/refresh-all-servers` - Refresh all servers health
- `GET /api/v1/health/ssh-pool` - Get SSH pool status

### Tasks
- `GET /api/v1/tasks/` - List tasks
- `GET /api/v1/tasks/{id}` - Get task details
- `POST /api/v1/tasks/expiry-check` - Trigger expiry check
- `GET /api/v1/tasks/expiry/summary` - Get expiry summary

## Configuration

The plugin can be configured through the admin interface or by editing the configuration file:

```json
{
    "enabled": true,
    "client_config": {
        "default_traffic_gb": 100,
        "default_expiry_days": 30,
        "auto_remove_expired": false,
        "expiry_warning_days": 7
    },
    "server_config": {
        "default_xray_path": "/etc/xray/config.json",
        "default_xray_service": "xray",
        "ssh_timeout": 30,
        "health_check_interval": 300
    },
    "config_templates": {
        "templates_file": "configs/services/config_templates.json",
        "backup_directory": "backups/vpn",
        "max_backups": 10
    },
    "vpn_api": {
        "base_url": "https://blueblue.api.limjianhui.com",
        "username": "admin",
        "password": "admin123",
        "timeout": 30,
        "retry_attempts": 3,
        "retry_delay": 5
    },
    "redemption": {
        "enabled": true,
        "default_expiry_days": 30,
        "code_prefix": "VPN",
        "code_length": 12
    },
    "monitoring": {
        "enable_auto_health_check": true,
        "health_check_cron": "*/5 * * * *",
        "enable_expiry_notifications": true,
        "notification_channels": ["email", "webhook"]
    }
}
```

## Usage

### 1. Server Setup
1. Navigate to VPN → Servers
2. Click "Add Server"
3. Enter server details (host, SSH credentials)
4. Test connection before saving
5. Server will be added and health check will run

### 2. Client Management
1. Navigate to VPN → Clients
2. Click "Add Client" or "Bulk Create"
3. Select server and enter client details
4. Set expiry date (DD-MM-YYYY format or 'lifetime')
5. Client will be created and added to server config

### 3. Configuration Backup
1. Navigate to VPN → Configurations
2. Click "Backup"
3. Select servers to backup
4. Add optional description
5. Backups will be created and stored

### 4. Health Monitoring
1. Navigate to VPN → Dashboard
2. View overall system health
3. Check individual server status
4. Monitor client expiry status
5. Set up automatic health checks

## Templates

The plugin includes the following templates:
- `vpn_servers.html` - Server management interface
- `vpn_server_form.html` - Server create/edit form
- `vpn_clients.html` - Client management interface
- `vpn_client_form.html` - Client create/edit form
- `vpn_bulk_create.html` - Bulk client creation
- `vpn_configurations.html` - Configuration overview
- `vpn_server_config.html` - Server configuration viewer
- `vpn_config_edit.html` - Configuration editor
- `vpn_sync.html` - Configuration sync interface
- `vpn_backups.html` - Backup management
- `vpn_health.html` - Health dashboard
- `vpn_service_status.html` - Service status viewer
- `vpn_settings.html` - Plugin settings

## Security

- All API communications are authenticated using JWT tokens
- SSH connections support both password and key-based authentication
- Configuration backups are stored securely
- Client data is encrypted in transit
- Role-based access control for admin functions

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check API credentials in settings
   - Ensure API server is accessible
   - Verify network connectivity

2. **SSH Connection Failed**
   - Verify SSH credentials
   - Check firewall settings
   - Ensure SSH service is running

3. **Configuration Sync Issues**
   - Validate configuration JSON
   - Check file permissions on server
   - Ensure Xray service has proper permissions

4. **Client Creation Failed**
   - Check server configuration
   - Verify client ID uniqueness
   - Ensure proper date format

## Development

### Adding New Features

1. **New API Endpoint**
   - Add method to `VPNAPIService`
   - Create route in `vpn_routes.py`
   - Add template if needed

2. **New Template**
   - Create template in `templates/` directory
   - Add route to render template
   - Update navigation if needed

3. **New Service**
   - Create service class in `services/`
   - Initialize in `plugin.py`
   - Pass to route blueprint

## Support

For issues or questions:
1. Check the logs in the application
2. Verify API connectivity
3. Review configuration settings
4. Contact support with error details

## Version History

- **2.0.0** - Complete implementation with all API endpoints
- **1.0.0** - Initial release with basic functionality