from flask import Blueprint, jsonify, request
from services.order_service import (
    get_to_ship_orders, get_shipped_orders, get_completed_orders,
    search_order, ship_order, get_order_status, get_order_details
)
from services.stock_service import get_stock_item, update_stock
from utils.auth import require_api_key
from utils.var_sku_extractor import extract_var_sku
import config
from services.chat_service import send_chat_message
import html
from flask import Blueprint, jsonify, request
from services.netflix_service import get_netflix_signin_code
import json
import datetime
from services.netflix_service import get_netflix_signin_code_status

MANUAL_ORDERS_FILE = 'configs/data/manual_orders.json'
order_bp = Blueprint('order', __name__)

@order_bp.route('/get_to_ship_orders', methods=['GET'])
@require_api_key
def api_get_to_ship_orders():
    return jsonify(get_to_ship_orders())

@order_bp.route('/get_shipped_orders', methods=['GET'])
@require_api_key
def api_get_shipped_orders():
    return jsonify(get_shipped_orders())

@order_bp.route('/get_completed_orders', methods=['GET'])
@require_api_key
def api_get_completed_orders():
    return jsonify(get_completed_orders())

@order_bp.route('/search_order', methods=['GET'])
@require_api_key
def api_search_order():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn parameter is required"}), 400
    return jsonify(search_order(order_sn))

@order_bp.route('/ship_order', methods=['GET'])
@require_api_key
def api_ship_order():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn is required"}), 400
    return ship_order(order_sn)

@order_bp.route('/get_order_status', methods=['GET'])
@require_api_key
def api_get_order_status():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn parameter is required"}), 400
    return jsonify(get_order_status(order_sn))

@order_bp.route('/get_order_details', methods=['GET'])
@require_api_key
def api_get_order_details():
    """
    Updated API Endpoint:
    Fetch order details using order_sn instead of order_id.
    """
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn parameter is required"}), 400
    result, status = get_order_details(order_sn)
    return jsonify(result), status

@order_bp.route('/process_order', methods=['POST'])
def process_order():
    payload = request.json
    if not payload or 'order_sn' not in payload:
        return jsonify({"error": "Order SN is required"}), 400

    order_sn = payload['order_sn']

    # 检查是否为手动创建的订单
    manual_orders = load_manual_orders()
    manual_order = next((order for order in manual_orders if order['order_sn'] == order_sn), None)

    if manual_order:
        # 对于手动创建的订单，直接从库存中获取商品
        var_sku = manual_order['var_sku']
        result = get_stock_item(var_sku)
        if not result:
            return jsonify({"error": f"No result found for var_sku: {var_sku}"}), 400

        if result.get('is_unlimited_stock', False):
            # 对于无限库存的商品，直接返回结果
            message = result['message'].replace('{result}', 'Unlimited Stock')
            return jsonify({
                "status": "Processed",
                "results": [{
                    "var_sku": var_sku,
                    "result": message,
                    "is_manual_order": True,
                    "is_unlimited_stock": True
                }]
            })
        else:
            # 对于有限库存的商品，更新库存
            update_result = update_stock(var_sku, order_sn)
            if update_result['status'] == 'success':
                return jsonify({
                    "status": "Processed",
                    "results": [{
                        "var_sku": var_sku,
                        "result": update_result['item'],
                        "is_manual_order": True,
                        "is_unlimited_stock": False
                    }]
                })
            elif update_result['status'] == 'already_redeemed':
                # 处理已经兑换的情况
                return jsonify({
                    "status": "Processed",
                    "results": [{
                        "var_sku": var_sku,
                        "result": update_result['item'],
                        "is_manual_order": True,
                        "is_unlimited_stock": False,
                        "note": "Already redeemed"
                    }]
                })
            else:
                return jsonify({"error": f"Failed to process manual order: {update_result['status']}"}), 400

    # 获取订单详情
    order_details, details_status_code = get_order_details(order_sn)
    if details_status_code != 200:
        return jsonify({"error": "Failed to get order details"}), details_status_code

    # 检查订单状态
    order_status, status_code = get_order_status(order_sn)
    if status_code != 200:
        return jsonify({"error": "Failed to get order status"}), status_code

    if order_status['status'] in ["Shipped", "Completed", "Order Received", "Delivered"]:
        # 提取所有 var_sku
        var_skus = extract_var_sku(order_details)
        if not var_skus:
            return jsonify({"error": "Failed to get var_sku from order details"}), 400

        results = []
        for var_sku in var_skus:
            stock_item = get_stock_item(var_sku)
            if not stock_item:
                continue

            if stock_item.get('is_unlimited_stock', False):
                # 对于无限库存的商品，直接使用 result
                message = stock_item['message'].replace('{result}', 'Unlimited Stock')
                results.append({
                    "var_sku": var_sku,
                    "result": message,
                    "is_unlimited_stock": True,
                    "is_manual_order": False
                })
            else:
                # 对于有限库存的商品，从库存中获取
                redeemed_item = get_stock_item(var_sku, order_sn)
                if redeemed_item:
                    message = stock_item['message'].replace('{result}', redeemed_item['item'])
                    results.append({
                        "var_sku": var_sku,
                        "result": message,
                        "is_unlimited_stock": False,
                        "is_manual_order": False,
                        "note": "Already redeemed"
                    })
                else:
                    results.append({
                        "var_sku": var_sku,
                        "error": "No redeemed item found",
                        "is_unlimited_stock": False,
                        "is_manual_order": False
                    })

        return jsonify({
            "status": "Already Processed",
            "message": f"Order {order_sn} has already been {order_status['status'].lower()}. Showing redeemed items.",
            "order_status": order_status['status'],
            "results": results
        }), 200

    if order_status['status'] != "To Ship":
        return jsonify({"error": f"Invalid Order Status: {order_status['status']}"}), 400

    # 提取所有 var_sku
    var_skus = extract_var_sku(order_details)
    if not var_skus:
        return jsonify({"error": "Failed to get var_sku from order details"}), 400

    results = []
    should_ship = True
    for var_sku in var_skus:
        if not var_sku:  # Skip items without a SKU
            continue

        result = get_stock_item(var_sku)
        if not result:
            results.append({
                "var_sku": var_sku,
                "error": f"No result found for var_sku: {var_sku}"
            })
            should_ship = False
            continue

        # 如果是有限库存，更新库存
        if not result.get('is_unlimited_stock', False):
            update_result = update_stock(var_sku, order_sn)
            if update_result['status'] == 'success':
                stock_item = update_result['item']
                message = result['message'].replace('{result}', stock_item)
                results.append({
                    "var_sku": var_sku,
                    "result": message,
                    "is_unlimited_stock": False,
                    "stock_updated": True
                })
            elif update_result['status'] == 'already_redeemed':
                stock_item = update_result['item']
                message = result['message'].replace('{result}', stock_item)
                results.append({
                    "var_sku": var_sku,
                    "result": message,
                    "is_unlimited_stock": False,
                    "stock_updated": False,
                    "note": "Already redeemed"
                })
                should_ship = False
            elif update_result['status'] == 'out_of_stock':
                results.append({
                    "var_sku": var_sku,
                    "error": "Out of stock",
                    "is_unlimited_stock": False,
                    "stock_updated": False
                })
                should_ship = False
            else:
                results.append({
                    "var_sku": var_sku,
                    "error": "Failed to update stock",
                    "is_unlimited_stock": False,
                    "stock_updated": False
                })
                should_ship = False
        else:
            # 无限库存的情况
            message = result['message'].replace('{result}', 'Unlimited Stock')
            results.append({
                "var_sku": var_sku,
                "result": message,
                "is_unlimited_stock": True
            })

    # 只有当所有商品都成功处理且订单状态为 "To Ship" 时才发货
    if should_ship and config.AUTO_SHIP_ORDER:
        ship_result, ship_status_code = ship_order(order_sn)
        if ship_status_code != 200:
            results.append({
                "error": f"Failed to ship order: {ship_result.get('error', 'Unknown error')}"
            })
        else:
            results.append({
                "message": "Order shipped successfully"
            })
            
            # Check if we should send a message on successful shipping
            if config.SEND_MESSAGE_ON_SHIP:
                # Get order details to retrieve buyer information
                order_details, _ = get_order_details(order_sn)
                buyer_username = order_details['data']['buyer_user'].get('user_name', 'Valued Customer')
                
                # Prepare the message
                message = config.SHIP_SUCCESS_MESSAGE_TEMPLATE.format(
                    buyer_username=buyer_username,
                    order_sn=order_sn
                )
                
                # Unescape HTML entities and preserve newlines
                message = html.unescape(message).replace('\n', '\r\n')
                
                # Send the chat message
                chat_result = send_chat_message({
                    'order_sn': order_sn,
                    'text': message
                })
                
                if isinstance(chat_result, tuple) and len(chat_result) == 2:
                    chat_response, status_code = chat_result
                    if status_code == 200:
                        results.append({
                            "message": "Shipping notification sent to buyer"
                        })
                    else:
                        results.append({
                            "error": f"Failed to send shipping notification: {chat_response.get('error', 'Unknown error')}"
                        })
                else:
                    results.append({
                        "error": "Unexpected response from send_chat_message"
                    })

    return jsonify({
        "status": "Processed",
        "results": results
    })

def load_manual_orders():
    try:
        with open(MANUAL_ORDERS_FILE, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

@order_bp.route('/get_netflix_signin_code', methods=['POST'])
def api_get_netflix_signin_code():
    data = request.json
    order_sn = data.get('order_sn')

    if not order_sn:
        return jsonify({"error": "order_sn are required"}), 400

    result, status_code = get_netflix_signin_code(order_sn)
    return jsonify(result), status_code

@order_bp.route('/get_netflix_signin_code_status/<order_sn>', methods=['GET'])
def api_get_netflix_signin_code_status(order_sn):
    result, status_code = get_netflix_signin_code_status(order_sn)
    return jsonify(result), status_code

@order_bp.route('/create_fake_order', methods=['POST'])
def api_create_fake_order():
    """
    Create a fake order for testing purposes.

    Expected payload:
    {
        "order_sn": "250612BWY37BUV",
        "var_sku": "canva_30",
        "buyer_username": "test_user",
        "status": "To Ship"  # optional, defaults to "To Ship"
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify({"error": "JSON payload is required"}), 400

        # Validate required fields
        required_fields = ['order_sn', 'var_sku']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400

        order_sn = data['order_sn']
        var_sku = data['var_sku']
        buyer_username = data.get('buyer_username', 'test_user')
        status = data.get('status', 'To Ship')

        # Check if order already exists
        manual_orders = load_manual_orders()
        existing_order = next((order for order in manual_orders if order['order_sn'] == order_sn), None)
        if existing_order:
            return jsonify({"error": f"Order {order_sn} already exists"}), 400

        # Create fake order
        fake_order = {
            "order_sn": order_sn,
            "var_sku": var_sku,
            "buyer_username": buyer_username,
            "status": status,
            "created_at": json.dumps(datetime.datetime.now(), default=str),
            "is_fake_order": True,
            "order_type": "fake_test_order"
        }

        # Add to manual orders
        manual_orders.append(fake_order)

        # Save to file
        with open(MANUAL_ORDERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(manual_orders, f, indent=2, ensure_ascii=False)

        return jsonify({
            "success": True,
            "message": f"Fake order {order_sn} created successfully",
            "order": fake_order
        }), 201

    except Exception as e:
        return jsonify({"error": f"Failed to create fake order: {str(e)}"}), 500

@order_bp.route('/list_fake_orders', methods=['GET'])
def api_list_fake_orders():
    """List all fake orders"""
    try:
        manual_orders = load_manual_orders()
        fake_orders = [order for order in manual_orders if order.get('is_fake_order', False)]

        return jsonify({
            "success": True,
            "fake_orders": fake_orders,
            "count": len(fake_orders)
        }), 200

    except Exception as e:
        return jsonify({"error": f"Failed to list fake orders: {str(e)}"}), 500

@order_bp.route('/delete_fake_order/<order_sn>', methods=['DELETE'])
def api_delete_fake_order(order_sn):
    """Delete a fake order"""
    try:
        manual_orders = load_manual_orders()

        # Find and remove the fake order
        original_count = len(manual_orders)
        manual_orders = [order for order in manual_orders
                        if not (order['order_sn'] == order_sn and order.get('is_fake_order', False))]

        if len(manual_orders) == original_count:
            return jsonify({"error": f"Fake order {order_sn} not found"}), 404

        # Save updated list
        with open(MANUAL_ORDERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(manual_orders, f, indent=2, ensure_ascii=False)

        return jsonify({
            "success": True,
            "message": f"Fake order {order_sn} deleted successfully"
        }), 200

    except Exception as e:
        return jsonify({"error": f"Failed to delete fake order: {str(e)}"}), 500