{"v": {"command": "vv", "description": "Generate a VPN configuration", "response_text": "🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️", "image_urls": [], "required_params": ["server", "days", "telco", "plan"], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "vlist": {"command": "vvlist", "description": "List available VPN servers and configurations", "response_text": "📋 Available VPN Servers & Configurations", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "vuser": {"command": "vvuser", "description": "View VPN configurations for a user", "response_text": "👤 User VPN Configurations", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "vdel": {"command": "vvdel", "description": "Delete a VPN configuration", "response_text": "🗑️ VPN Configuration Deleted", "image_urls": [], "required_params": ["client_id"], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "vrenew": {"command": "vvrenew", "description": "Renew/extend a VPN configuration", "response_text": "🔄 VPN Configuration Renewed", "image_urls": [], "required_params": ["client_id", "days"], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "vtest": {"command": "vvtest", "description": "Test VPN API connectivity", "response_text": "🔧 VPN API Test", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "vservers": {"command": "vvservers", "description": "List all VPN servers with their IDs", "response_text": "🖥️ Available VPN Servers", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "vhelp": {"command": "vvhelp", "description": "Show help for all VPN commands", "response_text": "📚 VPN Commands Help", "image_urls": [], "required_params": [], "enabled": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}