"""
Steam Inventory Service
Handles Steam inventory and stock management
"""

import json
import os
import logging
import threading
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class SteamInventoryService:
    """Service for managing Steam inventory and stock"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.inventory_config = config.get('inventory_config', {})
        self.stock_file = self.inventory_config.get('stock_file', 'data/steam_stock.json')
        self.auto_update = self.inventory_config.get('auto_update', True)
        self.update_interval = self.inventory_config.get('update_interval', 300)
        
        self.stock_data = {}
        self.lock = threading.Lock()
        self.update_thread = None
        self.running = False
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.stock_file), exist_ok=True)
        
        # Load initial stock data
        self.load_stock_data()
        
        # Start auto-update if enabled
        if self.auto_update:
            self.start_auto_update()
            
    def update_config(self, config: Dict[str, Any]):
        """Update service configuration"""
        self.config = config
        self.inventory_config = config.get('inventory_config', {})
        
        # Update file path if changed
        new_stock_file = self.inventory_config.get('stock_file', 'data/steam_stock.json')
        if new_stock_file != self.stock_file:
            self.stock_file = new_stock_file
            os.makedirs(os.path.dirname(self.stock_file), exist_ok=True)
            self.load_stock_data()
            
    def load_stock_data(self):
        """Load stock data from file"""
        with self.lock:
            try:
                if os.path.exists(self.stock_file):
                    with open(self.stock_file, 'r', encoding='utf-8') as f:
                        self.stock_data = json.load(f)
                    logger.info(f"Loaded stock data from {self.stock_file}")
                else:
                    self.stock_data = {}
                    self.save_stock_data()
                    logger.info(f"Created new stock file: {self.stock_file}")
            except Exception as e:
                logger.error(f"Error loading stock data: {e}")
                self.stock_data = {}

    def save_stock_data(self):
        """Save stock data to file"""
        try:
            with open(self.stock_file, 'w', encoding='utf-8') as f:
                json.dump(self.stock_data, f, indent=2, ensure_ascii=False)
            logger.debug("Stock data saved successfully")
        except Exception as e:
            logger.error(f"Error saving stock data: {e}")
            
    def get_stock_item(self, sku: str) -> Optional[Dict[str, Any]]:
        """Get stock item by SKU"""
        with self.lock:
            return self.stock_data.get(sku)
            
    def update_stock(self, sku: str, quantity: int, operation: str = 'set') -> bool:
        """Update stock quantity for an item"""
        with self.lock:
            try:
                if sku not in self.stock_data:
                    self.stock_data[sku] = {
                        'sku': sku,
                        'quantity': 0,
                        'reserved': 0,
                        'last_updated': datetime.now().isoformat(),
                        'history': []
                    }
                    
                item = self.stock_data[sku]
                old_quantity = item['quantity']
                
                if operation == 'set':
                    item['quantity'] = quantity
                elif operation == 'add':
                    item['quantity'] += quantity
                elif operation == 'subtract':
                    item['quantity'] = max(0, item['quantity'] - quantity)
                else:
                    logger.error(f"Invalid operation: {operation}")
                    return False
                    
                # Update metadata
                item['last_updated'] = datetime.now().isoformat()
                item['history'].append({
                    'timestamp': datetime.now().isoformat(),
                    'operation': operation,
                    'old_quantity': old_quantity,
                    'new_quantity': item['quantity'],
                    'change': quantity
                })
                
                # Keep only last 100 history entries
                if len(item['history']) > 100:
                    item['history'] = item['history'][-100:]
                    
                self.save_stock_data()
                logger.info(f"Updated stock for {sku}: {old_quantity} -> {item['quantity']}")
                return True
                
            except Exception as e:
                logger.error(f"Error updating stock for {sku}: {e}")
                return False
                
    def reserve_stock(self, sku: str, quantity: int) -> bool:
        """Reserve stock for an order"""
        with self.lock:
            try:
                if sku not in self.stock_data:
                    logger.error(f"SKU {sku} not found in stock")
                    return False
                    
                item = self.stock_data[sku]
                available = item['quantity'] - item.get('reserved', 0)
                
                if available < quantity:
                    logger.warning(f"Insufficient stock for {sku}: available={available}, requested={quantity}")
                    return False
                    
                item['reserved'] = item.get('reserved', 0) + quantity
                self.save_stock_data()
                logger.info(f"Reserved {quantity} units of {sku}")
                return True
                
            except Exception as e:
                logger.error(f"Error reserving stock for {sku}: {e}")
                return False
                
    def release_reservation(self, sku: str, quantity: int) -> bool:
        """Release reserved stock"""
        with self.lock:
            try:
                if sku not in self.stock_data:
                    logger.error(f"SKU {sku} not found in stock")
                    return False
                    
                item = self.stock_data[sku]
                item['reserved'] = max(0, item.get('reserved', 0) - quantity)
                self.save_stock_data()
                logger.info(f"Released {quantity} reserved units of {sku}")
                return True
                
            except Exception as e:
                logger.error(f"Error releasing reservation for {sku}: {e}")
                return False
                
    def get_all_stock(self) -> Dict[str, Any]:
        """Get all stock data"""
        with self.lock:
            return self.stock_data.copy()
            
    def get_item_count(self) -> int:
        """Get total number of items in inventory"""
        with self.lock:
            return len(self.stock_data)
            
    def get_low_stock_items(self, threshold: int = 5) -> List[Dict[str, Any]]:
        """Get items with low stock"""
        with self.lock:
            low_stock = []
            for sku, item in self.stock_data.items():
                available = item['quantity'] - item.get('reserved', 0)
                if available <= threshold:
                    low_stock.append({
                        'sku': sku,
                        'available': available,
                        'quantity': item['quantity'],
                        'reserved': item.get('reserved', 0)
                    })
            return low_stock
            
    def start_auto_update(self):
        """Start automatic inventory updates"""
        if self.running:
            return
            
        self.running = True
        self.update_thread = threading.Thread(target=self._auto_update_worker, daemon=True)
        self.update_thread.start()
        logger.info("Started automatic inventory updates")
        
    def stop_auto_update(self):
        """Stop automatic inventory updates"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("Stopped automatic inventory updates")
        
    def _auto_update_worker(self):
        """Worker thread for automatic updates"""
        while self.running:
            try:
                # Perform periodic maintenance tasks
                self._cleanup_old_history()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in auto-update worker: {e}")
                time.sleep(60)  # Wait a minute before retrying
                
    def _cleanup_old_history(self):
        """Clean up old history entries"""
        with self.lock:
            for item in self.stock_data.values():
                if 'history' in item and len(item['history']) > 100:
                    item['history'] = item['history'][-100:]
                    
    def add_new_item(self, sku: str, name: str, initial_quantity: int = 0) -> bool:
        """Add a new item to inventory"""
        with self.lock:
            try:
                if sku in self.stock_data:
                    logger.warning(f"Item {sku} already exists in inventory")
                    return False

                self.stock_data[sku] = {
                    'sku': sku,
                    'name': name,
                    'quantity': initial_quantity,
                    'reserved': 0,
                    'created': datetime.now().isoformat(),
                    'last_updated': datetime.now().isoformat(),
                    'history': [{
                        'timestamp': datetime.now().isoformat(),
                        'operation': 'create',
                        'old_quantity': 0,
                        'new_quantity': initial_quantity,
                        'change': initial_quantity
                    }]
                }

                self.save_stock_data()
                logger.info(f"Added new item to inventory: {sku} ({name}) with quantity {initial_quantity}")
                return True

            except Exception as e:
                logger.error(f"Error adding new item {sku}: {e}")
                return False

    def remove_item(self, sku: str) -> bool:
        """Remove an item from inventory"""
        with self.lock:
            try:
                if sku not in self.stock_data:
                    logger.warning(f"Item {sku} not found in inventory")
                    return False

                del self.stock_data[sku]
                self.save_stock_data()
                logger.info(f"Removed item from inventory: {sku}")
                return True

            except Exception as e:
                logger.error(f"Error removing item {sku}: {e}")
                return False

    def cleanup(self):
        """Cleanup service resources"""
        self.stop_auto_update()
        self.save_stock_data()
        logger.info("Steam inventory service cleanup completed")
