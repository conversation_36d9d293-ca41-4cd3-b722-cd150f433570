{% extends "base.html" %}

{% block title %}Universal Fake Order System - SteamCodeTool{% endblock %}

{% block header %}🧪 Universal Fake Order System{% endblock %}

{% block extra_head %}
<style>
    /* Multi-step form styles */
    .step-container {
        display: none;
    }
    .step-container.active {
        display: block;
    }
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }
    .step {
        display: flex;
        align-items: center;
        margin: 0 10px;
    }
    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 8px;
    }
    .step.active .step-number {
        background-color: #007bff;
        color: white;
    }
    .step.completed .step-number {
        background-color: #28a745;
        color: white;
    }
    .step-line {
        width: 50px;
        height: 2px;
        background-color: #e9ecef;
        margin: 0 10px;
    }
    .step.completed + .step-line {
        background-color: #28a745;
    }

    /* Form styling */
    .form-section {
        background: white;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .form-section h3 {
        margin-top: 0;
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
    }
    
    /* Template selection */
    .template-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    .template-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .template-card:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0,123,255,0.2);
    }
    .template-card.selected {
        border-color: #007bff;
        background-color: #f8f9ff;
    }
    .template-category {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: bold;
    }
    .template-name {
        font-weight: bold;
        margin: 5px 0;
    }
    .template-price {
        color: #28a745;
        font-weight: bold;
    }

    /* Real-time validation */
    .field-validation {
        margin-top: 5px;
        font-size: 14px;
    }
    .validation-success {
        color: #28a745;
    }
    .validation-error {
        color: #dc3545;
    }
    .validation-warning {
        color: #ffc107;
    }

    /* SKU suggestions */
    .sku-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }
    .sku-suggestion {
        padding: 10px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
    }
    .sku-suggestion:hover {
        background-color: #f8f9fa;
    }
    .sku-suggestion:last-child {
        border-bottom: none;
    }

    /* Navigation buttons */
    .form-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    /* Tabs for different modes */
    .mode-tabs {
        display: flex;
        margin-bottom: 30px;
        border-bottom: 2px solid #e9ecef;
    }
    .mode-tab {
        padding: 12px 24px;
        cursor: pointer;
        border-bottom: 3px solid transparent;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .mode-tab:hover {
        background-color: #f8f9fa;
    }
    .mode-tab.active {
        border-bottom-color: #007bff;
        color: #007bff;
    }

    /* Content sections */
    .content-section {
        display: none;
    }
    .content-section.active {
        display: block;
    }

    /* Batch interface styles */
    .batch-upload-area {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        margin: 20px 0;
        transition: all 0.3s ease;
    }
    .batch-upload-area:hover {
        border-color: #007bff;
        background-color: #f8f9ff;
    }
    .batch-upload-area.dragover {
        border-color: #007bff;
        background-color: #e3f2fd;
    }

    /* Management dashboard styles */
    .filter-bar {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .order-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
    }
    .order-card.fake-order {
        border-left-color: #ffc107;
    }
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .order-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    .status-to-ship { background-color: #e3f2fd; color: #1976d2; }
    .status-shipped { background-color: #f3e5f5; color: #7b1fa2; }
    .status-completed { background-color: #e8f5e8; color: #388e3c; }
    .status-unpaid { background-color: #fff3e0; color: #f57c00; }

    /* Processing interface */
    .processing-panel {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
    }
    .processing-result {
        margin-top: 15px;
        padding: 15px;
        border-radius: 5px;
    }
    .result-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .result-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .template-grid {
            grid-template-columns: 1fr;
        }
        .step-indicator {
            flex-direction: column;
        }
        .step-line {
            width: 2px;
            height: 30px;
            margin: 10px 0;
        }
        .form-navigation {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4">
    <div id="alertContainer"></div>

    <!-- Mode Tabs -->
    <div class="mode-tabs">
        <div class="mode-tab active" data-mode="single">
            <i class="fas fa-plus-circle mr-2"></i>Single Order
        </div>
        <div class="mode-tab" data-mode="batch">
            <i class="fas fa-layer-group mr-2"></i>Batch Orders
        </div>
        <div class="mode-tab" data-mode="management">
            <i class="fas fa-list-ul mr-2"></i>Order Management
        </div>
        <div class="mode-tab" data-mode="testing">
            <i class="fas fa-flask mr-2"></i>Testing Interface
        </div>
    </div>

    <!-- Single Order Creation -->
    <div id="single-mode" class="content-section active">
        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <span>Product Selection</span>
            </div>
            <div class="step-line"></div>
            <div class="step" data-step="2">
                <div class="step-number">2</div>
                <span>Configuration</span>
            </div>
            <div class="step-line"></div>
            <div class="step" data-step="3">
                <div class="step-number">3</div>
                <span>Customer Info</span>
            </div>
            <div class="step-line"></div>
            <div class="step" data-step="4">
                <div class="step-number">4</div>
                <span>Review & Create</span>
            </div>
        </div>

        <form id="advancedFakeOrderForm">
            <!-- Step 1: Product Selection -->
            <div class="step-container active" data-step="1">
                <div class="form-section">
                    <h3><i class="fas fa-box mr-2"></i>Product Selection</h3>
                    
                    <!-- Template Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Choose Product Template:</label>
                        <div id="templateGrid" class="template-grid">
                            <!-- Templates will be loaded here -->
                        </div>
                    </div>

                    <!-- Custom SKU Input -->
                    <div class="mb-4">
                        <label for="customSku" class="block text-sm font-medium text-gray-700 mb-2">Or Enter Custom SKU:</label>
                        <div class="relative">
                            <input type="text" id="customSku" name="custom_sku" 
                                placeholder="Type SKU for suggestions..." 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <div id="skuSuggestions" class="sku-suggestions"></div>
                        </div>
                        <div id="skuValidation" class="field-validation"></div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Product Configuration -->
            <div class="step-container" data-step="2">
                <div class="form-section">
                    <h3><i class="fas fa-cog mr-2"></i>Product Configuration</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">Quantity:</label>
                            <input type="number" id="quantity" name="quantity" value="1" min="1" max="10"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label for="priceOverride" class="block text-sm font-medium text-gray-700 mb-2">Price Override (Optional):</label>
                            <input type="number" id="priceOverride" name="price_override" step="0.01" min="0"
                                placeholder="Leave empty for default price"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                    </div>

                    <!-- Product-specific metadata fields will be populated here -->
                    <div id="productMetadataFields" class="mt-6"></div>
                </div>
            </div>

            <!-- Step 3: Customer Information -->
            <div class="step-container" data-step="3">
                <div class="form-section">
                    <h3><i class="fas fa-user mr-2"></i>Customer Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="buyerUsername" class="block text-sm font-medium text-gray-700 mb-2">Username:</label>
                            <input type="text" id="buyerUsername" name="buyer_username" value="test_user"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label for="buyerName" class="block text-sm font-medium text-gray-700 mb-2">Full Name:</label>
                            <input type="text" id="buyerName" name="buyer_name" value="Test Customer"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label for="buyerPhone" class="block text-sm font-medium text-gray-700 mb-2">Phone:</label>
                            <input type="tel" id="buyerPhone" name="buyer_phone" value="+60123456789"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label for="buyerEmail" class="block text-sm font-medium text-gray-700 mb-2">Email:</label>
                            <input type="email" id="buyerEmail" name="buyer_email" value="<EMAIL>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label for="buyerAddress" class="block text-sm font-medium text-gray-700 mb-2">Address:</label>
                        <textarea id="buyerAddress" name="buyer_address" rows="3" 
                            placeholder="123 Test Street, Test City, Test State"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    </div>
                </div>
            </div>

            <!-- Step 4: Review & Create -->
            <div class="step-container" data-step="4">
                <div class="form-section">
                    <h3><i class="fas fa-check-circle mr-2"></i>Review & Create Order</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="orderSn" class="block text-sm font-medium text-gray-700 mb-2">Order SN (Optional):</label>
                            <input type="text" id="orderSn" name="order_sn" 
                                placeholder="Leave empty to auto-generate"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label for="orderStatus" class="block text-sm font-medium text-gray-700 mb-2">Order Status:</label>
                            <select id="orderStatus" name="order_status"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="To Ship">To Ship</option>
                                <option value="Shipped">Shipped</option>
                                <option value="Completed">Completed</option>
                                <option value="Unpaid">Unpaid</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="paymentStatus" class="block text-sm font-medium text-gray-700 mb-2">Payment Status:</label>
                            <select id="paymentStatus" name="payment_status"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="paid">Paid</option>
                                <option value="pending">Pending</option>
                                <option value="failed">Failed</option>
                                <option value="refunded">Refunded</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="testScenario" class="block text-sm font-medium text-gray-700 mb-2">Test Scenario:</label>
                            <input type="text" id="testScenario" name="test_scenario" value="general_testing"
                                placeholder="e.g., canva_pro_testing"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                    </div>

                    <!-- Order Preview -->
                    <div id="orderPreview" class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="font-semibold mb-3">Order Preview:</h4>
                        <div id="previewContent">
                            <!-- Preview will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="form-navigation">
                <button type="button" id="prevBtn" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200" style="display: none;">
                    <i class="fas fa-arrow-left mr-2"></i>Previous
                </button>
                <div class="flex space-x-3">
                    <button type="button" id="nextBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded transition duration-200">
                        Next<i class="fas fa-arrow-right ml-2"></i>
                    </button>
                    <button type="submit" id="createBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded transition duration-200" style="display: none;">
                        <i class="fas fa-plus mr-2"></i>Create Order
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Batch Order Creation -->
    <div id="batch-mode" class="content-section">
        <div class="form-section">
            <h3><i class="fas fa-layer-group mr-2"></i>Batch Order Creation</h3>
            
            <!-- Quick Actions -->
            <div class="mb-6">
                <div class="flex flex-wrap gap-3">
                    <button type="button" id="downloadTemplateBtn" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-download mr-2"></i>Download CSV Template
                    </button>
                    <button type="button" id="createSampleBatchBtn" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-magic mr-2"></i>Create Sample Batch
                    </button>
                    <button type="button" id="clearBatchBtn" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-trash mr-2"></i>Clear Batch
                    </button>
                </div>
            </div>

            <!-- CSV Upload Area -->
            <div class="batch-upload-area" id="csvUploadArea">
                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                <h4 class="text-lg font-semibold mb-2">Upload CSV File</h4>
                <p class="text-gray-600 mb-4">Drag and drop your CSV file here, or click to browse</p>
                <div class="text-sm text-gray-500 mb-4">
                    <strong>Required columns:</strong> var_sku, buyer_username<br>
                    <strong>Optional columns:</strong> buyer_name, buyer_phone, buyer_email, quantity, status, payment_status, test_scenario
                </div>
                <input type="file" id="csvFileInput" accept=".csv" class="hidden">
                <button type="button" onclick="document.getElementById('csvFileInput').click()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                    Choose File
                </button>
            </div>

            <!-- Manual Batch Creation -->
            <div class="mt-6">
                <h4 class="font-semibold mb-3">Or Create Batch Manually:</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Product SKU:</label>
                            <select id="batchSku" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">Select SKU</option>
                                <!-- Will be populated with available SKUs -->
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Number of Orders:</label>
                            <input type="number" id="batchCount" min="1" max="50" value="5"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div class="flex items-end">
                            <button type="button" id="addToBatchBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                                <i class="fas fa-plus mr-2"></i>Add to Batch
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Batch Configuration Templates -->
            <div class="mt-6">
                <h4 class="font-semibold mb-3">Batch Configuration Templates:</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button type="button" class="batch-template-btn bg-blue-100 hover:bg-blue-200 border border-blue-300 rounded-lg p-4 text-left transition duration-200" data-template="mixed_products">
                        <div class="font-semibold text-blue-800">Mixed Products</div>
                        <div class="text-sm text-blue-600">5 orders with different product types</div>
                    </button>
                    <button type="button" class="batch-template-btn bg-green-100 hover:bg-green-200 border border-green-300 rounded-lg p-4 text-left transition duration-200" data-template="canva_bulk">
                        <div class="font-semibold text-green-800">Canva Bulk Test</div>
                        <div class="text-sm text-green-600">10 Canva Pro orders for bulk testing</div>
                    </button>
                    <button type="button" class="batch-template-btn bg-purple-100 hover:bg-purple-200 border border-purple-300 rounded-lg p-4 text-left transition duration-200" data-template="error_testing">
                        <div class="font-semibold text-purple-800">Error Testing</div>
                        <div class="text-sm text-purple-600">Orders with invalid data for error testing</div>
                    </button>
                </div>
            </div>

            <!-- Batch Configuration -->
            <div class="mt-6">
                <h4 class="font-semibold mb-3">Batch Processing Configuration:</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="continueOnError" checked class="mr-2">
                            Continue on Error
                        </label>
                        <div class="text-xs text-gray-500 mt-1">Process remaining orders if some fail</div>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="generateUniqueOrderSns" checked class="mr-2">
                            Generate Unique Order SNs
                        </label>
                        <div class="text-xs text-gray-500 mt-1">Auto-generate unique order numbers</div>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="validateBeforeProcessing" checked class="mr-2">
                            Validate Before Processing
                        </label>
                        <div class="text-xs text-gray-500 mt-1">Check all orders before creating</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Batch Size:</label>
                        <select id="batchSize" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="5">5 orders per batch</option>
                            <option value="10" selected>10 orders per batch</option>
                            <option value="25">25 orders per batch</option>
                            <option value="50">50 orders per batch</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Test Scenario:</label>
                    <input type="text" id="defaultTestScenario" placeholder="e.g., batch_testing_2025" value="batch_testing"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
            </div>

            <!-- Batch Preview -->
            <div id="batchPreview" class="mt-6" style="display: none;">
                <div class="flex justify-between items-center mb-3">
                    <h4 class="font-semibold">Batch Preview:</h4>
                    <div class="flex space-x-2">
                        <button type="button" id="editBatchBtn" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-sm transition duration-200">
                            <i class="fas fa-edit mr-1"></i>Edit
                        </button>
                        <button type="button" id="validateBatchBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm transition duration-200">
                            <i class="fas fa-check mr-1"></i>Validate
                        </button>
                    </div>
                </div>
                <div id="batchPreviewContent" class="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                    <!-- Preview content will be populated here -->
                </div>
                <div class="mt-4 flex justify-between items-center">
                    <div class="text-sm text-gray-600">
                        <span id="batchOrderCount">0</span> orders ready for processing
                    </div>
                    <div class="flex space-x-2">
                        <button type="button" id="saveBatchTemplateBtn" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                            <i class="fas fa-save mr-2"></i>Save as Template
                        </button>
                        <button type="button" id="processBatchBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded transition duration-200">
                            <i class="fas fa-play mr-2"></i>Process Batch
                        </button>
                    </div>
                </div>
            </div>

            <!-- Batch Validation Results -->
            <div id="batchValidation" class="mt-6" style="display: none;">
                <h4 class="font-semibold mb-3">Validation Results:</h4>
                <div id="batchValidationContent" class="bg-white p-4 rounded-lg border">
                    <!-- Validation results will be populated here -->
                </div>
            </div>

            <!-- Progress Tracking -->
            <div id="batchProgress" class="mt-6" style="display: none;">
                <h4 class="font-semibold mb-3">Processing Progress:</h4>
                <div class="bg-white p-4 rounded-lg border">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium">Progress</span>
                        <span class="text-sm text-gray-600" id="progressPercentage">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
                        <div id="progressBar" class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="progressText" class="text-sm text-gray-600 mb-4">Preparing batch...</div>
                    
                    <!-- Real-time status -->
                    <div class="grid grid-cols-3 gap-4 text-center mb-4">
                        <div class="bg-blue-100 p-3 rounded">
                            <div class="text-lg font-bold text-blue-800" id="processedCount">0</div>
                            <div class="text-xs text-blue-600">Processed</div>
                        </div>
                        <div class="bg-green-100 p-3 rounded">
                            <div class="text-lg font-bold text-green-800" id="successCount">0</div>
                            <div class="text-xs text-green-600">Successful</div>
                        </div>
                        <div class="bg-red-100 p-3 rounded">
                            <div class="text-lg font-bold text-red-800" id="errorCount">0</div>
                            <div class="text-xs text-red-600">Errors</div>
                        </div>
                    </div>
                    
                    <div class="flex justify-between">
                        <button type="button" id="pauseBatchBtn" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-sm transition duration-200" style="display: none;">
                            <i class="fas fa-pause mr-1"></i>Pause
                        </button>
                        <button type="button" id="cancelBatchBtn" class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-sm transition duration-200">
                            <i class="fas fa-stop mr-1"></i>Cancel
                        </button>
                    </div>
                </div>
                
                <!-- Detailed Results -->
                <div id="batchResults" class="mt-4"></div>
            </div>
        </div>
    </div>

    <!-- Order Management Dashboard -->
    <div id="management-mode" class="content-section">
        <div class="form-section">
            <h3><i class="fas fa-list-ul mr-2"></i>Fake Order Management</h3>
            
            <!-- Filter Bar -->
            <div class="filter-bar">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <input type="text" id="searchFilter" placeholder="Search orders..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <select id="statusFilter"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">All Statuses</option>
                            <option value="To Ship">To Ship</option>
                            <option value="Shipped">Shipped</option>
                            <option value="Completed">Completed</option>
                            <option value="Unpaid">Unpaid</option>
                        </select>
                    </div>
                    <div>
                        <select id="skuFilter"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">All Products</option>
                            <!-- Will be populated with available SKUs -->
                        </select>
                    </div>
                    <div>
                        <button type="button" id="applyFiltersBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                            <i class="fas fa-filter mr-2"></i>Apply Filters
                        </button>
                    </div>
                </div>
                
                <div class="mt-4 flex justify-between items-center">
                    <div class="flex space-x-2">
                        <button type="button" id="selectAllBtn" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-1 px-3 rounded text-sm transition duration-200">
                            Select All
                        </button>
                        <button type="button" id="bulkDeleteBtn" class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-sm transition duration-200">
                            <i class="fas fa-trash mr-1"></i>Bulk Delete
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        <span id="orderCount">0</span> orders found
                    </div>
                </div>
            </div>

            <!-- Orders List -->
            <div id="ordersList" class="mt-6">
                <!-- Orders will be populated here -->
            </div>

            <!-- Pagination -->
            <div id="pagination" class="mt-6 flex justify-center">
                <!-- Pagination will be populated here -->
            </div>
        </div>
    </div>

    <!-- Testing Interface -->
    <div id="testing-mode" class="content-section">
        <div class="form-section">
            <h3><i class="fas fa-flask mr-2"></i>Order Testing & Processing Interface</h3>
            
            <!-- Quick Test Scenarios -->
            <div class="mb-6">
                <h4 class="font-semibold mb-3">Quick Test Scenarios:</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button type="button" class="scenario-btn bg-blue-100 hover:bg-blue-200 border border-blue-300 rounded-lg p-4 text-left transition duration-200" data-scenario="canva_basic">
                        <div class="font-semibold text-blue-800">Canva Basic Test</div>
                        <div class="text-sm text-blue-600">Test Canva Pro 30-day order processing</div>
                    </button>
                    <button type="button" class="scenario-btn bg-green-100 hover:bg-green-200 border border-green-300 rounded-lg p-4 text-left transition duration-200" data-scenario="netflix_premium">
                        <div class="font-semibold text-green-800">Netflix Premium Test</div>
                        <div class="text-sm text-green-600">Test Netflix Premium account delivery</div>
                    </button>
                    <button type="button" class="scenario-btn bg-purple-100 hover:bg-purple-200 border border-purple-300 rounded-lg p-4 text-left transition duration-200" data-scenario="steam_auth">
                        <div class="font-semibold text-purple-800">Steam Auth Test</div>
                        <div class="text-sm text-purple-600">Test Steam authentication code flow</div>
                    </button>
                </div>
            </div>

            <!-- Processing Panel -->
            <div class="processing-panel">
                <h4 class="font-semibold mb-3">Order Processing:</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="testOrderSn" class="block text-sm font-medium text-gray-700 mb-2">Order SN to Test:</label>
                        <input type="text" id="testOrderSn" placeholder="Enter order SN or select from list"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div class="flex items-end">
                        <button type="button" id="processOrderBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded transition duration-200">
                            <i class="fas fa-play mr-2"></i>Process Order
                        </button>
                    </div>
                </div>

                <!-- Processing Results -->
                <div id="processingResults" class="mt-4" style="display: none;">
                    <!-- Results will be populated here -->
                </div>
            </div>

            <!-- Validation Tools -->
            <div class="mt-6">
                <h4 class="font-semibold mb-3">Validation Tools:</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button type="button" id="validateAllBtn" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-check-double mr-2"></i>Validate All Orders
                    </button>
                    <button type="button" id="cleanupOldBtn" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-broom mr-2"></i>Cleanup Old Orders
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Global state management
    let currentStep = 1;
    let currentMode = 'single';
    let availableTemplates = {};
    let selectedTemplate = null;
    let fakeOrders = [];
    let currentPage = 1;
    const ordersPerPage = 10;

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeUI();
        loadTemplates();
        loadFakeOrders();
        setupEventListeners();
    });

    function initializeUI() {
        // Mode tab switching
        document.querySelectorAll('.mode-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                switchMode(this.dataset.mode);
            });
        });

        // Step navigation
        document.getElementById('nextBtn').addEventListener('click', nextStep);
        document.getElementById('prevBtn').addEventListener('click', prevStep);
        
        // Form submission
        document.getElementById('advancedFakeOrderForm').addEventListener('submit', handleAdvancedFormSubmit);
    }

    function setupEventListeners() {
        // Custom SKU input with suggestions
        const customSkuInput = document.getElementById('customSku');
        customSkuInput.addEventListener('input', handleSkuInput);
        customSkuInput.addEventListener('focus', showSkuSuggestions);
        customSkuInput.addEventListener('blur', hideSkuSuggestions);

        // Batch processing
        document.getElementById('csvFileInput').addEventListener('change', handleCsvUpload);
        document.getElementById('processBatchBtn').addEventListener('click', processBatch);
        setupBatchEventListeners();

        // Management filters
        document.getElementById('applyFiltersBtn').addEventListener('click', applyFilters);
        document.getElementById('selectAllBtn').addEventListener('click', selectAllOrders);
        document.getElementById('bulkDeleteBtn').addEventListener('click', bulkDeleteOrders);

        // Testing interface
        document.querySelectorAll('.scenario-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                runTestScenario(this.dataset.scenario);
            });
        });
        document.getElementById('processOrderBtn').addEventListener('click', processTestOrder);
        document.getElementById('validateAllBtn').addEventListener('click', validateAllOrders);
        document.getElementById('cleanupOldBtn').addEventListener('click', cleanupOldOrders);

        // Drag and drop for CSV
        const uploadArea = document.getElementById('csvUploadArea');
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
    }

    // Mode switching
    function switchMode(mode) {
        currentMode = mode;
        
        // Update tab appearance
        document.querySelectorAll('.mode-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-mode="${mode}"]`).classList.add('active');
        
        // Show/hide content sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${mode}-mode`).classList.add('active');
        
        // Load mode-specific data
        if (mode === 'management') {
            loadOrderManagement();
        } else if (mode === 'testing') {
            loadTestingInterface();
        }
    }

    // Step navigation for single order form
    function nextStep() {
        if (validateCurrentStep()) {
            if (currentStep < 4) {
                currentStep++;
                updateStepDisplay();
                updateStepContent();
            }
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
            updateStepContent();
        }
    }

    function updateStepDisplay() {
        // Update step indicator
        document.querySelectorAll('.step').forEach((step, index) => {
            const stepNum = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNum === currentStep) {
                step.classList.add('active');
            } else if (stepNum < currentStep) {
                step.classList.add('completed');
            }
        });

        // Show/hide step containers
        document.querySelectorAll('.step-container').forEach((container, index) => {
            const stepNum = index + 1;
            container.classList.remove('active');
            if (stepNum === currentStep) {
                container.classList.add('active');
            }
        });

        // Update navigation buttons
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const createBtn = document.getElementById('createBtn');

        prevBtn.style.display = currentStep > 1 ? 'block' : 'none';
        nextBtn.style.display = currentStep < 4 ? 'block' : 'none';
        createBtn.style.display = currentStep === 4 ? 'block' : 'none';
    }

    function updateStepContent() {
        if (currentStep === 2 && selectedTemplate) {
            generateProductMetadataFields();
        } else if (currentStep === 4) {
            generateOrderPreview();
        }
    }

    function validateCurrentStep() {
        switch (currentStep) {
            case 1:
                return validateProductSelection();
            case 2:
                return validateProductConfiguration();
            case 3:
                return validateCustomerInfo();
            case 4:
                return true; // Final step, no validation needed
            default:
                return true;
        }
    }

    function validateProductSelection() {
        const customSku = document.getElementById('customSku').value;
        if (!selectedTemplate && !customSku) {
            showAlert('Please select a product template or enter a custom SKU', 'warning');
            return false;
        }
        return true;
    }

    function validateProductConfiguration() {
        const quantity = document.getElementById('quantity').value;
        if (!quantity || quantity < 1) {
            showAlert('Please enter a valid quantity', 'warning');
            return false;
        }
        return true;
    }

    function validateCustomerInfo() {
        const requiredFields = ['buyerUsername', 'buyerName', 'buyerPhone', 'buyerEmail'];
        for (const fieldId of requiredFields) {
            const field = document.getElementById(fieldId);
            if (!field.value.trim()) {
                showAlert(`Please fill in the ${field.previousElementSibling.textContent.replace(':', '')}`, 'warning');
                field.focus();
                return false;
            }
        }
        return true;
    }

    // Template management
    // API configuration
    const API_KEY = 'MTYB_OFFICIAL';
    
    // Helper function to make authenticated API calls
    function makeAuthenticatedRequest(url, options = {}) {
        const defaultHeaders = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${API_KEY}`
        };
        
        return fetch(url, {
            ...options,
            headers: {
                ...defaultHeaders,
                ...options.headers
            }
        });
    }

    async function loadTemplates() {
        try {
            const response = await makeAuthenticatedRequest('/api/fake-orders/templates');
            const data = await response.json();
            
            if (data.success) {
                availableTemplates = data.templates;
                renderTemplateGrid();
                populateSkuFilters();
            } else {
                showAlert('Failed to load templates: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error loading templates: ' + error.message, 'danger');
        }
    }

    function renderTemplateGrid() {
        const grid = document.getElementById('templateGrid');
        grid.innerHTML = '';

        Object.values(availableTemplates).forEach(template => {
            const card = document.createElement('div');
            card.className = 'template-card';
            card.dataset.sku = template.var_sku;
            
            card.innerHTML = `
                <div class="template-category">${template.category}</div>
                <div class="template-name">${template.product_name}</div>
                <div class="template-price">$${template.default_price.toFixed(2)}</div>
                <div class="text-sm text-gray-600 mt-2">${template.var_sku}</div>
            `;
            
            card.addEventListener('click', function() {
                selectTemplate(template);
            });
            
            grid.appendChild(card);
        });

        // Also populate batch SKU dropdown
        populateBatchSkuDropdown();
    }

    function populateBatchSkuDropdown() {
        const batchSku = document.getElementById('batchSku');
        if (batchSku) {
            batchSku.innerHTML = '<option value="">Select SKU</option>';
            Object.values(availableTemplates).forEach(template => {
                const option = document.createElement('option');
                option.value = template.var_sku;
                option.textContent = `${template.var_sku} - ${template.product_name}`;
                batchSku.appendChild(option);
            });
        }
    }

    function selectTemplate(template) {
        selectedTemplate = template;
        
        // Update visual selection
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.querySelector(`[data-sku="${template.var_sku}"]`).classList.add('selected');
        
        // Clear custom SKU input
        document.getElementById('customSku').value = '';
        
        // Update price override placeholder
        document.getElementById('priceOverride').placeholder = `Default: $${template.default_price.toFixed(2)}`;
    }

    // SKU suggestions
    function handleSkuInput(event) {
        const input = event.target.value.toLowerCase();
        if (input.length > 0) {
            const suggestions = Object.keys(availableTemplates).filter(sku => 
                sku.toLowerCase().includes(input)
            );
            showSkuSuggestions(suggestions);
        } else {
            hideSkuSuggestions();
        }
    }

    function showSkuSuggestions(suggestions = null) {
        const suggestionsDiv = document.getElementById('skuSuggestions');
        
        if (suggestions === null) {
            suggestions = Object.keys(availableTemplates);
        }
        
        if (suggestions.length > 0) {
            suggestionsDiv.innerHTML = suggestions.slice(0, 10).map(sku => {
                const template = availableTemplates[sku];
                return `
                    <div class="sku-suggestion" data-sku="${sku}">
                        <div class="font-semibold">${sku}</div>
                        <div class="text-sm text-gray-600">${template.product_name} - $${template.default_price.toFixed(2)}</div>
                    </div>
                `;
            }).join('');
            
            // Add click handlers
            suggestionsDiv.querySelectorAll('.sku-suggestion').forEach(suggestion => {
                suggestion.addEventListener('click', function() {
                    const sku = this.dataset.sku;
                    document.getElementById('customSku').value = sku;
                    selectTemplate(availableTemplates[sku]);
                    hideSkuSuggestions();
                });
            });
            
            suggestionsDiv.style.display = 'block';
        } else {
            hideSkuSuggestions();
        }
    }

    function hideSkuSuggestions() {
        setTimeout(() => {
            document.getElementById('skuSuggestions').style.display = 'none';
        }, 200);
    }

    // Product metadata fields generation
    function generateProductMetadataFields() {
        const container = document.getElementById('productMetadataFields');
        container.innerHTML = '';
        
        if (!selectedTemplate) return;
        
        const schema = selectedTemplate.metadata_schema;
        if (!schema || Object.keys(schema).length === 0) return;
        
        container.innerHTML = '<h4 class="font-semibold mb-3">Product-Specific Configuration:</h4>';
        
        const fieldsGrid = document.createElement('div');
        fieldsGrid.className = 'grid grid-cols-1 md:grid-cols-2 gap-4';
        
        Object.entries(schema).forEach(([fieldName, fieldSchema]) => {
            const fieldDiv = document.createElement('div');
            const label = document.createElement('label');
            label.className = 'block text-sm font-medium text-gray-700 mb-2';
            label.textContent = fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + ':';
            
            let input;
            if (fieldSchema.allowed_values) {
                input = document.createElement('select');
                input.innerHTML = '<option value="">Select...</option>';
                fieldSchema.allowed_values.forEach(value => {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = value;
                    input.appendChild(option);
                });
            } else if (fieldSchema.type === 'int') {
                input = document.createElement('input');
                input.type = 'number';
                if (fieldSchema.min) input.min = fieldSchema.min;
                if (fieldSchema.max) input.max = fieldSchema.max;
            } else {
                input = document.createElement('input');
                input.type = 'text';
            }
            
            input.name = `metadata_${fieldName}`;
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500';
            
            // Set default value from sample data
            if (selectedTemplate.sample_data && selectedTemplate.sample_data[fieldName]) {
                input.value = selectedTemplate.sample_data[fieldName];
            }
            
            fieldDiv.appendChild(label);
            fieldDiv.appendChild(input);
            fieldsGrid.appendChild(fieldDiv);
        });
        
        container.appendChild(fieldsGrid);
    }

    // Order preview generation
    function generateOrderPreview() {
        const previewContent = document.getElementById('previewContent');
        const formData = new FormData(document.getElementById('advancedFakeOrderForm'));
        
        const preview = {
            product: selectedTemplate ? selectedTemplate.product_name : document.getElementById('customSku').value,
            sku: selectedTemplate ? selectedTemplate.var_sku : document.getElementById('customSku').value,
            quantity: formData.get('quantity') || '1',
            price: formData.get('price_override') || (selectedTemplate ? selectedTemplate.default_price : 'N/A'),
            customer: formData.get('buyer_name') || 'Test Customer',
            email: formData.get('buyer_email') || '<EMAIL>',
            status: formData.get('order_status') || 'To Ship',
            payment: formData.get('payment_status') || 'paid',
            scenario: formData.get('test_scenario') || 'general_testing'
        };
        
        previewContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div><strong>Product:</strong> ${preview.product}</div>
                <div><strong>SKU:</strong> ${preview.sku}</div>
                <div><strong>Quantity:</strong> ${preview.quantity}</div>
                <div><strong>Price:</strong> $${preview.price}</div>
                <div><strong>Customer:</strong> ${preview.customer}</div>
                <div><strong>Email:</strong> ${preview.email}</div>
                <div><strong>Order Status:</strong> ${preview.status}</div>
                <div><strong>Payment Status:</strong> ${preview.payment}</div>
                <div class="md:col-span-2"><strong>Test Scenario:</strong> ${preview.scenario}</div>
            </div>
        `;
    }

    // Advanced form submission
    async function handleAdvancedFormSubmit(event) {
        event.preventDefault();
        
        if (!validateCurrentStep()) return;
        
        const formData = new FormData(event.target);
        const customMetadata = {};
        
        // Extract metadata fields
        for (const [key, value] of formData.entries()) {
            if (key.startsWith('metadata_')) {
                const metadataKey = key.replace('metadata_', '');
                customMetadata[metadataKey] = value;
            }
        }
        
        const orderConfig = {
            order_sn: formData.get('order_sn') || '',
            product_config: {
                var_sku: selectedTemplate ? selectedTemplate.var_sku : formData.get('custom_sku'),
                quantity: parseInt(formData.get('quantity')) || 1,
                price_override: formData.get('price_override') ? parseFloat(formData.get('price_override')) : null,
                custom_metadata: customMetadata
            },
            buyer_config: {
                username: formData.get('buyer_username'),
                name: formData.get('buyer_name'),
                phone: formData.get('buyer_phone'),
                email: formData.get('buyer_email'),
                address: formData.get('buyer_address')
            },
            order_config: {
                status: formData.get('order_status'),
                payment_status: formData.get('payment_status')
            },
            test_scenario: formData.get('test_scenario')
        };
        
        try {
            const response = await makeAuthenticatedRequest('/api/fake-orders/generate', {
                method: 'POST',
                body: JSON.stringify(orderConfig)
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert(`Fake order ${data.order_sn} created successfully!`, 'success');
                resetForm();
                loadFakeOrders();
            } else {
                showAlert('Error: ' + data.error, 'danger');
                if (data.validation_errors) {
                    console.error('Validation errors:', data.validation_errors);
                }
            }
        } catch (error) {
            showAlert('Error: ' + error.message, 'danger');
        }
    }

    function resetForm() {
        document.getElementById('advancedFakeOrderForm').reset();
        selectedTemplate = null;
        currentStep = 1;
        updateStepDisplay();
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.getElementById('productMetadataFields').innerHTML = '';
        document.getElementById('previewContent').innerHTML = '';
    }

    // Enhanced batch processing
    function handleCsvUpload(event) {
        const file = event.target.files[0];
        if (file) {
            processCsvFile(file);
        }
    }

    function handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }

    function handleDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0 && files[0].name.endsWith('.csv')) {
            processCsvFile(files[0]);
        }
    }

    function handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');
    }

    // Enhanced batch functionality
    function setupBatchEventListeners() {
        // Download template
        document.getElementById('downloadTemplateBtn').addEventListener('click', downloadCsvTemplate);
        
        // Create sample batch
        document.getElementById('createSampleBatchBtn').addEventListener('click', createSampleBatch);
        
        // Clear batch
        document.getElementById('clearBatchBtn').addEventListener('click', clearBatch);
        
        // Manual batch creation
        document.getElementById('addToBatchBtn').addEventListener('click', addToBatch);
        
        // Batch template buttons
        document.querySelectorAll('.batch-template-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                loadBatchTemplate(this.dataset.template);
            });
        });
        
        // Batch validation and editing
        document.getElementById('validateBatchBtn').addEventListener('click', validateBatch);
        document.getElementById('editBatchBtn').addEventListener('click', editBatch);
        document.getElementById('saveBatchTemplateBtn').addEventListener('click', saveBatchTemplate);
        
        // Progress control
        document.getElementById('cancelBatchBtn').addEventListener('click', cancelBatch);
    }

    function downloadCsvTemplate() {
        // Create CSV content
        const csvContent = `var_sku,buyer_username,buyer_name,buyer_phone,buyer_email,quantity,status,payment_status,test_scenario
canva_30,test_user_1,Test Customer 1,+60123456789,<EMAIL>,1,To Ship,paid,canva_batch_test
netflix_30,test_user_2,Test Customer 2,+60123456790,<EMAIL>,1,To Ship,paid,netflix_batch_test
steam_auth_code,test_user_3,Test Customer 3,+60123456791,<EMAIL>,1,To Ship,paid,steam_batch_test
vpn_monthly,test_user_4,Test Customer 4,+60123456792,<EMAIL>,2,To Ship,paid,vpn_batch_test
canva_lifetime,test_user_5,Test Customer 5,+60123456793,<EMAIL>,1,Unpaid,pending,canva_lifetime_test`;
        
        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'fake_order_batch_template.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        showAlert('CSV template downloaded successfully!', 'success');
    }

    function createSampleBatch() {
        const sampleOrders = [
            {
                var_sku: 'canva_30',
                buyer_username: 'sample_user_1',
                buyer_name: 'Sample Customer 1',
                buyer_phone: '+60123456789',
                buyer_email: '<EMAIL>',
                quantity: '1',
                status: 'To Ship',
                payment_status: 'paid',
                test_scenario: 'sample_batch_test'
            },
            {
                var_sku: 'netflix_30',
                buyer_username: 'sample_user_2',
                buyer_name: 'Sample Customer 2',
                buyer_phone: '+60123456790',
                buyer_email: '<EMAIL>',
                quantity: '1',
                status: 'To Ship',
                payment_status: 'paid',
                test_scenario: 'sample_batch_test'
            },
            {
                var_sku: 'steam_auth_code',
                buyer_username: 'sample_user_3',
                buyer_name: 'Sample Customer 3',
                buyer_phone: '+60123456791',
                buyer_email: '<EMAIL>',
                quantity: '1',
                status: 'To Ship',
                payment_status: 'paid',
                test_scenario: 'sample_batch_test'
            }
        ];
        
        displayBatchPreview(sampleOrders);
        showAlert('Sample batch created with 3 orders', 'success');
    }

    function clearBatch() {
        if (window.batchOrders && window.batchOrders.length > 0) {
            if (confirm('Are you sure you want to clear the current batch?')) {
                window.batchOrders = [];
                document.getElementById('batchPreview').style.display = 'none';
                document.getElementById('batchValidation').style.display = 'none';
                document.getElementById('batchProgress').style.display = 'none';
                showAlert('Batch cleared successfully', 'success');
            }
        } else {
            showAlert('No batch to clear', 'info');
        }
    }

    function addToBatch() {
        const sku = document.getElementById('batchSku').value;
        const count = parseInt(document.getElementById('batchCount').value) || 1;
        
        if (!sku) {
            showAlert('Please select a product SKU', 'warning');
            return;
        }
        
        if (count < 1 || count > 50) {
            showAlert('Number of orders must be between 1 and 50', 'warning');
            return;
        }
        
        const newOrders = [];
        for (let i = 1; i <= count; i++) {
            newOrders.push({
                var_sku: sku,
                buyer_username: `batch_user_${Date.now()}_${i}`,
                buyer_name: `Batch Customer ${i}`,
                buyer_phone: `+6012345${String(6789 + i).padStart(4, '0')}`,
                buyer_email: `batch_user_${Date.now()}_${i}@example.com`,
                quantity: '1',
                status: 'To Ship',
                payment_status: 'paid',
                test_scenario: 'manual_batch_test'
            });
        }
        
        // Add to existing batch or create new
        if (!window.batchOrders) {
            window.batchOrders = [];
        }
        window.batchOrders = window.batchOrders.concat(newOrders);
        
        displayBatchPreview(window.batchOrders);
        showAlert(`Added ${count} orders for ${sku} to batch`, 'success');
    }

    function loadBatchTemplate(templateName) {
        let templateOrders = [];
        
        switch (templateName) {
            case 'mixed_products':
                templateOrders = [
                    { var_sku: 'canva_30', buyer_username: 'mixed_user_1', test_scenario: 'mixed_products_test' },
                    { var_sku: 'netflix_30', buyer_username: 'mixed_user_2', test_scenario: 'mixed_products_test' },
                    { var_sku: 'steam_auth_code', buyer_username: 'mixed_user_3', test_scenario: 'mixed_products_test' },
                    { var_sku: 'vpn_monthly', buyer_username: 'mixed_user_4', test_scenario: 'mixed_products_test' },
                    { var_sku: 'hulu_10', buyer_username: 'mixed_user_5', test_scenario: 'mixed_products_test' }
                ];
                break;
                
            case 'canva_bulk':
                for (let i = 1; i <= 10; i++) {
                    templateOrders.push({
                        var_sku: i % 2 === 0 ? 'canva_30' : 'canva_lifetime',
                        buyer_username: `canva_bulk_user_${i}`,
                        test_scenario: 'canva_bulk_test'
                    });
                }
                break;
                
            case 'error_testing':
                templateOrders = [
                    { var_sku: 'invalid_sku', buyer_username: 'error_user_1', test_scenario: 'error_testing' },
                    { var_sku: 'canva_30', buyer_username: '', test_scenario: 'error_testing' }, // Missing username
                    { var_sku: 'netflix_30', buyer_username: 'error_user_3', quantity: 'invalid', test_scenario: 'error_testing' },
                    { var_sku: 'steam_auth_code', buyer_username: 'error_user_4', status: 'Invalid Status', test_scenario: 'error_testing' }
                ];
                break;
        }
        
        // Fill in default values
        templateOrders = templateOrders.map((order, index) => ({
            var_sku: order.var_sku,
            buyer_username: order.buyer_username || `template_user_${index + 1}`,
            buyer_name: order.buyer_name || `Template Customer ${index + 1}`,
            buyer_phone: order.buyer_phone || `+6012345${String(6789 + index).padStart(4, '0')}`,
            buyer_email: order.buyer_email || `${order.buyer_username || `template_user_${index + 1}`}@example.com`,
            quantity: order.quantity || '1',
            status: order.status || 'To Ship',
            payment_status: order.payment_status || 'paid',
            test_scenario: order.test_scenario || templateName
        }));
        
        window.batchOrders = templateOrders;
        displayBatchPreview(templateOrders);
        showAlert(`Loaded ${templateName.replace('_', ' ')} template with ${templateOrders.length} orders`, 'success');
    }

    async function validateBatch() {
        if (!window.batchOrders || window.batchOrders.length === 0) {
            showAlert('No batch to validate', 'warning');
            return;
        }
        
        const validationResults = {
            valid: [],
            warnings: [],
            errors: []
        };
        
        // Validate each order
        window.batchOrders.forEach((order, index) => {
            const orderErrors = [];
            const orderWarnings = [];
            
            // Required field validation
            if (!order.var_sku) orderErrors.push('Missing var_sku');
            if (!order.buyer_username) orderErrors.push('Missing buyer_username');
            
            // SKU validation
            if (order.var_sku && !availableTemplates[order.var_sku]) {
                orderWarnings.push(`Unknown SKU: ${order.var_sku}`);
            }
            
            // Quantity validation
            if (order.quantity && (isNaN(order.quantity) || parseInt(order.quantity) < 1)) {
                orderErrors.push('Invalid quantity');
            }
            
            // Email validation
            if (order.buyer_email && !order.buyer_email.includes('@')) {
                orderWarnings.push('Invalid email format');
            }
            
            if (orderErrors.length > 0) {
                validationResults.errors.push({
                    index: index + 1,
                    order: order,
                    errors: orderErrors
                });
            } else if (orderWarnings.length > 0) {
                validationResults.warnings.push({
                    index: index + 1,
                    order: order,
                    warnings: orderWarnings
                });
            } else {
                validationResults.valid.push({
                    index: index + 1,
                    order: order
                });
            }
        });
        
        // Display validation results
        displayBatchValidation(validationResults);
    }

    function displayBatchValidation(results) {
        const container = document.getElementById('batchValidationContent');
        const validationDiv = document.getElementById('batchValidation');
        
        let html = `
            <div class="grid grid-cols-3 gap-4 text-center mb-4">
                <div class="bg-green-100 p-3 rounded">
                    <div class="text-lg font-bold text-green-800">${results.valid.length}</div>
                    <div class="text-xs text-green-600">Valid Orders</div>
                </div>
                <div class="bg-yellow-100 p-3 rounded">
                    <div class="text-lg font-bold text-yellow-800">${results.warnings.length}</div>
                    <div class="text-xs text-yellow-600">Warnings</div>
                </div>
                <div class="bg-red-100 p-3 rounded">
                    <div class="text-lg font-bold text-red-800">${results.errors.length}</div>
                    <div class="text-xs text-red-600">Errors</div>
                </div>
            </div>
        `;
        
        if (results.errors.length > 0) {
            html += `
                <div class="mb-4">
                    <h5 class="font-semibold text-red-800 mb-2">Errors (must be fixed):</h5>
                    <div class="space-y-2">
                        ${results.errors.slice(0, 5).map(error => `
                            <div class="bg-red-50 p-2 rounded text-sm">
                                <strong>Order ${error.index}:</strong> ${error.errors.join(', ')}
                                <div class="text-xs text-gray-600">SKU: ${error.order.var_sku}, User: ${error.order.buyer_username}</div>
                            </div>
                        `).join('')}
                        ${results.errors.length > 5 ? `<div class="text-sm text-gray-600">... and ${results.errors.length - 5} more errors</div>` : ''}
                    </div>
                </div>
            `;
        }
        
        if (results.warnings.length > 0) {
            html += `
                <div class="mb-4">
                    <h5 class="font-semibold text-yellow-800 mb-2">Warnings (recommended to fix):</h5>
                    <div class="space-y-2">
                        ${results.warnings.slice(0, 3).map(warning => `
                            <div class="bg-yellow-50 p-2 rounded text-sm">
                                <strong>Order ${warning.index}:</strong> ${warning.warnings.join(', ')}
                            </div>
                        `).join('')}
                        ${results.warnings.length > 3 ? `<div class="text-sm text-gray-600">... and ${results.warnings.length - 3} more warnings</div>` : ''}
                    </div>
                </div>
            `;
        }
        
        container.innerHTML = html;
        validationDiv.style.display = 'block';
        
        // Update process button state
        const processBtn = document.getElementById('processBatchBtn');
        if (results.errors.length > 0) {
            processBtn.disabled = true;
            processBtn.classList.add('opacity-50', 'cursor-not-allowed');
            processBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Fix Errors First';
        } else {
            processBtn.disabled = false;
            processBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            processBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Process Batch';
        }
    }

    function editBatch() {
        // Convert current batch to CSV format for editing
        if (!window.batchOrders || window.batchOrders.length === 0) {
            showAlert('No batch to edit', 'warning');
            return;
        }
        
        const headers = ['var_sku', 'buyer_username', 'buyer_name', 'buyer_phone', 'buyer_email', 'quantity', 'status', 'payment_status', 'test_scenario'];
        const csvContent = [
            headers.join(','),
            ...window.batchOrders.map(order => 
                headers.map(header => order[header] || '').join(',')
            )
        ].join('\n');
        
        // Create a simple text editor modal (simplified for this implementation)
        const editModal = document.createElement('div');
        editModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        editModal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-96">
                <h3 class="text-lg font-semibold mb-4">Edit Batch CSV</h3>
                <textarea id="batchEditTextarea" class="w-full h-64 p-3 border border-gray-300 rounded font-mono text-sm" placeholder="Edit your batch data...">${csvContent}</textarea>
                <div class="flex justify-end space-x-3 mt-4">
                    <button onclick="this.closest('.fixed').remove()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">Cancel</button>
                    <button onclick="saveBatchEdit()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">Save Changes</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(editModal);
    }

    function saveBatchEdit() {
        const textarea = document.getElementById('batchEditTextarea');
        const csvContent = textarea.value;
        
        try {
            const lines = csvContent.split('\n').filter(line => line.trim());
            const headers = lines[0].split(',').map(h => h.trim());
            
            const orders = [];
            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim()) {
                    const values = lines[i].split(',').map(v => v.trim());
                    const order = {};
                    headers.forEach((header, index) => {
                        order[header] = values[index] || '';
                    });
                    orders.push(order);
                }
            }
            
            window.batchOrders = orders;
            displayBatchPreview(orders);
            document.querySelector('.fixed').remove();
            showAlert('Batch updated successfully', 'success');
            
        } catch (error) {
            showAlert('Error parsing CSV: ' + error.message, 'danger');
        }
    }

    function saveBatchTemplate() {
        if (!window.batchOrders || window.batchOrders.length === 0) {
            showAlert('No batch to save as template', 'warning');
            return;
        }
        
        const templateName = prompt('Enter template name:');
        if (!templateName) return;
        
        // Save to localStorage for this demo (in production, this would be saved to server)
        const templates = JSON.parse(localStorage.getItem('batchTemplates') || '{}');
        templates[templateName] = {
            name: templateName,
            orders: window.batchOrders,
            created_at: new Date().toISOString()
        };
        localStorage.setItem('batchTemplates', JSON.stringify(templates));
        
        showAlert(`Batch template "${templateName}" saved successfully`, 'success');
    }

    function cancelBatch() {
        if (confirm('Are you sure you want to cancel the batch processing?')) {
            // Reset progress indicators
            document.getElementById('batchProgress').style.display = 'none';
            document.getElementById('progressBar').style.width = '0%';
            document.getElementById('progressPercentage').textContent = '0%';
            document.getElementById('progressText').textContent = 'Cancelled';
            
            showAlert('Batch processing cancelled', 'warning');
        }
    }

    function processCsvFile(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const headers = lines[0].split(',').map(h => h.trim());
            
            const orders = [];
            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim()) {
                    const values = lines[i].split(',').map(v => v.trim());
                    const order = {};
                    headers.forEach((header, index) => {
                        order[header] = values[index] || '';
                    });
                    orders.push(order);
                }
            }
            
            displayBatchPreview(orders);
        };
        reader.readAsText(file);
    }

    function displayBatchPreview(orders) {
        const preview = document.getElementById('batchPreview');
        const content = document.getElementById('batchPreviewContent');
        
        content.innerHTML = `
            <div class="mb-4">
                <strong>Found ${orders.length} orders in CSV file</strong>
            </div>
            <div class="space-y-2">
                ${orders.slice(0, 5).map((order, index) => `
                    <div class="bg-white p-3 rounded border">
                        <strong>Order ${index + 1}:</strong> 
                        SKU: ${order.var_sku || 'N/A'}, 
                        Buyer: ${order.buyer_username || 'N/A'}
                    </div>
                `).join('')}
                ${orders.length > 5 ? `<div class="text-gray-600">... and ${orders.length - 5} more orders</div>` : ''}
            </div>
        `;
        
        preview.style.display = 'block';
        
        // Store orders for processing
        window.batchOrders = orders;
    }

    async function processBatch() {
        if (!window.batchOrders) return;
        
        const batchConfig = {
            continue_on_error: document.getElementById('continueOnError').checked,
            generate_unique_order_sns: document.getElementById('generateUniqueOrderSns').checked,
            default_test_scenario: document.getElementById('defaultTestScenario').value
        };
        
        const batchData = {
            orders: window.batchOrders.map(order => ({
                product_config: {
                    var_sku: order.var_sku,
                    quantity: parseInt(order.quantity) || 1
                },
                buyer_config: {
                    username: order.buyer_username || 'batch_user',
                    name: order.buyer_name || 'Batch Customer',
                    phone: order.buyer_phone || '+60123456789',
                    email: order.buyer_email || '<EMAIL>'
                },
                order_config: {
                    status: order.status || 'To Ship',
                    payment_status: order.payment_status || 'paid'
                },
                test_scenario: order.test_scenario || batchConfig.default_test_scenario
            })),
            batch_config: batchConfig
        };
        
        try {
            showBatchProgress(0, 'Starting batch processing...');
            
            const response = await makeAuthenticatedRequest('/api/fake-orders/batch-generate', {
                method: 'POST',
                body: JSON.stringify(batchData)
            });
            
            const data = await response.json();
            
            if (data.success || data.batch_summary.successful > 0) {
                showBatchProgress(100, 'Batch processing completed!');
                displayBatchResults(data);
                loadFakeOrders();
            } else {
                showAlert('Batch processing failed: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error processing batch: ' + error.message, 'danger');
        }
    }

    function showBatchProgress(percentage, message) {
        const progress = document.getElementById('batchProgress');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        progress.style.display = 'block';
        progressBar.style.width = percentage + '%';
        progressText.textContent = message;
    }

    function displayBatchResults(data) {
        const results = document.getElementById('batchResults');
        const summary = data.batch_summary;
        
        results.innerHTML = `
            <div class="bg-white p-4 rounded-lg border">
                <h5 class="font-semibold mb-3">Batch Processing Results:</h5>
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div class="bg-green-100 p-3 rounded">
                        <div class="text-2xl font-bold text-green-800">${summary.successful}</div>
                        <div class="text-sm text-green-600">Successful</div>
                    </div>
                    <div class="bg-red-100 p-3 rounded">
                        <div class="text-2xl font-bold text-red-800">${summary.failed}</div>
                        <div class="text-sm text-red-600">Failed</div>
                    </div>
                    <div class="bg-blue-100 p-3 rounded">
                        <div class="text-2xl font-bold text-blue-800">${summary.success_rate.toFixed(1)}%</div>
                        <div class="text-sm text-blue-600">Success Rate</div>
                    </div>
                </div>
                ${data.failed_orders.length > 0 ? `
                    <div class="mt-4">
                        <h6 class="font-semibold text-red-800">Failed Orders:</h6>
                        <div class="text-sm text-red-600">
                            ${data.failed_orders.slice(0, 3).map(order => order.error).join('<br>')}
                            ${data.failed_orders.length > 3 ? `<br>... and ${data.failed_orders.length - 3} more errors` : ''}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Order management
    async function loadFakeOrders() {
        try {
            const response = await makeAuthenticatedRequest('/api/fake-orders/list?limit=100');
            const data = await response.json();
            
            if (data.success) {
                fakeOrders = data.fake_orders;
                if (currentMode === 'management') {
                    displayOrdersManagement();
                }
                updateOrderCount();
            } else {
                showAlert('Failed to load fake orders: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error loading fake orders: ' + error.message, 'danger');
        }
    }

    function loadOrderManagement() {
        displayOrdersManagement();
        populateSkuFilters();
    }

    function displayOrdersManagement() {
        const container = document.getElementById('ordersList');
        const startIndex = (currentPage - 1) * ordersPerPage;
        const endIndex = startIndex + ordersPerPage;
        const pageOrders = fakeOrders.slice(startIndex, endIndex);
        
        if (pageOrders.length === 0) {
            container.innerHTML = '<div class="text-center text-gray-600 py-8">No fake orders found.</div>';
            return;
        }
        
        container.innerHTML = pageOrders.map(order => `
            <div class="order-card fake-order">
                <div class="order-header">
                    <div class="flex items-center">
                        <input type="checkbox" class="order-checkbox mr-3" data-order-sn="${order.order_sn}">
                        <div>
                            <div class="font-semibold">${order.order_sn}</div>
                            <div class="text-sm text-gray-600">${order.var_sku} - ${order.buyer_username}</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="order-status status-${order.status.toLowerCase().replace(' ', '-')}">${order.status}</span>
                        <div class="flex space-x-1">
                            <button onclick="processOrder('${order.order_sn}')" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-play mr-1"></i>Process
                            </button>
                            <button onclick="deleteOrder('${order.order_sn}')" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-trash mr-1"></i>Delete
                            </button>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div><strong>Product:</strong> ${order.var_sku}</div>
                    <div><strong>Buyer:</strong> ${order.buyer_username}</div>
                    <div><strong>Payment:</strong> ${order.payment_status || 'paid'}</div>
                    <div><strong>Scenario:</strong> ${order.test_scenario || 'N/A'}</div>
                </div>
            </div>
        `).join('');
        
        updatePagination();
    }

    function updatePagination() {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(fakeOrders.length / ordersPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // Previous button
        if (currentPage > 1) {
            paginationHTML += `<button onclick="changePage(${currentPage - 1})" class="bg-gray-300 hover:bg-gray-400 px-3 py-1 rounded mr-2">Previous</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                paginationHTML += `<button class="bg-blue-600 text-white px-3 py-1 rounded mr-2">${i}</button>`;
            } else {
                paginationHTML += `<button onclick="changePage(${i})" class="bg-gray-300 hover:bg-gray-400 px-3 py-1 rounded mr-2">${i}</button>`;
            }
        }
        
        // Next button
        if (currentPage < totalPages) {
            paginationHTML += `<button onclick="changePage(${currentPage + 1})" class="bg-gray-300 hover:bg-gray-400 px-3 py-1 rounded">Next</button>`;
        }
        
        pagination.innerHTML = paginationHTML;
    }

    function changePage(page) {
        currentPage = page;
        displayOrdersManagement();
    }

    function populateSkuFilters() {
        const skuFilter = document.getElementById('skuFilter');
        const uniqueSkus = [...new Set(fakeOrders.map(order => order.var_sku))];
        
        skuFilter.innerHTML = '<option value="">All Products</option>';
        uniqueSkus.forEach(sku => {
            const option = document.createElement('option');
            option.value = sku;
            option.textContent = sku;
            skuFilter.appendChild(option);
        });
    }

    function applyFilters() {
        const searchTerm = document.getElementById('searchFilter').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const skuFilter = document.getElementById('skuFilter').value;
        
        let filteredOrders = fakeOrders;
        
        if (searchTerm) {
            filteredOrders = filteredOrders.filter(order => 
                order.order_sn.toLowerCase().includes(searchTerm) ||
                order.buyer_username.toLowerCase().includes(searchTerm) ||
                order.var_sku.toLowerCase().includes(searchTerm)
            );
        }
        
        if (statusFilter) {
            filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
        }
        
        if (skuFilter) {
            filteredOrders = filteredOrders.filter(order => order.var_sku === skuFilter);
        }
        
        // Temporarily replace fakeOrders for display
        const originalOrders = fakeOrders;
        fakeOrders = filteredOrders;
        currentPage = 1;
        displayOrdersManagement();
        updateOrderCount();
        
        // Restore original orders
        setTimeout(() => {
            fakeOrders = originalOrders;
        }, 100);
    }

    function updateOrderCount() {
        const countElement = document.getElementById('orderCount');
        if (countElement) {
            countElement.textContent = fakeOrders.length;
        }
    }

    function selectAllOrders() {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        
        checkboxes.forEach(cb => {
            cb.checked = !allChecked;
        });
    }

    async function bulkDeleteOrders() {
        const selectedOrders = Array.from(document.querySelectorAll('.order-checkbox:checked'))
            .map(cb => cb.dataset.orderSn);
        
        if (selectedOrders.length === 0) {
            showAlert('Please select orders to delete', 'warning');
            return;
        }
        
        if (!confirm(`Are you sure you want to delete ${selectedOrders.length} orders?`)) {
            return;
        }
        
        try {
            const response = await makeAuthenticatedRequest('/api/fake-orders/cleanup', {
                method: 'DELETE',
                body: JSON.stringify({
                    order_sns: selectedOrders,
                    confirm: true
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert(`Successfully deleted ${data.cleanup_summary.deleted_count} orders`, 'success');
                loadFakeOrders();
            } else {
                showAlert('Error deleting orders: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error deleting orders: ' + error.message, 'danger');
        }
    }

    // Testing interface
    function loadTestingInterface() {
        // Populate test order SN dropdown with recent orders
        const testOrderSn = document.getElementById('testOrderSn');
        const recentOrders = fakeOrders.slice(0, 10);
        
        // Add datalist for autocomplete
        let datalist = document.getElementById('orderSnDatalist');
        if (!datalist) {
            datalist = document.createElement('datalist');
            datalist.id = 'orderSnDatalist';
            testOrderSn.setAttribute('list', 'orderSnDatalist');
            testOrderSn.parentNode.appendChild(datalist);
        }
        
        datalist.innerHTML = recentOrders.map(order => 
            `<option value="${order.order_sn}">${order.order_sn} - ${order.var_sku}</option>`
        ).join('');
    }

    async function runTestScenario(scenario) {
        const scenarioConfigs = {
            canva_basic: {
                var_sku: 'canva_30',
                test_scenario: 'canva_basic_test'
            },
            netflix_premium: {
                var_sku: 'netflix_30',
                test_scenario: 'netflix_premium_test'
            },
            steam_auth: {
                var_sku: 'steam_auth_code',
                test_scenario: 'steam_auth_test'
            }
        };
        
        const config = scenarioConfigs[scenario];
        if (!config) return;
        
        const orderConfig = {
            product_config: {
                var_sku: config.var_sku,
                quantity: 1
            },
            buyer_config: {
                username: `test_${scenario}`,
                name: `Test ${scenario.replace('_', ' ')}`,
                phone: '+60123456789',
                email: `test_${scenario}@example.com`
            },
            order_config: {
                status: 'To Ship',
                payment_status: 'paid'
            },
            test_scenario: config.test_scenario
        };
        
        try {
            const response = await makeAuthenticatedRequest('/api/fake-orders/generate', {
                method: 'POST',
                body: JSON.stringify(orderConfig)
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert(`Test scenario "${scenario}" created successfully! Order SN: ${data.order_sn}`, 'success');
                document.getElementById('testOrderSn').value = data.order_sn;
                loadFakeOrders();
            } else {
                showAlert('Error creating test scenario: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error creating test scenario: ' + error.message, 'danger');
        }
    }

    async function processTestOrder() {
        const orderSn = document.getElementById('testOrderSn').value;
        if (!orderSn) {
            showAlert('Please enter an order SN to test', 'warning');
            return;
        }
        
        try {
            // First, get order details
            const detailsResponse = await fetch(`/api/order/get_order_details?order_sn=${orderSn}`);
            const detailsData = await detailsResponse.json();
            
            if (!detailsData.success) {
                showAlert('Order not found: ' + detailsData.error, 'danger');
                return;
            }
            
            // Then process the order
            const processResponse = await fetch('/api/order/process_order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_sn: orderSn })
            });
            
            const processData = await processResponse.json();
            
            displayProcessingResults({
                order_details: detailsData,
                processing_result: processData
            });
            
        } catch (error) {
            showAlert('Error processing order: ' + error.message, 'danger');
        }
    }

    function displayProcessingResults(results) {
        const container = document.getElementById('processingResults');
        const orderDetails = results.order_details;
        const processingResult = results.processing_result;
        
        container.innerHTML = `
            <div class="processing-result ${processingResult.success ? 'result-success' : 'result-error'}">
                <h5 class="font-semibold mb-2">Processing Results:</h5>
                <div class="mb-3">
                    <strong>Order SN:</strong> ${orderDetails.data?.order_sn || 'N/A'}<br>
                    <strong>Status:</strong> ${processingResult.success ? 'Success' : 'Failed'}<br>
                    <strong>Message:</strong> ${processingResult.message || processingResult.error || 'N/A'}
                </div>
                ${processingResult.details ? `
                    <div class="text-sm">
                        <strong>Details:</strong><br>
                        <pre class="bg-gray-100 p-2 rounded mt-1 text-xs overflow-x-auto">${JSON.stringify(processingResult.details, null, 2)}</pre>
                    </div>
                ` : ''}
            </div>
        `;
        
        container.style.display = 'block';
    }

    async function validateAllOrders() {
        try {
            showAlert('Validating all fake orders...', 'info');
            
            let validCount = 0;
            let invalidCount = 0;
            const errors = [];
            
            for (const order of fakeOrders) {
                try {
                    const response = await fetch(`/api/order/get_order_details?order_sn=${order.order_sn}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        validCount++;
                    } else {
                        invalidCount++;
                        errors.push(`${order.order_sn}: ${data.error}`);
                    }
                } catch (error) {
                    invalidCount++;
                    errors.push(`${order.order_sn}: ${error.message}`);
                }
            }
            
            const message = `Validation complete: ${validCount} valid, ${invalidCount} invalid orders`;
            showAlert(message, invalidCount > 0 ? 'warning' : 'success');
            
            if (errors.length > 0) {
                console.log('Validation errors:', errors);
            }
            
        } catch (error) {
            showAlert('Error validating orders: ' + error.message, 'danger');
        }
    }

    async function cleanupOldOrders() {
        if (!confirm('This will delete fake orders older than 7 days. Continue?')) {
            return;
        }
        
        try {
            const response = await makeAuthenticatedRequest('/api/fake-orders/cleanup?older_than_days=7&confirm=true', {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert(`Cleanup complete: ${data.cleanup_summary.removed_count} orders removed`, 'success');
                loadFakeOrders();
            } else {
                showAlert('Error during cleanup: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error during cleanup: ' + error.message, 'danger');
        }
    }

    // Utility functions
    async function processOrder(orderSn) {
        document.getElementById('testOrderSn').value = orderSn;
        switchMode('testing');
        await processTestOrder();
    }

    async function deleteOrder(orderSn) {
        if (!confirm(`Are you sure you want to delete order ${orderSn}?`)) {
            return;
        }
        
        try {
            const response = await fetch(`/api/order/delete_fake_order/${orderSn}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert('Order deleted successfully', 'success');
                loadFakeOrders();
            } else {
                showAlert('Error deleting order: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error deleting order: ' + error.message, 'danger');
        }
    }

    function showAlert(message, type) {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        
        const alertClasses = {
            'success': 'bg-green-100 border border-green-400 text-green-700',
            'danger': 'bg-red-100 border border-red-400 text-red-700',
            'warning': 'bg-yellow-100 border border-yellow-400 text-yellow-700',
            'info': 'bg-blue-100 border border-blue-400 text-blue-700'
        };
        
        alertDiv.className = `px-4 py-3 rounded mb-4 ${alertClasses[type] || alertClasses['info']}`;
        alertDiv.innerHTML = `
            <div class="flex">
                <div class="flex-1">
                    ${message}
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg font-bold">&times;</button>
            </div>
        `;
        
        alertContainer.innerHTML = '';
        alertContainer.appendChild(alertDiv);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentElement) {
                alertDiv.remove();
            }
        }, 5000);
    }
</script>
{% endblock %}
