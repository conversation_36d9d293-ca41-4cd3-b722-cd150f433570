# Plugin UI Integration Standards

This document defines standards for integrating plugins into the SteamCodeTool user interface, ensuring consistent navigation, styling, and user experience across all plugins.

## 📋 Overview

All plugins must integrate seamlessly with the main application UI, following established patterns for navigation, styling, and user interaction.

## 🎯 Navigation Integration

### Base Template Integration

All plugins must be added to the main navigation menu in `templates/base.html`:

```html
<!-- Plugin Name -->
<li x-data="{ pluginOpen: false }">
    <button @click="pluginOpen = !pluginOpen"
        class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
        <span><i class="fas fa-icon-name mr-2"></i>Plugin Display Name</span>
        <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': pluginOpen }"></i>
    </button>
    <ul x-show="pluginOpen" class="pl-4 space-y-1 mt-1">
        <li>
            <a href="/admin/plugin-route/dashboard"
                class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
            </a>
        </li>
        <!-- Additional menu items -->
    </ul>
</li>
```

### Required Menu Items

Every plugin should include these standard menu items:

1. **Dashboard** - Main overview page (`fas fa-tachometer-alt`)
2. **Settings** - Plugin configuration (`fas fa-cog`)
3. **Management Pages** - Core functionality pages with appropriate icons

### Icon Guidelines

- Use Font Awesome 5 icons consistently
- Choose icons that represent the plugin's purpose
- Common icons:
  - `fas fa-tachometer-alt` - Dashboard
  - `fas fa-cog` - Settings
  - `fas fa-user-circle` - Account management
  - `fas fa-list` - List views
  - `fas fa-plus` - Create/Add actions
  - `fas fa-chart-bar` - Analytics/Reports

## 🎨 Template Standards

### Base Template Extension

All plugin templates must extend the base template:

```html
{% extends "base.html" %}

{% block title %}Plugin Name - Page Title{% endblock %}

{% block header %}Plugin Name{% endblock %}

{% block content %}
<!-- Plugin content here -->
{% endblock %}

{% block extra_head %}
<!-- Plugin-specific CSS/JS -->
{% endblock %}

{% block extra_scripts %}
<!-- Plugin-specific scripts -->
{% endblock %}
```

### CSS Framework Usage

- **Primary**: Tailwind CSS for layout and styling
- **Secondary**: Bootstrap 5 for complex components (modals, forms)
- **Icons**: Font Awesome 5
- **Charts**: Chart.js
- **Tables**: DataTables

### Responsive Design

All plugin pages must be responsive:

```html
<!-- Use responsive grid classes -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Content -->
</div>

<!-- Use responsive text sizes -->
<h1 class="text-xl md:text-2xl lg:text-3xl font-bold">Title</h1>
```

## 🔧 Component Standards

### Cards and Panels

Use consistent card styling:

```html
<div class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold mb-4">Card Title</h3>
    <!-- Card content -->
</div>
```

### Forms

Follow standard form patterns:

```html
<form class="space-y-6">
    <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
            Field Label
        </label>
        <input type="text" 
               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
    </div>
    
    <div class="flex justify-end space-x-3">
        <button type="button" 
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
            Cancel
        </button>
        <button type="submit" 
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Save
        </button>
    </div>
</form>
```

### Tables

Use DataTables for data display:

```html
<table id="dataTable" class="display w-full">
    <thead>
        <tr>
            <th>Column 1</th>
            <th>Column 2</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <!-- Table data -->
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        pageLength: 25,
        responsive: true,
        order: [[0, 'desc']]
    });
});
</script>
```

### Modals

Use Bootstrap modals for dialogs:

```html
<div class="modal fade" id="exampleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modal Title</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Modal content -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>
```

## 🚨 Alert and Notification Standards

### Success Messages

```html
<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
    <i class="fas fa-check-circle mr-2"></i>
    Operation completed successfully!
</div>
```

### Error Messages

```html
<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    <i class="fas fa-exclamation-circle mr-2"></i>
    An error occurred. Please try again.
</div>
```

### JavaScript Notifications

Use consistent notification patterns:

```javascript
function showAlert(message, type = 'info') {
    const alertClass = {
        'success': 'bg-green-100 border-green-400 text-green-700',
        'error': 'bg-red-100 border-red-400 text-red-700',
        'warning': 'bg-yellow-100 border-yellow-400 text-yellow-700',
        'info': 'bg-blue-100 border-blue-400 text-blue-700'
    }[type];
    
    const alertHtml = `
        <div class="${alertClass} px-4 py-3 rounded mb-4 alert-dismissible">
            ${message}
            <button type="button" class="float-right" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.getElementById('alerts-container').innerHTML = alertHtml;
}
```

## 📱 Mobile Responsiveness

### Breakpoint Guidelines

- **Mobile**: `< 768px` - Single column layout
- **Tablet**: `768px - 1024px` - Two column layout
- **Desktop**: `> 1024px` - Multi-column layout

### Mobile-First Approach

```html
<!-- Mobile-first responsive design -->
<div class="block md:flex md:space-x-6">
    <div class="w-full md:w-1/2 lg:w-1/3">
        <!-- Content -->
    </div>
</div>
```

## ✅ Compliance Checklist

### Navigation Integration
- [ ] Plugin added to base.html navigation menu
- [ ] Appropriate Font Awesome icon selected
- [ ] All major pages included in submenu
- [ ] URL patterns follow `/admin/plugin-name/` convention

### Template Standards
- [ ] All templates extend base.html
- [ ] Proper block usage (title, header, content)
- [ ] Responsive design implemented
- [ ] Consistent styling with existing pages

### Component Usage
- [ ] Standard card/panel styling
- [ ] Consistent form patterns
- [ ] DataTables for data display
- [ ] Bootstrap modals for dialogs

### User Experience
- [ ] Loading states implemented
- [ ] Error handling with user feedback
- [ ] Success/failure notifications
- [ ] Mobile-responsive design

## 📚 Examples

### Complete Plugin Navigation Entry

```html
<!-- OpenAI Plus Redeem Plugin -->
<li x-data="{ openaiOpen: false }">
    <button @click="openaiOpen = !openaiOpen"
        class="w-full flex items-center justify-between py-2 px-4 rounded transition duration-200 submenu-item">
        <span><i class="fas fa-brain mr-2"></i>OpenAI Plus Redeem</span>
        <i class="fas fa-chevron-down text-xs" x-bind:class="{ 'transform rotate-180': openaiOpen }"></i>
    </button>
    <ul x-show="openaiOpen" class="pl-4 space-y-1 mt-1">
        <li>
            <a href="/admin/openai-plus-redeem/"
                class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
            </a>
        </li>
        <li>
            <a href="/admin/openai-plus-redeem/accounts"
                class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                <i class="fas fa-user-circle mr-2"></i>Accounts
            </a>
        </li>
        <li>
            <a href="/admin/openai-plus-redeem/redemptions"
                class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                <i class="fas fa-ticket-alt mr-2"></i>Redemptions
            </a>
        </li>
        <li>
            <a href="/admin/openai-plus-redeem/cooldowns"
                class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                <i class="fas fa-clock mr-2"></i>Cooldown Management
            </a>
        </li>
        <li>
            <a href="/admin/openai-plus-redeem/settings"
                class="block py-1.5 px-3 rounded transition duration-200 text-sm submenu-item">
                <i class="fas fa-cog mr-2"></i>Settings
            </a>
        </li>
    </ul>
</li>
```

This example demonstrates proper navigation integration following all UI standards.
