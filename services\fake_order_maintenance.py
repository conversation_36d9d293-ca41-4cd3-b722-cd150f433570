"""
Fake Order Maintenance and Cleanup Service

This module provides automatic cleanup and maintenance tools for the fake order system,
including scheduled cleanup, storage optimization, archival systems, and template management.
"""

import json
import os
import shutil
import threading
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import gzip
import hashlib

from services.fake_order_storage import FakeOrderStorage, FakeOrder, FakeOrderQuery
from services.product_template_engine import product_template_engine
from utils.fake_order_security import fake_order_security

logger = logging.getLogger(__name__)

@dataclass
class CleanupConfig:
    """Configuration for cleanup operations"""
    older_than_days: int = 7
    processed_only: bool = False
    max_orders_per_cleanup: int = 1000
    preserve_recent_processed: bool = True
    preserve_error_orders: bool = True
    archive_before_delete: bool = True
    cleanup_templates: bool = False
    cleanup_logs: bool = True
    log_retention_days: int = 30

@dataclass
class MaintenanceStats:
    """Statistics from maintenance operations"""
    cleanup_stats: Dict[str, Any] = field(default_factory=dict)
    archive_stats: Dict[str, Any] = field(default_factory=dict)
    optimization_stats: Dict[str, Any] = field(default_factory=dict)
    template_stats: Dict[str, Any] = field(default_factory=dict)
    total_runtime_seconds: float = 0.0
    last_run: Optional[datetime] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

class FakeOrderMaintenance:
    """Comprehensive maintenance system for fake orders"""
    
    def __init__(self, storage_dir: str = 'configs/data/fake_orders'):
        self.storage_dir = Path(storage_dir)
        self.archive_dir = self.storage_dir / 'archive'
        self.maintenance_dir = self.storage_dir / 'maintenance'
        self.logs_dir = Path('logs')
        
        self.storage = FakeOrderStorage(str(self.storage_dir))
        self._lock = threading.RLock()
        
        self._ensure_directories()
        self._load_maintenance_config()
    
    def _ensure_directories(self):
        """Ensure all required directories exist"""
        self.archive_dir.mkdir(parents=True, exist_ok=True)
        self.maintenance_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for organized archival
        (self.archive_dir / 'by_date').mkdir(exist_ok=True)
        (self.archive_dir / 'by_scenario').mkdir(exist_ok=True)
        (self.archive_dir / 'by_sku').mkdir(exist_ok=True)
    
    def _load_maintenance_config(self):
        """Load maintenance configuration"""
        config_file = self.maintenance_dir / 'config.json'
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.default_config = CleanupConfig(**config_data)
            except Exception as e:
                logger.warning(f"Failed to load maintenance config: {e}")
                self.default_config = CleanupConfig()
        else:
            self.default_config = CleanupConfig()
            self._save_maintenance_config()
    
    def _save_maintenance_config(self):
        """Save maintenance configuration"""
        config_file = self.maintenance_dir / 'config.json'
        
        try:
            config_data = {
                'older_than_days': self.default_config.older_than_days,
                'processed_only': self.default_config.processed_only,
                'max_orders_per_cleanup': self.default_config.max_orders_per_cleanup,
                'preserve_recent_processed': self.default_config.preserve_recent_processed,
                'preserve_error_orders': self.default_config.preserve_error_orders,
                'archive_before_delete': self.default_config.archive_before_delete,
                'cleanup_templates': self.default_config.cleanup_templates,
                'cleanup_logs': self.default_config.cleanup_logs,
                'log_retention_days': self.default_config.log_retention_days
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save maintenance config: {e}")
    
    def run_scheduled_cleanup(self, config: Optional[CleanupConfig] = None) -> MaintenanceStats:
        """Run scheduled cleanup with comprehensive maintenance"""
        start_time = datetime.now()
        stats = MaintenanceStats()
        
        if config is None:
            config = self.default_config
        
        with self._lock:
            try:
                logger.info("Starting scheduled fake order maintenance...")
                
                # 1. Archive old orders before cleanup
                if config.archive_before_delete:
                    archive_stats = self._archive_old_orders(config)
                    stats.archive_stats = archive_stats
                
                # 2. Clean up old fake orders
                cleanup_stats = self._cleanup_old_orders(config)
                stats.cleanup_stats = cleanup_stats
                
                # 3. Optimize storage
                optimization_stats = self._optimize_storage()
                stats.optimization_stats = optimization_stats
                
                # 4. Clean up templates if requested
                if config.cleanup_templates:
                    template_stats = self._cleanup_templates()
                    stats.template_stats = template_stats
                
                # 5. Clean up logs
                if config.cleanup_logs:
                    self._cleanup_logs(config.log_retention_days)
                
                # 6. Update maintenance metadata
                self._update_maintenance_metadata(stats)
                
                stats.total_runtime_seconds = (datetime.now() - start_time).total_seconds()
                stats.last_run = datetime.now()
                
                logger.info(f"Scheduled maintenance completed in {stats.total_runtime_seconds:.2f}s")
                
                # Log security audit
                fake_order_security.log_fake_order_operation(
                    'scheduled_maintenance_completed',
                    {'maintenance_type': 'scheduled_cleanup'},
                    {
                        'cleanup_stats': cleanup_stats,
                        'archive_stats': stats.archive_stats,
                        'runtime_seconds': stats.total_runtime_seconds
                    }
                )
                
                return stats
                
            except Exception as e:
                error_msg = f"Scheduled maintenance failed: {e}"
                logger.error(error_msg)
                stats.errors.append(error_msg)
                
                fake_order_security.log_fake_order_operation(
                    'scheduled_maintenance_error',
                    {'maintenance_type': 'scheduled_cleanup'},
                    {'error': str(e)}
                )
                
                return stats
    
    def _archive_old_orders(self, config: CleanupConfig) -> Dict[str, Any]:
        """Archive old orders before deletion"""
        try:
            cutoff_date = datetime.now() - timedelta(days=config.older_than_days)
            
            # Query old orders
            query = FakeOrderQuery(
                created_before=cutoff_date,
                limit=config.max_orders_per_cleanup
            )
            
            old_orders = self.storage.query_orders(query)
            
            if not old_orders:
                return {
                    'archived_count': 0,
                    'archive_files_created': 0,
                    'total_size_bytes': 0
                }
            
            # Group orders for archival
            archive_groups = self._group_orders_for_archive(old_orders)
            
            archived_count = 0
            archive_files_created = 0
            total_size_bytes = 0
            
            for group_name, orders in archive_groups.items():
                if orders:
                    archive_file = self._create_archive_file(group_name, orders)
                    if archive_file:
                        archived_count += len(orders)
                        archive_files_created += 1
                        total_size_bytes += archive_file.stat().st_size
            
            logger.info(f"Archived {archived_count} orders in {archive_files_created} files")
            
            return {
                'archived_count': archived_count,
                'archive_files_created': archive_files_created,
                'total_size_bytes': total_size_bytes,
                'archive_groups': list(archive_groups.keys())
            }
            
        except Exception as e:
            logger.error(f"Failed to archive orders: {e}")
            return {'error': str(e)}
    
    def _group_orders_for_archive(self, orders: List[FakeOrder]) -> Dict[str, List[FakeOrder]]:
        """Group orders for organized archival"""
        groups = defaultdict(list)
        
        for order in orders:
            # Group by date (month)
            date_key = order.created_at.strftime('%Y-%m')
            groups[f"date_{date_key}"].append(order)
            
            # Group by scenario
            scenario_key = order.test_scenario.replace(' ', '_').lower()
            groups[f"scenario_{scenario_key}"].append(order)
            
            # Group by SKU category
            sku_category = order.var_sku.split('_')[0] if '_' in order.var_sku else 'other'
            groups[f"sku_{sku_category}"].append(order)
        
        return dict(groups)
    
    def _create_archive_file(self, group_name: str, orders: List[FakeOrder]) -> Optional[Path]:
        """Create compressed archive file for orders"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            archive_filename = f"{group_name}_{timestamp}.json.gz"
            
            # Determine archive subdirectory
            if group_name.startswith('date_'):
                archive_path = self.archive_dir / 'by_date' / archive_filename
            elif group_name.startswith('scenario_'):
                archive_path = self.archive_dir / 'by_scenario' / archive_filename
            elif group_name.startswith('sku_'):
                archive_path = self.archive_dir / 'by_sku' / archive_filename
            else:
                archive_path = self.archive_dir / archive_filename
            
            # Prepare archive data
            archive_data = {
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'group_name': group_name,
                    'order_count': len(orders),
                    'date_range': {
                        'oldest': min(order.created_at for order in orders).isoformat(),
                        'newest': max(order.created_at for order in orders).isoformat()
                    }
                },
                'orders': [order.to_dict() for order in orders]
            }
            
            # Write compressed archive
            with gzip.open(archive_path, 'wt', encoding='utf-8') as f:
                json.dump(archive_data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Created archive: {archive_path}")
            return archive_path
            
        except Exception as e:
            logger.error(f"Failed to create archive for {group_name}: {e}")
            return None
    
    def _cleanup_old_orders(self, config: CleanupConfig) -> Dict[str, Any]:
        """Clean up old orders with advanced filtering"""
        try:
            cutoff_date = datetime.now() - timedelta(days=config.older_than_days)
            
            # Get all orders for analysis
            all_orders = self.storage.query_orders(FakeOrderQuery(limit=None))
            
            orders_to_delete = []
            orders_preserved = []
            
            for order in all_orders:
                should_delete = False
                preserve_reason = None
                
                # Check age
                if order.created_at < cutoff_date:
                    should_delete = True
                    
                    # Apply preservation rules
                    if config.preserve_recent_processed and order.is_processed:
                        # Keep recently processed orders even if old
                        last_processed = self._get_last_processing_date(order)
                        if last_processed and (datetime.now() - last_processed).days < 3:
                            should_delete = False
                            preserve_reason = "recently_processed"
                    
                    if config.preserve_error_orders and order.error_history:
                        should_delete = False
                        preserve_reason = "has_errors"
                    
                    # Check processing status filter
                    if config.processed_only and not order.is_processed:
                        should_delete = False
                        preserve_reason = "not_processed"
                
                if should_delete:
                    orders_to_delete.append(order)
                elif preserve_reason:
                    orders_preserved.append({'order': order, 'reason': preserve_reason})
            
            # Limit cleanup batch size
            if len(orders_to_delete) > config.max_orders_per_cleanup:
                orders_to_delete = orders_to_delete[:config.max_orders_per_cleanup]
            
            # Perform deletion
            deleted_count = 0
            deletion_errors = []
            
            for order in orders_to_delete:
                try:
                    if self.storage.delete_order(order.order_sn):
                        deleted_count += 1
                    else:
                        deletion_errors.append(f"Failed to delete {order.order_sn}")
                except Exception as e:
                    deletion_errors.append(f"Error deleting {order.order_sn}: {e}")
            
            cleanup_stats = {
                'total_orders_analyzed': len(all_orders),
                'orders_eligible_for_deletion': len(orders_to_delete),
                'orders_deleted': deleted_count,
                'orders_preserved': len(orders_preserved),
                'preservation_reasons': {
                    reason: len([p for p in orders_preserved if p['reason'] == reason])
                    for reason in set(p['reason'] for p in orders_preserved)
                },
                'deletion_errors': deletion_errors,
                'cutoff_date': cutoff_date.isoformat()
            }
            
            logger.info(f"Cleanup completed: {deleted_count} deleted, {len(orders_preserved)} preserved")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Failed to cleanup orders: {e}")
            return {'error': str(e)}
    
    def _get_last_processing_date(self, order: FakeOrder) -> Optional[datetime]:
        """Get the last processing date from order history"""
        if not order.processing_history:
            return None
        
        try:
            last_entry = max(order.processing_history, key=lambda x: x.get('timestamp', ''))
            return datetime.fromisoformat(last_entry['timestamp'])
        except (ValueError, KeyError):
            return None
    
    def _optimize_storage(self) -> Dict[str, Any]:
        """Optimize storage files and indexes"""
        try:
            optimization_stats = {
                'files_optimized': 0,
                'space_saved_bytes': 0,
                'index_rebuilt': False,
                'cache_cleared': False
            }
            
            # Get file sizes before optimization
            orders_file = self.storage_dir / 'orders.json'
            index_file = self.storage_dir / 'index.json'
            
            original_orders_size = orders_file.stat().st_size if orders_file.exists() else 0
            original_index_size = index_file.stat().st_size if index_file.exists() else 0
            
            # Rebuild and optimize index
            self.storage._rebuild_index()
            optimization_stats['index_rebuilt'] = True
            
            # Clear cache to free memory
            self.storage._cache.clear()
            optimization_stats['cache_cleared'] = True
            
            # Compact orders file (remove any corruption or optimize format)
            self._compact_orders_file()
            optimization_stats['files_optimized'] += 1
            
            # Calculate space saved
            new_orders_size = orders_file.stat().st_size if orders_file.exists() else 0
            new_index_size = index_file.stat().st_size if index_file.exists() else 0
            
            space_saved = (original_orders_size + original_index_size) - (new_orders_size + new_index_size)
            optimization_stats['space_saved_bytes'] = max(0, space_saved)
            
            logger.info(f"Storage optimization completed, saved {space_saved} bytes")
            return optimization_stats
            
        except Exception as e:
            logger.error(f"Failed to optimize storage: {e}")
            return {'error': str(e)}
    
    def _compact_orders_file(self):
        """Compact and optimize the orders file"""
        try:
            orders_file = self.storage_dir / 'orders.json'
            if not orders_file.exists():
                return
            
            # Load and rewrite with consistent formatting
            with open(orders_file, 'r', encoding='utf-8') as f:
                orders_data = json.load(f)
            
            # Remove any null or invalid entries
            valid_orders = [order for order in orders_data if order and isinstance(order, dict)]
            
            # Write back with optimized formatting
            with open(orders_file, 'w', encoding='utf-8') as f:
                json.dump(valid_orders, f, separators=(',', ':'), ensure_ascii=False)
            
            logger.debug(f"Compacted orders file: {len(orders_data)} -> {len(valid_orders)} orders")
            
        except Exception as e:
            logger.error(f"Failed to compact orders file: {e}")
    
    def _cleanup_templates(self) -> Dict[str, Any]:
        """Clean up unused or invalid templates"""
        try:
            template_stats = {
                'templates_analyzed': 0,
                'templates_removed': 0,
                'invalid_templates': 0,
                'unused_templates': 0
            }
            
            # Get all templates
            all_templates = product_template_engine.get_all_templates()
            template_stats['templates_analyzed'] = len(all_templates)
            
            # Get all orders to check template usage
            all_orders = self.storage.query_orders(FakeOrderQuery(limit=None))
            used_skus = set(order.var_sku for order in all_orders)
            
            templates_to_remove = []
            
            for sku, template in all_templates.items():
                # Check if template is used
                if sku not in used_skus:
                    # Only remove custom templates, not built-in ones
                    if template.category == 'custom':
                        templates_to_remove.append(sku)
                        template_stats['unused_templates'] += 1
                
                # Check template validity
                try:
                    product_template_engine.validate_template(template)
                except Exception:
                    templates_to_remove.append(sku)
                    template_stats['invalid_templates'] += 1
            
            # Remove identified templates
            for sku in templates_to_remove:
                try:
                    product_template_engine.remove_template(sku)
                    template_stats['templates_removed'] += 1
                except Exception as e:
                    logger.error(f"Failed to remove template {sku}: {e}")
            
            logger.info(f"Template cleanup: {template_stats['templates_removed']} removed")
            return template_stats
            
        except Exception as e:
            logger.error(f"Failed to cleanup templates: {e}")
            return {'error': str(e)}
    
    def _cleanup_logs(self, retention_days: int):
        """Clean up old log files"""
        try:
            if not self.logs_dir.exists():
                return
            
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            cleaned_files = 0
            
            for log_file in self.logs_dir.glob('*.log*'):
                try:
                    file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_mtime < cutoff_date:
                        log_file.unlink()
                        cleaned_files += 1
                except Exception as e:
                    logger.error(f"Failed to clean log file {log_file}: {e}")
            
            logger.info(f"Cleaned up {cleaned_files} old log files")
            
        except Exception as e:
            logger.error(f"Failed to cleanup logs: {e}")
    
    def _update_maintenance_metadata(self, stats: MaintenanceStats):
        """Update maintenance metadata file"""
        try:
            metadata_file = self.maintenance_dir / 'last_maintenance.json'
            
            metadata = {
                'last_run': stats.last_run.isoformat() if stats.last_run else None,
                'runtime_seconds': stats.total_runtime_seconds,
                'cleanup_stats': stats.cleanup_stats,
                'archive_stats': stats.archive_stats,
                'optimization_stats': stats.optimization_stats,
                'template_stats': stats.template_stats,
                'errors': stats.errors,
                'warnings': stats.warnings
            }
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to update maintenance metadata: {e}")
    
    def get_maintenance_status(self) -> Dict[str, Any]:
        """Get current maintenance status and statistics"""
        try:
            metadata_file = self.maintenance_dir / 'last_maintenance.json'
            
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    last_maintenance = json.load(f)
            else:
                last_maintenance = None
            
            # Get current storage statistics
            storage_stats = self.storage.get_statistics()
            
            # Get archive information
            archive_info = self._get_archive_info()
            
            return {
                'maintenance_enabled': True,
                'last_maintenance': last_maintenance,
                'current_storage_stats': storage_stats,
                'archive_info': archive_info,
                'maintenance_config': {
                    'older_than_days': self.default_config.older_than_days,
                    'processed_only': self.default_config.processed_only,
                    'max_orders_per_cleanup': self.default_config.max_orders_per_cleanup,
                    'archive_before_delete': self.default_config.archive_before_delete
                },
                'next_recommended_cleanup': self._calculate_next_cleanup_date()
            }
            
        except Exception as e:
            logger.error(f"Failed to get maintenance status: {e}")
            return {'error': str(e)}
    
    def _get_archive_info(self) -> Dict[str, Any]:
        """Get information about archived data"""
        try:
            archive_files = list(self.archive_dir.rglob('*.json.gz'))
            
            total_size = sum(f.stat().st_size for f in archive_files)
            
            return {
                'archive_files_count': len(archive_files),
                'total_archive_size_bytes': total_size,
                'archive_directories': [
                    'by_date', 'by_scenario', 'by_sku'
                ],
                'oldest_archive': min((f.stat().st_mtime for f in archive_files), default=None),
                'newest_archive': max((f.stat().st_mtime for f in archive_files), default=None)
            }
            
        except Exception as e:
            logger.error(f"Failed to get archive info: {e}")
            return {'error': str(e)}
    
    def _calculate_next_cleanup_date(self) -> Optional[str]:
        """Calculate when the next cleanup should run"""
        try:
            metadata_file = self.maintenance_dir / 'last_maintenance.json'
            
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    last_maintenance = json.load(f)
                
                if last_maintenance.get('last_run'):
                    last_run = datetime.fromisoformat(last_maintenance['last_run'])
                    # Recommend cleanup every 7 days
                    next_cleanup = last_run + timedelta(days=7)
                    return next_cleanup.isoformat()
            
            # If no previous maintenance, recommend immediate cleanup
            return datetime.now().isoformat()
            
        except Exception:
            return None
    
    def update_maintenance_config(self, config_updates: Dict[str, Any]) -> bool:
        """Update maintenance configuration"""
        try:
            # Update configuration
            for key, value in config_updates.items():
                if hasattr(self.default_config, key):
                    setattr(self.default_config, key, value)
            
            # Save updated configuration
            self._save_maintenance_config()
            
            logger.info(f"Updated maintenance config: {config_updates}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update maintenance config: {e}")
            return False

# Global maintenance service instance
fake_order_maintenance = FakeOrderMaintenance()