#!/usr/bin/env python3
"""
Test script for tag-based VPN strategy system
Tests the new server tag filtering and SKU mapping functionality
"""

import sys
import os
import logging
from typing import Dict, List, Any

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sku_tag_mapping():
    """Test SKU to server tags mapping"""
    print("=" * 60)
    print("Testing SKU to Server Tags Mapping (from config file)")
    print("=" * 60)

    # Show config info first
    config_info = VPNStrategyFactory.get_config_info()
    print(f"Config file: {config_info['config_file_path']}")
    print(f"Config loaded: {config_info['config_loaded']}")
    print(f"Total SKU mappings: {config_info['total_sku_mappings']}")
    print(f"Categories: {', '.join(config_info['categories'])}")
    print(f"Version: {config_info['version']}")
    print()

    test_skus = [
        'my_30',
        'my_standard_30',
        'my_premium_60',
        'my_highspeed_30',
        'my_highspeed_premium_60',
        'sg_15',
        'sg_highspeed_30',
        'sg_premium_60',
        'sg_business_90',
        'unknown_sku'
    ]

    for sku in test_skus:
        tags = VPNStrategyFactory.get_server_tags_for_sku(sku)
        print(f"SKU: {sku:25} -> Tags: {tags}")

    print()

def test_server_filtering():
    """Test server filtering by tags"""
    print("=" * 60)
    print("Testing Server Filtering by Tags")
    print("=" * 60)
    
    # Mock server data for testing
    mock_servers = [
        {
            'id': 1,
            'name': 'MY-Shinjiru-01',
            'host': 'my1.example.com',
            'is_active': True,
            'tags': ['malaysia', 'shinjiru', 'basic']
        },
        {
            'id': 2,
            'name': 'MY-Shinjiru-02',
            'host': 'my2.example.com',
            'is_active': True,
            'tags': ['malaysia', 'shinjiru', 'standard', 'premium']
        },
        {
            'id': 3,
            'name': 'SG-DO-01',
            'host': 'sg1.example.com',
            'is_active': True,
            'tags': ['singapore', 'digitalocean', 'highspeed']
        },
        {
            'id': 4,
            'name': 'SG-DO-02',
            'host': 'sg2.example.com',
            'is_active': True,
            'tags': ['singapore', 'digitalocean', 'premium', 'business']
        },
        {
            'id': 5,
            'name': 'Global-Premium-01',
            'host': 'global1.example.com',
            'is_active': True,
            'tags': ['malaysia', 'singapore', 'highspeed', 'premium', 'business']
        },
        {
            'id': 6,
            'name': 'Inactive-Server',
            'host': 'inactive.example.com',
            'is_active': False,
            'tags': ['malaysia', 'shinjiru']
        }
    ]
    
    # Create a mock strategy to test filtering
    class MockStrategy:
        def _filter_servers_by_tags(self, servers: List[Dict[str, Any]], required_tags: List[str]) -> List[Dict[str, Any]]:
            """Mock implementation of server filtering"""
            if not required_tags:
                return servers
                
            filtered_servers = []
            
            for server in servers:
                server_tags = server.get('tags', [])
                
                # Convert tags to lowercase for case-insensitive matching
                server_tags_lower = [tag.lower().strip() for tag in server_tags if tag]
                required_tags_lower = [tag.lower().strip() for tag in required_tags if tag]
                
                # Check if server has any of the required tags
                has_required_tag = any(req_tag in server_tags_lower for req_tag in required_tags_lower)
                
                if has_required_tag:
                    filtered_servers.append(server)
            
            return filtered_servers
    
    mock_strategy = MockStrategy()
    
    # Test different tag combinations
    test_cases = [
        (['malaysia'], 'Malaysia servers'),
        (['singapore'], 'Singapore servers'),
        (['shinjiru'], 'Shinjiru servers'),
        (['digitalocean'], 'Digital Ocean servers'),
        (['premium'], 'Premium servers'),
        (['malaysia', 'shinjiru'], 'Malaysia Shinjiru servers'),
        (['singapore', 'digitalocean'], 'Singapore Digital Ocean servers'),
        (['highspeed', 'premium'], 'High-speed Premium servers'),
        (['nonexistent'], 'Non-existent tag'),
        ([], 'No tags (should return all)')
    ]
    
    # Only test with active servers
    active_servers = [s for s in mock_servers if s.get('is_active', False)]
    
    for required_tags, description in test_cases:
        filtered = mock_strategy._filter_servers_by_tags(active_servers, required_tags)
        print(f"\n{description}:")
        print(f"  Required tags: {required_tags}")
        print(f"  Found {len(filtered)} servers:")
        for server in filtered:
            print(f"    - {server['name']} (tags: {server['tags']})")

def test_strategy_creation():
    """Test strategy creation for different SKUs"""
    print("\n" + "=" * 60)
    print("Testing Strategy Creation")
    print("=" * 60)
    
    test_skus = [
        'my_30',
        'my_highspeed_30',
        'sg_15',
        'sg_premium_60'
    ]
    
    for sku in test_skus:
        try:
            # Note: This will fail if VPN API service is not initialized
            # but we can still test the strategy selection logic
            strategy = VPNStrategyFactory.create_strategy(sku)
            if strategy:
                print(f"SKU: {sku:20} -> Strategy: {strategy.__class__.__name__}")
            else:
                print(f"SKU: {sku:20} -> Strategy: None (VPN API not initialized)")
        except Exception as e:
            print(f"SKU: {sku:20} -> Error: {str(e)}")

def test_supported_skus():
    """Test getting supported SKUs"""
    print("\n" + "=" * 60)
    print("Supported SKUs and Strategies")
    print("=" * 60)
    
    supported = VPNStrategyFactory.get_supported_skus()
    for pattern, strategy_name in supported.items():
        print(f"Pattern: {pattern:20} -> Strategy: {strategy_name}")

def main():
    """Run all tests"""
    print("VPN Tag-Based Strategy Test Suite")
    print("=" * 60)
    
    try:
        test_sku_tag_mapping()
        test_server_filtering()
        test_strategy_creation()
        test_supported_skus()
        
        print("\n" + "=" * 60)
        print("All tests completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
