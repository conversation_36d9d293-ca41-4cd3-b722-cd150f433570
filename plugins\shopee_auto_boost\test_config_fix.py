#!/usr/bin/env python3
"""
Test script to verify the Shopee Auto Boost plugin configuration fix

This script tests:
1. Loading current configuration from the API
2. Saving configuration via the API
3. Verifying configuration persistence
"""

import json
import requests
import time
from typing import Dict, Any

BASE_URL = "http://localhost:5000"  # Adjust if different
CONFIG_API_URL = f"{BASE_URL}/api/shopee_auto_boost/config"
STATUS_API_URL = f"{BASE_URL}/api/shopee_auto_boost/status"

def test_config_loading():
    """Test loading current configuration"""
    print("Testing configuration loading...")
    
    try:
        response = requests.get(STATUS_API_URL)
        response.raise_for_status()
        
        data = response.json()
        config = data.get('config', {})
        
        print(f"✓ Successfully loaded configuration with {len(config)} keys")
        print(f"  Keys: {list(config.keys())}")
        
        # Check for required fields
        required_fields = [
            'enabled', 'boost_interval_hours', 'products_per_boost', 
            'auto_start', 'shopee_api_url', 'rotation_strategy',
            'product_filters', 'pinned_products', 'logging'
        ]
        
        missing_fields = [field for field in required_fields if field not in config]
        if missing_fields:
            print(f"✗ Missing required fields: {missing_fields}")
            return False
        else:
            print("✓ All required configuration fields present")
            return True
            
    except Exception as e:
        print(f"✗ Error loading configuration: {e}")
        return False

def test_config_saving():
    """Test saving configuration"""
    print("\nTesting configuration saving...")
    
    # First, get current config
    try:
        response = requests.get(STATUS_API_URL)
        response.raise_for_status()
        current_config = response.json().get('config', {})
        
        # Make a small change
        test_config = current_config.copy()
        original_interval = test_config.get('boost_interval_hours', 4)
        test_interval = 6 if original_interval != 6 else 8
        test_config['boost_interval_hours'] = test_interval
        
        print(f"  Changing boost_interval_hours from {original_interval} to {test_interval}")
        
        # Save the configuration
        save_response = requests.put(CONFIG_API_URL, json=test_config)
        save_response.raise_for_status()
        
        save_result = save_response.json()
        if save_result.get('success'):
            print("✓ Configuration saved successfully")
            
            # Wait a moment and verify the change persisted
            time.sleep(1)
            
            verify_response = requests.get(STATUS_API_URL)
            verify_response.raise_for_status()
            verify_config = verify_response.json().get('config', {})
            
            if verify_config.get('boost_interval_hours') == test_interval:
                print("✓ Configuration change persisted correctly")
                
                # Restore original configuration
                restore_config = current_config.copy()
                restore_response = requests.put(CONFIG_API_URL, json=restore_config)
                if restore_response.ok and restore_response.json().get('success'):
                    print("✓ Original configuration restored")
                    return True
                else:
                    print("✗ Failed to restore original configuration")
                    return False
            else:
                print(f"✗ Configuration change did not persist. Expected {test_interval}, got {verify_config.get('boost_interval_hours')}")
                return False
        else:
            print(f"✗ Failed to save configuration: {save_result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"✗ Error during configuration save test: {e}")
        return False

def test_config_validation():
    """Test configuration validation"""
    print("\nTesting configuration validation...")
    
    try:
        # Get current config
        response = requests.get(STATUS_API_URL)
        response.raise_for_status()
        current_config = response.json().get('config', {})
        
        # Test invalid configuration (negative interval)
        invalid_config = current_config.copy()
        invalid_config['boost_interval_hours'] = -1
        
        save_response = requests.put(CONFIG_API_URL, json=invalid_config)
        
        # This should either reject the invalid config or handle it gracefully
        if save_response.status_code >= 400:
            print("✓ Invalid configuration properly rejected")
            return True
        else:
            save_result = save_response.json()
            if not save_result.get('success'):
                print("✓ Invalid configuration handled gracefully")
                return True
            else:
                print("⚠ Warning: Invalid configuration was accepted (may be handled by frontend)")
                return True
                
    except Exception as e:
        print(f"✗ Error during configuration validation test: {e}")
        return False

def main():
    """Run all configuration tests"""
    print("Shopee Auto Boost Plugin Configuration Fix Test")
    print("=" * 50)
    
    tests = [
        ("Configuration Loading", test_config_loading),
        ("Configuration Saving", test_config_saving),
        ("Configuration Validation", test_config_validation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"✗ {test_name} failed")
    
    print(f"\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The configuration fix is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the plugin implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)