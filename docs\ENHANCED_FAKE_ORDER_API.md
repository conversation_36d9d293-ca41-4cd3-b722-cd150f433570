# Enhanced Fake Order API Documentation

## Overview

The Enhanced Fake Order API provides comprehensive endpoints for creating, managing, and processing fake orders for testing purposes. This API is part of the Universal Fake Order System and offers advanced configuration options, batch processing, template management, and order lifecycle management.

## Base URL

All endpoints are prefixed with `/api/fake-orders`

## Authentication

All endpoints (except health check) require API key authentication via the `X-API-Key` header.

```http
X-API-Key: your_api_key_here
```

## Endpoints

### 1. Health Check

**GET** `/api/fake-orders/health`

Check the health status of the fake order system.

**Authentication:** Not required

**Response:**
```json
{
  "status": "healthy",
  "service": "fake_order_system",
  "components": {
    "fake_order_generator": "operational",
    "product_template_engine": "operational",
    "order_data_factory": "operational"
  },
  "statistics": {
    "available_templates": 9,
    "total_fake_orders": 0
  },
  "checked_at": "2025-01-15T10:30:00Z"
}
```

### 2. Generate Single Fake Order

**POST** `/api/fake-orders/generate`

Create a single fake order with advanced configuration options.

**Request Body:**
```json
{
  "order_sn": "FAKE_ORDER_001",  // optional, will be generated if not provided
  "product_config": {
    "var_sku": "canva_30",
    "quantity": 1,
    "price_override": 15.00,  // optional
    "custom_metadata": {}     // optional
  },
  "buyer_config": {
    "username": "test_user",
    "name": "Test Customer",
    "phone": "+60123456789",
    "email": "<EMAIL>",
    "address": "123 Test Street, Test City"
  },
  "order_config": {
    "status": "To Ship",
    "payment_status": "paid",
    "created_date": "2025-01-15T10:30:00Z",  // optional
    "custom_timestamps": {},                  // optional
    "custom_metadata": {}                     // optional
  },
  "test_scenario": "canva_pro_basic_flow"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Fake order FAKE_ORDER_001 created successfully",
  "order_data": {
    "message": "Fake order details fetched successfully",
    "data": {
      "order_sn": "FAKE_ORDER_001",
      "buyer_user": {
        "user_name": "test_user"
      },
      "order_items": [...],
      "total_price": 15.00,
      "is_fake_order": true,
      "fake_order_metadata": {...}
    }
  },
  "order_sn": "FAKE_ORDER_001",
  "processing_options": {
    "process_order_url": "/api/order/process_order",
    "get_order_details_url": "/api/order/get_order_details?order_sn=FAKE_ORDER_001",
    "get_order_status_url": "/api/order/get_order_status?order_sn=FAKE_ORDER_001"
  },
  "validation_warnings": [],
  "generated_at": "2025-01-15T10:30:00Z"
}
```

### 3. Batch Generate Fake Orders

**POST** `/api/fake-orders/batch-generate`

Create multiple fake orders in a single request.

**Request Body:**
```json
{
  "orders": [
    {
      "order_sn": "FAKE_BATCH_001",  // optional
      "product_config": { "var_sku": "canva_30", "quantity": 1 },
      "buyer_config": { "username": "user1" },
      "order_config": { "status": "To Ship" },
      "test_scenario": "batch_test_1"
    },
    {
      "product_config": { "var_sku": "netflix_30", "quantity": 1 },
      "buyer_config": { "username": "user2" },
      "test_scenario": "batch_test_2"
    }
  ],
  "batch_config": {
    "continue_on_error": true,
    "generate_unique_order_sns": true,
    "default_test_scenario": "batch_testing"
  }
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Batch generation completed: 2 successful, 0 failed",
  "batch_summary": {
    "total_requested": 2,
    "successful": 2,
    "failed": 0,
    "success_rate": 100.0
  },
  "successful_orders": [
    {
      "index": 0,
      "success": true,
      "order_data": {...},
      "order_sn": "FAKE_BATCH_001"
    }
  ],
  "failed_orders": [],
  "processing_options": {
    "bulk_process_url": "/api/order/process_order",
    "batch_status_check": "/api/fake-orders/list"
  },
  "generated_at": "2025-01-15T10:30:00Z"
}
```

### 4. Get Product Templates

**GET** `/api/fake-orders/templates`

Retrieve available product templates for fake order generation.

**Query Parameters:**
- `category` (optional): Filter by category (gaming, streaming, design, security)
- `include_sample_data` (optional): Include sample data in response (default: false)

**Response:**
```json
{
  "success": true,
  "templates": {
    "canva_30": {
      "var_sku": "canva_30",
      "product_name": "Canva Pro 30 Days",
      "category": "design",
      "default_price": 15.00,
      "metadata_schema": {...},
      "validation_rules": {...}
    },
    "netflix_30": {
      "var_sku": "netflix_30",
      "product_name": "Netflix Premium 30 Days",
      "category": "streaming",
      "default_price": 35.00,
      "metadata_schema": {...},
      "validation_rules": {...}
    }
  },
  "summary": {
    "total_templates": 9,
    "categories": {
      "gaming": [...],
      "streaming": [...],
      "design": [...],
      "security": [...]
    },
    "all_skus": [...]
  },
  "filters_applied": {
    "category": null,
    "include_sample_data": false
  },
  "retrieved_at": "2025-01-15T10:30:00Z"
}
```

### 5. Save Custom Template

**POST** `/api/fake-orders/templates/save`

Save a custom product template for fake order generation.

**Request Body:**
```json
{
  "var_sku": "custom_product_123",
  "product_name": "Custom Product Name",
  "category": "custom",
  "default_price": 25.00,
  "metadata_schema": {
    "custom_field": {"type": "str", "allowed_values": ["value1", "value2"]}
  },
  "validation_rules": {
    "required_fields": ["custom_field"],
    "optional_fields": []
  },
  "sample_data": {
    "custom_field": "value1",
    "description": "Sample custom product"
  }
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Custom template 'custom_product_123' saved successfully",
  "template": {...},
  "saved_at": "2025-01-15T10:30:00Z"
}
```

### 6. List Fake Orders

**GET** `/api/fake-orders/list`

List fake orders with filtering and pagination options.

**Query Parameters:**
- `limit` (optional): Maximum number of orders to return (default: 50, max: 200)
- `status` (optional): Filter by order status
- `var_sku` (optional): Filter by product SKU
- `test_scenario` (optional): Filter by test scenario
- `created_after` (optional): Filter orders created after this date (ISO format)
- `created_before` (optional): Filter orders created before this date (ISO format)

**Response:**
```json
{
  "success": true,
  "fake_orders": [
    {
      "order_sn": "FAKE_ORDER_001",
      "var_sku": "canva_30",
      "buyer_username": "test_user",
      "status": "To Ship",
      "created_at": "2025-01-15T10:30:00Z",
      "is_fake_order": true,
      "test_scenario": "canva_pro_basic_flow"
    }
  ],
  "count": 1,
  "filters_applied": {
    "limit": 50,
    "status": null,
    "var_sku": null,
    "test_scenario": null,
    "created_after": null,
    "created_before": null
  },
  "management_options": {
    "bulk_delete_url": "/api/fake-orders/cleanup",
    "individual_delete_url": "/api/order/delete_fake_order/{order_sn}",
    "process_order_url": "/api/order/process_order"
  },
  "retrieved_at": "2025-01-15T10:30:00Z"
}
```

### 7. Cleanup Fake Orders

**DELETE** `/api/fake-orders/cleanup`

Bulk delete fake orders based on various criteria.

**Query Parameters (for date-based cleanup):**
- `older_than_days` (optional): Delete orders older than specified days (default: 7)
- `status` (optional): Delete orders with specific status
- `test_scenario` (optional): Delete orders with specific test scenario
- `confirm`: Must be 'true' to proceed with deletion

**Or JSON payload for specific order SNs:**
```json
{
  "order_sns": ["FAKE_ORDER_001", "FAKE_ORDER_002"],
  "confirm": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Cleaned up 5 fake orders older than 7 days",
  "cleanup_summary": {
    "original_count": 10,
    "deleted_count": 5,
    "remaining_count": 5,
    "deletion_method": "date_based"
  },
  "cleaned_at": "2025-01-15T10:30:00Z"
}
```

## Available Product Templates

The system comes with built-in templates for common digital products:

### Gaming
- `steam_auth_code`: Steam Authentication Code
- `text_based`: Steam Account (Text Based)

### Streaming
- `netflix_30`: Netflix Premium 30 Days
- `netflix_60`: Netflix Premium 60 Days
- `hulu_10`: Hulu Account 10 Days

### Design
- `canva_30`: Canva Pro 30 Days
- `canva_lifetime`: Canva Pro Lifetime

### Security
- `vpn_monthly`: VPN Service Monthly
- `vpn_yearly`: VPN Service Yearly

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error description",
  "error_code": "ERROR_CODE",
  "additional_info": {...}
}
```

Common error codes:
- `MISSING_PAYLOAD`: JSON payload is required
- `VALIDATION_FAILED`: Order configuration validation failed
- `GENERATION_FAILED`: Failed to generate fake order
- `CONFIRMATION_REQUIRED`: Confirmation required for destructive operations
- `INVALID_PARAMETER`: Invalid query parameter or request data

## Integration with Existing Order System

Fake orders generated through this API are fully compatible with the existing order processing system:

1. **Order Processing**: Use `/api/order/process_order` with the generated order_sn
2. **Order Details**: Use `/api/order/get_order_details?order_sn={order_sn}`
3. **Order Status**: Use `/api/order/get_order_status?order_sn={order_sn}`

## Testing and Development

### Test Script

A test script is provided at `test_fake_order_api.py` to verify API functionality:

```bash
python test_fake_order_api.py
```

### Example Usage

```python
import requests

# Generate a fake order
response = requests.post('http://localhost:5000/api/fake-orders/generate', 
    headers={'X-API-Key': 'your_api_key'},
    json={
        'product_config': {'var_sku': 'canva_30'},
        'test_scenario': 'api_test'
    })

order_sn = response.json()['order_sn']

# Process the fake order
process_response = requests.post('http://localhost:5000/api/order/process_order',
    json={'order_sn': order_sn})
```

## Security Considerations

1. **API Key Authentication**: All endpoints require valid API key
2. **Rate Limiting**: Standard rate limiting applies to all endpoints
3. **Fake Order Isolation**: Fake orders are clearly marked and isolated from real orders
4. **Audit Logging**: All fake order operations are logged for audit purposes
5. **Cleanup**: Automatic and manual cleanup options prevent data accumulation

## Best Practices

1. **Use Test Scenarios**: Always specify meaningful test_scenario values for tracking
2. **Cleanup Regularly**: Use the cleanup endpoint to remove old test data
3. **Validate Templates**: Use the templates endpoint to verify available SKUs
4. **Batch Processing**: Use batch generation for creating multiple test orders efficiently
5. **Monitor Health**: Use the health endpoint to verify system status

## Support

For issues or questions about the Enhanced Fake Order API, please refer to the main documentation or contact the development team.