"""
Shopee Messaging Service

Service for integrating with existing ShopeeAPI to send account details,
reassignment notifications, and expiration warnings to customers.
"""

import sys
import os
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from .base_service import BaseService
from ..models.chatgpt_account import ChatGPTAccount
from ..models.order_redemption import OrderRedemption
from ..models.utils import format_duration, ValidationError


class ShopeeMessagingService(BaseService):
    """
    Service for sending messages via Shopee chat
    
    Provides functionality for:
    - Sending ChatGPT account details to customers
    - Sending reassignment notifications
    - Sending expiration warnings
    - Message formatting and templating
    - Integration with ShopeeAPI client
    """
    
    def __init__(self, config: Dict[str, Any], logger=None):
        super().__init__(config, logger)
        self.service_name = "ShopeeMessagingService"
        
        # Configuration
        self.shopee_config = self._get_config_value('shopee_config', {})
        self.message_config = self._get_config_value('message_config', {})
        
        # Message settings
        self.enable_messaging = self.shopee_config.get('enable_messaging', True)
        self.auto_send_account_details = self.message_config.get('auto_send_account_details', True)
        self.auto_send_expiration_warnings = self.message_config.get('auto_send_expiration_warnings', True)
        self.expiration_warning_days = self.message_config.get('expiration_warning_days', [7, 3, 1])
        
        # Message templates
        self.message_templates = self._get_config_value('message_templates', {})
        
        # ShopeeAPI client
        self._shopee_api = None
        self._api_available = False
    
    def initialize(self) -> bool:
        """Initialize the Shopee Messaging Service"""
        try:
            self.logger.info(f"Initializing {self.service_name}...")
            
            # Validate configuration
            if not self._validate_service_config():
                return False
            
            # Initialize ShopeeAPI client
            if self.enable_messaging:
                self._initialize_shopee_api()
            
            self._mark_initialized()
            return True
            
        except Exception as e:
            self._handle_service_error("initialize", e)
            return False
    
    def shutdown(self) -> bool:
        """Shutdown the Shopee Messaging Service"""
        try:
            self.logger.info(f"Shutting down {self.service_name}...")
            
            # Clean up ShopeeAPI client
            self._shopee_api = None
            self._api_available = False
            
            self._mark_shutdown()
            return True
            
        except Exception as e:
            self._handle_service_error("shutdown", e)
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        self._update_health_check_time()
        
        try:
            health_data = {
                'status': 'healthy',
                'service': self.service_name,
                'messaging_enabled': self.enable_messaging,
                'api_available': self._api_available,
                'auto_send_account_details': self.auto_send_account_details,
                'auto_send_expiration_warnings': self.auto_send_expiration_warnings,
                'timestamp': datetime.now().isoformat()
            }
            
            # Check ShopeeAPI availability
            if self.enable_messaging and not self._api_available:
                health_data['status'] = 'degraded'
                health_data['issues'] = ['ShopeeAPI not available']
            
            return health_data
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': self.service_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def send_account_details(self, order_id: str, buyer_username: str, 
                           account: ChatGPTAccount, redemption: OrderRedemption) -> Dict[str, Any]:
        """
        Send ChatGPT account details to customer via Shopee
        
        Args:
            order_id: Shopee order ID
            buyer_username: Buyer's username
            account: ChatGPT account details
            redemption: Order redemption details
            
        Returns:
            Dictionary with send result
        """
        try:
            if not self.enable_messaging:
                return {
                    'success': False,
                    'error': 'Messaging is disabled'
                }
            
            if not self._api_available:
                return {
                    'success': False,
                    'error': 'ShopeeAPI not available'
                }
            
            # Format account details message
            message = self._format_account_details_message(account, redemption)
            
            # Send message via ShopeeAPI
            result = self._send_shopee_message(buyer_username, message)
            
            if result['success']:
                self._log_operation("send_account_details", {
                    'order_id': order_id,
                    'buyer_username': buyer_username,
                    'account_id': account.account_id,
                    'redemption_id': redemption.redemption_id
                })
            
            return result
            
        except Exception as e:
            self._handle_service_error("send_account_details", e)
            return {
                'success': False,
                'error': f'Failed to send account details: {str(e)}'
            }
    
    def send_reassignment_notification(self, order_id: str, buyer_username: str,
                                     old_account: ChatGPTAccount, new_account: ChatGPTAccount,
                                     reason: str = "") -> Dict[str, Any]:
        """
        Send account reassignment notification to customer
        
        Args:
            order_id: Shopee order ID
            buyer_username: Buyer's username
            old_account: Previous account details
            new_account: New account details
            reason: Reason for reassignment
            
        Returns:
            Dictionary with send result
        """
        try:
            if not self.enable_messaging:
                return {
                    'success': False,
                    'error': 'Messaging is disabled'
                }
            
            if not self._api_available:
                return {
                    'success': False,
                    'error': 'ShopeeAPI not available'
                }
            
            # Format reassignment message
            message = self._format_reassignment_message(old_account, new_account, reason)
            
            # Send message via ShopeeAPI
            result = self._send_shopee_message(buyer_username, message)
            
            if result['success']:
                self._log_operation("send_reassignment_notification", {
                    'order_id': order_id,
                    'buyer_username': buyer_username,
                    'old_account_id': old_account.account_id,
                    'new_account_id': new_account.account_id,
                    'reason': reason
                })
            
            return result
            
        except Exception as e:
            self._handle_service_error("send_reassignment_notification", e)
            return {
                'success': False,
                'error': f'Failed to send reassignment notification: {str(e)}'
            }
    
    def send_expiration_warning(self, order_id: str, buyer_username: str,
                              account: ChatGPTAccount, days_remaining: int) -> Dict[str, Any]:
        """
        Send expiration warning to customer
        
        Args:
            order_id: Shopee order ID
            buyer_username: Buyer's username
            account: ChatGPT account details
            days_remaining: Days until expiration
            
        Returns:
            Dictionary with send result
        """
        try:
            if not self.enable_messaging:
                return {
                    'success': False,
                    'error': 'Messaging is disabled'
                }
            
            if not self._api_available:
                return {
                    'success': False,
                    'error': 'ShopeeAPI not available'
                }
            
            # Format expiration warning message
            message = self._format_expiration_warning_message(account, days_remaining)
            
            # Send message via ShopeeAPI
            result = self._send_shopee_message(buyer_username, message)
            
            if result['success']:
                self._log_operation("send_expiration_warning", {
                    'order_id': order_id,
                    'buyer_username': buyer_username,
                    'account_id': account.account_id,
                    'days_remaining': days_remaining
                })
            
            return result
            
        except Exception as e:
            self._handle_service_error("send_expiration_warning", e)
            return {
                'success': False,
                'error': f'Failed to send expiration warning: {str(e)}'
            }
    
    def send_custom_message(self, buyer_username: str, message: str) -> Dict[str, Any]:
        """
        Send custom message to customer
        
        Args:
            buyer_username: Buyer's username
            message: Custom message text
            
        Returns:
            Dictionary with send result
        """
        try:
            if not self.enable_messaging:
                return {
                    'success': False,
                    'error': 'Messaging is disabled'
                }
            
            if not self._api_available:
                return {
                    'success': False,
                    'error': 'ShopeeAPI not available'
                }
            
            # Send message via ShopeeAPI
            result = self._send_shopee_message(buyer_username, message)
            
            if result['success']:
                self._log_operation("send_custom_message", {
                    'buyer_username': buyer_username,
                    'message_length': len(message)
                })
            
            return result
            
        except Exception as e:
            self._handle_service_error("send_custom_message", e)
            return {
                'success': False,
                'error': f'Failed to send custom message: {str(e)}'
            }
    
    def _initialize_shopee_api(self) -> None:
        """Initialize ShopeeAPI client"""
        try:
            # Import ShopeeAPI client
            shopee_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'services')
            if shopee_api_path not in sys.path:
                sys.path.append(shopee_api_path)
            
            from shopee_api_client import ShopeeAPIClient
            
            # Initialize client
            self._shopee_api = ShopeeAPIClient()
            self._api_available = True
            
            self.logger.info("ShopeeAPI client initialized successfully")
            
        except ImportError as e:
            self.logger.warning(f"ShopeeAPI client not available: {e}")
            self._api_available = False
        except Exception as e:
            self.logger.error(f"Failed to initialize ShopeeAPI client: {e}")
            self._api_available = False
    
    def _send_shopee_message(self, username: str, message: str) -> Dict[str, Any]:
        """
        Send message via ShopeeAPI
        
        Args:
            username: Recipient username
            message: Message text
            
        Returns:
            Dictionary with send result
        """
        try:
            if not self._shopee_api:
                return {
                    'success': False,
                    'error': 'ShopeeAPI client not initialized'
                }
            
            # Prepare message payload
            payload = {
                'text': message,
                'username': username,
                'force_send_cancel_order_warning': False,
                'comply_cancel_order_warning': True
            }
            
            # Send message
            response, status_code = self._shopee_api.send_chat_message(payload)
            
            if status_code == 200:
                return {
                    'success': True,
                    'message_id': response.get('data', {}).get('message_id'),
                    'message_parts_sent': response.get('message_parts_sent', 1),
                    'response': response
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Unknown error'),
                    'status_code': status_code
                }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Message send error: {str(e)}'
            }
    
    def _format_account_details_message(self, account: ChatGPTAccount, redemption: OrderRedemption) -> str:
        """Format account details message"""
        template = self.message_templates.get('account_details', """
🎉 Your ChatGPT Plus Account is Ready!

📧 Email: {email}
🔑 Password: {password}
📅 Valid Until: {expiration_date}
👥 Shared Users: {current_users}/{max_users}

Order ID: {order_id}
Redemption ID: {redemption_id}

⚠️ Important Notes:
- This account is shared with other users
- Please do not change the password
- Account expires on {expiration_date}
- Contact support if you have any issues

Thank you for your purchase! 🙏
        """).strip()
        
        return template.format(
            email=account.email,
            password=account.password,
            expiration_date=account.expiration_date.strftime('%Y-%m-%d %H:%M'),
            current_users=account.current_users,
            max_users=account.max_concurrent_users,
            order_id=redemption.order_id,
            redemption_id=redemption.redemption_id
        )
    
    def _format_reassignment_message(self, old_account: ChatGPTAccount, 
                                   new_account: ChatGPTAccount, reason: str) -> str:
        """Format reassignment notification message"""
        template = self.message_templates.get('reassignment', """
🔄 Account Reassignment Notice

Your ChatGPT Plus account has been reassigned.

🆕 New Account Details:
📧 Email: {new_email}
🔑 Password: {new_password}
📅 Valid Until: {new_expiration}

📝 Reason: {reason}

⚠️ Please update your login details and stop using the previous account.

If you have any questions, please contact support.
        """).strip()
        
        return template.format(
            new_email=new_account.email,
            new_password=new_account.password,
            new_expiration=new_account.expiration_date.strftime('%Y-%m-%d %H:%M'),
            reason=reason or "Account optimization"
        )
    
    def _format_expiration_warning_message(self, account: ChatGPTAccount, days_remaining: int) -> str:
        """Format expiration warning message"""
        template = self.message_templates.get('expiration_warning', """
⏰ Account Expiration Warning

Your ChatGPT Plus account will expire in {days_remaining} day(s).

📧 Account: {email}
📅 Expires: {expiration_date}

⚠️ After expiration, you will lose access to this account.

If you need to extend your subscription, please place a new order.

Thank you for using our service! 🙏
        """).strip()
        
        return template.format(
            days_remaining=days_remaining,
            email=account.email,
            expiration_date=account.expiration_date.strftime('%Y-%m-%d %H:%M')
        )
    
    def _validate_service_config(self) -> bool:
        """Validate service configuration"""
        # Check expiration warning days
        if not isinstance(self.expiration_warning_days, list):
            self.logger.error("expiration_warning_days must be a list")
            return False
        
        for days in self.expiration_warning_days:
            if not isinstance(days, int) or days < 0:
                self.logger.error(f"Invalid expiration warning days: {days}")
                return False
        
        return True
