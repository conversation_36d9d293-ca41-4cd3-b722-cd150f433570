"""
Netflix Routes Implementation
Handles all Netflix-related API endpoints
"""

import logging
from flask import Blueprint, request, jsonify
from typing import Optional

logger = logging.getLogger(__name__)

def create_netflix_blueprint(signin_service, session_service, order_service):
    """Create and configure Netflix blueprint with services"""
    
    netflix_bp = Blueprint('netflix', __name__)
    
    @netflix_bp.route('/signin_code', methods=['POST'])
    def get_signin_code():
        """Get Netflix sign-in code"""
        try:
            data = request.get_json() or {}
            order_id = data.get('order_id')
            
            if not order_id:
                return jsonify({
                    'status': 'error',
                    'message': 'order_id is required'
                }), 400
            
            result = signin_service.get_signin_code(order_id)
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"Error getting signin code: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500
    
    @netflix_bp.route('/sessions', methods=['GET'])
    def get_sessions():
        """Get Netflix sessions"""
        try:
            sessions = session_service.get_all_sessions()
            return jsonify({
                'status': 'success',
                'sessions': sessions
            })
            
        except Exception as e:
            logger.error(f"Error getting sessions: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500
    
    @netflix_bp.route('/sessions/<session_id>', methods=['GET'])
    def get_session(session_id):
        """Get specific Netflix session"""
        try:
            session = session_service.get_session(session_id)
            if session:
                return jsonify({
                    'status': 'success',
                    'session': session
                })
            else:
                return jsonify({
                    'status': 'not_found',
                    'message': 'Session not found'
                }), 404
                
        except Exception as e:
            logger.error(f"Error getting session {session_id}: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500
    
    @netflix_bp.route('/orders', methods=['POST'])
    def create_order():
        """Create Netflix order"""
        try:
            data = request.get_json() or {}
            order_id = data.get('order_id')
            email = data.get('email')
            
            if not order_id or not email:
                return jsonify({
                    'status': 'error',
                    'message': 'order_id and email are required'
                }), 400
            
            result = order_service.create_order(order_id, email)
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500
    
    @netflix_bp.route('/orders/<order_id>', methods=['GET'])
    def get_order(order_id):
        """Get Netflix order"""
        try:
            order = order_service.get_order(order_id)
            if order:
                return jsonify({
                    'status': 'success',
                    'order': order
                })
            else:
                return jsonify({
                    'status': 'not_found',
                    'message': 'Order not found'
                }), 404
                
        except Exception as e:
            logger.error(f"Error getting order {order_id}: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500
    
    @netflix_bp.route('/orders/<order_id>/complete', methods=['POST'])
    def complete_order(order_id):
        """Complete Netflix order"""
        try:
            result = order_service.complete_order(order_id)
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"Error completing order {order_id}: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500
    
    @netflix_bp.route('/health', methods=['GET'])
    def health_check():
        """Netflix plugin health check"""
        return jsonify({
            'status': 'healthy',
            'plugin': 'netflix',
            'version': '1.0.0'
        })
    
    return netflix_bp
