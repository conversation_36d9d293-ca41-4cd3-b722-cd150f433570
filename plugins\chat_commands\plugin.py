"""
Chat Commands Plugin for SteamCodeTool

This plugin provides automated chat command responses for Shopee conversations.
It subscribes to ShopeeAPI webhooks and responds to commands like #android_help.
"""

import os
import logging
import traceback
from typing import Dict, Any, Optional
from flask import Blueprint

from core.plugin_manager import PluginInterface
from .services import ChatCommandService, MessageProcessor
from .routes import create_routes
from .webhook_integration import WebhookIntegration
from .log_filter import setup_log_filtering, remove_log_filtering

logger = logging.getLogger(__name__)


class Plugin(PluginInterface):
    """Chat Commands Plugin"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "Chat Commands"
        self.version = "1.0.0"
        self.description = "Automated chat command responses for Shopee conversations"
        self.dependencies = []
        self.url_prefix = "/chat-commands"
        
        # Plugin directory
        self.plugin_dir = os.path.dirname(__file__)
        
        # Services
        self.command_service: Optional[ChatCommandService] = None
        self.message_processor: Optional[MessageProcessor] = None
        self.blueprint: Optional[Blueprint] = None
        self.webhook_integration: Optional[WebhookIntegration] = None

        # ShopeeAPI client reference
        self.shopee_api_client = None
        self.vpn_config_generator_plugin = None
    
    def initialize(self):
        """Initialize the plugin"""
        try:
            logger.info("Initializing Chat Commands plugin")

            # Get VPN config generator plugin
            if self.plugin_manager:
                self.vpn_config_generator_plugin = self.plugin_manager.get_plugin("vpn_config_generator")
                if self.vpn_config_generator_plugin:
                    logger.info(f"ChatCommands Plugin: Successfully got vpn_config_generator_plugin: {self.vpn_config_generator_plugin.name}")
                    if hasattr(self.vpn_config_generator_plugin, 'config_service') and self.vpn_config_generator_plugin.config_service:
                        logger.info(f"ChatCommands Plugin: vpn_config_generator_plugin has config_service.")
                        if hasattr(self.vpn_config_generator_plugin.config_service, 'get_api_config'):
                             logger.info(f"ChatCommands Plugin: vpn_config_generator_plugin.config_service has get_api_config method.")
                        else:
                             logger.warning(f"ChatCommands Plugin: vpn_config_generator_plugin.config_service does NOT have get_api_config method.")
                    else:
                        logger.warning(f"ChatCommands Plugin: vpn_config_generator_plugin does NOT have a config_service or it is None.")
                else:
                    logger.warning("ChatCommands Plugin: Failed to get vpn_config_generator_plugin. It is None.")
            else:
                logger.warning("ChatCommands Plugin: Plugin manager not available, cannot get vpn_config_generator_plugin.")

            # Setup log filtering first
            setup_log_filtering(self.plugin_dir)

            # Initialize services
            self.command_service = ChatCommandService(self.plugin_dir, self.plugin_manager.app)

            # Initialize message processor
            self.message_processor = MessageProcessor(self.command_service)

            # Setup ShopeeAPI client with configured URL
            self._setup_shopee_api_client()

            # Initialize webhook integration
            self.webhook_integration = WebhookIntegration(self)

            # Setup webhook integration with configured URL
            webhook_config = self.command_service.get_webhook_config()
            if webhook_config and webhook_config.shopee_api_base_url:
                self.webhook_integration.setup_integration(webhook_config.shopee_api_base_url)
            else:
                # Try auto-detection
                self.webhook_integration.setup_integration()

            # Create routes
            self.blueprint = create_routes(self)

            # Register webhook if auto-register is enabled
            self._register_webhook()

            logger.info("Chat Commands plugin initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Chat Commands plugin: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def shutdown(self) -> bool:
        """Shutdown the plugin"""
        try:
            logger.info("Shutting down Chat Commands Plugin")
            
            # Unregister webhook if needed
            self._unregister_webhook()

            # Remove log filtering
            remove_log_filtering()

            # Clean up resources
            self.command_service = None
            self.message_processor = None
            self.blueprint = None
            self.webhook_integration = None
            self.shopee_api_client = None
            
            logger.info("Chat Commands Plugin shut down successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error shutting down Chat Commands Plugin: {e}")
            return False
    
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return Flask blueprint for this plugin's routes"""
        return self.blueprint

    def register_external_command(self, command, plugin_name: str, handler_callback=None) -> bool:
        """Allow external plugins to register commands"""
        if self.command_service:
            return self.command_service.register_external_command(command, plugin_name, handler_callback)
        return False

    def unregister_external_command(self, command_name: str, plugin_name: str) -> bool:
        """Allow external plugins to unregister commands"""
        if self.command_service:
            return self.command_service.unregister_external_command(command_name, plugin_name)
        return False
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return configuration schema for this plugin"""
        return {
            "type": "object",
            "properties": {
                "enabled": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable/disable the chat commands plugin"
                },
                "webhook_enabled": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable/disable webhook processing"
                },
                "auto_response_enabled": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable/disable automatic responses"
                },

            }
        }
    
    def load_config(self, config: Dict[str, Any]):
        """Load plugin configuration"""
        super().load_config(config)

        # Update services with new config if they exist
        if self.command_service:
            # Reload service configuration
            self.command_service.load_config()
    
    def _setup_shopee_api_client(self):
        """Setup ShopeeAPI client reference"""
        try:
            # Try to import and use the ShopeeAPI client
            from services.shopee_api_client import ShopeeAPIClient
            
            # Get the base URL from webhook config - no hardcoded defaults
            if not self.command_service:
                logger.error("Command service not available, cannot setup ShopeeAPI client")
                return
                
            webhook_config = self.command_service.get_webhook_config()
            if not webhook_config or not webhook_config.shopee_api_base_url:
                logger.error("Shopee API base URL not configured in webhook settings")
                return
            
            base_url = webhook_config.shopee_api_base_url
            logger.info(f"Initializing ShopeeAPIClient with configured base_url: {base_url}")
            
            # Create the client instance with the configured base URL
            self.shopee_api_client = ShopeeAPIClient(base_url=base_url)
            
            # Update message processor with the configured client
            if self.message_processor:
                self.message_processor.shopee_api_client = self.shopee_api_client
                
            logger.info("ShopeeAPI client setup successful")
            
        except ImportError:
            logger.warning("ShopeeAPI client not available, responses will not be sent automatically")
        except Exception as e:
            logger.error(f"Could not setup ShopeeAPI client: {e}")
    
    def _register_webhook(self):
        """Register webhook endpoint with ShopeeAPI"""
        try:
            if self.webhook_integration:
                success = self.webhook_integration.register_webhook()
                if success:
                    logger.info("Webhook registered successfully with ShopeeAPI")
                else:
                    logger.warning("Failed to register webhook with ShopeeAPI")

            # Also log our local endpoint
            webhook_url = f"{self.url_prefix}/api/webhook"
            logger.info(f"Chat Commands webhook endpoint available at: {webhook_url}")

        except Exception as e:
            logger.error(f"Error registering webhook: {e}")

    def _unregister_webhook(self):
        """Unregister webhook endpoint"""
        try:
            if self.webhook_integration:
                success = self.webhook_integration.unregister_webhook()
                if success:
                    logger.info("Webhook unregistered successfully")
                else:
                    logger.warning("Failed to unregister webhook")
        except Exception as e:
            logger.error(f"Error unregistering webhook: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Return plugin status information"""
        status = super().get_status()
        
        if self.command_service:
            commands = self.command_service.get_all_commands()
            status.update({
                'commands_loaded': len(commands),
                'shopee_api_available': self.shopee_api_client is not None,
                'webhook_endpoint': f"{self.url_prefix}/api/webhook"
            })

        if self.webhook_integration:
            status.update(self.webhook_integration.get_status())
        
        return status
    
    def process_webhook_message(self, webhook_data: Dict[str, Any]) -> bool:
        """Process incoming webhook message (can be called externally)"""
        try:
            if not self.message_processor:
                return False
            
            responses = self.message_processor.process_webhook_message(webhook_data)
            
            if responses and self.shopee_api_client:
                # Send responses
                sender_name = None
                success_count = 0
                
                for response in responses:
                    # Extract sender info from webhook using the same logic as WebhookMessage (only once)
                    if sender_name is None:
                        # Try to get sender name from the new webhook format
                        if webhook_data.get('type') == 'shopee_message':
                            data = webhook_data.get('data', {})
                            message_content = data.get('message_content', '')
                            if isinstance(message_content, str):
                                try:
                                    import json
                                    parsed_content = json.loads(message_content)

                                    # Determine the correct sender name based on message direction
                                    send_by_yourself = parsed_content.get('send_by_yourself', False)

                                    if send_by_yourself:
                                        # This is a message we sent, so the customer is the recipient (to_user_name)
                                        sender_name = parsed_content.get('to_user_name', '')
                                    else:
                                        # This is a message we received, so the customer is the sender (from_user_name)
                                        sender_name = parsed_content.get('from_user_name', '')

                                    # Fallback to from_name if no username found
                                    if not sender_name:
                                        sender_name = parsed_content.get('from_name', '')

                                except json.JSONDecodeError:
                                    logger.warning("Failed to parse message_content for sender info in plugin")

                        # Fallback to legacy format
                        if not sender_name:
                            message = webhook_data.get('message', {})
                            # For legacy format, also check send_by_yourself
                            send_by_yourself = message.get('send_by_yourself', False)
                            if send_by_yourself:
                                sender_name = message.get('to_user_name', '') or message.get('to_name', '')
                            else:
                                sender_name = message.get('from_user_name', '') or message.get('from_name', '')

                        # Final fallback
                        if not sender_name:
                            sender_name = 'unknown_user'
                            logger.warning("Could not determine sender name in plugin, using fallback")

                    # Get command configuration to check send order
                    command_config = self.command_service.get_command_config()
                    send_images_first = command_config.send_images_first if command_config else False

                    # Define functions for sending text and images
                    def send_text():
                        if response.text:
                            result = self.shopee_api_client.send_chat_message({
                                'username': sender_name,
                                'text': response.text
                            })
                            if result[1] == 200:
                                nonlocal success_count
                                success_count += 1

                    def send_images():
                        for image_url in response.image_urls:
                            result = self.shopee_api_client.send_image_message({
                                'username': sender_name,
                                'image_url': image_url
                            })
                            if result[1] == 200:
                                nonlocal success_count
                                success_count += 1

                    # Send in configured order
                    if send_images_first:
                        send_images()
                        send_text()
                    else:
                        send_text()
                        send_images()
                
                # Mark conversation as unread AFTER successfully sending all responses
                if success_count > 0 and sender_name and sender_name != 'unknown_user':
                    try:
                        logger.info(f"Marking conversation with {sender_name} as unread after sending {success_count} responses")
                        
                        result = self.shopee_api_client.set_conversation_unread_by_username(sender_name)
                        if result[1] == 200:
                            logger.info(f"Successfully marked conversation with {sender_name} as unread")
                        else:
                            logger.warning(f"Failed to mark conversation as unread for {sender_name}: HTTP {result[1]} - {result[0]}")
                    except Exception as e:
                        logger.error(f"Error marking conversation as unread for {sender_name}: {e}")
                        import traceback
                        logger.error(f"Unread marking error details: {traceback.format_exc()}")
                
                return True
            
            return responses is not None
            
        except Exception as e:
            logger.error(f"Error processing webhook message: {e}")
            return False
