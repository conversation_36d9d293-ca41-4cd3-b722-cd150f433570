#!/usr/bin/env python3
"""
Test script for VPN SKU validity configuration
Tests the validity days extraction from configuration and SKU patterns
"""

import sys
import os
import logging

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_validity_extraction():
    """Test validity days extraction for various SKUs"""
    print("=" * 80)
    print("VPN SKU VALIDITY DAYS EXTRACTION TEST")
    print("=" * 80)
    
    test_skus = [
        # Malaysia Basic
        'my_', 'my_15', 'my_30', 'my_60', 'my_90',
        
        # Malaysia Standard
        'my_standard', 'my_standard_15', 'my_standard_30', 'my_standard_60',
        
        # Malaysia Premium
        'my_premium', 'my_premium_15', 'my_premium_30', 'my_premium_60', 'my_premium_90',
        
        # Malaysia High-Speed
        'my_highspeed', 'my_highspeed_15', 'my_highspeed_30', 'my_highspeed_60', 'my_highspeed_90',
        'my_highspeed_premium', 'my_highspeed_premium_30', 'my_highspeed_premium_60', 'my_highspeed_premium_90',
        
        # Singapore Basic
        'sg_', 'sg_15', 'sg_30', 'sg_60',
        
        # Singapore High-Speed
        'sg_highspeed', 'sg_highspeed_15', 'sg_highspeed_30', 'sg_highspeed_60',
        
        # Singapore Premium
        'sg_premium', 'sg_premium_15', 'sg_premium_30', 'sg_premium_60', 'sg_premium_90',
        
        # Singapore Business
        'sg_business', 'sg_business_30', 'sg_business_60', 'sg_business_90',
        
        # Unknown/Test SKUs
        'unknown_sku', 'test_45', 'new_product_120'
    ]
    
    print(f"Testing {len(test_skus)} SKUs for validity days extraction:")
    print()
    
    for sku in test_skus:
        try:
            validity_days = VPNStrategyFactory.get_validity_days_for_sku(sku)
            print(f"SKU: {sku:30} -> Validity: {validity_days:3} days")
        except Exception as e:
            print(f"SKU: {sku:30} -> ERROR: {str(e)}")
    
    print()

def test_configuration_coverage():
    """Test configuration coverage for validity mapping"""
    print("=" * 80)
    print("CONFIGURATION COVERAGE ANALYSIS")
    print("=" * 80)
    
    # Get configuration info
    config_info = VPNStrategyFactory.get_config_info()
    print(f"Configuration file: {config_info['config_file_path']}")
    print(f"Configuration loaded: {config_info['config_loaded']}")
    print(f"Total SKU mappings: {config_info['total_sku_mappings']}")
    print()
    
    # Get validity mapping
    validity_mapping = VPNStrategyFactory._get_flattened_sku_validity_mapping()
    
    print(f"SKUs with explicit validity configuration: {len(validity_mapping)}")
    print()
    
    if validity_mapping:
        print("Explicit validity mappings:")
        for sku, days in sorted(validity_mapping.items()):
            print(f"  {sku:30} -> {days:3} days")
        print()
    
    # Test some edge cases
    edge_cases = [
        ('my_', 'Basic Malaysia without number'),
        ('sg_', 'Basic Singapore without number'),
        ('my_highspeed', 'High-speed without number'),
        ('unknown_product_45', 'Unknown product with number'),
        ('test_product', 'Unknown product without number')
    ]
    
    print("Edge case testing:")
    for sku, description in edge_cases:
        validity = VPNStrategyFactory.get_validity_days_for_sku(sku)
        print(f"  {sku:25} ({description:30}) -> {validity:3} days")
    print()

def test_validity_vs_tags_consistency():
    """Test consistency between validity and tags mapping"""
    print("=" * 80)
    print("VALIDITY VS TAGS CONSISTENCY CHECK")
    print("=" * 80)
    
    # Get both mappings
    tags_mapping = VPNStrategyFactory._get_flattened_sku_mapping()
    validity_mapping = VPNStrategyFactory._get_flattened_sku_validity_mapping()
    
    print(f"SKUs with tags mapping: {len(tags_mapping)}")
    print(f"SKUs with validity mapping: {len(validity_mapping)}")
    print()
    
    # Check for SKUs with tags but no validity
    tags_only = set(tags_mapping.keys()) - set(validity_mapping.keys())
    if tags_only:
        print("⚠️  SKUs with tags but no explicit validity (will use regex/default):")
        for sku in sorted(tags_only):
            validity = VPNStrategyFactory.get_validity_days_for_sku(sku)
            print(f"  {sku:30} -> {validity:3} days (fallback)")
        print()
    
    # Check for SKUs with validity but no tags (shouldn't happen)
    validity_only = set(validity_mapping.keys()) - set(tags_mapping.keys())
    if validity_only:
        print("❌ SKUs with validity but no tags (configuration error):")
        for sku in sorted(validity_only):
            print(f"  {sku}")
        print()
    
    # Check consistency
    common_skus = set(tags_mapping.keys()) & set(validity_mapping.keys())
    print(f"✅ SKUs with both tags and validity: {len(common_skus)}")
    
    if not tags_only and not validity_only:
        print("✅ Configuration is consistent!")
    print()

def test_validity_patterns():
    """Test validity extraction patterns"""
    print("=" * 80)
    print("VALIDITY PATTERN TESTING")
    print("=" * 80)
    
    # Test different number patterns
    pattern_tests = [
        # Standard patterns
        ('my_15', 15, 'Standard 15-day pattern'),
        ('my_30', 30, 'Standard 30-day pattern'),
        ('my_60', 60, 'Standard 60-day pattern'),
        ('my_90', 90, 'Standard 90-day pattern'),
        
        # Complex patterns
        ('my_highspeed_premium_30', 30, 'Complex pattern with number'),
        ('sg_business_90', 90, 'Business pattern'),
        
        # Edge cases
        ('my_', 30, 'No number - should default'),
        ('sg_premium', 30, 'No number - should default'),
        ('test_123', 123, 'Unknown SKU with number'),
        ('no_number_sku', 30, 'No number at all'),
        
        # Multiple numbers (should take last one)
        ('my_15_premium_30', 30, 'Multiple numbers - should take last'),
        ('sg_30_business_60', 60, 'Multiple numbers - should take last'),
    ]
    
    print("Testing validity extraction patterns:")
    print()
    
    for sku, expected, description in pattern_tests:
        actual = VPNStrategyFactory.get_validity_days_for_sku(sku)
        status = "✅" if actual == expected else "❌"
        print(f"{status} {sku:30} -> Expected: {expected:3}, Got: {actual:3} ({description})")
    
    print()

def main():
    """Run all validity tests"""
    print("VPN SKU Validity Configuration Test Suite")
    print("=" * 80)
    
    try:
        test_validity_extraction()
        test_configuration_coverage()
        test_validity_vs_tags_consistency()
        test_validity_patterns()
        
        print("=" * 80)
        print("All validity tests completed!")
        print("=" * 80)
        
    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
