# Plugin Code Style Standards

This document defines the coding standards, naming conventions, and style guidelines for all plugins in the SteamCodeTool system.

## 📝 Naming Conventions

### 1. Plugin Names
```python
# ✅ Good - snake_case for plugin names
self.name = "email_service"
self.name = "vpn_manager" 
self.name = "order_processor"

# ❌ Bad - inconsistent naming
self.name = "EmailService"      # PascalCase
self.name = "vpn-manager"       # kebab-case
self.name = "Order Processor"   # spaces
```

### 2. URL Prefixes
```python
# ✅ Good - kebab-case for URLs
self.url_prefix = "/api/email-service"
self.url_prefix = "/admin/vpn-manager"
self.url_prefix = "/api/order-processor"

# ❌ Bad - inconsistent URL naming
self.url_prefix = "/api/email_service"  # snake_case
self.url_prefix = "/api/EmailService"   # PascalCase
```

### 3. Class Names
```python
# ✅ Good - PascalCase for classes
class EmailService:
class VPNManager:
class OrderProcessor:
class Plugin:

# ❌ Bad - inconsistent class naming
class email_service:    # snake_case
class vpnManager:       # camelCase
class order_Processor:  # mixed case
```

### 4. Method and Variable Names
```python
# ✅ Good - snake_case for methods and variables
def send_email(self, recipient_address):
def get_user_data(self):
def process_order_items(self):

user_id = 123
email_address = "<EMAIL>"
order_total = 99.99

# ❌ Bad - inconsistent naming
def sendEmail(self, recipientAddress):  # camelCase
def GetUserData(self):                  # PascalCase
def process_OrderItems(self):           # mixed case
```

### 5. Constants
```python
# ✅ Good - UPPER_SNAKE_CASE for constants
DEFAULT_TIMEOUT = 30
MAX_RETRY_ATTEMPTS = 3
API_BASE_URL = "https://api.example.com"

# ❌ Bad - inconsistent constant naming
default_timeout = 30        # lowercase
MaxRetryAttempts = 3       # PascalCase
apiBaseUrl = "..."         # camelCase
```

### 6. File and Directory Names
```python
# ✅ Good - snake_case for files and directories
plugins/email_service/
plugins/vpn_manager/
services/email_service.py
routes/user_routes.py

# ❌ Bad - inconsistent file naming
plugins/EmailService/      # PascalCase
plugins/vpn-manager/       # kebab-case
services/emailService.py   # camelCase
```

## 🎨 Code Formatting Standards

### 1. Import Organization
```python
# ✅ Good - organized imports
# Standard library imports
import os
import sys
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

# Third-party imports
import requests
from flask import Blueprint, jsonify, request
import jsonschema

# Local imports
from core.plugin_manager import PluginInterface
from .services.email_service import EmailService
from .routes.plugin_routes import create_plugin_blueprint

# ❌ Bad - disorganized imports
from flask import Blueprint
import os
from .services.email_service import EmailService
import requests
from typing import Dict
import sys
```

### 2. Class Structure
```python
# ✅ Good - well-structured class
class EmailService(BaseService):
    """Email service for sending notifications and alerts.
    
    This service handles email sending functionality including
    SMTP configuration, template processing, and delivery tracking.
    """
    
    # Class constants
    DEFAULT_TIMEOUT = 30
    MAX_RETRY_ATTEMPTS = 3
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize email service with configuration.
        
        Args:
            config: Service configuration dictionary
        """
        super().__init__(config)
        self.smtp_client = None
        self._initialized = False
    
    def initialize(self) -> bool:
        """Initialize the email service."""
        # Implementation here
        pass
    
    def send_email(self, recipient: str, subject: str, body: str) -> bool:
        """Send an email to the specified recipient.
        
        Args:
            recipient: Email address of the recipient
            subject: Email subject line
            body: Email body content
            
        Returns:
            True if email was sent successfully, False otherwise
            
        Raises:
            EmailServiceError: If email sending fails
        """
        # Implementation here
        pass
    
    def _validate_email_address(self, email: str) -> bool:
        """Validate email address format (private method)."""
        # Implementation here
        pass
```

### 3. Function Documentation
```python
# ✅ Good - comprehensive docstrings
def process_order(self, order_id: str, items: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Process an order with the given items.
    
    This function validates the order items, calculates totals,
    applies discounts, and creates the order record.
    
    Args:
        order_id: Unique identifier for the order
        items: List of order items, each containing:
            - product_id (str): Product identifier
            - quantity (int): Number of items
            - price (float): Unit price
    
    Returns:
        Dictionary containing:
            - order_id (str): The processed order ID
            - total (float): Order total amount
            - status (str): Processing status
            - created_at (str): ISO timestamp
    
    Raises:
        ValidationError: If order items are invalid
        ProcessingError: If order processing fails
        
    Example:
        >>> items = [{"product_id": "123", "quantity": 2, "price": 10.99}]
        >>> result = process_order("ORD-001", items)
        >>> print(result["total"])
        21.98
    """
    # Implementation here
    pass

# ❌ Bad - minimal or missing documentation
def process_order(self, order_id, items):
    """Process order."""
    # Implementation here
    pass
```

### 4. Error Handling Style
```python
# ✅ Good - specific exception handling
def send_email(self, recipient: str, subject: str, body: str) -> bool:
    """Send email with proper error handling."""
    try:
        self._validate_email_address(recipient)
        self._connect_to_smtp()
        self._send_message(recipient, subject, body)
        
        self.logger.info(f"Email sent successfully to {recipient}")
        return True
        
    except ValidationError as e:
        self.logger.error(f"Email validation failed: {e}")
        raise
    except SMTPConnectionError as e:
        self.logger.error(f"SMTP connection failed: {e}")
        return False
    except SMTPSendError as e:
        self.logger.error(f"Failed to send email: {e}")
        return False
    except Exception as e:
        self.logger.error(f"Unexpected error sending email: {e}")
        raise EmailServiceError(f"Email sending failed: {e}")

# ❌ Bad - generic exception handling
def send_email(self, recipient, subject, body):
    try:
        # Implementation here
        return True
    except Exception as e:
        print(f"Error: {e}")  # Bad logging
        return False
```

### 5. Configuration and Constants
```python
# ✅ Good - well-organized configuration
class EmailServiceConfig:
    """Configuration constants for email service."""
    
    # SMTP Configuration
    DEFAULT_SMTP_PORT = 587
    DEFAULT_SMTP_TIMEOUT = 30
    DEFAULT_USE_TLS = True
    
    # Retry Configuration
    MAX_RETRY_ATTEMPTS = 3
    RETRY_DELAY_SECONDS = 5
    
    # Email Validation
    MAX_SUBJECT_LENGTH = 200
    MAX_BODY_LENGTH = 10000
    
    # Supported email providers
    SUPPORTED_PROVIDERS = {
        'gmail': {'host': 'smtp.gmail.com', 'port': 587},
        'outlook': {'host': 'smtp-mail.outlook.com', 'port': 587},
        'yahoo': {'host': 'smtp.mail.yahoo.com', 'port': 587}
    }

# ❌ Bad - scattered magic numbers
def send_email(self, recipient, subject, body):
    if len(subject) > 200:  # Magic number
        raise ValueError("Subject too long")
    
    for attempt in range(3):  # Magic number
        try:
            # Implementation
            break
        except Exception:
            time.sleep(5)  # Magic number
```

## 🔧 Code Organization Patterns

### 1. Service Layer Organization
```python
# ✅ Good - clear service structure
class BaseService(ABC):
    """Base class for all services."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the service."""
        pass
    
    @abstractmethod
    def shutdown(self) -> bool:
        """Shutdown the service."""
        pass

class EmailService(BaseService):
    """Concrete email service implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.smtp_client = None
    
    def initialize(self) -> bool:
        """Initialize email service."""
        try:
            self._setup_smtp_client()
            self._test_connection()
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize: {e}")
            return False
```

### 2. Route Organization
```python
# ✅ Good - organized route structure
def create_email_blueprint(plugin: 'EmailPlugin') -> Blueprint:
    """Create email service blueprint."""
    
    bp = Blueprint('email', __name__)
    
    # Health and info endpoints
    @bp.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        return jsonify(plugin.get_health_status())
    
    @bp.route('/info', methods=['GET'])
    def get_info():
        """Plugin information endpoint."""
        return jsonify(plugin.get_info())
    
    # Business logic endpoints
    @bp.route('/send', methods=['POST'])
    def send_email():
        """Send email endpoint."""
        try:
            data = request.get_json()
            result = plugin.services['email'].send_email(
                recipient=data['recipient'],
                subject=data['subject'],
                body=data['body']
            )
            return jsonify({'success': result})
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    return bp
```

### 3. Configuration Management
```python
# ✅ Good - structured configuration
class PluginConfig:
    """Plugin configuration management."""
    
    def __init__(self, raw_config: Dict[str, Any]):
        self.raw_config = raw_config
        self._validate_config()
        self._apply_defaults()
    
    def _validate_config(self):
        """Validate configuration against schema."""
        # Validation logic here
        pass
    
    def _apply_defaults(self):
        """Apply default values."""
        # Default application logic here
        pass
    
    @property
    def email_config(self) -> Dict[str, Any]:
        """Get email service configuration."""
        return self.raw_config.get('email_config', {})
    
    @property
    def is_debug_enabled(self) -> bool:
        """Check if debug mode is enabled."""
        return self.raw_config.get('debug', False)
```

## 📋 Code Quality Standards

### 1. Type Hints
```python
# ✅ Good - comprehensive type hints
from typing import Dict, List, Optional, Union, Any, Callable

def process_data(
    data: List[Dict[str, Any]], 
    processor: Callable[[Dict[str, Any]], Dict[str, Any]],
    options: Optional[Dict[str, Union[str, int, bool]]] = None
) -> List[Dict[str, Any]]:
    """Process data with type hints."""
    # Implementation here
    pass

# ❌ Bad - missing type hints
def process_data(data, processor, options=None):
    """Process data without type hints."""
    # Implementation here
    pass
```

### 2. Logging Standards
```python
# ✅ Good - structured logging
import logging

class EmailService:
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.config = config
    
    def send_email(self, recipient: str, subject: str) -> bool:
        """Send email with proper logging."""
        self.logger.info(
            "Sending email",
            extra={
                'recipient': recipient,
                'subject_length': len(subject),
                'service': 'email'
            }
        )
        
        try:
            # Send email logic
            self.logger.info(f"Email sent successfully to {recipient}")
            return True
        except Exception as e:
            self.logger.error(
                f"Failed to send email to {recipient}: {e}",
                extra={'recipient': recipient, 'error': str(e)}
            )
            return False

# ❌ Bad - poor logging
def send_email(self, recipient, subject):
    print(f"Sending email to {recipient}")  # Bad: using print
    try:
        # Send email logic
        print("Email sent")  # Bad: no context
        return True
    except Exception as e:
        print(f"Error: {e}")  # Bad: generic error
        return False
```

### 3. Comment Standards
```python
# ✅ Good - meaningful comments
class OrderProcessor:
    def calculate_total(self, items: List[Dict[str, Any]]) -> float:
        """Calculate order total including taxes and discounts."""
        
        # Calculate subtotal from all items
        subtotal = sum(item['price'] * item['quantity'] for item in items)
        
        # Apply volume discount for orders over $100
        if subtotal > 100:
            discount = subtotal * 0.1  # 10% volume discount
            subtotal -= discount
        
        # Add tax (rate varies by region)
        tax_rate = self._get_tax_rate()
        tax_amount = subtotal * tax_rate
        
        return subtotal + tax_amount

# ❌ Bad - unnecessary or unclear comments
def calculate_total(self, items):
    # Calculate total
    total = 0
    # Loop through items
    for item in items:
        # Add to total
        total += item['price'] * item['quantity']  # Multiply price by quantity
    # Return total
    return total
```

This code style standard ensures consistency and readability across all plugins while maintaining professional development practices.
