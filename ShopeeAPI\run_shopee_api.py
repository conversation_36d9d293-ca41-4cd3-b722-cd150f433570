"""
Runner script for ShopeeAPI service.
"""
import os
import uvicorn
import sys
import pathlib

# Ensure the current directory is in the Python path
current_dir = str(pathlib.Path(__file__).parent.absolute())
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import the logging configuration
try:
    from logging_config import LOG<PERSON>NG_CONFIG
except ImportError:
    LOGGING_CONFIG = None  # Fallback if the file doesn't exist

# Print the current working directory and config path for debugging
print(f"Current working directory: {os.getcwd()}")
print(f"ShopeeAPI directory: {current_dir}")
print(f"Expected config path: {os.path.join(current_dir, 'config.json')}")

if __name__ == "__main__":
    # Define port
    port = int(os.environ.get("PORT", 8000))

    # Run the application
    print(f"Starting ShopeeAPI service on port {port}...")
    # Since we're now inside the ShopeeAPI directory, we can import directly
    uvicorn.run(
        "api:app", 
        host="0.0.0.0", 
        port=port, 
        reload=True,
        log_config=LOGGING_CONFIG
    )
