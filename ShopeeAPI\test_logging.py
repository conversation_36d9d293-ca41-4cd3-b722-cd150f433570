#!/usr/bin/env python3
"""
Test script to verify logging configuration is working correctly.
This script simulates the logging behavior of ShopeeAPI components.
"""

import os
import sys
import logging
import time
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import logging configuration
try:
    from logging_config import LOGGING_CONFIG
    import logging.config
    logging.config.dictConfig(LOGGING_CONFIG)
    print("✓ Logging configuration loaded successfully")
except ImportError as e:
    print(f"✗ Failed to load logging configuration: {e}")
    sys.exit(1)

def test_logging_levels():
    """Test different logging levels for different components."""
    
    print("\n=== Testing Logging Levels ===")
    
    # Test root logger
    root_logger = logging.getLogger()
    print(f"Root logger level: {logging.getLevelName(root_logger.level)}")
    
    # Test WebSocket logger
    websocket_logger = logging.getLogger('services.websocket')
    print(f"WebSocket logger level: {logging.getLevelName(websocket_logger.level)}")
    
    # Test Webhook logger
    webhook_logger = logging.getLogger('utils.webhook')
    print(f"Webhook logger level: {logging.getLevelName(webhook_logger.level)}")
    
    print("\n=== Testing Log Messages ===")
    
    # Test root logger messages
    print("\n1. Root Logger Messages:")
    root_logger.debug("This DEBUG message should NOT appear (level: WARNING)")
    root_logger.info("This INFO message should NOT appear (level: WARNING)")
    root_logger.warning("This WARNING message SHOULD appear")
    root_logger.error("This ERROR message SHOULD appear")
    
    # Test WebSocket logger messages
    print("\n2. WebSocket Logger Messages:")
    websocket_logger.debug("WebSocket DEBUG: should NOT appear (level: ERROR)")
    websocket_logger.info("WebSocket INFO: should NOT appear (level: ERROR)")
    websocket_logger.warning("WebSocket WARNING: should NOT appear (level: ERROR)")
    websocket_logger.error("WebSocket ERROR: SHOULD appear")
    
    # Test Webhook logger messages
    print("\n3. Webhook Logger Messages:")
    webhook_logger.debug("Webhook DEBUG: should NOT appear (level: ERROR)")
    webhook_logger.info("Webhook INFO: should NOT appear (level: ERROR)")
    webhook_logger.warning("Webhook WARNING: should NOT appear (level: ERROR)")
    webhook_logger.error("Webhook ERROR: SHOULD appear")

def simulate_frequent_messages():
    """Simulate the frequent messages that were causing log spam."""
    
    print("\n=== Simulating Frequent Messages (should be quiet now) ===")
    
    websocket_logger = logging.getLogger('services.websocket')
    webhook_logger = logging.getLogger('utils.webhook')
    
    # Simulate WebSocket message processing (these should not appear)
    for i in range(5):
        websocket_logger.info(f"Received Socket.IO message - ID: test_{i}, Type: message")
        websocket_logger.info(f"Processing unique message: test_{i} (type: message)")
        time.sleep(0.1)
    
    # Simulate webhook calls (these should not appear)
    for i in range(5):
        webhook_logger.info(f"Successfully sent MESSAGE_RECEIVED webhook to SteamCodeTool Chat Commands")
        time.sleep(0.1)
    
    print("✓ Frequent message simulation completed (should see no spam above)")

def check_log_files():
    """Check if log files are being created correctly."""
    
    print("\n=== Checking Log Files ===")
    
    log_dir = "logs"
    if not os.path.exists(log_dir):
        print(f"✗ Log directory '{log_dir}' does not exist")
        return
    
    log_files = [
        "shopee_api.log",
        "shopee_api_errors.log"
    ]
    
    for log_file in log_files:
        log_path = os.path.join(log_dir, log_file)
        if os.path.exists(log_path):
            size = os.path.getsize(log_path)
            print(f"✓ {log_file}: {size} bytes")
        else:
            print(f"✗ {log_file}: not found")

def main():
    """Main test function."""
    
    print("ShopeeAPI Logging Configuration Test")
    print("=" * 40)
    print(f"Test started at: {datetime.now()}")
    
    # Show environment variables
    print(f"\nEnvironment Variables:")
    print(f"LOG_LEVEL: {os.environ.get('LOG_LEVEL', 'not set')}")
    print(f"WEBSOCKET_LOG_LEVEL: {os.environ.get('WEBSOCKET_LOG_LEVEL', 'not set')}")
    print(f"WEBHOOK_LOG_LEVEL: {os.environ.get('WEBHOOK_LOG_LEVEL', 'not set')}")
    print(f"DEBUG_MODE: {os.environ.get('DEBUG_MODE', 'not set')}")
    
    # Run tests
    test_logging_levels()
    simulate_frequent_messages()
    check_log_files()
    
    print(f"\n✓ Test completed at: {datetime.now()}")
    print("\nIf you see only WARNING and ERROR messages above (no INFO/DEBUG spam),")
    print("then the logging optimization is working correctly!")

if __name__ == "__main__":
    main()
