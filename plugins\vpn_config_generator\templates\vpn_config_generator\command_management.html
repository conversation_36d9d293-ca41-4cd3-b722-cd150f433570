{% extends "base.html" %}

{% block title %}VPN Command Management{% endblock %}

{% block extra_head %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<style>
    .command-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .command-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .command-header {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 1.5rem;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 600;
    }
    
    .status-enabled {
        background-color: #dcfce7;
        color: #166534;
    }
    
    .status-disabled {
        background-color: #f3f4f6;
        color: #6b7280;
    }
    
    .action-btn {
        padding: 0.5rem;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        margin: 0 0.25rem;
    }
    
    .action-btn:hover {
        transform: scale(1.05);
    }
    
    .btn-edit {
        background-color: #fbbf24;
        color: white;
    }
    
    .btn-edit:hover {
        background-color: #f59e0b;
    }
    
    .btn-delete {
        background-color: #ef4444;
        color: white;
    }
    
    .btn-delete:hover {
        background-color: #dc2626;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #d1d5db;
    }
</style>
{% endblock %}

{% block header %}VPN Command Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Chat Command Management</h2>
                <p class="text-gray-600 mt-1">Manage VPN chat commands that can be used in conversations</p>
            </div>
            <button type="button" class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" onclick="showCreateCommandModal()">
                <i class="fas fa-plus mr-2"></i>Add Command
            </button>
        </div>
        
        <!-- Info Section -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h4 class="text-lg font-semibold text-blue-800 mb-2">Command Registration</h4>
                    <p class="text-blue-700 mb-2">
                        Commands configured here will be automatically registered with the Chat Commands plugin.
                    </p>
                    <p class="text-sm text-blue-600">
                        <strong>Note:</strong> Changes to commands will automatically re-register them with the chat system.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Commands List -->
    <div id="commands-list" class="space-y-6">
        <!-- Command cards will be loaded here -->
    </div>
</div>

<!-- Create/Edit Command Modal -->
<div class="modal fade" id="commandModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
            <div class="modal-header bg-gradient-to-r from-blue-500 to-purple-600 text-white" style="border-radius: 16px 16px 0 0;">
                <h4 class="modal-title font-semibold" id="commandModalTitle">Add Command</h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-6">
                <form id="commandForm">
                    <div class="mb-4">
                        <label for="commandKey" class="form-label font-semibold text-gray-700">Command Key</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all" id="commandKey" required placeholder="e.g., vlist, vhelp">
                        <small class="text-gray-500">Internal identifier for the command (used in code)</small>
                    </div>
                    <div class="mb-4">
                        <label for="commandName" class="form-label font-semibold text-gray-700">Command Name</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all" id="commandName" required placeholder="e.g., vlist, vhelp">
                        <small class="text-gray-500">The actual command users will type (without #)</small>
                    </div>
                    <div class="mb-4">
                        <label for="commandDescription" class="form-label font-semibold text-gray-700">Description</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all" id="commandDescription" required placeholder="Brief description of what the command does">
                    </div>
                    <div class="mb-4">
                        <label for="responseText" class="form-label font-semibold text-gray-700">Response Text</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all" id="responseText" required placeholder="Default response text or header">
                    </div>
                    <div class="mb-4">
                        <label for="requiredParams" class="form-label font-semibold text-gray-700">Required Parameters</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all" id="requiredParams" placeholder="server,days,telco,plan (comma-separated)">
                        <small class="text-gray-500">Leave empty for commands with no parameters</small>
                    </div>
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="commandEnabled" checked>
                            <label class="form-check-label font-semibold text-gray-700" for="commandEnabled">
                                Enabled
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-6 pt-0">
                <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all" onclick="saveCommand()">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
let editMode = false;
let currentCommandKey = null;

// Configure toastr
toastr.options = {
    "closeButton": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "timeOut": "3000"
};

// Load commands on page load
$(document).ready(function() {
    loadCommands();
});

function loadCommands() {
    $.get('/vpn-config-generator/api/commands')
        .done(function(response) {
            if (response.success) {
                displayCommands(response.commands);
            } else {
                toastr.error('Failed to load commands: ' + response.error);
            }
        })
        .fail(function() {
            toastr.error('Failed to load commands');
        });
}

function displayCommands(commands) {
    const container = $('#commands-list');
    container.empty();

    if (Object.keys(commands).length === 0) {
        container.html(`
            <div class="empty-state">
                <i class="fas fa-terminal"></i>
                <h3 class="text-xl font-semibold mb-2">No Commands Configured</h3>
                <p class="text-gray-500 mb-4">Get started by adding your first chat command</p>
                <button class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all" onclick="showCreateCommandModal()">
                    <i class="fas fa-plus mr-2"></i>Add Your First Command
                </button>
            </div>
        `);
        return;
    }

    for (const [commandKey, command] of Object.entries(commands)) {
        const commandCard = createCommandCard(commandKey, command);
        container.append(commandCard);
    }
}

function createCommandCard(commandKey, command) {
    const statusBadge = command.enabled ? 
        '<span class="status-badge status-enabled">Enabled</span>' : 
        '<span class="status-badge status-disabled">Disabled</span>';
    
    const paramsList = command.required_params && command.required_params.length > 0 ? 
        command.required_params.join(', ') : 'None';

    return `
        <div class="command-card bg-white shadow-lg">
            <div class="command-header">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold">#${command.command}</h3>
                            ${statusBadge}
                        </div>
                        <p class="text-blue-100 text-sm mb-2">${command.description}</p>
                        <div class="text-blue-100 text-xs">
                            <strong>Key:</strong> ${commandKey} | 
                            <strong>Parameters:</strong> ${paramsList}
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button class="action-btn btn-edit" onclick="editCommand('${commandKey}')" title="Edit Command">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn btn-delete" onclick="deleteCommand('${commandKey}')" title="Delete Command">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-3">
                    <strong class="text-gray-700">Response Text:</strong>
                    <p class="text-gray-600 mt-1">${command.response_text}</p>
                </div>
                <div class="text-sm text-gray-500">
                    <strong>Created:</strong> ${new Date(command.created_at).toLocaleDateString()} | 
                    <strong>Updated:</strong> ${new Date(command.updated_at).toLocaleDateString()}
                </div>
            </div>
        </div>
    `;
}

function showCreateCommandModal() {
    editMode = false;
    currentCommandKey = null;
    $('#commandModalTitle').text('Add Command');
    $('#commandForm')[0].reset();
    $('#commandEnabled').prop('checked', true);
    $('#commandKey').prop('disabled', false);
    $('#commandModal').modal('show');
}

function editCommand(commandKey) {
    $.get('/vpn-config-generator/api/commands')
        .done(function(response) {
            if (response.success && response.commands[commandKey]) {
                const command = response.commands[commandKey];
                editMode = true;
                currentCommandKey = commandKey;
                
                $('#commandModalTitle').text('Edit Command');
                $('#commandKey').val(commandKey).prop('disabled', true);
                $('#commandName').val(command.command);
                $('#commandDescription').val(command.description);
                $('#responseText').val(command.response_text);
                $('#requiredParams').val(command.required_params ? command.required_params.join(',') : '');
                $('#commandEnabled').prop('checked', command.enabled);
                
                $('#commandModal').modal('show');
            } else {
                toastr.error('Command not found');
            }
        })
        .fail(function() {
            toastr.error('Failed to load command details');
        });
}

function saveCommand() {
    const data = {
        command_key: $('#commandKey').val(),
        command: $('#commandName').val(),
        description: $('#commandDescription').val(),
        response_text: $('#responseText').val(),
        required_params: $('#requiredParams').val() ? $('#requiredParams').val().split(',').map(p => p.trim()) : [],
        enabled: $('#commandEnabled').is(':checked')
    };

    const url = editMode ? 
        `/vpn-config-generator/api/commands/${currentCommandKey}` : 
        '/vpn-config-generator/api/commands';
    const method = editMode ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data)
    })
    .done(function(response) {
        if (response.success) {
            toastr.success(editMode ? 'Command updated successfully' : 'Command created successfully');
            $('#commandModal').modal('hide');
            loadCommands();
        } else {
            toastr.error('Failed to save command: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to save command');
    });
}

function deleteCommand(commandKey) {
    const confirmHtml = `
        <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
                    <div class="modal-header bg-gradient-to-r from-red-500 to-pink-600 text-white" style="border-radius: 16px 16px 0 0;">
                        <h5 class="modal-title font-semibold">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-6 text-center">
                        <i class="fas fa-terminal text-red-500 text-4xl mb-4"></i>
                        <h4 class="font-semibold text-gray-800 mb-2">Delete Command</h4>
                        <p class="text-gray-600">Are you sure you want to delete this command? This action cannot be undone.</p>
                    </div>
                    <div class="modal-footer border-0 p-6 pt-0">
                        <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn bg-gradient-to-r from-red-500 to-pink-600 text-white px-6 py-2 rounded-lg font-semibold" onclick="confirmDeleteCommand('${commandKey}')">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('body').append(confirmHtml);
    $('#confirmDeleteModal').modal('show');
    $('#confirmDeleteModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

function confirmDeleteCommand(commandKey) {
    $('#confirmDeleteModal').modal('hide');
    $.ajax({
        url: `/vpn-config-generator/api/commands/${commandKey}`,
        method: 'DELETE'
    })
    .done(function(response) {
        if (response.success) {
            toastr.success('Command deleted successfully');
            loadCommands();
        } else {
            toastr.error('Failed to delete command: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to delete command');
    });
}
</script>
{% endblock %}
