import config
from services.order_service import get_to_ship_orders, get_order_details
from services.chat_service import send_chat_message, send_image_message
from utils.var_sku_extractor import extract_var_sku
import traceback
import fnmatch

def auto_redeem_orders():
    try:
        if not config.ENABLE_AUTO_REDEEM and not config.CHECK_NEW_ORDERS:
            print("Auto Redeem and New Order Check are disabled.")
            return

        orders = get_to_ship_orders()

        if not orders or 'card_list' not in orders:
            print("No orders to ship.")
            return

        auto_skus = config.AUTO_REDEEM_VAR_SKUS
        card_list = orders.get('card_list', [])
        if not card_list:
            # print("📦 No card_list found in orders data.")
            return

        for idx, card in enumerate(card_list):
            
            # Ensure card structure is valid
            if not isinstance(card, dict) or 'order_card' not in card:
                print(f"Skipped Order {idx}: Invalid card structure.")
                continue

            order = card['order_card']
            order_sn = order['card_header'].get('order_sn')

            if config.CHECK_NEW_ORDERS and order_sn in config.sent_orders:
                print(f"Order {order_sn} already processed. Skipping.")
                continue

            # Fetch order details
            order_details, status = get_order_details(order_sn)
            if status != 200:
                print(f"Failed to get order details for order_sn {order_sn}: {order_details.get('error')}")
                continue

            # Extract var_sku using the extractor
            var_skus = extract_var_sku(order_details)
            if not var_skus:
                print(f"No var_sku found for order_sn {order_sn}")
                continue

            for var_sku in var_skus:
                matching_sku = next((sku for sku in auto_skus if fnmatch.fnmatch(var_sku, sku['sku'])), None)
                if matching_sku:
                    print(f"var_sku {var_sku} matches pattern {matching_sku['sku']}. Sending redeem message.")
                    
                    # Use custom message if available, otherwise use global message
                    message = matching_sku.get('message') or config.user_config.get('AUTO_REDEEM_MESSAGE', "Hello, this is a default auto-redeem message.")
                    
                    # Extract order details
                    order_data = order_details['data']
                    buyer_user = order_data['buyer_user']
                    order_item = order_data['order_items'][0]

                    # Replace variables in the message
                    variables = {
                        "{Steam_username}": "",  # You may want to remove this or set a default value
                        "{order_sn}": order_sn,
                        "{buyer_username}": buyer_user['user_name'],
                        "{item_name}": order_item['product']['name'],
                        "{item_price}": order_data['total_price'],
                        "{buyer_name}": order_data['buyer_address_name'],
                        "{buyer_phone}": order_data['buyer_address_phone'],
                        "{create_time}": order_data['create_time'],
                        "{shipping_address}": order_data['shipping_address'],
                        "{item_sku}": order_item['item_model']['sku'],
                        "{item_quantity}": str(order_item['amount']),
                        "{payment_method}": str(order_data['payment_method']),
                        "{shop_name}": order_data['seller_address']['name'],
                        "{escrow_release_time}": order_data['escrow_release_time'],
                        "{buyer_rating}": str(buyer_user['rating_star']),
                        "{order_status}": str(order_data['status']),
                    }

                    for key, value in variables.items():
                        message = message.replace(key, str(value))

                    payload = {
                        "username": buyer_user['user_name'],
                        "text": message,
                        "force_send_cancel_order_warning": False,
                        "comply_cancel_order_warning": False
                    }
                    response, status_code = send_chat_message(payload)
                    if status_code == 200 and isinstance(response, dict):
                        print(f"Sent redeem message for order_sn: {order_sn}")
                        config.sent_orders.add(order_sn)
                        config.save_sent_orders(config.sent_orders)

                        # Send images if provided
                        image_urls = matching_sku.get('image_urls', [])
                        username = buyer_user['user_name']
                        for url in image_urls:
                            img_payload = {'username': username, 'image_url': url.strip()}
                            try:
                                img_response, img_status = send_image_message(img_payload)
                                if img_status == 200:
                                    print(f"Successfully sent image {url.strip()} for order_sn: {order_sn}")
                                else:
                                    print(f"Failed to send image {url.strip()} for order_sn: {order_sn}. Status: {img_status}")
                                    print(f"Image send error details: {img_response}")
                                    # If it's a conversation lookup error, try to debug
                                    if img_status == 500 and isinstance(img_response, dict) and 'error' in img_response:
                                        error_msg = img_response['error']
                                        if 'conversation info' in error_msg.lower():
                                            print(f"Conversation lookup failed for user {buyer_user['user_name']} during image send")
                            except Exception as e:
                                print(f"Exception sending image {url.strip()}: {e}")
                    else:
                        error = response.get('error') if isinstance(response, dict) else 'Unknown error'
                        print(f"Failed to send redeem message for order_sn: {order_sn}. Status code: {status_code}, Error: {error}")
                        print(f"Full response: {response}")
            
    except Exception as e:
        print(traceback.format_exc())
        print(f"Error in auto_redeem_orders: {str(e)}")