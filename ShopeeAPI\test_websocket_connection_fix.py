#!/usr/bin/env python3
"""
Test script to verify WebSocket connection fixes.

This script tests the improved connection stability and heartbeat mechanism.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.websocket import WebSocketService
from config.config_manager import ConfigManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_websocket_connection():
    """Test the WebSocket connection with the new fixes."""
    logger.info("🧪 Starting WebSocket connection test...")
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        
        # Create WebSocket service
        websocket_service = WebSocketService(config_manager)
        
        logger.info("📡 Testing connection to Shopee...")
        
        # Test connection
        success = await websocket_service.connect_to_shopee()
        
        if success:
            logger.info("✅ Connection successful!")
            logger.info(f"   - Socket.IO connected: {websocket_service.sio_connected}")
            logger.info(f"   - Is connected: {websocket_service.is_connected}")
            logger.info(f"   - Reconnect attempts: {websocket_service.reconnect_attempt}")
            
            # Test heartbeat for 2 minutes
            logger.info("💓 Testing heartbeat mechanism for 2 minutes...")
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < 120:  # 2 minutes
                await asyncio.sleep(10)
                
                # Check connection status
                if websocket_service.sio_client and websocket_service.sio_client.connected:
                    logger.info(f"   ✓ Connection still active at {datetime.now()}")
                    logger.info(f"   - Last ping: {websocket_service.last_ping_time}")
                else:
                    logger.warning(f"   ❌ Connection lost at {datetime.now()}")
                    break
            
            logger.info("🏁 Test completed successfully!")
            
        else:
            logger.error("❌ Connection failed!")
            logger.error(f"   - Reconnect attempts: {websocket_service.reconnect_attempt}")
            logger.error(f"   - Auth failures: {websocket_service.auth_failure_count}")
        
        # Clean up
        logger.info("🧹 Cleaning up...")
        await websocket_service.close()
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

async def test_heartbeat_format():
    """Test that heartbeat uses the correct format."""
    logger.info("🧪 Testing heartbeat format...")
    
    # This is a simple test to verify the heartbeat format
    expected_format = "pmminichat|0|0"
    logger.info(f"✅ Expected heartbeat format: {expected_format}")
    
    # The actual heartbeat test happens in the connection test above

def main():
    """Main test function."""
    logger.info("🚀 Starting WebSocket Connection Fix Tests")
    logger.info("=" * 50)
    
    # Test heartbeat format
    asyncio.run(test_heartbeat_format())
    
    logger.info("-" * 50)
    
    # Test actual connection
    asyncio.run(test_websocket_connection())
    
    logger.info("=" * 50)
    logger.info("🎉 All tests completed!")

if __name__ == "__main__":
    main()
