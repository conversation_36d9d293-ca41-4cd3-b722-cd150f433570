{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Configuration Backups</h2>
                <a href="{{ url_for('vpn.configurations') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Configurations
                </a>
            </div>
        </div>
    </div>
    
    <!-- Create Backup -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Create New Backup</h3>
                </div>
                <form method="POST" action="{{ url_for('vpn.backup_configurations') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Select Servers to Backup:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all">
                                        <label class="form-check-label" for="select-all">
                                            <strong>Select All Servers</strong>
                                        </label>
                                    </div>
                                    <hr>
                                    {% for server in servers %}
                                    <div class="form-check">
                                        <input class="form-check-input server-checkbox" type="checkbox" 
                                               name="server_ids" value="{{ server.id }}" id="server-{{ server.id }}">
                                        <label class="form-check-label" for="server-{{ server.id }}">
                                            {{ server.name }} ({{ server.host }})
                                        </label>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="description">Backup Description:</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"
                                              placeholder="Enter a description for this backup..."></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Backup
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Existing Backups -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Existing Backups</h3>
                    {% if backups and backups.backups %}
                    <div class="card-tools">
                        <span class="badge badge-info">{{ backups.total_backups }} backups</span>
                        <span class="badge badge-secondary">{{ (backups.total_size / 1024 / 1024) | round(2) }} MB</span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if backups and backups.backups %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Server</th>
                                    <th>Backup Name</th>
                                    <th>Size</th>
                                    <th>Created</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups.backups %}
                                <tr>
                                    <td>{{ backup.server_name }}</td>
                                    <td>
                                        <code>{{ backup.backup_name }}</code>
                                    </td>
                                    <td>{{ (backup.file_size / 1024) | round(2) }} KB</td>
                                    <td>{{ backup.created_at }}</td>
                                    <td>
                                        {% if backup.is_accessible %}
                                        <span class="badge badge-success">Available</span>
                                        {% else %}
                                        <span class="badge badge-danger">Not Accessible</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-info" 
                                                    onclick="viewBackup('{{ backup.backup_path }}')">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-warning" 
                                                    onclick="restoreBackup({{ backup.server_id }}, '{{ backup.backup_path }}', '{{ backup.server_name }}')">
                                                <i class="fas fa-undo"></i> Restore
                                            </button>
                                            <button class="btn btn-sm btn-primary" 
                                                    onclick="downloadBackup('{{ backup.backup_path }}')">
                                                <i class="fas fa-download"></i> Download
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <p class="text-muted">No backups found. Create your first backup above.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Restore Modal -->
<div class="modal fade" id="restoreModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Restore Configuration</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ url_for('vpn.restore_configuration') }}">
                <div class="modal-body">
                    <p>Are you sure you want to restore configuration for server "<span id="restore-server-name"></span>"?</p>
                    <input type="hidden" name="server_id" id="restore-server-id">
                    <input type="hidden" name="backup_path" id="restore-backup-path">
                    
                    <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="create_backup" 
                               name="create_backup" checked>
                        <label class="custom-control-label" for="create_backup">
                            Create backup before restore
                        </label>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i> 
                        This will overwrite the current server configuration!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Restore Configuration</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Backup Modal -->
<div class="modal fade" id="viewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Backup Content</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <pre id="backup-content" class="language-json"><code>Loading...</code></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-json.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">

<script>
// Select all checkbox
$('#select-all').change(function() {
    $('.server-checkbox').prop('checked', $(this).prop('checked'));
});

// Update select all when individual checkboxes change
$('.server-checkbox').change(function() {
    var allChecked = $('.server-checkbox:checked').length === $('.server-checkbox').length;
    $('#select-all').prop('checked', allChecked);
});

function restoreBackup(serverId, backupPath, serverName) {
    $('#restore-server-id').val(serverId);
    $('#restore-backup-path').val(backupPath);
    $('#restore-server-name').text(serverName);
    $('#restoreModal').modal('show');
}

function viewBackup(backupPath) {
    $('#viewModal').modal('show');
    
    // In a real implementation, you would fetch the backup content from the server
    // For now, we'll just show a placeholder
    $('#backup-content code').text('{\n  "backup": "content",\n  "path": "' + backupPath + '"\n}');
    Prism.highlightAll();
}

function downloadBackup(backupPath) {
    // In a real implementation, you would trigger a download
    // For now, we'll just show a message
    toastr.info('Download feature coming soon for: ' + backupPath);
}
</script>
{% endblock %}