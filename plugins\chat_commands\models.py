"""
Data models for Chat Commands Plugin
"""

from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any
from datetime import datetime
import json


@dataclass
class ChatCommand:
    """Represents a chat command configuration"""
    command: str
    description: str
    response_text: str
    image_urls: List[str] = None
    required_params: List[str] = None
    enabled: bool = True
    created_at: str = None
    updated_at: str = None
    plugin_source: str = None  # Source plugin name for external commands
    handler_callback: Any = None  # Callback function for external command handling
    send_images_first: bool = False  # If True, send images before text for this command
    mark_as_unread: bool = False  # If True, mark conversation as unread after sending response

    def __post_init__(self):
        if self.image_urls is None:
            self.image_urls = []
        if self.required_params is None:
            self.required_params = []
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        if self.updated_at is None:
            self.updated_at = datetime.now().isoformat()
        # Ensure new fields have default values if not set
        if not hasattr(self, 'send_images_first'):
            self.send_images_first = False
        if not hasattr(self, 'mark_as_unread'):
            self.mark_as_unread = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, ensuring non-serializable fields are handled."""
        # Manually construct the dictionary to avoid issues with asdict() and unpicklable fields
        return {
            "command": self.command,
            "description": self.description,
            "response_text": self.response_text,
            "image_urls": self.image_urls,
            "required_params": self.required_params,
            "enabled": self.enabled,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "plugin_source": self.plugin_source,
            "send_images_first": self.send_images_first,
            "mark_as_unread": self.mark_as_unread
            # Explicitly exclude handler_callback
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatCommand':
        """Create from dictionary"""
        # Remove handler_callback from data as it's not serializable
        data_copy = data.copy()
        if 'handler_callback' in data_copy:
            del data_copy['handler_callback']

        # Ensure new fields have default values if not present
        data_copy.setdefault('send_images_first', False)
        data_copy.setdefault('mark_as_unread', False)

        return cls(**data_copy)



@dataclass
class WebhookConfig:
    """Webhook configuration for ShopeeAPI integration"""
    enabled: bool = True
    shopee_api_base_url: str = ""  # No default - must be configured by user
    steamcodetool_base_url: str = ""  # No default - must be configured by user
    webhook_endpoint: str = "/chat-commands/api/webhook"
    auto_register: bool = True
    retry_count: int = 3
    retry_delay: int = 5
    timeout: int = 30

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WebhookConfig':
        """Create from dictionary"""
        return cls(**data)

    def get_full_webhook_url(self) -> str:
        """Get the complete webhook URL"""
        return f"{self.steamcodetool_base_url}{self.webhook_endpoint}"

    def get_shopee_webhook_register_url(self) -> str:
        """Get the ShopeeAPI webhook registration URL"""
        return f"{self.shopee_api_base_url}/api/webhooks/register"


@dataclass
class DebugConfig:
    """Debug configuration for controlling console output"""
    enabled: bool = False
    log_webhook_data: bool = False
    log_message_parsing: bool = False
    log_command_processing: bool = False
    log_response_generation: bool = False
    log_info_messages: bool = False
    log_access_audit: bool = False
    log_werkzeug: bool = False
    log_service_operations: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DebugConfig':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class WebhookMessage:
    """Represents a webhook message from ShopeeAPI"""
    conversation_id: str
    message_id: str
    content: str
    sender_id: str
    sender_name: str
    timestamp: str
    message_type: str = "text"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_webhook_data(cls, webhook_data: Dict[str, Any]) -> 'WebhookMessage':
        """Create from webhook data - handles multiple webhook formats"""
        import logging
        logger = logging.getLogger(__name__)

        # Get debug config to control logging
        debug_enabled = cls._should_log_debug(webhook_data)

        # Log the incoming webhook data for debugging
        if debug_enabled:
            logger.info(f"Parsing webhook data: {json.dumps(webhook_data, indent=2)}")

        # Handle the current webhook format: {"type": "shopee_message", "data": {...}}
        if webhook_data.get('type') == 'shopee_message':
            data = webhook_data.get('data', {})
            message_content = data.get('message_content', '')

            # Parse the JSON-encoded message content
            try:
                if isinstance(message_content, str):
                    parsed_content = json.loads(message_content)
                else:
                    parsed_content = message_content

                if debug_enabled:
                    logger.info(f"Parsed message content: {json.dumps(parsed_content, indent=2)}")

                # Check if this is an actual message or just a status update
                msg_type = parsed_content.get('type', '')
                if msg_type == 'chat_update_msg_status':
                    if debug_enabled:
                        logger.info("Received status update, not a message - ignoring")
                    # Return empty content for status updates
                    return cls(
                        conversation_id=parsed_content.get('conversation_id', ''),
                        message_id=data.get('message_id', ''),
                        content='',  # Empty content for status updates
                        sender_id=str(parsed_content.get('user_id', '')),
                        sender_name='',
                        timestamp=webhook_data.get('server_time', datetime.now().isoformat()),
                        message_type='status_update'
                    )

                # Handle actual message content
                content_data = parsed_content.get('content', {})
                text_content = ''

                # Extract text from various possible locations
                if isinstance(content_data, dict):
                    text_content = content_data.get('text', '')
                elif isinstance(content_data, str):
                    text_content = content_data

                # If no text in content, check if the message itself has text
                if not text_content:
                    text_content = parsed_content.get('text', '')

                # Determine the correct sender name based on message direction
                # For MESSAGE_RECEIVED: from_user_name is the customer (sender)
                # For MESSAGE_SENT: to_user_name is the customer (recipient)
                sender_name = ''
                send_by_yourself = parsed_content.get('send_by_yourself', False)

                if send_by_yourself:
                    # This is a message we sent, so the customer is the recipient (to_user_name)
                    sender_name = parsed_content.get('to_user_name', '')
                    if debug_enabled:
                        logger.info(f"MESSAGE_SENT detected: using to_user_name '{sender_name}' as customer")
                else:
                    # This is a message we received, so the customer is the sender (from_user_name)
                    sender_name = parsed_content.get('from_user_name', '')
                    if debug_enabled:
                        logger.info(f"MESSAGE_RECEIVED detected: using from_user_name '{sender_name}' as customer")

                # Fallback to from_name if no username found
                if not sender_name:
                    sender_name = parsed_content.get('from_name', '')
                    if debug_enabled:
                        logger.warning(f"No username found, falling back to from_name: '{sender_name}'")

                return cls(
                    conversation_id=parsed_content.get('conversation_id', ''),
                    message_id=data.get('message_id', ''),
                    content=text_content,
                    sender_id=str(parsed_content.get('from_id', parsed_content.get('user_id', ''))),
                    sender_name=sender_name,
                    timestamp=webhook_data.get('server_time', datetime.now().isoformat()),
                    message_type=data.get('message_type', 'text')
                )
                
            except json.JSONDecodeError as e:
                if debug_enabled:
                    logger.error(f"Failed to parse message_content JSON: {e}")
                    logger.error(f"Raw message_content: {message_content}")
                # Return with raw content if JSON parsing fails
                return cls(
                    conversation_id='',
                    message_id=data.get('message_id', ''),
                    content=str(message_content),
                    sender_id='',
                    sender_name='',
                    timestamp=webhook_data.get('server_time', datetime.now().isoformat()),
                    message_type=data.get('message_type', 'text')
                )
        
        # Handle the original expected format: {"message": {...}, "conversation_id": "..."}
        message = webhook_data.get('message', {})

        # Apply the same username logic for legacy format
        sender_name = ''
        send_by_yourself = message.get('send_by_yourself', False)

        if send_by_yourself:
            # This is a message we sent, so the customer is the recipient
            sender_name = message.get('to_user_name', '') or message.get('to_name', '')
        else:
            # This is a message we received, so the customer is the sender
            sender_name = message.get('from_user_name', '') or message.get('from_name', '')

        # Final fallback for legacy format
        if not sender_name:
            sender_name = message.get('from_name', '')
            if debug_enabled:
                logger.warning(f"Legacy format: No username found, falling back to from_name: '{sender_name}'")

        return cls(
            conversation_id=webhook_data.get('conversation_id', ''),
            message_id=message.get('message_id', ''),
            content=message.get('content', {}).get('text', ''),
            sender_id=message.get('from_id', ''),
            sender_name=sender_name,
            timestamp=message.get('timestamp', datetime.now().isoformat()),
            message_type=message.get('type', 'text')
        )

    @staticmethod
    def _should_log_debug(webhook_data: Dict[str, Any]) -> bool:
        """Check if debug logging should be enabled"""
        # Try to get debug config from global state or default to False
        # This is a simple implementation - in a real scenario you might want to
        # pass the debug config as a parameter or use a global registry
        try:
            # Try to import and get debug config from service
            # This is a bit of a hack but works for our use case
            import os
            import json as json_lib
            
            plugin_dir = os.path.dirname(__file__)
            config_file = os.path.join(plugin_dir, 'config.json')
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json_lib.load(f)
                    debug_config = config_data.get('debug_config', {})
                    return debug_config.get('enabled', False) and debug_config.get('log_message_parsing', False)
        except:
            pass
        
        return False


@dataclass
class CommandConfig:
    """Configuration for command processing"""
    command_prefix: str = "#"
    case_sensitive: bool = False
    max_response_length: int = 4000
    max_images_per_response: int = 5
    send_images_first: bool = False  # If True, send images before text (global default)
    message_split_limit: int = 600  # Character limit for auto-splitting messages

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CommandConfig':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class CommandResponse:
    """Represents a response to be sent"""
    text: str = ""
    image_urls: List[str] = None
    mark_as_unread: bool = False  # If True, mark conversation as unread after sending

    def __post_init__(self):
        if self.image_urls is None:
            self.image_urls = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
