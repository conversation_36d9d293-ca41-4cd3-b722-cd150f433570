{"permissions": {"allow": ["Bash(cp:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(python3:*)", "Bash(find:*)", "Bash(rm:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(curl:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(PYTHONPATH=/mnt/d/SourceCode/SteamCodeTool python3 -c \"import sys; sys.path.insert(0, ''.''); import services.chat_service\" 2 >& 1)", "<PERSON><PERSON>(timeout:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker-compose down:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(chmod:*)", "Bash(chown:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp -r:*)", "<PERSON><PERSON>(rmdir:*)", "Bash(ln:*)", "Bash(ssh:*)", "<PERSON><PERSON>(scp:*)", "<PERSON><PERSON>(crontab:*)", "Bash(systemctl:*)", "Bash(service:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "Bash(tar:*)", "Bash(zip:*)", "Ba<PERSON>(unzip:*)", "<PERSON><PERSON>(wget:*)", "<PERSON><PERSON>(curl -O:*)", "Bash(ping:*)", "<PERSON><PERSON>(netstat:*)", "Bash(ss:*)", "Bash(ifconfig:*)", "Bash(ip:*)", "Bash(df:*)", "<PERSON><PERSON>(du:*)", "<PERSON><PERSON>(free:*)", "<PERSON><PERSON>(top:*)", "<PERSON><PERSON>(htop:*)", "Bash(ps:*)", "Bash(kill:*)", "<PERSON><PERSON>(killall:*)", "Bash(xargs:*)", "<PERSON><PERSON>(tr:*)", "Bash(sort:*)", "<PERSON><PERSON>(uniq:*)", "<PERSON><PERSON>(diff:*)", "Bash(patch:*)", "<PERSON><PERSON>(tee:*)", "<PERSON><PERSON>(yes:*)", "<PERSON><PERSON>(nohup:*)", "<PERSON><PERSON>(screen:*)", "<PERSON><PERSON>(tmux:*)", "Bash(vi:*)", "Bash(nano:*)", "Bash(emacs:*)", "Bash(less:*)", "Bash(more:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "<PERSON><PERSON>(date:*)", "Bash(cal:*)", "Bash(bc:*)", "<PERSON><PERSON>(expr:*)", "Bash(seq:*)", "Bash(factor:*)", "Bash(uname:*)", "<PERSON><PERSON>(hostname:*)", "<PERSON><PERSON>(whoami:*)", "<PERSON><PERSON>(users:*)", "Bash(groups:*)", "Bash(id:*)", "Bash(env:*)", "<PERSON><PERSON>(printenv:*)", "<PERSON><PERSON>(set:*)", "Bash(export:*)", "<PERSON><PERSON>(alias:*)", "<PERSON><PERSON>(unalias:*)", "<PERSON><PERSON>(history:*)", "<PERSON><PERSON>(clear:*)", "Bash(reset:*)", "<PERSON><PERSON>(exit:*)", "<PERSON><PERSON>(logout:*)", "Bash(shutdown:*)", "<PERSON><PERSON>(reboot:*)", "Bash(halt:*)", "<PERSON><PERSON>(sleep:*)", "<PERSON><PERSON>(wait:*)", "<PERSON><PERSON>(time:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(yes:*)", "<PERSON><PERSON>(no:*)", "Bash(sed -i '/print(f\"\"Exception in send_order_message:/d; /print(traceback.format_exc())/d; /print(f\"\"Sending order message for order:/d; /print(f\"\"Searching for order:/d; /print(f\"\"Order search result:/d; /print(f\"\"Order.*not found\"\")/d; /print(\"\"Found package_level_order_card structure\"\")/d; /print(\"\"Found order_card structure\"\")/d; /print(\"\"Failed to extract order_id from order data\"\")/d; /print(\"\"Failed to extract buyer_user_id from order data\"\")/d; /print(f\"\"Found order_id:.*buyer_user_id:/d; /print(f\"\"Getting conversation info by user_id:/d' ShopeeAPI/services/chat.py)", "<PERSON><PERSON>(pkill:*)"], "deny": []}}