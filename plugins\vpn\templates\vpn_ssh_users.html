<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH User Management - VPN Plugin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .server-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .server-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
        }
        .user-table {
            font-size: 0.9em;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .session-info {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .violation-alert {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .autokill-config {
            background-color: #e7f3ff;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users"></i> SSH User Management</h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshAllData()">
                            <i class="fas fa-sync-alt"></i> Refresh All
                        </button>
                        <a href="{{ url_for('vpn.health') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>

                {% if servers %}
                    {% for server in servers %}
                    <div class="server-card" id="server-{{ server.id }}">
                        <div class="server-header">
                            <h4 class="mb-0">
                                <i class="fas fa-server"></i> {{ server.name }}
                                <small class="ms-2">{{ server.host }}:{{ server.port }}</small>
                                <span class="badge bg-light text-dark ms-2">ID: {{ server.id }}</span>
                            </h4>
                        </div>
                        
                        <div class="card-body">
                            <!-- Navigation tabs for each server -->
                            <ul class="nav nav-tabs" id="server{{ server.id }}Tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="users{{ server.id }}Tab" data-bs-toggle="tab" 
                                            data-bs-target="#users{{ server.id }}" type="button" role="tab">
                                        <i class="fas fa-users"></i> Users
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="sessions{{ server.id }}Tab" data-bs-toggle="tab" 
                                            data-bs-target="#sessions{{ server.id }}" type="button" role="tab">
                                        <i class="fas fa-terminal"></i> Sessions
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="autokill{{ server.id }}Tab" data-bs-toggle="tab" 
                                            data-bs-target="#autokill{{ server.id }}" type="button" role="tab">
                                        <i class="fas fa-cog"></i> Auto-Kill
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="violations{{ server.id }}Tab" data-bs-toggle="tab" 
                                            data-bs-target="#violations{{ server.id }}" type="button" role="tab">
                                        <i class="fas fa-exclamation-triangle"></i> Violations
                                    </button>
                                </li>
                            </ul>

                            <!-- Tab content -->
                            <div class="tab-content mt-3" id="server{{ server.id }}TabContent">
                                <!-- Users Tab -->
                                <div class="tab-pane fade show active" id="users{{ server.id }}" role="tabpanel">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5>SSH Users</h5>
                                        <div>
                                            <button class="btn btn-sm btn-danger" onclick="autoDeleteExpired({{ server.id }})">
                                                <i class="fas fa-trash"></i> Delete Expired
                                            </button>
                                            <button class="btn btn-sm btn-primary" onclick="loadUsers({{ server.id }})">
                                                <i class="fas fa-sync-alt"></i> Refresh
                                            </button>
                                        </div>
                                    </div>
                                    <div id="users-{{ server.id }}" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading users...
                                    </div>
                                </div>

                                <!-- Sessions Tab -->
                                <div class="tab-pane fade" id="sessions{{ server.id }}" role="tabpanel">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5>Active Sessions</h5>
                                        <button class="btn btn-sm btn-primary" onclick="loadSessions({{ server.id }})">
                                            <i class="fas fa-sync-alt"></i> Refresh
                                        </button>
                                    </div>
                                    <div id="sessions-{{ server.id }}" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading sessions...
                                    </div>
                                </div>

                                <!-- Auto-Kill Tab -->
                                <div class="tab-pane fade" id="autokill{{ server.id }}" role="tabpanel">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5>Auto-Kill Configuration</h5>
                                        <button class="btn btn-sm btn-primary" onclick="loadAutokillStatus({{ server.id }})">
                                            <i class="fas fa-sync-alt"></i> Refresh
                                        </button>
                                    </div>
                                    <div id="autokill-{{ server.id }}" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading auto-kill status...
                                    </div>
                                </div>

                                <!-- Violations Tab -->
                                <div class="tab-pane fade" id="violations{{ server.id }}" role="tabpanel">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5>Multi-Login Violations</h5>
                                        <button class="btn btn-sm btn-primary" onclick="loadViolations({{ server.id }})">
                                            <i class="fas fa-sync-alt"></i> Refresh
                                        </button>
                                    </div>
                                    <div id="violations-{{ server.id }}" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading violations...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        No servers found. Please <a href="{{ url_for('vpn.servers') }}">add servers</a> first.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Renew User Modal -->
    <div class="modal fade" id="renewUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Renew SSH User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="renewUserForm">
                        <input type="hidden" id="renewServerId">
                        <input type="hidden" id="renewUsername">
                        <div class="mb-3">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-control" id="renewUsernameDisplay" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="renewDays" class="form-label">Extend by (days)</label>
                            <input type="number" class="form-control" id="renewDays" min="1" max="365" value="30" required>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="renewUnlock" checked>
                                <label class="form-check-label" for="renewUnlock">
                                    Unlock user account
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitRenewUser()">Renew User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Configure Auto-Kill Modal -->
    <div class="modal fade" id="configureAutokillModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Configure Auto-Kill</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="configureAutokillForm">
                        <input type="hidden" id="autokillServerId">
                        <div class="mb-3">
                            <label for="autokillInterval" class="form-label">Check Interval (minutes)</label>
                            <select class="form-select" id="autokillInterval" required>
                                <option value="5">5 minutes</option>
                                <option value="10">10 minutes</option>
                                <option value="15">15 minutes</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="autokillMaxSessions" class="form-label">Maximum Sessions per User</label>
                            <input type="number" class="form-control" id="autokillMaxSessions" min="1" max="10" value="2" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitConfigureAutokill()">Configure</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            {% for server in servers %}
            loadUsers({{ server.id }});
            {% endfor %}
        });

        // Load SSH users for a server
        function loadUsers(serverId) {
            const container = document.getElementById(`users-${serverId}`);
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading users...</div>';

            fetch(`/admin/vpn/api/servers/${serverId}/ssh-users/members`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> ${data.error}</div>`;
                        return;
                    }

                    let html = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><strong>Total Users:</strong> ${data.count || 0}</span>
                        </div>
                    `;

                    if (data.members && data.members.length > 0) {
                        html += `
                            <div class="table-responsive">
                                <table class="table table-sm user-table">
                                    <thead>
                                        <tr>
                                            <th>Username</th>
                                            <th>UID</th>
                                            <th>Expiry</th>
                                            <th>Status</th>
                                            <th>Locked</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        data.members.forEach(user => {
                            const statusBadge = user.status === 'active' ? 'bg-success' : 'bg-warning';
                            const lockedBadge = user.locked ? 'bg-danger' : 'bg-success';
                            const lockedText = user.locked ? 'Yes' : 'No';

                            html += `
                                <tr>
                                    <td><strong>${user.username}</strong></td>
                                    <td>${user.uid}</td>
                                    <td>${user.expiry || 'N/A'}</td>
                                    <td><span class="badge ${statusBadge} status-badge">${user.status || 'unknown'}</span></td>
                                    <td><span class="badge ${lockedBadge} status-badge">${lockedText}</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="showRenewModal(${serverId}, '${user.username}')">
                                            <i class="fas fa-clock"></i> Renew
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table></div>';
                    } else {
                        html += '<div class="alert alert-info">No SSH users found on this server.</div>';
                    }

                    container.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> Error loading users: ${error.message}</div>`;
                });
        }

        // Load SSH sessions for a server
        function loadSessions(serverId) {
            const container = document.getElementById(`sessions-${serverId}`);
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading sessions...</div>';

            fetch(`/admin/vpn/api/servers/${serverId}/ssh-users/sessions`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> ${data.error}</div>`;
                        return;
                    }

                    let html = '';

                    // Dropbear sessions
                    if (data.dropbear && data.dropbear.length > 0) {
                        html += `
                            <div class="session-info">
                                <h6><i class="fas fa-terminal"></i> Dropbear Sessions (${data.dropbear.length})</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr><th>PID</th><th>Username</th><th>IP Address</th></tr>
                                        </thead>
                                        <tbody>
                        `;
                        data.dropbear.forEach(session => {
                            html += `<tr><td>${session.pid}</td><td>${session.username}</td><td>${session.ip}</td></tr>`;
                        });
                        html += '</tbody></table></div></div>';
                    }

                    // OpenSSH sessions
                    if (data.openssh && data.openssh.length > 0) {
                        html += `
                            <div class="session-info">
                                <h6><i class="fas fa-terminal"></i> OpenSSH Sessions (${data.openssh.length})</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr><th>PID</th><th>Username</th><th>IP Address</th></tr>
                                        </thead>
                                        <tbody>
                        `;
                        data.openssh.forEach(session => {
                            html += `<tr><td>${session.pid}</td><td>${session.username}</td><td>${session.ip}</td></tr>`;
                        });
                        html += '</tbody></table></div></div>';
                    }

                    // OpenVPN TCP sessions
                    if (data.openvpn_tcp && data.openvpn_tcp.length > 0) {
                        html += `
                            <div class="session-info">
                                <h6><i class="fas fa-shield-alt"></i> OpenVPN TCP Sessions (${data.openvpn_tcp.length})</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr><th>Username</th><th>IP Address</th><th>Connected Since</th></tr>
                                        </thead>
                                        <tbody>
                        `;
                        data.openvpn_tcp.forEach(session => {
                            html += `<tr><td>${session.username}</td><td>${session.ip}</td><td>${session.connected_since}</td></tr>`;
                        });
                        html += '</tbody></table></div></div>';
                    }

                    // OpenVPN UDP sessions
                    if (data.openvpn_udp && data.openvpn_udp.length > 0) {
                        html += `
                            <div class="session-info">
                                <h6><i class="fas fa-shield-alt"></i> OpenVPN UDP Sessions (${data.openvpn_udp.length})</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr><th>Username</th><th>IP Address</th><th>Connected Since</th></tr>
                                        </thead>
                                        <tbody>
                        `;
                        data.openvpn_udp.forEach(session => {
                            html += `<tr><td>${session.username}</td><td>${session.ip}</td><td>${session.connected_since}</td></tr>`;
                        });
                        html += '</tbody></table></div></div>';
                    }

                    if (!html) {
                        html = '<div class="alert alert-info">No active sessions found on this server.</div>';
                    }

                    container.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading sessions:', error);
                    container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> Error loading sessions: ${error.message}</div>`;
                });
        }

        // Load auto-kill status for a server
        function loadAutokillStatus(serverId) {
            const container = document.getElementById(`autokill-${serverId}`);
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading auto-kill status...</div>';

            fetch(`/admin/vpn/api/servers/${serverId}/ssh-users/autokill/status`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> ${data.error}</div>`;
                        return;
                    }

                    let html = '<div class="autokill-config">';

                    if (data.enabled) {
                        html += `
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-check-circle text-success"></i> Auto-Kill Enabled</h6>
                                <button class="btn btn-sm btn-warning" onclick="showConfigureAutokillModal(${serverId}, ${data.interval_minutes}, ${data.max_sessions})">
                                    <i class="fas fa-edit"></i> Reconfigure
                                </button>
                            </div>
                            <p><strong>Check Interval:</strong> ${data.interval_minutes} minutes</p>
                            <p><strong>Max Sessions:</strong> ${data.max_sessions} per user</p>
                        `;
                    } else {
                        html += `
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-times-circle text-danger"></i> Auto-Kill Disabled</h6>
                                <button class="btn btn-sm btn-success" onclick="showConfigureAutokillModal(${serverId}, 5, 2)">
                                    <i class="fas fa-plus"></i> Enable
                                </button>
                            </div>
                            <p class="text-muted">Auto-kill is not configured for this server.</p>
                        `;
                    }

                    html += '</div>';
                    container.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading auto-kill status:', error);
                    container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> Error loading auto-kill status: ${error.message}</div>`;
                });
        }

        // Load violations for a server
        function loadViolations(serverId) {
            const container = document.getElementById(`violations-${serverId}`);
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading violations...</div>';

            fetch(`/admin/vpn/api/servers/${serverId}/ssh-users/limit-violations`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> ${data.error}</div>`;
                        return;
                    }

                    let html = '';

                    if (data.exists && data.lines && data.lines.length > 0) {
                        html += `
                            <div class="violation-alert">
                                <h6><i class="fas fa-exclamation-triangle text-warning"></i> Multi-Login Violations Found (${data.lines.length})</h6>
                                <div class="mt-2">
                        `;
                        data.lines.forEach(line => {
                            html += `<div class="mb-1"><code>${line}</code></div>`;
                        });
                        html += '</div></div>';
                    } else {
                        html += '<div class="alert alert-success"><i class="fas fa-check-circle"></i> No multi-login violations found.</div>';
                    }

                    container.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading violations:', error);
                    container.innerHTML = `<div class="error"><i class="fas fa-exclamation-circle"></i> Error loading violations: ${error.message}</div>`;
                });
        }

        // Show renew user modal
        function showRenewModal(serverId, username) {
            document.getElementById('renewServerId').value = serverId;
            document.getElementById('renewUsername').value = username;
            document.getElementById('renewUsernameDisplay').value = username;
            document.getElementById('renewDays').value = 30;
            document.getElementById('renewUnlock').checked = true;

            const modal = new bootstrap.Modal(document.getElementById('renewUserModal'));
            modal.show();
        }

        // Submit renew user form
        function submitRenewUser() {
            const serverId = document.getElementById('renewServerId').value;
            const username = document.getElementById('renewUsername').value;
            const days = parseInt(document.getElementById('renewDays').value);
            const unlock = document.getElementById('renewUnlock').checked;

            if (!days || days < 1) {
                alert('Please enter a valid number of days');
                return;
            }

            const data = {
                username: username,
                days: days,
                unlock: unlock
            };

            fetch(`/admin/vpn/api/servers/${serverId}/ssh-users/renew`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.error) {
                    alert(`Error: ${result.error}`);
                } else if (result.success) {
                    alert(`User ${username} renewed successfully. New expiry: ${result.new_expires_on}`);
                    loadUsers(serverId);
                    bootstrap.Modal.getInstance(document.getElementById('renewUserModal')).hide();
                } else {
                    alert(`Failed to renew user: ${result.message || 'Unknown error'}`);
                }
            })
            .catch(error => {
                console.error('Error renewing user:', error);
                alert(`Error renewing user: ${error.message}`);
            });
        }

        // Show configure auto-kill modal
        function showConfigureAutokillModal(serverId, currentInterval = 5, currentMaxSessions = 2) {
            document.getElementById('autokillServerId').value = serverId;
            document.getElementById('autokillInterval').value = currentInterval;
            document.getElementById('autokillMaxSessions').value = currentMaxSessions;

            const modal = new bootstrap.Modal(document.getElementById('configureAutokillModal'));
            modal.show();
        }

        // Submit configure auto-kill form
        function submitConfigureAutokill() {
            const serverId = document.getElementById('autokillServerId').value;
            const intervalMinutes = parseInt(document.getElementById('autokillInterval').value);
            const maxSessions = parseInt(document.getElementById('autokillMaxSessions').value);

            if (!intervalMinutes || !maxSessions || maxSessions < 1) {
                alert('Please enter valid values');
                return;
            }

            const data = {
                interval_minutes: intervalMinutes,
                max_sessions: maxSessions
            };

            fetch(`/admin/vpn/api/servers/${serverId}/ssh-users/autokill/configure`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.error) {
                    alert(`Error: ${result.error}`);
                } else if (result.enabled) {
                    alert(`Auto-kill configured successfully. Interval: ${result.interval_minutes} minutes, Max sessions: ${result.max_sessions}`);
                    loadAutokillStatus(serverId);
                    bootstrap.Modal.getInstance(document.getElementById('configureAutokillModal')).hide();
                } else {
                    alert('Failed to configure auto-kill');
                }
            })
            .catch(error => {
                console.error('Error configuring auto-kill:', error);
                alert(`Error configuring auto-kill: ${error.message}`);
            });
        }

        // Auto delete expired users
        function autoDeleteExpired(serverId) {
            if (!confirm('Are you sure you want to delete all expired SSH users? This action cannot be undone.')) {
                return;
            }

            fetch(`/admin/vpn/api/servers/${serverId}/ssh-users/auto-delete-expired`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(result => {
                if (result.error) {
                    alert(`Error: ${result.error}`);
                } else if (result.success) {
                    const deletedCount = result.deleted_count || 0;
                    const deletedUsers = result.deleted || [];

                    if (deletedCount > 0) {
                        alert(`Successfully deleted ${deletedCount} expired users: ${deletedUsers.join(', ')}`);
                    } else {
                        alert('No expired users found to delete.');
                    }

                    loadUsers(serverId);
                } else {
                    alert('Failed to delete expired users');
                }
            })
            .catch(error => {
                console.error('Error deleting expired users:', error);
                alert(`Error deleting expired users: ${error.message}`);
            });
        }

        // Refresh all data
        function refreshAllData() {
            {% for server in servers %}
            loadUsers({{ server.id }});
            loadSessions({{ server.id }});
            loadAutokillStatus({{ server.id }});
            loadViolations({{ server.id }});
            {% endfor %}
        }
    </script>
</body>
</html>
