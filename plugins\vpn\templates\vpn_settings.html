{% extends "base.html" %}

{% block title %}VPN Settings{% endblock %}

{% block content %}
<div x-data="vpnSettingsData()" x-init="init()" class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">VPN Settings</h1>
        <div class="flex space-x-2">
            <button @click="testConfiguration()" 
                    :disabled="testing"
                    class="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-md">
                <span x-show="!testing">Test Connection</span>
                <span x-show="testing">Testing...</span>
            </button>
            <button @click="saveConfiguration()" 
                    :disabled="saving"
                    class="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-2 rounded-md">
                <span x-show="!saving">Save Settings</span>
                <span x-show="saving">Saving...</span>
            </button>
        </div>
    </div>

    <!-- Connection Status -->
    <div x-show="connectionStatus" 
         :class="connectionStatus === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700'"
         class="border px-4 py-3 rounded">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg x-show="connectionStatus === 'success'" class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <svg x-show="connectionStatus === 'error'" class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm" x-text="connectionMessage"></p>
            </div>
        </div>
    </div>

    <!-- Configuration Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">VPN API Configuration</h3>
            
            <div class="grid grid-cols-1 gap-6">
                <!-- Plugin Enabled -->
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="config.enabled" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Enable VPN Plugin</span>
                    </label>
                </div>

                <!-- API Settings -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900">API Settings</h4>
                    
                    <div>
                        <label for="base_url" class="block text-sm font-medium text-gray-700">Base URL</label>
                        <div class="mt-1 space-y-2">
                            <!-- Quick presets -->
                            <div class="flex flex-wrap gap-2">
                                <button type="button" @click="setPresetUrl('production')" 
                                        class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                                        :class="{'bg-blue-500 text-white': config.vpn_api.base_url === 'https://blueblue.api.limjianhui.com'}">
                                    Production
                                </button>
                                <button type="button" @click="setPresetUrl('localhost')" 
                                        class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200"
                                        :class="{'bg-green-500 text-white': config.vpn_api.base_url === 'http://localhost:8000'}">
                                    Localhost
                                </button>
                                <button type="button" @click="setPresetUrl('development')" 
                                        class="px-3 py-1 text-xs bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200"
                                        :class="{'bg-orange-500 text-white': config.vpn_api.base_url === 'http://localhost:3000'}">
                                    Development
                                </button>
                            </div>
                            <!-- Custom URL input -->
                            <input type="url" id="base_url" x-model="config.vpn_api.base_url" 
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="https://blueblue.api.limjianhui.com">
                        </div>
                        <div class="mt-1 flex items-center space-x-2">
                            <span class="text-xs text-gray-500">Select a preset or enter a custom API backend URL</span>
                            <span x-show="getCurrentEnvironment()" 
                                  :class="getEnvironmentClass()" 
                                  class="px-2 py-1 text-xs rounded-full"
                                  x-text="getCurrentEnvironment()"></span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                            <input type="text" id="username" x-model="config.vpn_api.username" 
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="admin">
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                            <input type="password" id="password" x-model="config.vpn_api.password" 
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="admin123">
                        </div>
                    </div>

                    <div>
                        <label for="timeout" class="block text-sm font-medium text-gray-700">Timeout (seconds)</label>
                        <input type="number" id="timeout" x-model="config.vpn_api.timeout" min="5" max="120"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <!-- Client Configuration -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900">Client Configuration</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="default_traffic_gb" class="block text-sm font-medium text-gray-700">Default Traffic (GB)</label>
                            <input type="number" id="default_traffic_gb" x-model="config.client_config.default_traffic_gb" min="1"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="default_expiry_days" class="block text-sm font-medium text-gray-700">Default Expiry (days)</label>
                            <input type="number" id="default_expiry_days" x-model="config.client_config.default_expiry_days" min="1"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>

                <!-- Redemption Configuration -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900">Redemption Configuration</h4>
                    
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="config.redemption.enabled" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Enable VPN Redemption</span>
                        </label>
                    </div>

                    <div>
                        <label for="redemption_expiry_days" class="block text-sm font-medium text-gray-700">Default Redemption Expiry (days)</label>
                        <input type="number" id="redemption_expiry_days" x-model="config.redemption.default_expiry_days" min="1"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <!-- File Paths -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900">File Paths</h4>
                    
                    <div>
                        <label for="servers_file" class="block text-sm font-medium text-gray-700">Servers File</label>
                        <input type="text" id="servers_file" x-model="config.server_config.servers_file" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               readonly>
                    </div>

                    <div>
                        <label for="templates_file" class="block text-sm font-medium text-gray-700">Templates File</label>
                        <input type="text" id="templates_file" x-model="config.config_templates.templates_file" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               readonly>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function vpnSettingsData() {
    return {
        config: {
            enabled: true,
            vpn_api: {
                base_url: 'https://blueblue.api.limjianhui.com',
                username: 'admin',
                password: 'admin123',
                timeout: 30
            },
            client_config: {
                default_traffic_gb: 100,
                default_expiry_days: 30
            },
            redemption: {
                enabled: true,
                default_expiry_days: 30
            },
            server_config: {
                servers_file: 'configs/services/vpn_servers.json',
                connection_timeout: 30
            },
            config_templates: {
                templates_file: 'configs/services/config_templates.json'
            }
        },
        loading: false,
        saving: false,
        testing: false,
        connectionStatus: null,
        connectionMessage: '',

        init() {
            this.loadConfiguration();
        },

        setPresetUrl(preset) {
            const presets = {
                'production': 'https://blueblue.api.limjianhui.com',
                'localhost': 'http://localhost:8000',
                'development': 'http://localhost:3000'
            };
            
            if (presets[preset]) {
                this.config.vpn_api.base_url = presets[preset];
            }
        },

        getCurrentEnvironment() {
            const url = this.config.vpn_api.base_url;
            if (url === 'https://blueblue.api.limjianhui.com') return 'Production';
            if (url === 'http://localhost:8000') return 'Localhost';
            if (url === 'http://localhost:3000') return 'Development';
            if (url && url.includes('localhost')) return 'Local';
            if (url && url.includes('dev')) return 'Dev';
            return url ? 'Custom' : '';
        },

        getEnvironmentClass() {
            const env = this.getCurrentEnvironment();
            if (env === 'Production') return 'bg-blue-100 text-blue-800';
            if (env === 'Localhost' || env === 'Local') return 'bg-green-100 text-green-800';
            if (env === 'Development' || env === 'Dev') return 'bg-orange-100 text-orange-800';
            return 'bg-gray-100 text-gray-800';
        },

        loadConfiguration() {
            this.loading = true;
            fetch('/admin/vpn/api/config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.config = data.config;
                    } else {
                        this.showAlert('Failed to load configuration: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading configuration:', error);
                    this.showAlert('Error loading configuration', 'error');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        saveConfiguration() {
            this.saving = true;
            fetch('/admin/vpn/api/config', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.config)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.showAlert('Configuration saved successfully', 'success');
                    } else {
                        this.showAlert('Failed to save configuration: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error saving configuration:', error);
                    this.showAlert('Error saving configuration', 'error');
                })
                .finally(() => {
                    this.saving = false;
                });
        },

        testConfiguration() {
            this.testing = true;
            this.connectionStatus = null;
            
            fetch('/admin/vpn/api/test_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.config)
            })
                .then(response => response.json())
                .then(data => {
                    this.connectionStatus = data.success ? 'success' : 'error';
                    this.connectionMessage = data.message;
                })
                .catch(error => {
                    console.error('Error testing configuration:', error);
                    this.connectionStatus = 'error';
                    this.connectionMessage = 'Error testing configuration';
                })
                .finally(() => {
                    this.testing = false;
                });
        },

        showAlert(message, type) {
            // You can implement a toast notification system here
            if (type === 'success') {
                alert('✅ ' + message);
            } else {
                alert('❌ ' + message);
            }
        }
    };
}
</script>
{% endblock %}
