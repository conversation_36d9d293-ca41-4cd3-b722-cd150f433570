{"test_environment": {"name": "VPN Config Generator Test Environment", "version": "1.0.0", "description": "Test configuration for VPN Config Generator Plugin"}, "api_config": {"enabled": true, "use_vpn_plugin_api": false, "fallback_api_endpoint": "https://test.api.example.com", "fallback_username": "test_admin", "fallback_password": "test_password123", "timeout": 30}, "generator_settings": {"default_server": "server11", "default_days": "30", "default_telco": "digi", "default_plan": "unlimited", "auto_generate_username": true, "username_prefix": "testuser"}, "test_data": {"valid_requests": [{"server": "server11", "days": "30", "telco": "digi", "plan": "unlimited", "username": "testuser1"}, {"server": "server12", "days": "7", "telco": "maxis", "plan": "basic", "username": "testuser2"}], "invalid_requests": [{"server": "", "days": "30", "telco": "digi", "plan": "unlimited", "username": "testuser"}, {"server": "server11", "days": "0", "telco": "digi", "plan": "unlimited", "username": "testuser"}], "test_templates": [{"id": "template1", "name": "Digi 30 Days", "description": "Standard Digi configuration for 30 days", "server": "server11", "days": "30", "telco": "digi", "plan": "unlimited", "enabled": true}, {"id": "template2", "name": "Maxis 7 Days", "description": "Basic Maxis configuration for 7 days", "server": "server12", "days": "7", "telco": "maxis", "plan": "basic", "enabled": true}]}, "mock_responses": {"successful_auth": {"status_code": 200, "response": {"access_token": "test_access_token_12345", "token_type": "Bearer", "expires_in": 3600}}, "successful_config_generation": {"status_code": 201, "response": {"config_url": "vmess://eyJ2IjoiMiIsInBzIjoidGVzdC1jb25maWciLCJhZGQiOiJ0ZXN0LnNlcnZlci5jb20iLCJwb3J0IjoiNDQzIiwiaWQiOiJ0ZXN0LXV1aWQiLCJhaWQiOiIwIiwibmV0Ijoid3MiLCJ0eXBlIjoibm9uZSIsImhvc3QiOiJ0ZXN0LnNlcnZlci5jb20iLCJwYXRoIjoiL3dzIiwidGxzIjoidGxzIn0=", "created_date": "2024-01-01", "expired_date": "2024-01-31", "server_info": {"server_id": 11, "server_name": "server11", "location": "Singapore"}}}, "failed_auth": {"status_code": 401, "response": {"error": "Unauthorized", "message": "Invalid credentials"}}, "failed_config_generation": {"status_code": 400, "response": {"error": "Bad Request", "message": "Invalid server configuration"}}}, "test_settings": {"timeout": 10, "retry_count": 3, "cleanup_temp_files": true, "verbose_logging": false}}