@echo off
REM Quick Docker Hub push script for SteamCodeTool
REM This script quickly builds and pushes the latest changes

echo Quick Docker Hub Push for SteamCodeTool
echo ==========================================

REM Configuration
set DOCKER_HUB_USERNAME=limjianhui789
set DOCKER_HUB_REPO=mtyb-tools
set IMAGE_NAME=%DOCKER_HUB_USERNAME%/%DOCKER_HUB_REPO%

REM Check if Docker is running
docker info >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Docker is not running. Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Set version based on current timestamp
for /f "tokens=2 delims==" %%a in ('wmic os get localdatetime /value') do set datetime=%%a
set VERSION=%datetime:~0,4%.%datetime:~4,2%.%datetime:~6,2%.%datetime:~8,4%
echo Using version: %VERSION%

REM Build the Docker image
echo Building Docker image...
docker build -t %IMAGE_NAME%:%VERSION% -t %IMAGE_NAME%:latest .
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to build Docker image.
    pause
    exit /b 1
)
echo Docker image built successfully

REM Login to Docker Hub
echo Logging in to Docker Hub...
docker login
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to log in to Docker Hub.
    pause
    exit /b 1
)

REM Push both version and latest tags
echo Pushing version tag: %VERSION%
docker push %IMAGE_NAME%:%VERSION%
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to push version tag.
    pause
    exit /b 1
)

echo Pushing latest tag...
docker push %IMAGE_NAME%:latest
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to push latest tag.
    pause
    exit /b 1
)

echo Successfully pushed to Docker Hub!
echo Image: %IMAGE_NAME%:%VERSION%
echo Latest: %IMAGE_NAME%:latest
echo.
echo Next Steps:
echo 1. Users can now pull the latest image: docker pull %IMAGE_NAME%:latest
echo 2. Deploy using: .\deploy-steamcodetool-fixed.bat
echo 3. Test deployment: .\test-deployment.bat
echo.
pause
