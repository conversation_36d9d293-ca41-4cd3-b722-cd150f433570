from services.shopee_api_client import shopee_client
from services.fake_order_generator_service import fake_order_service
from services.order_data_factory import order_data_factory
from utils.fake_order_security import fake_order_security, enhance_order_for_display
import config
import json
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Rate limiting for repetitive error logs
_error_log_cache = {}
_ERROR_LOG_COOLDOWN = 600  # 10 minutes (increased from 5 minutes)

def _should_log_error(error_type, error_message):
    """Rate limit repetitive error logs to prevent spam"""
    cache_key = f"{error_type}:{hash(error_message)}"
    now = datetime.now()
    
    if cache_key in _error_log_cache:
        last_logged = _error_log_cache[cache_key]
        if now - last_logged < timedelta(seconds=_ERROR_LOG_COOLDOWN):
            return False
    
    _error_log_cache[cache_key] = now
    return True

MANUAL_INVOICE_FILE = 'configs/data/manual_invoice.json'

def get_manual_invoice(order_sn=None):
    """Load manual invoice data from file"""
    try:
        with open(MANUAL_INVOICE_FILE, 'r', encoding='utf-8') as f:
            invoices = json.load(f)
            if order_sn:
                # Find invoice by reference_id
                for invoice in invoices.values():
                    if invoice.get('reference_id') == order_sn:
                        return invoice
                return None
            return invoices
    except FileNotFoundError:
        return {} if not order_sn else None

def process_manual_invoice(order_sn):
    """Convert manual invoice to order format"""
    invoice = get_manual_invoice(order_sn)
    if not invoice:
        return None
        
    # Create order data structure matching Shopee format
    order_data = {
        'data': {
            'card_list': [{
                'order_card': {
                    'card_header': {
                        'order_sn': invoice['reference_id']
                    },
                    'order_ext_info': {
                        'order_id': invoice['id']
                    },
                    'status_info': {
                        'status': 'To Ship' if invoice['status'] == 'paid' else 'Unpaid'
                    },
                    'buyer_info': {
                        'buyer_user': {
                            'user_name': invoice['customer']['name']
                        }
                    }
                }
            }]
        }
    }
    
    return order_data

def process_fake_order(order_sn):
    """Convert fake order to order format using enhanced data structures"""
    fake_order = get_fake_order(order_sn)
    if not fake_order:
        return None

    # Check if this is an enhanced fake order with full_order_data
    if 'full_order_data' in fake_order:
        # Use the complete order data structure and convert to card_list format
        full_data = fake_order['full_order_data']
        if 'data' in full_data:
            order_data = {
                'data': {
                    'card_list': [{
                        'order_card': {
                            'card_header': {
                                'order_sn': full_data['data']['order_sn']
                            },
                            'order_ext_info': {
                                'order_id': hash(full_data['data']['order_sn']) % 1000000
                            },
                            'status_info': {
                                'status': full_data['data'].get('status', 'To Ship')
                            },
                            'buyer_info': {
                                'buyer_user': {
                                    'user_name': full_data['data']['buyer_user']['user_name']
                                }
                            },
                            'fake_order_metadata': full_data['data'].get('fake_order_metadata', {})
                        }
                    }]
                }
            }
            return order_data

    # Fallback for legacy fake orders - create order data structure matching Shopee format
    order_data = {
        'data': {
            'card_list': [{
                'order_card': {
                    'card_header': {
                        'order_sn': fake_order['order_sn']
                    },
                    'order_ext_info': {
                        'order_id': hash(fake_order['order_sn']) % 1000000  # Generate a fake order ID
                    },
                    'status_info': {
                        'status': fake_order.get('status', 'To Ship')
                    },
                    'buyer_info': {
                        'buyer_user': {
                            'user_name': fake_order.get('buyer_username', 'test_user')
                        }
                    },
                    'is_fake_order': True,
                    'legacy_fake_order': True
                }
            }]
        }
    }

    return order_data

def get_fake_order(order_sn):
    """Get fake order from both legacy manual orders and new fake order service with enhanced data structures"""
    # First check the new fake order service
    fake_orders = fake_order_service.get_fake_orders()
    for order in fake_orders:
        if order.get('order_sn') == order_sn:
            # Add security markers and validation
            order = fake_order_security.add_fake_order_markers(order)
            
            # Enhance the order with additional metadata if it's from the new service
            if 'full_order_data' in order:
                # Add processing metadata for enhanced fake orders
                order['enhanced_fake_order'] = True
                order['supports_full_metadata'] = True
                order['data_source'] = 'fake_order_generator_service'
                
                # Log enhanced fake order access with security context
                fake_order_security.log_fake_order_operation(
                    'retrieve_enhanced_fake_order',
                    order,
                    {'access_method': 'fake_order_service', 'has_full_data': True}
                )
                logger.info(f"{fake_order_security.get_log_prefix(order_sn)} Retrieved enhanced fake order: {order_sn} with full data structure")
            else:
                # Mark as legacy fake order from new service
                order['legacy_fake_order'] = True
                order['data_source'] = 'fake_order_service_legacy'
                
                # Log legacy fake order access
                fake_order_security.log_fake_order_operation(
                    'retrieve_legacy_fake_order',
                    order,
                    {'access_method': 'fake_order_service', 'has_full_data': False}
                )
                logger.debug(f"{fake_order_security.get_log_prefix(order_sn)} Retrieved legacy fake order from new service: {order_sn}")
            
            return order
    
    # Fallback to legacy manual orders file for backward compatibility
    try:
        with open('configs/data/manual_orders.json', 'r', encoding='utf-8') as f:
            manual_orders = json.load(f)

        # Find fake order
        for order in manual_orders:
            if order.get('order_sn') == order_sn and order.get('is_fake_order', False):
                # Mark as legacy manual fake order
                order['legacy_manual_fake_order'] = True
                order['data_source'] = 'manual_orders_file'
                logger.debug(f"Retrieved legacy manual fake order: {order_sn}")
                return order

        return None
    except (FileNotFoundError, json.JSONDecodeError):
        logger.debug(f"No legacy manual orders file found or error reading it for order: {order_sn}")
        return None

def get_to_ship_orders():
    """Get orders with 'To Ship' status using centralized Shopee API."""
    response_data, status_code = shopee_client.get_to_ship_orders()
    if status_code == 200:
        return response_data.get('data', {})
    elif status_code == 503:  # Service unavailable
        if _should_log_error("to_ship_orders_service_unavailable", "Service unavailable"):
            logger.warning("Shopee API service is currently unavailable - returning empty order list")
        return {"data": {"card_list": []}, "error": "Service temporarily unavailable"}
    else:
        error_msg = f"Failed to get to_ship orders: status={status_code}, response={response_data}"
        if _should_log_error("to_ship_orders", error_msg):
            logger.error(error_msg)
        else:
            logger.debug(f"[Rate Limited] {error_msg}")
        return {"data": {"card_list": []}, "error": response_data.get('error', 'Unknown error')}

def get_shipped_orders():
    """Get orders with 'Shipped' status using centralized Shopee API."""
    response_data, status_code = shopee_client.get_shipped_orders()
    if status_code == 200:
        return response_data.get('data', {})
    else:
        logger.error(f"Failed to get shipped orders: {response_data}")
        return {"data": {"card_list": []}, "error": response_data.get('error', 'Unknown error')}

def get_completed_orders():
    """Get orders with 'Completed' status using centralized Shopee API."""
    response_data, status_code = shopee_client.get_completed_orders()
    if status_code == 200:
        return response_data.get('data', {})
    else:
        logger.error(f"Failed to get completed orders: {response_data}")
        return {"data": {"card_list": []}, "error": response_data.get('error', 'Unknown error')}

def search_order(order_sn):
    """Search for an order by order number with enhanced fake order compatibility."""
    # First check manual invoices
    manual_order = process_manual_invoice(order_sn)
    if manual_order:
        logger.debug(f"Found manual invoice for order search: {order_sn}")
        return manual_order

    # Check for fake orders with enhanced processing
    fake_order = process_fake_order(order_sn)
    if fake_order:
        # Add search metadata to fake order results
        if 'data' in fake_order and 'card_list' in fake_order['data']:
            for card in fake_order['data']['card_list']:
                if 'order_card' in card:
                    card['order_card']['search_metadata'] = {
                        'searched_by': 'order_service_search',
                        'search_timestamp': datetime.now().isoformat(),
                        'found_via': 'fake_order_processing',
                        'is_fake_order': True,
                        'enhanced_fake_order': 'fake_order_metadata' in card['order_card']
                    }
        
        logger.info(f"Found fake order for search: {order_sn}")
        return fake_order

    # If not found, proceed with Shopee API search
    response_data, status_code = shopee_client.search_order(order_sn)
    if status_code == 200:
        logger.debug(f"Found real order via Shopee API search: {order_sn}")
        return response_data.get('data', {})
    else:
        logger.error(f"Failed to search order {order_sn}: {response_data}")
        return {"data": {"card_list": []}, "error": response_data.get('error', 'Unknown error')}

def ship_order(order_sn):
    """Ship an order using centralized Shopee API."""
    response_data, status_code = shopee_client.ship_order(order_sn)

    if status_code == 200:
        return response_data, 200
    else:
        logger.error(f"Failed to ship order {order_sn}: {response_data}")
        return response_data, status_code

def get_order_status(order_sn):
    """Get the status of an order with enhanced fake order metadata support."""
    # First check if this is a manual invoice
    invoice = get_manual_invoice(order_sn)
    if invoice:
        return {
            "order_sn": invoice['reference_id'],
            "status": 'To Ship' if invoice['status'] == 'paid' else 'Unpaid'
        }, 200

    # Check if this is a fake order
    fake_order = get_fake_order(order_sn)
    if fake_order:
        # Enhanced response for fake orders with metadata
        response = {
            "order_sn": fake_order['order_sn'],
            "status": fake_order.get('status', 'To Ship'),
            "is_fake_order": True
        }
        
        # Add enhanced metadata if available
        if 'full_order_data' in fake_order:
            full_data = fake_order['full_order_data']
            if 'data' in full_data:
                response.update({
                    "payment_status": full_data['data'].get('payment_status', 'paid'),
                    "order_status": full_data['data'].get('order_status', fake_order.get('status', 'To Ship')),
                    "enhanced_fake_order": True,
                    "fake_order_metadata": full_data['data'].get('fake_order_metadata', {})
                })
        else:
            # Legacy fake order metadata
            response.update({
                "enhanced_fake_order": False,
                "legacy_fake_order": True,
                "data_source": fake_order.get('data_source', 'unknown')
            })
        
        logger.debug(f"Retrieved fake order status for {order_sn}: {response['status']}")
        return response, 200

    # If not a manual invoice or fake order, proceed with Shopee API status check
    response_data, status_code = shopee_client.get_order_status(order_sn)

    if status_code == 200:
        return response_data, 200
    else:
        logger.error(f"Failed to get order status for {order_sn}: {response_data}")
        return response_data, status_code

def get_order_details(order_sn):
    """
    Fetch detailed information for a specific order using order_sn.
    Now supports Shopee orders, manual invoices, and fake orders.
    """
    # First check if this is a manual invoice
    invoice = get_manual_invoice(order_sn)
    if invoice:
        # Convert invoice to order details format
        order_data = {
            'message': 'Order details fetched successfully',
            'data': {
                'order_sn': invoice['reference_id'],
                'buyer_user': {
                    'user_name': invoice['customer']['name']
                },
                'order_items': [{
                    'var_sku': invoice.get('var_sku', 'N/A'),
                    'product': {
                        'name': invoice.get('description', 'Manual Invoice Item')
                    },
                    'item_model': {
                        'sku': invoice.get('var_sku', 'N/A')
                    }
                }],
                'total_price': invoice['amount'],
                'buyer_address_name': invoice['customer']['name'],
                'buyer_address_phone': invoice['customer']['contact'],
                'create_time': invoice['created_at'],
                'status': invoice['status'],
                'is_manual_invoice': True
            }
        }
        return order_data, 200

    # Check if this is a fake order
    fake_order = get_fake_order(order_sn)
    if fake_order:
        # Check if this is an enhanced fake order with full_order_data
        if 'full_order_data' in fake_order:
            # Return the complete order data structure with validation
            full_data = fake_order['full_order_data']
            
            # Validate the order structure using order data factory
            validation_result = order_data_factory.validate_order_structure(full_data)
            if not validation_result['is_valid']:
                logger.warning(f"Enhanced fake order {order_sn} has validation issues: {validation_result['errors']}")
            
            # Add processing metadata to the response
            if 'data' in full_data:
                full_data['data']['order_processing_metadata'] = {
                    'processed_by': 'order_service_enhanced',
                    'processing_timestamp': datetime.now().isoformat(),
                    'validation_status': 'validated' if validation_result['is_valid'] else 'has_warnings',
                    'validation_warnings': validation_result.get('warnings', []),
                    'enhanced_fake_order': True,
                    'supports_full_pipeline': True
                }
            
            logger.info(f"Retrieved enhanced fake order details for {order_sn}")
            return full_data, 200
        
        # Fallback for legacy fake orders - convert to order details format with enhanced metadata
        order_data = {
            'message': 'Legacy fake order details fetched successfully',
            'data': {
                'order_sn': fake_order['order_sn'],
                'buyer_user': {
                    'user_name': fake_order.get('buyer_username', 'test_user')
                },
                'order_items': [{
                    'var_sku': fake_order.get('var_sku', 'unknown_sku'),
                    'product': {
                        'name': f'Test Product for {fake_order.get("var_sku", "unknown_sku")}'
                    },
                    'item_model': {
                        'sku': fake_order.get('var_sku', 'unknown_sku')
                    },
                    'quantity': fake_order.get('quantity', 1),
                    'price': 0.00,  # Legacy fake orders have no real price
                    'total_price': 0.00
                }],
                'total_price': 0.00,  # Legacy fake orders have no real price
                'buyer_address_name': fake_order.get('buyer_username', 'test_user'),
                'buyer_address_phone': fake_order.get('buyer_phone', '************'),
                'buyer_address_email': fake_order.get('buyer_email', '<EMAIL>'),
                'create_time': fake_order.get('created_at', datetime.now().isoformat()),
                'status': fake_order.get('status', 'To Ship'),
                'payment_status': fake_order.get('payment_status', 'paid'),
                'is_fake_order': True,
                'legacy_fake_order': True,
                'order_processing_metadata': {
                    'processed_by': 'order_service_legacy',
                    'processing_timestamp': datetime.now().isoformat(),
                    'enhanced_fake_order': False,
                    'data_source': fake_order.get('data_source', 'unknown'),
                    'supports_full_pipeline': False,
                    'requires_upgrade': True
                }
            }
        }
        
        logger.debug(f"Retrieved legacy fake order details for {order_sn}")
        return order_data, 200

    # If not a manual invoice or fake order, proceed with Shopee API order details
    response_data, status_code = shopee_client.get_order_details(order_sn)

    if status_code == 200:
        return response_data, 200
    else:
        logger.error(f"Failed to get order details for {order_sn}: {response_data}")
        return response_data, status_code

def get_order_id_by_sn(order_sn):
    """
    Get order ID by order serial number.
    This function is now simplified to use the centralized API.
    """
    # Use the search order function to get order details
    response_data, status_code = shopee_client.search_order(order_sn)

    if status_code != 200:
        return {"error": "Order not found"}, 404

    # Extract order_id from the response
    order_data = response_data.get('data', {})
    card_list = order_data.get('card_list', [])

    if not card_list:
        return {"error": "Order not found"}, 404

    order = card_list[0]

    # Extract order_id based on the order structure
    if 'package_level_order_card' in order:
        order_id = order['package_level_order_card']['order_ext_info']['order_id']
    elif 'order_card' in order:
        order_id = order['order_card']['order_ext_info']['order_id']
    else:
        return {"error": "Unexpected order structure"}, 500

    return {"order_id": order_id}, 200
    