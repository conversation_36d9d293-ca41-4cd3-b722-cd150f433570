"""
Model Utilities and Validation

Utility functions for data validation, serialization/deserialization,
and JSON persistence helpers for all OpenAI Plus Redeem models.
"""

import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Type, TypeVar
from dataclasses import is_dataclass
import logging

# Type variable for generic model operations
T = TypeVar('T')

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass


class DataPersistenceError(Exception):
    """Custom exception for data persistence errors"""
    pass


def validate_email(email: str) -> bool:
    """
    Validate email address format
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
    
    # Basic email regex pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email.strip()))


def validate_password(password: str, min_length: int = 8) -> bool:
    """
    Validate password strength
    
    Args:
        password: Password to validate
        min_length: Minimum password length
        
    Returns:
        True if valid, False otherwise
    """
    if not password or not isinstance(password, str):
        return False
    
    return len(password.strip()) >= min_length


def validate_sku(sku: str) -> bool:
    """
    Validate SKU format
    
    Args:
        sku: SKU to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not sku or not isinstance(sku, str):
        return False
    
    # Allow alphanumeric, underscores, and hyphens
    pattern = r'^[a-zA-Z0-9_-]+$'
    return bool(re.match(pattern, sku.strip()))


def validate_username(username: str) -> bool:
    """
    Validate username format
    
    Args:
        username: Username to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not username or not isinstance(username, str):
        return False
    
    # Allow alphanumeric, underscores, hyphens, and dots
    pattern = r'^[a-zA-Z0-9._-]+$'
    return bool(re.match(pattern, username.strip())) and len(username.strip()) >= 3


def validate_iso_datetime(datetime_str: str) -> bool:
    """
    Validate ISO datetime string format
    
    Args:
        datetime_str: Datetime string to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not datetime_str or not isinstance(datetime_str, str):
        return False
    
    try:
        datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        return True
    except (ValueError, AttributeError):
        return False


def validate_verification_code(code: str) -> bool:
    """
    Validate verification code format (6 digits)
    
    Args:
        code: Verification code to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not code or not isinstance(code, str):
        return False
    
    # 6-digit numeric code
    pattern = r'^\d{6}$'
    return bool(re.match(pattern, code.strip()))


def sanitize_string(value: str, max_length: int = 255) -> str:
    """
    Sanitize string input
    
    Args:
        value: String to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
    """
    if not isinstance(value, str):
        return ""
    
    # Strip whitespace and limit length
    sanitized = value.strip()[:max_length]
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\']', '', sanitized)
    
    return sanitized


def serialize_model(model: Any) -> Dict[str, Any]:
    """
    Serialize a dataclass model to dictionary
    
    Args:
        model: Dataclass model instance
        
    Returns:
        Dictionary representation
        
    Raises:
        ValidationError: If model is not a dataclass
    """
    if not is_dataclass(model):
        raise ValidationError("Model must be a dataclass")
    
    if hasattr(model, 'to_dict'):
        return model.to_dict()
    
    # Fallback to basic serialization
    return {field.name: getattr(model, field.name) for field in model.__dataclass_fields__.values()}


def deserialize_model(model_class: Type[T], data: Dict[str, Any]) -> T:
    """
    Deserialize dictionary to dataclass model
    
    Args:
        model_class: Dataclass model class
        data: Dictionary data
        
    Returns:
        Model instance
        
    Raises:
        ValidationError: If deserialization fails
    """
    try:
        if hasattr(model_class, 'from_dict'):
            return model_class.from_dict(data)
        
        # Fallback to basic deserialization
        return model_class(**data)
    except Exception as e:
        raise ValidationError(f"Failed to deserialize {model_class.__name__}: {e}")


def load_json_data(file_path: str) -> Dict[str, Any]:
    """
    Load JSON data from file
    
    Args:
        file_path: Path to JSON file
        
    Returns:
        Loaded data dictionary
        
    Raises:
        DataPersistenceError: If loading fails
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"JSON file not found: {file_path}")
            return {"metadata": {"version": "1.0.0", "total_items": 0}, "items": []}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.debug(f"Loaded JSON data from {file_path}")
        return data
        
    except Exception as e:
        logger.error(f"Failed to load JSON data from {file_path}: {e}")
        raise DataPersistenceError(f"Failed to load data: {e}")


def save_json_data(file_path: str, data: Dict[str, Any]) -> None:
    """
    Save JSON data to file
    
    Args:
        file_path: Path to JSON file
        data: Data to save
        
    Raises:
        DataPersistenceError: If saving fails
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Update metadata
        if 'metadata' in data:
            data['metadata']['last_updated'] = datetime.now().isoformat()
        
        # Write data with proper formatting
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.debug(f"Saved JSON data to {file_path}")
        
    except Exception as e:
        logger.error(f"Failed to save JSON data to {file_path}: {e}")
        raise DataPersistenceError(f"Failed to save data: {e}")


def backup_json_file(file_path: str, backup_dir: str = None) -> str:
    """
    Create backup of JSON file
    
    Args:
        file_path: Path to original file
        backup_dir: Directory for backup (optional)
        
    Returns:
        Path to backup file
        
    Raises:
        DataPersistenceError: If backup fails
    """
    try:
        if not os.path.exists(file_path):
            raise DataPersistenceError(f"Original file not found: {file_path}")
        
        # Determine backup directory
        if backup_dir is None:
            backup_dir = os.path.join(os.path.dirname(file_path), 'backups')
        
        os.makedirs(backup_dir, exist_ok=True)
        
        # Create backup filename with timestamp
        filename = os.path.basename(file_path)
        name, ext = os.path.splitext(filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"{name}_{timestamp}{ext}"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # Copy file
        import shutil
        shutil.copy2(file_path, backup_path)
        
        logger.info(f"Created backup: {backup_path}")
        return backup_path
        
    except Exception as e:
        logger.error(f"Failed to backup file {file_path}: {e}")
        raise DataPersistenceError(f"Failed to create backup: {e}")


def cleanup_old_backups(backup_dir: str, max_backups: int = 30) -> None:
    """
    Clean up old backup files
    
    Args:
        backup_dir: Directory containing backups
        max_backups: Maximum number of backups to keep
    """
    try:
        if not os.path.exists(backup_dir):
            return
        
        # Get all backup files sorted by modification time
        backup_files = []
        for filename in os.listdir(backup_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(backup_dir, filename)
                backup_files.append((file_path, os.path.getmtime(file_path)))
        
        # Sort by modification time (newest first)
        backup_files.sort(key=lambda x: x[1], reverse=True)
        
        # Remove old backups
        for file_path, _ in backup_files[max_backups:]:
            try:
                os.remove(file_path)
                logger.debug(f"Removed old backup: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to remove backup {file_path}: {e}")
                
    except Exception as e:
        logger.error(f"Failed to cleanup backups in {backup_dir}: {e}")


def validate_model_data(model_class: Type[T], data: Dict[str, Any]) -> List[str]:
    """
    Validate model data and return list of validation errors
    
    Args:
        model_class: Model class to validate against
        data: Data to validate
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    try:
        # Try to create model instance
        model = deserialize_model(model_class, data)
        
        # Perform model-specific validation
        if hasattr(model, 'validate'):
            model_errors = model.validate()
            if model_errors:
                errors.extend(model_errors)
                
    except Exception as e:
        errors.append(f"Failed to create {model_class.__name__}: {e}")
    
    return errors


def get_data_file_path(plugin_name: str, filename: str) -> str:
    """
    Get full path to data file
    
    Args:
        plugin_name: Name of the plugin
        filename: Name of the data file
        
    Returns:
        Full path to data file
    """
    # Assuming data files are stored in configs/data/<plugin_name>/
    base_dir = os.path.join('configs', 'data', plugin_name)
    return os.path.join(base_dir, filename)


def ensure_data_directory(plugin_name: str) -> str:
    """
    Ensure data directory exists for plugin
    
    Args:
        plugin_name: Name of the plugin
        
    Returns:
        Path to data directory
    """
    data_dir = os.path.join('configs', 'data', plugin_name)
    os.makedirs(data_dir, exist_ok=True)
    return data_dir


def format_duration(seconds: int) -> str:
    """
    Format duration in seconds to human-readable string
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes}m"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}h {minutes}m" if minutes > 0 else f"{hours}h"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}d {hours}h" if hours > 0 else f"{days}d"


def parse_duration_string(duration_str: str) -> int:
    """
    Parse duration string to seconds
    
    Args:
        duration_str: Duration string (e.g., "1h 30m", "2d", "45m")
        
    Returns:
        Duration in seconds
    """
    if not duration_str:
        return 0
    
    total_seconds = 0
    
    # Extract days
    days_match = re.search(r'(\d+)d', duration_str)
    if days_match:
        total_seconds += int(days_match.group(1)) * 86400
    
    # Extract hours
    hours_match = re.search(r'(\d+)h', duration_str)
    if hours_match:
        total_seconds += int(hours_match.group(1)) * 3600
    
    # Extract minutes
    minutes_match = re.search(r'(\d+)m', duration_str)
    if minutes_match:
        total_seconds += int(minutes_match.group(1)) * 60
    
    # Extract seconds
    seconds_match = re.search(r'(\d+)s', duration_str)
    if seconds_match:
        total_seconds += int(seconds_match.group(1))
    
    return total_seconds
