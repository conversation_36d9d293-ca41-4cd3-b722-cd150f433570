"""
ShopeeAPI Integration Helper

This module provides helper functions to integrate the Chat Commands plugin
with the existing ShopeeAPI webhook system.
"""

import logging
import requests
import json
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class ShopeeAPIIntegration:
    """Helper class for integrating with ShopeeAPI"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or "http://localhost:8000"
        self.chat_commands_plugin = None
    
    def set_plugin_reference(self, plugin):
        """Set reference to the chat commands plugin"""
        self.chat_commands_plugin = plugin
    
    def configure_webhook(self) -> bool:
        """Configure ShopeeAPI to send webhooks to our plugin"""
        try:
            # Update ShopeeAPI webhook configuration to include our endpoint
            webhook_config = {
                "ENABLED": True,
                "MESSAGE_RECEIVED": {
                    "ENABLED": True,
                    "URL": f"{self.base_url}/chat-commands/api/webhook",
                    "RETRY_COUNT": 3,
                    "RETRY_DELAY": 5
                }
            }
            
            # This would update the ShopeeAPI config
            # For now, we'll log the configuration that should be applied
            logger.info(f"ShopeeAPI webhook configuration: {json.dumps(webhook_config, indent=2)}")
            
            # TODO: Implement actual configuration update
            # This might involve:
            # 1. Updating the ShopeeAPI config.json file
            # 2. Restarting the ShopeeAPI service
            # 3. Or calling a configuration API endpoint
            
            return True
            
        except Exception as e:
            logger.error(f"Error configuring webhook: {e}")
            return False
    
    def test_webhook_connectivity(self) -> bool:
        """Test if webhook endpoint is reachable"""
        try:
            test_payload = {
                "event": "test",
                "message": {
                    "message_id": "test-123",
                    "content": {"text": "#help"},
                    "from_name": "test-user",
                    "from_id": "test-user-id",
                    "send_by_yourself": False
                },
                "conversation_id": "test-conversation"
            }
            
            response = requests.post(
                f"{self.base_url}/chat-commands/api/webhook",
                json=test_payload,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("Webhook connectivity test successful")
                return True
            else:
                logger.error(f"Webhook test failed with status {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Webhook connectivity test failed: {e}")
            return False
    
    def send_test_message(self, username: str, text: str) -> bool:
        """Send a test message using ShopeeAPI"""
        try:
            payload = {
                "username": username,
                "text": text
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat/send-message",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"Test message sent successfully to {username}")
                return True
            else:
                logger.error(f"Failed to send test message: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending test message: {e}")
            return False
    
    def get_shopee_api_status(self) -> Dict[str, Any]:
        """Get ShopeeAPI status"""
        try:
            # Use root endpoint instead of /health since ShopeeAPI doesn't have /health
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    # Check if this is the expected ShopeeAPI response
                    if response_data.get('message') == 'Shopee API Service':
                        return {
                            "status": "online",
                            "base_url": self.base_url,
                            "response": response_data
                        }
                    else:
                        return {
                            "status": "warning",
                            "base_url": self.base_url,
                            "response": response_data,
                            "message": "Unexpected response format"
                        }
                except ValueError:
                    return {
                        "status": "warning",
                        "base_url": self.base_url,
                        "response": {"raw_text": response.text[:200]},
                        "message": "Non-JSON response"
                    }
            else:
                return {
                    "status": "error",
                    "base_url": self.base_url,
                    "error": f"HTTP {response.status_code}"
                }
        except Exception as e:
            return {
                "status": "offline",
                "base_url": self.base_url,
                "error": str(e)
            }
    
    def process_shopee_webhook(self, webhook_data: Dict[str, Any]) -> bool:
        """Process webhook data from ShopeeAPI"""
        try:
            if not self.chat_commands_plugin:
                logger.error("Chat commands plugin not set")
                return False
            
            # Check if this is a message_received event
            event_type = webhook_data.get('event', '')
            if event_type != 'message_received':
                logger.debug(f"Ignoring non-message event: {event_type}")
                return True
            
            # Extract message data
            message = webhook_data.get('message', {})
            
            # Check if message is from customer (not from us)
            send_by_yourself = message.get('send_by_yourself', False)
            if send_by_yourself:
                logger.debug("Ignoring message sent by ourselves")
                return True
            
            # Get message content
            content = message.get('content', {})
            text = content.get('text', '')
            
            # Only process messages that start with #
            if not text.startswith('#'):
                logger.debug("Message doesn't start with #, ignoring")
                return True
            
            logger.info(f"Processing command message: {text}")
            
            # Use the plugin's process_webhook_message method
            return self.chat_commands_plugin.process_webhook_message(webhook_data)
            
        except Exception as e:
            logger.error(f"Error processing ShopeeAPI webhook: {e}")
            return False


def setup_integration(plugin, shopee_api_url: str = None) -> ShopeeAPIIntegration:
    """Setup integration between Chat Commands plugin and ShopeeAPI"""
    try:
        integration = ShopeeAPIIntegration(shopee_api_url)
        integration.set_plugin_reference(plugin)
        
        # Test connectivity
        status = integration.get_shopee_api_status()
        logger.info(f"ShopeeAPI status: {status}")
        
        if status['status'] == 'online':
            # Configure webhook
            if integration.configure_webhook():
                logger.info("ShopeeAPI integration setup successful")
            else:
                logger.warning("Webhook configuration failed")
            
            # Test webhook connectivity
            if integration.test_webhook_connectivity():
                logger.info("Webhook connectivity test passed")
            else:
                logger.warning("Webhook connectivity test failed")
        else:
            logger.warning(f"ShopeeAPI is not available: {status}")
        
        return integration
        
    except Exception as e:
        logger.error(f"Error setting up ShopeeAPI integration: {e}")
        return None


def create_webhook_handler(integration: ShopeeAPIIntegration):
    """Create a webhook handler function for ShopeeAPI"""
    def handle_webhook(webhook_data: Dict[str, Any]) -> bool:
        """Handle incoming webhook from ShopeeAPI"""
        return integration.process_shopee_webhook(webhook_data)
    
    return handle_webhook


# Example usage for manual testing
if __name__ == "__main__":
    # This can be used for testing the integration
    logging.basicConfig(level=logging.INFO)
    
    # Create integration instance
    integration = ShopeeAPIIntegration()
    
    # Test ShopeeAPI status
    status = integration.get_shopee_api_status()
    print(f"ShopeeAPI Status: {json.dumps(status, indent=2)}")
    
    # Test webhook connectivity
    if integration.test_webhook_connectivity():
        print("Webhook connectivity: OK")
    else:
        print("Webhook connectivity: FAILED")
    
    # Test message sending
    if integration.send_test_message("test-user", "Test message from Chat Commands plugin"):
        print("Test message: SENT")
    else:
        print("Test message: FAILED")
