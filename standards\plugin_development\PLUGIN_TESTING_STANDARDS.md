# Plugin Testing Standards

This document defines the testing standards, patterns, and requirements for all plugins in the SteamCodeTool system.

## 🧪 Testing Framework Standards

### 1. Required Testing Structure

```
plugins/your_plugin/
├── tests/
│   ├── __init__.py
│   ├── conftest.py                 # Pytest configuration and fixtures
│   ├── test_plugin.py              # Plugin class tests
│   ├── test_services/              # Service layer tests
│   │   ├── __init__.py
│   │   ├── test_email_service.py
│   │   └── test_data_service.py
│   ├── test_routes/                # Route/API tests
│   │   ├── __init__.py
│   │   └── test_api_routes.py
│   ├── test_integration/           # Integration tests
│   │   ├── __init__.py
│   │   └── test_plugin_integration.py
│   └── fixtures/                   # Test data and fixtures
│       ├── sample_config.json
│       └── test_data.json
```

### 2. Testing Dependencies

```python
# requirements-test.txt
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0
requests-mock>=1.10.0
factory-boy>=3.2.0
freezegun>=1.2.0
```

### 3. Pytest Configuration

```python
# conftest.py
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock
from typing import Dict, Any

@pytest.fixture
def temp_dir():
    """Create temporary directory for tests."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)

@pytest.fixture
def mock_plugin_manager():
    """Mock plugin manager for testing."""
    manager = Mock()
    manager.app = Mock()
    manager.get_plugin_config.return_value = {}
    return manager

@pytest.fixture
def sample_config() -> Dict[str, Any]:
    """Sample plugin configuration for testing."""
    return {
        "enabled": True,
        "debug": False,
        "service_config": {
            "timeout": 30,
            "retry_attempts": 3
        },
        "api_config": {
            "base_url": "https://api.test.com",
            "api_key": "test-key"
        }
    }

@pytest.fixture
def plugin_instance(mock_plugin_manager, sample_config):
    """Create plugin instance for testing."""
    from plugins.your_plugin.plugin import Plugin
    
    plugin = Plugin(mock_plugin_manager)
    plugin.load_config(sample_config)
    return plugin
```

## 🔬 Unit Testing Standards

### 1. Plugin Class Testing

```python
# test_plugin.py
import pytest
from unittest.mock import Mock, patch
from plugins.your_plugin.plugin import Plugin

class TestPlugin:
    """Test cases for the main plugin class."""
    
    def test_plugin_initialization(self, mock_plugin_manager):
        """Test plugin initialization."""
        plugin = Plugin(mock_plugin_manager)
        
        assert plugin.name == "your_plugin"
        assert plugin.version is not None
        assert plugin.description is not None
        assert isinstance(plugin.dependencies, list)
    
    def test_plugin_initialize_success(self, plugin_instance):
        """Test successful plugin initialization."""
        with patch.object(plugin_instance, '_initialize_services') as mock_init:
            mock_init.return_value = True
            
            result = plugin_instance.initialize()
            
            assert result is True
            mock_init.assert_called_once()
    
    def test_plugin_initialize_failure(self, plugin_instance):
        """Test plugin initialization failure."""
        with patch.object(plugin_instance, '_initialize_services') as mock_init:
            mock_init.side_effect = Exception("Initialization failed")
            
            result = plugin_instance.initialize()
            
            assert result is False
    
    def test_plugin_shutdown(self, plugin_instance):
        """Test plugin shutdown."""
        plugin_instance._initialized = True
        
        result = plugin_instance.shutdown()
        
        assert result is True
    
    def test_get_config_schema(self, plugin_instance):
        """Test configuration schema retrieval."""
        schema = plugin_instance.get_config_schema()
        
        assert isinstance(schema, dict)
        assert "type" in schema
        assert schema["type"] == "object"
        assert "properties" in schema
        assert "enabled" in schema["properties"]
    
    def test_get_blueprint(self, plugin_instance):
        """Test blueprint retrieval."""
        blueprint = plugin_instance.get_blueprint()
        
        # Blueprint might be None if not initialized
        if blueprint is not None:
            assert hasattr(blueprint, 'name')
```

### 2. Service Testing

```python
# test_services/test_email_service.py
import pytest
from unittest.mock import Mock, patch, MagicMock
from plugins.your_plugin.services.email_service import EmailService

class TestEmailService:
    """Test cases for email service."""
    
    @pytest.fixture
    def email_config(self):
        """Email service configuration."""
        return {
            "smtp_host": "smtp.test.com",
            "smtp_port": 587,
            "username": "<EMAIL>",
            "password": "testpass",
            "use_tls": True
        }
    
    @pytest.fixture
    def email_service(self, email_config):
        """Create email service instance."""
        return EmailService(email_config)
    
    def test_service_initialization(self, email_service):
        """Test service initialization."""
        with patch.object(email_service, '_test_connection') as mock_test:
            mock_test.return_value = True
            
            result = email_service.initialize()
            
            assert result is True
            assert email_service.is_initialized() is True
    
    def test_send_email_success(self, email_service):
        """Test successful email sending."""
        email_service._initialized = True
        
        with patch.object(email_service, '_send_smtp_email') as mock_send:
            mock_send.return_value = True
            
            result = email_service.send_email(
                recipient="<EMAIL>",
                subject="Test Subject",
                body="Test Body"
            )
            
            assert result is True
            mock_send.assert_called_once()
    
    def test_send_email_invalid_recipient(self, email_service):
        """Test email sending with invalid recipient."""
        email_service._initialized = True
        
        with pytest.raises(ValueError, match="Invalid email address"):
            email_service.send_email(
                recipient="invalid-email",
                subject="Test Subject", 
                body="Test Body"
            )
    
    def test_send_email_service_not_initialized(self, email_service):
        """Test email sending when service not initialized."""
        with pytest.raises(RuntimeError, match="Service not initialized"):
            email_service.send_email(
                recipient="<EMAIL>",
                subject="Test Subject",
                body="Test Body"
            )
    
    def test_health_check_healthy(self, email_service):
        """Test health check when service is healthy."""
        email_service._initialized = True
        
        with patch.object(email_service, '_test_connection') as mock_test:
            mock_test.return_value = True
            
            health = email_service.health_check()
            
            assert health["status"] == "healthy"
            assert "last_check" in health
    
    def test_health_check_unhealthy(self, email_service):
        """Test health check when service is unhealthy."""
        email_service._initialized = True
        
        with patch.object(email_service, '_test_connection') as mock_test:
            mock_test.side_effect = Exception("Connection failed")
            
            health = email_service.health_check()
            
            assert health["status"] == "unhealthy"
            assert "reason" in health
```

### 3. Route Testing

```python
# test_routes/test_api_routes.py
import pytest
import json
from unittest.mock import Mock, patch
from flask import Flask
from plugins.your_plugin.routes.plugin_routes import create_plugin_blueprint

class TestAPIRoutes:
    """Test cases for API routes."""
    
    @pytest.fixture
    def app(self):
        """Create Flask app for testing."""
        app = Flask(__name__)
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def mock_plugin(self):
        """Mock plugin instance."""
        plugin = Mock()
        plugin.name = "test_plugin"
        plugin.version = "1.0.0"
        plugin.services = {
            'email': Mock(),
            'data': Mock()
        }
        return plugin
    
    @pytest.fixture
    def client(self, app, mock_plugin):
        """Create test client."""
        blueprint = create_plugin_blueprint(mock_plugin)
        app.register_blueprint(blueprint, url_prefix='/api/test')
        return app.test_client()
    
    def test_health_endpoint(self, client, mock_plugin):
        """Test health check endpoint."""
        mock_plugin.get_health_status.return_value = {"status": "healthy"}
        
        response = client.get('/api/test/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "healthy"
    
    def test_info_endpoint(self, client, mock_plugin):
        """Test plugin info endpoint."""
        mock_plugin.get_info.return_value = {
            "name": "test_plugin",
            "version": "1.0.0"
        }
        
        response = client.get('/api/test/info')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["name"] == "test_plugin"
    
    def test_send_email_endpoint_success(self, client, mock_plugin):
        """Test successful email sending via API."""
        mock_plugin.services['email'].send_email.return_value = True
        
        payload = {
            "recipient": "<EMAIL>",
            "subject": "Test Subject",
            "body": "Test Body"
        }
        
        response = client.post(
            '/api/test/send',
            data=json.dumps(payload),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["success"] is True
    
    def test_send_email_endpoint_missing_data(self, client):
        """Test email endpoint with missing data."""
        response = client.post('/api/test/send')
        
        assert response.status_code == 400
    
    def test_send_email_endpoint_service_error(self, client, mock_plugin):
        """Test email endpoint when service fails."""
        mock_plugin.services['email'].send_email.side_effect = Exception("Service error")
        
        payload = {
            "recipient": "<EMAIL>",
            "subject": "Test Subject",
            "body": "Test Body"
        }
        
        response = client.post(
            '/api/test/send',
            data=json.dumps(payload),
            content_type='application/json'
        )
        
        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data
```

## 🔗 Integration Testing Standards

### 1. Plugin Integration Tests

```python
# test_integration/test_plugin_integration.py
import pytest
from unittest.mock import Mock
from flask import Flask
from core.plugin_manager import PluginManager

class TestPluginIntegration:
    """Integration tests for plugin with system."""
    
    @pytest.fixture
    def app(self):
        """Create Flask app for integration testing."""
        app = Flask(__name__)
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def plugin_manager(self, app):
        """Create plugin manager for testing."""
        return PluginManager(app, plugins_dir="test_plugins")
    
    def test_plugin_loading(self, plugin_manager):
        """Test plugin loading through plugin manager."""
        # This would test actual plugin loading
        # Implementation depends on your plugin manager
        pass
    
    def test_plugin_api_integration(self, app):
        """Test plugin API integration with Flask app."""
        # Test that plugin routes are properly registered
        # and accessible through the app
        pass
    
    def test_plugin_configuration_integration(self, plugin_manager):
        """Test plugin configuration loading and validation."""
        # Test configuration loading from files
        # and proper validation
        pass
```

### 2. External Service Integration Tests

```python
# test_integration/test_external_services.py
import pytest
import requests_mock
from plugins.your_plugin.services.api_service import APIService

class TestExternalServiceIntegration:
    """Integration tests with external services."""
    
    @pytest.fixture
    def api_service(self):
        """Create API service for testing."""
        config = {
            "base_url": "https://api.test.com",
            "api_key": "test-key",
            "timeout": 30
        }
        return APIService(config)
    
    def test_api_call_success(self, api_service):
        """Test successful API call."""
        with requests_mock.Mocker() as m:
            m.get(
                "https://api.test.com/data",
                json={"result": "success"},
                status_code=200
            )
            
            result = api_service.get_data()
            
            assert result["result"] == "success"
    
    def test_api_call_failure(self, api_service):
        """Test API call failure handling."""
        with requests_mock.Mocker() as m:
            m.get(
                "https://api.test.com/data",
                status_code=500
            )
            
            with pytest.raises(APIError):
                api_service.get_data()
```

## 📊 Test Coverage Standards

### 1. Coverage Requirements

```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --cov=plugins/your_plugin
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --strict-markers
    --disable-warnings
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
```

### 2. Coverage Analysis

```bash
# Run tests with coverage
pytest --cov=plugins/your_plugin --cov-report=html

# Generate coverage report
coverage html

# Check coverage percentage
coverage report --fail-under=80
```

## 🚀 Test Execution Standards

### 1. Test Commands

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit
pytest -m integration
pytest -m "not slow"

# Run tests for specific plugin
pytest plugins/your_plugin/tests/

# Run with verbose output
pytest -v

# Run with coverage
pytest --cov=plugins/your_plugin
```

### 2. Continuous Integration

```yaml
# .github/workflows/test.yml
name: Plugin Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run tests
      run: |
        pytest --cov=plugins --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

## 📋 Testing Best Practices

### 1. Test Organization
- Group related tests in classes
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Use fixtures for common setup
- Mock external dependencies

### 2. Test Data Management
- Use factories for test data generation
- Store test fixtures in separate files
- Use parametrized tests for multiple scenarios
- Clean up test data after tests

### 3. Performance Testing
- Include performance benchmarks for critical paths
- Test with realistic data volumes
- Monitor test execution time
- Use profiling for optimization

This testing standard ensures reliable, maintainable, and comprehensive test coverage for all plugins.
