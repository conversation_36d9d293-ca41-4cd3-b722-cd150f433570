# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/

# Virtual environments
venv/
env/
ENV/
.env

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Jupyter Notebook
.ipynb_checkpoints

# Local configuration
*.log

# OS specific files
.DS_Store
Thumbs.db

# Testing
.coverage
htmlcov/
.pytest_cache/

# Temporary files
*.tmp
*.bak
*.backup

# Cache files
cache/
*.cache
