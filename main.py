"""
SteamCodeTool Main Application
Unified plugin-based architecture for SteamCodeTool system
Consolidates all functionality from previous main files
"""

import os
import sys
import logging
import secrets
import hmac
import time
import datetime
import json
from urllib.parse import urlparse, parse_qs
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

from flask import Flask, render_template, jsonify, request, session, redirect
from flask_cors import CORS

from core.plugin_manager import PluginManager
from config import load_config
from scheduler.scheduler import start_scheduler, shutdown_scheduler
from utils.credential_manager import credential_manager
from services.canva_service import CanvaService
from security_headers import init_security, secure_redirect_wrapper, sanitize_user_input
from anti_phishing_measures import init_anti_phishing_protection

# Import all API blueprints
from api.order_routes import order_bp
from api.conversation_routes import conversation_bp
from api.chat_routes import chat_bp
from api.email_routes import email_bp
from api.admin_routes import admin_bp
from api.canva_routes import canva_bp
from api.fake_order_routes import fake_order_bp
# Note: vpn_bp is handled by the plugin system
from api.curlec_routes import curlec_blueprint

# Import security enhancements
from utils.rate_limiter import security_rate_limiter, check_security_headers, get_client_ip
from utils.audit_logger import audit_logger

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Suppress APScheduler executor info messages
logging.getLogger('apscheduler.executors.default').setLevel(logging.WARNING)
# Also suppress other potential APScheduler noise
logging.getLogger('apscheduler.scheduler').setLevel(logging.WARNING)

# Suppress verbose service messages
logging.getLogger('services.session_service').setLevel(logging.WARNING)
logging.getLogger('plugins.ai_chat.plugin').setLevel(logging.WARNING)

# Initialize configuration early
try:
    from utils.config_initializer import initialize_all_configs
    logger.info("Initializing configuration files...")
    initialize_all_configs()
except Exception as e:
    logger.warning(f"Could not initialize configuration files: {e}")
    logger.warning("Application will continue with default configurations")

class SteamCodeToolApp:
    """Main application class for plugin-based SteamCodeTool"""

    def __init__(self):
        self.app = Flask(__name__)
        CORS(self.app)

        # Set Flask secret key
        self.app.secret_key = os.urandom(24)

        # Initialize security configurations FIRST
        init_security(self.app)

        # Initialize anti-phishing protection
        init_anti_phishing_protection(self.app)

        # Setup security middleware
        self._setup_security_middleware()

        # Initialize services
        self.canva_service = CanvaService()

        # Initialize plugin manager
        self.plugin_manager = PluginManager(self.app, plugins_dir="plugins")
        
        # Attach plugin manager to Flask app for access via current_app
        self.app.plugin_manager = self.plugin_manager

        # Initialize plugins early so their routes are available
        self.initialize_plugins()

        # Setup template context processors
        self._setup_template_context()

        # Register blueprints first
        self._register_blueprints()

        # Setup routes after blueprints to ensure they take precedence
        self._setup_core_routes()
        self._setup_legacy_routes()

        # Load configuration
        self.config = load_config()
    
    def _setup_security_middleware(self):
        """Setup security middleware for request/response processing"""
        
        @self.app.before_request
        def security_before_request():
            """Security checks before processing each request"""
            client_ip = get_client_ip()
            
            # Check if IP is blocked
            if security_rate_limiter.is_ip_blocked(client_ip):
                audit_logger.log_security_event('blocked_ip_access', {
                    'ip': client_ip,
                    'endpoint': request.endpoint,
                    'user_agent': request.headers.get('User-Agent')
                }, 'WARNING')
                return jsonify({'error': 'Access denied'}), 403
            
            # Log access attempt for sensitive endpoints
            if request.endpoint and any(path in request.path for path in ['/admin', '/api']):
                audit_logger.log_access(200)  # Will be updated with actual status in after_request
        
        @self.app.after_request
        def security_after_request(response):
            """Security processing after each request"""
            try:
                # Add security headers
                response = check_security_headers(response)
                
                # Log final response for audit
                if request.endpoint and any(path in request.path for path in ['/admin', '/api']):
                    audit_logger.log_access(response.status_code, 
                                          len(response.get_data()) if hasattr(response, 'get_data') else 0)
                
                return response
            except Exception as e:
                logger.error(f"Error in security after_request: {e}")
                return response
        
        @self.app.errorhandler(429)
        def handle_rate_limit(error):
            """Handle rate limit exceeded errors"""
            client_ip = get_client_ip()
            audit_logger.log_rate_limit_violation('general', 100)  # Default limit
            
            return jsonify({
                'error': 'Rate limit exceeded',
                'message': 'Too many requests. Please slow down.',
                'retry_after': 60
            }), 429
        
        @self.app.errorhandler(403)
        def handle_forbidden(error):
            """Handle forbidden access"""
            audit_logger.log_security_event('forbidden_access', {
                'endpoint': request.endpoint,
                'method': request.method
            }, 'WARNING')
            
            return jsonify({
                'error': 'Access forbidden',
                'message': 'You do not have permission to access this resource.'
            }), 403

    def _setup_template_context(self):
        """Setup template context processors"""

        @self.app.context_processor
        def inject_plugin_info():
            """Make plugin information available in templates"""
            return {
                'loaded_plugins': list(self.plugin_manager.get_all_plugins().keys()),
                'is_plugin_loaded': lambda plugin_name: plugin_name in self.plugin_manager.get_all_plugins()
            }

    def _setup_core_routes(self):
        """Setup core application routes"""
        
        @self.app.route('/')
        def index():
            """Main dashboard"""
            return render_template('index.html')
            
        @self.app.route('/health')
        def health_check():
            """Health check endpoint"""
            return jsonify({
                "status": "healthy",
                "plugins": list(self.plugin_manager.get_all_plugins().keys())
            })

        @self.app.route('/robots.txt')
        def robots_txt():
            """Serve robots.txt file"""
            try:
                from flask import send_from_directory, Response
                import os

                # Check if static/robots.txt exists
                robots_path = os.path.join('static', 'robots.txt')
                if os.path.exists(robots_path):
                    return send_from_directory('static', 'robots.txt', mimetype='text/plain')
                else:
                    # Return a basic robots.txt if file doesn't exist
                    robots_content = """User-agent: *
Disallow: /api/
Disallow: /admin/
Disallow: /canva_redeem_invitation
Disallow: /fake_order
Allow: /
Allow: /health
Crawl-delay: 10"""
                    return Response(robots_content, mimetype='text/plain')
            except Exception as e:
                logger.error(f"Error serving robots.txt: {e}")
                return Response("User-agent: *\nDisallow: /api/\nAllow: /", mimetype='text/plain')
            
        @self.app.route('/api/plugins', methods=['GET'])
        def get_plugins():
            """Get all discovered plugins with their status"""
            try:
                plugins_info = {}

                # Get all discovered plugins
                discovered_plugins = self.plugin_manager.discover_plugins()

                for plugin_name in discovered_plugins:
                    try:
                        # Check if plugin is enabled in config
                        plugin_config = self.plugin_manager.plugin_configs.get(plugin_name, {})
                        is_enabled = plugin_config.get('enabled', True)

                        # Get plugin info if it's loaded
                        if plugin_name in self.plugin_manager.plugins:
                            plugin_info = self.plugin_manager.plugins[plugin_name].get_info()
                            plugin_info['enabled'] = is_enabled
                        else:
                            # Plugin is not loaded (disabled), create basic info
                            plugin_info = {
                                'name': plugin_name.replace('_', ' ').title(),
                                'version': '1.0.0',
                                'description': f'{plugin_name.replace("_", " ").title()} plugin',
                                'enabled': is_enabled,
                                'status': 'disabled'
                            }

                        plugins_info[plugin_name] = plugin_info
                    except Exception as e:
                        logger.error(f"Error processing plugin {plugin_name}: {e}")

                return jsonify(plugins_info)
            except Exception as e:
                logger.error(f"Error getting plugins: {e}")
                return jsonify({"error": "Internal server error"}), 500
                
        @self.app.route('/api/plugins/status', methods=['GET'])
        def get_plugins_status():
            """Get status of all plugins"""
            try:
                status = self.plugin_manager.get_plugin_status()
                return jsonify(status)
            except Exception as e:
                logger.error(f"Error getting plugin status: {e}")
                return jsonify({"error": "Internal server error"}), 500
                
        @self.app.route('/api/plugins/<plugin_name>/enable', methods=['POST'])
        def enable_plugin(plugin_name: str):
            """Enable a plugin"""
            try:
                success = self.plugin_manager.enable_plugin(plugin_name)
                if success:
                    return jsonify({"message": f"Plugin {plugin_name} enabled successfully"})
                else:
                    return jsonify({"error": f"Failed to enable plugin {plugin_name}"}), 500
            except Exception as e:
                logger.error(f"Error enabling plugin {plugin_name}: {e}")
                return jsonify({"error": "Internal server error"}), 500
                
        @self.app.route('/api/plugins/<plugin_name>/disable', methods=['POST'])
        def disable_plugin(plugin_name: str):
            """Disable a plugin"""
            try:
                success = self.plugin_manager.disable_plugin(plugin_name)
                if success:
                    return jsonify({"message": f"Plugin {plugin_name} disabled successfully"})
                else:
                    return jsonify({"error": f"Failed to disable plugin {plugin_name}"}), 500
            except Exception as e:
                logger.error(f"Error disabling plugin {plugin_name}: {e}")
                return jsonify({"error": "Internal server error"}), 500
                
        @self.app.route('/api/plugins/<plugin_name>/config', methods=['GET'])
        def get_plugin_config(plugin_name: str):
            """Get plugin configuration"""
            try:
                plugin = self.plugin_manager.get_plugin(plugin_name)
                if not plugin:
                    return jsonify({"error": "Plugin not found"}), 404
                    
                return jsonify({
                    "config": plugin.config,
                    "schema": plugin.get_config_schema()
                })
            except Exception as e:
                logger.error(f"Error getting config for plugin {plugin_name}: {e}")
                return jsonify({"error": "Internal server error"}), 500
                
        @self.app.route('/api/plugins/<plugin_name>/config', methods=['PUT'])
        def update_plugin_config(plugin_name: str):
            """Update plugin configuration"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"error": "No configuration data provided"}), 400
                    
                success = self.plugin_manager.update_plugin_config(plugin_name, data)
                if success:
                    return jsonify({"message": f"Configuration updated for plugin {plugin_name}"})
                else:
                    return jsonify({"error": f"Failed to update configuration for plugin {plugin_name}"}), 500
            except Exception as e:
                logger.error(f"Error updating config for plugin {plugin_name}: {e}")
                return jsonify({"error": "Internal server error"}), 500
                
        @self.app.route('/api/plugins/discover', methods=['POST'])
        def discover_plugins():
            """Discover available plugins"""
            try:
                plugins = self.plugin_manager.discover_plugins()
                return jsonify({"plugins": plugins})
            except Exception as e:
                logger.error(f"Error discovering plugins: {e}")
                return jsonify({"error": "Internal server error"}), 500
                
        @self.app.route('/admin/plugins')
        def admin_plugins():
            """Plugin management admin page"""
            return render_template('admin_plugins.html')

        @self.app.route('/test-alpine')
        def test_alpine():
            """Test page for Alpine.js CSP fix"""
            return render_template('test_alpine.html')

        # Error handlers
        @self.app.errorhandler(401)
        def unauthorized(error):
            return jsonify({"error": "Unauthorized access"}), 401

        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({"error": "Not found"}), 404

        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({"error": "Internal server error"}), 500

    def _setup_legacy_routes(self):
        """Setup legacy routes from original main.py for backward compatibility"""

        @self.app.route('/canva_order')
        def enter_canva_order():
            return render_template('canva_order.html')

        @self.app.route('/canva_redeem_invitation')
        def canva_redeem_invitation():
            """处理Canva邀请链接的兑换"""
            token = request.args.get('token')
            order_sn = request.args.get('order')

            if not token or not order_sn:
                return render_template('canva_redeem_error.html',
                                     error="Invalid invitation link"), 400

            # 首先检查订单是否已经被兑换
            self.canva_service.load_orders()  # 确保使用最新数据
            if order_sn in self.canva_service.orders:
                if self.canva_service.orders[order_sn].get('redeemed', False):
                    return render_template('canva_redeem_error.html',
                                         error="This invitation has already been redeemed"), 400

            canva_url, error = self.canva_service.redeem_invitation(token, order_sn)
            if error:
                return render_template('canva_redeem_error.html',
                                     error=error), 400

            # 生成临时令牌
            temp_token = secrets.token_urlsafe(32)
            session[temp_token] = canva_url

            # 传递临时令牌而不是实际URL
            return render_template('canva_redeem_success.html',
                                 temp_token=temp_token)

        @self.app.route('/api/canva/get_redirect', methods=['POST'])
        @secure_redirect_wrapper
        def canva_get_redirect():
            """获取实际的重定向URL"""
            try:
                data = request.json
                temp_token = data.get('token')

                if not temp_token or temp_token not in session:
                    return jsonify({'error': 'Invalid token'}), 400

                # 清理用户输入
                temp_token = sanitize_user_input(temp_token)

                # 获取并立即删除会话中的URL
                redirect_url = session.pop(temp_token, None)
                if not redirect_url:
                    return jsonify({'error': 'Token expired'}), 400

                # 从URL中提取order_sn参数
                parsed_url = urlparse(redirect_url)
                query_params = parse_qs(parsed_url.query)
                order_sn = query_params.get('order', [None])[0]

                if order_sn:
                    # 清理order_sn输入
                    order_sn = sanitize_user_input(order_sn)

                    # 重新加载订单数据以确保数据最新
                    self.canva_service.load_orders()

                    if order_sn in self.canva_service.orders:
                        # 使用 canva_service 的方法更新订单状态
                        self.canva_service.orders[order_sn].update({
                            'redeemed': True,
                            'redeemed_at': datetime.datetime.now().isoformat(),
                            'redemption_token': None
                        })
                        # 确保保存更改
                        self.canva_service.save_orders()

                        # 验证更改是否成功
                        self.canva_service.load_orders()
                        if not self.canva_service.orders[order_sn].get('redeemed', False):
                            print(f"Warning: Failed to update order {order_sn} status to redeemed")

                return jsonify({
                    'url': redirect_url
                })
            except Exception as e:
                print(f"Error in get_redirect: {str(e)}")  # 添加错误日志
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/order')
        def enter_order():
            # Get order_sn from URL parameters and pass to template
            order_sn = request.args.get('order_sn', '')
            return render_template('order.html', order_sn=order_sn)

        @self.app.route('/netflix_signin')
        def netflix_signin():
            return render_template('netflix_signin.html')

        @self.app.route('/fake_order')
        def fake_order():
            return render_template('fake_order.html')

    def _register_blueprints(self):
        """Register all API blueprints"""
        try:
            logger.info("Starting blueprint registration...")

            # Register API blueprints with /api prefix
            self.app.register_blueprint(order_bp, url_prefix='/api')
            logger.info("Registered order_bp")

            self.app.register_blueprint(conversation_bp, url_prefix='/api')
            logger.info("Registered conversation_bp")

            self.app.register_blueprint(chat_bp, url_prefix='/api')
            logger.info("Registered chat_bp")

            self.app.register_blueprint(email_bp, url_prefix='/api')
            logger.info("Registered email_bp")

            self.app.register_blueprint(canva_bp, url_prefix='/api')
            logger.info("Registered canva_bp")

            self.app.register_blueprint(fake_order_bp)
            logger.info("Registered fake_order_bp")

            # Register admin blueprint without prefix (it has its own /admin routes)
            self.app.register_blueprint(admin_bp)
            logger.info("Registered admin_bp")

            # Note: VPN blueprint is already registered by the plugin system
            # No need to register vpn_bp again

            # Register other blueprints
            self.app.register_blueprint(curlec_blueprint)
            logger.info("Registered curlec_blueprint")

            logger.info("Successfully registered all API blueprints")
        except Exception as e:
            logger.error(f"Error registering blueprints: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def init_credential_manager(self):
        """Initialize credential manager with config values"""
        try:
            config = load_config()
            credential_manager.update_credentials(
                authorization_code=config.get('AUTHORIZATION_CODE', ''),
                cookie=config.get('COOKIE', '')
            )
            logger.info("Credential manager initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing credential manager: {e}")

    def initialize_plugins(self):
        """Initialize all plugins"""
        try:
            logger.info("Loading plugin configurations...")
            self.plugin_manager.load_plugin_configs()

            logger.info("Loading all plugins...")
            self.plugin_manager.load_all_plugins()

            loaded_plugins = list(self.plugin_manager.get_all_plugins().keys())
            logger.info(f"Successfully loaded plugins: {loaded_plugins}")

        except Exception as e:
            logger.error(f"Error initializing plugins: {e}")
            
    def run(self, host='0.0.0.0', port=5000, debug=False):
        """Run the application"""
        try:
            # Initialize credential manager
            self.init_credential_manager()

            # Plugins are already initialized in __init__
            # No need to initialize again

            # Start scheduler
            logger.info("Starting scheduler...")
            start_scheduler()

            logger.info(f"Starting SteamCodeTool Plugin System on {host}:{port}")
            self.app.run(host=host, port=port, debug=debug)

        except KeyboardInterrupt:
            logger.info("Shutting down application...")
            self.shutdown()
        except Exception as e:
            logger.error(f"Error running application: {e}")
            self.shutdown()
            
    def shutdown(self):
        """Shutdown the application and all plugins"""
        try:
            logger.info("Shutting down all plugins...")
            self.plugin_manager.shutdown_all_plugins()

            # Shutdown scheduler
            try:
                shutdown_scheduler()
            except Exception as e:
                logger.error(f"Error shutting down scheduler: {e}")

            logger.info("Application shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

def create_app():
    """Factory function to create Flask app"""
    app_instance = SteamCodeToolApp()
    app_instance.init_credential_manager()
    # Plugins are already initialized in __init__
    # Ensure plugin manager is attached to the Flask app
    app_instance.app.plugin_manager = app_instance.plugin_manager
    return app_instance.app

if __name__ == '__main__':
    # Create and run the application
    app_instance = SteamCodeToolApp()

    # Get configuration
    config = load_config()
    port = config.get('PORT', 5000)
    debug = config.get('DEBUG', False)

    # Run the application
    app_instance.run(port=port, debug=debug)
