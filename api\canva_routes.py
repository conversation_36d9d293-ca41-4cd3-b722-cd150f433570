from flask import Blueprint, jsonify, request, redirect, render_template
from services.canva_service import CanvaService
import json

canva_bp = Blueprint('canva', __name__)
canva_service = CanvaService()

@canva_bp.route('/canva_orders', methods=['GET'])
def get_orders():
    """获取所有Canva订单"""
    try:
        return jsonify(canva_service.orders)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@canva_bp.route('/canva/config', methods=['GET', 'POST'])
def handle_config():
    """处理Canva配置的获取和更新"""
    try:
        if request.method == 'GET':
            return jsonify(canva_service.config)
        
        # POST请求更新配置
        new_config = request.json
        canva_service.config = new_config
        with open('configs/services/canva_config.json', 'w') as f:
            json.dump(new_config, f, indent=4)
        return jsonify({'message': 'Configuration updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@canva_bp.route('/create_invitation', methods=['POST'])
def create_invitation():
    """创建邀请链接"""
    data = request.json
    order_sn = data.get('order_sn')
    
    url, error = canva_service.create_invitation_url(order_sn)
    if error:
        return jsonify({'error': error}), 400
    
    return jsonify({'invitation_url': url})

@canva_bp.route('/redeem_invitation')
def redeem_invitation():
    """兑换邀请链接"""
    token = request.args.get('token')
    order_sn = request.args.get('order')
    
    # 首先检查订单状态
    canva_service.load_orders()  # 确保使用最新数据
    if order_sn in canva_service.orders:
        order = canva_service.orders[order_sn]
        if order.get('redeemed', False):
            return render_template('canva_redeem_error.html', 
                                 error="This order has already been redeemed")
    
    canva_url, error = canva_service.redeem_invitation(token, order_sn)
    if error:
        return render_template('canva_redeem_error.html', error=error)
    
    return redirect(canva_url)

@canva_bp.route('/canva/reset_order', methods=['POST'])
def reset_order():
    """重置订单状态"""
    data = request.json
    order_sn = data.get('order_sn')
    
    success, error = canva_service.reset_order(order_sn)
    if error:
        return jsonify({'error': error}), 400
    
    return jsonify({'message': 'Order reset successfully'})

@canva_bp.route('/canva/extend_validity', methods=['POST'])
def extend_validity():
    """延长订单有效期"""
    data = request.json
    order_sn = data.get('order_sn')
    days = data.get('days')
    
    if not days or not isinstance(days, int):
        return jsonify({'error': 'Invalid days parameter'}), 400
    
    success, error = canva_service.extend_validity(order_sn, days)
    if error:
        return jsonify({'error': error}), 400
    
    return jsonify({'message': 'Validity extended successfully'})