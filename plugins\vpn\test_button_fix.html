<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h3>VPN Button Test</h3>
                    </div>
                    <div class="card-body">
                        <!-- Test form fields -->
                        <div class="form-group mb-3">
                            <label for="host">Host/IP Address</label>
                            <input type="text" class="form-control" id="host" value="127.0.0.1">
                        </div>
                        <div class="form-group mb-3">
                            <label for="port">SSH Port</label>
                            <input type="number" class="form-control" id="port" value="22">
                        </div>
                        <div class="form-group mb-3">
                            <label for="username">SSH Username</label>
                            <input type="text" class="form-control" id="username" value="root">
                        </div>
                        <div class="form-group mb-3">
                            <label for="password">SSH Password</label>
                            <input type="password" class="form-control" id="password" value="test123">
                        </div>
                        <div class="form-group mb-3">
                            <label for="private_key">SSH Private Key</label>
                            <textarea class="form-control" id="private_key" rows="3"></textarea>
                        </div>
                        <div class="form-group mb-3">
                            <label for="private_key_passphrase">Private Key Passphrase</label>
                            <input type="password" class="form-control" id="private_key_passphrase">
                        </div>
                        <div class="form-group mb-3">
                            <label for="xray_config_path">Xray Config Path</label>
                            <input type="text" class="form-control" id="xray_config_path" value="/etc/xray/config.json">
                        </div>
                        <div class="form-group mb-3">
                            <label for="xray_service_name">Xray Service Name</label>
                            <input type="text" class="form-control" id="xray_service_name" value="xray">
                        </div>

                        <!-- Test buttons -->
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-primary btn-block test-button" id="testSSHBtn" 
                                        onclick="if(typeof window.testSSHConnection === 'function') { window.testSSHConnection(); } else { console.error('testSSHConnection not found'); alert('Function not available. Please refresh the page.'); }">
                                    <i class="fas fa-plug"></i> Test SSH Connection
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-success btn-block test-button" id="testXrayBtn"
                                        onclick="if(typeof window.testXrayService === 'function') { window.testXrayService(); } else { console.error('testXrayService not found'); alert('Function not available. Please refresh the page.'); }">
                                    <i class="fas fa-cogs"></i> Test Xray Service
                                </button>
                            </div>
                        </div>

                        <!-- Test results -->
                        <div id="testResults" class="mt-3" style="display: none;">
                            <div class="alert" id="testAlert" role="alert"></div>
                            <div id="testDetails" class="small text-muted"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('=== VPN BUTTON TEST SCRIPT LOADED ===');
        
        // Test SSH Connection function
        window.testSSHConnection = function() {
            console.log('SSH INLINE CLICK! Testing SSH connection...');
            const btn = document.getElementById('testSSHBtn');
            const originalText = btn.innerHTML;
            
            // Get form values
            const host = document.getElementById('host').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const privateKey = document.getElementById('private_key').value;
            
            // Validate required fields
            if (!host || !username) {
                showTestResult('error', 'Please fill in Host/IP Address and SSH Username before testing.');
                return;
            }
            
            if (!password && !privateKey) {
                showTestResult('error', 'Please provide either SSH Password or Private Key for authentication.');
                return;
            }
            
            // Show loading state
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing SSH Connection...';
            btn.disabled = true;
            
            // Prepare test data
            const testData = {
                host: host,
                port: parseInt(document.getElementById('port').value) || 22,
                username: username,
                password: password,
                private_key: privateKey,
                private_key_passphrase: document.getElementById('private_key_passphrase').value
            };
            
            console.log('Test data prepared:', testData);
            
            // Make fetch request
            const testUrl = window.location.origin + '/admin/vpn/api/test-ssh-credentials';
            console.log('Making request to:', testUrl);
            
            fetch(testUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Success response:', data);
                if (data.success) {
                    showTestResult('success', 'SSH Connection Successful!', data.details);
                } else {
                    showTestResult('error', 'SSH Connection Failed: ' + (data.message || 'Unknown error'), data.details);
                }
            })
            .catch(error => {
                console.log('Error:', error);
                showTestResult('error', 'Connection test failed: ' + error.message);
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }
        
        // Test Xray Service function
        window.testXrayService = function() {
            console.log('XRAY INLINE CLICK! Testing Xray service...');
            const btn = document.getElementById('testXrayBtn');
            const originalText = btn.innerHTML;
            
            // Get form values
            const host = document.getElementById('host').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const privateKey = document.getElementById('private_key').value;
            
            // Validate required fields
            if (!host || !username) {
                showTestResult('error', 'Please fill in Host/IP Address and SSH Username before testing.');
                return;
            }
            
            if (!password && !privateKey) {
                showTestResult('error', 'Please provide either SSH Password or Private Key for authentication.');
                return;
            }
            
            // Show loading state
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing Xray Service...';
            btn.disabled = true;
            
            // Prepare test data
            const testData = {
                host: host,
                port: parseInt(document.getElementById('port').value) || 22,
                username: username,
                password: password,
                private_key: privateKey,
                private_key_passphrase: document.getElementById('private_key_passphrase').value,
                xray_config_path: document.getElementById('xray_config_path').value || '/etc/xray/config.json',
                xray_service_name: document.getElementById('xray_service_name').value || 'xray'
            };
            
            console.log('Xray test data prepared:', testData);
            
            // Make fetch request
            const testUrl = window.location.origin + '/admin/vpn/api/test-xray-service';
            console.log('Making Xray request to:', testUrl);
            
            fetch(testUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Xray success response:', data);
                if (data.success) {
                    showTestResult('success', 'Xray Service Test Successful!', data.details);
                } else {
                    showTestResult('warning', 'Xray Service Issues Found: ' + (data.message || 'Unknown error'), data.details);
                }
            })
            .catch(error => {
                console.log('Xray error:', error);
                showTestResult('error', 'Xray service test failed: ' + error.message);
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }
        
        // Show test result function
        window.showTestResult = function(type, message, details) {
            console.log('Showing test result:', type, message, details);
            
            const alertClass = type === 'success' ? 'alert-success' :
                type === 'warning' ? 'alert-warning' : 'alert-danger';
            
            const icon = type === 'success' ? 'fas fa-check-circle' :
                type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-times-circle';
            
            const testAlert = document.getElementById('testAlert');
            const testDetails = document.getElementById('testDetails');
            const testResults = document.getElementById('testResults');
            
            // Clear previous classes
            testAlert.className = 'alert ' + alertClass;
            testAlert.innerHTML = `<i class="${icon}"></i> ${message}`;
            
            if (details) {
                let detailsHtml = '<strong>Details:</strong><br>';
                if (typeof details === 'object') {
                    for (const [key, value] of Object.entries(details)) {
                        detailsHtml += `<strong>${key}:</strong> ${value}<br>`;
                    }
                } else {
                    detailsHtml += details;
                }
                testDetails.innerHTML = detailsHtml;
            } else {
                testDetails.innerHTML = '';
            }
            
            testResults.style.display = 'block';
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    testResults.style.display = 'none';
                }, 5000);
            }
        }
        
        console.log('Functions defined:');
        console.log('- testSSHConnection:', typeof window.testSSHConnection !== 'undefined');
        console.log('- testXrayService:', typeof window.testXrayService !== 'undefined');
        console.log('- showTestResult:', typeof window.showTestResult !== 'undefined');
    </script>
</body>
</html>
