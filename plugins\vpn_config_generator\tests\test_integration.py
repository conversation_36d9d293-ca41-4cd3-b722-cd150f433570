"""
Integration tests for VPN Config Generator plugin.
"""

import unittest
import sys
import os
import tempfile
import json
import time
from unittest.mock import Mock, patch, MagicMock

# Add plugin directory to path
plugin_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, plugin_dir)

from plugin import VPNConfigGeneratorPlugin
from services import VPNConfigGeneratorService
from models import VPNConfigRequest, ConfigTemplate


class TestVPNConfigGeneratorIntegration(unittest.TestCase):
    """Integration tests for VPN Config Generator"""
    
    def setUp(self):
        """Set up test environment"""
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp()
        
        # Create mock plugin manager
        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.plugin_configs = {}
        self.mock_plugin_manager.get_plugin.return_value = None
        
        # Create plugin instance
        self.plugin = VPNConfigGeneratorPlugin(self.mock_plugin_manager)
        self.plugin.plugin_dir = self.temp_dir
        
        # Test configuration
        self.test_config = {
            'api_config': {
                'enabled': True,
                'use_vpn_plugin_api': False,
                'fallback_api_endpoint': 'https://test.api.com',
                'fallback_username': 'admin',
                'fallback_password': 'admin123',
                'timeout': 30
            },
            'generator_settings': {
                'default_server': 'server11',
                'default_days': '30',
                'default_telco': 'digi',
                'default_plan': 'unlimited',
                'auto_generate_username': True,
                'username_prefix': 'user'
            }
        }
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_full_plugin_lifecycle(self):
        """Test complete plugin lifecycle"""
        # 1. Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            
            result = self.plugin.initialize(self.test_config)
            self.assertTrue(result)
            self.assertIsNotNone(self.plugin.config_service)
        
        # 2. Test configuration management
        api_config = self.plugin.config_service.get_api_config()
        self.assertTrue(api_config.enabled)
        self.assertEqual(api_config.fallback_api_endpoint, 'https://test.api.com')
        
        # 3. Test template management
        template = ConfigTemplate(
            id="test_template",
            name="Test Template",
            description="Integration test template",
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited"
        )
        
        add_result = self.plugin.config_service.add_template(template)
        self.assertTrue(add_result)
        
        retrieved_template = self.plugin.config_service.get_template("test_template")
        self.assertIsNotNone(retrieved_template)
        self.assertEqual(retrieved_template.name, "Test Template")
        
        # 4. Test configuration persistence
        self.plugin.config_service.save_config()
        config_file = os.path.join(self.temp_dir, 'config.json')
        self.assertTrue(os.path.exists(config_file))
        
        # 5. Test shutdown
        with patch.object(self.plugin, '_unregister_chat_commands'):
            shutdown_result = self.plugin.shutdown()
            self.assertTrue(shutdown_result)
    
    def test_config_generation_workflow(self):
        """Test complete config generation workflow"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            
            self.plugin.initialize(self.test_config)
        
        # Mock successful API response
        with patch('requests.Session') as mock_session_class:
            mock_session = Mock()
            mock_session_class.return_value = mock_session
            
            # Mock authentication response
            mock_auth_response = Mock()
            mock_auth_response.status_code = 200
            mock_auth_response.json.return_value = {'access_token': 'test_token'}
            
            # Mock client creation response
            mock_client_response = Mock()
            mock_client_response.status_code = 201
            mock_client_response.json.return_value = {
                'config_url': 'vmess://test-config-url'
            }
            
            mock_session.post.side_effect = [mock_auth_response, mock_client_response]
            
            # Test config generation
            request = VPNConfigRequest(
                server="server11",
                days="30",
                telco="digi",
                plan="unlimited",
                username="testuser"
            )
            
            result = self.plugin.config_service.generate_config(request)
            
            self.assertTrue(result.success)
            self.assertEqual(result.config, 'vmess://test-config-url')
            self.assertIsNotNone(result.created_date)
            self.assertIsNotNone(result.expired_date)
    
    def test_chat_command_integration(self):
        """Test chat command integration"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            
            self.plugin.initialize(self.test_config)
        
        # Mock successful config generation
        mock_response = Mock()
        mock_response.success = True
        mock_response.created_date = '2024-01-01'
        mock_response.expired_date = '2024-01-31'
        mock_response.config = 'vmess://test-config'
        
        self.plugin.config_service.generate_config.return_value = mock_response
        
        # Mock message
        mock_message = Mock()
        mock_message.sender_name = 'testuser'
        
        # Test command handling
        params = ['server11', '30', 'digi', 'unlimited']
        responses = self.plugin._handle_config_command(mock_message, params)
        
        # Verify responses
        self.assertIsNotNone(responses)
        self.assertGreaterEqual(len(responses), 1)
        
        # Check response content
        main_response = responses[0]
        self.assertIn('ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ', main_response.text)
        self.assertIn('2024-01-01', main_response.text)
        self.assertIn('2024-01-31', main_response.text)
        self.assertIn('digi', main_response.text)
        self.assertIn('30', main_response.text)
        
        # Check config response if present
        if len(responses) > 1:
            config_response = responses[1]
            self.assertIn('vmess://test-config', config_response.text)
    
    def test_template_workflow(self):
        """Test complete template management workflow"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            
            self.plugin.initialize(self.test_config)
        
        service = self.plugin.config_service
        
        # 1. Create multiple templates
        templates = [
            ConfigTemplate(
                id="template1",
                name="Digi Template",
                description="Template for Digi users",
                server="server11",
                days="30",
                telco="digi",
                plan="unlimited"
            ),
            ConfigTemplate(
                id="template2",
                name="Maxis Template",
                description="Template for Maxis users",
                server="server12",
                days="7",
                telco="maxis",
                plan="basic"
            )
        ]
        
        for template in templates:
            result = service.add_template(template)
            self.assertTrue(result)
        
        # 2. Retrieve all templates
        all_templates = service.get_all_templates()
        self.assertEqual(len(all_templates), 2)
        
        # 3. Update a template
        template1 = service.get_template("template1")
        template1.days = "60"
        update_result = service.update_template("template1", template1)
        self.assertTrue(update_result)
        
        # Verify update
        updated_template = service.get_template("template1")
        self.assertEqual(updated_template.days, "60")
        
        # 4. Delete a template
        delete_result = service.delete_template("template2")
        self.assertTrue(delete_result)
        
        # Verify deletion
        remaining_templates = service.get_all_templates()
        self.assertEqual(len(remaining_templates), 1)
        self.assertEqual(remaining_templates[0].id, "template1")
    
    def test_configuration_persistence(self):
        """Test configuration persistence across plugin restarts"""
        # Initialize plugin with custom config
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            
            self.plugin.initialize(self.test_config)
        
        # Modify configuration
        api_config = self.plugin.config_service.get_api_config()
        api_config.timeout = 120
        api_config.fallback_username = 'modified_admin'
        
        self.plugin.config_service.update_api_config(api_config)
        
        # Add a template
        template = ConfigTemplate(
            id="persistent_template",
            name="Persistent Template",
            description="Test persistence",
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited"
        )
        self.plugin.config_service.add_template(template)
        
        # Shutdown plugin
        with patch.object(self.plugin, '_unregister_chat_commands'):
            self.plugin.shutdown()
        
        # Create new plugin instance (simulating restart)
        new_plugin = VPNConfigGeneratorPlugin(self.mock_plugin_manager)
        new_plugin.plugin_dir = self.temp_dir
        
        # Initialize new plugin
        with patch.object(new_plugin, '_register_api_endpoints'), \
             patch.object(new_plugin, '_register_chat_commands'):
            
            new_plugin.initialize({})  # Empty config to test loading from file
        
        # Verify configuration was persisted
        loaded_api_config = new_plugin.config_service.get_api_config()
        self.assertEqual(loaded_api_config.timeout, 120)
        self.assertEqual(loaded_api_config.fallback_username, 'modified_admin')
        
        # Verify template was persisted
        loaded_template = new_plugin.config_service.get_template("persistent_template")
        self.assertIsNotNone(loaded_template)
        self.assertEqual(loaded_template.name, "Persistent Template")
    
    def test_error_handling_integration(self):
        """Test error handling in integration scenarios"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            
            self.plugin.initialize(self.test_config)
        
        # Test config generation with API failure
        with patch('requests.Session') as mock_session_class:
            mock_session = Mock()
            mock_session_class.return_value = mock_session
            
            # Mock authentication failure
            mock_auth_response = Mock()
            mock_auth_response.status_code = 401
            mock_session.post.return_value = mock_auth_response
            
            request = VPNConfigRequest(
                server="server11",
                days="30",
                telco="digi",
                plan="unlimited",
                username="testuser"
            )
            
            result = self.plugin.config_service.generate_config(request)
            
            self.assertFalse(result.success)
            self.assertIn("Authentication failed", result.error)
        
        # Test invalid template operations
        invalid_template_result = self.plugin.config_service.get_template("nonexistent")
        self.assertIsNone(invalid_template_result)
        
        delete_nonexistent_result = self.plugin.config_service.delete_template("nonexistent")
        self.assertFalse(delete_nonexistent_result)


if __name__ == '__main__':
    unittest.main()
