# Environment variables (IMPORTANT: Never commit sensitive data)
.env
.env.local
.env.production
.env.staging

# Configuration files with sensitive data
config.json
configs/core/config.json
**/config.json

# Backup files
*.backup
config.py.backup

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Data and cache files
data/
cache/
*.json.cache
*.cache

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp

# Browser extension builds
extension.crx
extension.pem

# Sensitive directories
configs/data/
ShopeeAPI/cache/

# Pytest
.pytest_cache/
.coverage

# Flask
instance/

# Security
*.key
*.cert
*.pem
private_keys/