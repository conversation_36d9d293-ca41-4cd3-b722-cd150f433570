#!/bin/bash

# Docker entrypoint script for SteamCodeTool
# Fully automated deployment - no manual intervention required

echo "=== SteamCodeTool Docker Container Starting ==="
echo "Working directory: $(pwd)"
echo "User: $(whoami) ($(id -u):$(id -g))"

# Ensure all directories exist with proper permissions
mkdir -p /app/configs/core /app/configs/cache /app/configs/data /app/configs/services /app/logs /app/data /app/data/netflix

# Force proper permissions (running as root, so this should always work)
chmod -R 777 /app/configs /app/logs /app/data 2>/dev/null || echo "⚠️ Could not set permissions, but continuing..."

echo "📁 Directory permissions:"
ls -la /app/configs/ 2>/dev/null || echo "configs directory not accessible"
ls -la /app/logs/ 2>/dev/null || echo "logs directory not accessible"
ls -la /app/data/ 2>/dev/null || echo "data directory not accessible"

# Initialize configuration automatically
echo "=== Auto-Initializing Configuration ==="
python -c "
import sys
sys.path.append('/app')
try:
    from utils.config_initializer import initialize_all_configs
    print('🚀 Auto-initializing all configuration files...')
    initialize_all_configs()
    print('✅ Configuration initialization completed!')
except Exception as e:
    print(f'⚠️ Config initialization warning: {e}')
    print('📝 Application will create configs at runtime')
" 2>/dev/null || echo "📝 Configuration will be created at runtime"

echo "=== Starting Application ==="
echo "🌐 SteamCodeTool will be available at http://localhost:5000"
echo "📊 Health check available at http://localhost:5000/health"

# Start the application
exec python main.py
