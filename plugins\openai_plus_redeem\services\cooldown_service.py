"""
Cooldown Service

Service for managing user cooldowns and access restrictions.
Provides centralized cooldown management with admin override capabilities.
"""

from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta

from .base_service import BaseService
from ..models.account_cooldown import AccountCooldown, CooldownType
from ..models.utils import (
    load_json_data, save_json_data, validate_username, format_duration,
    DataPersistenceError, ValidationError, get_data_file_path
)


class CooldownService(BaseService):
    """
    Service for managing user cooldowns
    
    Provides functionality for:
    - Setting and managing cooldowns
    - Checking cooldown status
    - Admin cooldown overrides
    - Cooldown cleanup and maintenance
    - Bulk cooldown operations
    """
    
    def __init__(self, config: Dict[str, Any], logger=None):
        super().__init__(config, logger)
        self.service_name = "CooldownService"
        
        # Data file path
        self.data_file = get_data_file_path('openai_plus_redeem', 'cooldowns.json')
        
        # Configuration
        self.cooldown_config = self._get_config_value('cooldown_config', {})
        self.default_cooldown_hours = self.cooldown_config.get('default_hours', 24)
        self.max_cooldown_hours = self.cooldown_config.get('max_hours', 168)  # 7 days
        self.cleanup_interval_hours = self.cooldown_config.get('cleanup_interval_hours', 24)
        self.enable_admin_override = self.cooldown_config.get('enable_admin_override', True)
        
        # Cooldown cache
        self._cooldowns: Dict[str, AccountCooldown] = {}
        self._cache_loaded = False
        self._last_cleanup = datetime.now()
    
    def initialize(self) -> bool:
        """Initialize the Cooldown Service"""
        try:
            self.logger.info(f"Initializing {self.service_name}...")
            
            # Validate configuration
            if not self._validate_service_config():
                return False
            
            # Load cooldowns
            if not self._load_cooldowns():
                self.logger.warning("Failed to load cooldowns, starting with empty cache")
                self._cooldowns = {}
            
            self._cache_loaded = True
            
            # Perform initial cleanup
            self._cleanup_expired_cooldowns()
            
            self._mark_initialized()
            return True
            
        except Exception as e:
            self._handle_service_error("initialize", e)
            return False
    
    def shutdown(self) -> bool:
        """Shutdown the Cooldown Service"""
        try:
            self.logger.info(f"Shutting down {self.service_name}...")
            
            # Save cooldowns
            if self._cache_loaded:
                self._save_cooldowns()
            
            # Clear cache
            self._cooldowns.clear()
            self._cache_loaded = False
            
            self._mark_shutdown()
            return True
            
        except Exception as e:
            self._handle_service_error("shutdown", e)
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        self._update_health_check_time()
        
        try:
            # Perform periodic cleanup
            self._periodic_cleanup()
            
            active_cooldowns = sum(1 for c in self._cooldowns.values() if not c.is_expired())
            expired_cooldowns = sum(1 for c in self._cooldowns.values() if c.is_expired())
            
            health_data = {
                'status': 'healthy',
                'service': self.service_name,
                'cache_loaded': self._cache_loaded,
                'total_cooldowns': len(self._cooldowns),
                'active_cooldowns': active_cooldowns,
                'expired_cooldowns': expired_cooldowns,
                'last_cleanup': self._last_cleanup.isoformat(),
                'timestamp': datetime.now().isoformat()
            }
            
            # Check for issues
            issues = []
            if not self._cache_loaded:
                issues.append("Cache not loaded")
            
            if expired_cooldowns > 100:  # Arbitrary threshold
                issues.append(f"High number of expired cooldowns ({expired_cooldowns})")
            
            if issues:
                health_data['status'] = 'degraded'
                health_data['issues'] = issues
            
            return health_data
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': self.service_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def set_user_cooldown(self, username: str, hours: int, 
                         cooldown_type: CooldownType = CooldownType.REDEMPTION,
                         reason: str = "") -> Dict[str, Any]:
        """
        Set cooldown for a user
        
        Args:
            username: User's username
            hours: Cooldown duration in hours
            cooldown_type: Type of cooldown
            reason: Reason for cooldown
            
        Returns:
            Dictionary with cooldown result
        """
        try:
            if not validate_username(username):
                return {
                    'success': False,
                    'error': 'Invalid username format'
                }
            
            if hours < 0 or hours > self.max_cooldown_hours:
                return {
                    'success': False,
                    'error': f'Invalid cooldown hours: {hours} (max: {self.max_cooldown_hours})'
                }
            
            # Create cooldown
            cooldown = AccountCooldown.create_cooldown(
                username=username,
                hours=hours,
                cooldown_type=cooldown_type,
                reason=reason
            )
            
            # Store cooldown
            self._cooldowns[cooldown.cooldown_id] = cooldown
            
            # Save to storage
            self._save_cooldowns()
            
            self._log_operation("set_user_cooldown", {
                'username': username,
                'hours': hours,
                'cooldown_type': cooldown_type.value,
                'reason': reason,
                'cooldown_id': cooldown.cooldown_id
            })
            
            return {
                'success': True,
                'cooldown_id': cooldown.cooldown_id,
                'username': username,
                'cooldown_until': cooldown.cooldown_until,
                'hours': hours,
                'message': f'Cooldown set for {hours} hours'
            }
            
        except Exception as e:
            self._handle_service_error("set_user_cooldown", e)
            return {
                'success': False,
                'error': f'Failed to set cooldown: {str(e)}'
            }
    
    def get_user_cooldown_status(self, username: str) -> Dict[str, Any]:
        """
        Get cooldown status for a user
        
        Args:
            username: User's username
            
        Returns:
            Dictionary with cooldown status
        """
        try:
            # Find active cooldowns for user
            user_cooldowns = [
                cooldown for cooldown in self._cooldowns.values()
                if cooldown.username == username and not cooldown.is_expired()
            ]
            
            if not user_cooldowns:
                return {
                    'has_cooldown': False,
                    'username': username,
                    'can_redeem': True
                }
            
            # Find the longest remaining cooldown
            longest_cooldown = max(user_cooldowns, key=lambda c: c.cooldown_until)
            remaining_time = longest_cooldown.get_remaining_time()
            
            return {
                'has_cooldown': True,
                'username': username,
                'can_redeem': False,
                'cooldown_id': longest_cooldown.cooldown_id,
                'cooldown_type': longest_cooldown.cooldown_type.value,
                'cooldown_until': longest_cooldown.cooldown_until,
                'remaining_seconds': int(remaining_time.total_seconds()),
                'remaining_hours': remaining_time.total_seconds() / 3600,
                'remaining_formatted': format_duration(remaining_time),
                'reason': longest_cooldown.reason,
                'total_active_cooldowns': len(user_cooldowns)
            }
            
        except Exception as e:
            self._handle_service_error("get_user_cooldown_status", e)
            return {
                'has_cooldown': False,
                'username': username,
                'error': f'Failed to check status: {str(e)}'
            }
    
    def remove_user_cooldown(self, username: str, cooldown_type: CooldownType = None,
                           admin_override: bool = False) -> Dict[str, Any]:
        """
        Remove cooldown(s) for a user
        
        Args:
            username: User's username
            cooldown_type: Specific cooldown type to remove (optional)
            admin_override: Whether this is an admin override
            
        Returns:
            Dictionary with removal result
        """
        try:
            if admin_override and not self.enable_admin_override:
                return {
                    'success': False,
                    'error': 'Admin override is disabled'
                }
            
            # Find cooldowns to remove
            cooldowns_to_remove = []
            for cooldown_id, cooldown in self._cooldowns.items():
                if cooldown.username == username and not cooldown.is_expired():
                    if cooldown_type is None or cooldown.cooldown_type == cooldown_type:
                        cooldowns_to_remove.append(cooldown_id)
            
            if not cooldowns_to_remove:
                return {
                    'success': False,
                    'error': 'No active cooldowns found for user'
                }
            
            # Remove cooldowns
            removed_count = 0
            for cooldown_id in cooldowns_to_remove:
                if cooldown_id in self._cooldowns:
                    cooldown = self._cooldowns[cooldown_id]
                    if admin_override:
                        cooldown.admin_override(reason="Admin manual removal")
                    else:
                        cooldown.expire_cooldown()
                    removed_count += 1
            
            # Save changes
            self._save_cooldowns()
            
            self._log_operation("remove_user_cooldown", {
                'username': username,
                'cooldown_type': cooldown_type.value if cooldown_type else 'all',
                'admin_override': admin_override,
                'removed_count': removed_count
            })
            
            return {
                'success': True,
                'username': username,
                'removed_count': removed_count,
                'admin_override': admin_override,
                'message': f'Removed {removed_count} cooldown(s) for user'
            }
            
        except Exception as e:
            self._handle_service_error("remove_user_cooldown", e)
            return {
                'success': False,
                'error': f'Failed to remove cooldown: {str(e)}'
            }
    
    def get_all_active_cooldowns(self) -> List[Dict[str, Any]]:
        """
        Get all active cooldowns
        
        Returns:
            List of active cooldown information
        """
        try:
            active_cooldowns = []
            
            for cooldown in self._cooldowns.values():
                if not cooldown.is_expired():
                    remaining_time = cooldown.get_remaining_time()
                    active_cooldowns.append({
                        'cooldown_id': cooldown.cooldown_id,
                        'username': cooldown.username,
                        'cooldown_type': cooldown.cooldown_type.value,
                        'cooldown_until': cooldown.cooldown_until,
                        'remaining_seconds': int(remaining_time.total_seconds()),
                        'remaining_hours': remaining_time.total_seconds() / 3600,
                        'remaining_formatted': format_duration(remaining_time),
                        'reason': cooldown.reason,
                        'created_at': cooldown.created_at,
                        'admin_override_available': not cooldown.admin_overridden
                    })
            
            # Sort by remaining time (shortest first)
            active_cooldowns.sort(key=lambda x: x['remaining_seconds'])
            
            return active_cooldowns
            
        except Exception as e:
            self._handle_service_error("get_all_active_cooldowns", e)
            return []
    
    def get_cooldown_statistics(self) -> Dict[str, Any]:
        """
        Get cooldown statistics
        
        Returns:
            Dictionary with cooldown statistics
        """
        try:
            now = datetime.now()
            
            # Count cooldowns by status
            active_count = 0
            expired_count = 0
            overridden_count = 0
            
            # Count by type
            type_counts = {}
            for cooldown_type in CooldownType:
                type_counts[cooldown_type.value] = 0
            
            # Count by time ranges
            expiring_soon = 0  # < 1 hour
            expiring_today = 0  # < 24 hours
            
            for cooldown in self._cooldowns.values():
                if cooldown.is_expired():
                    expired_count += 1
                else:
                    active_count += 1
                    remaining = cooldown.get_remaining_time()
                    
                    if remaining.total_seconds() < 3600:  # < 1 hour
                        expiring_soon += 1
                    elif remaining.total_seconds() < 86400:  # < 24 hours
                        expiring_today += 1
                
                if cooldown.admin_overridden:
                    overridden_count += 1
                
                type_counts[cooldown.cooldown_type.value] += 1
            
            return {
                'total_cooldowns': len(self._cooldowns),
                'active_cooldowns': active_count,
                'expired_cooldowns': expired_count,
                'admin_overridden': overridden_count,
                'expiring_soon': expiring_soon,
                'expiring_today': expiring_today,
                'cooldowns_by_type': type_counts,
                'last_cleanup': self._last_cleanup.isoformat(),
                'timestamp': now.isoformat()
            }
            
        except Exception as e:
            self._handle_service_error("get_cooldown_statistics", e)
            return {
                'error': f'Failed to get statistics: {str(e)}'
            }
    
    def cleanup_expired_cooldowns(self) -> Dict[str, Any]:
        """
        Manually trigger cleanup of expired cooldowns
        
        Returns:
            Dictionary with cleanup result
        """
        try:
            removed_count = self._cleanup_expired_cooldowns()
            
            return {
                'success': True,
                'removed_count': removed_count,
                'remaining_cooldowns': len(self._cooldowns),
                'cleanup_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self._handle_service_error("cleanup_expired_cooldowns", e)
            return {
                'success': False,
                'error': f'Cleanup failed: {str(e)}'
            }
    
    def bulk_set_cooldowns(self, usernames: List[str], hours: int,
                          cooldown_type: CooldownType = CooldownType.REDEMPTION,
                          reason: str = "") -> Dict[str, Any]:
        """
        Set cooldowns for multiple users
        
        Args:
            usernames: List of usernames
            hours: Cooldown duration in hours
            cooldown_type: Type of cooldown
            reason: Reason for cooldown
            
        Returns:
            Dictionary with bulk operation result
        """
        try:
            if hours < 0 or hours > self.max_cooldown_hours:
                return {
                    'success': False,
                    'error': f'Invalid cooldown hours: {hours} (max: {self.max_cooldown_hours})'
                }
            
            results = []
            success_count = 0
            
            for username in usernames:
                if not validate_username(username):
                    results.append({
                        'username': username,
                        'success': False,
                        'error': 'Invalid username format'
                    })
                    continue
                
                try:
                    # Create cooldown
                    cooldown = AccountCooldown.create_cooldown(
                        username=username,
                        hours=hours,
                        cooldown_type=cooldown_type,
                        reason=reason
                    )
                    
                    # Store cooldown
                    self._cooldowns[cooldown.cooldown_id] = cooldown
                    
                    results.append({
                        'username': username,
                        'success': True,
                        'cooldown_id': cooldown.cooldown_id,
                        'cooldown_until': cooldown.cooldown_until
                    })
                    
                    success_count += 1
                    
                except Exception as e:
                    results.append({
                        'username': username,
                        'success': False,
                        'error': str(e)
                    })
            
            # Save changes
            self._save_cooldowns()
            
            self._log_operation("bulk_set_cooldowns", {
                'total_users': len(usernames),
                'success_count': success_count,
                'hours': hours,
                'cooldown_type': cooldown_type.value,
                'reason': reason
            })
            
            return {
                'success': True,
                'total_users': len(usernames),
                'success_count': success_count,
                'failed_count': len(usernames) - success_count,
                'results': results
            }
            
        except Exception as e:
            self._handle_service_error("bulk_set_cooldowns", e)
            return {
                'success': False,
                'error': f'Bulk operation failed: {str(e)}'
            }
    
    def get_users_in_cooldown(self, cooldown_type: CooldownType = None) -> List[str]:
        """
        Get list of users currently in cooldown
        
        Args:
            cooldown_type: Filter by cooldown type (optional)
            
        Returns:
            List of usernames in cooldown
        """
        try:
            users_in_cooldown = set()
            
            for cooldown in self._cooldowns.values():
                if not cooldown.is_expired():
                    if cooldown_type is None or cooldown.cooldown_type == cooldown_type:
                        users_in_cooldown.add(cooldown.username)
            
            return sorted(list(users_in_cooldown))
            
        except Exception as e:
            self._handle_service_error("get_users_in_cooldown", e)
            return []
    
    def _cleanup_expired_cooldowns(self) -> int:
        """
        Remove expired cooldowns from cache
        
        Returns:
            Number of cooldowns removed
        """
        try:
            expired_ids = [
                cooldown_id for cooldown_id, cooldown in self._cooldowns.items()
                if cooldown.is_expired()
            ]
            
            for cooldown_id in expired_ids:
                del self._cooldowns[cooldown_id]
            
            if expired_ids:
                self._save_cooldowns()
                self.logger.info(f"Cleaned up {len(expired_ids)} expired cooldowns")
            
            self._last_cleanup = datetime.now()
            return len(expired_ids)
            
        except Exception as e:
            self.logger.error(f"Error during cooldown cleanup: {e}")
            return 0
    
    def _periodic_cleanup(self) -> None:
        """Perform periodic cleanup if needed"""
        if (datetime.now() - self._last_cleanup).total_seconds() > (self.cleanup_interval_hours * 3600):
            self._cleanup_expired_cooldowns()
    
    def _validate_service_config(self) -> bool:
        """Validate service configuration"""
        if self.default_cooldown_hours < 0:
            self.logger.error("Invalid default cooldown hours")
            return False
        
        if self.max_cooldown_hours < self.default_cooldown_hours:
            self.logger.error("Max cooldown hours must be >= default cooldown hours")
            return False
        
        return True
    
    def _load_cooldowns(self) -> bool:
        """Load cooldowns from storage"""
        try:
            data = load_json_data(self.data_file)
            cooldowns_data = data.get('cooldowns', [])
            
            self._cooldowns.clear()
            
            for cooldown_data in cooldowns_data:
                try:
                    cooldown = AccountCooldown.from_dict(cooldown_data)
                    self._cooldowns[cooldown.cooldown_id] = cooldown
                except Exception as e:
                    self.logger.warning(f"Failed to load cooldown: {e}")
            
            self.logger.info(f"Loaded {len(self._cooldowns)} cooldowns")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load cooldowns: {e}")
            return False
    
    def _save_cooldowns(self) -> bool:
        """Save cooldowns to storage"""
        try:
            cooldowns_data = [cooldown.to_dict() for cooldown in self._cooldowns.values()]
            
            data = {
                'cooldowns': cooldowns_data,
                'metadata': {
                    'version': '1.0.0',
                    'total_cooldowns': len(cooldowns_data),
                    'last_updated': datetime.now().isoformat(),
                    'last_cleanup': self._last_cleanup.isoformat()
                }
            }
            
            save_json_data(self.data_file, data)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save cooldowns: {e}")
            return False
