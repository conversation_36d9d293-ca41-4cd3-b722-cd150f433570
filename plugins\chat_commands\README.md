# Chat Commands Plugin

This plugin provides automated chat command responses for Shopee conversations. It subscribes to ShopeeAPI webhooks and responds to commands like `#android_help`, `#ios_help`, etc.

## Features

- **Automated Command Processing**: Responds to chat commands starting with `#`
- **Configurable Commands**: Easy CRUD operations for chat commands through web UI
- **Image Support**: Send images along with text responses
- **VPN Config Generation**: Generate VPN configurations using configurable API endpoint
- **Webhook Integration**: Subscribes to ShopeeAPI message_received webhooks
- **Real-time Processing**: Instant responses to customer commands
- **UI Management**: Web-based interface for managing commands and settings

## Supported Commands

### Default Commands

- `#android_help` - Android VPN setup instructions with tutorial image
- `#ios_help` - iOS VPN setup instructions with tutorial image
- `#android_digi` - Android Digi APN settings with images
- `#ios_digi` - iOS Digi APN settings with image
- `#help` - List of available commands
- `#vpn` - Explain what VPN configuration is
- `#bypass` - Provide bypass configuration links
- `#openwrt_help` - OpenWrt tutorial link

**Note:** VPN configuration generation (`#config` command) is now handled by the dedicated VPN Config Generator plugin.

### Custom Commands

You can add, edit, and delete commands through the web interface at `/chat-commands/`.

## Configuration

### Plugin Configuration

- **Default Endpoint**: `https://blueblue.api.limjianhui.com/openapi.json`
- **Default Credentials**: `admin` / `admin123`
- **Configurable**: All settings can be changed through the web UI

### Webhook Configuration

The plugin provides comprehensive webhook management through the web interface:

#### Webhook Settings
- **ShopeeAPI Base URL**: The URL where your ShopeeAPI is running (default: `http://localhost:8000`)
- **SteamCodeTool Base URL**: The URL where SteamCodeTool is accessible (default: `http://localhost:5000`)
- **Webhook Endpoint**: The endpoint path for receiving webhooks (default: `/chat-commands/api/webhook`)
- **Auto Register**: Automatically register webhook with ShopeeAPI on startup
- **Retry Count**: Number of retry attempts for failed webhook deliveries (default: 3)
- **Retry Delay**: Delay between retry attempts in seconds (default: 5)
- **Timeout**: Request timeout in seconds (default: 30)

#### Webhook Management Features
- **Real-time Status Monitoring**: Check connectivity to ShopeeAPI and webhook endpoint health
- **One-click Registration**: Register/unregister webhooks with ShopeeAPI directly from the UI
- **Connectivity Testing**: Test both ShopeeAPI connectivity and webhook endpoint accessibility
- **Configuration Validation**: Ensure all webhook settings are properly configured

## Installation

1. The plugin is automatically discovered by the SteamCodeTool plugin manager
2. Enable the plugin through the admin interface
3. Configure VPN settings if needed
4. Commands are ready to use immediately

## Usage

### Managing Commands

1. Navigate to `/chat-commands/` in your SteamCodeTool interface
2. View all existing commands in the table
3. Use the "Add Command" button to create new commands
4. Edit existing commands by clicking the edit button
5. Test commands using the test button
6. Delete commands using the delete button

### Command Structure

Each command consists of:
- **Command Name**: The trigger word (without #)
- **Description**: Human-readable description
- **Response Text**: The text message to send
- **Image URLs**: Optional list of image URLs to send
- **Required Parameters**: For commands that need parameters
- **Enabled Status**: Whether the command is active

### VPN Configuration Generation

**VPN configuration generation has been moved to the VPN Config Generator plugin.**

The `#config` command is now handled by the dedicated VPN Config Generator plugin, which provides:
- Better separation of concerns
- Dedicated configuration management
- Enhanced webhook handling
- Template-based configuration generation

To use VPN configuration features, ensure the VPN Config Generator plugin is enabled.

### Webhook Management

The plugin provides comprehensive webhook management through the web interface:

#### Configuration
1. Navigate to `/chat-commands/` in your SteamCodeTool interface
2. Locate the "Webhook Configuration" section
3. Configure the following settings:
   - **ShopeeAPI Base URL**: Where your ShopeeAPI is running
   - **SteamCodeTool Base URL**: Where SteamCodeTool is accessible
   - **Retry Count**: Number of retry attempts for failed deliveries
   - **Timeout**: Request timeout in seconds
   - **Enable Webhook**: Toggle webhook processing on/off
   - **Auto Register**: Automatically register with ShopeeAPI on startup

#### Status Monitoring
1. Check the "Webhook Status" section for real-time status
2. Monitor ShopeeAPI connectivity
3. Verify webhook endpoint accessibility
4. View configuration details

#### Management Actions
- **Test**: Test connectivity to both ShopeeAPI and webhook endpoint
- **Register**: Register webhook with ShopeeAPI to start receiving events
- **Unregister**: Remove webhook registration to stop receiving events
- **Refresh Status**: Update status information

## API Endpoints

### Commands Management
- `GET /chat-commands/api/commands` - Get all commands
- `GET /chat-commands/api/commands/<name>` - Get specific command
- `POST /chat-commands/api/commands` - Create new command
- `PUT /chat-commands/api/commands/<name>` - Update command
- `DELETE /chat-commands/api/commands/<name>` - Delete command

### VPN Configuration
- `GET /chat-commands/api/vpn-config` - Get VPN configuration
- `PUT /chat-commands/api/vpn-config` - Update VPN configuration

### Webhook Configuration
- `GET /chat-commands/api/webhook-config` - Get webhook configuration
- `PUT /chat-commands/api/webhook-config` - Update webhook configuration

### Webhook Management
- `POST /chat-commands/api/webhook` - Receive ShopeeAPI webhooks
- `GET /chat-commands/api/webhook/status` - Get webhook status and connectivity
- `POST /chat-commands/api/webhook/register` - Register webhook with ShopeeAPI
- `POST /chat-commands/api/webhook/unregister` - Unregister webhook from ShopeeAPI
- `POST /chat-commands/api/webhook/test` - Test webhook connectivity

### Testing
- `POST /chat-commands/api/test-command` - Test a command

## Integration with ShopeeAPI

The plugin integrates with the existing ShopeeAPI infrastructure:

1. **Webhook Subscription**: Automatically subscribes to `message_received` events
2. **Message Processing**: Processes incoming messages for commands
3. **Response Sending**: Uses ShopeeAPI to send responses back to customers
4. **Real-time**: No polling required, instant response to commands

## File Structure

```
plugins/chat_commands/
├── __init__.py              # Plugin package initialization
├── plugin.py                # Main plugin class
├── models.py                # Data models
├── services.py              # Business logic services
├── routes.py                # Flask routes/API endpoints
├── webhook_integration.py   # Webhook integration logic
├── config.json              # Plugin configuration
├── commands.json            # Command definitions
└── README.md               # This documentation
```

## Customization

### Adding New Commands

1. Use the web interface to add commands, or
2. Edit `commands.json` directly and restart the plugin

### Custom Response Logic

For complex command logic, modify the `MessageProcessor` class in `services.py`.

### Custom VPN API

Update the VPN configuration through the web interface to use your own API endpoint.

## Troubleshooting

### Commands Not Responding

1. Check if the plugin is enabled
2. Verify webhook integration is working
3. Check ShopeeAPI connectivity
4. Review plugin logs

### VPN Config Generation Fails

1. Verify VPN API endpoint is accessible
2. Check credentials are correct
3. Ensure API is responding correctly
4. Review error logs

### Webhook Issues

1. Check if ShopeeAPI is running
2. Verify webhook registration
3. Test webhook endpoint manually
4. Check network connectivity

## Migration from Stools-MTYB

This plugin replaces the polling-based approach from Stools-MTYB with a webhook-based system:

- **Before**: Polling conversations every few seconds
- **After**: Real-time webhook processing
- **Benefits**: Lower latency, reduced server load, more reliable

All existing commands and responses are preserved and enhanced with the new system.
