"""
Shopee Auto Boost Plugin Implementation

Automatically boosts 5 products every 4 hours to improve visibility.
"""

import os
import logging
from typing import Dict, Any, Optional
from flask import Blueprint

from core.plugin_manager import PluginInterface
from .services.boost_service import BoostService
from .services.product_service import ProductService
from .services.scheduler_service import SchedulerService
from .routes.boost_routes import create_boost_routes

logger = logging.getLogger(__name__)


class Plugin(PluginInterface):
    """Shopee Auto Boost Plugin"""

    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "shopee_auto_boost"
        self.version = "1.0.0"
        self.description = "Automatically boost 5 products every 4 hours on Shopee via local ShopeeAPI service"
        self.dependencies = []  # No longer depends on shopee plugin - uses local ShopeeAPI service
        self.url_prefix = "/api/shopee_auto_boost"

        # Plugin directory
        self.plugin_dir = os.path.dirname(__file__)
        
        # Services
        self.boost_service: Optional[BoostService] = None
        self.product_service: Optional[ProductService] = None
        self.scheduler_service: Optional[SchedulerService] = None
        self.blueprint: Optional[Blueprint] = None

        # Shopee plugin reference
        self.shopee_plugin = None

    def initialize(self) -> bool:
        """Initialize the plugin"""
        try:
            logger.info(f"Initializing {self.name} plugin...")

            # Check if ShopeeAPI service is available (but don't fail if it's not)
            api_url = self.config.get("shopee_api_url", "http://localhost:8000")
            api_available = False
            try:
                import requests
                response = requests.get(f"{api_url}/status", timeout=5)
                if response.status_code == 200:
                    logger.info(f"ShopeeAPI service is available at {api_url}")
                    api_available = True
                else:
                    logger.warning(f"ShopeeAPI service returned status {response.status_code} at {api_url}")
            except Exception as e:
                logger.warning(f"Cannot connect to ShopeeAPI service at {api_url}: {e}")
                logger.info("Plugin will continue to load but API-dependent features may not work")

            # Store API availability status
            self.api_available = api_available

            # Initialize services (no longer need shopee plugin)
            self.product_service = ProductService(None, self.config)
            self.boost_service = BoostService(None, self.config)
            self.scheduler_service = SchedulerService(
                self.boost_service,
                self.product_service,
                self.config,
                self.plugin_dir
            )

            # Create routes
            self.blueprint = create_boost_routes(self)

            # Start scheduler if auto_start is enabled (regardless of API availability)
            if self.config.get('auto_start', True):
                self.scheduler_service.start_scheduler()
                if not api_available:
                    logger.info("ShopeeAPI service not available; scheduler will retry at the configured interval")

            logger.info(f"{self.name} plugin initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize {self.name} plugin: {e}")
            return False

    def load_config(self, config):
        """Load plugin configuration and reinitialize services"""
        super().load_config(config)
        
        # Reinitialize services with new configuration if they exist
        if hasattr(self, 'product_service') and self.product_service:
            self.product_service.config = self.config
            self.product_service.api_base_url = self.config.get("shopee_api_url", "http://localhost:8000")
            logger.info(f"Updated product_service API URL to: {self.product_service.api_base_url}")
            
        if hasattr(self, 'boost_service') and self.boost_service:
            self.boost_service.config = self.config
            self.boost_service.api_base_url = self.config.get("shopee_api_url", "http://localhost:8000")
            logger.info(f"Updated boost_service API URL to: {self.boost_service.api_base_url}")

    def get_blueprint(self):
        """Get the plugin's Flask blueprint"""
        return self.blueprint

    def get_config_schema(self):
        """Get configuration schema"""
        return {
            "type": "object",
            "properties": {
                "enabled": {"type": "boolean", "default": True},
                "boost_check_interval_minutes": {"type": "integer", "default": 15, "minimum": 1},
                "boost_interval_hours": {"type": "integer", "default": 4, "minimum": 1},
                "products_per_boost": {"type": "integer", "default": 5, "minimum": 1, "maximum": 10},
                "auto_start": {"type": "boolean", "default": True},
                "shopee_api_url": {"type": "string", "default": "http://localhost:8000"},
                "product_filters": {
                    "type": "object",
                    "properties": {
                        "min_stock": {"type": "integer", "default": 1},
                        "exclude_unlisted": {"type": "boolean", "default": True},
                        "exclude_inactive": {"type": "boolean", "default": True}
                    }
                },
                "rotation_strategy": {
                    "type": "string",
                    "enum": ["least_recently_boosted", "random", "highest_sold"],
                    "default": "least_recently_boosted"
                },
                "pinned_products": {
                    "type": "object",
                    "properties": {
                        "enabled": {"type": "boolean", "default": True},
                        "product_ids": {"type": "array", "default": []},
                        "cooldown_hours": {"type": "integer", "default": 4, "minimum": 1},
                        "description": {"type": "string", "default": "List of product IDs to automatically boost. Each product has individual 4-hour cooldown after successful boost."}
                    }
                },
                "logging": {
                    "type": "object",
                    "properties": {
                        "log_boost_attempts": {"type": "boolean", "default": True},
                        "log_product_selection": {"type": "boolean", "default": True}
                    }
                }
            }
        }

    def get_admin_routes(self) -> Dict[str, str]:
        """Return admin interface routes for this plugin"""
        return {
            "Shopee Auto Boost Dashboard": "/admin/shopee_auto_boost/dashboard",
            "Boostable Products": "/admin/shopee_auto_boost/products",
            "Boost History": "/admin/shopee_auto_boost/history",
            "Boost Settings": "/admin/shopee_auto_boost/settings"
        }

    def get_status(self):
        """Get plugin status"""
        status = super().get_status()
        if self.scheduler_service:
            status.update({
                "scheduler_running": self.scheduler_service.is_running(),
                "next_boost_time": self.scheduler_service.get_next_run_time(),
                "last_boost_time": self.scheduler_service.get_last_boost_time(),
                "total_boosts": self.scheduler_service.get_total_boosts()
            })
        return status

    def shutdown(self) -> bool:
        """Shutdown the plugin"""
        try:
            if self.scheduler_service:
                self.scheduler_service.stop_scheduler()
            logger.info(f"{self.name} plugin shutdown successfully")
            return True
        except Exception as e:
            logger.error(f"Error shutting down {self.name} plugin: {e}")
            return False
