# Universal Fake Order System - API Documentation

## Overview

The Universal Fake Order System provides comprehensive API endpoints for generating, managing, and maintaining fake orders for testing purposes. This documentation covers all available endpoints, request/response formats, and usage examples.

## Base URL

All API endpoints are prefixed with `/api/fake-orders`

## Authentication

All endpoints require API key authentication via the `X-API-Key` header.

```bash
curl -H "X-API-Key: your-api-key" https://your-domain/api/fake-orders/health
```

## Core Endpoints

### 1. Generate Single Fake Order

**Endpoint:** `POST /api/fake-orders/generate`

**Description:** Create a single fake order with full configuration support.

**Required Permissions:** `create_fake_orders`

**Request Body:**
```json
{
  "order_sn": "FAKE_ORDER_001",
  "product_config": {
    "var_sku": "canva_30",
    "quantity": 1,
    "price_override": 15.00,
    "custom_metadata": {
      "test_flag": "automated_test"
    }
  },
  "buyer_config": {
    "username": "test_user",
    "name": "Test Customer",
    "phone": "+60123456789",
    "email": "<EMAIL>",
    "address": "123 Test Street, Test City"
  },
  "order_config": {
    "status": "To Ship",
    "payment_status": "paid",
    "created_date": "2025-01-15T10:30:00Z",
    "custom_timestamps": {},
    "custom_metadata": {}
  },
  "test_scenario": "canva_pro_basic_flow"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Fake order FAKE_ORDER_001 created successfully",
  "order_data": {
    "success": true,
    "data": {
      "order_sn": "FAKE_ORDER_001",
      "var_sku": "canva_30",
      "buyer_username": "test_user",
      "status": "To Ship",
      "payment_status": "paid",
      "quantity": 1,
      "total_price": 15.00,
      "created_at": "2025-01-15T10:30:00Z"
    }
  },
  "order_sn": "FAKE_ORDER_001",
  "processing_options": {
    "process_order_url": "/api/order/process_order",
    "get_order_details_url": "/api/order/get_order_details?order_sn=FAKE_ORDER_001",
    "get_order_status_url": "/api/order/get_order_status?order_sn=FAKE_ORDER_001"
  },
  "validation_warnings": [],
  "generated_at": "2025-01-16T14:30:00Z"
}
```

### 2. Batch Generate Fake Orders

**Endpoint:** `POST /api/fake-orders/batch-generate`

**Description:** Create multiple fake orders in a single request.

**Required Permissions:** `bulk_operations`

**Request Body:**
```json
{
  "orders": [
    {
      "product_config": { "var_sku": "canva_30", "quantity": 1 },
      "buyer_config": { "username": "user1" },
      "order_config": { "status": "To Ship" },
      "test_scenario": "batch_test_1"
    },
    {
      "product_config": { "var_sku": "netflix_premium", "quantity": 1 },
      "buyer_config": { "username": "user2" },
      "order_config": { "status": "To Ship" },
      "test_scenario": "batch_test_2"
    }
  ],
  "batch_config": {
    "continue_on_error": true,
    "generate_unique_order_sns": true,
    "default_test_scenario": "batch_testing"
  }
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Batch generation completed: 2 successful, 0 failed",
  "batch_summary": {
    "total_requested": 2,
    "successful": 2,
    "failed": 0,
    "success_rate": 100.0
  },
  "successful_orders": [
    {
      "index": 0,
      "order_sn": "FAKE_BATCH_000_20250116143000",
      "success": true
    },
    {
      "index": 1,
      "order_sn": "FAKE_BATCH_001_20250116143001",
      "success": true
    }
  ],
  "failed_orders": [],
  "processing_options": {
    "bulk_process_url": "/api/order/process_order",
    "batch_status_check": "/api/fake-orders/list"
  },
  "generated_at": "2025-01-16T14:30:00Z"
}
```

### 3. List Fake Orders

**Endpoint:** `GET /api/fake-orders/list`

**Description:** Retrieve fake orders with filtering and pagination.

**Query Parameters:**
- `limit` (int): Maximum number of orders to return (default: 50, max: 200)
- `status` (string): Filter by order status
- `var_sku` (string): Filter by product SKU
- `test_scenario` (string): Filter by test scenario
- `created_after` (ISO date): Filter orders created after this date
- `created_before` (ISO date): Filter orders created before this date

**Example Request:**
```bash
GET /api/fake-orders/list?limit=10&status=To%20Ship&var_sku=canva_30
```

**Response (200 OK):**
```json
{
  "success": true,
  "fake_orders": [
    {
      "order_sn": "FAKE_ORDER_001",
      "var_sku": "canva_30",
      "buyer_username": "test_user",
      "status": "To Ship",
      "payment_status": "paid",
      "created_at": "2025-01-15T10:30:00Z",
      "test_scenario": "canva_pro_basic_flow",
      "is_processed": false
    }
  ],
  "count": 1,
  "filters_applied": {
    "limit": 10,
    "status": "To Ship",
    "var_sku": "canva_30",
    "test_scenario": null,
    "created_after": null,
    "created_before": null
  },
  "management_options": {
    "bulk_delete_url": "/api/fake-orders/cleanup",
    "individual_delete_url": "/api/order/delete_fake_order/{order_sn}",
    "process_order_url": "/api/order/process_order"
  },
  "retrieved_at": "2025-01-16T14:30:00Z"
}
```

### 4. Cleanup Fake Orders

**Endpoint:** `DELETE /api/fake-orders/cleanup`

**Description:** Bulk delete fake orders based on criteria.

**Required Permissions:** `delete_fake_orders`

**Query Parameters:**
- `older_than_days` (int): Delete orders older than specified days (default: 7)
- `status` (string): Delete orders with specific status
- `test_scenario` (string): Delete orders with specific test scenario
- `confirm` (boolean): Must be 'true' to proceed with deletion

**Alternative JSON Payload for Specific Orders:**
```json
{
  "order_sns": ["FAKE_ORDER_001", "FAKE_ORDER_002"],
  "confirm": true
}
```

**Example Request:**
```bash
DELETE /api/fake-orders/cleanup?older_than_days=14&confirm=true
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Cleaned up 5 fake orders older than 14 days",
  "cleanup_summary": {
    "original_count": 20,
    "removed_count": 5,
    "remaining_count": 15
  },
  "cleanup_method": "date_based",
  "cleaned_at": "2025-01-16T14:30:00Z"
}
```

## Template Management

### 5. Get Product Templates

**Endpoint:** `GET /api/fake-orders/templates`

**Description:** Retrieve available product templates.

**Query Parameters:**
- `category` (string): Filter by category
- `include_sample_data` (boolean): Include sample data in response (default: false)

**Response (200 OK):**
```json
{
  "success": true,
  "templates": {
    "canva_30": {
      "var_sku": "canva_30",
      "product_name": "Canva Pro 30 Days",
      "category": "design_tools",
      "default_price": 15.00,
      "metadata_schema": {
        "account_type": {"type": "str", "allowed_values": ["pro", "teams"]},
        "duration_days": {"type": "int", "min": 1, "max": 365}
      },
      "validation_rules": {
        "required_fields": ["account_type"],
        "optional_fields": ["duration_days"]
      }
    }
  },
  "summary": {
    "total_templates": 1,
    "categories": ["design_tools"],
    "most_used_template": "canva_30"
  },
  "filters_applied": {
    "category": null,
    "include_sample_data": false
  },
  "retrieved_at": "2025-01-16T14:30:00Z"
}
```

### 6. Save Custom Template

**Endpoint:** `POST /api/fake-orders/templates/save`

**Description:** Save a custom product template.

**Request Body:**
```json
{
  "var_sku": "custom_product_123",
  "product_name": "Custom Product Name",
  "category": "custom",
  "default_price": 25.00,
  "metadata_schema": {
    "custom_field": {"type": "str", "allowed_values": ["value1", "value2"]}
  },
  "validation_rules": {
    "required_fields": ["custom_field"],
    "optional_fields": []
  },
  "sample_data": {
    "custom_field": "value1",
    "description": "Sample custom product"
  }
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Custom template 'custom_product_123' saved successfully",
  "template": {
    "var_sku": "custom_product_123",
    "product_name": "Custom Product Name",
    "category": "custom",
    "default_price": 25.00
  },
  "saved_at": "2025-01-16T14:30:00Z"
}
```

## Maintenance Operations

### 7. Run Maintenance

**Endpoint:** `POST /api/fake-orders/maintenance/run`

**Description:** Run maintenance operations manually.

**Required Permissions:** `maintenance_operations`

**Request Body:**
```json
{
  "maintenance_type": "full",
  "config": {
    "older_than_days": 7,
    "processed_only": false,
    "max_orders_per_cleanup": 1000,
    "archive_before_delete": true,
    "cleanup_templates": false,
    "cleanup_logs": true,
    "log_retention_days": 30
  }
}
```

**Maintenance Types:**
- `cleanup`: Clean up old orders only
- `optimization`: Optimize storage and indexes only
- `archive`: Archive old orders only
- `full`: Run complete maintenance (default)

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Maintenance operation 'full' completed",
  "maintenance_type": "full",
  "stats": {
    "cleanup_stats": {
      "total_orders_analyzed": 100,
      "orders_deleted": 15,
      "orders_preserved": 5
    },
    "archive_stats": {
      "archived_count": 15,
      "archive_files_created": 3,
      "total_size_bytes": 1024000
    },
    "optimization_stats": {
      "files_optimized": 2,
      "space_saved_bytes": 50000,
      "index_rebuilt": true
    },
    "total_runtime_seconds": 12.5,
    "errors": [],
    "warnings": []
  },
  "executed_at": "2025-01-16T14:30:00Z"
}
```

### 8. Get Maintenance Status

**Endpoint:** `GET /api/fake-orders/maintenance/status`

**Description:** Get maintenance system status and statistics.

**Response (200 OK):**
```json
{
  "success": true,
  "maintenance_status": {
    "maintenance_enabled": true,
    "last_maintenance": {
      "last_run": "2025-01-16T02:00:00Z",
      "runtime_seconds": 15.2,
      "cleanup_stats": {
        "orders_deleted": 10
      }
    },
    "current_storage_stats": {
      "total_orders": 85,
      "processed_orders": 60,
      "storage_size_bytes": 2048000
    },
    "archive_info": {
      "archive_files_count": 5,
      "total_archive_size_bytes": 5120000
    },
    "next_recommended_cleanup": "2025-01-23T02:00:00Z"
  },
  "retrieved_at": "2025-01-16T14:30:00Z"
}
```

## Scheduler Operations

### 9. Get Scheduler Status

**Endpoint:** `GET /api/fake-orders/scheduler/status`

**Description:** Get status of scheduled maintenance tasks.

**Query Parameters:**
- `task_name` (string): Get status of specific task (optional)

**Response (200 OK):**
```json
{
  "success": true,
  "scheduler_status": {
    "scheduler_running": true,
    "total_tasks": 4,
    "enabled_tasks": 3,
    "tasks": {
      "daily_cleanup": {
        "enabled": true,
        "last_run": "2025-01-16T02:00:00Z",
        "error_count": 0,
        "last_success": true
      },
      "weekly_deep_cleanup": {
        "enabled": true,
        "last_run": "2025-01-14T03:00:00Z",
        "error_count": 0,
        "last_success": true
      },
      "storage_optimization": {
        "enabled": true,
        "last_run": "2025-01-16T08:00:00Z",
        "error_count": 0,
        "last_success": true
      },
      "archive_cleanup": {
        "enabled": false,
        "last_run": null,
        "error_count": 0,
        "last_success": null
      }
    }
  },
  "retrieved_at": "2025-01-16T14:30:00Z"
}
```

### 10. Run Scheduled Task

**Endpoint:** `POST /api/fake-orders/scheduler/task/{task_name}/run`

**Description:** Run a scheduled task immediately.

**Required Permissions:** `maintenance_operations`

**Response (200 OK):**
```json
{
  "success": true,
  "task_name": "daily_cleanup",
  "result": {
    "task_type": "daily_cleanup",
    "maintenance_stats": {
      "cleanup_stats": {
        "orders_deleted": 5
      }
    }
  },
  "runtime_seconds": 8.3,
  "executed_at": "2025-01-16T14:30:00Z"
}
```

### 11. Toggle Scheduled Task

**Endpoint:** `POST /api/fake-orders/scheduler/task/{task_name}/toggle`

**Description:** Enable or disable a scheduled task.

**Required Permissions:** `maintenance_operations`

**Request Body:**
```json
{
  "enable": true
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Task 'daily_cleanup' enabled successfully",
  "task_name": "daily_cleanup",
  "enabled": true,
  "updated_at": "2025-01-16T14:30:00Z"
}
```

## System Health

### 12. Health Check

**Endpoint:** `GET /api/fake-orders/health`

**Description:** Check system health and get basic statistics.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "service": "fake_order_system",
  "components": {
    "fake_order_generator": "operational",
    "product_template_engine": "operational",
    "order_data_factory": "operational",
    "maintenance_system": "operational",
    "scheduler": "operational"
  },
  "statistics": {
    "total_fake_orders": 85,
    "fake_orders_today": 12,
    "available_templates": 15,
    "last_cleanup": "2025-01-16T02:00:00Z"
  },
  "security_stats": {
    "fake_orders_created_today": 12,
    "security_validations_passed": 85,
    "security_warnings": 0
  },
  "timestamp": "2025-01-16T14:30:00Z"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error description",
  "error_code": "ERROR_CODE",
  "additional_info": {}
}
```

### Common Error Codes

- `MISSING_PAYLOAD`: JSON payload is required
- `MISSING_REQUIRED_FIELD`: Required field is missing
- `VALIDATION_FAILED`: Request validation failed
- `GENERATION_FAILED`: Order generation failed
- `CLEANUP_FAILED`: Cleanup operation failed
- `MAINTENANCE_FAILED`: Maintenance operation failed
- `UNAUTHORIZED`: Invalid or missing API key
- `PERMISSION_DENIED`: Insufficient permissions
- `RATE_LIMITED`: Too many requests

## Rate Limiting

API endpoints are rate-limited based on API key:
- Standard operations: 100 requests per minute
- Bulk operations: 10 requests per minute
- Maintenance operations: 5 requests per minute

## Security Considerations

1. **API Key Management**: Store API keys securely and rotate regularly
2. **Fake Order Markers**: All generated orders include security markers
3. **Audit Logging**: All operations are logged for security audit
4. **Permission System**: Operations require specific permissions
5. **Data Validation**: All inputs are validated for security

## Best Practices

1. **Use Batch Operations**: For multiple orders, use batch endpoints
2. **Regular Cleanup**: Schedule regular cleanup to manage storage
3. **Monitor Health**: Check health endpoint regularly
4. **Handle Errors**: Implement proper error handling
5. **Test Scenarios**: Use meaningful test scenario names
6. **Template Management**: Create reusable templates for common products

## SDK and Integration Examples

See the separate examples documentation for:
- Python SDK usage
- JavaScript/Node.js integration
- cURL command examples
- Postman collection
- Testing framework integration