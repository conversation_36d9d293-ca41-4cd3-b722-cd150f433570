"""
Order Redeem Service

Main service for handling the order redemption process including
order validation, account assignment, cooldown management, and status tracking.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from .base_service import BaseService
from .chatgpt_account_service import Chat<PERSON>TAccountService
from .order_redemption_service import OrderRedemptionService
from ..models.order_redemption import OrderRedemption, RedemptionStatus
from ..models.chatgpt_account import ChatGPTAccount
from ..models.utils import validate_username, validate_sku, ValidationError


class OrderRedeemService(BaseService):
    """
    Main service for order redemption process
    
    Orchestrates the complete redemption workflow:
    - Order validation
    - Account assignment
    - Cooldown management
    - Status tracking
    - Integration with other services
    """
    
    def __init__(self, config: Dict[str, Any], logger=None):
        super().__init__(config, logger)
        self.service_name = "OrderRedeemService"
        
        # Service dependencies
        self.account_service: Optional[ChatGPTAccountService] = None
        self.redemption_service: Optional[OrderRedemptionService] = None
        
        # Configuration
        self.auto_assign_accounts = self._get_config_value('redemption_config.auto_assign_accounts', True)
        self.require_email_verification = self._get_config_value('email_config.require_verification', True)
        self.default_cooldown_hours = self._get_config_value('cooldown_config.default_hours', 24)
        self.max_redemptions_per_user = self._get_config_value('security_config.max_redemptions_per_user', 5)
        self.enable_abuse_prevention = self._get_config_value('security_config.enable_abuse_prevention', True)

        # Shopee API integration
        self._shopee_available = False
        self._search_order = None
        self._get_order_status = None
        self._shopee_api = None
    
    def initialize(self) -> bool:
        """Initialize the Order Redeem Service"""
        try:
            self.logger.info(f"Initializing {self.service_name}...")
            
            # Validate configuration
            if not self._validate_service_config():
                return False
            
            # Initialize dependent services
            self.account_service = ChatGPTAccountService(self.config, self.logger)
            self.redemption_service = OrderRedemptionService(self.config, self.logger)
            
            if not self.account_service.initialize():
                self.logger.error("Failed to initialize ChatGPT Account Service")
                return False
            
            if not self.redemption_service.initialize():
                self.logger.error("Failed to initialize Order Redemption Service")
                return False

            # Initialize Shopee API integration
            self._initialize_shopee_integration()

            self._mark_initialized()
            return True
            
        except Exception as e:
            self._handle_service_error("initialize", e)
            return False
    
    def shutdown(self) -> bool:
        """Shutdown the Order Redeem Service"""
        try:
            self.logger.info(f"Shutting down {self.service_name}...")
            
            # Shutdown dependent services
            if self.account_service:
                self.account_service.shutdown()
            
            if self.redemption_service:
                self.redemption_service.shutdown()
            
            self._mark_shutdown()
            return True
            
        except Exception as e:
            self._handle_service_error("shutdown", e)
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        self._update_health_check_time()
        
        try:
            health_data = {
                'status': 'healthy',
                'service': self.service_name,
                'timestamp': datetime.now().isoformat()
            }
            
            # Check dependent services
            services_health = {}
            issues = []
            
            if self.account_service:
                account_health = self.account_service.health_check()
                services_health['account_service'] = account_health
                if account_health.get('status') != 'healthy':
                    issues.append("Account service unhealthy")
            else:
                issues.append("Account service not initialized")
            
            if self.redemption_service:
                redemption_health = self.redemption_service.health_check()
                services_health['redemption_service'] = redemption_health
                if redemption_health.get('status') != 'healthy':
                    issues.append("Redemption service unhealthy")
            else:
                issues.append("Redemption service not initialized")
            
            health_data['services'] = services_health
            
            if issues:
                health_data['status'] = 'degraded'
                health_data['issues'] = issues
            
            return health_data
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': self.service_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def process_order_redemption(self, order_id: str, buyer_username: str, 
                               sku: str, var_sku: str) -> Dict[str, Any]:
        """
        Process a complete order redemption
        
        Args:
            order_id: Shopee order ID
            buyer_username: Buyer's username
            sku: Product SKU
            var_sku: Product variant SKU
            
        Returns:
            Dictionary with redemption result and details
        """
        try:
            self._log_operation("process_order_redemption", {
                'order_id': order_id,
                'buyer_username': buyer_username,
                'sku': sku,
                'var_sku': var_sku
            })
            
            # Step 1: Validate order and user
            validation_result = self._validate_redemption_request(
                order_id, buyer_username, sku, var_sku
            )
            
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error'],
                    'step': 'validation'
                }
            
            # Step 2: Check user cooldown status
            cooldown_status = self.redemption_service.get_user_cooldown_status(buyer_username)
            
            if cooldown_status['has_cooldown']:
                return {
                    'success': False,
                    'error': 'User is in cooldown period',
                    'cooldown_info': cooldown_status,
                    'step': 'cooldown_check'
                }
            
            # Step 3: Create redemption record
            redemption = self.redemption_service.create_redemption_from_order(
                order_id, buyer_username, sku, var_sku
            )
            
            if not redemption:
                return {
                    'success': False,
                    'error': 'Failed to create redemption record',
                    'step': 'redemption_creation'
                }
            
            # Step 4: Find and assign account (if auto-assignment enabled)
            if self.auto_assign_accounts:
                assignment_result = self._assign_account_to_redemption(redemption)
                
                if assignment_result['success']:
                    return {
                        'success': True,
                        'redemption_id': redemption.redemption_id,
                        'account_assigned': True,
                        'account_info': assignment_result['account_info'],
                        'requires_verification': self.require_email_verification,
                        'step': 'completed'
                    }
                else:
                    # No account available - set to pending
                    return {
                        'success': True,
                        'redemption_id': redemption.redemption_id,
                        'account_assigned': False,
                        'message': 'Redemption created, waiting for account availability',
                        'step': 'pending_assignment'
                    }
            else:
                # Manual assignment required
                return {
                    'success': True,
                    'redemption_id': redemption.redemption_id,
                    'account_assigned': False,
                    'message': 'Redemption created, manual account assignment required',
                    'step': 'manual_assignment_required'
                }
            
        except Exception as e:
            self._handle_service_error("process_order_redemption", e)
            return {
                'success': False,
                'error': f'Internal error: {str(e)}',
                'step': 'error'
            }
    
    def assign_account_to_redemption(self, redemption_id: str, account_id: str = None) -> Dict[str, Any]:
        """
        Assign a ChatGPT account to a redemption
        
        Args:
            redemption_id: Redemption ID
            account_id: Specific account ID (optional, auto-selects if not provided)
            
        Returns:
            Dictionary with assignment result
        """
        try:
            redemption = self.redemption_service.get_redemption(redemption_id)
            if not redemption:
                return {
                    'success': False,
                    'error': 'Redemption not found'
                }
            
            if not redemption.can_redeem():
                return {
                    'success': False,
                    'error': f'Redemption cannot be processed (status: {redemption.status.value})'
                }
            
            # Find account
            if account_id:
                account = self.account_service.get_account(account_id)
                if not account:
                    return {
                        'success': False,
                        'error': 'Specified account not found'
                    }
                
                if not account.can_assign_user():
                    return {
                        'success': False,
                        'error': 'Account cannot accept new users'
                    }
            else:
                # Auto-select best account
                account = self.account_service.find_best_account_for_assignment(redemption.var_sku)
                if not account:
                    return {
                        'success': False,
                        'error': 'No available accounts found'
                    }
            
            # Assign user to account
            if not self.account_service.assign_user_to_account(account.account_id):
                return {
                    'success': False,
                    'error': 'Failed to assign user to account'
                }
            
            # Activate redemption
            if not self.redemption_service.activate_redemption(redemption_id, account.account_id):
                # Rollback account assignment
                self.account_service.release_user_from_account(account.account_id)
                return {
                    'success': False,
                    'error': 'Failed to activate redemption'
                }
            
            self._log_operation("assign_account", {
                'redemption_id': redemption_id,
                'account_id': account.account_id,
                'buyer_username': redemption.buyer_username
            })
            
            return {
                'success': True,
                'account_info': {
                    'account_id': account.account_id,
                    'email': account.email,
                    'password': account.password,
                    'expiration_date': account.expiration_date,
                    'current_users': account.current_users,
                    'max_users': account.max_concurrent_users
                },
                'requires_verification': self.require_email_verification
            }
            
        except Exception as e:
            self._handle_service_error("assign_account_to_redemption", e)
            return {
                'success': False,
                'error': f'Internal error: {str(e)}'
            }
    
    def release_redemption(self, redemption_id: str, reason: str = "") -> Dict[str, Any]:
        """
        Release a redemption and free up the assigned account
        
        Args:
            redemption_id: Redemption ID
            reason: Reason for release
            
        Returns:
            Dictionary with release result
        """
        try:
            redemption = self.redemption_service.get_redemption(redemption_id)
            if not redemption:
                return {
                    'success': False,
                    'error': 'Redemption not found'
                }
            
            # Release user from account if assigned
            if redemption.assigned_account_id:
                self.account_service.release_user_from_account(redemption.assigned_account_id)
            
            # Set cooldown
            cooldown_hours = self.default_cooldown_hours
            self.redemption_service.set_redemption_cooldown(redemption_id, cooldown_hours)
            
            self._log_operation("release_redemption", {
                'redemption_id': redemption_id,
                'account_id': redemption.assigned_account_id,
                'reason': reason,
                'cooldown_hours': cooldown_hours
            })
            
            return {
                'success': True,
                'cooldown_hours': cooldown_hours,
                'message': f'Redemption released, cooldown set for {cooldown_hours} hours'
            }
            
        except Exception as e:
            self._handle_service_error("release_redemption", e)
            return {
                'success': False,
                'error': f'Internal error: {str(e)}'
            }
    
    def get_redemption_status(self, redemption_id: str) -> Dict[str, Any]:
        """
        Get detailed redemption status
        
        Args:
            redemption_id: Redemption ID
            
        Returns:
            Dictionary with redemption status details
        """
        try:
            redemption = self.redemption_service.get_redemption(redemption_id)
            if not redemption:
                return {
                    'found': False,
                    'error': 'Redemption not found'
                }
            
            status_info = {
                'found': True,
                'redemption_id': redemption.redemption_id,
                'order_id': redemption.order_id,
                'buyer_username': redemption.buyer_username,
                'status': redemption.status.value,
                'created_at': redemption.created_at,
                'can_redeem': redemption.can_redeem(),
                'can_access': redemption.can_access()
            }
            
            # Add account info if assigned
            if redemption.assigned_account_id:
                account = self.account_service.get_account(redemption.assigned_account_id)
                if account:
                    status_info['account_info'] = {
                        'email': account.email,
                        'password': account.password,
                        'expiration_date': account.expiration_date,
                        'is_expired': account.is_expired()
                    }
            
            # Add cooldown info if in cooldown
            if redemption.is_in_cooldown():
                remaining_time = redemption.get_cooldown_remaining_time()
                status_info['cooldown_info'] = {
                    'remaining_seconds': int(remaining_time.total_seconds()),
                    'remaining_hours': remaining_time.total_seconds() / 3600,
                    'cooldown_until': redemption.cooldown_until
                }
            
            # Add error info if in error state
            if redemption.status == RedemptionStatus.ERROR:
                status_info['error_info'] = {
                    'error_message': redemption.error_message,
                    'retry_count': redemption.retry_count
                }
            
            return status_info
            
        except Exception as e:
            self._handle_service_error("get_redemption_status", e)
            return {
                'found': False,
                'error': f'Internal error: {str(e)}'
            }
    
    def get_user_redemption_summary(self, buyer_username: str) -> Dict[str, Any]:
        """
        Get redemption summary for a user
        
        Args:
            buyer_username: Buyer's username
            
        Returns:
            Dictionary with user's redemption summary
        """
        try:
            user_redemptions = self.redemption_service.get_redemptions_by_user(buyer_username)
            cooldown_status = self.redemption_service.get_user_cooldown_status(buyer_username)
            
            # Count redemptions by status
            status_counts = {}
            for status in RedemptionStatus:
                count = sum(1 for r in user_redemptions if r.status == status)
                status_counts[status.value] = count
            
            # Find active redemptions
            active_redemptions = [r for r in user_redemptions if r.status == RedemptionStatus.ACTIVE]
            
            return {
                'buyer_username': buyer_username,
                'total_redemptions': len(user_redemptions),
                'status_counts': status_counts,
                'active_redemptions': len(active_redemptions),
                'cooldown_status': cooldown_status,
                'can_redeem_new': not cooldown_status['has_cooldown'] and len(user_redemptions) < self.max_redemptions_per_user,
                'active_redemption_details': [
                    {
                        'redemption_id': r.redemption_id,
                        'order_id': r.order_id,
                        'created_at': r.created_at,
                        'assigned_account_id': r.assigned_account_id
                    }
                    for r in active_redemptions
                ]
            }
            
        except Exception as e:
            self._handle_service_error("get_user_redemption_summary", e)
            return {
                'buyer_username': buyer_username,
                'error': f'Failed to get summary: {str(e)}'
            }
    
    def _validate_redemption_request(self, order_id: str, buyer_username: str, 
                                   sku: str, var_sku: str) -> Dict[str, Any]:
        """
        Validate redemption request
        
        Args:
            order_id: Order ID
            buyer_username: Buyer's username
            sku: Product SKU
            var_sku: Product variant SKU
            
        Returns:
            Dictionary with validation result
        """
        try:
            # Basic validation
            if not validate_username(buyer_username):
                return {
                    'valid': False,
                    'error': 'Invalid username format'
                }
            
            if not validate_sku(sku):
                return {
                    'valid': False,
                    'error': 'Invalid SKU format'
                }

            # Validate order with Shopee API
            shopee_validation = self._validate_order_with_shopee(order_id, buyer_username)
            if not shopee_validation['valid']:
                return shopee_validation

            # Check for duplicate order
            existing_redemption = self.redemption_service.get_redemption_by_order_id(order_id)
            if existing_redemption:
                return {
                    'valid': False,
                    'error': 'Order has already been redeemed',
                    'existing_redemption_id': existing_redemption.redemption_id
                }
            
            # Check user redemption limits
            if self.enable_abuse_prevention:
                user_redemptions = self.redemption_service.get_redemptions_by_user(buyer_username)
                if len(user_redemptions) >= self.max_redemptions_per_user:
                    return {
                        'valid': False,
                        'error': f'User has reached maximum redemption limit ({self.max_redemptions_per_user})'
                    }
            
            return {
                'valid': True
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'Validation error: {str(e)}'
            }
    
    def _assign_account_to_redemption(self, redemption: OrderRedemption) -> Dict[str, Any]:
        """
        Internal method to assign account to redemption
        
        Args:
            redemption: OrderRedemption object
            
        Returns:
            Dictionary with assignment result
        """
        try:
            # Find best available account
            account = self.account_service.find_best_account_for_assignment(redemption.var_sku)
            
            if not account:
                return {
                    'success': False,
                    'error': 'No available accounts found'
                }
            
            # Assign user to account
            if not self.account_service.assign_user_to_account(account.account_id):
                return {
                    'success': False,
                    'error': 'Failed to assign user to account'
                }
            
            # Activate redemption
            if not self.redemption_service.activate_redemption(redemption.redemption_id, account.account_id):
                # Rollback account assignment
                self.account_service.release_user_from_account(account.account_id)
                return {
                    'success': False,
                    'error': 'Failed to activate redemption'
                }
            
            return {
                'success': True,
                'account_info': {
                    'account_id': account.account_id,
                    'email': account.email,
                    'password': account.password,
                    'expiration_date': account.expiration_date
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Assignment error: {str(e)}'
            }
    
    def _validate_service_config(self) -> bool:
        """Validate service configuration"""
        # No required configuration keys for basic functionality
        return True

    def _initialize_shopee_integration(self) -> None:
        """Initialize Shopee API integration"""
        try:
            # Import existing order service
            import sys
            import os

            # Add services directory to path
            services_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'services')
            if services_path not in sys.path:
                sys.path.append(services_path)

            # Import order service functions
            from order_service import search_order, get_order_status
            from shopee_api_client import ShopeeAPIClient

            # Store references to order service functions
            self._search_order = search_order
            self._get_order_status = get_order_status

            # Initialize ShopeeAPI client for direct API calls if needed
            self._shopee_api = ShopeeAPIClient()
            self._shopee_available = True

            self.logger.info("Shopee API integration initialized successfully")

        except ImportError as e:
            self.logger.warning(f"Shopee API integration not available: {e}")
            self._shopee_available = False
            self._search_order = None
            self._get_order_status = None
            self._shopee_api = None
        except Exception as e:
            self.logger.error(f"Failed to initialize Shopee API integration: {e}")
            self._shopee_available = False
            self._search_order = None
            self._get_order_status = None
            self._shopee_api = None

    def _validate_order_with_shopee(self, order_id: str, buyer_username: str) -> Dict[str, Any]:
        """
        Validate order with Shopee API

        Args:
            order_id: Order ID to validate
            buyer_username: Expected buyer username

        Returns:
            Dictionary with validation result
        """
        try:
            if not self._shopee_available:
                # If Shopee API is not available, skip validation
                self.logger.warning("Shopee API not available - skipping order validation")
                return {'valid': True, 'warning': 'Shopee API validation skipped'}

            # Search for the order
            order_data = self._search_order(order_id)

            if not order_data or 'data' not in order_data:
                return {
                    'valid': False,
                    'error': 'Order not found in Shopee system'
                }

            card_list = order_data['data'].get('card_list', [])
            if not card_list:
                return {
                    'valid': False,
                    'error': 'Order not found in Shopee system'
                }

            order = card_list[0]

            # Extract order information
            order_info = self._extract_order_info(order)

            # Validate order status
            if order_info['status'] not in ['Completed', 'Shipped']:
                return {
                    'valid': False,
                    'error': f'Order status is {order_info["status"]}. Only completed or shipped orders can be redeemed.'
                }

            # Validate buyer username if available
            if order_info.get('buyer_username') and order_info['buyer_username'] != buyer_username:
                return {
                    'valid': False,
                    'error': 'Order buyer username does not match provided username'
                }

            return {
                'valid': True,
                'order_info': order_info
            }

        except Exception as e:
            self.logger.error(f"Error validating order with Shopee API: {e}")
            # Don't fail validation if API error occurs
            return {'valid': True, 'warning': f'Shopee API validation failed: {str(e)}'}

    def _extract_order_info(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract order information from Shopee API response

        Args:
            order: Order data from Shopee API

        Returns:
            Dictionary with extracted order information
        """
        try:
            order_info = {}

            # Handle different order structures
            if 'package_level_order_card' in order:
                package = order['package_level_order_card']['package_list'][0]
                order_info['status'] = package['status_info']['status']
                order_info['order_sn'] = order['package_level_order_card']['card_header']['order_sn']
                order_info['buyer_username'] = order['package_level_order_card']['card_header'].get('buyer_username')
            elif 'order_card' in order:
                if 'return_id' in order['order_card']['order_ext_info']:
                    order_info['status'] = "Refunded"
                else:
                    order_info['status'] = order['order_card']['status_info']['status']
                order_info['order_sn'] = order['order_card']['card_header']['order_sn']
                order_info['buyer_username'] = order['order_card']['card_header'].get('buyer_username')
            else:
                order_info['status'] = 'Unknown'
                order_info['order_sn'] = 'Unknown'
                order_info['buyer_username'] = None

            return order_info

        except Exception as e:
            self.logger.error(f"Error extracting order info: {e}")
            return {
                'status': 'Unknown',
                'order_sn': 'Unknown',
                'buyer_username': None
            }
