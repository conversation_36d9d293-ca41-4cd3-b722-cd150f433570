"""
Netflix Plugin Implementation
Handles Netflix sign-in codes, session management, and order processing
"""

import logging
from typing import Dict, Any, Optional
from flask import Blueprint
from core.plugin_manager import PluginInterface
from .services.netflix_signin_service import NetflixSigninService
from .services.netflix_session_service import NetflixSessionService
from .services.netflix_order_service import NetflixOrderService
from .routes.netflix_routes import create_netflix_blueprint

logger = logging.getLogger(__name__)

class Plugin(PluginInterface):
    """Netflix Plugin Main Class"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "netflix"
        self.version = "1.0.0"
        self.description = "Netflix integration for sign-in codes and session management"
        self.dependencies = []
        
        # Services
        self.signin_service = None
        self.session_service = None
        self.order_service = None
        self.blueprint = None
        
    def initialize(self) -> bool:
        """Initialize the Netflix plugin"""
        try:
            logger.info("Initializing Netflix plugin...")
            
            # Initialize services
            self.signin_service = NetflixSigninService(self.config)
            self.session_service = NetflixSessionService(self.config)
            self.order_service = NetflixOrderService(self.config)
            
            # Create blueprint with services
            self.blueprint = create_netflix_blueprint(
                self.signin_service,
                self.session_service,
                self.order_service
            )
            
            logger.info("Netflix plugin initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Netflix plugin: {e}")
            return False
            
    def shutdown(self) -> bool:
        """Shutdown the Netflix plugin"""
        try:
            logger.info("Shutting down Netflix plugin...")
            
            # Cleanup services
            if self.signin_service:
                self.signin_service.cleanup()
            if self.session_service:
                self.session_service.cleanup()
            if self.order_service:
                self.order_service.cleanup()
                
            logger.info("Netflix plugin shutdown successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error shutting down Netflix plugin: {e}")
            return False
            
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return Flask blueprint for Netflix routes"""
        return self.blueprint
        
    def get_config_schema(self) -> Dict[str, Any]:
        """Return configuration schema for Netflix plugin"""
        return {
            "type": "object",
            "properties": {
                "enabled": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable/disable Netflix plugin"
                },
                "accounts": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "email": {"type": "string"},
                            "password": {"type": "string"},
                            "name": {"type": "string"},
                            "enabled": {"type": "boolean", "default": True}
                        },
                        "required": ["email", "password", "name"]
                    },
                    "description": "Netflix account credentials"
                },
                "session_config": {
                    "type": "object",
                    "properties": {
                        "cooldown_time": {
                            "type": "integer",
                            "default": 600,
                            "description": "Cooldown time between requests in seconds"
                        },
                        "request_timeout": {
                            "type": "integer",
                            "default": 30,
                            "description": "Request timeout in seconds"
                        },
                        "max_concurrent_requests": {
                            "type": "integer",
                            "default": 3,
                            "description": "Maximum concurrent requests"
                        }
                    }
                },
                "order_config": {
                    "type": "object",
                    "properties": {
                        "auto_ship": {
                            "type": "boolean",
                            "default": True,
                            "description": "Automatically ship orders after processing"
                        },
                        "chat_messages": {
                            "type": "object",
                            "properties": {
                                "signin_code_success": {
                                    "type": "string",
                                    "default": "Netflix sign-in code: {code} for account: {email}",
                                    "description": "Message template for successful sign-in code"
                                },
                                "signin_code_failure": {
                                    "type": "string",
                                    "default": "Failed to retrieve Netflix sign-in code. Please contact support.",
                                    "description": "Message template for failed sign-in code"
                                }
                            }
                        }
                    }
                },
                "signin_config": {
                    "type": "object",
                    "properties": {
                        "base_url": {
                            "type": "string",
                            "default": "https://www.netflix.com",
                            "description": "Netflix base URL"
                        },
                        "user_agent": {
                            "type": "string",
                            "default": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                            "description": "User agent for requests"
                        },
                        "retry_attempts": {
                            "type": "integer",
                            "default": 3,
                            "description": "Number of retry attempts for failed requests"
                        }
                    }
                }
            },
            "required": ["enabled", "accounts"]
        }
        
    def get_status(self) -> Dict[str, Any]:
        """Return Netflix plugin status"""
        status = super().get_status()
        
        if hasattr(self, '_initialized') and self._initialized:
            status.update({
                'services': {
                    'signin_service': self.signin_service is not None,
                    'session_service': self.session_service is not None,
                    'order_service': self.order_service is not None
                },
                'netflix_accounts': len(self.config.get('accounts', [])),
                'active_sessions': len(self.session_service.get_all_sessions()) if self.session_service else 0
            })
            
        return status
        
    def load_config(self, config: Dict[str, Any]):
        """Load Netflix plugin configuration"""
        super().load_config(config)
        
        # Update services with new config
        if hasattr(self, '_initialized') and self._initialized:
            if self.signin_service:
                self.signin_service.update_config(config)
            if self.session_service:
                self.session_service.update_config(config)
            if self.order_service:
                self.order_service.update_config(config)
