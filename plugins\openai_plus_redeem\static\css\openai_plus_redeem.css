/**
 * OpenAI Plus Redeem Plugin Styles
 * 
 * Responsive design with consistent styling for the main application
 * and user-friendly interface elements.
 */

/* ========== CSS Variables ========== */
:root {
    /* Primary Colors */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #a5b4fc;
    
    /* Secondary Colors */
    --secondary-color: #764ba2;
    --secondary-dark: #6b46c1;
    --secondary-light: #c4b5fd;
    
    /* Status Colors */
    --success-color: #10b981;
    --success-light: #d1fae5;
    --warning-color: #f59e0b;
    --warning-light: #fef3c7;
    --error-color: #ef4444;
    --error-light: #fee2e2;
    --info-color: #3b82f6;
    --info-light: #dbeafe;
    
    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ========== Base Styles ========== */
.openai-plus-redeem {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
}

/* ========== Layout Components ========== */
.opr-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.opr-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.opr-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.opr-card-header {
    border-bottom: 1px solid var(--gray-200);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.opr-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.opr-card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: var(--spacing-xs) 0 0 0;
}

/* ========== Form Elements ========== */
.opr-form-group {
    margin-bottom: var(--spacing-lg);
}

.opr-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xs);
}

.opr-label.required::after {
    content: ' *';
    color: var(--error-color);
}

.opr-input,
.opr-select,
.opr-textarea {
    width: 100%;
    padding: 0.75rem var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background-color: white;
}

.opr-input:focus,
.opr-select:focus,
.opr-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.opr-input:disabled,
.opr-select:disabled,
.opr-textarea:disabled {
    background-color: var(--gray-50);
    color: var(--gray-500);
    cursor: not-allowed;
}

.opr-input.error,
.opr-select.error,
.opr-textarea.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* ========== Buttons ========== */
.opr-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 2.5rem;
}

.opr-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.opr-btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.opr-btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.opr-btn-success {
    background-color: var(--success-color);
    color: white;
}

.opr-btn-success:hover:not(:disabled) {
    background-color: #059669;
    transform: translateY(-1px);
}

.opr-btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.opr-btn-warning:hover:not(:disabled) {
    background-color: #d97706;
    transform: translateY(-1px);
}

.opr-btn-danger {
    background-color: var(--error-color);
    color: white;
}

.opr-btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
    transform: translateY(-1px);
}

.opr-btn-secondary {
    background-color: var(--gray-500);
    color: white;
}

.opr-btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-600);
    transform: translateY(-1px);
}

.opr-btn-outline {
    background-color: transparent;
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
}

.opr-btn-outline:hover:not(:disabled) {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.opr-btn-sm {
    padding: 0.5rem var(--spacing-md);
    font-size: 0.75rem;
    min-height: 2rem;
}

.opr-btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    min-height: 3rem;
}

/* ========== Status Badges ========== */
.opr-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.opr-badge-success {
    background-color: var(--success-light);
    color: #065f46;
}

.opr-badge-warning {
    background-color: var(--warning-light);
    color: #92400e;
}

.opr-badge-error {
    background-color: var(--error-light);
    color: #991b1b;
}

.opr-badge-info {
    background-color: var(--info-light);
    color: #1e40af;
}

.opr-badge-gray {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

/* ========== Progress Indicators ========== */
.opr-progress {
    width: 100%;
    height: 0.5rem;
    background-color: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.opr-progress-bar {
    height: 100%;
    transition: width var(--transition-normal);
    border-radius: var(--radius-sm);
}

.opr-progress-success .opr-progress-bar {
    background-color: var(--success-color);
}

.opr-progress-warning .opr-progress-bar {
    background-color: var(--warning-color);
}

.opr-progress-error .opr-progress-bar {
    background-color: var(--error-color);
}

.opr-progress-gradient .opr-progress-bar {
    background: linear-gradient(90deg, var(--error-color) 0%, var(--warning-color) 50%, var(--success-color) 100%);
}

/* ========== Loading Spinner ========== */
.opr-spinner {
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    animation: opr-spin 1s linear infinite;
}

.opr-spinner-sm {
    width: 1rem;
    height: 1rem;
    border-width: 2px;
}

.opr-spinner-lg {
    width: 3rem;
    height: 3rem;
    border-width: 4px;
}

@keyframes opr-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========== Step Indicator ========== */
.opr-steps {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-xl);
}

.opr-step {
    display: flex;
    align-items: center;
    padding: 0.5rem var(--spacing-md);
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0 var(--spacing-sm);
    transition: all var(--transition-normal);
    background-color: var(--gray-200);
    color: var(--gray-600);
}

.opr-step.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.opr-step.completed {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
}

.opr-step-number {
    margin-right: var(--spacing-xs);
}

/* ========== Alerts and Notifications ========== */
.opr-alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border-left: 4px solid;
    margin-bottom: var(--spacing-md);
}

.opr-alert-success {
    background-color: var(--success-light);
    border-color: var(--success-color);
    color: #065f46;
}

.opr-alert-warning {
    background-color: var(--warning-light);
    border-color: var(--warning-color);
    color: #92400e;
}

.opr-alert-error {
    background-color: var(--error-light);
    border-color: var(--error-color);
    color: #991b1b;
}

.opr-alert-info {
    background-color: var(--info-light);
    border-color: var(--info-color);
    color: #1e40af;
}

/* ========== Tables ========== */
.opr-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.opr-table th {
    background-color: var(--gray-50);
    padding: 0.75rem var(--spacing-md);
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.opr-table td {
    padding: 0.75rem var(--spacing-md);
    border-top: 1px solid var(--gray-200);
}

.opr-table tbody tr:hover {
    background-color: var(--gray-50);
}

/* ========== Statistics Cards ========== */
.opr-stat-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.opr-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.opr-stat-header {
    padding: var(--spacing-md);
    color: white;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.opr-stat-header.accounts {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
}

.opr-stat-header.redemptions {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
}

.opr-stat-header.cooldowns {
    background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
}

.opr-stat-header.services {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.opr-stat-body {
    padding: var(--spacing-md);
}

.opr-stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
}

.opr-stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-top: var(--spacing-xs);
}

/* ========== Modals ========== */
.opr-modal-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    z-index: 50;
}

.opr-modal {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.opr-modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: between;
}

.opr-modal-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    flex: 1;
}

.opr-modal-close {
    background: none;
    border: none;
    color: var(--gray-400);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
}

.opr-modal-close:hover {
    color: var(--gray-600);
}

.opr-modal-body {
    padding: var(--spacing-lg);
}

.opr-modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* ========== Responsive Design ========== */
@media (max-width: 768px) {
    .opr-container {
        padding: 0 var(--spacing-sm);
    }
    
    .opr-card {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .opr-steps {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .opr-step {
        margin: 0;
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
    
    .opr-modal {
        margin: var(--spacing-sm);
        max-width: calc(100% - 2rem);
    }
    
    .opr-modal-footer {
        flex-direction: column;
    }
    
    .opr-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .opr-stat-value {
        font-size: 1.5rem;
    }
    
    .opr-card-title {
        font-size: 1.125rem;
    }
    
    .opr-table {
        font-size: 0.75rem;
    }
    
    .opr-table th,
    .opr-table td {
        padding: 0.5rem var(--spacing-sm);
    }
}

/* ========== Animations ========== */
.opr-fade-in {
    animation: opr-fadeIn var(--transition-slow) ease-in;
}

@keyframes opr-fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.opr-slide-in {
    animation: opr-slideIn var(--transition-normal) ease-out;
}

@keyframes opr-slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* ========== Utility Classes ========== */
.opr-text-center { text-align: center; }
.opr-text-left { text-align: left; }
.opr-text-right { text-align: right; }

.opr-hidden { display: none !important; }
.opr-visible { display: block !important; }

.opr-flex { display: flex; }
.opr-flex-col { flex-direction: column; }
.opr-items-center { align-items: center; }
.opr-justify-center { justify-content: center; }
.opr-justify-between { justify-content: space-between; }

.opr-gap-sm { gap: var(--spacing-sm); }
.opr-gap-md { gap: var(--spacing-md); }
.opr-gap-lg { gap: var(--spacing-lg); }

.opr-mb-sm { margin-bottom: var(--spacing-sm); }
.opr-mb-md { margin-bottom: var(--spacing-md); }
.opr-mb-lg { margin-bottom: var(--spacing-lg); }
.opr-mb-xl { margin-bottom: var(--spacing-xl); }

.opr-mt-sm { margin-top: var(--spacing-sm); }
.opr-mt-md { margin-top: var(--spacing-md); }
.opr-mt-lg { margin-top: var(--spacing-lg); }
.opr-mt-xl { margin-top: var(--spacing-xl); }

.opr-p-sm { padding: var(--spacing-sm); }
.opr-p-md { padding: var(--spacing-md); }
.opr-p-lg { padding: var(--spacing-lg); }
.opr-p-xl { padding: var(--spacing-xl); }

/* ========== Accessibility Features ========== */

/* Skip Navigation Link */
.opr-skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: 100;
    font-weight: 500;
}

.opr-skip-link:focus {
    top: 6px;
}

/* Focus Indicators */
.opr-focus-visible:focus-visible,
.opr-input:focus-visible,
.opr-select:focus-visible,
.opr-textarea:focus-visible,
.opr-btn:focus-visible,
button:focus-visible,
a:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --success-color: #006400;
        --warning-color: #ff8c00;
        --error-color: #8b0000;
        --gray-300: #000000;
        --gray-600: #ffffff;
        --gray-800: #000000;
    }

    .opr-card {
        border: 2px solid var(--gray-800);
    }

    .opr-btn {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .opr-spinner {
        animation: none;
        border-top-color: transparent;
    }
}

/* Screen Reader Only Content */
.opr-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Live Region for Screen Reader Announcements */
.opr-live-region {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Keyboard Navigation Indicators */
.opr-keyboard-nav .opr-btn:focus,
.opr-keyboard-nav button:focus,
.opr-keyboard-nav a:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

/* Error State Accessibility */
.opr-input[aria-invalid="true"],
.opr-select[aria-invalid="true"],
.opr-textarea[aria-invalid="true"] {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* Loading State Accessibility */
.opr-loading {
    position: relative;
}

.opr-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Progress Bar Accessibility */
.opr-progress[role="progressbar"] {
    position: relative;
}

.opr-progress[role="progressbar"]::after {
    content: attr(aria-valuenow) '%';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--gray-800);
}

/* Modal Accessibility */
.opr-modal-overlay[aria-hidden="true"] {
    display: none;
}

.opr-modal[role="dialog"] {
    position: relative;
}

/* Table Accessibility */
.opr-table th[scope="col"] {
    text-align: left;
}

.opr-table td[role="gridcell"] {
    position: relative;
}

/* Form Accessibility */
.opr-form-group {
    position: relative;
}

.opr-error-message {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
}

.opr-error-message::before {
    content: '⚠';
    margin-right: var(--spacing-xs);
    font-weight: bold;
}

/* Status Indicators Accessibility */
.opr-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.opr-status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.opr-status-success::before {
    background-color: var(--success-color);
}

.opr-status-warning::before {
    background-color: var(--warning-color);
}

.opr-status-error::before {
    background-color: var(--error-color);
}

.opr-status-info::before {
    background-color: var(--info-color);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-400: #9ca3af;
        --gray-500: #d1d5db;
        --gray-600: #e5e7eb;
        --gray-700: #f3f4f6;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }

    .opr-card {
        background-color: var(--gray-100);
        border: 1px solid var(--gray-200);
    }

    .opr-input,
    .opr-select,
    .opr-textarea {
        background-color: var(--gray-50);
        border-color: var(--gray-300);
        color: var(--gray-800);
    }

    .opr-modal {
        background-color: var(--gray-100);
    }

    .opr-skip-link {
        background: var(--gray-800);
        color: var(--gray-100);
    }
}
