# VPN Config Generator - Command Auto-Update System

## Overview

The VPN Config Generator plugin now features an **automatic command name update system** that ensures all sub-commands automatically update when you change the main command name through the interface.

## How It Works

### 1. Dynamic Command Structure
- **Main Command**: The base command (e.g., `#vv`)
- **Sub-Commands**: Automatically generated with suffixes:
  - `#vvlist` (list servers)
  - `#vvuser` (user configurations)
  - `#vvdel` (delete config)
  - `#vvrenew` (renew config)
  - `#vvtest` (test API)
  - `#vvservers` (list all servers)
  - `#vvhelp` (show help)

### 2. Automatic Updates
When you change the main command name from the interface:

1. **Old Command Cleanup**: All old commands are automatically unregistered
2. **Command Name Updates**: All sub-commands are updated to use the new prefix
3. **New Command Registration**: All commands are re-registered with the chat system

### 3. Configuration Files Auto-Updated
- `command_config.json` - Main command configuration
- `commands.json` - All command definitions with updated names

## Usage Instructions

### Method 1: Web Interface (Recommended)
1. Go to `/vpn-config-generator/command-management`
2. Change the "Command Name" field
3. Click "Update Configuration"
4. All sub-commands will automatically update

### Method 2: API Call
```bash
curl -X PUT http://localhost:5000/vpn-config-generator/api/command-config \
  -H "Content-Type: application/json" \
  -d '{"command_name": "newname", "enabled": true}'
```

### Method 3: Direct File Edit (Not Recommended)
If you manually edit `command_config.json`, you must trigger the update through the web interface to activate the auto-update system.

## Example: Changing from 'vv' to 'vpn'

**Before:**
- `#vv` - Generate config
- `#vvlist` - List servers
- `#vvuser` - User configs

**After Update:**
- `#vpn` - Generate config  
- `#vpnlist` - List servers
- `#vpnuser` - User configs

## Technical Details

### Key Components

1. **`services.py`**:
   - `update_command_config()` - Handles the main update logic
   - `_update_all_command_names()` - Updates all command names automatically

2. **`routes.py`**:
   - `/api/command-config` PUT endpoint
   - Handles old command unregistration and new command registration

3. **`plugin.py`**:
   - `_register_chat_commands()` - Registers commands with chat plugin
   - `_unregister_chat_commands()` - Cleans up old commands

### Error Handling
- **Graceful Failures**: If old commands don't exist, warnings are logged instead of errors
- **Defensive Unregistration**: New command names are defensively unregistered before registration
- **Automatic Recovery**: System recovers gracefully from partial updates

## Benefits

✅ **No Manual Work**: Change once, everything updates automatically  
✅ **No Orphaned Commands**: Old commands are properly cleaned up  
✅ **Consistent Naming**: All sub-commands always match the main prefix  
✅ **Error Prevention**: Reduces manual configuration errors  
✅ **User Friendly**: Simple interface for command name changes  

## Testing

Run the test script to verify the system:
```bash
cd plugins/vpn_config_generator
python3 test_command_name_change.py
```

## Troubleshooting

### Issue: Commands not updating
**Solution**: Use the web interface to trigger the update system

### Issue: Old commands still working
**Solution**: Restart the application to ensure command cleanup

### Issue: Registration warnings in logs
**Solution**: These are normal during command name changes - the system handles them gracefully

## Migration Notes

This system was implemented to fix the original issue where:
- Changing command names required manual JSON file editing
- Sub-commands had to be updated individually
- Old commands remained registered causing conflicts

The new system provides a seamless experience for command name management.