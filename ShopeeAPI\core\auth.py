"""
Authentication and credential management for Shopee API.
"""
import re
import json
import time
from datetime import datetime
from typing import Dict, List, Callable, Optional, Union, Any

try:
    from core.exceptions import AuthenticationError
    from services.email_service import EmailService
except ImportError:
    # Try relative imports first (most reliable within package)
    try:
        from .exceptions import AuthenticationError
        from ..services.email_service import EmailService
    except ImportError:
        try:
            from ShopeeAPI.core.exceptions import AuthenticationError
            from ShopeeAPI.services.email_service import EmailService
        except ImportError:
            try:
                # When running directly from the core directory
                from exceptions import AuthenticationError
                from ..services.email_service import EmailService
            except ImportError:
                # Last resort
                from exceptions import AuthenticationError
                from ..services.email_service import EmailService


class CredentialManager:
    """
    Manages authentication credentials for Shopee API.
    Includes methods to extract tokens from cookies and update credentials.
    Supports both string cookies and JSON cookies.
    """

    def __init__(self, authorization_code: str = '', cookie: Union[str, Dict[str, Any]] = '', email_config: Optional[Dict[str, Any]] = None):
        self.authorization_code = authorization_code
        self._cookie_json = None
        self._cookie_str = ''
        self.set_cookie(cookie)
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0"
        self._subscribers: List[Callable] = []
        
        # Email configuration for sending expiration notifications
        self.email_config = email_config or {}
        self.email_service = EmailService() if email_config else None
        
        # Track last email sent to prevent spam
        self._last_expiration_email_time: Optional[datetime] = None
        self._email_cooldown_minutes = 60  # Minimum 60 minutes between expiration emails

    def set_cookie(self, cookie: Union[str, Dict[str, Any], list]) -> None:
        """
        Set cookie value, handling both string and JSON formats.

        Args:
            cookie: Cookie string or JSON object containing cookies
        """
        if isinstance(cookie, (dict, list)):
            self._cookie_json = cookie
            # Convert JSON to string format for backward compatibility
            self._cookie_str = self._json_to_cookie_string(cookie)
        elif isinstance(cookie, str):
            self._cookie_str = cookie
            # Try to parse as JSON if it looks like JSON
            if cookie.strip() and (
                (cookie.strip().startswith('{') and cookie.strip().endswith('}')) or
                (cookie.strip().startswith('[') and cookie.strip().endswith(']'))
            ):
                try:
                    self._cookie_json = json.loads(cookie)
                except json.JSONDecodeError:
                    self._cookie_json = None
            else:
                self._cookie_json = None
        else:
            # Handle other types by converting to string
            self._cookie_str = str(cookie) if cookie else ''
            self._cookie_json = None

    def _json_to_cookie_string(self, cookie_json: Union[Dict[str, Any], list]) -> str:
        """
        Convert JSON cookie format to string format.

        Args:
            cookie_json: Dictionary or list of cookies

        Returns:
            Cookie string in format "name1=value1; name2=value2"
        """
        if not cookie_json:
            return ''

        # Handle array of cookie objects with name/value properties
        if isinstance(cookie_json, list):
            return '; '.join([f"{c.get('name', '')}={c.get('value', '')}" for c in cookie_json if 'name' in c and 'value' in c])

        # Handle dictionary with nested cookie objects
        if isinstance(cookie_json, dict):
            cookie_parts = []
            for name, value in cookie_json.items():
                if isinstance(value, dict) and 'value' in value:
                    # Handle nested structure like {"SPC_STK": {"value": "test", ...}}
                    cookie_parts.append(f"{name}={value['value']}")
                else:
                    # Handle simple structure like {"SPC_STK": "test"}
                    cookie_parts.append(f"{name}={value}")
            return '; '.join(cookie_parts)

        # Fallback for any other type
        return str(cookie_json)

    @property
    def cookie(self) -> str:
        """
        Get cookie string for backward compatibility.

        Returns:
            Cookie string
        """
        return self._cookie_str

    @cookie.setter
    def cookie(self, value: Union[str, Dict[str, Any]]) -> None:
        """
        Set cookie value, handling both string and JSON formats.

        Args:
            value: Cookie string or JSON object
        """
        self.set_cookie(value)

    def update_credentials(self, authorization_code: str, cookie: Union[str, Dict[str, Any]]) -> None:
        """
        Update authorization code and cookie, then notify subscribers.

        Args:
            authorization_code: Bearer token for authorization
            cookie: Cookie string or JSON object containing authentication tokens
        """
        self.authorization_code = authorization_code
        self.set_cookie(cookie)
        self._notify_subscribers()

    def get_credentials(self) -> Dict[str, str]:
        """
        Get current credentials.

        Returns:
            Dict containing authorization_code and cookie
        """
        return {
            'authorization_code': self.authorization_code,
            'cookie': self.cookie
        }

    def subscribe(self, callback: Callable) -> None:
        """
        Subscribe to credential updates.

        Args:
            callback: Function to call when credentials are updated
        """
        if callback not in self._subscribers:
            self._subscribers.append(callback)

    def unsubscribe(self, callback: Callable) -> None:
        """
        Unsubscribe from credential updates.

        Args:
            callback: Function to remove from subscribers
        """
        if callback in self._subscribers:
            self._subscribers.remove(callback)

    def _notify_subscribers(self) -> None:
        """Notify all subscribers of credential updates."""
        credentials = self.get_credentials()
        for subscriber in self._subscribers:
            try:
                subscriber(credentials)
            except Exception as e:
                print(f"Error notifying subscriber: {e}")

    def extract_cookie_value(self, cookie_name: str) -> Optional[str]:
        """
        Extract a specific cookie value from the cookie string or JSON.

        Args:
            cookie_name: Name of the cookie to extract

        Returns:
            Cookie value or None if not found
        """
        # First try to get from JSON if available
        if self._cookie_json:
            # Handle array of cookie objects
            if isinstance(self._cookie_json, list):
                for cookie in self._cookie_json:
                    if cookie.get('name') == cookie_name:
                        value = cookie.get('value')
                        return value
            # Handle direct dictionary access
            elif cookie_name in self._cookie_json:
                value = self._cookie_json[cookie_name]
                if isinstance(value, dict) and 'value' in value:
                    value = value['value']
                return value

        # Fall back to string extraction if JSON didn't have it or no JSON available
        if not self._cookie_str:
            print(f"Warning: Cannot extract {cookie_name} - cookie string is empty")
            return None

        try:
            pattern = f"{cookie_name}=([^;]+)"
            match = re.search(pattern, self._cookie_str)
            if match:
                value = match.group(1)
                # For debugging purposes, print a truncated version of the value
                truncated_value = value[:10] + "..." if len(value) > 10 else value
                print(f"Found {cookie_name} in string: {truncated_value}")
                return value

            print(f"Warning: {cookie_name} not found in cookie string or JSON")
            return None
        except Exception as e:
            print(f"Error extracting {cookie_name} from cookie: {str(e)}")
            return None

    def get_cookie_expiration(self, cookie_name: str = 'SPC_STK') -> Optional[float]:
        """
        Get expiration timestamp for a specific cookie.

        Args:
            cookie_name: Name of the cookie to check expiration for

        Returns:
            Expiration timestamp as float or None if not found
        """
        # First try to get from JSON if available
        if self._cookie_json:
            # Handle array of cookie objects with expiration
            if isinstance(self._cookie_json, list):
                for cookie in self._cookie_json:
                    if cookie.get('name') == cookie_name:
                        # Check for expirationDate in the cookie object
                        if 'expirationDate' in cookie:
                            return float(cookie['expirationDate'])
                        return None
            # Handle direct dictionary access with domain structure
            elif cookie_name in self._cookie_json and isinstance(self._cookie_json[cookie_name], dict):
                cookie_obj = self._cookie_json[cookie_name]
                if 'expirationDate' in cookie_obj:
                    return float(cookie_obj['expirationDate'])

        # No expiration info available in string format
        return None

    def get_spc_cds(self) -> str:
        """
        Extract SPC_CDS value from cookie.

        Returns:
            SPC_CDS value or empty string if not found
        """
        value = self.extract_cookie_value("SPC_CDS")
        if not value:
            print(f"Note: SPC_CDS not found in cookie. Using empty string instead.")
            return ""  # Return empty string instead of None
        return value

    def get_spc_cds_ver(self) -> str:
        """
        Extract SPC_CDS_VER value from cookie.

        Returns:
            SPC_CDS_VER value or empty string if not found
        """
        value = self.extract_cookie_value("SPC_CDS_VER")
        if not value:
            print(f"Note: SPC_CDS_VER not found in cookie. Using empty string instead.")
            return ""  # Return empty string instead of None
        return value

    def validate_credentials(self) -> bool:
        """
        Validate that all required credentials are present.

        Returns:
            True if all required credentials are present, False otherwise

        Raises:
            AuthenticationError: If credentials are invalid
        """
        try:
            # Check for authorization code
            if not self.authorization_code:
                print("Warning: Missing authorization code")
                return False

            # Check if authorization code starts with "Bearer "
            if not self.authorization_code.startswith("Bearer "):
                print("Warning: Authorization code must start with 'Bearer '")
                return False

            # Check if cookie is not empty (either string or JSON)
            if not self._cookie_str and not self._cookie_json:
                print("Warning: Cookie is empty (both string and JSON)")
                return False

            # Check for essential cookies
            essential_cookies = ["SPC_STK", "SPC_EC"]
            for cookie_name in essential_cookies:
                if not self.extract_cookie_value(cookie_name):
                    print(f"Warning: Essential cookie {cookie_name} is missing")
                    # Don't return False here, just warn - Shopee's requirements change frequently

            # Check if SPC_STK cookie is expired
            expiration = self.get_cookie_expiration("SPC_STK")
            if expiration and expiration < time.time():
                expiration_time = datetime.fromtimestamp(expiration)
                print(f"Warning: SPC_STK cookie has expired at {expiration_time}")
                
                # Send email notification if configured and cooldown period has passed
                self._send_expiration_email_if_needed(
                    reason="SPC_STK cookie has expired",
                    details=f"Cookie expired at: {expiration_time}\nCurrent time: {datetime.now()}\nPlease update your credentials in the config.json file."
                )
                
                return False

            return True
        except Exception as e:
            print(f"Error validating credentials: {str(e)}")
            return False

    def get_csrf_token(self) -> Optional[str]:
        """
        Extract CTOKEN value from cookie.

        Returns:
            CSRF token or None if not found
        """
        token = self.extract_cookie_value("CTOKEN")
        if not token:
            print("Warning: CSRF token (CTOKEN) not found in cookie")
        return token

    def get_spc_cds_chat(self) -> Optional[str]:
        """
        Extract SPC_CDS_CHAT value from cookie.

        Returns:
            SPC_CDS_CHAT value or None if not found
        """
        chat_token = self.extract_cookie_value("SPC_CDS_CHAT")
        if not chat_token:
            print("Warning: SPC_CDS_CHAT not found in cookie")
        return chat_token

    def _send_expiration_email_if_needed(self, reason: str, details: str = "") -> bool:
        """
        Send an email notification about credential expiration if configured and cooldown has passed.
        
        Args:
            reason: The reason for the expiration warning
            details: Additional details about the expiration
            
        Returns:
            bool: True if email was sent, False otherwise
        """
        # Check if email service is configured
        if not self.email_service or not self.email_config:
            return False
            
        # Check required email configuration
        required_fields = ['from_email', 'from_password', 'to_email']
        for field in required_fields:
            if field not in self.email_config:
                print(f"Warning: Email configuration missing field: {field}")
                return False
        
        # Check cooldown period to prevent spam
        current_time = datetime.now()
        if self._last_expiration_email_time:
            time_since_last = current_time - self._last_expiration_email_time
            if time_since_last.total_seconds() < (self._email_cooldown_minutes * 60):
                remaining_minutes = self._email_cooldown_minutes - int(time_since_last.total_seconds() / 60)
                print(f"Email cooldown active, {remaining_minutes} minutes remaining until next email")
                return False
        
        try:
            # Send the expiration email
            success = self.email_service.send_cookie_expiration_warning_email(
                from_email=self.email_config['from_email'],
                from_password=self.email_config['from_password'],
                to_email=self.email_config['to_email'],
                reason=reason,
                details=details
            )
            
            if success:
                self._last_expiration_email_time = current_time
                print(f"Credential expiration email sent successfully to {self.email_config['to_email']}")
            else:
                print("Failed to send credential expiration email")
                
            return success
            
        except Exception as e:
            print(f"Error sending credential expiration email: {e}")
            return False

    def set_email_config(self, email_config: Dict[str, Any]) -> None:
        """
        Set email configuration for sending expiration notifications.
        
        Args:
            email_config: Dictionary containing email settings
                         Must include: from_email, from_password, to_email
        """
        self.email_config = email_config
        if email_config:
            self.email_service = EmailService()
        else:
            self.email_service = None

    def get_re_policy(self) -> str:
        """
        Get the dfp_access_f token for re_policy.
        
        Returns:
            Fixed dfp_access_f token value
        """
        # Temporary fixed value as provided
        return "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||MTAwMDA="
