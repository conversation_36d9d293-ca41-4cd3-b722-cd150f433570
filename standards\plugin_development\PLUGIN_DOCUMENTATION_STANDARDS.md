# Plugin Documentation Standards

This document defines the documentation standards and requirements for all plugins in the SteamCodeTool system.

## 📚 Documentation Requirements

### 1. Mandatory Documentation Files

```
plugins/your_plugin/
├── README.md                       # Main plugin documentation (REQUIRED)
├── CHANGELOG.md                    # Version history (REQUIRED)
├── API.md                          # API documentation (REQUIRED if has API)
├── CONFIGURATION.md                # Configuration guide (REQUIRED)
├── TROUBLESHOOTING.md              # Common issues and solutions (RECOMMENDED)
├── DEVELOPMENT.md                  # Development setup guide (RECOMMENDED)
└── docs/                           # Additional documentation (OPTIONAL)
    ├── architecture.md
    ├── examples/
    └── images/
```

### 2. README.md Template

```markdown
# Plugin Name

Brief description of what the plugin does and its main purpose.

## 🚀 Features

- Feature 1: Description of feature 1
- Feature 2: Description of feature 2  
- Feature 3: Description of feature 3

## 📋 Requirements

- Python 3.8+
- Required dependencies:
  - dependency1>=1.0.0
  - dependency2>=2.0.0
- Optional dependencies:
  - optional_dep>=1.0.0 (for feature X)

## ⚙️ Installation

### Automatic Installation
1. Copy plugin to `plugins/your_plugin/` directory
2. Update `configs/core/plugin_config.json`:
   ```json
   {
     "your_plugin": {
       "enabled": true
     }
   }
   ```
3. Restart the application

### Manual Installation
1. Install dependencies: `pip install -r requirements.txt`
2. Configure plugin (see [Configuration](#configuration))
3. Enable plugin in plugin manager

## 🔧 Configuration

### Basic Configuration
```json
{
  "enabled": true,
  "debug": false,
  "service_config": {
    "timeout": 30,
    "retry_attempts": 3
  }
}
```

### Advanced Configuration
See [CONFIGURATION.md](CONFIGURATION.md) for detailed configuration options.

## 🌐 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/your-plugin/health` | Health check |
| GET | `/api/your-plugin/info` | Plugin information |
| POST | `/api/your-plugin/action` | Perform action |

See [API.md](API.md) for detailed API documentation.

## 📖 Usage Examples

### Basic Usage
```python
# Example code showing basic usage
from plugins.your_plugin.services import YourService

service = YourService(config)
result = service.perform_action()
```

### Advanced Usage
```python
# Example showing advanced features
service = YourService(config)
result = service.advanced_action(
    param1="value1",
    param2="value2"
)
```

## 🧪 Testing

Run tests for this plugin:
```bash
pytest plugins/your_plugin/tests/
```

Run with coverage:
```bash
pytest --cov=plugins/your_plugin plugins/your_plugin/tests/
```

## 🐛 Troubleshooting

### Common Issues

**Issue**: Plugin fails to initialize
- **Cause**: Missing configuration
- **Solution**: Check configuration file and ensure all required fields are present

**Issue**: API endpoints return 500 errors
- **Cause**: Service not properly initialized
- **Solution**: Check logs for initialization errors

See [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for more issues and solutions.

## 📝 Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and changes.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## 📄 License

This plugin is part of the SteamCodeTool project and follows the same license.

## 📞 Support

- Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
- Review existing issues in the project repository
- Contact the development team
```

### 3. API.md Template

```markdown
# Plugin API Documentation

This document describes the API endpoints provided by the Your Plugin.

## Base URL

All API endpoints are prefixed with `/api/your-plugin`

## Authentication

This plugin uses the same authentication as the main application.

## Endpoints

### Health Check

Check plugin health status.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "plugin": "your_plugin",
  "version": "1.0.0",
  "last_check": "2023-12-01T10:00:00Z"
}
```

**Status Codes:**
- `200`: Plugin is healthy
- `503`: Plugin is unhealthy

### Plugin Information

Get plugin information and capabilities.

**Endpoint:** `GET /info`

**Response:**
```json
{
  "name": "your_plugin",
  "version": "1.0.0",
  "description": "Plugin description",
  "features": ["feature1", "feature2"],
  "endpoints": ["/health", "/info", "/action"]
}
```

### Perform Action

Execute the main plugin action.

**Endpoint:** `POST /action`

**Request Body:**
```json
{
  "param1": "value1",
  "param2": "value2",
  "options": {
    "option1": true,
    "option2": "value"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "result": {
    "action_id": "12345",
    "message": "Action completed successfully"
  }
}
```

**Error Response:**
```json
{
  "status": "error",
  "error": "Error description",
  "code": "ERROR_CODE",
  "details": {
    "field": "error details"
  }
}
```

**Status Codes:**
- `200`: Success
- `400`: Bad request (validation error)
- `500`: Internal server error

## Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `INVALID_PARAM` | Invalid parameter value | Check parameter format |
| `SERVICE_UNAVAILABLE` | External service unavailable | Retry later |
| `CONFIGURATION_ERROR` | Plugin misconfigured | Check configuration |

## Rate Limiting

API endpoints are rate limited to 100 requests per minute per IP address.

## Examples

### cURL Examples

```bash
# Health check
curl -X GET http://localhost:5000/api/your-plugin/health

# Get plugin info
curl -X GET http://localhost:5000/api/your-plugin/info

# Perform action
curl -X POST http://localhost:5000/api/your-plugin/action \
  -H "Content-Type: application/json" \
  -d '{"param1": "value1", "param2": "value2"}'
```

### Python Examples

```python
import requests

# Health check
response = requests.get("http://localhost:5000/api/your-plugin/health")
print(response.json())

# Perform action
data = {"param1": "value1", "param2": "value2"}
response = requests.post("http://localhost:5000/api/your-plugin/action", json=data)
print(response.json())
```
```

### 4. CONFIGURATION.md Template

```markdown
# Configuration Guide

This document provides detailed configuration options for the Your Plugin.

## Configuration File Location

Plugin configuration is stored in:
- Main config: `configs/core/plugin_config.json`
- Plugin-specific: `configs/plugins/your_plugin/config.json`

## Configuration Schema

### Core Settings

```json
{
  "enabled": {
    "type": "boolean",
    "default": true,
    "description": "Enable/disable the plugin"
  },
  "debug": {
    "type": "boolean", 
    "default": false,
    "description": "Enable debug logging"
  }
}
```

### Service Configuration

```json
{
  "service_config": {
    "timeout": {
      "type": "integer",
      "default": 30,
      "minimum": 1,
      "maximum": 300,
      "description": "Service timeout in seconds"
    },
    "retry_attempts": {
      "type": "integer",
      "default": 3,
      "minimum": 0,
      "maximum": 10,
      "description": "Number of retry attempts"
    }
  }
}
```

### API Configuration

```json
{
  "api_config": {
    "base_url": {
      "type": "string",
      "format": "uri",
      "description": "Base URL for external API"
    },
    "api_key": {
      "type": "string",
      "description": "API key for authentication"
    }
  }
}
```

## Environment Variables

The plugin supports configuration via environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `YOUR_PLUGIN_API_KEY` | API key for external service | None |
| `YOUR_PLUGIN_DEBUG` | Enable debug mode | false |
| `YOUR_PLUGIN_TIMEOUT` | Service timeout | 30 |

## Configuration Examples

### Development Configuration

```json
{
  "enabled": true,
  "debug": true,
  "service_config": {
    "timeout": 10,
    "retry_attempts": 1
  },
  "api_config": {
    "base_url": "https://api-dev.example.com"
  }
}
```

### Production Configuration

```json
{
  "enabled": true,
  "debug": false,
  "service_config": {
    "timeout": 30,
    "retry_attempts": 3
  },
  "api_config": {
    "base_url": "https://api.example.com"
  }
}
```

## Configuration Validation

The plugin validates configuration on startup. Common validation errors:

- **Missing required fields**: Ensure all required configuration fields are present
- **Invalid types**: Check that values match expected types (string, integer, boolean)
- **Out of range values**: Ensure numeric values are within allowed ranges
- **Invalid URLs**: Verify URL format for API endpoints

## Dynamic Configuration Updates

The plugin supports runtime configuration updates through the admin interface or API.

To update configuration:
1. Modify configuration file
2. Call plugin reload API: `POST /api/your-plugin/reload`
3. Or restart the application
```

### 5. CHANGELOG.md Template

```markdown
# Changelog

All notable changes to this plugin will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New features that will be in the next release

### Changed
- Changes to existing functionality

### Deprecated
- Features that will be removed in future versions

### Removed
- Features removed in this version

### Fixed
- Bug fixes

### Security
- Security improvements

## [1.2.0] - 2023-12-01

### Added
- New API endpoint for bulk operations
- Support for configuration validation
- Health check improvements

### Changed
- Improved error handling in service layer
- Updated dependencies to latest versions

### Fixed
- Fixed memory leak in background tasks
- Resolved race condition in initialization

## [1.1.0] - 2023-11-15

### Added
- Configuration hot-reload support
- Enhanced logging with structured output
- API rate limiting

### Changed
- Refactored service architecture
- Improved test coverage to 95%

### Fixed
- Fixed timeout handling in external API calls
- Resolved configuration loading issues

## [1.0.0] - 2023-11-01

### Added
- Initial plugin implementation
- Core service functionality
- Basic API endpoints
- Configuration management
- Unit tests and documentation

[Unreleased]: https://github.com/project/plugin/compare/v1.2.0...HEAD
[1.2.0]: https://github.com/project/plugin/compare/v1.1.0...v1.2.0
[1.1.0]: https://github.com/project/plugin/compare/v1.0.0...v1.1.0
[1.0.0]: https://github.com/project/plugin/releases/tag/v1.0.0
```

## 📝 Code Documentation Standards

### 1. Docstring Standards

```python
def send_email(self, recipient: str, subject: str, body: str, attachments: Optional[List[str]] = None) -> bool:
    """Send an email to the specified recipient.
    
    This method sends an email using the configured SMTP server. It supports
    HTML and plain text content, as well as file attachments.
    
    Args:
        recipient: Email address of the recipient. Must be a valid email format.
        subject: Email subject line. Maximum length is 200 characters.
        body: Email body content. Can contain HTML or plain text.
        attachments: Optional list of file paths to attach to the email.
    
    Returns:
        True if the email was sent successfully, False otherwise.
    
    Raises:
        ValueError: If recipient email format is invalid.
        SMTPError: If SMTP server connection or sending fails.
        FileNotFoundError: If any attachment file doesn't exist.
    
    Example:
        >>> service = EmailService(config)
        >>> success = service.send_email(
        ...     recipient="<EMAIL>",
        ...     subject="Welcome!",
        ...     body="<h1>Welcome to our service!</h1>"
        ... )
        >>> print(success)
        True
    
    Note:
        This method requires the service to be initialized before use.
        Large attachments (>10MB) may cause timeouts.
    """
```

### 2. Inline Documentation

```python
class EmailService:
    """Email service for sending notifications and alerts.
    
    This service provides email functionality including SMTP configuration,
    template processing, and delivery tracking. It supports both HTML and
    plain text emails with optional attachments.
    
    Attributes:
        config: Service configuration dictionary
        smtp_client: SMTP client instance
        template_engine: Email template processor
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize email service with configuration."""
        self.config = config
        self.smtp_client = None  # Initialized in initialize()
        self.template_engine = TemplateEngine()
        self._connection_pool = None  # SMTP connection pool
```

## 📊 Documentation Quality Standards

### 1. Documentation Checklist

- [ ] README.md with clear description and usage examples
- [ ] API documentation with all endpoints documented
- [ ] Configuration guide with all options explained
- [ ] Changelog with version history
- [ ] Code comments for complex logic
- [ ] Docstrings for all public methods
- [ ] Examples for common use cases
- [ ] Troubleshooting guide for common issues

### 2. Documentation Review Process

1. **Technical Review**: Verify technical accuracy
2. **Clarity Review**: Ensure clear and understandable language
3. **Completeness Review**: Check all features are documented
4. **Example Review**: Verify all examples work correctly
5. **Link Review**: Ensure all links are valid and working

This documentation standard ensures that all plugins are well-documented, making them easier to use, maintain, and contribute to.
