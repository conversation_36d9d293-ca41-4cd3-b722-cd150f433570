"""
Performance tests for VPN Config Generator plugin.
"""

import unittest
import sys
import os
import time
import tempfile
import threading
from unittest.mock import Mock, patch

# Add plugin directory to path
plugin_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, plugin_dir)

from services import VPNConfigGeneratorService
from models import VPNConfigRequest, ConfigTemplate


class TestVPNConfigGeneratorPerformance(unittest.TestCase):
    """Performance tests for VPN Config Generator"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.app_config = {
            'api_config': {
                'enabled': True,
                'use_vpn_plugin_api': False,
                'fallback_api_endpoint': 'https://test.api.com',
                'fallback_username': 'admin',
                'fallback_password': 'admin123',
                'timeout': 30
            },
            'generator_settings': {
                'default_server': 'server11',
                'default_days': '30',
                'default_telco': 'digi',
                'default_plan': 'unlimited',
                'auto_generate_username': True,
                'username_prefix': 'user'
            }
        }
        
        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.plugin_configs = {}
        
        self.service = VPNConfigGeneratorService(
            self.temp_dir, 
            self.app_config, 
            self.mock_plugin_manager
        )
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_service_initialization_performance(self):
        """Test service initialization performance"""
        start_time = time.time()
        
        # Create multiple service instances
        for i in range(10):
            temp_dir = tempfile.mkdtemp()
            try:
                service = VPNConfigGeneratorService(
                    temp_dir, 
                    self.app_config, 
                    self.mock_plugin_manager
                )
                self.assertIsNotNone(service)
            finally:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should initialize 10 services in less than 1 second
        self.assertLess(execution_time, 1.0, 
                       f"Service initialization took {execution_time:.2f}s, expected < 1.0s")
        print(f"Service initialization: {execution_time:.3f}s for 10 instances")
    
    def test_template_operations_performance(self):
        """Test template operations performance"""
        # Create many templates
        templates = []
        for i in range(100):
            template = ConfigTemplate(
                id=f"template_{i}",
                name=f"Template {i}",
                description=f"Test template {i}",
                server=f"server{i % 12 + 1}",
                days=str((i % 30) + 1),
                telco="digi" if i % 2 == 0 else "maxis",
                plan="unlimited" if i % 3 == 0 else "basic"
            )
            templates.append(template)
        
        # Test bulk template addition
        start_time = time.time()
        for template in templates:
            self.service.add_template(template)
        add_time = time.time() - start_time
        
        # Test template retrieval
        start_time = time.time()
        all_templates = self.service.get_all_templates()
        retrieval_time = time.time() - start_time
        
        # Test individual template lookup
        start_time = time.time()
        for i in range(0, 100, 10):  # Test every 10th template
            template = self.service.get_template(f"template_{i}")
            self.assertIsNotNone(template)
        lookup_time = time.time() - start_time
        
        # Test template deletion
        start_time = time.time()
        for i in range(0, 100, 2):  # Delete every other template
            self.service.delete_template(f"template_{i}")
        deletion_time = time.time() - start_time
        
        # Performance assertions
        self.assertLess(add_time, 2.0, f"Adding 100 templates took {add_time:.2f}s, expected < 2.0s")
        self.assertLess(retrieval_time, 0.1, f"Retrieving all templates took {retrieval_time:.2f}s, expected < 0.1s")
        self.assertLess(lookup_time, 0.1, f"Looking up 10 templates took {lookup_time:.2f}s, expected < 0.1s")
        self.assertLess(deletion_time, 1.0, f"Deleting 50 templates took {deletion_time:.2f}s, expected < 1.0s")
        
        print(f"Template operations performance:")
        print(f"  Add 100 templates: {add_time:.3f}s")
        print(f"  Retrieve all templates: {retrieval_time:.3f}s")
        print(f"  Lookup 10 templates: {lookup_time:.3f}s")
        print(f"  Delete 50 templates: {deletion_time:.3f}s")
        
        # Verify final state
        remaining_templates = self.service.get_all_templates()
        self.assertEqual(len(remaining_templates), 50)
    
    def test_config_generation_performance(self):
        """Test config generation performance"""
        # Mock successful API responses
        with patch('requests.Session') as mock_session_class:
            mock_session = Mock()
            mock_session_class.return_value = mock_session
            
            # Mock authentication response
            mock_auth_response = Mock()
            mock_auth_response.status_code = 200
            mock_auth_response.json.return_value = {'access_token': 'test_token'}
            
            # Mock client creation response
            mock_client_response = Mock()
            mock_client_response.status_code = 201
            mock_client_response.json.return_value = {
                'config_url': 'vmess://test-config-url'
            }
            
            mock_session.post.side_effect = [mock_auth_response, mock_client_response]
            
            # Test multiple config generations
            requests = []
            for i in range(20):
                request = VPNConfigRequest(
                    server=f"server{i % 12 + 1}",
                    days=str((i % 30) + 1),
                    telco="digi" if i % 2 == 0 else "maxis",
                    plan="unlimited" if i % 3 == 0 else "basic",
                    username=f"testuser{i}"
                )
                requests.append(request)
            
            # Sequential generation
            start_time = time.time()
            results = []
            for request in requests:
                # Reset mock for each request
                mock_session.post.side_effect = [mock_auth_response, mock_client_response]
                result = self.service.generate_config(request)
                results.append(result)
            sequential_time = time.time() - start_time
            
            # Verify all succeeded
            for result in results:
                self.assertTrue(result.success)
            
            # Performance assertion
            self.assertLess(sequential_time, 5.0, 
                           f"Generating 20 configs took {sequential_time:.2f}s, expected < 5.0s")
            
            print(f"Config generation performance:")
            print(f"  20 sequential generations: {sequential_time:.3f}s")
            print(f"  Average per generation: {sequential_time/20:.3f}s")
    
    def test_concurrent_operations_performance(self):
        """Test concurrent operations performance"""
        results = []
        errors = []
        
        def add_templates_worker(start_id, count):
            """Worker function to add templates"""
            try:
                for i in range(count):
                    template = ConfigTemplate(
                        id=f"concurrent_template_{start_id}_{i}",
                        name=f"Concurrent Template {start_id}-{i}",
                        description=f"Concurrent test template {start_id}-{i}",
                        server=f"server{i % 12 + 1}",
                        days=str((i % 30) + 1),
                        telco="digi" if i % 2 == 0 else "maxis",
                        plan="unlimited" if i % 3 == 0 else "basic"
                    )
                    result = self.service.add_template(template)
                    results.append(result)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = []
        thread_count = 5
        templates_per_thread = 10
        
        start_time = time.time()
        
        for i in range(thread_count):
            thread = threading.Thread(
                target=add_templates_worker,
                args=(i, templates_per_thread)
            )
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        concurrent_time = time.time() - start_time
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Concurrent operations had errors: {errors}")
        self.assertEqual(len(results), thread_count * templates_per_thread)
        self.assertTrue(all(results), "Some template additions failed")
        
        # Performance assertion
        self.assertLess(concurrent_time, 3.0, 
                       f"Concurrent operations took {concurrent_time:.2f}s, expected < 3.0s")
        
        print(f"Concurrent operations performance:")
        print(f"  {thread_count} threads, {templates_per_thread} templates each: {concurrent_time:.3f}s")
        
        # Verify final state
        all_templates = self.service.get_all_templates()
        self.assertEqual(len(all_templates), thread_count * templates_per_thread)
    
    def test_memory_usage_performance(self):
        """Test memory usage during operations"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform memory-intensive operations
        templates = []
        for i in range(1000):
            template = ConfigTemplate(
                id=f"memory_template_{i}",
                name=f"Memory Template {i}",
                description=f"Memory test template {i} with longer description to use more memory",
                server=f"server{i % 12 + 1}",
                days=str((i % 30) + 1),
                telco="digi" if i % 2 == 0 else "maxis",
                plan="unlimited" if i % 3 == 0 else "basic"
            )
            templates.append(template)
            self.service.add_template(template)
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Clean up templates
        for template in templates:
            self.service.delete_template(template.id)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_increase = peak_memory - initial_memory
        memory_cleanup = peak_memory - final_memory
        
        print(f"Memory usage performance:")
        print(f"  Initial memory: {initial_memory:.1f} MB")
        print(f"  Peak memory: {peak_memory:.1f} MB")
        print(f"  Final memory: {final_memory:.1f} MB")
        print(f"  Memory increase: {memory_increase:.1f} MB")
        print(f"  Memory cleaned up: {memory_cleanup:.1f} MB")
        
        # Memory should not increase excessively
        self.assertLess(memory_increase, 100, 
                       f"Memory increased by {memory_increase:.1f} MB, expected < 100 MB")
        
        # Most memory should be cleaned up
        cleanup_ratio = memory_cleanup / memory_increase if memory_increase > 0 else 1
        self.assertGreater(cleanup_ratio, 0.5, 
                          f"Only {cleanup_ratio:.1%} of memory was cleaned up")


if __name__ == '__main__':
    # Check if psutil is available for memory tests
    try:
        import psutil
        print("Running performance tests with memory monitoring...")
    except ImportError:
        print("Running performance tests without memory monitoring (psutil not available)...")
    
    unittest.main(verbosity=2)
