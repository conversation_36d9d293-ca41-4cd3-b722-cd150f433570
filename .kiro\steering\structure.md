# Project Structure & Organization

## Root Directory Layout
```
SteamCodeTool/
├── main.py                 # Main application entry point
├── config.py              # Configuration management
├── requirements.txt       # Python dependencies
├── Dockerfile            # Docker build configuration
├── docker-compose.steamcodetool.yml  # Docker deployment config
├── api/                  # API route blueprints
├── core/                 # Core system components
├── plugins/              # Plugin system (modular features)
├── services/             # Business logic services
├── templates/            # Jinja2 HTML templates
├── static/               # Static web assets
├── utils/                # Utility functions and helpers
├── configs/              # Configuration files (Docker persistent)
├── data/                 # Application data storage
├── logs/                 # Application logs
└── docs/                 # Documentation
```

## Core Architecture Components

### `/core/` - System Core
- `plugin_manager.py` - Plugin lifecycle management
- `__init__.py` - Core module initialization

### `/api/` - API Layer
- `admin_routes.py` - Admin panel endpoints
- `chat_routes.py` - Chat/messaging endpoints
- `conversation_routes.py` - Conversation management
- `order_routes.py` - Order processing endpoints
- `email_routes.py` - Email service endpoints
- `canva_routes.py` - Canva integration endpoints
- `vpn_routes.py` - VPN management endpoints
- `curlec_routes.py` - Payment gateway integration

### `/services/` - Business Logic
- `*_service.py` - Service layer for each domain (chat, order, email, etc.)
- `health_monitoring_service.py` - System health monitoring
- `session_service.py` - Session management

### `/plugins/` - Modular Features
Each plugin follows this structure:
```
plugins/{plugin_name}/
├── __init__.py
├── plugin.py             # Main plugin class (implements PluginInterface)
├── routes/               # Plugin-specific routes
├── services/             # Plugin business logic
├── templates/            # Plugin HTML templates
└── config.json          # Plugin configuration
```

Available plugins:
- `ai_chat/` - AI-powered customer service
- `chat_commands/` - Chat command processing
- `netflix/` - Netflix account management
- `steam/` - Steam code management
- `vpn/` - VPN server and client management
- `canva/` - Canva Pro account management
- `shopee/` - Shopee marketplace integration

### `/configs/` - Configuration Management
```
configs/
├── core/                 # Core application config
│   ├── config.json      # Main app configuration
│   └── plugin_config.json  # Plugin configurations
├── services/            # Service-specific configs
├── data/                # Application data files
├── cache/               # Temporary cache files
└── plugins/             # Plugin persistent configs (Docker mounted)
```

## Key Architectural Patterns

### Plugin System
- **Interface**: All plugins implement `PluginInterface` from `core.plugin_manager`
- **Lifecycle**: Plugins have `initialize()`, `shutdown()`, and `get_blueprint()` methods
- **Configuration**: Each plugin has its own config schema and persistence
- **Routes**: Plugins register Flask blueprints with custom URL prefixes

### Service Layer Pattern
- Business logic separated into service classes
- Services handle data persistence, external API calls, and business rules
- Controllers (routes) are thin and delegate to services

### Configuration Management
- Centralized configuration in `config.py`
- JSON-based configuration files with hot-reloading
- Environment-specific settings support
- Sensitive data encryption

### Security Architecture
- `security_headers.py` - HTTP security headers
- `anti_phishing_measures.py` - Anti-phishing protection
- `utils/auth.py` - Authentication utilities
- `utils/rate_limiter.py` - Rate limiting and IP blocking
- `utils/audit_logger.py` - Security audit logging

## File Naming Conventions
- **Services**: `{domain}_service.py` (e.g., `chat_service.py`)
- **Routes**: `{domain}_routes.py` (e.g., `admin_routes.py`)
- **Plugins**: Main class always named `Plugin` in `plugin.py`
- **Templates**: HTML files in `templates/` with descriptive names
- **Configuration**: JSON files with `_config.json` suffix for services

## Data Flow
1. **Request** → API Routes (`/api/`)
2. **Route** → Service Layer (`/services/`)
3. **Service** → Data Storage (`/configs/data/`)
4. **Plugin Communication** → Plugin Manager (`/core/`)

## Development Guidelines
- Follow the plugin interface for new features
- Use service layer for business logic
- Store configuration in appropriate `/configs/` subdirectory
- Implement proper error handling and logging
- Follow Flask blueprint patterns for routes
- Use JSON for data persistence (no database required)