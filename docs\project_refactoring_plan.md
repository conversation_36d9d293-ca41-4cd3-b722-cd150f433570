# Project Refactoring Plan

This document outlines the step-by-step plan for refactoring the SteamCodeTool project structure to improve organization and maintainability.

## Current Issues

- Many files scattered in the root directory
- Mixed deployment scripts with application code
- Unclear separation of concerns
- Documentation spread across multiple files
- Configuration files in root directory

## Refactoring Goals

- Create a clean, organized directory structure
- Separate application code from deployment scripts
- Improve maintainability and readability
- Make the project more developer-friendly
- Ensure backward compatibility

## Implementation Plan

### Phase 1: Analysis and Preparation

- [x] Analyze current project structure
- [x] Identify core components and dependencies
- [x] Create a detailed refactoring plan

### Phase 2: Directory Structure Creation

- [ ] Create the following directory structure:
  ```
  steamcodetool/
  ├── app/                    # Main application code
  │   ├── api/                # API endpoints
  │   ├── core/               # Core functionality
  │   ├── plugins/            # Plugin modules
  │   ├── scheduler/          # Scheduler jobs
  │   ├── services/           # Business logic services
  │   ├── static/             # Static assets
  │   ├── templates/          # Template files
  │   └── utils/              # Utility functions
  ├── config/                 # Configuration files
  │   ├── templates/          # Config templates
  │   └── environments/       # Environment-specific configs
  ├── data/                   # Data storage
  │   ├── steam/              # Steam plugin data
  │   ├── netflix/            # Netflix plugin data
  │   ├── vpn/                # VPN plugin data
  │   └── canva/              # Canva plugin data
  ├── deployment/             # Deployment scripts
  │   ├── docker/             # Docker-related files
  │   └── scripts/            # Deployment scripts
  ├── docs/                   # Documentation
  ├── logs/                   # Log files
  └── tests/                  # Test files
  ```

### Phase 3: File Migration

- [ ] Move deployment scripts to `deployment/scripts/`:
  - [ ] `Pack.bat`
  - [ ] `Updates.bat`
  - [ ] `docker-hub-push.bat`
  - [ ] `docker-hub-push-mtyb-tools.bat`
  - [ ] `run_plugin_dev.bat`
  - [ ] `run_plugin_prod.bat`
  - [ ] `deploy_plugin_system.py`

- [ ] Move Docker files to `deployment/docker/`:
  - [ ] `Dockerfile`
  - [ ] `docker-compose.steamcodetool.yml`
  - [ ] `docker-compose.plugins.yml`

- [ ] Move documentation to `docs/`:
  - [ ] `MAIN_CONSOLIDATION.md`
  - [ ] `PLUGIN_ARCHITECTURE.md`
  - [ ] `PLUGIN_REFACTOR_SUMMARY.md`
  - [ ] `CONSOLIDATION_COMPLETE.md`
  - [ ] `STEAMCODETOOL_DOCKER.md`
  - [ ] `DOCKER_QUICK_START.md`

- [ ] Move configuration files to `config/`:
  - [ ] `config.json`
  - [ ] `config_templates.json`
  - [ ] `plugin_config.json`
  - [ ] `vpn_servers.json`
  - [ ] `canva_config.json`

- [ ] Move application code to `app/`:
  - [ ] Move `main.py` to `app/main.py`
  - [ ] Move existing directories to their corresponding locations in `app/`

### Phase 4: Code Updates

- [ ] Create new `main.py` entry point in root directory
- [ ] Update import statements in all files
- [ ] Update configuration loading logic to use new paths
- [ ] Update Docker configuration to reflect new structure

### Phase 5: Testing and Verification

- [ ] Test application functionality
- [ ] Verify plugin system works correctly
- [ ] Test Docker deployment
- [ ] Fix any issues that arise

### Phase 6: Documentation and Cleanup

- [ ] Create comprehensive README.md
- [ ] Update deployment documentation
- [ ] Remove backup files after successful migration
- [ ] Remove any temporary files created during migration

## Migration Script

A migration script (`migrate_project_structure.py`) has been created to automate most of these tasks. The script will:

1. Create a backup of the current project structure
2. Create the new directory structure
3. Move files to their appropriate locations
4. Create a new main.py entry point
5. Log instructions for manual updates needed

## Execution Checklist

- [ ] Run `python migrate_project_structure.py`
- [ ] Manually update import statements as needed
- [ ] Test application functionality
- [ ] Update Docker configuration
- [ ] Test Docker deployment
- [ ] Update documentation
- [ ] Clean up backup files

## Notes

- Keep track of any issues encountered during migration
- Document any manual changes required
- Test thoroughly after each major change