{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Edit Server Configuration</h2>
                <a href="{{ url_for('vpn.server_configuration', server_id=server_id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Cancel
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Configuration Editor</h3>
                    <div class="card-tools">
                        <button class="btn btn-sm btn-info" onclick="formatJSON()">
                            <i class="fas fa-code"></i> Format JSON
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="validateJSON()">
                            <i class="fas fa-check"></i> Validate JSON
                        </button>
                    </div>
                </div>
                <form method="POST" action="{{ url_for('vpn.edit_server_configuration', server_id=server_id) }}">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="config">Configuration JSON:</label>
                            <textarea class="form-control" id="config" name="config" rows="25" 
                                      style="font-family: monospace; font-size: 14px;" required>{{ config.config | tojson(indent=2) if config else '{}' }}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="create_backup" 
                                       name="create_backup" checked>
                                <label class="custom-control-label" for="create_backup">
                                    Create backup before saving
                                </label>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            <strong>Warning:</strong> Editing the configuration directly can break your VPN server. 
                            Make sure you know what you're doing!
                        </div>
                        
                        <div id="validation-result"></div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary" onclick="return confirmSave()">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewChanges()">
                            <i class="fas fa-eye"></i> Preview Changes
                        </button>
                        <a href="{{ url_for('vpn.server_configuration', server_id=server_id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Configuration Changes Preview</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="preview-content">
                        <p>Comparing current configuration with your changes...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
var originalConfig = {{ config.config | tojson if config else '{}' }};

function formatJSON() {
    try {
        var configText = $('#config').val();
        var configObj = JSON.parse(configText);
        $('#config').val(JSON.stringify(configObj, null, 2));
        toastr.success('JSON formatted successfully!');
    } catch (e) {
        toastr.error('Invalid JSON: ' + e.message);
    }
}

function validateJSON() {
    try {
        var configText = $('#config').val();
        var configObj = JSON.parse(configText);
        
        // Basic validation
        var errors = [];
        var warnings = [];
        
        if (!configObj.inbounds || !Array.isArray(configObj.inbounds)) {
            errors.push('Configuration must have an "inbounds" array');
        } else if (configObj.inbounds.length === 0) {
            warnings.push('No inbounds configured');
        }
        
        if (!configObj.outbounds || !Array.isArray(configObj.outbounds)) {
            errors.push('Configuration must have an "outbounds" array');
        }
        
        // Check for clients in inbounds
        if (configObj.inbounds) {
            configObj.inbounds.forEach(function(inbound, index) {
                if (inbound.settings && inbound.settings.clients) {
                    if (!Array.isArray(inbound.settings.clients)) {
                        errors.push(`Inbound ${index}: clients must be an array`);
                    }
                } else {
                    warnings.push(`Inbound ${index}: no clients configured`);
                }
            });
        }
        
        // Display results
        var resultHtml = '';
        if (errors.length > 0) {
            resultHtml += '<div class="alert alert-danger"><strong>Errors:</strong><ul>';
            errors.forEach(function(error) {
                resultHtml += '<li>' + error + '</li>';
            });
            resultHtml += '</ul></div>';
        }
        
        if (warnings.length > 0) {
            resultHtml += '<div class="alert alert-warning"><strong>Warnings:</strong><ul>';
            warnings.forEach(function(warning) {
                resultHtml += '<li>' + warning + '</li>';
            });
            resultHtml += '</ul></div>';
        }
        
        if (errors.length === 0 && warnings.length === 0) {
            resultHtml = '<div class="alert alert-success">JSON is valid!</div>';
        }
        
        $('#validation-result').html(resultHtml);
        
    } catch (e) {
        $('#validation-result').html(
            '<div class="alert alert-danger"><strong>JSON Parse Error:</strong> ' + e.message + '</div>'
        );
    }
}

function confirmSave() {
    // Validate JSON before saving
    try {
        var configText = $('#config').val();
        JSON.parse(configText);
        return confirm('Are you sure you want to save this configuration? This will update the server immediately.');
    } catch (e) {
        alert('Invalid JSON: ' + e.message);
        return false;
    }
}

function previewChanges() {
    try {
        var newConfigText = $('#config').val();
        var newConfig = JSON.parse(newConfigText);
        
        $('#previewModal').modal('show');
        
        // Simple comparison - in a real app, you'd use a diff library
        var changes = [];
        
        // Check for added/removed inbounds
        var oldInbounds = originalConfig.inbounds ? originalConfig.inbounds.length : 0;
        var newInbounds = newConfig.inbounds ? newConfig.inbounds.length : 0;
        
        if (oldInbounds !== newInbounds) {
            changes.push(`Inbounds: ${oldInbounds} → ${newInbounds}`);
        }
        
        // Check for client changes
        var oldClients = countClients(originalConfig);
        var newClients = countClients(newConfig);
        
        if (oldClients !== newClients) {
            changes.push(`Total clients: ${oldClients} → ${newClients}`);
        }
        
        var previewHtml = '<h5>Configuration Changes:</h5>';
        if (changes.length > 0) {
            previewHtml += '<ul>';
            changes.forEach(function(change) {
                previewHtml += '<li>' + change + '</li>';
            });
            previewHtml += '</ul>';
        } else {
            previewHtml += '<p>No significant changes detected.</p>';
        }
        
        previewHtml += '<hr><h5>New Configuration Preview:</h5>';
        previewHtml += '<pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">' + 
                       JSON.stringify(newConfig, null, 2) + '</pre>';
        
        $('#preview-content').html(previewHtml);
        
    } catch (e) {
        alert('Invalid JSON: ' + e.message);
    }
}

function countClients(config) {
    var count = 0;
    if (config.inbounds) {
        config.inbounds.forEach(function(inbound) {
            if (inbound.settings && inbound.settings.clients && Array.isArray(inbound.settings.clients)) {
                count += inbound.settings.clients.length;
            }
        });
    }
    return count;
}

// Auto-save draft
var autoSaveTimer;
$('#config').on('input', function() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(function() {
        localStorage.setItem('vpn_config_draft_' + {{ server_id }}, $('#config').val());
        toastr.info('Draft saved locally', '', { timeOut: 1000 });
    }, 2000);
});

// Load draft if exists
$(document).ready(function() {
    var draft = localStorage.getItem('vpn_config_draft_' + {{ server_id }});
    if (draft && draft !== $('#config').val()) {
        if (confirm('A draft configuration was found. Do you want to load it?')) {
            $('#config').val(draft);
            toastr.success('Draft loaded');
        }
    }
});

// Clear draft on successful save
$('form').on('submit', function() {
    localStorage.removeItem('vpn_config_draft_' + {{ server_id }});
});
</script>
{% endblock %}