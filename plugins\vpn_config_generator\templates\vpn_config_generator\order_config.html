<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Configuration - Crypto Style</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='spinkit.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Light Crypto Style Variables - Optimized */
        :root {
            --crypto-light: #f8fafc;
            --crypto-lighter: #ffffff;
            --crypto-card: #ffffff;
            --crypto-border: #e2e8f0;
            --crypto-accent: #3b82f6;
            --crypto-accent-dark: #2563eb;
            --crypto-purple: #8b5cf6;
            --crypto-green: #10b981;
            --crypto-red: #ef4444;
            --crypto-orange: #f59e0b;
            --crypto-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --crypto-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            --crypto-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        * {
            font-family: 'Inter', sans-serif;
        }

        body {
            background: var(--crypto-light);
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
            min-height: 100vh;
        }

        .crypto-card {
            background: var(--crypto-card);
            border: 1px solid var(--crypto-border);
            border-radius: 20px;
            box-shadow: var(--crypto-shadow-lg);
        }

        /* Modern Button System */
        .crypto-button {
            position: relative;
            background: linear-gradient(135deg, var(--crypto-accent), var(--crypto-accent-dark));
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            font-size: 14px;
            padding: 10px 20px;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 2px 8px rgba(59, 130, 246, 0.12),
                0 1px 2px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(10px);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 40px;
        }

        .crypto-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .crypto-button:hover::before {
            left: 100%;
        }

        .crypto-button:hover {
            transform: translateY(-1px);
            box-shadow:
                0 4px 12px rgba(59, 130, 246, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.12);
        }

        .crypto-button:active {
            transform: translateY(0px);
            transition: all 0.1s ease;
        }

        .crypto-button:disabled {
            background: linear-gradient(135deg, #9ca3af, #6b7280);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: not-allowed;
            transform: none;
            opacity: 0.6;
        }

        .crypto-button:disabled::before {
            display: none;
        }

        /* Button Variants */
        .crypto-button-secondary {
            background: linear-gradient(135deg, var(--crypto-purple), #7c3aed);
            box-shadow:
                0 2px 8px rgba(139, 92, 246, 0.12),
                0 1px 2px rgba(0, 0, 0, 0.08);
        }

        .crypto-button-secondary:hover {
            box-shadow:
                0 4px 12px rgba(139, 92, 246, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.12);
        }

        .crypto-button-success {
            background: linear-gradient(135deg, var(--crypto-green), #059669);
            box-shadow:
                0 2px 8px rgba(16, 185, 129, 0.12),
                0 1px 2px rgba(0, 0, 0, 0.08);
        }

        .crypto-button-success:hover {
            box-shadow:
                0 4px 12px rgba(16, 185, 129, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.12);
        }

        .crypto-button-danger {
            background: linear-gradient(135deg, var(--crypto-red), #dc2626);
            box-shadow:
                0 2px 8px rgba(239, 68, 68, 0.12),
                0 1px 2px rgba(0, 0, 0, 0.08);
        }

        .crypto-button-danger:hover {
            box-shadow:
                0 4px 12px rgba(239, 68, 68, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.12);
        }

        /* Button Sizes */
        .crypto-button-sm {
            padding: 8px 16px;
            font-size: 13px;
            border-radius: 6px;
            min-height: 32px;
        }

        .crypto-button-lg {
            padding: 12px 24px;
            font-size: 15px;
            border-radius: 10px;
            min-height: 48px;
        }

        /* Ghost button size overrides */
        .crypto-button-ghost.crypto-button-sm {
            padding: 8px 16px;
            font-size: 13px;
            border-radius: 6px;
            min-height: 32px;
        }

        /* Ghost Button */
        .crypto-button-ghost {
            background: transparent;
            border: 1px solid var(--crypto-border);
            color: #6b7280;
            box-shadow: none;
            font-weight: 400;
            font-size: 14px;
            padding: 10px 20px;
            border-radius: 8px;
            min-height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .crypto-button-ghost:hover {
            background: #f9fafb;
            border-color: var(--crypto-accent);
            color: var(--crypto-accent);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        /* Loading Animation */
        .crypto-button-loading {
            pointer-events: none;
        }

        .crypto-button-loading .button-content {
            opacity: 0;
        }

        .crypto-button-loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        /* Icon Button Enhancements */
        .button-icon {
            transition: transform 0.2s ease;
        }

        .crypto-button:hover .button-icon {
            transform: scale(1.1);
        }

        .crypto-button:active .button-icon {
            transform: scale(0.95);
        }

        /* Ripple Effect */
        .crypto-button-ripple {
            position: relative;
            overflow: hidden;
        }

        .crypto-button-ripple::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            top: var(--y);
            left: var(--x);
            width: 40px;
            height: 40px;
            margin: -20px 0 0 -20px;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Enhanced Spin Animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Button Group */
        .crypto-button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .crypto-button-group .crypto-button {
            flex: 1;
            min-width: 100px;
        }

        /* Pulse Animation for Important Buttons */
        .crypto-button-pulse {
            animation: button-pulse 2s infinite;
        }

        @keyframes button-pulse {
            0% {
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.15);
            }
            50% {
                box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            }
            100% {
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.15);
            }
        }

        .crypto-input {
            background: var(--crypto-lighter);
            border: 2px solid var(--crypto-border);
            border-radius: 12px;
            color: #1f2937;
            padding: 16px;
            font-size: 16px;
            transition: border-color 0.2s ease;
        }

        .crypto-input:focus {
            border-color: var(--crypto-accent);
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .crypto-input::placeholder {
            color: #9ca3af;
        }

        .floating-label-input {
            position: relative;
        }

        .floating-label-input input {
            height: 60px;
            padding: 20px 16px 8px 16px;
        }

        .floating-label-input label {
            position: absolute;
            top: 20px;
            left: 16px;
            transition: all 0.2s ease;
            pointer-events: none;
            color: #9ca3af;
            font-weight: 500;
        }

        .floating-label-input input:focus + label,
        .floating-label-input input:not(:placeholder-shown) + label {
            font-size: 12px;
            top: 8px;
            color: var(--crypto-accent);
        }

        .modal-enter {
            opacity: 0;
            transform: scale(0.95);
        }

        .modal-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: all 200ms ease-out;
        }

        .modal-exit {
            opacity: 1;
        }

        .modal-exit-active {
            opacity: 0;
            transform: scale(0.95);
            transition: all 200ms ease-in;
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            transform: rotate(180deg);
        }

        .wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 80px;
        }

        .wave .shape-fill {
            fill: rgba(59, 130, 246, 0.05);
        }

        .telco-card {
            background: var(--crypto-card);
            border: 1px solid var(--crypto-border);
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .telco-card:hover {
            transform: translateY(-1px);
            border-color: var(--crypto-accent);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .telco-card.selected {
            border-color: var(--crypto-green);
            background: linear-gradient(145deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }

        .plan-card {
            background: var(--crypto-card);
            border: 1px solid var(--crypto-border);
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .plan-card:hover {
            transform: translateY(-1px);
            border-color: var(--crypto-purple);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .plan-card.selected {
            border-color: var(--crypto-green);
            background: linear-gradient(145deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }

        .plan-card.selected::before {
            content: '✓';
            position: absolute;
            top: 12px;
            right: 12px;
            width: 20px;
            height: 20px;
            background: var(--crypto-green);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* Modern plan card styling for change config modal */
        .plan-card-modern {
            transition: all 0.2s ease;
        }

        .plan-card-modern:hover .radio-indicator {
            border-color: #6366f1;
        }

        .plan-card-modern input[type="radio"]:checked + div {
            border-color: #6366f1 !important;
            background-color: #f0f9ff !important;
        }

        .plan-card-modern input[type="radio"]:checked + div .radio-indicator {
            border-color: #6366f1;
            background-color: #6366f1;
        }

        .plan-card-modern input[type="radio"]:checked + div .radio-indicator div {
            opacity: 1 !important;
            background-color: white;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-available {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-claimed {
            background: rgba(245, 158, 11, 0.1);
            color: #d97706;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-expired {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .glow-text {
            color: #1f2937;
            text-shadow: none;
        }

        .crypto-modal {
            background: rgba(15, 23, 42, 0.5);
        }

        .crypto-modal-content {
            background: var(--crypto-card);
            border: 1px solid var(--crypto-border);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .info-card {
            background: var(--crypto-card);
            border: 1px solid var(--crypto-border);
            border-radius: 16px;
            box-shadow: var(--crypto-shadow);
        }

        .spinner-crypto {
            width: 32px;
            height: 32px;
            border: 3px solid rgba(59, 130, 246, 0.1);
            border-top: 3px solid var(--crypto-accent);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .pulse-glow {
            animation: pulse-simple 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-simple {
            from {
                opacity: 0.7;
            }
            to {
                opacity: 1;
            }
        }

        /* Mobile Optimizations */
        @media (max-width: 640px) {
            .crypto-card {
                border-radius: 16px;
                margin: 12px;
            }

            .crypto-modal-content {
                margin: 20px;
                border-radius: 20px;
                max-height: calc(100vh - 40px);
            }

            .telco-card,
            .plan-card {
                border-radius: 12px;
            }

            .floating-label-input input {
                font-size: 16px; /* Prevent zoom on iOS */
            }
        }

        /* Animation delay utilities */
        .animation-delay-150 {
            animation-delay: 150ms;
        }
        
        .animation-delay-300 {
            animation-delay: 300ms;
        }

        /* Improved scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--crypto-border);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--crypto-accent);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--crypto-accent-dark);
        }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center relative">
    <!-- Animated background elements -->
    <div class="wave">
        <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path
                d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
                class="shape-fill"></path>
        </svg>
    </div>

    <!-- Main Container -->
    <div class="crypto-card p-8 w-full max-w-md mx-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center mb-4">
                <svg class="w-10 h-10 text-crypto-accent mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                <h1 class="text-3xl font-bold glow-text">VPN Config</h1>
            </div>
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-3 mt-2">
                <p class="text-gray-700 text-sm flex items-center justify-center font-medium">
                    <svg class="w-4 h-4 mr-2 text-crypto-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span class="text-crypto-accent font-semibold">
                        Seamless VPN configuration with modern, intuitive design
                    </span>
                </p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex justify-center mb-6">
            <button onclick="showHistoryModal()" class="crypto-button-ghost crypto-button-sm">
                <span class="button-content flex items-center">
                    <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    View History
                </span>
            </button>
        </div>

        <!-- Order Form -->
        <form id="orderForm" class="space-y-6">
            <div class="floating-label-input">
                <input type="text" id="orderId" name="orderId" required placeholder=" "
                    class="crypto-input w-full">
                <label for="orderId">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v3M7 4H5a1 1 0 00-1 1v14a1 1 0 001 1h14a1 1 0 001-1V5a1 1 0 00-1-1h-2M7 4h10M9 8h6m-6 4h6m-3 4h3"></path>
                    </svg>
                    Order ID
                </label>
            </div>

            <button type="submit" id="verifyButton"
                class="crypto-button w-full">
                <span class="button-content flex items-center justify-center">
                    <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Verify Order</span>
                </span>
            </button>
        </form>

        <!-- Status Indicator -->
        <div class="mt-6 text-center">
            <div class="inline-flex items-center space-x-2 text-sm text-gray-600">
                <div class="w-2 h-2 bg-crypto-green rounded-full pulse-glow"></div>
                <div class="w-5 h-5 bg-crypto-green rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <span>System Online</span>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="fixed bottom-0 w-full text-center py-4 text-gray-600 text-sm z-20">
        <div class="flex items-center justify-center space-x-2">
            <div class="w-2 h-2 bg-crypto-accent rounded-full"></div>
            <span>&copy; 2024 MTYB Official - Powered by Crypto Technology</span>
        </div>
    </footer>

    <!-- Order Verification Modal -->
    <div id="verificationModal" class="fixed inset-0 crypto-modal overflow-y-auto h-full w-full hidden z-50"
        onclick="handleModalClick(event)">
        <div class="relative top-20 mx-auto p-6 crypto-modal-content w-11/12 max-w-md">
            <div id="verificationContent" class="text-center">
                <!-- Loading State -->
                <div id="loadingIndicator" class="flex flex-col items-center">
                    <!-- Modern Loading Spinner -->
                    <div class="relative w-16 h-16 mb-6">
                        <div class="absolute inset-0 w-16 h-16 border-4 border-gray-200 rounded-full"></div>
                        <div class="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full animate-spin"></div>
                        <div class="absolute inset-2 w-12 h-12 border-2 border-transparent border-t-purple-400 border-l-blue-400 rounded-full animate-spin animation-delay-150"></div>
                    </div>
                </div>

                <!-- Success State -->
                <div id="successIndicator" class="hidden">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center mx-auto mb-4 pulse-glow">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>

                <!-- Error State -->
                <div id="errorIndicator" class="hidden">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                </div>

                <h3 class="text-xl font-semibold glow-text mb-2" id="verificationTitle">Verifying Order</h3>
                <div class="mb-6">
                    <p class="text-gray-600" id="verificationMessage">
                        Please wait while we verify your order...
                    </p>
                </div>
                <div>
                    <button id="proceedButton" type="button"
                        class="hidden crypto-button crypto-button-success transition duration-200"
                        onclick="showServiceSelection()">
                        <span class="button-content flex items-center justify-center">
                            <svg class="button-icon w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                            Proceed to Service Selection
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Selection Modal -->
    <div id="serviceModal" class="fixed inset-0 crypto-modal overflow-y-auto h-full w-full hidden z-50"
        onclick="handleServiceModalClick(event)">
        <div class="relative top-4 mx-auto p-0 crypto-modal-content w-11/12 max-w-2xl max-h-screen overflow-hidden">
            <div class="bg-white rounded-2xl shadow-2xl">
                <!-- Modern Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-100">
                    <div>
                        <h3 id="serviceModalTitle" class="text-xl font-semibold text-gray-900">Select VPN Service</h3>
                        <p id="serviceModalSubtitle" class="text-sm text-gray-500 mt-1">Configure your VPN connection settings</p>
                    </div>
                    <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors rounded-full p-2 hover:bg-gray-100" onclick="closeServiceModal()">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 max-h-[calc(100vh-200px)] overflow-y-auto">

                <!-- Current Configuration Display (for renewal/view workflows) -->
                <div id="currentConfigDisplay" class="mb-6 bg-blue-50 border border-blue-200 rounded-xl p-4 hidden">
                    <h4 class="font-semibold text-blue-800 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Current Configuration
                    </h4>
                    <div class="text-sm space-y-2">
                        <div class="flex items-center space-x-2">
                            <span class="text-blue-700">Telco:</span>
                            <span id="currentTelco" class="font-semibold text-blue-900"></span>
                            <span class="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">🔒 Locked</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-blue-700">Plan:</span>
                            <span id="currentPlan" class="font-medium text-blue-800"></span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-blue-700">UUID:</span>
                            <code id="currentUUIDDisplay" class="text-xs bg-blue-100 px-2 py-1 rounded font-mono text-blue-900"></code>
                        </div>
                    </div>
                    <p class="text-xs text-blue-600 mt-3">You can change the plan and server for configuration display</p>
                </div>

                <!-- User Info -->
                <div id="userInfo" class="mb-6 bg-gray-50 rounded-xl p-4">
                    <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Order Information
                    </h4>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div>
                            <span class="text-gray-600">Order:</span>
                            <div id="displayOrderSn" class="font-medium text-gray-900"></div>
                        </div>
                        <div>
                            <span class="text-gray-600">Customer:</span>
                            <div id="displayBuyerUsername" class="font-medium text-gray-900"></div>
                        </div>
                        <div>
                            <span class="text-gray-600">SKU:</span>
                            <div id="displaySku" class="font-medium text-gray-900"></div>
                        </div>
                        <div>
                            <span class="text-gray-600">Status:</span>
                            <div><span id="displayStatus" class="status-badge"></span></div>
                        </div>
                    </div>
                    <div id="restrictionInfo" class="mt-3 p-3 rounded-lg bg-orange-50 border border-orange-200 hidden">
                        <p class="text-orange-600 text-xs flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span id="restrictionMessage"></span>
                        </p>
                    </div>
                </div>

                <!-- Service Configuration Form -->
                <form id="serviceForm" class="space-y-6">
                    <!-- Server Selection Configuration -->
                    <div class="bg-white border border-gray-200 rounded-xl p-6">
                        <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                            </svg>
                            Server Selection
                        </h4>

                        <div class="bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg p-4 mb-4">
                            <div class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-indigo-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-indigo-800 mb-1">Server Configuration</p>
                                    <p class="text-xs text-indigo-700" id="uuidCreationInfo">
                                        Your UUID is available on ALL related servers. Choose any server below to generate its configuration template.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">Available Servers</label>
                                <select id="serverSelect" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
                                    <option value="">Loading servers...</option>
                                </select>
                            </div>

                            <div class="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                                <div class="text-center">
                                    <div class="text-xs text-gray-500 mb-1">Validity Period</div>
                                    <div id="autoValidityDisplay" class="font-semibold text-indigo-600">Loading...</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-xs text-gray-500 mb-1">Server Tags</div>
                                    <div id="autoTagsDisplay" class="font-medium text-gray-700 text-xs">Loading...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden fields for form submission -->
                    <input type="hidden" id="serverInput" required>
                    <input type="hidden" id="daysInput" required>

                    <!-- Telco Selection -->
                    <div class="bg-white border border-gray-200 rounded-xl p-6">
                        <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Telecommunications Provider
                        </h4>
                        <div id="telcoSelection" class="space-y-3">
                            <!-- Telco cards will be populated here -->
                        </div>
                    </div>

                    <!-- Plan Selection -->
                    <div id="planSelectionContainer" class="bg-white border border-gray-200 rounded-xl p-6 hidden">
                        <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Select Plan
                        </h4>
                        <div id="planSelection" class="space-y-3">
                            <!-- Plan cards will be populated here -->
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-100">
                        <button type="button" onclick="closeServiceModal()" class="crypto-button-ghost">
                            <span class="button-content flex items-center justify-center">
                                <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </span>
                        </button>

                        <div class="flex items-center space-x-3">
                            <!-- Template Update Button (shows if user has existing configs) -->
                            <button type="button" id="updateTemplateButton" onclick="updateTemplateOnly()"
                                class="crypto-button crypto-button-secondary hidden" disabled>
                                <span class="button-content flex items-center justify-center">
                                    <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Update Template
                                </span>
                            </button>

                            <button type="submit" id="generateButton" disabled class="crypto-button crypto-button-success">
                                <span class="button-content flex items-center justify-center">
                                    <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    Generate Configuration (Auto-creates on all mapped servers)
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Result Modal -->
    <div id="resultModal" class="fixed inset-0 crypto-modal overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-6 crypto-modal-content w-11/12 max-w-lg">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold glow-text">VPN Configuration</h3>
                    <button type="button" class="text-gray-400 hover:text-crypto-accent transition-colors rounded-lg p-2 hover:bg-gray-100" onclick="closeResultModal()">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="configResult" class="space-y-4">
                    <!-- Configuration result will be displayed here -->
                </div>

                <div class="flex justify-end mt-6">
                </div>
            </div>
        </div>
    </div>

    <!-- Redemption History Modal -->
    <div id="historyModal" class="fixed inset-0 crypto-modal overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-6 crypto-modal-content w-11/12 max-w-2xl max-h-screen overflow-y-auto">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold glow-text flex items-center">
                        <svg class="w-6 h-6 mr-2 text-crypto-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Redemption History
                    </h3>
                    <button type="button" class="text-gray-400 hover:text-crypto-accent transition-colors rounded-lg p-2 hover:bg-gray-100" onclick="closeHistoryModal()">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="historyContent" class="space-y-4">
                    <!-- History content will be populated here -->
                </div>

                <div class="mt-6 flex flex-col sm:flex-row sm:justify-between gap-3">
                    <div class="flex flex-col sm:flex-row gap-2">
                        <button onclick="exportHistory()" class="crypto-button crypto-button-secondary crypto-button-sm">
                            <span class="button-content flex items-center justify-center space-x-2">
                                <svg class="button-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>Export</span>
                            </span>
                        </button>
                        <button onclick="clearHistoryFromModal()" class="crypto-button crypto-button-danger crypto-button-sm">
                            <span class="button-content flex items-center justify-center space-x-2">
                                <svg class="button-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                <span>Clear All</span>
                            </span>
                        </button>
                    </div>
                    <button onclick="closeHistoryModal()" class="crypto-button-ghost crypto-button-sm">
                        <span class="button-content">Close</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        /*
         * UUID-Centric VPN Configuration Workflow Implementation
         *
         * This implementation supports three main workflows:
         *
         * 1. NEW UUID CREATION (new_uuid):
         *    - Query all mapping servers with tags related to selected var_sku
         *    - Generate single UUID and create it across ALL related servers automatically
         *    - Present selection options for telco and plan (telco locked after selection)
         *    - Server selection is only for displaying the vless config template to user
         *    - UUID is created on all servers regardless of which server user selects for display
         *
         * 2. UUID RENEWAL (uuid_renewal):
         *    - Maintain same UUID value
         *    - Allow plan changes but keep telco locked to original selection
         *    - Update only template configuration, not core UUID
         *
         * 3. VIEW CONFIGURATION (view_config):
         *    - Display existing UUID to user
         *    - Allow plan changes while keeping telco locked
         *    - Only modify template parameters, not underlying UUID
         *
         * Core Principle:
         * The UUID serves as the primary identifier that binds the Shopee username
         * to the VPN service. The vless configuration template dynamically populates with:
         * - Server address (based on user's server selection)
         * - Telco-specific plan configuration (locked after initial selection)
         * - The persistent UUID
         *
         * Technical Constraints:
         * - Telco locking enforced after initial selection
         * - Plan changes only update template variables, not create new UUIDs
         * - All server-side UUID operations happen automatically when new UUID is created
         */

        // Enhanced Button Interaction Functions
        function setButtonLoading(button, loading = true) {
            if (loading) {
                button.classList.add('crypto-button-loading');
                button.disabled = true;
            } else {
                button.classList.remove('crypto-button-loading');
                button.disabled = false;
            }
        }

        function addRippleEffect(e) {
            const button = e.currentTarget;
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            button.style.setProperty('--x', x + 'px');
            button.style.setProperty('--y', y + 'px');
            
            button.classList.add('crypto-button-ripple');
            setTimeout(() => {
                button.classList.remove('crypto-button-ripple');
            }, 600);
        }

        function enhanceButtonInteractions() {
            // Add ripple effect to all crypto buttons
            const buttons = document.querySelectorAll('.crypto-button, .crypto-button-ghost, .crypto-button-secondary, .crypto-button-success, .crypto-button-danger');
            buttons.forEach(button => {
                button.addEventListener('click', addRippleEffect);
                
                // Improve accessibility
                if (!button.getAttribute('aria-label') && !button.getAttribute('title')) {
                    const buttonText = button.textContent.trim();
                    if (buttonText) {
                        button.setAttribute('aria-label', buttonText);
                    }
                }
                
                // Add keyboard support
                button.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        addRippleEffect(e);
                        this.click();
                    }
                });
            });

            // Add enhanced focus states
            buttons.forEach(button => {
                button.addEventListener('focus', function() {
                    this.style.outline = '3px solid rgba(59, 130, 246, 0.3)';
                    this.style.outlineOffset = '2px';
                    this.style.transform = 'scale(1.02)';
                });
                
                button.addEventListener('blur', function() {
                    this.style.outline = 'none';
                    this.style.transform = '';
                });
            });

            // Add loading state announcements for screen readers
            const observeLoadingButtons = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.target.classList.contains('crypto-button-loading')) {
                        mutation.target.setAttribute('aria-busy', 'true');
                        mutation.target.setAttribute('aria-live', 'polite');
                    } else {
                        mutation.target.setAttribute('aria-busy', 'false');
                        mutation.target.removeAttribute('aria-live');
                    }
                });
            });

            buttons.forEach(button => {
                observeLoadingButtons.observe(button, { attributes: true, attributeFilter: ['class'] });
            });
        }

        function showButtonSuccess(button, originalText, successText = 'Success!') {
            const originalContent = button.innerHTML;
            button.innerHTML = `
                <span class="button-content flex items-center justify-center">
                    <svg class="button-icon w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    ${successText}
                </span>
            `;
            button.classList.add('crypto-button-success');
            
            setTimeout(() => {
                button.innerHTML = originalContent;
                button.classList.remove('crypto-button-success');
            }, 2000);
        }

        function showButtonError(button, originalText, errorText = 'Error!') {
            const originalContent = button.innerHTML;
            button.innerHTML = `
                <span class="button-content flex items-center justify-center">
                    <svg class="button-icon w-5 h-5 mr-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    ${errorText}
                </span>
            `;
            button.classList.add('crypto-button-danger');
            
            setTimeout(() => {
                button.innerHTML = originalContent;
                button.classList.remove('crypto-button-danger');
            }, 2000);
        }

        // Global variables
        let currentUserData = null;
        let selectedTelco = null;
        let selectedPlan = null;
        let availableTelcos = {};
        let currentWorkflowType = null; // 'new_uuid', 'uuid_renewal', 'view_config'
        let lockedTelco = null; // For telco locking in renewal/view workflows
        let currentUUID = null; // Current UUID for renewal/view workflows
        let currentServerInfo = null; // Store current server information for display

        // Get server display information with order details and SKU mapping
        function getServerDisplayInfo() {
            const serverSelect = document.getElementById('serverSelect');
            let serverInfo = 'Auto';

            if (serverSelect && serverSelect.selectedIndex >= 0) {
                const selectedOption = serverSelect.options[serverSelect.selectedIndex];
                if (selectedOption && selectedOption.value && selectedOption.value !== '') {
                    serverInfo = selectedOption.text;
                }
            }

            // If we have current server info stored, use it
            if (currentServerInfo) {
                serverInfo = currentServerInfo.name || serverInfo;
            }

            // Add order and SKU context if available
            const orderContext = [];
            if (currentUserData) {
                if (currentUserData.order_sn) {
                    orderContext.push(`Order: ${currentUserData.order_sn}`);
                }
                if (currentUserData.var_sku) {
                    orderContext.push(`SKU: ${currentUserData.var_sku}`);
                }
            }

            // Add server tags if available
            const tagsDisplay = document.getElementById('autoTagsDisplay');
            if (tagsDisplay && tagsDisplay.textContent && tagsDisplay.textContent !== 'Loading...' && tagsDisplay.textContent !== 'Default tags') {
                orderContext.push(`Tags: ${tagsDisplay.textContent}`);
            }

            if (orderContext.length > 0) {
                return `${serverInfo} (${orderContext.join(', ')})`;
            }

            return serverInfo;
        }

        // Load telcos data via AJAX to avoid JSON parsing issues
        async function loadTelcosData() {
            try {
                const response = await axios.get('/vpn-config-generator/api/telcos');
                if (response.data.success) {
                    availableTelcos = response.data.telcos;
                    console.log('✅ Telcos data loaded successfully:', Object.keys(availableTelcos));
                } else {
                    console.error('❌ Failed to load telcos data:', response.data.error);
                    availableTelcos = {};
                }
            } catch (error) {
                console.error('❌ Error loading telcos data:', error);
                availableTelcos = {};
            }
        }

        // LocalStorage keys
        const HISTORY_KEY = 'vpn_redemption_history';
        const STATS_KEY = 'vpn_redemption_stats';

        // Initialize history on page load
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 VPN Config page loaded - initializing...');
            
            // Verify critical elements exist
            const orderForm = document.getElementById('orderForm');
            const orderId = document.getElementById('orderId');
            const verificationModal = document.getElementById('verificationModal');
            const historyModal = document.getElementById('historyModal');
            
            if (!orderForm || !orderId || !verificationModal || !historyModal) {
                console.error('Critical page elements missing:', {
                    orderForm: !!orderForm,
                    orderId: !!orderId,
                    verificationModal: !!verificationModal,
                    historyModal: !!historyModal
                });
                showAlert('Page loading error: Critical elements missing. Please refresh the page.', 'error');
                return;
            }
            
            // Load telcos data first
            await loadTelcosData();
            
            testLocalStorage();
            updateHistoryCount();
            initializeFormListeners();
            enhanceButtonInteractions();
            
            // Add test button for debugging (remove in production)
            if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
                const testBtn = document.createElement('button');
                testBtn.innerHTML = '🧪 Test Save';
                testBtn.className = 'fixed bottom-4 left-4 z-50 px-3 py-2 bg-purple-500 text-white text-xs rounded';
                testBtn.onclick = function() {
                    const testOrder = {
                        order_sn: 'TEST-' + Date.now(),
                        buyer_username: 'Test User',
                        var_sku: 'TEST_SKU_30',
                        user_uuid: 'test-uuid'
                    };
                    const testConfig = {
                        telco: 'celcom',
                        plan: 'unlimited',
                        server: 'auto',
                        days: '30'
                    };
                    const saved = saveToHistory(testOrder, testConfig);
                    if (saved) {
                        showAlert('🧪 Test data saved successfully!', 'success');
                    }
                };
                document.body.appendChild(testBtn);
            }
        });

        // Form event listeners - Fix for backdrop issue
        function initializeFormListeners() {
            const orderForm = document.getElementById('orderForm');
            if (orderForm) {
                // Remove existing listeners first
                orderForm.removeEventListener('submit', handleFormSubmit);
                // Add fresh listener
                orderForm.addEventListener('submit', handleFormSubmit);
                console.log('✅ Form listeners initialized successfully');
            } else {
                console.error('❌ Order form not found during listener initialization');
                showAlert('Form initialization error: Order form not found.', 'error');
            }
        }

        async function handleFormSubmit(e) {
            e.preventDefault();
            const orderId = document.getElementById('orderId').value.trim();

            if (!orderId) {
                showAlert('Please enter an order ID', 'warning');
                return;
            }

            await verifyOrder(orderId);
        }

        // LocalStorage functions
        function saveToHistory(orderData, configData) {
            console.log('🔄 saveToHistory called with:', { orderData, configData });
            try {
                let history = getHistory();
                const historyItem = {
                    id: Date.now(),
                    timestamp: new Date().toISOString(),
                    orderId: orderData.order_sn || orderData.orderId || orderData.order_id || orderData.orderSn || 'Unknown',
                    customer: orderData.buyer_username || 'Unknown',
                    sku: orderData.var_sku || orderData.sku || 'Unknown',
                    telco: configData?.telco || selectedTelco || 'N/A',
                    plan: configData?.plan || selectedPlan || 'N/A',
                    server: configData?.server || getServerDisplayInfo(),
                    validity: configData?.days || document.getElementById('daysInput')?.value || 'N/A',
                    status: 'Success',
                    userUuid: orderData.user_uuid || 'N/A'
                };

                console.log('💾 Saving history item:', historyItem);
                history.unshift(historyItem); // Add to beginning
                
                // Keep only last 50 records
                if (history.length > 50) {
                    history = history.slice(0, 50);
                }

                localStorage.setItem(HISTORY_KEY, JSON.stringify(history));
                console.log('✅ History saved successfully. Total records:', history.length);
                
                updateHistoryCount();
                updateStats();
                
                return true;
            } catch (error) {
                console.error('❌ Error saving to history:', error);
                showAlert('Failed to save to history: ' + error.message, 'error');
                return false;
            }
        }

        // Test function to verify localStorage is working
        function testLocalStorage() {
            try {
                const testKey = 'test_vpn_storage';
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem(testKey, JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                localStorage.removeItem(testKey);
                
                if (retrieved && retrieved.test === 'data') {
                    console.log('✅ LocalStorage is working properly');
                    return true;
                } else {
                    console.error('❌ LocalStorage test failed - data mismatch');
                    return false;
                }
            } catch (error) {
                console.error('❌ LocalStorage test failed:', error);
                showAlert('LocalStorage is not available or blocked', 'error');
                return false;
            }
        }

        function getHistory() {
            try {
                const history = localStorage.getItem(HISTORY_KEY);
                return history ? JSON.parse(history) : [];
            } catch (error) {
                console.error('Error reading history:', error);
                return [];
            }
        }

        function clearHistory() {
            if (confirm('🗑️ Are you sure you want to clear all redemption history? This action cannot be undone.')) {
                localStorage.removeItem(HISTORY_KEY);
                localStorage.removeItem(STATS_KEY);
                updateHistoryCount();
                showAlert('History cleared successfully', 'success');
            }
        }

        function clearHistoryFromModal() {
            if (confirm('🗑️ Are you sure you want to clear all redemption history? This action cannot be undone.')) {
                localStorage.removeItem(HISTORY_KEY);
                localStorage.removeItem(STATS_KEY);
                updateHistoryCount();
                closeHistoryModal();
                showAlert('✅ History cleared successfully', 'success');
            }
        }

        function updateHistoryCount() {
            const history = getHistory();
            const historyButtons = document.querySelectorAll('[onclick="showHistoryModal()"]');
            historyButtons.forEach(button => {
                const countSpan = button.querySelector('.history-count');
                if (countSpan) {
                    countSpan.textContent = history.length;
                } else if (history.length > 0) {
                    const span = document.createElement('span');
                    span.className = 'history-count ml-1 px-2 py-1 bg-gray-800 text-white text-xs rounded-full font-semibold';
                    span.textContent = history.length;
                    button.appendChild(span);
                }
            });
        }

        function updateStats() {
            try {
                const history = getHistory();
                const stats = {
                    totalRedemptions: history.length,
                    lastUpdate: new Date().toISOString(),
                    telcoBreakdown: {},
                    monthlyStats: {}
                };

                // Calculate telco breakdown
                history.forEach(item => {
                    if (item.telco && item.telco !== 'N/A') {
                        stats.telcoBreakdown[item.telco] = (stats.telcoBreakdown[item.telco] || 0) + 1;
                    }
                });

                localStorage.setItem(STATS_KEY, JSON.stringify(stats));
            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        // History modal functions
        function showHistoryModal() {
            const modal = document.getElementById('historyModal');
            const content = document.getElementById('historyContent');
            
            // Check if modal elements exist
            if (!modal || !content) {
                console.error('Missing required elements for history modal');
                showAlert('Error: History modal elements not found. Please refresh the page.', 'error');
                return;
            }
            
            const history = getHistory();

            if (history.length === 0) {
                content.innerHTML = `
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">No History Found</h3>
                        <p class="text-gray-500">Your redemption history will appear here after you complete your first order.</p>
                    </div>
                `;
            } else {
                let html = `
                    <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Statistics
                        </h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-blue-600">Total Redemptions:</span>
                                <span class="font-medium">${history.length}</span>
                            </div>
                            <div>
                                <span class="text-blue-600">Last Activity:</span>
                                <span class="font-medium">${new Date(history[0].timestamp).toLocaleDateString()}</span>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                `;

                history.forEach((item, index) => {
                    const date = new Date(item.timestamp);
                    const timeAgo = getTimeAgo(date);
                    
                    html += `
                        <div class="info-card p-4 ${index === 0 ? 'border-l-4 border-crypto-green' : ''}">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h4 class="font-semibold text-gray-900 flex items-center">
                                        <svg class="w-4 h-4 mr-2 text-crypto-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        ${item.orderId}
                                        ${index === 0 ? '<span class="ml-2 px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">Latest</span>' : ''}
                                    </h4>
                                    <p class="text-sm text-gray-600">${item.customer}</p>
                                </div>
                                <span class="text-xs text-gray-500">${timeAgo}</span>
                            </div>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">SKU:</span>
                                    <span class="font-medium">${item.sku}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Telco:</span>
                                    <span class="font-medium text-crypto-accent">${item.telco}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Plan:</span>
                                    <span class="font-medium">${item.plan}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Validity:</span>
                                    <span class="font-medium">${item.validity} days</span>
                                </div>
                            </div>
                            <div class="mt-3 flex justify-between items-center">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-green-600 text-sm font-medium">${item.status}</span>
                                </div>
                                <button onclick="copyOrderId('${item.orderId}')" class="text-crypto-accent hover:text-crypto-accent-dark text-sm">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    Copy ID
                                </button>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
                content.innerHTML = html;
            }

            // Show the modal with proper animation
            modal.classList.remove('hidden');
            modal.classList.add('modal-enter');
            setTimeout(() => {
                modal.classList.remove('modal-enter');
                modal.classList.add('modal-enter-active');
            }, 10);
            
            console.log('History modal displayed successfully with', history.length, 'items');
        }

        function closeHistoryModal() {
            const modal = document.getElementById('historyModal');
            modal.classList.add('modal-exit');
            setTimeout(() => {
                modal.classList.add('modal-exit-active');
                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.classList.remove('modal-exit', 'modal-exit-active');
                }, 200);
            }, 10);
        }

        // Utility functions
        function getTimeAgo(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;
            return date.toLocaleDateString();
        }

        function copyOrderId(orderId) {
            const copyButton = event.target.closest('button');
            const originalText = copyButton.innerHTML;
            
            navigator.clipboard.writeText(orderId).then(() => {
                showButtonSuccess(copyButton, originalText, '✓ Copied!');
                showAlert('Order ID copied to clipboard!', 'success');
            }).catch(() => {
                showButtonError(copyButton, originalText, 'Failed!');
                showAlert('Failed to copy Order ID', 'error');
            });
        }

        function exportHistory() {
            const history = getHistory();
            if (history.length === 0) {
                showAlert('No history to export', 'warning');
                return;
            }

            const dataStr = JSON.stringify(history, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `vpn_redemption_history_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
            
            showAlert('History exported successfully!', 'success');
        }

        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white text-sm max-w-sm transition-all duration-300 transform`;
            
            switch(type) {
                case 'success':
                    alertDiv.className += ' bg-green-500';
                    break;
                case 'error':
                    alertDiv.className += ' bg-red-500';
                    break;
                case 'warning':
                    alertDiv.className += ' bg-orange-500';
                    break;
                default:
                    alertDiv.className += ' bg-blue-500';
            }

            alertDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    ${message}
                </div>
            `;

            document.body.appendChild(alertDiv);

            // Animate in
            setTimeout(() => {
                alertDiv.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                alertDiv.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 300);
            }, 3000);
        }

        // Form submission - removed to prevent duplicate listeners

        async function verifyOrder(orderId) {
            showVerificationModal();

            try {
                const response = await axios.post('/vpn-config-generator/api/order/verify', {
                    order_sn: orderId
                });

                if (response.data.success) {
                    currentUserData = response.data;

                    // Debug logging to help identify order field issues
                    console.log('Order verification successful. User data received:', currentUserData);
                    console.log('Available order fields:', {
                        order_sn: currentUserData.order_sn,
                        orderId: currentUserData.orderId,
                        order_id: currentUserData.order_id,
                        orderSn: currentUserData.orderSn
                    });

                    // Note: Order shipping is automatically handled by the backend during verification
                    // No need to make a separate ship order API call from frontend

                    showVerificationSuccess();
                } else {
                    showVerificationError(response.data.error);
                }
            } catch (error) {
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                showVerificationError(errorMessage);
            }
        }

        function resetVerificationModalContent() {
            const verificationContent = document.getElementById('verificationContent');
            if (verificationContent) {
                verificationContent.innerHTML = `
                    <!-- Loading State -->
                    <div id="loadingIndicator" class="flex flex-col items-center">
                        <!-- Modern Loading Spinner -->
                        <div class="relative w-16 h-16 mb-6">
                            <div class="absolute inset-0 w-16 h-16 border-4 border-gray-200 rounded-full"></div>
                            <div class="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full animate-spin"></div>
                            <div class="absolute inset-2 w-12 h-12 border-2 border-transparent border-t-purple-400 border-l-blue-400 rounded-full animate-spin animation-delay-150"></div>
                        </div>
                    </div>

                    <!-- Success State -->
                    <div id="successIndicator" class="hidden">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center mx-auto mb-4 pulse-glow">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div id="errorIndicator" class="hidden">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>

                    <h3 class="text-xl font-semibold glow-text mb-2" id="verificationTitle">Verifying Order</h3>
                    <div class="mb-6">
                        <p class="text-gray-600" id="verificationMessage">
                            Please wait while we verify your order...
                        </p>
                    </div>
                    <div>
                        <button id="proceedButton" type="button"
                            class="hidden crypto-button crypto-button-success transition duration-200"
                            onclick="showServiceSelection()">
                            <span class="button-content flex items-center justify-center">
                                <svg class="button-icon w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                                Proceed to Service Selection
                            </span>
                        </button>
                    </div>
                `;
            }
        }

        function showVerificationModal() {
            // Reset modal content to ensure all elements exist
            resetVerificationModalContent();

            const modal = document.getElementById('verificationModal');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const successIndicator = document.getElementById('successIndicator');
            const errorIndicator = document.getElementById('errorIndicator');
            const title = document.getElementById('verificationTitle');
            const message = document.getElementById('verificationMessage');
            const proceedButton = document.getElementById('proceedButton');

            // Check if all required elements exist
            if (!modal || !loadingIndicator || !successIndicator || !errorIndicator || !title || !message || !proceedButton) {
                console.error('Missing required modal elements for verification modal');
                showAlert('Error: Modal elements not found. Please refresh the page.', 'error');
                return;
            }

            // Reset state
            loadingIndicator.classList.remove('hidden');
            successIndicator.classList.add('hidden');
            errorIndicator.classList.add('hidden');
            proceedButton.classList.add('hidden');

            title.textContent = 'Verifying Order';
            message.textContent = 'Please wait while we verify your order...';

            modal.classList.remove('hidden');
            modal.classList.add('modal-enter');
            setTimeout(() => {
                modal.classList.remove('modal-enter');
                modal.classList.add('modal-enter-active');
            }, 10);
        }

        function showVerificationSuccess() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const successIndicator = document.getElementById('successIndicator');
            const title = document.getElementById('verificationTitle');
            const message = document.getElementById('verificationMessage');
            const proceedButton = document.getElementById('proceedButton');

            // Check if all required elements exist
            if (!loadingIndicator || !successIndicator || !title || !message || !proceedButton) {
                console.error('Missing required elements for verification success');
                showAlert('Error: Verification elements not found. Please refresh the page.', 'error');
                return;
            }

            loadingIndicator.classList.add('hidden');
            successIndicator.classList.remove('hidden');
            title.textContent = 'Order Verified';

            let messageText = currentUserData.message;
            if (currentUserData.is_repeat_customer) {
                messageText += ' Welcome back!';
            }
            message.textContent = messageText;

            // Show action selection for repeat customers
            if (currentUserData.is_repeat_customer) {
                showActionSelection();
            } else {
                proceedButton.classList.remove('hidden');
            }
        }

        function showActionSelection() {
            const verificationContent = document.getElementById('verificationContent');

            // Debug logging
            console.log('showActionSelection called with:', {
                configurations_generated: currentUserData.configurations_generated,
                is_repeat_customer: currentUserData.is_repeat_customer,
                order_claimed: currentUserData.order_claimed,
                assigned_telco: currentUserData.assigned_telco,
                message: currentUserData.message
            });

            // Check if order is claimed (has generated configurations)
            const isOrderClaimed = currentUserData.order_claimed || (currentUserData.configurations_generated && currentUserData.configurations_generated > 0);

            if (isOrderClaimed) {
                // Order is claimed - only show view options, NO new config creation or renewals
                verificationContent.innerHTML = `
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">❌ Order Already Claimed</h3>
                    <div class="mt-2 px-7 py-3">
                        <div class="bg-red-50 p-4 rounded-lg mb-4">
                            <p class="text-sm font-medium text-red-800 mb-2">🚫 Access Restricted</p>
                            <p class="text-xs text-red-600">This order has been claimed and ${currentUserData.configurations_generated || 0} configuration(s) generated.</p>
                            <p class="text-xs text-red-600 mt-1">No new configurations or renewals are allowed.</p>
                        </div>
                        ${currentUserData.assigned_telco ? `
                            <div class="bg-blue-50 p-3 rounded-lg mb-4">
                                <p class="text-sm font-medium text-blue-800">🔒 Telco Lock</p>
                                <p class="text-xs text-blue-600">Locked to <strong>${currentUserData.assigned_telco.toUpperCase()}</strong> telco</p>
                            </div>
                        ` : ''}
                        <p class="text-sm font-medium text-gray-700 mb-4">Available actions:</p>
                    </div>
                    <div class="flex flex-col space-y-3 px-7">
                        <button type="button" onclick="showExistingConfigs()" 
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-md transition duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            View Your Configurations Only
                        </button>
                        <div class="text-center">
                            <p class="text-xs text-gray-500 italic">Configuration creation and renewals are disabled for claimed orders</p>
                        </div>
                    </div>
                `;
            } else {
                // Order not claimed yet - show all options for new customers
                verificationContent.innerHTML = `
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">${currentUserData.is_repeat_customer ? 'Welcome Back!' : 'Order Verified'}</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500 mb-4">
                            ${currentUserData.message}
                        </p>
                        <p class="text-sm font-medium text-gray-700 mb-4">What would you like to do?</p>
                    </div>
                    <div class="flex flex-col space-y-3 px-7">
                        ${currentUserData.is_repeat_customer ? `
                            <button type="button" onclick="showRenewalSelection()" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md transition duration-200 flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Renew Existing Configuration
                            </button>
                            <button type="button" onclick="showExistingConfigs()" 
                                class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-md transition duration-200 flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                View Existing Configurations
                            </button>
                        ` : ''}
                        <button type="button" onclick="showServiceSelection()" 
                            class="crypto-button crypto-button-success w-full">
                            <span class="button-content flex items-center justify-center">
                                <svg class="button-icon w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create New Configuration
                            </span>
                        </button>
                    </div>
                `;
            }
        }

        function showVerificationError(errorMessage) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const errorIndicator = document.getElementById('errorIndicator');
            const title = document.getElementById('verificationTitle');
            const message = document.getElementById('verificationMessage');

            // Check if all required elements exist
            if (!loadingIndicator || !errorIndicator || !title || !message) {
                console.error('Missing required elements for verification error');
                showAlert('Error: Verification error elements not found. Please refresh the page.', 'error');
                return;
            }

            loadingIndicator.classList.add('hidden');
            errorIndicator.classList.remove('hidden');
            title.textContent = 'Verification Failed';
            message.textContent = errorMessage;
        }

        function showServiceSelection() {
            hideVerificationModal();

            // Determine workflow type based on user data
            determineWorkflowType();

            // Populate user info with better fallback handling
            console.log('Current user data for order display:', currentUserData);
            console.log('Workflow type determined:', currentWorkflowType);

            // Try multiple possible field names for order
            const orderDisplay = currentUserData.order_sn ||
                                currentUserData.orderId ||
                                currentUserData.order_id ||
                                currentUserData.orderSn ||
                                'N/A';

            console.log('Order display value:', orderDisplay);

            // Check if user info elements exist
            const displayOrderSn = document.getElementById('displayOrderSn');
            const displayBuyerUsername = document.getElementById('displayBuyerUsername');
            const displaySku = document.getElementById('displaySku');

            if (!displayOrderSn || !displayBuyerUsername || !displaySku) {
                console.error('Missing user info display elements');
                showAlert('Error: User info elements not found. Please refresh the page.', 'error');
                return;
            }

            displayOrderSn.textContent = orderDisplay;
            displayBuyerUsername.textContent = currentUserData.buyer_username || 'N/A';
            displaySku.textContent = currentUserData.var_sku || currentUserData.sku || 'N/A';

            // Display validity and status based on workflow type
            const displayValidity = document.getElementById('displayValidity');
            const statusElement = document.getElementById('displayStatus');

            if (!statusElement) {
                console.error('Missing status display element');
                showAlert('Error: Display elements not found. Please refresh the page.', 'error');
                return;
            }

            // Only update validity if element exists
            if (displayValidity) {
                displayValidity.textContent = '30 days'; // Will be updated by loadAutoSuggestions
            }

            // Update status based on workflow type
            if (currentWorkflowType === 'new_uuid') {
                statusElement.textContent = 'New UUID Creation';
                statusElement.className = 'status-badge status-available';
            } else if (currentWorkflowType === 'uuid_renewal') {
                statusElement.textContent = 'UUID Renewal';
                statusElement.className = 'status-badge status-claimed';
            } else if (currentWorkflowType === 'view_config') {
                statusElement.textContent = 'View Configuration';
                statusElement.className = 'status-badge status-claimed';
            }

            // Show workflow type and UUID information
            const workflowDisplay = document.getElementById('workflowTypeDisplay');
            const workflowTypeElement = document.getElementById('displayWorkflowType');
            const uuidDisplay = document.getElementById('uuidDisplay');
            const uuidElement = document.getElementById('displayUUID');

            if (workflowDisplay && workflowTypeElement) {
                workflowTypeElement.textContent = currentWorkflowType?.replace('_', ' ').toUpperCase() || 'Standard';
                workflowDisplay.style.display = 'flex';
            }

            if (uuidDisplay && uuidElement && currentUUID) {
                uuidElement.textContent = currentUUID;
                uuidDisplay.style.display = 'flex';
            }

            // Show workflow-specific restriction info
            showWorkflowRestrictions();

            // Load auto-suggestions based on SKU and workflow
            loadAutoSuggestions();

            // Populate telco selection based on workflow type
            populateTelcoSelectionByWorkflow();

            // Update service modal title based on workflow type
            updateServiceModalTitle();

            // Show service modal
            const modal = document.getElementById('serviceModal');
            modal.classList.remove('hidden');
        }

        function updateServiceModalTitle() {
            const titleElement = document.getElementById('serviceModalTitle');
            const subtitleElement = document.getElementById('serviceModalSubtitle');
            if (!titleElement) return;

            let title = 'Select VPN Service';
            let subtitle = 'Configure your VPN connection settings';

            if (currentWorkflowType === 'new_uuid') {
                title = 'Create New UUID Configuration';
                subtitle = 'Your UUID will be created on all related servers automatically';
            } else if (currentWorkflowType === 'uuid_renewal') {
                title = 'Renew UUID Configuration';
                subtitle = 'Update your existing UUID with new plan settings';
            } else if (currentWorkflowType === 'view_config') {
                title = 'Change VPN Configuration';
                subtitle = 'Update your configuration template and server selection';
            }

            titleElement.textContent = title;
            if (subtitleElement) {
                subtitleElement.textContent = subtitle;
            }

            // Show current configuration for renewal/view workflows
            const currentConfigDisplay = document.getElementById('currentConfigDisplay');
            if (currentConfigDisplay && (currentWorkflowType === 'uuid_renewal' || currentWorkflowType === 'view_config')) {
                currentConfigDisplay.classList.remove('hidden');

                // Populate current configuration data
                const currentTelcoElement = document.getElementById('currentTelco');
                const currentPlanElement = document.getElementById('currentPlan');
                const currentUUIDElement = document.getElementById('currentUUIDDisplay');

                if (currentTelcoElement && lockedTelco) {
                    currentTelcoElement.textContent = lockedTelco.toUpperCase();
                }
                if (currentPlanElement && selectedPlan) {
                    currentPlanElement.textContent = selectedPlan;
                }
                if (currentUUIDElement && currentUUID) {
                    currentUUIDElement.textContent = currentUUID;
                }
            } else if (currentConfigDisplay) {
                currentConfigDisplay.classList.add('hidden');
            }
        }

        async function loadAutoSuggestions() {
            try {
                const sku = currentUserData.var_sku || currentUserData.sku;

                // Show loading state
                document.getElementById('autoValidityDisplay').textContent = 'Loading...';
                document.getElementById('autoTagsDisplay').textContent = 'Loading...';

                const response = await axios.post('/vpn-config-generator/api/order/get-suggestions', {
                    sku: sku
                });

                if (response.data.success) {
                    const suggestions = response.data.suggestions;

                    // Update display
                    document.getElementById('autoValidityDisplay').textContent =
                        `${suggestions.auto_validity_days} days`;
                    document.getElementById('autoTagsDisplay').textContent =
                        suggestions.server_tags.join(', ') || 'Default tags';

                    // Update order info validity display (if element exists)
                    const displayValidityElement = document.getElementById('displayValidity');
                    if (displayValidityElement) {
                        displayValidityElement.textContent = `${suggestions.auto_validity_days} days`;
                    }

                    // Set hidden form values
                    document.getElementById('daysInput').value = suggestions.auto_validity_days || 30;

                    // Populate server dropdown with all matching servers
                    await populateServerDropdown(suggestions.server_tags);

                    // Update generate button state
                    updateGenerateButton();

                } else {
                    throw new Error(response.data.error || 'Failed to load suggestions');
                }
            } catch (error) {
                console.error('Error loading auto suggestions:', error);
                console.error('Current user data:', currentUserData);

                // Try to extract validity from SKU as fallback
                let fallbackValidity = 30;
                let fallbackTags = [];
                const sku = currentUserData.var_sku || currentUserData.sku;

                if (sku) {
                    // Simple regex extraction as fallback
                    const match = sku.match(/_(\d+)$/);
                    if (match) {
                        fallbackValidity = parseInt(match[1]);
                        console.log(`Extracted validity from SKU ${sku}: ${fallbackValidity} days`);
                    }

                    // Try to determine fallback tags based on SKU prefix
                    const skuLower = sku.toLowerCase();
                    if (skuLower.startsWith('sg_')) {
                        fallbackTags = ['singapore', 'digitalocean'];
                    } else if (skuLower.startsWith('my_')) {
                        fallbackTags = ['malaysia', 'shinjiru'];
                    } else {
                        fallbackTags = ['malaysia', 'shinjiru']; // Default fallback
                    }
                }

                // Fallback values
                document.getElementById('autoValidityDisplay').textContent = `${fallbackValidity} days`;
                document.getElementById('autoTagsDisplay').textContent = fallbackTags.join(', ') || 'Default tags';
                document.getElementById('daysInput').value = fallbackValidity;

                // Try to populate servers with fallback tags
                if (fallbackTags.length > 0) {
                    await populateServerDropdown(fallbackTags);
                } else {
                    // Last resort fallback with SKU context
                    const serverSelect = document.getElementById('serverSelect');
                    const sku = currentUserData?.var_sku || currentUserData?.sku || 'Unknown';
                    serverSelect.innerHTML = `<option value="">No server mapping for SKU: ${sku}</option><option value="auto">Auto-select optimal server</option>`;
                    serverSelect.value = '';
                    document.getElementById('serverInput').value = '';
                }

                updateGenerateButton();
            }
        }

        // UUID-centric workflow functions
        function determineWorkflowType() {
            // Determine workflow based on user data and existing configurations
            if (currentUserData.is_repeat_customer && currentUserData.configurations_generated > 0) {
                if (currentUserData.order_claimed) {
                    // Order is claimed - only allow viewing configurations
                    currentWorkflowType = 'view_config';
                    lockedTelco = currentUserData.assigned_telco;
                    currentUUID = currentUserData.user_uuid;
                } else {
                    // Repeat customer but order not claimed - allow renewal
                    currentWorkflowType = 'uuid_renewal';
                    lockedTelco = currentUserData.assigned_telco;
                    currentUUID = currentUserData.user_uuid;
                }
            } else {
                // New customer or no existing configurations - create new UUID
                currentWorkflowType = 'new_uuid';
                lockedTelco = null;
                currentUUID = null;
            }

            console.log('🔄 Workflow determined:', {
                type: currentWorkflowType,
                lockedTelco: lockedTelco,
                currentUUID: currentUUID,
                isRepeatCustomer: currentUserData.is_repeat_customer,
                configurationsGenerated: currentUserData.configurations_generated,
                orderClaimed: currentUserData.order_claimed,
                willCreateMultipleUUIDs: currentWorkflowType === 'new_uuid'
            });
        }

        function showWorkflowRestrictions() {
            const restrictionInfo = document.getElementById('restrictionInfo');
            const restrictionMessage = document.getElementById('restrictionMessage');

            if (!restrictionInfo || !restrictionMessage) return;

            let showRestriction = false;
            let message = '';

            if (currentWorkflowType === 'new_uuid') {
                showRestriction = true;
                message = 'UUID will be created automatically on ALL servers related to your SKU. Telco will be locked after selection.';
            } else if (currentWorkflowType === 'uuid_renewal') {
                showRestriction = true;
                message = `UUID Renewal: Telco locked to ${lockedTelco?.toUpperCase() || 'assigned telco'}. Only plan changes allowed.`;
            } else if (currentWorkflowType === 'view_config') {
                showRestriction = true;
                message = `View Configuration: Telco locked to ${lockedTelco?.toUpperCase() || 'assigned telco'}. Only template updates allowed.`;
            }

            if (showRestriction) {
                restrictionMessage.textContent = message;
                restrictionInfo.classList.remove('hidden');
            } else {
                restrictionInfo.classList.add('hidden');
            }

            // Update the UUID creation info text based on workflow
            const uuidCreationInfo = document.getElementById('uuidCreationInfo');
            if (uuidCreationInfo) {
                if (currentWorkflowType === 'new_uuid') {
                    uuidCreationInfo.textContent = 'Your UUID will be automatically created on ALL servers related to your SKU when you click "Generate Configuration".';
                } else if (currentWorkflowType === 'uuid_renewal') {
                    uuidCreationInfo.textContent = 'Your existing UUID will be renewed with updated plan settings. Server selection is for choosing which configuration template to display.';
                } else if (currentWorkflowType === 'view_config') {
                    uuidCreationInfo.textContent = 'Your existing UUID configuration will be updated with new template settings. Server selection is for choosing which configuration to display.';
                }
            }
        }

        async function queryMappingServersByVarSku(varSku) {
            // Query all mapping servers that have tags related to the selected var_sku
            try {
                const response = await axios.post('/vpn-config-generator/api/servers/query-by-sku', {
                    var_sku: varSku
                });

                if (response.data.success) {
                    return response.data.servers || [];
                } else {
                    console.error('Failed to query mapping servers:', response.data.error);
                    return [];
                }
            } catch (error) {
                console.error('Error querying mapping servers:', error);
                return [];
            }
        }

        function populateTelcoSelectionByWorkflow() {
            const container = document.getElementById('telcoSelection');
            container.innerHTML = '';

            if (!availableTelcos || Object.keys(availableTelcos).length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="spinner-crypto mx-auto mb-4"></div>
                        <p class="text-gray-600">Loading telco options...</p>
                    </div>
                `;
                return;
            }

            if (currentWorkflowType === 'uuid_renewal' || currentWorkflowType === 'view_config') {
                // For renewal/view workflows, show locked telco only
                if (lockedTelco && availableTelcos[lockedTelco]) {
                    const telco = availableTelcos[lockedTelco];
                    const card = document.createElement('div');
                    card.className = 'telco-card p-4 selected';
                    card.innerHTML = `
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-900">${telco.name}</h4>
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                <div class="w-3 h-3 rounded-full bg-orange-500"></div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">${telco.description}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-crypto-accent">${Object.keys(telco.plans || {}).length} plans available</span>
                            <div class="text-xs font-medium text-orange-600 flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                Locked
                            </div>
                        </div>
                        <p class="text-xs text-orange-600 mt-2 font-medium">🔒 Cannot be changed for ${currentWorkflowType === 'uuid_renewal' ? 'renewal' : 'view'} operations</p>
                    `;
                    container.appendChild(card);
                    selectedTelco = lockedTelco;
                    populatePlanSelection(lockedTelco);
                } else {
                    container.innerHTML = '<p class="text-red-500 text-center">Error: Locked telco not found</p>';
                }
            } else {
                // For new UUID creation, use the original logic but add workflow-specific messaging
                populateTelcoSelection();
            }
        }

        function populateTelcoSelection() {
            const container = document.getElementById('telcoSelection');
            container.innerHTML = '';

            // Check if telcos data is loaded
            if (!availableTelcos || Object.keys(availableTelcos).length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="spinner-crypto mx-auto mb-4"></div>
                        <p class="text-gray-600">Loading telco options...</p>
                    </div>
                `;
                return;
            }

            // Check if order is claimed and user has assigned telco
            const isOrderClaimed = currentUserData.order_claimed || (currentUserData.configurations_generated && currentUserData.configurations_generated > 0);

            Object.values(availableTelcos).forEach(telco => {
                if (!telco.enabled) return;

                // For claimed orders, only show assigned telco
                if (isOrderClaimed && currentUserData.assigned_telco) {
                    if (telco.id !== currentUserData.assigned_telco) {
                        return; // Skip this telco
                    }
                }

                // Check if user can access this telco (for non-claimed orders)
                const canAccess = !currentUserData.is_restricted ||
                    !currentUserData.assigned_telco ||
                    currentUserData.assigned_telco === telco.id ||
                    currentUserData.allowed_telcos.includes(telco.id);

                const card = document.createElement('div');
                card.className = `telco-card p-4 ${canAccess ? '' : 'opacity-50 cursor-not-allowed'}`;

                // Add special styling for locked telco
                if (isOrderClaimed && currentUserData.assigned_telco === telco.id) {
                    card.classList.add('selected');
                }

                card.innerHTML = `
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-900">${telco.name}</h4>
                        <div class="w-3 h-3 rounded-full bg-crypto-green ${canAccess ? 'pulse-glow' : 'opacity-50'}"></div>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">${telco.description}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-crypto-accent">${Object.keys(telco.plans || {}).length} plans available</span>
                        ${isOrderClaimed && currentUserData.assigned_telco === telco.id ?
                            '<div class="text-xs font-medium text-crypto-green flex items-center"><svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path></svg>Locked</div>' : ''}
                    </div>
                `;

                if (canAccess) {
                    card.addEventListener('click', () => selectTelco(telco.id, card));
                }

                container.appendChild(card);
            });

            // If order is claimed and user has assigned telco, auto-select it
            if (isOrderClaimed && currentUserData.assigned_telco) {
                const assignedTelcoCard = container.querySelector('.telco-card');
                if (assignedTelcoCard) {
                    setTimeout(() => {
                        assignedTelcoCard.click();
                    }, 100);
                }
            }
        }

        function selectTelco(telcoId, cardElement) {
            // For renewal/view workflows, telco is locked - don't allow changes
            if ((currentWorkflowType === 'uuid_renewal' || currentWorkflowType === 'view_config') && lockedTelco) {
                showAlert('Telco is locked for this operation type', 'warning');
                return;
            }

            // Remove previous selection
            document.querySelectorAll('.telco-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current telco
            cardElement.classList.add('selected');
            selectedTelco = telcoId;

            // For new UUID creation, warn about telco locking
            if (currentWorkflowType === 'new_uuid') {
                showAlert('⚠️ Telco will be locked after UUID creation', 'info');
            }

            // Show plan selection
            populatePlanSelection(telcoId);
            document.getElementById('planSelectionContainer').classList.remove('hidden');

            // Reset plan selection
            selectedPlan = null;
            updateGenerateButton();
        }

        function populatePlanSelection(telcoId) {
            const container = document.getElementById('planSelection');
            container.innerHTML = '';

            const telco = availableTelcos[telcoId];
            if (!telco || !telco.plans) return;

            Object.values(telco.plans).forEach(plan => {
                if (!plan.enabled) return;

                const card = document.createElement('div');
                card.className = 'plan-card p-4 cursor-pointer';
                card.innerHTML = `
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 text-base">${plan.name}</h4>
                                <p class="text-sm text-gray-600 mt-1">${plan.description}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>Click to select this plan</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                `;

                card.addEventListener('click', () => selectPlan(plan.id, card));
                container.appendChild(card);
            });
        }

        function selectPlan(planId, cardElement) {
            // Remove previous selection
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current plan
            cardElement.classList.add('selected');
            selectedPlan = planId;

            updateGenerateButton();
        }

        async function updateTemplateOnly() {
            // Template update function for existing configurations
            const server = document.getElementById('serverInput').value;
            const updateButton = document.getElementById('updateTemplateButton');
            const orderSn = currentUserData.order_sn || currentUserData.orderId || currentUserData.order_id || currentUserData.orderSn;

            // Show enhanced loading state
            const originalText = updateButton.innerHTML;
            setButtonLoading(updateButton, true);

            try {
                // Use existing generate-config endpoint with template update flag
                const response = await axios.post('/vpn-config-generator/api/order/generate-config', {
                    user_uuid: currentUserData.user_uuid,
                    order_sn: orderSn,
                    server: server,
                    days: document.getElementById('daysInput')?.value || '30',
                    telco: lockedTelco || selectedTelco,
                    plan: selectedPlan,
                    workflow_type: 'template_update',
                    existing_uuid: currentUUID,
                    template_update_only: true
                });

                if (response.data.success) {
                    // Save to history
                    const configData = {
                        telco: lockedTelco || selectedTelco,
                        plan: selectedPlan,
                        server: server,
                        workflow_type: 'template_update',
                        uuid: currentUUID
                    };
                    saveToHistory(currentUserData, configData);

                    showConfigurationResult(response.data);
                    showButtonSuccess(updateButton, originalText, 'Updated!');
                    showAlert('✅ Template configuration updated successfully!', 'success');
                } else {
                    showButtonError(updateButton, originalText, 'Failed!');
                    showAlert('❌ Error: ' + response.data.error, 'error');
                }
            } catch (error) {
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                showButtonError(updateButton, originalText, 'Error!');
                showAlert('❌ Error: ' + errorMessage, 'error');
            } finally {
                // Restore button state
                setButtonLoading(updateButton, false);
                updateButton.innerHTML = originalText;
            }
        }

        function updateGenerateButton() {
            const button = document.getElementById('generateButton');
            const templateButton = document.getElementById('updateTemplateButton');
            const server = document.getElementById('serverInput').value;
            const days = document.getElementById('daysInput').value;

            // For renewal/view workflows, use locked telco instead of selected telco
            const effectiveTelco = (currentWorkflowType === 'uuid_renewal' || currentWorkflowType === 'view_config')
                ? lockedTelco : selectedTelco;

            const hasSelections = server && days && effectiveTelco && selectedPlan;

            // Update button text based on workflow type
            if (button) {
                const buttonContent = button.querySelector('.button-content span:last-child');
                if (buttonContent) {
                    if (currentWorkflowType === 'new_uuid') {
                        buttonContent.textContent = 'Create New UUID Configuration';
                    } else if (currentWorkflowType === 'uuid_renewal') {
                        buttonContent.textContent = 'Renew UUID Configuration';
                    } else if (currentWorkflowType === 'view_config') {
                        buttonContent.textContent = 'Generate Configuration View';
                    } else {
                        buttonContent.textContent = 'Generate Configuration';
                    }
                }

                // Enable/disable generate button
                button.disabled = !hasSelections;
            }

            // Show template update button for view_config workflow or existing configurations
            if (templateButton) {
                const showTemplateButton = (currentWorkflowType === 'view_config' ||
                    (currentUserData && currentUserData.configurations_generated > 0)) && hasSelections;

                if (showTemplateButton) {
                    templateButton.classList.remove('hidden');
                    templateButton.disabled = !hasSelections;
                } else {
                    templateButton.classList.add('hidden');
                }
            }


        }

        async function populateServerDropdown(serverTags) {
            const serverSelect = document.getElementById('serverSelect');

            try {
                // Get servers that match the tags
                const response = await axios.post('/vpn-config-generator/api/servers/by-tags', {
                    tags: serverTags
                });

                if (response.data.success && response.data.servers) {
                    const servers = response.data.servers;

                    // Clear existing options
                    serverSelect.innerHTML = '';

                    if (servers.length > 0) {
                        // Add a prompt option first
                        const promptOption = document.createElement('option');
                        promptOption.value = '';
                        promptOption.textContent = `Choose from ${servers.length} available server${servers.length > 1 ? 's' : ''}`;
                        promptOption.disabled = true;
                        promptOption.selected = true;
                        serverSelect.appendChild(promptOption);

                        // Add all matching servers with enhanced display information
                        servers.forEach(server => {
                            const option = document.createElement('option');
                            option.value = server.id;

                            // Build enhanced server display name
                            let displayName = server.name || `Server ${server.id}`;
                            const details = [];

                            if (server.location) {
                                details.push(server.location);
                            }
                            if (server.tags && server.tags.length > 0) {
                                details.push(`Tags: ${server.tags.join(', ')}`);
                            }

                            if (details.length > 0) {
                                displayName += ` (${details.join(' - ')})`;
                            }

                            option.textContent = displayName;
                            serverSelect.appendChild(option);
                        });

                        // Don't auto-select any server - let user choose
                        document.getElementById('serverInput').value = '';

                        console.log(`Populated ${servers.length} servers for tags: ${serverTags.join(', ')}`);
                    } else {
                        // No servers found with matching tags - show more informative message
                        const sku = currentUserData?.var_sku || currentUserData?.sku || 'Unknown';
                        serverSelect.innerHTML = `<option value="">No servers found for SKU: ${sku}</option><option value="auto">Auto-select optimal server</option>`;
                        serverSelect.value = '';
                        document.getElementById('serverInput').value = '';
                        console.warn(`No servers found for tags: ${serverTags.join(', ')}`);
                    }
                } else {
                    throw new Error(response.data.error || 'Failed to load servers');
                }
            } catch (error) {
                console.error('Error loading servers:', error);

                // Fallback to auto selection with more context
                const sku = currentUserData?.var_sku || currentUserData?.sku || 'Unknown';
                serverSelect.innerHTML = `<option value="">Server loading failed for SKU: ${sku}</option><option value="auto">Auto-select optimal server</option>`;
                serverSelect.value = '';
                document.getElementById('serverInput').value = '';
            }

            updateGenerateButton();
        }

        // Handle server selection change
        document.addEventListener('DOMContentLoaded', function () {
            const serverSelect = document.getElementById('serverSelect');
            if (serverSelect) {
                serverSelect.addEventListener('change', function () {
                    document.getElementById('serverInput').value = this.value;

                    // Store current server info for display
                    if (this.selectedIndex >= 0 && this.options[this.selectedIndex]) {
                        const selectedOption = this.options[this.selectedIndex];
                        currentServerInfo = {
                            id: selectedOption.value,
                            name: selectedOption.text
                        };
                    }

                    updateGenerateButton();
                });
            }
        });

        // Service form submission
        document.getElementById('serviceForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await generateConfiguration();
        });



        async function generateConfiguration() {
            const server = document.getElementById('serverInput').value;
            const days = document.getElementById('daysInput').value;
            const generateButton = document.getElementById('generateButton');
            const orderSn = currentUserData.order_sn || currentUserData.orderId || currentUserData.order_id || currentUserData.orderSn;

            // Show enhanced loading state
            const originalText = generateButton.innerHTML;
            setButtonLoading(generateButton, true);

            try {
                let response;

                // Use existing API endpoint but with workflow-specific parameters
                const requestData = {
                    user_uuid: currentUserData.user_uuid,
                    order_sn: orderSn,
                    server: server,
                    days: days,
                    telco: selectedTelco || lockedTelco,
                    plan: selectedPlan
                };

                // Add workflow-specific metadata
                if (currentWorkflowType === 'new_uuid') {
                    requestData.workflow_type = 'new_uuid';
                    requestData.var_sku = currentUserData.var_sku || currentUserData.sku;
                    requestData.create_on_all_servers = true; // Flag for backend to create on all related servers
                    requestData.display_server = server; // Server for config template display only
                    console.log('🚀 New UUID creation request - will create on all servers for SKU:', requestData.var_sku);
                } else if (currentWorkflowType === 'uuid_renewal') {
                    requestData.workflow_type = 'uuid_renewal';
                    requestData.existing_uuid = currentUUID;
                    requestData.locked_telco = lockedTelco;
                } else if (currentWorkflowType === 'view_config') {
                    requestData.workflow_type = 'view_config';
                    requestData.existing_uuid = currentUUID;
                    requestData.locked_telco = lockedTelco;
                    requestData.template_update_only = true;
                }

                // Log the request for debugging
                console.log('🚀 Generating configuration with workflow:', currentWorkflowType);
                console.log('📤 Request data:', requestData);

                // Use the existing generate-config endpoint
                response = await axios.post('/vpn-config-generator/api/order/generate-config', requestData);

                if (response.data.success) {
                    // Log the response for debugging multi-server creation
                    console.log('✅ Configuration generation successful:', response.data);
                    if (currentWorkflowType === 'new_uuid') {
                        console.log('🖥️ Multi-server creation details:', {
                            total_configs: response.data.total_configs_created,
                            servers_used: response.data.servers_used,
                            message: response.data.message
                        });
                    }

                    // Save to history
                    const configData = {
                        telco: selectedTelco || lockedTelco,
                        plan: selectedPlan,
                        server: server,
                        days: days,
                        workflow_type: currentWorkflowType,
                        uuid: response.data.uuid || currentUUID
                    };
                    saveToHistory(currentUserData, configData);

                    showConfigurationResult(response.data);
                    showButtonSuccess(generateButton, originalText, 'Generated!');

                    if (currentWorkflowType === 'new_uuid') {
                        const serverCount = response.data.total_configs_created || response.data.servers_configured || 'all related';
                        const serverNames = response.data.servers_used ? ` (${response.data.servers_used.join(', ')})` : '';
                        showAlert(`✅ New UUID created and configured across ${serverCount} servers${serverNames}! Telco now locked.`, 'success');
                        // Lock telco for future operations
                        lockedTelco = selectedTelco;
                        currentUUID = response.data.uuid || response.data.client_id || currentUserData.user_uuid;
                    } else if (currentWorkflowType === 'uuid_renewal') {
                        showAlert('✅ UUID renewed with updated plan configuration!', 'success');
                        currentUUID = response.data.uuid || response.data.client_id || currentUUID;
                    } else if (currentWorkflowType === 'view_config') {
                        showAlert('✅ Configuration template updated successfully!', 'success');
                        currentUUID = response.data.uuid || response.data.client_id || currentUUID;
                    } else {
                        showAlert('✅ Configuration generated and saved to history!', 'success');
                    }
                } else {
                    showButtonError(generateButton, originalText, 'Failed!');
                    showAlert('❌ Error: ' + response.data.error, 'error');
                }
            } catch (error) {
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                showButtonError(generateButton, originalText, 'Error!');
                showAlert('❌ Error: ' + errorMessage, 'error');
            } finally {
                // Restore button state
                setButtonLoading(generateButton, false);
                generateButton.innerHTML = originalText;
            }
        }

        function showConfigurationResult(result) {
            console.log('📋 showConfigurationResult called with:', result);
            
            // Save to history when result is shown
            if (currentUserData) {
                const configData = {
                    telco: selectedTelco,
                    plan: selectedPlan,
                    server: document.getElementById('serverSelect')?.value || 'auto',
                    days: document.getElementById('daysInput')?.value || '30'
                };
                console.log('💾 Attempting to save to history from showConfigurationResult');
                const saved = saveToHistory(currentUserData, configData);
                if (saved) {
                    console.log('✅ Successfully saved to history from showConfigurationResult');
                } else {
                    console.error('❌ Failed to save to history from showConfigurationResult');
                }
            }
            
            closeServiceModal();

            const container = document.getElementById('configResult');
            const orderSn = result.order_sn || currentUserData.order_sn;
            const displayUUID = result.uuid || currentUUID || currentUserData.user_uuid;
            const effectiveTelco = selectedTelco || lockedTelco;

            // Determine workflow-specific title and styling
            let titleText = 'Configuration Generated Successfully';
            let titleClass = 'text-green-800';
            let borderClass = 'border-green-200';
            let bgClass = 'bg-green-50';

            if (currentWorkflowType === 'new_uuid') {
                titleText = 'New UUID Created Successfully';
                titleClass = 'text-blue-800';
                borderClass = 'border-blue-200';
                bgClass = 'bg-blue-50';
            } else if (currentWorkflowType === 'uuid_renewal') {
                titleText = 'UUID Configuration Renewed';
                titleClass = 'text-purple-800';
                borderClass = 'border-purple-200';
                bgClass = 'bg-purple-50';
            } else if (currentWorkflowType === 'view_config') {
                titleText = 'Configuration Template Updated';
                titleClass = 'text-orange-800';
                borderClass = 'border-orange-200';
                bgClass = 'bg-orange-50';
            }

            container.innerHTML = `
                <div class="${bgClass} border ${borderClass} rounded-lg p-4">
                    <h4 class="font-semibold ${titleClass} mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        ${titleText}
                    </h4>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">Workflow:</span> <span class="text-crypto-accent">${currentWorkflowType?.replace('_', ' ').toUpperCase() || 'Standard'}</span></p>
                        <p><span class="font-medium">Order:</span> ${orderSn}</p>
                        <p><span class="font-medium">Customer:</span> ${currentUserData.buyer_username}</p>
                        <p><span class="font-medium">SKU:</span> ${currentUserData.var_sku || currentUserData.sku}</p>
                        <p><span class="font-medium">VPN UUID:</span> <code class="bg-gray-100 px-2 py-1 rounded text-xs font-mono">${displayUUID}</code></p>
                        ${result.created_date ? `<p><span class="font-medium">Created:</span> ${result.created_date}</p>` : ''}
                        ${result.expired_date ? `<p><span class="font-medium">Expires:</span> ${result.expired_date}</p>` : ''}
                        <p><span class="font-medium">Display Server:</span> ${getServerDisplayInfo()}</p>
                        <p><span class="font-medium">Validity:</span> ${document.getElementById('autoValidityDisplay')?.textContent || 'N/A'}</p>
                        <p><span class="font-medium">Telco:</span> <span class="text-crypto-accent">${effectiveTelco || 'N/A'}</span> ${(currentWorkflowType === 'uuid_renewal' || currentWorkflowType === 'view_config') ? '🔒' : ''}</p>
                        <p><span class="font-medium">Plan:</span> ${selectedPlan || 'N/A'}</p>
                        ${currentWorkflowType === 'new_uuid' ? `<p class="text-xs text-blue-600 mt-2">✅ UUID created on ${result.total_configs_created || result.servers_configured || 'all'} related servers. Telco now locked.</p>` : ''}
                        ${currentWorkflowType === 'new_uuid' && result.servers_used ? `<p class="text-xs text-gray-600 mt-1">🖥️ Servers: ${result.servers_used.join(', ')}</p>` : ''}
                        ${currentWorkflowType === 'new_uuid' ? '<p class="text-xs text-gray-600 mt-1">📋 Configuration shown is from selected display server only</p>' : ''}
                    </div>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Saved to History
                    </h4>
                    <div class="text-sm text-blue-700">
                        <p>✅ This configuration has been automatically saved to your local history.</p>
                        <p class="mt-1">View your redemption history by clicking the "View History" button.</p>
                    </div>
                </div>
                
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            VPN Configuration
                        </h4>
                        <button onclick="copyConfiguration()" class="crypto-button-ghost crypto-button-sm">
                            <span class="button-content flex items-center">
                                <svg class="button-icon w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                Copy
                            </span>
                        </button>
                    </div>
                    <pre id="configText" class="text-xs bg-white p-3 rounded border overflow-x-auto">${result.config}</pre>
                </div>
                
                ${result.message ? `<div class="text-sm text-gray-600 italic">${result.message}</div>` : ''}
            `;

            document.getElementById('resultModal').classList.remove('hidden');
        }

        function copyConfiguration() {
            const configText = document.getElementById('configText').textContent;
            const copyButton = event.target.closest('button');
            const originalText = copyButton.innerHTML;
            
            navigator.clipboard.writeText(configText).then(() => {
                showButtonSuccess(copyButton, originalText, '✓ Copied!');
                showAlert('Configuration copied to clipboard!', 'success');
            }).catch(err => {
                console.error('Failed to copy: ', err);
                showButtonError(copyButton, originalText, 'Failed!');
                showAlert('Failed to copy configuration', 'error');
            });
        }

        async function showRenewalSelection() {
            hideVerificationModal();

            // Create and show renewal modal
            const renewalModal = document.createElement('div');
            renewalModal.id = 'renewalModal';
            renewalModal.className = 'fixed inset-0 crypto-modal overflow-y-auto h-full w-full z-50';
            renewalModal.innerHTML = `
                <div class="relative top-10 mx-auto p-6 crypto-modal-content w-11/12 max-w-md">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Renew VPN Configuration</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeRenewalModal()">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div id="renewalContent">
                            <div class="text-center py-8">
                                <div class="sk-wave sk-primary mx-auto">
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                </div>
                                <p class="mt-4 text-gray-600">Loading your configurations...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(renewalModal);

            try {
                // Add timeout to prevent infinite loading
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Request timeout - please again')), 10000);
                });

                // Load user configurations with timeout
                const apiPromise = axios.post('/vpn-config-generator/api/order/user-configs', {
                    buyer_username: currentUserData.buyer_username
                });

                const response = await Promise.race([apiPromise, timeoutPromise]);

                if (response.data.success) {
                    const configs = response.data.configs;
                    populateRenewalList(configs);
                } else {
                    throw new Error(response.data.error || 'Failed to load configurations');
                }
            } catch (error) {
                console.error('Error loading configurations:', error);
                const content = document.getElementById('renewalContent');
                if (content) {
                    content.innerHTML = `
                        <div class="text-center py-8">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                            <p class="mt-4 text-red-600">Failed to load configurations</p>
                            <p class="text-sm text-gray-500">${error.message}</p>
                            <button onclick="closeRenewalModal()"
                                class="mt-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition duration-200">
                                Close
                            </button>
                        </div>
                    `;
                }
            }
        }

        function populateRenewalList(configs) {
            const content = document.getElementById('renewalContent');

            if (configs.length === 0) {
                content.innerHTML = `
                    <div class="text-center py-8">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
                            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <p class="mt-4 text-gray-500">No configurations found</p>
                        <p class="text-sm text-gray-400">Create your first configuration to get started</p>
                        <button onclick="closeRenewalModal(); showServiceSelection();" 
                            class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200">
                            Create Configuration
                        </button>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">Select Configuration to Renew</h4>
                    <p class="text-sm text-blue-700">Choose a configuration to extend its validity period based on your SKU.</p>
                </div>
                <div class="space-y-4">
            `;

            configs.forEach(config => {
                const statusColor = config.is_expired ? 'text-red-600' : 'text-green-600';
                const statusIcon = config.is_expired ? '❌' : '✅';

                html += `
                    <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-semibold text-gray-800">${config.telco.toUpperCase()} - ${config.plan}</h4>
                                <p class="text-sm text-gray-600">${config.server_name || 'Server ' + config.server_id}</p>
                            </div>
                            <span class="${statusColor} text-sm font-medium">${statusIcon} ${config.validity_status}</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">Created:</span>
                                <span class="font-medium">${config.created_date}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Expires:</span>
                                <span class="font-medium">${config.expired_date}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Client ID:</span>
                                <span class="font-medium">${config.client_id || config.numeric_id}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Order:</span>
                                <span class="font-medium">${config.order_sn}</span>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button onclick="showConfigDetails('${(config.config_text || '').replace(/'/g, "\'")}', '${config.telco} - ${config.plan}')"
                                class="w-full px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm rounded-md transition duration-200">
                                📋 View Config
                            </button>
                        </div>
                        ${currentUserData.order_claimed ? `
                            <div class="mt-2 text-center">
                                <p class="text-xs text-red-500">⚠️ Renewals disabled for claimed orders</p>
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            html += `
                </div>
                <div class="mt-6 flex justify-between">
                    <button onclick="showExistingConfigs()" 
                        class="px-4 py-2 text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition duration-200">
                        View All Configurations
                    </button>
                    <button onclick="closeRenewalModal()" 
                        class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition duration-200">
                        Cancel
                    </button>
                </div>
            `;

            content.innerHTML = html;
        }

        async function renewConfig(clientId, userUuid, orderSn) {
            try {
                const response = await axios.post('/vpn-config-generator/api/order/renew-config', {
                    client_id: clientId,
                    user_uuid: userUuid,
                    order_sn: orderSn
                });

                if (response.data.success) {
                    alert(`✅ Configuration renewed successfully!\n\nClient ID: ${response.data.client_id}\nNew Expiry: ${response.data.new_expiry_date}\nDays Extended: ${response.data.days_extended}`);

                    // Refresh the renewal list
                    showRenewalSelection();
                } else {
                    alert(`❌ Renewal failed: ${response.data.error}`);
                }
            } catch (error) {
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                alert(`❌ Renewal failed: ${errorMessage}`);
            }
        }

        function showConfigDetails(configText, title) {
            if (!configText) {
                alert('Configuration text not available');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 crypto-modal overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-6 crypto-modal-content w-11/12 max-w-md">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">${title || 'VPN Configuration'}</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="this.closest('.fixed').remove()">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <textarea readonly class="w-full h-40 p-3 text-sm font-mono bg-white border border-gray-300 rounded-md resize-none">${configText}</textarea>
                        </div>
                        <div class="flex justify-end mt-4">
                            <button onclick="navigator.clipboard.writeText('${configText}'); alert('Configuration copied to clipboard!')" 
                                class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200">
                                📋 Copy Configuration
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        async function showExistingConfigs() {
            hideVerificationModal();
            closeRenewalModal();
            closeExistingConfigsModal(); // Close any existing modal first

            // Create and show existing configs modal
            const modal = document.createElement('div');
            modal.id = 'existingConfigsModal';
            modal.className = 'fixed inset-0 crypto-modal overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-10 mx-auto p-6 crypto-modal-content w-11/12 max-w-md">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Your VPN Configurations</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeExistingConfigsModal()">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div id="existingConfigsContent">
                            <div class="text-center py-8">
                                <div class="sk-wave sk-primary mx-auto">
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                    <div class="sk-wave-rect"></div>
                                </div>
                                <p class="mt-4 text-gray-600">Loading your configurations...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            try {
                // Add timeout to prevent infinite loading
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Request timeout - please again')), 10000);
                });

                // Load user configurations with timeout
                const apiPromise = axios.post('/vpn-config-generator/api/order/user-configs', {
                    buyer_username: currentUserData.buyer_username
                });

                const response = await Promise.race([apiPromise, timeoutPromise]);

                if (response.data.success) {
                    const configs = response.data.configs;
                    populateExistingConfigs(configs);
                } else {
                    throw new Error(response.data.error || 'Failed to load configurations');
                }
            } catch (error) {
                console.error('Error loading configurations:', error);
                const content = document.getElementById('existingConfigsContent');
                if (content) {
                    content.innerHTML = `
                        <div class="text-center py-8">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                            <p class="mt-4 text-red-600">Failed to load configurations</p>
                            <p class="text-sm text-gray-500">${error.message}</p>
                            <button onclick="closeExistingConfigsModal()"
                                class="mt-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition duration-200">
                                Close
                            </button>
                        </div>
                    `;
                }
            }
        }

        function populateExistingConfigs(configs) {
            const content = document.getElementById('existingConfigsContent');

            if (configs.length === 0) {
                content.innerHTML = `
                    <div class="text-center py-8">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
                            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <p class="mt-4 text-gray-500">No configurations found</p>
                        <p class="text-sm text-gray-400">Create your first configuration to get started</p>
                        <button onclick="closeExistingConfigsModal(); showServiceSelection();" 
                            class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200">
                            Create Configuration
                        </button>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">Your VPN Configurations</h4>
                    <p class="text-sm text-blue-700">Total configurations: ${configs.length}</p>
                </div>
                <div class="space-y-4">
            `;

            configs.forEach(config => {
                const statusColor = config.is_expired ? 'text-red-600' : 'text-green-600';
                const statusIcon = config.is_expired ? '❌' : '✅';
                const orderSn = config.order_sn || 'N/A';

                html += `
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-semibold text-gray-800">${config.telco.toUpperCase()} - ${config.plan}</h4>
                                <p class="text-sm text-gray-600">${config.server_name || 'Server ' + config.server_id}</p>
                            </div>
                            <span class="${statusColor} text-sm font-medium">${statusIcon} ${config.validity_status}</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm mb-4">
                            <div>
                                <span class="text-gray-500">Created:</span>
                                <span class="font-medium">${config.created_date}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Expires:</span>
                                <span class="font-medium">${config.expired_date}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Client ID:</span>
                                <span class="font-medium">${config.client_id || config.numeric_id}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Order:</span>
                                <span class="font-medium">${orderSn}</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-md mb-3">
                            <textarea readonly class="w-full h-20 text-xs font-mono bg-transparent border-none resize-none">${config.config_text || 'Configuration not available'}</textarea>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="navigator.clipboard.writeText('${(config.config_text || '').replace(/'/g, "\'")}').then(() => { alert('Configuration copied to clipboard!'); });"
                                class="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-md transition duration-200">
                                📋 Copy
                            </button>
                            ${(config.client_id || config.numeric_id) ? `
                                <button onclick="showChangeConfigModal('${config.client_id || config.numeric_id}', '${config.client_id || config.numeric_id}', '${orderSn}', '${config.telco}', '${config.plan}', '${currentUserData.var_sku || ''}')"
                                    class="px-3 py-2 bg-purple-500 hover:bg-purple-600 text-white text-sm rounded-md transition duration-200">
                                    🔄 Change Config
                                </button>
                                ${!currentUserData.order_claimed ? `
                                    <button onclick="renewConfig(${config.client_id || config.numeric_id}, '${currentUserData.user_uuid}', '${orderSn}')"
                                        class="px-3 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded-md transition duration-200">
                                        ⏰ Renew
                                    </button>
                                ` : ''}
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            html += `
                </div>
                <div class="mt-6 flex justify-between">
                    ${!currentUserData.order_claimed ? `
                        <button onclick="closeExistingConfigsModal(); showServiceSelection();" 
                            class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition duration-200">
                            Create New Configuration
                        </button>
                    ` : `
                        <div class="text-center flex-1">
                            <p class="text-sm text-red-500">🚫 New configuration creation disabled for claimed orders</p>
                        </div>
                    `}
                    <button onclick="closeExistingConfigsModal()" 
                        class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition duration-200">
                        Close
                    </button>
                </div>
            `;

            content.innerHTML = html;
        }

        function generateAnother() {
            closeResultModal();
            showServiceSelection();
        }

        // Plan Change Selection for claimed orders
        function showPlanChangeSelection() {
            hideVerificationModal();

            // Create and show plan change modal
            const modal = document.createElement('div');
            modal.id = 'planChangeModal';
            modal.className = 'fixed inset-0 crypto-modal overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-10 mx-auto p-6 crypto-modal-content w-11/12 max-w-md">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Change Plan - ${currentUserData.assigned_telco.toUpperCase()}</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closePlanChangeModal()">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">Order Information</h4>
                            <div class="text-sm text-blue-700">
                                <p><span class="font-medium">Order:</span> ${currentUserData.order_sn}</p>
                                <p><span class="font-medium">Customer:</span> ${currentUserData.buyer_username}</p>
                                <p><span class="font-medium">Locked Telco:</span> <strong>${currentUserData.assigned_telco.toUpperCase()}</strong></p>
                            </div>
                        </div>
                        
                        <div id="planChangeContent">
                            <div class="text-center py-4">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                                <p class="mt-2 text-gray-600">Loading available plans...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Load available plans for the assigned telco
            loadPlanChangeOptions();
        }

        function closePlanChangeModal() {
            const modal = document.getElementById('planChangeModal');
            if (modal) {
                modal.remove();
            }
        }

        async function loadPlanChangeOptions() {
            try {
                const telcoConfig = availableTelcos[currentUserData.assigned_telco];
                if (!telcoConfig || !telcoConfig.plans) {
                    throw new Error(`No plans available for ${currentUserData.assigned_telco}`);
                }

                const content = document.getElementById('planChangeContent');
                let html = `
                    <form onsubmit="createPlanChangeConfig(event)">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Select New Plan</label>
                            <div class="grid grid-cols-1 gap-3">
                `;

                Object.entries(telcoConfig.plans).forEach(([planKey, planConfig]) => {
                    if (!planConfig.enabled) return;

                    html += `
                        <label class="flex items-center p-3 border-2 border-gray-200 rounded-lg hover:border-blue-300 cursor-pointer transition-colors">
                            <input type="radio" name="plan" value="${planKey}" class="mr-3" required>
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">${planConfig.name || planKey}</div>
                                <div class="text-sm text-gray-600">${planConfig.description || 'No description available'}</div>
                            </div>
                        </label>
                    `;
                });

                html += `
                            </div>
                        </div>
                        
                        <div class="mb-4 p-3 bg-yellow-50 rounded-lg">
                            <p class="text-sm text-yellow-800">
                                <strong>Note:</strong> This will create a new configuration with the selected plan on all servers mapped to your SKU.
                            </p>
                        </div>
                        
                        <div class="flex space-x-3">
                            <button type="button" onclick="closePlanChangeModal()"
                                class="flex-1 px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition duration-200">
                                Cancel
                            </button>
                            <button type="submit"
                                class="flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition duration-200">
                                🔄 Create New Configuration
                            </button>
                        </div>
                    </form>
                `;

                content.innerHTML = html;
            } catch (error) {
                const content = document.getElementById('planChangeContent');
                content.innerHTML = `
                    <div class="text-center py-4">
                        <div class="text-red-500 mb-2">❌ Error loading plans</div>
                        <p class="text-sm text-gray-600">${error.message}</p>
                        <button onclick="closePlanChangeModal()"
                            class="mt-3 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition duration-200">
                            Close
                        </button>
                    </div>
                `;
            }
        }

        async function createPlanChangeConfig(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const selectedPlan = formData.get('plan');

            if (!selectedPlan) {
                alert('Please select a plan');
                return;
            }

            try {
                const response = await axios.post('/vpn-config-generator/api/order/generate-config', {
                    user_uuid: currentUserData.user_uuid,
                    order_sn: currentUserData.order_sn,
                    server: 'auto', // Let system choose appropriate servers
                    days: document.getElementById('daysInput')?.value || 30,
                    telco: currentUserData.assigned_telco,
                    plan: selectedPlan
                });

                if (response.data.success) {
                    alert(`✅ New configuration created successfully!\n\nPlan: ${selectedPlan}\nTelco: ${currentUserData.assigned_telco.toUpperCase()}`);
                    closePlanChangeModal();
                    showExistingConfigs();
                } else {
                    alert(`❌ Configuration creation failed: ${response.data.error}`);
                }
            } catch (error) {
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                alert(`❌ Configuration creation failed: ${errorMessage}`);
            }
        }

        // Modal management functions
        function hideVerificationModal() {
            const modal = document.getElementById('verificationModal');
            modal.classList.add('modal-exit');
            setTimeout(() => {
                modal.classList.add('modal-exit-active');
                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.classList.remove('modal-exit', 'modal-exit-active');
                    // Reset modal content for next use
                    resetVerificationModalContent();
                }, 300);
            }, 10);
        }

        function closeServiceModal() {
            document.getElementById('serviceModal').classList.add('hidden');
        }

        function closeResultModal() {
            document.getElementById('resultModal').classList.add('hidden');
        }

        function closeRenewalModal() {
            const modal = document.getElementById('renewalModal');
            if (modal) {
                modal.remove();
            }
        }

        function closeExistingConfigsModal() {
            const modal = document.getElementById('existingConfigsModal');
            if (modal) {
                modal.remove();
            }
        }

        function handleModalClick(event) {
            if (event.target === document.getElementById('verificationModal')) {
                hideVerificationModal();
                // Reinitialize form listeners after modal close
                setTimeout(() => {
                    initializeFormListeners();
                }, 400); // Increased timeout to ensure modal animation completes
            }
        }

        function handleServiceModalClick(event) {
            if (event.target === document.getElementById('serviceModal')) {
                closeServiceModal();
            }
        }

        // URL parameter handling
        function getUrlParameter(name) {
            name = name.replace(/[\\[]/, '\\[').replace(/[\\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Change Config Modal Functions
        function showChangeConfigModal(clientId, displayUuid, orderSn, currentTelco, currentPlan, varSku = null) {
            const modal = document.createElement('div');
            modal.id = 'changeConfigModal';
            modal.className = 'fixed inset-0 crypto-modal overflow-y-auto h-full w-full z-50';

            modal.innerHTML = `
                <div class="relative top-4 mx-auto p-0 crypto-modal-content w-11/12 max-w-2xl">
                    <div class="bg-white rounded-2xl shadow-2xl max-h-[90vh] flex flex-col">
                        <!-- Modern Header -->
                        <div class="flex items-center justify-between p-6 border-b border-gray-100 flex-shrink-0">
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900">Change VPN Configuration</h3>
                                <p class="text-sm text-gray-500 mt-1">Update your plan and server selection</p>
                            </div>
                            <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors rounded-full p-2 hover:bg-gray-100" onclick="closeChangeConfigModal()">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Modal Content -->
                        <div class="flex-1 overflow-y-auto">
                            <!-- Current Configuration Display -->
                            <div class="p-6 border-b border-gray-100">
                                <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                    <h4 class="font-semibold text-blue-800 mb-3 flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Current Configuration
                                    </h4>
                                    <div class="text-sm space-y-2">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-blue-700">Telco:</span>
                                            <span class="font-semibold text-blue-900">${currentTelco.toUpperCase()}</span>
                                            <span class="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">🔒 Locked</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-blue-700">Plan:</span>
                                            <span class="font-medium text-blue-800">${currentPlan}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-blue-700">Client ID:</span>
                                            <code class="text-xs bg-blue-100 px-2 py-1 rounded font-mono text-blue-900">${displayUuid}</code>
                                        </div>
                                        ${varSku ? `
                                        <div class="flex items-center space-x-2">
                                            <span class="text-blue-700">SKU:</span>
                                            <code class="text-xs bg-blue-100 px-2 py-1 rounded font-mono text-blue-900">${varSku}</code>
                                        </div>
                                        ` : ''}
                                    </div>
                                    <p class="text-xs text-blue-600 mt-3">You can change the plan and server for configuration display</p>
                                </div>
                            </div>

                            <div id="changeConfigContent" class="p-6">
                                <div class="text-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto"></div>
                                    <p class="mt-3 text-gray-600">Loading configuration options...</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div id="changeConfigActions" class="p-6 border-t border-gray-100 flex-shrink-0 hidden">
                            <div class="flex items-center justify-between">
                                <button type="button" onclick="closeChangeConfigModal()" class="crypto-button-ghost">
                                    <span class="button-content flex items-center justify-center">
                                        <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Cancel
                                    </span>
                                </button>

                                <button type="button" id="changeConfigBtn" onclick="handleChangeConfiguration()" class="crypto-button crypto-button-success">
                                    <span class="button-content flex items-center justify-center">
                                        <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Change Configuration
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Load available plans and servers for the telco
            loadTelcoPlansAndServers(currentTelco, clientId, currentUserData.user_uuid, orderSn, varSku);
        }

        function closeChangeConfigModal() {
            const modal = document.getElementById('changeConfigModal');
            if (modal) {
                modal.remove();
            }
        }

        async function loadTelcoPlansAndServers(telco, clientId, userUuid, orderSn, varSku = null) {
            try {
                // Get telco configuration from the global telcos data
                const telcoConfig = availableTelcos[telco];
                if (!telcoConfig || !telcoConfig.plans) {
                    throw new Error(`No plans available for ${telco}`);
                }

                // Get available servers based on SKU
                const servers = await getServersBySku(varSku);
                console.log('Loaded servers for SKU:', varSku, servers);

                const content = document.getElementById('changeConfigContent');
                const actionsDiv = document.getElementById('changeConfigActions');

                let html = `
                    <div class="change-config-form space-y-6" data-client-id="${clientId}" data-user-uuid="${currentUserData.user_uuid}" data-order-sn="${orderSn}" data-telco="${telco}" data-var-sku="${varSku || ''}">

                        <!-- Server Selection -->
                        <div class="bg-gray-50 border border-gray-200 rounded-xl p-6">
                            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                                </svg>
                                Server Selection
                            </h4>

                            <div class="bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg p-4 mb-4">
                                <div class="flex items-start space-x-3">
                                    <svg class="w-5 h-5 text-indigo-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <p class="text-sm font-medium text-indigo-800 mb-1">SKU-Based Server Selection</p>
                                        <p class="text-xs text-indigo-700">
                                            ${varSku ? `Servers filtered for SKU: <code class="bg-indigo-100 px-1 rounded">${varSku}</code>` : 'Your UUID is available on ALL related servers.'}
                                            Choose any server below to generate its configuration template.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">Available Servers (${servers.length} found)</label>
                                <select name="server" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors" required>
                                    <option value="">Select a server...</option>
                `;

                // Add server options
                if (servers && servers.length > 0) {
                    servers.forEach(server => {
                        const tags = server.tags ? ` (${server.tags.join(', ')})` : '';
                        html += `<option value="${server.id}">${server.name} - ${server.location}${tags}</option>`;
                    });
                } else {
                    html += `<option value="default">No specific servers found - using default</option>`;
                }

                html += `
                                </select>
                            </div>
                        </div>

                        <!-- Plan Selection -->
                        <div class="bg-gray-50 border border-gray-200 rounded-xl p-6">
                            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Select New Plan
                            </h4>
                            <div class="space-y-3">
                `;

                Object.entries(telcoConfig.plans).forEach(([planKey, planConfig]) => {
                    html += `
                        <label class="plan-card-modern cursor-pointer block">
                            <input type="radio" name="plan" value="${planKey}" class="sr-only" required>
                            <div class="p-4 border border-gray-200 rounded-xl hover:border-indigo-300 transition-all duration-200 hover:shadow-md">
                                <div class="flex items-start justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h5 class="font-semibold text-gray-900 text-base">${planConfig.name || planKey}</h5>
                                            <p class="text-sm text-gray-600 mt-1">${planConfig.description || 'No description available'}</p>
                                        </div>
                                    </div>
                                    <div class="radio-indicator w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                        <div class="w-2.5 h-2.5 bg-indigo-600 rounded-full opacity-0 transition-opacity"></div>
                                    </div>
                                </div>
                            </div>
                        </label>
                    `;
                });

                html += `
                            </div>
                        </div>
                    </div>
                `;

                content.innerHTML = html;

                // Show the action buttons
                if (actionsDiv) {
                    actionsDiv.classList.remove('hidden');
                }

                // Add event listeners for radio button styling
                addRadioButtonStyling();

            } catch (error) {
                console.error('Error loading telco plans and servers:', error);
                const content = document.getElementById('changeConfigContent');
                content.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-500 mb-3 text-lg">❌ Error loading configuration</div>
                        <p class="text-sm text-gray-600 mb-4">${error.message}</p>
                        <button onclick="closeChangeConfigModal()" class="crypto-button crypto-button-ghost">
                            <span class="button-content">Close</span>
                        </button>
                    </div>
                `;
            }
        }

        // Get servers based on SKU using the VPN strategy factory
        async function getServersBySku(varSku) {
            try {
                if (!varSku) {
                    console.log('No SKU provided, getting all available servers');
                    // If no SKU, get all available servers
                    const response = await axios.get('/vpn-config-generator/api/servers');
                    if (response.data.success) {
                        return response.data.servers.map(server => ({
                            id: server.id,
                            name: server.name || 'Unknown Server',
                            location: server.description || 'Unknown Location',
                            tags: server.tags || []
                        }));
                    }
                    return [];
                }

                console.log('Getting server tags for SKU:', varSku);

                // First, get the server tags for this SKU from the backend
                const tagsResponse = await axios.post('/vpn-config-generator/api/sku/server-tags', {
                    sku: varSku
                });

                if (!tagsResponse.data.success) {
                    console.warn('Failed to get server tags for SKU:', varSku, tagsResponse.data.error);
                    // Fallback to all servers
                    const response = await axios.get('/vpn-config-generator/api/servers');
                    if (response.data.success) {
                        return response.data.servers.map(server => ({
                            id: server.id,
                            name: server.name || 'Unknown Server',
                            location: server.description || 'Unknown Location',
                            tags: server.tags || []
                        }));
                    }
                    return [];
                }

                const serverTags = tagsResponse.data.tags;
                console.log('Server tags for SKU', varSku, ':', serverTags);

                if (!serverTags || serverTags.length === 0) {
                    console.log('No server tags found for SKU, using all servers');
                    const response = await axios.get('/vpn-config-generator/api/servers');
                    if (response.data.success) {
                        return response.data.servers.map(server => ({
                            id: server.id,
                            name: server.name || 'Unknown Server',
                            location: server.description || 'Unknown Location',
                            tags: server.tags || []
                        }));
                    }
                    return [];
                }

                // Get servers that match these tags
                const serversResponse = await axios.post('/vpn-config-generator/api/servers/by-tags', {
                    tags: serverTags
                });

                if (serversResponse.data.success) {
                    console.log('Found servers matching tags:', serversResponse.data.servers);
                    return serversResponse.data.servers.map(server => ({
                        id: server.id,
                        name: server.name || 'Unknown Server',
                        location: server.location || server.description || 'Unknown Location',
                        tags: server.tags || []
                    }));
                } else {
                    console.warn('Failed to get servers by tags:', serversResponse.data.error);
                    return [];
                }

            } catch (error) {
                console.error('Error getting servers by SKU:', error);

                // Fallback: try to get all available servers
                try {
                    const response = await axios.get('/vpn-config-generator/api/servers');
                    if (response.data.success) {
                        return response.data.servers.map(server => ({
                            id: server.id,
                            name: server.name || 'Unknown Server',
                            location: server.description || 'Unknown Location',
                            tags: server.tags || []
                        }));
                    }
                } catch (fallbackError) {
                    console.error('Fallback server fetch also failed:', fallbackError);
                }

                return [{ id: 'default', name: 'Default Server', location: 'Auto', tags: [] }];
            }
        }

        // Add styling for custom radio buttons
        function addRadioButtonStyling() {
            const radioLabels = document.querySelectorAll('.plan-card-modern');
            radioLabels.forEach(label => {
                const radio = label.querySelector('input[type="radio"]');
                const indicator = label.querySelector('.radio-indicator div');
                const card = label.querySelector('div');

                radio.addEventListener('change', function() {
                    // Reset all cards
                    radioLabels.forEach(otherLabel => {
                        const otherCard = otherLabel.querySelector('div');
                        const otherIndicator = otherLabel.querySelector('.radio-indicator div');
                        otherCard.classList.remove('border-indigo-500', 'bg-indigo-50');
                        otherCard.classList.add('border-gray-200');
                        otherIndicator.classList.remove('opacity-100');
                        otherIndicator.classList.add('opacity-0');
                    });

                    // Style selected card
                    if (this.checked) {
                        card.classList.remove('border-gray-200');
                        card.classList.add('border-indigo-500', 'bg-indigo-50');
                        indicator.classList.remove('opacity-0');
                        indicator.classList.add('opacity-100');
                    }
                });
            });
        }





        // Test function to verify the function is accessible
        window.testChangeConfig = function () {
            console.log('Test function called - handleChangeConfiguration is available:', typeof window.handleChangeConfiguration);
            alert('Test function works! Check console for details.');
        };

        // Function to handle change configuration without form submission
        window.handleChangeConfiguration = async function () {
            console.log('handleChangeConfiguration called');

            // Get form data
            const form = document.querySelector('.change-config-form');
            if (!form) {
                alert('Configuration form not found');
                return;
            }

            const clientId = form.dataset.clientId;
            const userUuid = form.dataset.userUuid;
            const orderSn = form.dataset.orderSn;
            const telco = form.dataset.telco;
            const varSku = form.dataset.varSku;

            console.log('Form data:', { clientId, userUuid, orderSn, telco, varSku });

            // Get selected plan from radio buttons
            const selectedPlanRadio = document.querySelector('input[name="plan"]:checked');
            if (!selectedPlanRadio) {
                alert('Please select a plan');
                return;
            }

            // Get selected server
            const selectedServerSelect = document.querySelector('select[name="server"]');
            if (!selectedServerSelect || !selectedServerSelect.value) {
                alert('Please select a server');
                return;
            }

            const selectedPlan = selectedPlanRadio.value;
            const selectedServer = selectedServerSelect.value;
            console.log('Selected plan:', selectedPlan, 'Selected server:', selectedServer);

            // Show spinner and disable button
            const changeButton = document.getElementById('changeConfigBtn');
            const originalText = changeButton ? changeButton.innerHTML : '';
            if (changeButton) {
                changeButton.disabled = true;
                changeButton.innerHTML = `
                    <span class="button-content flex items-center justify-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Updating Configuration...
                    </span>
                `;
            }

            try {
                console.log('Sending AJAX request to change config:', {
                    client_id: clientId,
                    user_uuid: userUuid,
                    order_sn: orderSn,
                    telco: telco,
                    plan: selectedPlan,
                    server: selectedServer,
                    var_sku: varSku
                });

                const response = await axios.post('/vpn-config-generator/api/order/change-config', {
                    client_id: clientId,
                    user_uuid: userUuid,
                    order_sn: orderSn,
                    telco: telco,
                    plan: selectedPlan,
                    server: selectedServer,
                    var_sku: varSku
                });

                console.log('Response received:', response.data);

                if (response.data.success) {
                    // Show success message with server info
                    const serverName = selectedServerSelect.options[selectedServerSelect.selectedIndex].text;
                    alert(`✅ Configuration updated successfully!\n\nNew Plan: ${selectedPlan}\nServer: ${serverName}\nClient ID: ${response.data.client_id}`);
                    closeChangeConfigModal();

                    // Add a small delay before refreshing to avoid race conditions
                    setTimeout(() => {
                        showExistingConfigs();
                    }, 500);
                } else {
                    alert(`❌ Update failed: ${response.data.error}`);
                }
            } catch (error) {
                console.error('Error in handleChangeConfiguration:', error);
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                alert(`❌ Change failed: ${errorMessage}`);
            } finally {
                // Restore button state
                if (changeButton) {
                    changeButton.disabled = false;
                    changeButton.innerHTML = originalText;
                }
            }
        };

        window.addEventListener('load', function () {
            var orderSn = getUrlParameter('order_sn');
            if (orderSn) {
                document.getElementById('orderId').value = orderSn;
            }
        });

        async function updateTemplateOnly() {
            const templateButton = document.getElementById('updateTemplateButton');
            const originalText = templateButton.innerHTML;
            
            // Show loading state
            setButtonLoading(templateButton, true);

            try {
                const response = await axios.post('/vpn-config-generator/api/order/update-template-only', {
                    user_uuid: currentUserData.user_uuid,
                    telco: selectedTelco,
                    plan: selectedPlan,
                    server: document.getElementById('serverInput').value,
                    days: document.getElementById('daysInput').value
                });

                if (response.data.success) {
                    // Show template update result
                    showTemplateUpdateResult(response.data);
                    showButtonSuccess(templateButton, originalText, 'Updated!');
                    showAlert('✅ Template updated successfully! UUID and expiry date preserved.', 'success');
                    
                    // Save to history with template update flag
                    const configData = {
                        telco: selectedTelco,
                        plan: selectedPlan,
                        server: document.getElementById('serverInput').value,
                        days: document.getElementById('daysInput').value,
                        template_update: true
                    };
                    saveToHistory(currentUserData, configData);
                } else {
                    showButtonError(templateButton, originalText, 'Failed!');
                    showAlert('❌ Error: ' + response.data.error, 'error');
                }
            } catch (error) {
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                showButtonError(templateButton, originalText, 'Error!');
                showAlert('❌ Error: ' + errorMessage, 'error');
            } finally {
                // Restore button state
                setButtonLoading(templateButton, false);
                templateButton.innerHTML = originalText;
            }
        }

        function showTemplateUpdateResult(result) {
            closeServiceModal();

            const container = document.getElementById('configResult');
            const orderSn = result.order_sn || currentUserData.order_sn;
            container.innerHTML = `
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Template Updated Successfully
                    </h4>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">Order:</span> ${orderSn}</p>
                        <p><span class="font-medium">Customer:</span> ${currentUserData.buyer_username}</p>
                        <p><span class="font-medium">SKU:</span> ${currentUserData.var_sku || currentUserData.sku}</p>
                        <p><span class="font-medium">New Telco:</span> <span class="text-crypto-accent">${result.template_change.new_telco}</span></p>
                        <p><span class="font-medium">New Plan:</span> ${result.template_change.new_plan}</p>
                    </div>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 class="font-semibold text-green-800 mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        Preserved Information
                    </h4>
                    <div class="text-sm text-green-700">
                        <p>✅ <strong>UUID Preserved:</strong> ${result.template_change.uuid_preserved}</p>
                        <p>✅ <strong>Original Expiry Date:</strong> ${result.template_change.expiry_preserved}</p>
                        <p>✅ <strong>Original Creation Date:</strong> ${result.created_date}</p>
                        <p class="mt-2 text-xs italic">Your existing UUID and expiration date have been preserved. Only the template configuration has been updated.</p>
                    </div>
                </div>
                
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Updated VPN Configuration
                        </h4>
                        <button onclick="copyConfiguration()" class="crypto-button-ghost crypto-button-sm">
                            <span class="button-content flex items-center">
                                <svg class="button-icon w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                Copy
                            </span>
                        </button>
                    </div>
                    <pre id="configText" class="text-xs bg-white p-3 rounded border overflow-x-auto">${result.config}</pre>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 class="font-semibold text-yellow-800 mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Important Notice
                    </h4>
                    <div class="text-sm text-yellow-700">
                        <p>🔄 <strong>Template Only Update:</strong> This operation changed only the template configuration (telco/plan).</p>
                        <p>🔒 <strong>UUID & Expiry Preserved:</strong> Your original UUID and expiration date remain unchanged.</p>
                        <p>📱 <strong>Same Configuration:</strong> You can continue using your existing VPN connection with the new template.</p>
                    </div>
                </div>
                
                ${result.message ? `<div class="text-sm text-gray-600 italic">${result.message}</div>` : ''}
            `;

            document.getElementById('resultModal').classList.remove('hidden');
        }
    </script>
</body>

</html>