{% extends "base.html" %}

{% block title %}OpenAI Plus Redeem - Admin Dashboard{% endblock %}

{% block header %}
<i class="fas fa-robot mr-2"></i>OpenAI Plus Redeem Dashboard
{% endblock %}

{% block extra_head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .stat-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .stat-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
    }
    
    .stat-header.accounts {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    .stat-header.redemptions {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    .stat-header.cooldowns {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .stat-header.services {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
    
    .quick-action-btn {
        transition: all 0.2s ease;
    }
    
    .quick-action-btn:hover {
        transform: scale(1.05);
    }
    
    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .status-healthy { background-color: #10b981; }
    .status-degraded { background-color: #f59e0b; }
    .status-unhealthy { background-color: #ef4444; }
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Overview -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-bold text-gray-800">Dashboard Overview</h2>
        <button id="refreshDashboard" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
            <i class="fas fa-sync-alt mr-2"></i>Refresh
        </button>
    </div>
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Accounts Card -->
        <div class="stat-card">
            <div class="stat-header accounts">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">ChatGPT Accounts</h3>
                        <p class="text-sm opacity-90">Active accounts</p>
                    </div>
                    <i class="fas fa-user-circle text-2xl opacity-80"></i>
                </div>
            </div>
            <div class="p-4">
                <div class="text-3xl font-bold text-gray-800" id="totalAccounts">-</div>
                <div class="text-sm text-gray-600 mt-1">
                    <span id="activeAccounts">-</span> active, 
                    <span id="expiredAccounts">-</span> expired
                </div>
                <div class="mt-2">
                    <div class="text-xs text-gray-500">Capacity Usage</div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div id="capacityBar" class="bg-green-500 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        <span id="usedCapacity">-</span>/<span id="totalCapacity">-</span> users
                    </div>
                </div>
            </div>
        </div>

        <!-- Redemptions Card -->
        <div class="stat-card">
            <div class="stat-header redemptions">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Redemptions</h3>
                        <p class="text-sm opacity-90">Order redemptions</p>
                    </div>
                    <i class="fas fa-gift text-2xl opacity-80"></i>
                </div>
            </div>
            <div class="p-4">
                <div class="text-3xl font-bold text-gray-800" id="totalRedemptions">-</div>
                <div class="text-sm text-gray-600 mt-1">
                    <span id="activeRedemptions">-</span> active, 
                    <span id="pendingRedemptions">-</span> pending
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    Today: <span id="todayRedemptions">-</span> new
                </div>
            </div>
        </div>

        <!-- Cooldowns Card -->
        <div class="stat-card">
            <div class="stat-header cooldowns">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Cooldowns</h3>
                        <p class="text-sm opacity-90">Active cooldowns</p>
                    </div>
                    <i class="fas fa-clock text-2xl opacity-80"></i>
                </div>
            </div>
            <div class="p-4">
                <div class="text-3xl font-bold text-gray-800" id="activeCooldowns">-</div>
                <div class="text-sm text-gray-600 mt-1">
                    <span id="expiringSoon">-</span> expiring soon
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    Total users affected: <span id="cooldownUsers">-</span>
                </div>
            </div>
        </div>

        <!-- Services Card -->
        <div class="stat-card">
            <div class="stat-header services">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Services</h3>
                        <p class="text-sm opacity-90">System health</p>
                    </div>
                    <i class="fas fa-cogs text-2xl opacity-80"></i>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-2">
                    <span id="serviceStatusIndicator" class="status-indicator status-healthy"></span>
                    <span id="serviceStatusText" class="text-lg font-semibold text-gray-800">Healthy</span>
                </div>
                <div class="text-sm text-gray-600">
                    <span id="healthyServices">-</span>/<span id="totalServices">-</span> services healthy
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <button onclick="window.location.href='/admin/openai-plus-redeem/accounts'" 
                class="quick-action-btn bg-green-500 text-white p-4 rounded-lg hover:bg-green-600 text-center">
            <i class="fas fa-user-plus text-2xl mb-2"></i>
            <div class="text-sm font-medium">Add Account</div>
        </button>
        
        <button onclick="showCooldownModal()" 
                class="quick-action-btn bg-red-500 text-white p-4 rounded-lg hover:bg-red-600 text-center">
            <i class="fas fa-user-clock text-2xl mb-2"></i>
            <div class="text-sm font-medium">Set Cooldown</div>
        </button>
        
        <button onclick="cleanupExpiredData()" 
                class="quick-action-btn bg-yellow-500 text-white p-4 rounded-lg hover:bg-yellow-600 text-center">
            <i class="fas fa-broom text-2xl mb-2"></i>
            <div class="text-sm font-medium">Cleanup Data</div>
        </button>
        
        <button onclick="refreshServices()" 
                class="quick-action-btn bg-purple-500 text-white p-4 rounded-lg hover:bg-purple-600 text-center">
            <i class="fas fa-sync-alt text-2xl mb-2"></i>
            <div class="text-sm font-medium">Refresh Services</div>
        </button>
    </div>
</div>

<!-- Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Redemptions -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Redemptions</h2>
        <div id="recentRedemptions" class="space-y-3">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                <p>Loading recent redemptions...</p>
            </div>
        </div>
        <div class="mt-4 text-center">
            <a href="/admin/openai-plus-redeem/redemptions" class="text-blue-500 hover:text-blue-700 text-sm font-medium">
                View All Redemptions →
            </a>
        </div>
    </div>

    <!-- Service Status -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Service Status</h2>
        <div id="serviceStatus" class="space-y-3">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                <p>Loading service status...</p>
            </div>
        </div>
        <div class="mt-4 text-center">
            <button onclick="checkAllServices()" class="text-blue-500 hover:text-blue-700 text-sm font-medium">
                <i class="fas fa-sync-alt mr-1"></i>Refresh All Services
            </button>
        </div>
    </div>
</div>

<!-- Cooldown Modal -->
<div id="cooldownModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-800">Set User Cooldown</h3>
                <button onclick="hideCooldownModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="cooldownForm" class="space-y-4">
                <div>
                    <label for="cooldownUsername" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                    <input type="text" id="cooldownUsername" name="username" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter username" required>
                </div>
                
                <div>
                    <label for="cooldownHours" class="block text-sm font-medium text-gray-700 mb-1">Hours</label>
                    <input type="number" id="cooldownHours" name="hours" min="1" max="168" value="24"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                           required>
                </div>
                
                <div>
                    <label for="cooldownReason" class="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                    <textarea id="cooldownReason" name="reason" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                              placeholder="Reason for cooldown (optional)"></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideCooldownModal()" 
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                        Set Cooldown
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Dashboard functionality
let dashboardData = {};
let refreshInterval = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    
    // Auto-refresh every 30 seconds
    refreshInterval = setInterval(loadDashboardData, 30000);
});

function setupEventListeners() {
    document.getElementById('refreshDashboard').addEventListener('click', loadDashboardData);
    document.getElementById('cooldownForm').addEventListener('submit', handleCooldownSubmit);
}

async function initializeDashboard() {
    await loadDashboardData();
}

async function loadDashboardData() {
    try {
        const response = await makeAuthenticatedRequest('/admin/openai-plus-redeem/api/dashboard/stats');
        
        if (response.ok) {
            const data = await response.json();
            dashboardData = data.data;
            updateDashboardDisplay();
        } else {
            showAlert('Failed to load dashboard data', 'error');
        }
    } catch (error) {
        console.error('Dashboard load error:', error);
        showAlert('Error loading dashboard', 'error');
    }
}

function updateDashboardDisplay() {
    // Update account statistics
    if (dashboardData.accounts) {
        const accounts = dashboardData.accounts;
        document.getElementById('totalAccounts').textContent = accounts.total_accounts || 0;
        document.getElementById('activeAccounts').textContent = accounts.active_accounts || 0;
        document.getElementById('expiredAccounts').textContent = accounts.expired_accounts || 0;
        
        // Update capacity bar
        const usedCapacity = accounts.total_used_capacity || 0;
        const totalCapacity = accounts.total_max_capacity || 1;
        const capacityPercent = Math.round((usedCapacity / totalCapacity) * 100);
        
        document.getElementById('usedCapacity').textContent = usedCapacity;
        document.getElementById('totalCapacity').textContent = totalCapacity;
        document.getElementById('capacityBar').style.width = `${capacityPercent}%`;
    }
    
    // Update redemption statistics
    if (dashboardData.redemptions) {
        const redemptions = dashboardData.redemptions;
        document.getElementById('totalRedemptions').textContent = redemptions.total_redemptions || 0;
        document.getElementById('activeRedemptions').textContent = redemptions.active_redemptions || 0;
        document.getElementById('pendingRedemptions').textContent = redemptions.pending_redemptions || 0;
        document.getElementById('todayRedemptions').textContent = redemptions.today_redemptions || 0;
    }
    
    // Update cooldown statistics
    if (dashboardData.cooldowns) {
        const cooldowns = dashboardData.cooldowns;
        document.getElementById('activeCooldowns').textContent = cooldowns.active_cooldowns || 0;
        document.getElementById('expiringSoon').textContent = cooldowns.expiring_soon || 0;
        document.getElementById('cooldownUsers').textContent = cooldowns.total_users_affected || 0;
    }
    
    // Update service status
    if (dashboardData.services) {
        const services = dashboardData.services;
        const status = services.status || 'unknown';
        const totalServices = services.total_services || 0;
        const healthyServices = services.initialized_services || 0;
        
        document.getElementById('serviceStatusText').textContent = status.charAt(0).toUpperCase() + status.slice(1);
        document.getElementById('totalServices').textContent = totalServices;
        document.getElementById('healthyServices').textContent = healthyServices;
        
        // Update status indicator
        const indicator = document.getElementById('serviceStatusIndicator');
        indicator.className = `status-indicator status-${status}`;
    }
    
    // Load recent activity
    loadRecentRedemptions();
    loadServiceStatus();
}

async function loadRecentRedemptions() {
    // This would load recent redemptions from the API
    // For now, show placeholder
    document.getElementById('recentRedemptions').innerHTML = `
        <div class="text-center text-gray-500 py-4">
            <p>Recent redemptions will be displayed here</p>
            <p class="text-xs mt-1">API endpoint not yet implemented</p>
        </div>
    `;
}

async function loadServiceStatus() {
    try {
        const response = await makeAuthenticatedRequest('/admin/openai-plus-redeem/api/services/health');
        
        if (response.ok) {
            const data = await response.json();
            displayServiceStatus(data.data);
        }
    } catch (error) {
        console.error('Service status error:', error);
    }
}

function displayServiceStatus(healthData) {
    const container = document.getElementById('serviceStatus');
    
    if (!healthData.services) {
        container.innerHTML = '<p class="text-gray-500">No service data available</p>';
        return;
    }
    
    const serviceHtml = Object.entries(healthData.services).map(([serviceName, health]) => {
        const status = health.status || 'unknown';
        const statusClass = `status-${status}`;
        
        return `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <span class="status-indicator ${statusClass}"></span>
                    <span class="font-medium text-gray-800">${serviceName.replace('_', ' ')}</span>
                </div>
                <div class="text-sm text-gray-600">${status}</div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = serviceHtml;
}

function showCooldownModal() {
    document.getElementById('cooldownModal').classList.remove('hidden');
}

function hideCooldownModal() {
    document.getElementById('cooldownModal').classList.add('hidden');
    document.getElementById('cooldownForm').reset();
}

async function handleCooldownSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await makeAuthenticatedRequest('/admin/openai-plus-redeem/api/cooldowns', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert(`Cooldown set for ${data.username}`, 'success');
            hideCooldownModal();
            loadDashboardData(); // Refresh dashboard
        } else {
            const error = await response.json();
            showAlert(error.error || 'Failed to set cooldown', 'error');
        }
    } catch (error) {
        console.error('Cooldown error:', error);
        showAlert('Error setting cooldown', 'error');
    }
}

async function cleanupExpiredData() {
    if (!confirm('Are you sure you want to cleanup expired data? This action cannot be undone.')) {
        return;
    }
    
    try {
        const response = await makeAuthenticatedRequest('/admin/openai-plus-redeem/api/cleanup/expired', {
            method: 'POST'
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert('Cleanup completed successfully', 'success');
            loadDashboardData(); // Refresh dashboard
        } else {
            showAlert('Cleanup failed', 'error');
        }
    } catch (error) {
        console.error('Cleanup error:', error);
        showAlert('Error during cleanup', 'error');
    }
}

async function refreshServices() {
    try {
        showAlert('Refreshing services...', 'info');
        await loadServiceStatus();
        await loadDashboardData();
        showAlert('Services refreshed', 'success');
    } catch (error) {
        console.error('Refresh error:', error);
        showAlert('Error refreshing services', 'error');
    }
}

async function checkAllServices() {
    await loadServiceStatus();
}

// Helper function for authenticated requests
function makeAuthenticatedRequest(url, options = {}) {
    const defaultHeaders = {
        'Content-Type': 'application/json',
        'X-Admin-Key': 'admin_key_placeholder' // This should be configured properly
    };
    
    return fetch(url, {
        ...options,
        headers: {
            ...defaultHeaders,
            ...options.headers
        }
    });
}

// Alert system
function showAlert(message, type) {
    const alertClass = {
        'success': 'bg-green-100 border-green-500 text-green-700',
        'warning': 'bg-yellow-100 border-yellow-500 text-yellow-700',
        'error': 'bg-red-100 border-red-500 text-red-700',
        'info': 'bg-blue-100 border-blue-500 text-blue-700'
    }[type] || 'bg-blue-100 border-blue-500 text-blue-700';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `fixed top-4 right-4 ${alertClass} border-l-4 p-4 rounded shadow-lg z-50`;
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg">&times;</button>
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
{% endblock %}
