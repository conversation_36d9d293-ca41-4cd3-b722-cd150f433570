#!/usr/bin/env python3
"""
Test script for VPN Redemption Links functionality
"""

import os
import sys
import json
import tempfile
import shutil
from datetime import datetime, timedelta

# Add the plugin directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_redemption_link_models():
    """Test the RedemptionLink models"""
    print("Testing RedemptionLink models...")

    from models import (RedemptionLink, RedemptionLinkRequest, RedemptionLinkResponse,
                       ChatTemplateConfig, BulkCreationRequest, BulkCreationResult, RedemptionAnalytics)

    # Test RedemptionLinkRequest
    request = RedemptionLinkRequest(
        customer_username="test_user",
        validity_days=30,
        server_id="auto",
        telco="digi",
        plan="unlimited",
        var_sku="VPN_30D",
        notes="Test redemption link",
        created_by="admin"
    )

    print(f"✅ RedemptionLinkRequest created: {request.customer_username}")

    # Test RedemptionLink
    link = RedemptionLink(
        id="test-link-123",
        customer_username="test_user",
        validity_days=30,
        server_id="auto",
        telco="digi",
        plan="unlimited",
        var_sku="VPN_30D",
        notes="Test redemption link"
    )

    print(f"✅ RedemptionLink created: {link.id}")

    # Test validation
    is_valid = link.is_valid()
    print(f"✅ Link validation: {is_valid}")

    can_use, reason = link.can_be_used()
    print(f"✅ Can use link: {can_use} - {reason}")

    # Test serialization
    link_dict = link.to_dict()
    link_from_dict = RedemptionLink.from_dict(link_dict)
    print(f"✅ Serialization test: {link_from_dict.id == link.id}")

    # Test ChatTemplateConfig
    chat_config = ChatTemplateConfig()
    message = chat_config.format_message(link, "/redeem/test-123", "http://localhost:5000")
    print(f"✅ Chat template formatting: {len(message)} characters")

    # Test BulkCreationRequest
    bulk_request = BulkCreationRequest(
        customer_usernames=["user1", "user2", "user3"],
        validity_days=30,
        send_via_chat=True
    )
    print(f"✅ BulkCreationRequest created: {len(bulk_request.customer_usernames)} users")

    # Test RedemptionAnalytics
    analytics = RedemptionAnalytics(
        total_links=10,
        used_links=5,
        usage_rate=50.0
    )
    print(f"✅ RedemptionAnalytics created: {analytics.usage_rate}% usage rate")

    print("✅ All model tests passed!\n")

def test_redemption_link_service():
    """Test the RedemptionLinkService"""
    print("Testing RedemptionLinkService...")

    # Create a temporary directory for testing
    temp_dir = tempfile.mkdtemp()

    try:
        from services import RedemptionLinkService
        from models import RedemptionLinkRequest, BulkCreationRequest, ChatTemplateConfig

        # Initialize service
        service = RedemptionLinkService(temp_dir)
        print("✅ RedemptionLinkService initialized")

        # Test chat template config
        chat_config = service.get_chat_template_config()
        print(f"✅ Chat template config loaded: {chat_config.enabled}")

        # Create a redemption link
        request = RedemptionLinkRequest(
            customer_username="test_customer",
            validity_days=30,
            server_id="1",
            telco="digi",
            plan="unlimited",
            var_sku="VPN_30D",
            notes="Test link from service",
            created_by="admin"
        )

        result = service.create_redemption_link(request)
        print(f"✅ Link creation result: {result.success}")

        if result.success:
            link_id = result.redemption_link.id
            print(f"✅ Created link ID: {link_id}")

            # Test retrieval
            retrieved_link = service.get_redemption_link(link_id)
            print(f"✅ Link retrieval: {retrieved_link is not None}")

            # Test validation
            is_valid, message, link = service.validate_redemption_link(link_id)
            print(f"✅ Link validation: {is_valid} - {message}")

            # Test usage
            used = service.use_redemption_link(link_id, "test_customer")
            print(f"✅ Link usage: {used}")

            # Test deactivation
            deactivated = service.deactivate_redemption_link(link_id)
            print(f"✅ Link deactivation: {deactivated}")

            # Test cleanup
            cleaned = service.cleanup_expired_links()
            print(f"✅ Cleanup result: {cleaned} links cleaned")

        # Test bulk creation
        bulk_request = BulkCreationRequest(
            customer_usernames=["bulk_user1", "bulk_user2"],
            validity_days=15,
            telco="maxis",
            created_by="admin"
        )

        bulk_result = service.create_bulk_redemption_links(bulk_request)
        print(f"✅ Bulk creation result: {bulk_result.success} - {len(bulk_result.successful_links)} created")

        # Test analytics
        analytics = service.get_analytics()
        print(f"✅ Analytics generated: {analytics.total_links} total links")

        # Test export
        success, message, data = service.export_analytics_data('json')
        print(f"✅ Analytics export: {success} - {message}")

        print("✅ All service tests passed!\n")

    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir)

def test_integration():
    """Test integration between components"""
    print("Testing integration...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        from services import RedemptionLinkService
        from models import RedemptionLinkRequest
        
        service = RedemptionLinkService(temp_dir)
        
        # Create multiple links
        links_created = []
        for i in range(3):
            request = RedemptionLinkRequest(
                customer_username=f"customer_{i}",
                validity_days=30 + i * 10,
                server_id=f"server_{i}",
                telco="digi",
                plan="unlimited",
                created_by="admin"
            )
            
            result = service.create_redemption_link(request)
            if result.success:
                links_created.append(result.redemption_link.id)
        
        print(f"✅ Created {len(links_created)} links")
        
        # Test getting all links
        all_links = service.get_all_redemption_links()
        print(f"✅ Retrieved {len(all_links)} links")
        
        # Test persistence (reload service)
        service2 = RedemptionLinkService(temp_dir)
        all_links2 = service2.get_all_redemption_links()
        print(f"✅ Persistence test: {len(all_links2)} links after reload")
        
        print("✅ All integration tests passed!\n")
        
    finally:
        shutil.rmtree(temp_dir)

def main():
    """Run all tests"""
    print("🚀 Starting VPN Redemption Links Tests\n")
    
    try:
        test_redemption_link_models()
        test_redemption_link_service()
        test_integration()
        
        print("🎉 All tests passed successfully!")
        print("\n📋 Summary:")
        print("✅ Models working correctly")
        print("✅ Service layer functional")
        print("✅ Integration tests passed")
        print("✅ Ready for deployment!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
