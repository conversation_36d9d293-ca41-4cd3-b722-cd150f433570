/**
 * Redeem Statistics Widget
 * Displays account and order redeem statistics
 */

class RedeemStatsWidget extends window.DashboardWidget {
    constructor(config) {
        super(config);
        this.stats = {
            account_redeems: {},
            order_redeems: {}
        };
    }

    async loadData() {
        try {
            const response = await fetch('/admin/api/redeem/stats');
            const result = await response.json();
            
            if (result.success) {
                this.stats = result.data;
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load redeem statistics');
            }
        } catch (error) {
            console.error('Error loading redeem stats:', error);
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container) return;

        // Build account redeems HTML
        const accountRedeemsHtml = Object.entries(this.stats.account_redeems || {})
            .sort((a, b) => b[1] - a[1]) // Sort by count descending
            .map(([account, count]) => `
                <li class="py-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                                ${account}
                            </p>
                        </div>
                        <div class="inline-flex items-center text-base font-semibold text-gray-900">
                            ${count}
                        </div>
                    </div>
                </li>
            `).join('') || '<li class="py-4 text-gray-500 text-center">No account redeems yet</li>';

        // Build order redeems HTML
        const orderRedeemsHtml = Object.entries(this.stats.order_redeems || {})
            .sort((a, b) => b[1] - a[1]) // Sort by count descending
            .map(([order, count]) => `
                <li class="py-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                                ${order}
                            </p>
                        </div>
                        <div class="inline-flex items-center text-base font-semibold text-gray-900">
                            ${count}
                        </div>
                    </div>
                </li>
            `).join('') || '<li class="py-4 text-gray-500 text-center">No order redeems yet</li>';

        this.container.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- Account Redeem Statistics -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Account Redeem Statistics</h3>
                        <div class="mt-5">
                            <ul class="divide-y divide-gray-200 max-h-64 overflow-y-auto">
                                ${accountRedeemsHtml}
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Order Redeem Statistics -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Order Redeem Statistics</h3>
                        <div class="mt-5">
                            <ul class="divide-y divide-gray-200 max-h-64 overflow-y-auto">
                                ${orderRedeemsHtml}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    shouldRefresh() {
        // Refresh every 5 minutes
        if (!this.lastRefresh) return true;
        const now = new Date();
        const diff = now - this.lastRefresh;
        return diff > 5 * 60 * 1000;
    }
}

// Register widget
window.DashboardWidgets['redeem-stats'] = RedeemStatsWidget; 