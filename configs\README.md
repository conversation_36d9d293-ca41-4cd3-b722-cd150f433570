# SteamCodeTool 配置文件说明

本文档记录了 SteamCodeTool 项目中所有配置文件的作用和用途。

## 文件夹结构

```
configs/
├── core/                    # 核心配置文件
├── services/               # 服务相关配置
├── plugins/                # 插件配置文件 (Docker 持久化)
│   ├── chat_commands/      # 聊天命令插件配置
│   └── vpn_config_generator/ # VPN 配置生成器插件配置
├── data/                   # 数据文件
├── cache/                  # 缓存文件
└── README.md              # 本说明文件
```

## 核心配置文件 (core/)

### config.json
**作用**: 主应用程序配置文件
**内容**: 
- 管理员凭据 (ADMIN_CREDENTIALS)
- API 密钥和授权码 (API_KEY, AUTHORIZATION_CODE)
- Shopee 配置 (SHOP_ID, COOKIE)
- 自动回复设置 (AUTO_REPLY_*)
- 邮箱配置 (EMAIL_CONFIGS)
- Steam 凭据 (STEAM_CREDENTIALS)
- Netflix 配置 (NETFLIX_*)
- 支付配置 (CURLEC_*)

### plugin_config.json
**作用**: 插件系统配置文件
**内容**:
- Steam 插件配置 (邮箱设置、库存配置、订单配置)
- Netflix 插件配置 (账户信息、会话配置)
- VPN 插件配置 (服务器设置、客户端配置)
- Canva 插件配置 (API 配置、订单配置)
- Shopee 插件配置 (API 配置、聊天配置)

## 插件配置文件 (plugins/)

### chat_commands/
**作用**: 聊天命令插件的持久化配置
**文件**:
- `config.json` - 插件主配置 (Webhook、调试、命令设置)
- `commands.json` - 聊天命令定义

### vpn_config_generator/
**作用**: VPN 配置生成器插件的持久化配置
**文件**:
- `config.json` - 插件主配置 (API 设置、生成器设置)
- `templates.json` - VPN 配置模板
- `telco_configs.json` - 电信运营商配置

**重要**: 这些配置文件通过 Docker 挂载持久化，确保容器更新时配置不丢失。

## 服务配置文件 (services/)

### canva_config.json
**作用**: Canva 服务专用配置
**内容**:
- API 密钥和 URL (API_KEY, API_URL)
- SKU 有效期配置 (SKU_VALIDITY)
- 邀请链接类型配置 (types)

### config_templates.json
**作用**: VPN 配置模板
**内容**:
- 各种运营商的 VPN 配置模板
- 包含 Digi、Umobile、Celcom、Maxis、Yes、Yoodo、Unifi 等

### vpn_servers.json
**作用**: VPN 服务器列表
**内容**:
- VPN 服务器域名、端口、凭据
- 服务器连接状态

## 数据文件 (data/)

### canva_orders.json
**作用**: Canva 订单数据
**内容**:
- 订单日期、到期日期
- 兑换状态和时间
- 兑换令牌

### manual_invoice.json
**作用**: 手动发票数据
**内容**:
- 发票详情 (金额、货币、客户信息)
- 支付状态和记录
- 发票链接和设置

### manual_orders.json
**作用**: 手动订单数据
**内容**:
- 手动创建的订单列表
- 订单状态和详情

### netflix_sessions.json (已移动到 data/netflix/)
**作用**: Netflix 会话管理
**内容**:
- 订单与账户的映射关系
- Netflix 账户使用记录
**新位置**: `data/netflix/netflix_sessions.json`

### redeemed_stock.json
**作用**: 已兑换库存记录
**内容**:
- 订单号与兑换内容的映射
- 替换次数记录
- SKU 类型信息

### sent_orders.json
**作用**: 已发送订单追踪
**内容**:
- 已发送消息的订单号列表
- 防止重复发送

### dashboard_data.json
**作用**: 仪表板数据
**内容**:
- 认证码记录 (auth_code_records)
- 每日统计数据 (daily_stats)
- 最后更新时间

## 缓存文件 (cache/)

### ai_reply_cooldown.json
**作用**: AI 回复冷却时间缓存
**内容**:
- 用户 ID 与最后回复时间的映射
- 防止 AI 回复过于频繁

### auto_reply_cooldown.json
**作用**: 自动回复冷却时间缓存
**内容**:
- 用户 ID 与最后自动回复时间的映射
- 控制自动回复频率

## 重要说明

### 数据持久化
- 所有这些文件都需要在 Docker 部署时进行卷挂载
- 不挂载这些文件会导致数据丢失

### 备份建议
- 定期备份 `configs/` 整个文件夹
- 特别注意 `core/config.json` 包含敏感信息
- `data/` 文件夹包含业务数据，需要重点保护

### 安全注意事项
- `config.json` 包含 API 密钥、密码等敏感信息
- 不要将包含真实凭据的配置文件提交到版本控制
- 使用环境变量或配置模板来管理敏感信息

## 迁移说明

从根目录迁移到 `configs/` 文件夹后：
1. 所有 Python 代码中的文件路径已更新
2. Docker 配置文件已更新卷挂载路径
3. 文档已更新相应的路径引用
4. 保持了原有的功能和兼容性

### 迁移完成的文件

#### 已移动的文件：
- `config.json` → `configs/core/config.json`
- `plugin_config.json` → `configs/core/plugin_config.json`
- `canva_config.json` → `configs/services/canva_config.json`
- `config_templates.json` → `configs/services/config_templates.json`
- `vpn_servers.json` → `configs/services/vpn_servers.json`
- `canva_orders.json` → `configs/data/canva_orders.json`
- `manual_invoice.json` → `configs/data/manual_invoice.json`
- `manual_orders.json` → `configs/data/manual_orders.json`
- `netflix_sessions.json` → `data/netflix/netflix_sessions.json`
- `redeemed_stock.json` → `configs/data/redeemed_stock.json`
- `sent_orders.json` → `configs/data/sent_orders.json`
- `dashboard_data.json` → `configs/data/dashboard_data.json`
- `ai_reply_cooldown.json` → `configs/cache/ai_reply_cooldown.json`
- `auto_reply_cooldown.json` → `configs/cache/auto_reply_cooldown.json`

#### 已更新的代码文件：
- `config.py` - 更新配置文件路径
- `services/auto_reply_service.py` - 更新冷却文件路径
- `services/chat_service.py` - 更新AI回复冷却文件路径
- `services/canva_service.py` - 更新配置和订单文件路径
- `services/order_service.py` - 更新手动发票文件路径
- `services/manual_order_service.py` - 更新手动订单文件路径
- `services/netflix_session_service.py` - 更新会话文件路径
- `services/netflix_service.py` - 更新手动发票文件路径
- `services/stock_service.py` - 更新库存文件路径
- `api/vpn_routes.py` - 更新VPN配置文件路径
- `api/admin_routes.py` - 更新多个数据文件路径
- `api/canva_routes.py` - 更新Canva配置文件路径
- `api/order_routes.py` - 更新手动订单文件路径
- `core/plugin_manager.py` - 更新插件配置文件路径

#### 已更新的配置文件：
- `docker-compose.steamcodetool.yml` - 简化卷挂载为单个configs目录
- `Pack.bat` - 更新打包脚本以包含configs目录

#### 已更新的文档：
- `docs/DOCKER_QUICK_START.md` - 更新Docker运行命令和卷挂载说明
- `docs/STEAMCODETOOL_DOCKER.md` - 更新文件组织说明
- `docker-hub-push-mtyb-tools.bat` - 更新Docker使用说明

### 优势

1. **更清晰的项目结构** - 所有配置文件现在按功能分类组织
2. **简化的Docker部署** - 只需挂载一个configs目录而不是14个单独文件
3. **更好的维护性** - 配置文件有明确的分类和文档说明
4. **减少根目录混乱** - 根目录现在更加整洁
5. **向后兼容** - 所有现有功能保持不变
