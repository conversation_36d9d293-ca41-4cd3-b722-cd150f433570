{% extends "base.html" %}

{% block title %}Netflix Settings{% endblock %}
{% block header %}Netflix Settings{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="netflixSettingsData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Sidebar -->
        <div class="w-64 mr-8">
            <nav class="space-y-1">
                <template
                    x-for="(section, index) in ['Account Management', 'Performance Settings', 'Redeem Periods', 'Email Settings']"
                    :key="index">
                    <a href="#" @click.prevent="currentSection = section; animateSection()"
                        :class="{'bg-gray-100 text-gray-900': currentSection === section, 'text-gray-600 hover:bg-gray-50 hover:text-gray-900': currentSection !== section}"
                        class="group flex items-center px-3 py-2 text-sm font-medium rounded-md sidebar-item">
                        <span x-text="section"></span>
                    </a>
                </template>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1">
            <!-- Account Management Section -->
            <div x-show="currentSection === 'Account Management'" class="section-content">
                <div class="bg-white p-6 rounded-lg shadow-md config-item mb-8">
                    <h2 class="text-xl font-bold mb-4">Netflix Account Management</h2>
                    
                    <!-- Netflix Account Limit -->
                    <div class="mb-6">
                        <label for="netflix_account_limit" class="block text-sm font-medium text-gray-700">Netflix Account Limit</label>
                        <input type="number" id="netflix_account_limit" x-model="config.NETFLIX_ACCOUNT_LIMIT"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-sm text-gray-500">Maximum number of users allowed per Netflix account.</p>
                    </div>

                    <!-- Netflix Accounts -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700">Netflix Accounts (Email Only)</label>
                        <div class="mt-2">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Email</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <template x-for="(email, index) in config.NETFLIX_ACCOUNTS" :key="index">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="email" :value="email"
                                                    @input="updateNetflixEmail(index, $event.target.value)"
                                                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <button @click.prevent="removeNetflixAccount(index)"
                                                    class="text-red-600 hover:text-red-900">Delete</button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                            <button @click.prevent="addNetflixAccount"
                                class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Add Netflix Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Settings -->
            <div x-show="currentSection === 'Performance Settings'" class="section-content">
                <div class="bg-white p-6 rounded-lg shadow-md config-item mb-8">
                    <h2 class="text-xl font-bold mb-4">Performance Settings</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="netflix_max_retries" class="block text-sm font-medium text-gray-700">
                                Maximum Retries
                            </label>
                            <input type="number" 
                                   id="netflix_max_retries" 
                                   x-model="config.NETFLIX_MAX_RETRIES"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>

                        <div>
                            <label for="netflix_request_timeout" class="block text-sm font-medium text-gray-700">
                                Request Timeout (seconds)
                            </label>
                            <input type="number" 
                                   id="netflix_request_timeout" 
                                   x-model="config.NETFLIX_REQUEST_TIMEOUT"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>

                        <div>
                            <label for="netflix_retry_delay" class="block text-sm font-medium text-gray-700">
                                Retry Delay (seconds)
                            </label>
                            <input type="number" 
                                   id="netflix_retry_delay" 
                                   x-model="config.NETFLIX_RETRY_DELAY"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>

                        <div>
                            <label for="netflix_session_cooldown" class="block text-sm font-medium text-gray-700">
                                Session Cooldown Time (seconds)
                            </label>
                            <input type="number" 
                                   id="netflix_session_cooldown" 
                                   x-model="config.NETFLIX_SESSION_COOLDOWN_TIME"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Redeem Period Settings -->
            <div x-show="currentSection === 'Redeem Periods'" class="section-content">
                <div class="bg-white p-6 rounded-lg shadow-md config-item mb-8">
                    <h2 class="text-xl font-bold mb-4">Netflix Redeem Periods</h2>
                    <div class="mb-4">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        SKU
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Period (Days)
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(period, sku) in config.NETFLIX_REDEEM_PERIODS" :key="sku">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap" x-text="sku"></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="number" 
                                                   x-model="config.NETFLIX_REDEEM_PERIODS[sku]"
                                                   class="block w-24 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click="deleteRedeemPeriod(sku)"
                                                class="text-red-600 hover:text-red-900">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <button @click="addNetflixRedeemPeriod"
                            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Netflix Redeem Period
                        </button>
                    </div>
                </div>
            </div>

            <!-- Email Settings -->
            <div x-show="currentSection === 'Email Settings'" class="section-content">
                <div class="bg-white p-6 rounded-lg shadow-md config-item mb-8">
                    <h2 class="text-xl font-bold mb-4">Netflix Email Settings</h2>
                    
                    <!-- Netflix Sign-in Code Email -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700">Netflix Sign-in Code Email</label>
                        <div class="mt-2">
                            <input type="email" x-model="config.NETFLIX_SIGNIN_EMAIL.email"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                placeholder="Email for receiving Netflix sign-in codes">
                        </div>
                    </div>

                    <!-- Netflix Sign-in Code App Password -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700">Netflix Sign-in Code App Password</label>
                        <div class="mt-2">
                            <input type="password" x-model="config.NETFLIX_SIGNIN_EMAIL.app_password"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                placeholder="App password for the sign-in code email">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="mt-6">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function netflixSettingsData() {
        return {
            config: {},
            currentSection: 'Account Management',
            emailChanges: {},
            isLoaded: false,
            init() {
                this.loadConfig();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        
                        // Initialize Netflix configurations if not exists
                        if (!this.config.NETFLIX_ACCOUNTS) {
                            this.config.NETFLIX_ACCOUNTS = [];
                        }
                        if (!this.config.NETFLIX_REDEEM_PERIODS) {
                            this.config.NETFLIX_REDEEM_PERIODS = {};
                        }
                        if (!this.config.NETFLIX_SIGNIN_EMAIL) {
                            this.config.NETFLIX_SIGNIN_EMAIL = { email: '', app_password: '' };
                        }
                        
                        // Store initial values for email change tracking
                        this.initialNetflixAccounts = [...this.config.NETFLIX_ACCOUNTS];
                        
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            updateNetflixEmail(index, newEmail) {
                const initialEmail = this.initialNetflixAccounts[index];
                
                if (initialEmail !== newEmail) {
                    this.emailChanges[initialEmail] = newEmail;
                } else {
                    delete this.emailChanges[initialEmail];
                }

                this.config.NETFLIX_ACCOUNTS[index] = newEmail;
            },
            addNetflixAccount() {
                this.config.NETFLIX_ACCOUNTS.push('');
            },
            removeNetflixAccount(index) {
                if (confirm('Are you sure you want to delete this Netflix account?')) {
                    this.config.NETFLIX_ACCOUNTS.splice(index, 1);
                }
            },
            addNetflixRedeemPeriod() {
                const newSku = prompt("Enter the SKU for the new Netflix redeem period:");
                if (newSku) {
                    if (!this.config.NETFLIX_REDEEM_PERIODS) {
                        this.config.NETFLIX_REDEEM_PERIODS = {};
                    }
                    this.config.NETFLIX_REDEEM_PERIODS[newSku] = 30; // Default to 30 days
                }
            },
            deleteRedeemPeriod(sku) {
                if (confirm('Are you sure you want to delete this redeem period?')) {
                    delete this.config.NETFLIX_REDEEM_PERIODS[sku];
                    this.config = { ...this.config }; // Trigger Alpine.js reactivity
                }
            },
            updateNetflixSessions() {
                fetch('/admin/update_netflix_emails', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.emailChanges),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Netflix emails updated successfully');
                        this.initialNetflixAccounts = [...this.config.NETFLIX_ACCOUNTS]; // Update initial values
                        this.emailChanges = {}; // Clear change tracking
                    } else {
                        console.error('Failed to update Netflix emails');
                    }
                })
                .catch(error => {
                    console.error('Error updating Netflix emails:', error);
                });
            },
            saveConfig() {
                // Convert numeric fields to numbers
                const numericFields = [
                    'NETFLIX_MAX_RETRIES',
                    'NETFLIX_REQUEST_TIMEOUT',
                    'NETFLIX_RETRY_DELAY',
                    'NETFLIX_SESSION_COOLDOWN_TIME',
                    'NETFLIX_ACCOUNT_LIMIT'
                ];
                
                numericFields.forEach(field => {
                    if (this.config[field] !== undefined) {
                        this.config[field] = Number(this.config[field]);
                    }
                });

                // Convert NETFLIX_REDEEM_PERIODS values to numbers
                if (this.config.NETFLIX_REDEEM_PERIODS) {
                    for (let key in this.config.NETFLIX_REDEEM_PERIODS) {
                        this.config.NETFLIX_REDEEM_PERIODS[key] = Number(this.config.NETFLIX_REDEEM_PERIODS[key]);
                    }
                }

                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                .then(response => response.json())
                .then(data => {
                    // Handle Netflix email changes if any
                    if (Object.keys(this.emailChanges).length > 0) {
                        this.updateNetflixSessions();
                    }
                    
                    alert(data.message);
                    this.animateSaveButton();
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the configuration.');
                });
            },
            animateInitialLoad() {
                anime({
                    targets: '.sidebar-item',
                    translateX: [-50, 0],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutQuad'
                });

                this.animateSection();
            },
            animateSection() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}