# SteamCodeTool

A comprehensive plugin-based system for managing Steam codes, Netflix sessions, VPN configurations, and Canva orders with integrated Shopee API support.

## 🚀 One-Click Deployment

### Windows
```bash
.\deploy-steamcodetool.bat
```

### Linux/macOS
```bash
chmod +x deploy-steamcodetool.sh
./deploy-steamcodetool.sh
```

**That's it!** No configuration needed, no permission issues, fully automated.

Access your application at: **http://localhost:5000**

## ✨ Features

- **🔌 Plugin Architecture** - Modular system with Steam, Netflix, VPN, and Canva plugins
- **🛒 Shopee Integration** - Complete API integration for order management
- **🤖 AI Chat Support** - Automated customer service responses
- **📊 Admin Dashboard** - Comprehensive management interface
- **🔐 Secure Authentication** - Multi-level access control
- **📱 Responsive Design** - Works on desktop and mobile
- **🐳 Docker Ready** - One-click deployment with Docker

## 📋 Quick Links

- **[One-Click Deployment Guide](DEPLOY_README.md)** - Complete automated setup
- **[Docker Quick Start](docs/DOCKER_QUICK_START.md)** - Docker deployment options
- **[Permission Issues Guide](DOCKER_PERMISSION_ISSUES.md)** - Troubleshooting help

## 🔧 Manual Installation (Advanced)

If you prefer manual setup:

1. **Clone the repository**
   ```bash
   git clone https://github.com/limjianhui789/SteamCodeTool.git
   cd SteamCodeTool
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python main.py
   ```

## 🌐 Application URLs

After deployment:

- **Main Application**: http://localhost:5000
- **Health Check**: http://localhost:5000/health
- **Admin Panel**: http://localhost:5000/admin
- **Plugin Manager**: http://localhost:5000/admin/plugins

## 📦 Plugin System

SteamCodeTool uses a modular plugin architecture:

- **Chat Commands** - Automated chat responses and commands
- **VPN Management** - VPN configuration and server management  
- **Canva Integration** - Canva Pro account management
- **Netflix Sessions** - Netflix account session handling

## 🔒 Default Credentials

- **Username**: admin
- **Password**: whitepaperh0817

⚠️ **Important**: Change these credentials after first login!

## 📊 Management Commands

```bash
# View application logs
docker-compose -f docker-compose.steamcodetool.yml logs -f

# Stop the application
docker-compose -f docker-compose.steamcodetool.yml down

# Restart the application  
docker-compose -f docker-compose.steamcodetool.yml restart

# Update to latest version
.\deploy-steamcodetool.bat  # Windows
./deploy-steamcodetool.sh   # Linux/macOS
```

## 🆘 Troubleshooting

### Application Won't Start
```bash
# Check logs
docker-compose -f docker-compose.steamcodetool.yml logs

# Verify Docker is running
docker info
```

### Can't Access Application
```bash
# Check if container is running
docker ps

# Test health endpoint
curl http://localhost:5000/health
```

### Complete Reset
```bash
# Remove everything and start fresh
docker-compose -f docker-compose.steamcodetool.yml down
docker volume rm mtyb-configs mtyb-logs mtyb-data
.\deploy-steamcodetool.bat  # Run deployment again
```

## 📁 Project Structure

```
SteamCodeTool/
├── 🚀 deploy-steamcodetool.bat     # One-click Windows deployment
├── 🚀 deploy-steamcodetool.sh      # One-click Linux/macOS deployment
├── 📖 DEPLOY_README.md             # Complete deployment guide
├── 🐳 docker-compose.steamcodetool.yml  # Docker configuration
├── 📄 Dockerfile                   # Docker build configuration
├── 🐍 main.py                      # Main application entry point
├── 🔌 plugins/                     # Plugin directory
├── 🛠️ services/                    # Core services
├── 🌐 api/                         # API routes
├── 📊 templates/                   # Web templates
└── 📚 docs/                        # Documentation
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with Flask and modern web technologies
- Docker integration for easy deployment
- Plugin architecture for extensibility
- Shopee API integration for e-commerce functionality

---

**Need help?** Check out our [complete deployment guide](DEPLOY_README.md) or [troubleshooting documentation](DOCKER_PERMISSION_ISSUES.md).
