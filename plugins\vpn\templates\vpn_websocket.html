{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{{ title }}</h3>
                    <button type="button" class="btn btn-info" onclick="refreshStats()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    {% if ws_stats %}
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-plug"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Active Connections</span>
                                    <span class="info-box-number" id="active-connections">
                                        {{ ws_stats.get('active_connections', 0) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-arrow-up"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Connections</span>
                                    <span class="info-box-number" id="total-connections">
                                        {{ ws_stats.get('total_connections', 0) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-exchange-alt"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Messages Sent</span>
                                    <span class="info-box-number" id="messages-sent">
                                        {{ ws_stats.get('messages_sent', 0) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger">
                                    <i class="fas fa-inbox"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Messages Received</span>
                                    <span class="info-box-number" id="messages-received">
                                        {{ ws_stats.get('messages_received', 0) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Connection Details</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <td><strong>Server Status:</strong></td>
                                                <td>
                                                    {% if ws_stats.get('server_running', False) %}
                                                    <span class="badge badge-success">Running</span>
                                                    {% else %}
                                                    <span class="badge badge-danger">Stopped</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Uptime:</strong></td>
                                                <td>{{ ws_stats.get('uptime', 'N/A') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Last Activity:</strong></td>
                                                <td>{{ ws_stats.get('last_activity', 'N/A') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Error Count:</strong></td>
                                                <td>
                                                    <span class="badge badge-{{ 'danger' if ws_stats.get('error_count', 0) > 0 else 'success' }}">
                                                        {{ ws_stats.get('error_count', 0) }}
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Performance Metrics</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <td><strong>Average Response Time:</strong></td>
                                                <td>{{ ws_stats.get('avg_response_time', 'N/A') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Peak Connections:</strong></td>
                                                <td>{{ ws_stats.get('peak_connections', 0) }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Data Transferred:</strong></td>
                                                <td>{{ ws_stats.get('data_transferred', 'N/A') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Memory Usage:</strong></td>
                                                <td>{{ ws_stats.get('memory_usage', 'N/A') }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if ws_stats.get('recent_events') %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Recent Events</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Timestamp</th>
                                                    <th>Event Type</th>
                                                    <th>Description</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for event in ws_stats.recent_events %}
                                                <tr>
                                                    <td>{{ event.get('timestamp', 'N/A') }}</td>
                                                    <td>
                                                        <span class="badge badge-info">{{ event.get('type', 'Unknown') }}</span>
                                                    </td>
                                                    <td>{{ event.get('description', 'N/A') }}</td>
                                                    <td>
                                                        {% if event.get('status') == 'success' %}
                                                        <span class="badge badge-success">Success</span>
                                                        {% elif event.get('status') == 'error' %}
                                                        <span class="badge badge-danger">Error</span>
                                                        {% else %}
                                                        <span class="badge badge-secondary">{{ event.get('status', 'Unknown') }}</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-plug fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">WebSocket Statistics Unavailable</h5>
                        <p class="text-muted">Unable to retrieve WebSocket statistics from the API.</p>
                        <button type="button" class="btn btn-primary" onclick="refreshStats()">
                            <i class="fas fa-sync"></i> Try Again
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Loading statistics...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showLoading() {
    $('#loadingModal').modal('show');
}

function hideLoading() {
    $('#loadingModal').modal('hide');
}

function refreshStats() {
    showLoading();
    
    fetch('{{ url_for("vpn.api_websocket_stats") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.error) {
            toastr.error('Failed to refresh statistics: ' + data.error);
        } else {
            updateStats(data);
            toastr.success('Statistics refreshed successfully!');
        }
    })
    .catch(error => {
        hideLoading();
        toastr.error('Failed to refresh statistics: ' + error.message);
    });
}

function updateStats(stats) {
    // Update the statistics on the page
    const elements = {
        'active-connections': stats.active_connections || 0,
        'total-connections': stats.total_connections || 0,
        'messages-sent': stats.messages_sent || 0,
        'messages-received': stats.messages_received || 0
    };
    
    for (const [id, value] of Object.entries(elements)) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }
}

// Auto-refresh every 30 seconds
setInterval(function() {
    refreshStats();
}, 30000);
</script>
{% endblock %}
