import json
import time
from datetime import datetime
import os
from services.chat_service import send_chat_message
from services.conversation_service import get_conversation_info_by_username
import config
from utils.session import session
from urllib.parse import quote

COOLDOWN_FILE = 'configs/cache/auto_reply_cooldown.json'

def load_cooldown():
    if not os.path.exists(COOLDOWN_FILE):
        save_cooldown({})
    with open(COOLDOWN_FILE, 'r') as f:
        return json.load(f)

def save_cooldown(cooldown_data):
    with open(COOLDOWN_FILE, 'w') as f:
        json.dump(cooldown_data, f)

def set_conversation_unread(conversation_id):
    params = {
        "request_id": int(time.time() * 1000),  # Current timestamp in milliseconds
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.6.2",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }
    
    payload = {
        "biz_id": 0,
        "shop_id": config.SHOP_ID
    }
    
    url = f"https://seller.shopee.com.my/webchat/api/v1.2/conversations/{conversation_id}/unread"
    response = session.put(url, params=params, json=payload)
    print(response.content)
    return response.status_code == 200

def check_and_send_auto_reply(conversation):
    if not config.AUTO_REPLY_ENABLED:
        return

    if conversation.get('unread_count', 0) == 0:
        return

    cooldown_data = load_cooldown()
    current_time = time.time()
    user_id = conversation['to_id']
    
    if str(user_id) in cooldown_data: 
        last_reply_time = cooldown_data[str(user_id)]
        cooldown_minutes = float(config.AUTO_REPLY_COOLDOWN_MINUTES)
        if current_time - last_reply_time < (cooldown_minutes * 60):
            return
    
    cooldown_minutes = float(config.AUTO_REPLY_COOLDOWN_MINUTES)
    delay_minutes = float(config.AUTO_REPLY_DELAY_MINUTES)
    
    # Check cooldown
    if user_id in cooldown_data:
        last_reply_time = cooldown_data[user_id]
        if current_time - last_reply_time < (cooldown_minutes * 60):
            return

    # Check message delay
    last_message_time = datetime.strptime(
        conversation['last_message_time'], 
        "%Y-%m-%dT%H:%M:%S%z"
    ).timestamp()
    
    if current_time - last_message_time < (delay_minutes * 60):
        return

    # Send auto reply
    try:
        payload = {
            'text': config.AUTO_REPLY_MESSAGE,
            'username': conversation.get('to_name')  # Add username to payload
        }
        send_chat_message(payload)
        set_conversation_unread(conversation['id'])
        
        # Update cooldown
        cooldown_data[str(user_id)] = current_time  # Convert user_id to string for JSON serialization
        save_cooldown(cooldown_data)
    except Exception as e:
        print(f"Error sending auto reply: {str(e)}")
