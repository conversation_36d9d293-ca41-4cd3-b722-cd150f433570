/**
 * Steam Session Cooldowns Widget
 * Displays active Steam session cooldowns with ability to reset
 */

class SteamCooldownsWidget extends window.DashboardWidget {
    constructor(config) {
        super(config);
        this.cooldowns = [];
        this.dataTable = null;
    }

    async loadData() {
        try {
            const response = await fetch(this.config.data_endpoint);
            const result = await response.json();
            
            if (result.success) {
                this.cooldowns = result.data.cooldowns || [];
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load cooldowns');
            }
        } catch (error) {
            console.error('Error loading Steam cooldowns:', error);
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container) return;

        // Ensure cooldowns is an array
        if (!Array.isArray(this.cooldowns)) {
            this.cooldowns = [];
        }

        // Build table rows HTML
        const rowsHtml = this.cooldowns.map(cooldown => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${cooldown.username}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${cooldown.order_id}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${cooldown.remaining_cooldown}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded reset-cooldown-btn" 
                            data-username="${cooldown.username}" 
                            data-order-id="${cooldown.order_id}">
                        Reset
                    </button>
                </td>
            </tr>
        `).join('') || '<tr><td colspan="4" class="text-center py-4 text-gray-500">No active cooldowns</td></tr>';

        this.container.innerHTML = `
            <div class="bg-white overflow-hidden shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h2 class="text-2xl font-semibold mb-4">Active Steam Session Cooldowns</h2>
                    <table id="steamCooldownTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Username
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Order ID
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Remaining Cooldown (s)
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${rowsHtml}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        // Initialize DataTable if available and there are cooldowns
        if (this.cooldowns.length > 0) {
            this.initDataTable();
        }

        // Add event listeners
        this.container.querySelectorAll('.reset-cooldown-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const username = e.target.dataset.username;
                const orderId = e.target.dataset.orderId;
                this.resetCooldown(username, orderId);
            });
        });
    }

    initDataTable() {
        if (this.dataTable) {
            this.dataTable.destroy();
        }

        // Wait for jQuery and DataTables to be available
        if ($ && $.fn.DataTable && !$.fn.DataTable.isDataTable('#steamCooldownTable')) {
            this.dataTable = $('#steamCooldownTable').DataTable({
                responsive: true,
                order: [[2, 'asc']],
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                language: {
                    paginate: {
                        previous: "&#8592;",
                        next: "&#8594;"
                    },
                    lengthMenu: "_MENU_ per page",
                    search: "",
                    searchPlaceholder: "Search...",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries"
                },
                dom: "<'flex justify-between items-center'<l><f>>" +
                    "<'overflow-x-auto'tr>" +
                    "<'flex justify-between items-center'<i><p>>",
            });
        }
    }

    async resetCooldown(username, orderId) {
        if (!confirm(`Are you sure you want to reset the cooldown for ${username} with Order ID ${orderId}?`)) {
            return;
        }

        try {
            const response = await fetch('/admin/reset_cooldown', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    'username': username,
                    'order_id': orderId
                })
            });
            const result = await response.json();
            
            if (result.message) {
                alert(result.message);
                // Refresh the widget
                await this.refresh();
            } else {
                alert('Error resetting cooldown: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            alert('Error resetting cooldown: ' + error.message);
        }
    }

    destroy() {
        if (this.dataTable) {
            this.dataTable.destroy();
        }
    }
}

// Register widget
window.DashboardWidgets['steam-cooldowns'] = SteamCooldownsWidget; 