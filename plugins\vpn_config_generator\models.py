"""
Models for VPN Config Generator Plugin
"""

from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import uuid


@dataclass
class VPNConfigRequest:
    """Model for VPN configuration request"""
    server: str
    days: str
    telco: str
    plan: str
    username: str
    sku: Optional[str] = None  # SKU for intelligent server selection
    var_sku: Optional[str] = None  # Variable SKU for more specific matching

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNConfigRequest':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class VPNConfigResponse:
    """Model for VPN configuration response"""
    success: bool
    config: Optional[str] = None
    created_date: Optional[str] = None
    expired_date: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None
    client_id: Optional[str] = None
    numeric_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNConfigResponse':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class VPNAPIConfig:
    """Configuration for VPN API integration"""
    enabled: bool = True
    use_vpn_plugin_api: bool = True  # Use existing VPN plugin API
    fallback_api_endpoint: str = ""  # Fallback API endpoint
    fallback_username: str = ""
    fallback_password: str = ""
    timeout: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNAPIConfig':
        """Create from dictionary"""
        # Clean URL endpoint by stripping whitespace
        if 'fallback_api_endpoint' in data and data['fallback_api_endpoint']:
            cleaned_url = data['fallback_api_endpoint'].strip()
            # Ensure URL starts with http:// or https://
            if cleaned_url and not cleaned_url.startswith(('http://', 'https://')):
                cleaned_url = 'https://' + cleaned_url
            data['fallback_api_endpoint'] = cleaned_url
        return cls(**data)


@dataclass
class ConfigGeneratorSettings:
    """Settings for config generation"""
    default_validity_days: int = 30
    username_prefix: str = "user"
    add_random_suffix: bool = True
    config_format: str = "vless"  # vless, vmess, etc.

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigGeneratorSettings':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class VPNPlan:
    """Model for VPN plan within a telco"""
    id: str
    name: str
    description: str
    template: str  # The actual VPN configuration template
    variables: Dict[str, str]  # Variable descriptions for this plan
    info_message_template: Optional[str] = None  # Custom info message template
    enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNPlan':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class VPNTelco:
    """Model for VPN telco (telecommunications provider)"""
    id: str
    name: str
    description: str
    plans: Dict[str, VPNPlan]  # plan_id -> VPNPlan
    enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        # Convert plans dict to serializable format
        result['plans'] = {k: v.to_dict() for k, v in self.plans.items()}
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNTelco':
        """Create from dictionary"""
        plans_data = data.get('plans', {})
        plans = {k: VPNPlan.from_dict(v) for k, v in plans_data.items()}

        return cls(
            id=data['id'],
            name=data['name'],
            description=data['description'],
            plans=plans,
            enabled=data.get('enabled', True),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )

    def get_plan(self, plan_id: str) -> Optional[VPNPlan]:
        """Get a specific plan by ID"""
        return self.plans.get(plan_id)

    def add_plan(self, plan: VPNPlan) -> None:
        """Add a plan to this telco"""
        self.plans[plan.id] = plan

    def remove_plan(self, plan_id: str) -> bool:
        """Remove a plan from this telco"""
        if plan_id in self.plans:
            del self.plans[plan_id]
            return True
        return False

    def get_enabled_plans(self) -> Dict[str, VPNPlan]:
        """Get all enabled plans"""
        return {k: v for k, v in self.plans.items() if v.enabled}


@dataclass
class ConfigTemplate:
    """Legacy model for backward compatibility - will be deprecated"""
    id: str
    name: str
    description: str
    template: str
    variables: Dict[str, str]  # Variable descriptions
    category: str = "general"
    enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigTemplate':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class TemplateVariable:
    """Model for template variable"""
    name: str
    description: str
    default_value: str = ""
    required: bool = True
    variable_type: str = "string"  # string, number, boolean

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


@dataclass
class ChatCommand:
    """Chat command model"""
    command: str
    description: str
    response_text: str
    image_urls: List[str] = None
    required_params: List[str] = None
    enabled: bool = True
    created_at: str = None
    updated_at: str = None

    def __post_init__(self):
        if self.image_urls is None:
            self.image_urls = []
        if self.required_params is None:
            self.required_params = []
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        if self.updated_at is None:
            self.updated_at = datetime.now().isoformat()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatCommand':
        return cls(
            command=data.get('command', ''),
            description=data.get('description', ''),
            response_text=data.get('response_text', ''),
            image_urls=data.get('image_urls', []),
            required_params=data.get('required_params', []),
            enabled=data.get('enabled', True),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class WebhookMessage:
    """Webhook message model"""
    message_id: str
    conversation_id: str
    sender_name: str
    message_text: str
    timestamp: str
    message_type: str = "text"

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WebhookMessage':
        return cls(
            message_id=data.get('message_id', ''),
            conversation_id=data.get('conversation_id', ''),
            sender_name=data.get('sender_name', ''),
            message_text=data.get('message_text', ''),
            timestamp=data.get('timestamp', ''),
            message_type=data.get('message_type', 'text')
        )

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class CommandResponse:
    """Command response model"""
    text: str
    image_url: str = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class VPNCommandConfig:
    """Configuration for VPN chat commands"""
    command_name: str = "config"
    command_description: str = "Generate a VPN configuration"
    response_text: str = "🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️"
    enabled: bool = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNCommandConfig':
        return cls(
            command_name=data.get('command_name', 'config'),
            command_description=data.get('command_description', 'Generate a VPN configuration'),
            response_text=data.get('response_text', '🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️'),
            enabled=data.get('enabled', True)
        )

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class VPNUser:
    """Model for VPN user with UUID tracking"""
    uuid: str
    order_sn: str
    buyer_username: str
    sku: str
    var_sku: str
    assigned_telco: Optional[str] = None  # For SKU-based restrictions
    allowed_telcos: List[str] = None  # List of allowed telcos
    is_restricted: bool = False  # True for 'my_' prefixed SKUs
    created_at: Optional[str] = None
    last_access: Optional[str] = None
    configurations_generated: int = 0
    generated_configs: List[Dict[str, Any]] = None  # Store all generated configs with client_id, telco, etc.
    order_claimed: bool = False  # True if order has been claimed (new UUID generated or renewed to other UUID)

    def __post_init__(self):
        if self.allowed_telcos is None:
            self.allowed_telcos = []
        if self.generated_configs is None:
            self.generated_configs = []
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        # Check if this is a restricted SKU (my_ prefix)
        if self.var_sku and self.var_sku.startswith('my_'):
            self.is_restricted = True

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNUser':
        """Create from dictionary"""
        return cls(**data)

    def can_access_telco(self, telco: str) -> bool:
        """Check if user can access a specific telco"""
        if not self.is_restricted:
            return True

        # If restricted but no telco assigned yet, allow access (will be locked after first selection)
        if self.assigned_telco is None:
            return True

        # If telco is assigned, only allow access to that telco
        return telco == self.assigned_telco

    def assign_telco(self, telco: str) -> None:
        """Assign a telco to restricted user"""
        if self.is_restricted:
            self.assigned_telco = telco
            if telco not in self.allowed_telcos:
                self.allowed_telcos.append(telco)


@dataclass
class VPNOrderRequest:
    """Model for VPN order processing request"""
    order_sn: str
    buyer_username: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNOrderRequest':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class VPNOrderResponse:
    """Model for VPN order processing response"""
    success: bool
    order_sn: str
    status: str
    user_uuid: Optional[str] = None
    sku: Optional[str] = None
    var_sku: Optional[str] = None
    buyer_username: Optional[str] = None
    is_repeat_customer: bool = False
    assigned_telco: Optional[str] = None
    allowed_telcos: List[str] = None
    is_restricted: bool = False
    configurations_generated: int = 0
    order_claimed: bool = False
    error: Optional[str] = None
    message: Optional[str] = None

    def __post_init__(self):
        if self.allowed_telcos is None:
            self.allowed_telcos = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNOrderResponse':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class VPNConfigurationRequest:
    """Enhanced VPN configuration request with user context"""
    user_uuid: str
    order_sn: str
    server: str
    days: str
    telco: str
    plan: str
    username: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNConfigurationRequest':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class SKURestriction:
    """Model for SKU-based access restrictions"""
    sku_pattern: str  # Pattern to match SKUs (e.g., "my_*")
    restriction_type: str  # "telco_lock", "plan_limit", etc.
    allowed_telcos: List[str] = None
    max_configurations: int = -1  # -1 for unlimited
    single_telco_only: bool = False
    description: str = ""
    enabled: bool = True

    def __post_init__(self):
        if self.allowed_telcos is None:
            self.allowed_telcos = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SKURestriction':
        """Create from dictionary"""
        return cls(**data)

    def matches_sku(self, sku: str) -> bool:
        """Check if this restriction applies to the given SKU"""
        import fnmatch
        return fnmatch.fnmatch(sku.lower(), self.sku_pattern.lower())


@dataclass
class TelcoConfigManager:
    """Manager for telco and plan configurations"""
    telcos: Dict[str, VPNTelco]  # telco_id -> VPNTelco

    def __init__(self):
        self.telcos = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'telcos': {k: v.to_dict() for k, v in self.telcos.items()}
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TelcoConfigManager':
        """Create from dictionary"""
        manager = cls()
        telcos_data = data.get('telcos', {})
        manager.telcos = {k: VPNTelco.from_dict(v) for k, v in telcos_data.items()}
        return manager

    def get_telco(self, telco_id: str) -> Optional[VPNTelco]:
        """Get a specific telco by ID"""
        return self.telcos.get(telco_id)

    def add_telco(self, telco: VPNTelco) -> None:
        """Add a telco"""
        self.telcos[telco.id] = telco

    def remove_telco(self, telco_id: str) -> bool:
        """Remove a telco"""
        if telco_id in self.telcos:
            del self.telcos[telco_id]
            return True
        return False

    def get_plan(self, telco_id: str, plan_id: str) -> Optional[VPNPlan]:
        """Get a specific plan from a telco"""
        telco = self.get_telco(telco_id)
        if telco:
            return telco.get_plan(plan_id)
        return None

    def get_all_enabled_telcos(self) -> Dict[str, VPNTelco]:
        """Get all enabled telcos"""
        return {k: v for k, v in self.telcos.items() if v.enabled}

    def get_telco_plans(self, telco_id: str) -> Dict[str, VPNPlan]:
        """Get all plans for a specific telco"""
        telco = self.get_telco(telco_id)
        if telco:
            return telco.plans
        return {}

    def search_plans(self, query: str) -> List[tuple[str, str, VPNPlan]]:
        """Search for plans across all telcos"""
        results = []
        query_lower = query.lower()

        for telco_id, telco in self.telcos.items():
            if not telco.enabled:
                continue

            for plan_id, plan in telco.plans.items():
                if not plan.enabled:
                    continue

                if (query_lower in plan.name.lower() or
                    query_lower in plan.description.lower() or
                    query_lower in telco.name.lower()):
                    results.append((telco_id, plan_id, plan))

        return results


@dataclass
class VPNRenewalRequest:
    """Model for VPN configuration renewal request"""
    client_id: int
    days: int
    user_uuid: str
    order_sn: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNRenewalRequest':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class VPNRenewalResponse:
    """Model for VPN configuration renewal response"""
    success: bool
    client_id: Optional[int] = None
    new_expiry_date: Optional[str] = None
    days_extended: Optional[int] = None
    message: Optional[str] = None
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VPNRenewalResponse':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class WebhookConfig:
    """Webhook configuration model"""
    enabled: bool
    shopee_api_base_url: str
    steamcodetool_base_url: str
    webhook_endpoint: str
    auto_register: bool
    retry_count: int
    retry_delay: int
    timeout: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WebhookConfig':
        return cls(
            enabled=data.get('enabled', True),
            shopee_api_base_url=data.get('shopee_api_base_url', 'https://shop.api.limjianhui.com'),
            steamcodetool_base_url=data.get('steamcodetool_base_url', 'http://localhost:5000'),
            webhook_endpoint=data.get('webhook_endpoint', '/vpn-config-generator/api/webhook'),
            auto_register=data.get('auto_register', True),
            retry_count=data.get('retry_count', 3),
            retry_delay=data.get('retry_delay', 5),
            timeout=data.get('timeout', 30)
        )

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class RedemptionLink:
    """Model for custom VPN redemption links"""
    id: str
    customer_username: str  # Shopee customer username
    validity_days: int  # How many days the VPN config should be valid
    server_id: Optional[str] = None  # Specific server ID or 'auto'
    telco: Optional[str] = None  # Specific telco
    plan: Optional[str] = None  # Specific plan
    var_sku: Optional[str] = None  # SKU for server mapping and validity
    created_at: Optional[str] = None
    created_by: Optional[str] = None  # Admin who created the link
    expires_at: Optional[str] = None  # When the link expires (optional)
    used_at: Optional[str] = None  # When the link was used
    used_by: Optional[str] = None  # Who used the link
    is_active: bool = True
    notes: Optional[str] = None
    max_uses: int = 1  # How many times the link can be used
    current_uses: int = 0  # How many times it has been used

    def __post_init__(self):
        """Initialize default values"""
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RedemptionLink':
        """Create from dictionary"""
        return cls(**data)

    def is_valid(self) -> bool:
        """Check if the redemption link is valid"""
        if not self.is_active:
            return False

        if self.current_uses >= self.max_uses:
            return False

        if self.expires_at:
            try:
                expiry = datetime.fromisoformat(self.expires_at)
                if datetime.now() > expiry:
                    return False
            except:
                pass

        return True

    def can_be_used(self) -> Tuple[bool, str]:
        """Check if link can be used and return reason if not"""
        if not self.is_active:
            return False, "Redemption link has been deactivated"

        if self.current_uses >= self.max_uses:
            return False, f"Redemption link has already been used {self.current_uses}/{self.max_uses} times"

        if self.expires_at:
            try:
                expiry = datetime.fromisoformat(self.expires_at)
                if datetime.now() > expiry:
                    return False, f"Redemption link expired on {expiry.strftime('%Y-%m-%d %H:%M')}"
            except:
                pass

        return True, "Link is valid"

    def mark_used(self, used_by: str = None):
        """Mark the link as used"""
        self.current_uses += 1
        self.used_at = datetime.now().isoformat()
        if used_by:
            self.used_by = used_by


@dataclass
class RedemptionLinkRequest:
    """Request model for creating redemption links"""
    customer_username: str
    validity_days: int
    server_id: Optional[str] = None
    telco: Optional[str] = None
    plan: Optional[str] = None
    var_sku: Optional[str] = None
    expires_at: Optional[str] = None
    notes: Optional[str] = None
    max_uses: int = 1
    created_by: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RedemptionLinkRequest':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class RedemptionLinkResponse:
    """Response model for redemption link operations"""
    success: bool
    redemption_link: Optional[RedemptionLink] = None
    redemption_url: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        if self.redemption_link:
            result['redemption_link'] = self.redemption_link.to_dict()
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RedemptionLinkResponse':
        """Create from dictionary"""
        if 'redemption_link' in data and data['redemption_link']:
            data['redemption_link'] = RedemptionLink.from_dict(data['redemption_link'])
        return cls(**data)


@dataclass
class ChatTemplateConfig:
    """Configuration for chat message templates"""
    enabled: bool = True
    message_template: str = "🎁 VPN Configuration Link\n\nHi {customer_username}!\n\nYour VPN configuration is ready. Click the link below to generate your {validity_days}-day VPN config:\n\n🔗 {redemption_url}\n\n📋 Details:\n• Validity: {validity_days} days\n• Server: {server_display}\n• Telco: {telco_display}\n• Plan: {plan_display}\n\nThis link will expire if not used. Contact support if you need assistance!"
    include_expiry_warning: bool = True
    expiry_warning_template: str = "\n\n⚠️ This link expires on {expires_at}"
    include_usage_info: bool = True
    usage_info_template: str = "\n\n📊 Usage: {current_uses}/{max_uses} times"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatTemplateConfig':
        """Create from dictionary"""
        return cls(**data)

    def format_message(self, link: RedemptionLink, redemption_url: str, base_url: str = "") -> str:
        """Format the chat message using the template and link data"""
        # Prepare template variables
        variables = {
            'customer_username': link.customer_username,
            'validity_days': link.validity_days,
            'redemption_url': f"{base_url}{redemption_url}" if base_url else redemption_url,
            'server_display': link.server_id or 'Auto-selected',
            'telco_display': (link.telco or 'Any').title(),
            'plan_display': (link.plan or 'Any').title(),
            'link_id': link.id,
            'created_at': link.created_at,
            'current_uses': link.current_uses,
            'max_uses': link.max_uses
        }

        # Format the main message
        message = self.message_template.format(**variables)

        # Add expiry warning if enabled and link has expiry
        if self.include_expiry_warning and link.expires_at:
            try:
                from datetime import datetime
                expiry_date = datetime.fromisoformat(link.expires_at)
                variables['expires_at'] = expiry_date.strftime('%Y-%m-%d %H:%M')
                message += self.expiry_warning_template.format(**variables)
            except:
                pass

        # Add usage info if enabled
        if self.include_usage_info:
            message += self.usage_info_template.format(**variables)

        return message


@dataclass
class BulkCreationRequest:
    """Request model for bulk redemption link creation"""
    customer_usernames: List[str]
    validity_days: int
    server_id: Optional[str] = None
    telco: Optional[str] = None
    plan: Optional[str] = None
    var_sku: Optional[str] = None
    expires_at: Optional[str] = None
    notes: Optional[str] = None
    max_uses: int = 1
    created_by: Optional[str] = None
    send_via_chat: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BulkCreationRequest':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class BulkCreationResult:
    """Result model for bulk redemption link creation"""
    success: bool
    total_requested: int
    successful_links: List[RedemptionLink]
    failed_links: List[Dict[str, str]]  # username -> error message
    chat_results: Optional[Dict[str, bool]] = None  # link_id -> success status
    message: Optional[str] = None
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        if self.successful_links:
            result['successful_links'] = [link.to_dict() for link in self.successful_links]
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BulkCreationResult':
        """Create from dictionary"""
        if 'successful_links' in data and data['successful_links']:
            data['successful_links'] = [RedemptionLink.from_dict(link) for link in data['successful_links']]
        return cls(**data)


@dataclass
class RedemptionAnalytics:
    """Analytics data for redemption links"""
    total_links: int = 0
    active_links: int = 0
    used_links: int = 0
    expired_links: int = 0
    usage_rate: float = 0.0
    creation_trends: Dict[str, int] = None  # date -> count
    usage_trends: Dict[str, int] = None  # date -> count
    popular_telcos: Dict[str, int] = None  # telco -> count
    popular_plans: Dict[str, int] = None  # plan -> count
    average_time_to_use: Optional[float] = None  # hours
    creator_stats: Dict[str, int] = None  # creator -> count

    def __post_init__(self):
        """Initialize default values"""
        if self.creation_trends is None:
            self.creation_trends = {}
        if self.usage_trends is None:
            self.usage_trends = {}
        if self.popular_telcos is None:
            self.popular_telcos = {}
        if self.popular_plans is None:
            self.popular_plans = {}
        if self.creator_stats is None:
            self.creator_stats = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RedemptionAnalytics':
        """Create from dictionary"""
        return cls(**data)
