# WebSocket Error Spam Fix

## 问题描述

ShopeeAPI的WebSocket服务在认证失败时会无限重连，导致日志文件爆炸性增长，产生数GB的重复错误信息：

```
ERROR:services.websocket:Error getting chat login info: Authentication failed for https://seller.shopee.com.my/webchat/api/coreapi/v1.2/mini/login with status code 401
ERROR:services.websocket:✗ Chat login API failed with status 500
ERROR:services.websocket:Response: {'error': 'Error getting chat login info: Authentication failed for https://seller.shopee.com.my/webchat/api/coreapi/v1.2/mini/login with status code 401'}
ERROR:services.websocket:Authorization test failed - Socket.IO connection may fail
WARNING:services.websocket:Failed to connect to Shopee Socket.IO (attempt 55329)
```

## 根本原因

1. **无限重连循环**：当认证失败时，系统会不断尝试重连，没有适当的退避机制
2. **没有最大重连次数限制**：`connection_attempts` 会无限增长
3. **认证失败后仍然尝试连接**：即使 `test_authorization_status` 返回 False，系统仍会继续尝试重连
4. **重复错误日志**：相同的错误信息会被重复记录数千次

## 修复方案

### 1. 添加连接失败跟踪机制

在 `WebSocketService.__init__()` 中添加了新的跟踪变量：

```python
# Add connection failure tracking
self.max_reconnect_attempts = 10  # Maximum reconnection attempts before giving up
self.auth_failure_count = 0  # Track consecutive auth failures
self.max_auth_failures = 3  # Maximum auth failures before longer backoff
self.last_auth_failure_time: Optional[datetime] = None
self.auth_backoff_duration = 300  # 5 minutes backoff after repeated auth failures
```

### 2. 改进认证状态测试

在 `test_authorization_status()` 方法中添加了失败计数和退避逻辑：

```python
# Track auth failures
self.auth_failure_count += 1
self.last_auth_failure_time = datetime.now()

logger.error(f"✗ Chat login API failed with status {status_code} (failure #{self.auth_failure_count})")

# If we have too many auth failures, suggest longer backoff
if self.auth_failure_count >= self.max_auth_failures:
    logger.error(f"✗ Too many consecutive auth failures ({self.auth_failure_count}). "
               f"Will wait {self.auth_backoff_duration} seconds before next attempt.")
```

### 3. 添加认证失败退避机制

在 `connect_to_shopee()` 方法中添加了退避检查：

```python
# Check if we're in auth failure backoff period
if (self.last_auth_failure_time and 
    self.auth_failure_count >= self.max_auth_failures and
    (datetime.now() - self.last_auth_failure_time).total_seconds() < self.auth_backoff_duration):
    
    remaining_time = self.auth_backoff_duration - (datetime.now() - self.last_auth_failure_time).total_seconds()
    logger.warning(f"In auth failure backoff period. {remaining_time:.0f} seconds remaining.")
    return False
```

### 4. 限制最大重连次数

在 `maintain_connection()` 方法中添加了重连次数限制：

```python
# Check if we've exceeded maximum reconnection attempts
if connection_attempts >= self.max_reconnect_attempts:
    logger.error(f"Maximum reconnection attempts ({self.max_reconnect_attempts}) reached. "
               f"Stopping reconnection attempts for 5 minutes.")
    
    # Wait 5 minutes before resetting attempt counter
    await asyncio.sleep(300)  # 5 minutes
    connection_attempts = 0
    logger.info("Resetting reconnection attempt counter after 5-minute cooldown")
    continue
```

### 5. 指数退避重连机制

添加了指数退避等待时间：

```python
# Calculate exponential backoff wait time
wait_time = min(60, connection_attempts * 5)  # Max 60 seconds
logger.warning(f"Failed to connect to Shopee Socket.IO (attempt {connection_attempts}/{self.max_reconnect_attempts}). "
             f"Waiting {wait_time} seconds before next attempt.")

# Wait with exponential backoff
await asyncio.sleep(wait_time)
```

### 6. 减少重复错误日志

添加了错误日志去重机制：

```python
# Only log error if it's different from the last one or it's been more than 5 minutes
current_error = str(e)
now = datetime.now()

if (last_error_log is None or 
    current_error != last_error_log or 
    (now - last_status_log).total_seconds() > 300):  # 5 minutes
    
    logger.error(f"Error maintaining Socket.IO connection: {e}")
    last_error_log = current_error
    last_status_log = now
```

### 7. 添加连接状态重置方法

```python
def reset_connection_state(self):
    """Reset connection state and counters."""
    logger.info("Resetting connection state")
    self.is_connected = False
    self.sio_connected = False
    self.reconnect_attempt = 0
    self.auth_failure_count = 0
    self.last_auth_failure_time = None
    
    # Cancel any running tasks
    if self.shopee_heartbeat_task and not self.shopee_heartbeat_task.done():
        self.shopee_heartbeat_task.cancel()
        
    # Clean up client
    if self.sio_client:
        self.sio_client = None
```

## 修复效果

### 修复前
- 无限重连循环，日志文件可能增长到数GB
- 每秒产生多条重复错误信息
- 系统资源浪费在无效的重连尝试上
- 难以诊断真正的问题

### 修复后
- 最多尝试10次重连，然后进入5分钟冷却期
- 认证失败3次后进入5分钟退避期
- 指数退避机制减少重连频率
- 重复错误日志被过滤，只在5分钟间隔内记录一次
- 更清晰的错误信息，包含尝试次数和剩余时间

## 配置参数

新增的配置参数可以根据需要调整：

```python
self.max_reconnect_attempts = 10      # 最大重连次数
self.max_auth_failures = 3           # 最大认证失败次数
self.auth_backoff_duration = 300     # 认证失败退避时间（秒）
```

## 兼容性

此修复完全向后兼容，不会影响现有的功能：
- 正常的连接和重连逻辑保持不变
- 只在出现连续失败时才启用新的限制机制
- 成功连接后会自动重置所有计数器

## 测试建议

1. 使用无效凭据测试认证失败场景
2. 验证日志文件大小不会无限增长
3. 确认在退避期间不会产生重复错误
4. 测试正常连接恢复后的行为

这个修复彻底解决了WebSocket错误日志爆炸的问题，同时保持了系统的稳定性和可维护性。
