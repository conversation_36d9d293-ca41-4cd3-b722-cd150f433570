{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>VPN Configurations</h2>
                <div>
                    <a href="{{ url_for('vpn.backup_configurations') }}" class="btn btn-primary">
                        <i class="fas fa-save"></i> Backup
                    </a>
                    <a href="{{ url_for('vpn.sync_configurations') }}" class="btn btn-info">
                        <i class="fas fa-sync"></i> Sync
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Summary Cards -->
    {% if overview %}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="info-box">
                <span class="info-box-icon bg-primary"><i class="fas fa-server"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Total Servers</span>
                    <span class="info-box-number">{{ overview.total_servers }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="info-box">
                <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Total Clients</span>
                    <span class="info-box-number">{{ overview.total_clients }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="info-box">
                <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Active Clients</span>
                    <span class="info-box-number">{{ overview.total_active_clients }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="info-box">
                <span class="info-box-icon bg-danger"><i class="fas fa-times-circle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Expired Clients</span>
                    <span class="info-box-number">{{ overview.total_expired_clients }}</span>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Server Configurations -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Server Configurations</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Server</th>
                                    <th>Host</th>
                                    <th>Config Path</th>
                                    <th>Total Clients</th>
                                    <th>Active</th>
                                    <th>Expired</th>
                                    <th>Config Valid</th>
                                    <th>Last Backup</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if overview and overview.servers %}
                                {% for server in overview.servers %}
                                <tr>
                                    <td>
                                        <strong>{{ server.server_name }}</strong>
                                    </td>
                                    <td>{{ server.server_host }}</td>
                                    <td>
                                        <code>{{ server.config_path }}</code>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ server.total_clients }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">{{ server.active_clients }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-danger">{{ server.expired_clients }}</span>
                                    </td>
                                    <td>
                                        {% if server.config_valid %}
                                        <span class="badge badge-success">Valid</span>
                                        {% else %}
                                        <span class="badge badge-danger">Invalid</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if server.last_backup %}
                                        {{ server.last_backup }}
                                        {% else %}
                                        <span class="text-muted">Never</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('vpn.server_configuration', server_id=server.server_id) }}" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="{{ url_for('vpn.edit_server_configuration', server_id=server.server_id) }}" 
                                               class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <button class="btn btn-sm btn-info" onclick="validateConfig({{ server.server_id }})">
                                                <i class="fas fa-check"></i> Validate
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        No server configurations found
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function validateConfig(serverId) {
    toastr.info('Validating configuration...');
    $.get(`/admin/vpn/api/config/validate/server/${serverId}`, function(data) {
        if (data.is_valid) {
            toastr.success('Configuration is valid!');
        } else {
            var errors = data.validation_errors.join('\n');
            toastr.error('Configuration validation failed:\n' + errors);
        }
    }).fail(function() {
        toastr.error('Failed to validate configuration');
    });
}
</script>
{% endblock %}