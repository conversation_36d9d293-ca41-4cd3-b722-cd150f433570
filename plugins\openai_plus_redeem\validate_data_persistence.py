#!/usr/bin/env python3
"""
Data Persistence Validation Script

This script provides manual validation of data persistence operations
including file integrity checks, backup validation, and performance testing.
"""

import sys
import os
import json
import time
import shutil
from pathlib import Path
from datetime import datetime, timedelta

# Add plugin directory to Python path
plugin_dir = Path(__file__).parent
sys.path.insert(0, str(plugin_dir.parent.parent))

from plugins.openai_plus_redeem.models.utils import (
    load_json_data, save_json_data, backup_data_file, get_data_file_path
)
from plugins.openai_plus_redeem.models.chatgpt_account import ChatGPTAccount, AccountStatus
from plugins.openai_plus_redeem.services.chatgpt_account_service import ChatGPTAccountService


class DataPersistenceValidator:
    """Data persistence validation utility"""
    
    def __init__(self):
        self.test_dir = None
        self.original_get_data_file_path = None
    
    def setup_test_environment(self):
        """Setup test environment with temporary directory"""
        import tempfile
        from unittest.mock import patch
        
        self.test_dir = tempfile.mkdtemp(prefix='opr_data_test_')
        print(f"📁 Created test directory: {self.test_dir}")
        
        # Mock data file paths to use test directory
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.test_dir) / filename)
        
        # Store original function
        self.original_get_data_file_path = get_data_file_path
        
        # Apply mock
        import plugins.openai_plus_redeem.models.utils as utils_module
        utils_module.get_data_file_path = mock_get_data_file_path
        
        return True
    
    def cleanup_test_environment(self):
        """Cleanup test environment"""
        if self.test_dir and Path(self.test_dir).exists():
            shutil.rmtree(self.test_dir)
            print(f"🗑️  Cleaned up test directory: {self.test_dir}")
        
        # Restore original function
        if self.original_get_data_file_path:
            import plugins.openai_plus_redeem.models.utils as utils_module
            utils_module.get_data_file_path = self.original_get_data_file_path
    
    def test_basic_file_operations(self):
        """Test basic JSON file operations"""
        print("\n🔧 Testing basic file operations...")
        
        test_file = Path(self.test_dir) / 'basic_test.json'
        test_data = {
            'test_key': 'test_value',
            'timestamp': datetime.now().isoformat(),
            'nested': {
                'array': [1, 2, 3],
                'boolean': True
            }
        }
        
        # Test save
        print("  💾 Testing save operation...")
        save_result = save_json_data(str(test_file), test_data)
        if save_result:
            print("  ✅ Save operation successful")
        else:
            print("  ❌ Save operation failed")
            return False
        
        # Test load
        print("  📖 Testing load operation...")
        loaded_data = load_json_data(str(test_file))
        if loaded_data and loaded_data['test_key'] == 'test_value':
            print("  ✅ Load operation successful")
        else:
            print("  ❌ Load operation failed")
            return False
        
        # Test file integrity
        print("  🔍 Testing file integrity...")
        try:
            with open(test_file, 'r') as f:
                file_content = f.read()
                json.loads(file_content)  # Should parse without error
            print("  ✅ File integrity check passed")
        except Exception as e:
            print(f"  ❌ File integrity check failed: {e}")
            return False
        
        return True
    
    def test_service_persistence(self):
        """Test service-level persistence operations"""
        print("\n🏗️  Testing service persistence...")
        
        # Create test configuration
        test_config = {
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }
        
        # Create mock logger
        class MockLogger:
            def info(self, msg): print(f"    INFO: {msg}")
            def warning(self, msg): print(f"    WARNING: {msg}")
            def error(self, msg): print(f"    ERROR: {msg}")
            def debug(self, msg): print(f"    DEBUG: {msg}")
        
        logger = MockLogger()
        
        # Test account service persistence
        print("  👤 Testing account service...")
        account_service = ChatGPTAccountService(test_config, logger)
        
        if not account_service.initialize():
            print("  ❌ Account service initialization failed")
            return False
        
        # Create test account
        test_account = ChatGPTAccount(
            account_id='VALIDATION_TEST_001',
            email='<EMAIL>',
            password='validation_password',
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        
        # Add account
        add_result = account_service.add_account(test_account)
        if not add_result['success']:
            print(f"  ❌ Failed to add account: {add_result['error']}")
            return False
        
        # Verify persistence
        loaded_account = account_service.get_account_by_id('VALIDATION_TEST_001')
        if not loaded_account:
            print("  ❌ Failed to load saved account")
            return False
        
        if loaded_account.email != '<EMAIL>':
            print("  ❌ Account data integrity check failed")
            return False
        
        print("  ✅ Service persistence test passed")
        return True
    
    def test_backup_operations(self):
        """Test backup and recovery operations"""
        print("\n💾 Testing backup operations...")
        
        # Create test data file
        test_file = Path(self.test_dir) / 'backup_test.json'
        test_data = {
            'accounts': [
                {
                    'account_id': 'BACKUP_TEST_001',
                    'email': '<EMAIL>',
                    'password': 'backup_password',
                    'created_date': datetime.now().isoformat()
                }
            ],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }
        
        # Save initial data
        if not save_json_data(str(test_file), test_data):
            print("  ❌ Failed to save initial test data")
            return False
        
        # Create backup
        print("  📦 Creating backup...")
        backup_result = backup_data_file(str(test_file))
        if not backup_result['success']:
            print(f"  ❌ Backup creation failed: {backup_result['error']}")
            return False
        
        backup_file = Path(backup_result['backup_path'])
        if not backup_file.exists():
            print("  ❌ Backup file was not created")
            return False
        
        # Verify backup content
        print("  🔍 Verifying backup content...")
        backup_data = load_json_data(str(backup_file))
        if not backup_data or backup_data['accounts'][0]['account_id'] != 'BACKUP_TEST_001':
            print("  ❌ Backup content verification failed")
            return False
        
        print("  ✅ Backup operations test passed")
        return True
    
    def test_performance(self):
        """Test performance with various data sizes"""
        print("\n⚡ Testing performance...")
        
        # Test configuration
        test_config = {
            'email_config': {
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }
        
        class MockLogger:
            def info(self, msg): pass
            def warning(self, msg): pass
            def error(self, msg): pass
            def debug(self, msg): pass
        
        logger = MockLogger()
        account_service = ChatGPTAccountService(test_config, logger)
        account_service.initialize()
        
        # Test different data sizes
        test_sizes = [10, 100, 500]
        
        for size in test_sizes:
            print(f"  📊 Testing with {size} accounts...")
            
            # Create accounts
            start_time = time.time()
            for i in range(size):
                account = ChatGPTAccount(
                    account_id=f'PERF_TEST_{size}_{i:04d}',
                    email=f'perf{i}@example.com',
                    password=f'password{i}',
                    max_concurrent_users=5,
                    current_users=0,
                    expiration_date=datetime.now() + timedelta(days=30),
                    status=AccountStatus.ACTIVE,
                    created_date=datetime.now()
                )
                
                result = account_service.add_account(account)
                if not result['success']:
                    print(f"    ❌ Failed to add account {i}")
                    return False
            
            creation_time = time.time() - start_time
            
            # Test query performance
            start_time = time.time()
            all_accounts = account_service.get_all_accounts()
            query_time = time.time() - start_time
            
            print(f"    ⏱️  Creation: {creation_time:.2f}s, Query: {query_time:.2f}s")
            
            # Performance thresholds (adjust as needed)
            if creation_time > size * 0.1:  # 0.1s per account max
                print(f"    ⚠️  Creation time seems slow for {size} accounts")
            
            if query_time > 1.0:  # 1s max for query
                print(f"    ⚠️  Query time seems slow for {size} accounts")
        
        print("  ✅ Performance test completed")
        return True
    
    def test_error_handling(self):
        """Test error handling scenarios"""
        print("\n🚨 Testing error handling...")
        
        # Test invalid file paths
        print("  📁 Testing invalid file paths...")
        invalid_result = save_json_data('/invalid/path/file.json', {'test': 'data'})
        if invalid_result:
            print("  ⚠️  Expected save to invalid path to fail")
        else:
            print("  ✅ Invalid path handling works correctly")
        
        # Test corrupted JSON
        print("  🔧 Testing corrupted JSON handling...")
        corrupted_file = Path(self.test_dir) / 'corrupted.json'
        with open(corrupted_file, 'w') as f:
            f.write('{ invalid json content }')
        
        corrupted_data = load_json_data(str(corrupted_file))
        if corrupted_data is None:
            print("  ✅ Corrupted JSON handling works correctly")
        else:
            print("  ⚠️  Expected corrupted JSON to return None")
        
        print("  ✅ Error handling test completed")
        return True
    
    def run_comprehensive_validation(self):
        """Run comprehensive data persistence validation"""
        print("🚀 Starting comprehensive data persistence validation")
        print("=" * 60)
        
        try:
            # Setup test environment
            if not self.setup_test_environment():
                print("❌ Failed to setup test environment")
                return False
            
            # Run all tests
            tests = [
                ("Basic File Operations", self.test_basic_file_operations),
                ("Service Persistence", self.test_service_persistence),
                ("Backup Operations", self.test_backup_operations),
                ("Performance Testing", self.test_performance),
                ("Error Handling", self.test_error_handling)
            ]
            
            passed_tests = 0
            total_tests = len(tests)
            
            for test_name, test_func in tests:
                print(f"\n🧪 Running: {test_name}")
                try:
                    if test_func():
                        passed_tests += 1
                        print(f"✅ {test_name} PASSED")
                    else:
                        print(f"❌ {test_name} FAILED")
                except Exception as e:
                    print(f"❌ {test_name} ERROR: {e}")
            
            # Summary
            print("\n" + "=" * 60)
            print("📊 VALIDATION SUMMARY")
            print("=" * 60)
            print(f"Tests passed: {passed_tests}/{total_tests}")
            print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
            
            if passed_tests == total_tests:
                print("🎉 All data persistence validation tests passed!")
                return True
            else:
                print("⚠️  Some validation tests failed!")
                return False
        
        finally:
            # Always cleanup
            self.cleanup_test_environment()


def main():
    """Main entry point"""
    print("📊 OpenAI Plus Redeem Plugin - Data Persistence Validator")
    print("=" * 60)
    
    validator = DataPersistenceValidator()
    
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        print("Usage:")
        print("  python validate_data_persistence.py    # Run comprehensive validation")
        return 0
    
    success = validator.run_comprehensive_validation()
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
