"""
Security headers and configurations for SteamCodeTool
Helps prevent Chrome Safe Browsing false positives
"""

from flask import Flask
from functools import wraps

def add_security_headers(app: Flask):
    """Add security headers to all responses"""
    
    @app.after_request
    def set_security_headers(response):
        # Content Security Policy - 防止XSS攻击
        response.headers['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' "
            "https://cdn.tailwindcss.com "
            "https://cdn.jsdelivr.net "
            "https://cdnjs.cloudflare.com "
            "https://unpkg.com "
            "https://code.jquery.com "
            "https://cdn.datatables.net "
            "translate.googleapis.com "
            "translate.google.com "
            "*.google.com "
            "*.gstatic.com "
            "chrome-extension://bocbaocobfecmglnmeaeppambideimao/; "
            "style-src 'self' 'unsafe-inline' "
            "https://cdn.tailwindcss.com "
            "https://cdn.jsdelivr.net "
            "https://cdnjs.cloudflare.com "
            "https://fonts.googleapis.com; "
            "img-src 'self' data: https:; "
            "font-src 'self' https: "
            "https://fonts.gstatic.com "
            "https://cdnjs.cloudflare.com; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        
        # X-Frame-Options - 防止点击劫持
        response.headers['X-Frame-Options'] = 'DENY'
        
        # X-Content-Type-Options - 防止MIME类型嗅探
        response.headers['X-Content-Type-Options'] = 'nosniff'
        
        # X-XSS-Protection - 启用XSS保护
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # Referrer Policy - 控制引用信息
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Strict-Transport-Security - 强制HTTPS (如果使用HTTPS)
        if app.config.get('FORCE_HTTPS', False):
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Permissions Policy - 限制浏览器功能
        response.headers['Permissions-Policy'] = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )
        
        # Cache Control for sensitive pages
        from flask import request
        if request and (request.path.startswith('/admin') or request.path.startswith('/api')):
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
        
        return response

def validate_redirect_url(url: str) -> bool:
    """
    验证重定向URL是否安全
    只允许特定的域名和协议
    """
    import urllib.parse
    
    if not url:
        return False
    
    try:
        parsed = urllib.parse.urlparse(url)
        
        # 只允许HTTPS协议
        if parsed.scheme not in ['https']:
            return False
        
        # 允许的域名列表 (可以根据需要修改)
        allowed_domains = [
            'canva.com',
            'www.canva.com',
            'pro.canva.com'
        ]
        
        # 检查域名是否在允许列表中
        if parsed.netloc.lower() not in allowed_domains:
            return False
        
        return True
        
    except Exception:
        return False

def secure_redirect_wrapper(func):
    """
    装饰器：为重定向功能添加安全检查
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        from flask import jsonify, request
        
        # 获取原始响应
        response = func(*args, **kwargs)
        
        # 如果是JSON响应且包含URL
        if hasattr(response, 'json') and response.json:
            data = response.json
            if 'url' in data:
                if not validate_redirect_url(data['url']):
                    return jsonify({'error': 'Invalid redirect URL'}), 400
        
        return response
    
    return wrapper

def add_rate_limiting_headers(response, limit: int = 100, window: int = 3600):
    """
    添加速率限制头部信息
    """
    response.headers['X-RateLimit-Limit'] = str(limit)
    response.headers['X-RateLimit-Window'] = str(window)
    return response

def sanitize_user_input(data: str) -> str:
    """
    清理用户输入，防止XSS攻击
    """
    import html
    import re
    
    if not data:
        return ""
    
    # HTML转义
    data = html.escape(data)
    
    # 移除潜在的脚本标签
    data = re.sub(r'<script[^>]*>.*?</script>', '', data, flags=re.IGNORECASE | re.DOTALL)
    
    # 移除javascript:协议
    data = re.sub(r'javascript:', '', data, flags=re.IGNORECASE)
    
    # 移除data:协议 (除了图片)
    data = re.sub(r'data:(?!image)', '', data, flags=re.IGNORECASE)
    
    return data.strip()

class SecurityConfig:
    """安全配置类"""
    
    # 允许的文件上传类型
    ALLOWED_UPLOAD_EXTENSIONS = {'.txt', '.json', '.csv'}
    
    # 最大文件上传大小 (字节)
    MAX_UPLOAD_SIZE = 1024 * 1024  # 1MB
    
    # 会话超时时间 (秒)
    SESSION_TIMEOUT = 3600  # 1小时
    
    # 最大请求大小
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # CSRF保护
    CSRF_ENABLED = True
    
    # 安全的随机密钥长度
    SECRET_KEY_LENGTH = 32

def init_security(app: Flask):
    """
    初始化所有安全配置
    """
    # 添加安全头部
    add_security_headers(app)
    
    # 设置最大内容长度
    app.config['MAX_CONTENT_LENGTH'] = SecurityConfig.MAX_CONTENT_LENGTH
    
    # 设置会话配置
    app.config['PERMANENT_SESSION_LIFETIME'] = SecurityConfig.SESSION_TIMEOUT
    
    # 如果没有设置密钥，生成一个安全的密钥
    if not app.secret_key or len(app.secret_key) < SecurityConfig.SECRET_KEY_LENGTH:
        import secrets
        app.secret_key = secrets.token_hex(SecurityConfig.SECRET_KEY_LENGTH)
    
    print("Security configurations initialized successfully")
