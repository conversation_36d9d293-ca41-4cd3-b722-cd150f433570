# 📖 User Guide: Server-Specific Client Management

## 🎯 Overview

The Server-Specific Client Management feature allows you to manage VPN clients for individual servers with a dedicated, context-aware interface. This feature addresses the need to "manage users by server" with a professional, easy-to-use interface.

## 🚀 Getting Started

### Accessing Server-Client Management

1. **Navigate to VPN Servers**
   - Go to `/admin/vpn/servers` in your SteamCodeTool
   - You'll see your list of VPN servers

2. **Click "Manage Clients" Button**
   - Each server row now has a blue "👥 Manage Clients" button
   - Click this button for the server you want to manage

3. **Server-Client Management Page**
   - You'll be taken to `/admin/vpn/servers/{server_id}/clients`
   - This page shows only clients for the selected server

## 🖥️ Interface Overview

### Server Header
- **Server Information**: Name, description, host, port, status
- **Configuration Details**: Config path, service name
- **Visual Design**: Beautiful gradient header with server context

### Client Statistics Dashboard
Five visual cards showing:
- **Total Clients**: All clients on this server
- **Active Clients**: Currently active clients
- **Expired Clients**: Clients that have expired
- **Expiring Soon**: Clients expiring within 7 days
- **Lifetime Clients**: Clients with lifetime access

### Advanced Filtering
- **Search Box**: Search by email or username
- **Status Filter**: Filter by All, Active, Expired, or Expiring Soon
- **Pagination**: Choose 25, 50, or 100 clients per page
- **Auto-Submit**: Filters apply automatically

### Client Table
- **Email/ID**: Client email with description
- **Username**: Shopee username (if available)
- **Status**: Color-coded status badges
- **Expiry Date**: When the client expires
- **Days Left**: Days until expiry
- **Data Usage**: Current data usage in GB
- **Actions**: Quick action buttons

## 🎨 Visual Status Indicators

### Status Badges
- 🟢 **Active** (Green): Client is active and not expiring soon
- 🔴 **Expired** (Red): Client has expired
- 🟡 **Expiring Soon** (Yellow): Client expires within 7 days
- 🔵 **Lifetime** (Blue): Client has lifetime access

## ⚡ Quick Actions

### For Each Client
- **✏️ Edit**: Modify client details
- **📅 Extend**: Extend expiry date (prompts for days)
- **🗑️ Delete**: Remove client (with confirmation)

### Bulk Actions
- **➕ Add Client**: Create new client for this server
- **➕ Bulk Add**: Create multiple clients for this server

## 📱 Mobile Responsive Design

The interface works perfectly on:
- **Desktop**: Full feature set with optimal layout
- **Tablet**: Responsive grid layout
- **Mobile**: Touch-friendly interface with stacked layout

## 🔍 Search and Filter Guide

### Search Functionality
- **Email Search**: Type any part of an email address
- **Username Search**: Search by Shopee username
- **Case Insensitive**: Search is not case-sensitive
- **Partial Matching**: Finds partial matches

### Status Filters
- **All Clients**: Shows all clients (default)
- **Active Only**: Shows only active, non-expiring clients
- **Expired Only**: Shows only expired clients
- **Expiring Soon**: Shows clients expiring within 7 days

### Pagination
- **Per Page Options**: 25, 50, or 100 clients per page
- **Navigation**: Previous/Next buttons when needed
- **Count Display**: Shows "X to Y of Z clients"

## 🛠️ Common Workflows

### Daily Server Management
1. Go to server list
2. Click "Manage Clients" for your server
3. Review client statistics
4. Check for expiring clients (yellow badges)
5. Extend or manage clients as needed

### Adding New Clients
1. From server-client page, click "Add Client"
2. Form is pre-filled with server context
3. Fill in client details
4. Client is automatically assigned to this server

### Bulk Client Management
1. Click "Bulk Add" for multiple clients
2. Upload CSV or enter multiple clients
3. All clients are assigned to the current server

### Client Maintenance
1. Use status filter to find expired clients
2. Use search to find specific clients
3. Use quick actions for common tasks
4. Monitor statistics for server health

## 🔧 Troubleshooting

### Common Issues

**Q: "Manage Clients" button not visible**
- A: Ensure you're on the VPN servers page (`/admin/vpn/servers`)
- Check that the VPN plugin is enabled

**Q: Server-client page shows error**
- A: Verify the server exists and is accessible
- Check VPN API connection in settings

**Q: Client statistics not loading**
- A: Check API connectivity
- Verify server has clients configured

**Q: Search not working**
- A: Try clearing search and reloading page
- Check for JavaScript errors in browser console

### Performance Tips

**For Servers with Many Clients:**
- Use pagination (set to 25 or 50 per page)
- Use search to find specific clients
- Use status filters to narrow results

**For Better Performance:**
- Keep browser tabs to minimum
- Clear browser cache if issues persist
- Use modern browsers (Chrome, Firefox, Edge)

## 🎯 Best Practices

### Server Organization
- Use descriptive server names
- Add server descriptions for context
- Group related servers logically

### Client Management
- Use consistent email formats
- Add descriptions for business clients
- Monitor expiry dates regularly
- Use bulk operations for efficiency

### Workflow Efficiency
- Bookmark frequently used servers
- Use keyboard shortcuts where available
- Set up regular maintenance schedules
- Monitor statistics for trends

## 🔄 Navigation Tips

### Quick Navigation
- **Back to Servers**: Use the "← Back to Servers" button
- **Browser Back**: Standard browser back button works
- **Direct URLs**: Bookmark specific server-client pages

### Keyboard Shortcuts
- **Enter**: Submit search/filter forms
- **Escape**: Clear search (in some browsers)
- **Tab**: Navigate between form elements

## 📊 Understanding Statistics

### Client Count Metrics
- **Total**: All clients configured for this server
- **Active**: Clients that are not expired
- **Expired**: Clients past their expiry date
- **Expiring Soon**: Clients expiring within 7 days
- **Lifetime**: Clients with permanent access

### Data Usage
- Displayed in GB (Gigabytes)
- Updated based on server reports
- Helps monitor client activity

## 🎉 Benefits

### For Administrators
- **Server Context**: Always know which server you're managing
- **Focused View**: See only relevant clients
- **Quick Actions**: Manage clients efficiently
- **Visual Feedback**: Understand server status at a glance

### For Teams
- **Clear Workflows**: Standardized client management process
- **Reduced Errors**: Server context prevents mistakes
- **Better Organization**: Logical grouping by server
- **Improved Productivity**: Faster client management

## 📞 Support

If you encounter issues or need help:
1. Check this user guide first
2. Review the troubleshooting section
3. Check browser console for errors
4. Contact your system administrator

---

**🎊 Congratulations!** You now have a powerful, professional tool for managing VPN clients by server. This feature significantly improves the workflow for server administrators and provides a much better user experience than managing all clients globally.
