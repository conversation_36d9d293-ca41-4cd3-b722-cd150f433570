{% extends "base.html" %}

{% block title %}Shopee Auto Boost - Settings{% endblock %}

{% block header %}Boost Settings{% endblock %}

{% block content %}
<div class="container-fluid" x-data="shopeeAutoBoostSettings()">
    <!-- Current Configuration -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Current Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Plugin Enabled</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="pluginEnabled"
                                           x-model="config.enabled">
                                    <label class="form-check-label" for="pluginEnabled">
                                        Enable the Shopee Auto Boost plugin
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Auto Start</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="autoStart"
                                           x-model="config.auto_start">
                                    <label class="form-check-label" for="autoStart">
                                        Start scheduler automatically when plugin loads
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="boostInterval" class="form-label">Boost Interval (hours)</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="boostInterval"
                                       x-model="config.boost_interval_hours" 
                                       min="1" 
                                       max="24">
                                <div class="form-text">How often to automatically boost products (1-24 hours)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productsPerBoost" class="form-label">Products per Boost</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="productsPerBoost"
                                       x-model="config.products_per_boost" 
                                       min="1" 
                                       max="10">
                                <div class="form-text">Number of products to boost in each session (1-10)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rotationStrategy" class="form-label">Rotation Strategy</label>
                                <select class="form-select" id="rotationStrategy" x-model="config.rotation_strategy">
                                    <option value="least_recently_boosted">Least Recently Boosted</option>
                                    <option value="random">Random Selection</option>
                                    <option value="highest_sold">Highest Sales Count</option>
                                </select>
                                <div class="form-text">How to select products for boosting</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shopeeApiUrl" class="form-label">ShopeeAPI URL</label>
                                <input type="url" 
                                       class="form-control" 
                                       id="shopeeApiUrl"
                                       x-model="config.shopee_api_url" 
                                       placeholder="http://localhost:8000">
                                <div class="form-text">URL of the ShopeeAPI service for product management</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Filters -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Product Filters</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="minStock" class="form-label">Minimum Stock</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="minStock"
                                       x-model="config.product_filters.min_stock" 
                                       min="0">
                                <div class="form-text">Only boost products with at least this much stock</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Exclude Unlisted Products</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="excludeUnlisted"
                                           x-model="config.product_filters.exclude_unlisted">
                                    <label class="form-check-label" for="excludeUnlisted">
                                        Skip products that are unlisted
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Exclude Inactive Products</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="excludeInactive"
                                           x-model="config.product_filters.exclude_inactive">
                                    <label class="form-check-label" for="excludeInactive">
                                        Skip products that are inactive
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pinned Products Configuration -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pinned Products Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Enable Pinned Products</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="pinnedEnabled"
                                           x-model="config.pinned_products.enabled">
                                    <label class="form-check-label" for="pinnedEnabled">
                                        Enable automatic boosting of pinned products
                                    </label>
                                </div>
                                <div class="form-text">When enabled, pinned products will be automatically boosted</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cooldownHours" class="form-label">Cooldown Hours</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="cooldownHours"
                                       x-model="config.pinned_products.cooldown_hours" 
                                       min="1" 
                                       max="168">
                                <div class="form-text">Hours to wait before boosting the same pinned product again</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logging Configuration -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Logging Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Log Boost Attempts</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="logBoostAttempts"
                                           x-model="config.logging.log_boost_attempts">
                                    <label class="form-check-label" for="logBoostAttempts">
                                        Log all boost attempts for debugging
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Log Product Selection</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="logProductSelection"
                                           x-model="config.logging.log_product_selection">
                                    <label class="form-check-label" for="logProductSelection">
                                        Log product selection process for debugging
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" 
                                @click="saveConfig()" 
                                :disabled="loading">
                            <i class="fas fa-save me-2"></i>
                            <span x-show="!loading">Save Configuration</span>
                            <span x-show="loading">Saving...</span>
                        </button>
                        
                        <button class="btn btn-secondary" 
                                @click="resetConfig()">
                            <i class="fas fa-undo me-2"></i>Reset to Defaults
                        </button>
                        
                        <button class="btn btn-info" 
                                @click="loadCurrentConfig()">
                            <i class="fas fa-sync me-2"></i>Reload Current
                        </button>
                        
                        <button class="btn btn-success" 
                                @click="testConfiguration()">
                            <i class="fas fa-vial me-2"></i>Test Configuration
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Preview -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Configuration Preview</h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded"><code x-text="JSON.stringify(config, null, 2)"></code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Information -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Current Status</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <strong>Scheduler Status:</strong>
                            <span :class="status.scheduler_running ? 'badge bg-success' : 'badge bg-danger'" 
                                  x-text="status.scheduler_running ? 'Running' : 'Stopped'"></span>
                        </li>
                        <li class="mb-2">
                            <strong>Next Boost:</strong>
                            <span x-text="formatDateTime(status.next_boost_time) || 'Not scheduled'"></span>
                        </li>
                        <li class="mb-2">
                            <strong>Plugin Status:</strong>
                            <span :class="status.plugin_status?.enabled ? 'badge bg-success' : 'badge bg-warning'" 
                                  x-text="status.plugin_status?.enabled ? 'Enabled' : 'Disabled'"></span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Configuration Impact</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <strong>Boosts per Day:</strong>
                            <span x-text="calculateBoostsPerDay()"></span>
                        </li>
                        <li class="mb-2">
                            <strong>Products per Day:</strong>
                            <span x-text="calculateProductsPerDay()"></span>
                        </li>
                        <li class="mb-2">
                            <strong>Next Config Update:</strong>
                            <span x-text="configChanged ? 'Requires save' : 'Up to date'" 
                                  :class="configChanged ? 'text-warning' : 'text-success'"></span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div x-show="alertMessage" 
         :class="'alert alert-' + alertType + ' alert-dismissible fade show'" 
         role="alert">
        <span x-text="alertMessage"></span>
        <button type="button" class="btn-close" @click="alertMessage = ''" aria-label="Close"></button>
    </div>
</div>

<script>
function shopeeAutoBoostSettings() {
    return {
        config: {
            enabled: true,
            boost_interval_hours: 4,
            products_per_boost: 5,
            auto_start: true,
            shopee_api_url: 'http://localhost:8000',
            rotation_strategy: 'least_recently_boosted',
            product_filters: {
                min_stock: 1,
                exclude_unlisted: true,
                exclude_inactive: true
            },
            pinned_products: {
                enabled: true,
                product_ids: [],
                cooldown_hours: 4,
                description: "List of product IDs to automatically boost. Each product has individual 4-hour cooldown after successful boost."
            },
            logging: {
                log_boost_attempts: true,
                log_product_selection: true
            }
        },
        originalConfig: {},
        status: {},
        loading: false,
        alertMessage: '',
        alertType: 'info',
        
        init() {
            this.loadCurrentConfig();
            this.loadStatus();
        },
        
        get configChanged() {
            return JSON.stringify(this.config) !== JSON.stringify(this.originalConfig);
        },
        
        async loadCurrentConfig() {
            try {
                const response = await fetch('/api/shopee_auto_boost/status');
                if (response.ok) {
                    const data = await response.json();
                    this.config = { ...data.config };
                    this.originalConfig = { ...data.config };
                    this.showAlert('Configuration loaded successfully', 'success');
                }
            } catch (error) {
                this.showAlert('Error loading configuration: ' + error.message, 'danger');
            }
        },
        
        async loadStatus() {
            try {
                const response = await fetch('/api/shopee_auto_boost/status');
                if (response.ok) {
                    this.status = await response.json();
                }
            } catch (error) {
                console.error('Error loading status:', error);
            }
        },
        
        async saveConfig() {
            this.loading = true;
            try {
                const response = await fetch('/api/shopee_auto_boost/config', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.config)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.originalConfig = { ...this.config };
                    this.showAlert('Configuration saved successfully!', 'success');
                    this.loadStatus(); // Refresh status
                } else {
                    this.showAlert('Failed to save configuration: ' + result.message, 'danger');
                }
            } catch (error) {
                this.showAlert('Error saving configuration: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },
        
        resetConfig() {
            this.config = {
                enabled: true,
                boost_interval_hours: 4,
                products_per_boost: 5,
                auto_start: true,
                shopee_api_url: 'http://localhost:8000',
                rotation_strategy: 'least_recently_boosted',
                product_filters: {
                    min_stock: 1,
                    exclude_unlisted: true,
                    exclude_inactive: true
                },
                pinned_products: {
                    enabled: true,
                    product_ids: [],
                    cooldown_hours: 4,
                    description: "List of product IDs to automatically boost. Each product has individual 4-hour cooldown after successful boost."
                },
                logging: {
                    log_boost_attempts: true,
                    log_product_selection: true
                }
            };
            this.showAlert('Configuration reset to defaults', 'info');
        },
        
        async testConfiguration() {
            try {
                // Test by getting boostable products with current filters
                const response = await fetch('/api/shopee_auto_boost/products');
                if (response.ok) {
                    const data = await response.json();
                    const productCount = data.total_count || 0;
                    
                    if (productCount >= this.config.products_per_boost) {
                        this.showAlert(`Configuration test passed! Found ${productCount} boostable products.`, 'success');
                    } else {
                        this.showAlert(`Warning: Only ${productCount} boostable products found, but configured to boost ${this.config.products_per_boost} products.`, 'warning');
                    }
                } else {
                    this.showAlert('Configuration test failed: Unable to fetch products', 'danger');
                }
            } catch (error) {
                this.showAlert('Configuration test error: ' + error.message, 'danger');
            }
        },
        
        calculateBoostsPerDay() {
            return Math.floor(24 / this.config.boost_interval_hours);
        },
        
        calculateProductsPerDay() {
            return this.calculateBoostsPerDay() * this.config.products_per_boost;
        },
        
        formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString();
        },
        
        showAlert(message, type) {
            this.alertMessage = message;
            this.alertType = type;
            setTimeout(() => this.alertMessage = '', 5000);
        }
    }
}
</script>
{% endblock %}
