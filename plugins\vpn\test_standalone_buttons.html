<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-info">
            <h4>Standalone Button Test</h4>
            <p>This is a completely isolated test to verify button functionality works.</p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Test Buttons</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-primary btn-block" id="testSSHBtn">
                            <i class="fas fa-plug"></i> Test SSH Connection
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-success btn-block" id="testXrayBtn">
                            <i class="fas fa-cogs"></i> Test Xray Service
                        </button>
                    </div>
                </div>
                
                <div id="testResults" class="mt-3" style="display: none;">
                    <div class="alert" id="testAlert" role="alert"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('=== STANDALONE TEST SCRIPT LOADED ===');
        console.log('Current URL:', window.location.href);
        console.log('Document ready state:', document.readyState);
        
        // Immediate test
        alert('Standalone JavaScript is working!');
        
        // Wait for DOM and add handlers
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');
            
            const sshBtn = document.getElementById('testSSHBtn');
            const xrayBtn = document.getElementById('testXrayBtn');
            
            console.log('SSH button found:', !!sshBtn);
            console.log('Xray button found:', !!xrayBtn);
            
            if (sshBtn) {
                sshBtn.addEventListener('click', function() {
                    console.log('SSH BUTTON CLICKED!');
                    alert('SSH Button Clicked Successfully!');
                    
                    // Show result
                    const testResults = document.getElementById('testResults');
                    const testAlert = document.getElementById('testAlert');
                    
                    testAlert.className = 'alert alert-success';
                    testAlert.innerHTML = '<i class="fas fa-check-circle"></i> SSH Button Test Successful!';
                    testResults.style.display = 'block';
                });
                console.log('SSH button handler added');
            }
            
            if (xrayBtn) {
                xrayBtn.addEventListener('click', function() {
                    console.log('XRAY BUTTON CLICKED!');
                    alert('Xray Button Clicked Successfully!');
                    
                    // Show result
                    const testResults = document.getElementById('testResults');
                    const testAlert = document.getElementById('testAlert');
                    
                    testAlert.className = 'alert alert-success';
                    testAlert.innerHTML = '<i class="fas fa-check-circle"></i> Xray Button Test Successful!';
                    testResults.style.display = 'block';
                });
                console.log('Xray button handler added');
            }
        });
        
        // Also try immediate binding after a short delay
        setTimeout(function() {
            console.log('=== IMMEDIATE BINDING TEST ===');
            const sshBtn = document.getElementById('testSSHBtn');
            const xrayBtn = document.getElementById('testXrayBtn');
            
            if (sshBtn && !sshBtn.onclick) {
                sshBtn.onclick = function() {
                    console.log('SSH BUTTON CLICKED (immediate)!');
                    alert('SSH Button Clicked (immediate)!');
                };
                console.log('SSH immediate handler added');
            }
            
            if (xrayBtn && !xrayBtn.onclick) {
                xrayBtn.onclick = function() {
                    console.log('XRAY BUTTON CLICKED (immediate)!');
                    alert('Xray Button Clicked (immediate)!');
                };
                console.log('Xray immediate handler added');
            }
        }, 500);
    </script>
</body>
</html>