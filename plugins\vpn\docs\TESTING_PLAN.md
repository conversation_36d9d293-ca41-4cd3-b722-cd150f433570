# 🧪 VPN Plugin Testing Plan - Server-Client Management Feature

## 📋 Overview

Comprehensive testing plan for the newly implemented server-specific client management feature.

## 🎯 Testing Objectives

1. Verify all new API methods work correctly
2. Ensure UI routes function properly
3. Validate template rendering and responsiveness
4. Test user workflows and navigation
5. Verify error handling and edge cases
6. Ensure performance is acceptable

## 🔧 Pre-Testing Setup

### Prerequisites
- VPN plugin enabled in SteamCodeTool
- At least 2 VPN servers configured
- At least 5-10 clients across different servers
- Mix of client statuses (active, expired, expiring soon, lifetime)

### Test Data Requirements
```
Server 1: "Test Server SG" 
- 5 active clients
- 2 expired clients  
- 1 expiring soon client
- 1 lifetime client

Server 2: "Test Server MY"
- 3 active clients
- 1 expired client
- 2 lifetime clients
```

## 🧪 Test Cases

### **TC001: API Service Methods**

#### TC001.1: get_server_clients_detailed()
**Objective**: Verify enhanced server client data retrieval
**Steps**:
1. Call `api_service.get_server_clients_detailed(server_id=1)`
2. Verify response contains server info
3. Verify response contains clients array
4. Verify response contains total_clients count
5. Check server_config_clients data

**Expected Result**: 
- Returns dict with server, clients, total_clients, server_config_clients
- All data types are correct
- No errors or exceptions

#### TC001.2: get_server_client_stats()
**Objective**: Verify client statistics calculation
**Steps**:
1. Call `api_service.get_server_client_stats(server_id=1)`
2. Verify stats contain total_clients
3. Verify stats contain active_clients
4. Verify stats contain expired_clients
5. Verify stats contain expiring_soon
6. Verify stats contain lifetime_clients

**Expected Result**:
- Returns dict with all stat fields
- Numbers match actual client data
- Calculations are accurate

#### TC001.3: reset_client_traffic()
**Objective**: Verify traffic reset functionality (if available)
**Steps**:
1. Call `api_service.reset_client_traffic(server_id=1, client_email="<EMAIL>")`
2. Check response handling

**Expected Result**:
- Method executes without errors
- Handles API response appropriately

### **TC002: UI Routes**

#### TC002.1: Server-Client Management Route
**Objective**: Verify main route functionality
**Steps**:
1. Navigate to `/admin/vpn/servers/1/clients`
2. Verify page loads successfully
3. Check server information display
4. Verify client list rendering
5. Check statistics display

**Expected Result**:
- Page loads without errors
- Server info displayed correctly
- Client list shows server-specific clients only
- Statistics match expected values

#### TC002.2: Route Parameters
**Objective**: Test route parameter handling
**Steps**:
1. Test with valid server_id: `/admin/vpn/servers/1/clients`
2. Test with invalid server_id: `/admin/vpn/servers/999/clients`
3. Test with non-numeric server_id: `/admin/vpn/servers/abc/clients`

**Expected Result**:
- Valid ID: Page loads correctly
- Invalid ID: Graceful error handling with redirect
- Non-numeric ID: 404 error or proper error handling

#### TC002.3: Query Parameters
**Objective**: Test filtering and pagination
**Steps**:
1. Test search: `?search=<EMAIL>`
2. Test status filter: `?status=active`
3. Test pagination: `?page=2&per_page=25`
4. Test combined: `?search=test&status=expired&page=1&per_page=50`

**Expected Result**:
- Search filters clients correctly
- Status filter shows only matching clients
- Pagination works correctly
- Combined filters work together

### **TC003: API Endpoints**

#### TC003.1: Detailed Server Clients API
**Objective**: Test `/api/servers/<id>/clients/detailed`
**Steps**:
1. GET `/admin/vpn/api/servers/1/clients/detailed`
2. Verify JSON response structure
3. Check data accuracy

**Expected Result**:
- Returns valid JSON
- Contains server and clients data
- HTTP 200 status code

#### TC003.2: Server Client Stats API
**Objective**: Test `/api/servers/<id>/clients/stats`
**Steps**:
1. GET `/admin/vpn/api/servers/1/clients/stats`
2. Verify JSON response structure
3. Check statistics accuracy

**Expected Result**:
- Returns valid JSON with statistics
- Numbers are accurate
- HTTP 200 status code

### **TC004: User Interface**

#### TC004.1: Server List Integration
**Objective**: Verify "Manage Clients" button
**Steps**:
1. Navigate to `/admin/vpn/servers`
2. Locate "Manage Clients" button on each server
3. Click button for Server 1
4. Verify navigation to correct page

**Expected Result**:
- Button visible on all servers
- Button has correct icon and tooltip
- Clicking navigates to server-client page

#### TC004.2: Template Rendering
**Objective**: Verify template displays correctly
**Steps**:
1. Navigate to server-client management page
2. Check server header display
3. Verify statistics cards
4. Check client table rendering
5. Test filter controls

**Expected Result**:
- All elements render correctly
- No broken layouts or missing elements
- Responsive design works on different screen sizes

#### TC004.3: Client Actions
**Objective**: Test client action buttons
**Steps**:
1. Click "Edit" button on a client
2. Click "Extend" button and test functionality
3. Click "Delete" button and test confirmation
4. Test "Add Client" button
5. Test "Bulk Add" button

**Expected Result**:
- Edit button navigates to edit page
- Extend button shows prompt and processes request
- Delete button shows confirmation dialog
- Add buttons navigate with server context

### **TC005: Error Handling**

#### TC005.1: API Errors
**Objective**: Test error handling for API failures
**Steps**:
1. Simulate API service unavailable
2. Test with invalid server ID
3. Test with network timeout

**Expected Result**:
- Graceful error messages displayed
- User redirected appropriately
- No application crashes

#### TC005.2: Data Validation
**Objective**: Test input validation
**Steps**:
1. Test invalid search parameters
2. Test invalid pagination values
3. Test malformed requests

**Expected Result**:
- Invalid inputs handled gracefully
- Appropriate error messages
- Application remains stable

### **TC006: Performance**

#### TC006.1: Page Load Time
**Objective**: Verify acceptable performance
**Steps**:
1. Measure page load time for server with 100+ clients
2. Test pagination performance
3. Test filtering performance

**Expected Result**:
- Page loads within 3 seconds
- Pagination is responsive
- Filtering is immediate

#### TC006.2: Memory Usage
**Objective**: Check memory efficiency
**Steps**:
1. Monitor memory usage during page load
2. Check for memory leaks during navigation

**Expected Result**:
- Memory usage is reasonable
- No significant memory leaks

### **TC007: Cross-Browser Testing**

#### TC007.1: Browser Compatibility
**Objective**: Ensure cross-browser functionality
**Browsers to Test**:
- Chrome (latest)
- Firefox (latest)
- Edge (latest)
- Safari (if available)

**Steps**:
1. Test all functionality in each browser
2. Verify responsive design
3. Check JavaScript functionality

**Expected Result**:
- Consistent functionality across browsers
- No browser-specific issues
- Responsive design works everywhere

### **TC008: Mobile Testing**

#### TC008.1: Mobile Responsiveness
**Objective**: Verify mobile functionality
**Steps**:
1. Test on mobile devices or browser dev tools
2. Check touch interactions
3. Verify responsive layout

**Expected Result**:
- Layout adapts to mobile screens
- Touch interactions work correctly
- All functionality accessible on mobile

## 📊 Test Execution Tracking

### Test Results Template
```
Test Case: TC001.1
Status: [ ] Pass [ ] Fail [ ] Skip
Executed By: [Name]
Date: [Date]
Notes: [Any observations]
Issues Found: [List any issues]
```

## 🐛 Issue Reporting

### Issue Template
```
Issue ID: ISS-001
Test Case: TC001.1
Severity: [ ] Critical [ ] High [ ] Medium [ ] Low
Description: [Detailed description]
Steps to Reproduce: [Steps]
Expected Result: [What should happen]
Actual Result: [What actually happened]
Environment: [Browser, OS, etc.]
Status: [ ] Open [ ] In Progress [ ] Resolved [ ] Closed
```

## ✅ Test Completion Criteria

- [ ] All critical test cases pass
- [ ] No high-severity issues remain open
- [ ] Performance meets requirements
- [ ] Cross-browser compatibility verified
- [ ] Mobile responsiveness confirmed
- [ ] Documentation updated
- [ ] User acceptance testing completed

## 📝 Test Report

After completing all tests, generate a comprehensive test report including:
- Test execution summary
- Pass/fail statistics
- Issues found and resolved
- Performance metrics
- Recommendations for improvement
