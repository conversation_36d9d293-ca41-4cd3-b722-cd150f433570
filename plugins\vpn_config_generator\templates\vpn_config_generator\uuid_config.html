<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Configuration - UUID Access</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">🔄 Generate Additional VPN Configuration</h1>
                <p class="text-gray-600">For returning customers with existing User UUID</p>
            </div>

            <!-- UUID Input Form -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <form id="uuidForm" class="space-y-4">
                    <div>
                        <label for="userUuid" class="block text-sm font-medium text-gray-700 mb-2">
                            User UUID <span class="text-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            id="userUuid" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter your User UUID (e.g., 12345678-1234-1234-1234-123456789abc)"
                        >
                        <p class="text-xs text-gray-500 mt-1">
                            Your UUID was provided when you first generated a configuration. Check your previous configuration results.
                        </p>
                    </div>

                    <button 
                        type="submit" 
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition duration-200"
                    >
                        🔍 Load My Account
                    </button>
                </form>
            </div>

            <!-- Service Selection Modal (Hidden by default) -->
            <div id="serviceModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Generate New Configuration</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeServiceModal()">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- User Info Display -->
                        <div id="userInfo" class="mb-6 p-4 bg-blue-50 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">Your Account Information</h4>
                            <div class="text-sm text-blue-700 space-y-1">
                                <p><span class="font-medium">Order:</span> <span id="displayOrderSn"></span></p>
                                <p><span class="font-medium">Customer:</span> <span id="displayBuyerUsername"></span></p>
                                <p><span class="font-medium">SKU:</span> <span id="displaySku"></span></p>
                                <p><span class="font-medium">Configurations Generated:</span> <span id="displayConfigCount"></span></p>
                                <div id="restrictionInfo" class="mt-2 hidden">
                                    <p class="text-orange-600"><span class="font-medium">⚠️ Restriction:</span> <span id="restrictionMessage"></span></p>
                                </div>
                            </div>
                        </div>

                        <!-- Server Selection Configuration -->
                        <div class="bg-gray-50 p-4 rounded-lg mb-6">
                            <h4 class="font-semibold text-gray-800 mb-3">🖥️ Server Selection</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Choose Server</label>
                                    <select id="serverSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">Loading servers...</option>
                                    </select>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Validity:</span>
                                    <span id="autoValidityDisplay" class="font-medium text-blue-600">Loading...</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Server Tags:</span>
                                    <span id="autoTagsDisplay" class="font-medium text-gray-500">Loading...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Service Configuration Form -->
                        <form id="serviceForm" class="space-y-6">
                            <!-- Hidden fields for form submission -->
                            <input type="hidden" id="serverInput" required>
                            <input type="hidden" id="daysInput" required>

                            <!-- Telco Selection -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Telecommunications Provider</label>
                                <div id="telcoSelection" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <!-- Telco cards will be populated here -->
                                </div>
                            </div>

                            <!-- Plan Selection -->
                            <div id="planSelectionContainer" class="hidden">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Plan</label>
                                <div id="planSelection" class="grid grid-cols-1 gap-3">
                                    <!-- Plan cards will be populated here -->
                                </div>
                            </div>

                            <!-- Generate Button -->
                            <div class="flex space-x-3">
                                <button type="button" onclick="closeServiceModal()" 
                                    class="flex-1 px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition duration-200">
                                    Cancel
                                </button>
                                <button type="submit" id="generateButton" disabled
                                    class="flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition duration-200">
                                    🚀 Generate New Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Configuration Result Modal -->
            <div id="resultModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Configuration Generated</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeResultModal()">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div id="configResult" class="space-y-4">
                            <!-- Configuration result will be displayed here -->
                        </div>
                        
                        <div class="flex justify-end mt-6">
                            <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition duration-200" onclick="closeResultModal()">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 class="font-semibold text-yellow-800 mb-2">💡 Need Help?</h3>
                <div class="text-sm text-yellow-700 space-y-1">
                    <p><strong>Don't have your UUID?</strong> Use the main <a href="/vpn-config-generator/order-config" class="underline text-blue-600">order verification page</a> instead.</p>
                    <p><strong>First time user?</strong> Start with order verification to get your UUID.</p>
                    <p><strong>Multiple configurations:</strong> You can generate as many configurations as needed for different telcos and plans.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentUserData = null;
        let selectedTelco = null;
        let selectedPlan = null;
        let availableTelcos = {{ telcos | tojson }};

        // UUID form submission
        document.getElementById('uuidForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const userUuid = document.getElementById('userUuid').value.trim();
            
            if (!userUuid) {
                alert('Please enter your User UUID');
                return;
            }

            await loadUserByUuid(userUuid);
        });

        async function loadUserByUuid(userUuid) {
            try {
                // For now, we'll simulate loading user data
                // In a real implementation, you'd call an API to get user data by UUID
                alert('UUID-based user loading would be implemented here. For now, please use the main order verification page.');
                
                // TODO: Implement actual UUID-based user loading
                // const response = await axios.post('/vpn-config-generator/api/uuid/load-user', {
                //     user_uuid: userUuid
                // });
                
            } catch (error) {
                alert('Error loading user data: ' + (error.response?.data?.error || error.message));
            }
        }

        // Service form functions (reused from main page)
        async function loadAutoSuggestions() {
            try {
                const sku = currentUserData.var_sku || currentUserData.sku;
                
                // Show loading state
                document.getElementById('autoValidityDisplay').textContent = 'Loading...';
                document.getElementById('autoTagsDisplay').textContent = 'Loading...';
                
                const response = await axios.post('/vpn-config-generator/api/order/get-suggestions', {
                    sku: sku
                });

                if (response.data.success) {
                    const suggestions = response.data.suggestions;
                    
                    // Update display
                    document.getElementById('autoValidityDisplay').textContent = 
                        `${suggestions.auto_validity_days} days`;
                    document.getElementById('autoTagsDisplay').textContent = 
                        suggestions.server_tags.join(', ') || 'Default tags';
                    
                    // Set hidden form values
                    document.getElementById('daysInput').value = suggestions.auto_validity_days || 30;
                    
                    // Populate server dropdown
                    await populateServerDropdown(suggestions.server_tags);
                    
                    // Update generate button state
                    updateGenerateButton();
                    
                } else {
                    throw new Error(response.data.error || 'Failed to load suggestions');
                }
            } catch (error) {
                console.error('Error loading auto suggestions:', error);

                // Try to extract validity from SKU as fallback
                let fallbackValidity = 30;
                let fallbackTags = [];
                const sku = currentUserData.var_sku || currentUserData.sku;

                if (sku) {
                    // Simple regex extraction as fallback
                    const match = sku.match(/_(\d+)$/);
                    if (match) {
                        fallbackValidity = parseInt(match[1]);
                        console.log(`Extracted validity from SKU ${sku}: ${fallbackValidity} days`);
                    }

                    // Try to determine fallback tags based on SKU prefix
                    const skuLower = sku.toLowerCase();
                    if (skuLower.startsWith('sg_')) {
                        fallbackTags = ['singapore', 'digitalocean'];
                    } else if (skuLower.startsWith('my_')) {
                        fallbackTags = ['malaysia', 'shinjiru'];
                    } else {
                        fallbackTags = ['malaysia', 'shinjiru']; // Default fallback
                    }
                }

                // Fallback values
                document.getElementById('autoValidityDisplay').textContent = `${fallbackValidity} days`;
                document.getElementById('autoTagsDisplay').textContent = fallbackTags.join(', ') || 'Default tags';
                document.getElementById('daysInput').value = fallbackValidity;

                // Try to populate servers with fallback tags
                if (fallbackTags.length > 0) {
                    await populateServerDropdown(fallbackTags);
                } else {
                    // Last resort fallback
                    const serverSelect = document.getElementById('serverSelect');
                    serverSelect.innerHTML = '<option value="">Please select a server</option><option value="auto">Auto-selected by system</option>';
                    serverSelect.value = '';
                    document.getElementById('serverInput').value = '';
                }

                updateGenerateButton();
            }
        }

        function populateTelcoSelection() {
            const container = document.getElementById('telcoSelection');
            container.innerHTML = '';
            
            Object.values(availableTelcos).forEach(telco => {
                if (!telco.enabled) return;
                
                // Check if user can access this telco
                const canAccess = !currentUserData.is_restricted || 
                                !currentUserData.assigned_telco || 
                                currentUserData.assigned_telco === telco.id ||
                                currentUserData.allowed_telcos.includes(telco.id);
                
                const card = document.createElement('div');
                card.className = `telco-card p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 ${canAccess ? '' : 'opacity-50 cursor-not-allowed'}`;
                card.innerHTML = `
                    <h4 class="font-semibold text-gray-800">${telco.name}</h4>
                    <p class="text-sm text-gray-600 mt-1">${telco.description}</p>
                    <p class="text-xs text-blue-600 mt-2">${Object.keys(telco.plans || {}).length} plans available</p>
                `;
                
                if (canAccess) {
                    card.addEventListener('click', () => selectTelco(telco.id, card));
                }
                
                container.appendChild(card);
            });
        }

        function selectTelco(telcoId, cardElement) {
            // Remove previous selection
            document.querySelectorAll('.telco-card').forEach(card => {
                card.classList.remove('border-blue-500', 'bg-blue-50');
            });
            
            // Select current telco
            cardElement.classList.add('border-blue-500', 'bg-blue-50');
            selectedTelco = telcoId;
            
            // Show plan selection
            populatePlanSelection(telcoId);
            document.getElementById('planSelectionContainer').classList.remove('hidden');
            
            // Reset plan selection
            selectedPlan = null;
            updateGenerateButton();
        }

        function populatePlanSelection(telcoId) {
            const container = document.getElementById('planSelection');
            container.innerHTML = '';

            const telco = availableTelcos[telcoId];
            if (!telco || !telco.plans) return;

            Object.values(telco.plans).forEach(plan => {
                if (!plan.enabled) return;

                const card = document.createElement('div');
                card.className = 'plan-card p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition duration-200';
                card.innerHTML = `
                    <h4 class="font-semibold text-gray-800">${plan.name}</h4>
                    <p class="text-sm text-gray-600 mt-1">${plan.description}</p>
                `;

                card.addEventListener('click', () => selectPlan(plan.id, card));
                container.appendChild(card);
            });
        }

        function selectPlan(planId, cardElement) {
            // Remove previous selection
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('border-blue-500', 'bg-blue-50');
                card.classList.add('border-gray-200');
            });

            // Select current plan
            cardElement.classList.remove('border-gray-200');
            cardElement.classList.add('border-blue-500', 'bg-blue-50');
            selectedPlan = planId;

            updateGenerateButton();
        }

        function updateGenerateButton() {
            const button = document.getElementById('generateButton');
            const server = document.getElementById('serverInput').value;
            const days = document.getElementById('daysInput').value;
            
            if (server && days && selectedTelco && selectedPlan) {
                button.disabled = false;
            } else {
                button.disabled = true;
            }
        }

        // Service form submission
        document.getElementById('serviceForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await generateConfiguration();
        });

        async function generateConfiguration() {
            try {
                const response = await axios.post('/vpn-config-generator/api/uuid/generate-config', {
                    user_uuid: currentUserData.user_uuid,
                    telco: selectedTelco,
                    plan: selectedPlan
                });

                if (response.data.success) {
                    showConfigurationResult(response.data);
                } else {
                    alert('Error: ' + response.data.error);
                }
            } catch (error) {
                const errorMessage = error.response?.data?.error || error.message || 'An error occurred';
                alert('Error: ' + errorMessage);
            }
        }

        function showConfigurationResult(result) {
            closeServiceModal();
            
            const container = document.getElementById('configResult');
            container.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 class="font-semibold text-green-800 mb-2">✅ Configuration Generated Successfully</h4>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">Created:</span> ${result.created_date}</p>
                        <p><span class="font-medium">Expires:</span> ${result.expired_date}</p>
                        <p><span class="font-medium">Total Configurations:</span> ${result.user_info.configurations_generated}</p>
                    </div>
                </div>
                
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">VPN Configuration</h4>
                        <button onclick="copyConfiguration()" class="text-blue-600 hover:text-blue-800 text-sm">
                            📋 Copy
                        </button>
                    </div>
                    <pre id="configText" class="text-xs bg-white p-3 rounded border overflow-x-auto">${result.config}</pre>
                </div>
            `;
            
            document.getElementById('resultModal').classList.remove('hidden');
        }

        function copyConfiguration() {
            const configText = document.getElementById('configText').textContent;
            navigator.clipboard.writeText(configText).then(() => {
                alert('Configuration copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }



        function closeServiceModal() {
            document.getElementById('serviceModal').classList.add('hidden');
        }

        async function populateServerDropdown(serverTags) {
            const serverSelect = document.getElementById('serverSelect');

            try {
                // Get servers that match the tags
                const response = await axios.post('/vpn-config-generator/api/servers/by-tags', {
                    tags: serverTags
                });

                if (response.data.success && response.data.servers) {
                    const servers = response.data.servers;

                    // Clear existing options
                    serverSelect.innerHTML = '';

                    if (servers.length > 0) {
                        // Add a prompt option first
                        const promptOption = document.createElement('option');
                        promptOption.value = '';
                        promptOption.textContent = `Choose from ${servers.length} available server${servers.length > 1 ? 's' : ''}`;
                        promptOption.disabled = true;
                        promptOption.selected = true;
                        serverSelect.appendChild(promptOption);

                        // Add all matching servers
                        servers.forEach(server => {
                            const option = document.createElement('option');
                            option.value = server.id;
                            option.textContent = `${server.name} (${server.location || 'Unknown'})`;
                            serverSelect.appendChild(option);
                        });

                        // Don't auto-select any server - let user choose
                        document.getElementById('serverInput').value = '';

                        console.log(`Populated ${servers.length} servers for tags: ${serverTags.join(', ')}`);
                    } else {
                        // No servers found with matching tags
                        serverSelect.innerHTML = '<option value="">No servers found</option><option value="auto">Auto-selected by system</option>';
                        serverSelect.value = '';
                        document.getElementById('serverInput').value = '';
                        console.warn(`No servers found for tags: ${serverTags.join(', ')}`);
                    }
                } else {
                    throw new Error(response.data.error || 'Failed to load servers');
                }
            } catch (error) {
                console.error('Error loading servers:', error);

                // Fallback to auto selection
                serverSelect.innerHTML = '<option value="">Please select a server</option><option value="auto">Auto-selected by system</option>';
                serverSelect.value = '';
                document.getElementById('serverInput').value = '';
            }

            updateGenerateButton();
        }
        
        // Handle server selection change
        document.addEventListener('DOMContentLoaded', function() {
            const serverSelect = document.getElementById('serverSelect');
            if (serverSelect) {
                serverSelect.addEventListener('change', function() {
                    document.getElementById('serverInput').value = this.value;
                    updateGenerateButton();
                });
            }
        });

        function closeResultModal() {
            document.getElementById('resultModal').classList.add('hidden');
        }
    </script>
</body>
</html> 