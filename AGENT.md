# AGENT.md

## Commands
```bash
# Main application
python main.py
pip install -r requirements.txt

# Testing
cd ShopeeAPI && python run_tests.py  # ShopeeAPI tests
pytest                                # Run pytest tests

# Docker deployment
docker-compose -f docker-compose.steamcodetool.yml up -d  # Production
docker-compose -f docker-compose.steamcodetool.yml up mtyb-tools-local  # Development

# Health checks
curl http://localhost:5000/health
```

## Architecture
- **Plugin-based Flask app**: Modular plugins in `plugins/[name]/` with `PluginInterface`
- **Separate ShopeeAPI**: FastAPI app in `ShopeeAPI/` directory with WebSocket integration
- **Core components**: `core/plugin_manager.py`, `main.py`, `api/` routes, `services/`
- **Configuration**: <PERSON><PERSON><PERSON> configs in `configs/`, data in `data/`, logs in `logs/`

## Code Style
- **Imports**: stdlib → third-party → local, specific imports preferred
- **Naming**: PascalCase classes, snake_case functions/vars, UPPER_SNAKE_CASE constants
- **Types**: Extensive type hints from `typing` module
- **Error handling**: Custom exception hierarchy, try-except with logging, graceful degradation
- **Security**: Rate limiting, input sanitization, audit logging, thread-safe singletons
