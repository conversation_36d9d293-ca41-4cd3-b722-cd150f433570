/**
 * SteamCodeTool Dashboard Widget Loader
 * Dynamically loads and manages dashboard widgets from plugins
 */

class DashboardWidgetLoader {
    constructor() {
        this.widgets = [];
        this.loadedScripts = new Set();
        this.widgetInstances = new Map();
        this.widgetPositions = {
            header: [],
            main: [],
            sidebar: [],
            footer: []
        };
    }

    /**
     * Initialize the widget loader
     */
    async init() {
        try {
            console.log('Initializing widget loader...');
            // Fetch available widgets from the server
            const response = await fetch('/admin/api/dashboard/widgets');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Widget data received:', data);

            if (data.success) {
                this.widgets = data.widgets || [];
                console.log(`Loading ${this.widgets.length} widgets...`);
                await this.loadWidgets();
                this.startAutoRefresh();
                console.log('Widget loader initialization complete');
            } else {
                console.error('Failed to fetch widgets:', data.error);
            }
        } catch (error) {
            console.error('Error initializing widget loader:', error);
        }
    }

    /**
     * Load all widgets based on their position and order
     */
    async loadWidgets() {
        // Group widgets by position
        if (!Array.isArray(this.widgets)) {
            this.widgets = [];
        }

        this.widgets.forEach(widget => {
            const position = widget.position || 'main';
            if (!this.widgetPositions[position]) {
                this.widgetPositions[position] = [];
            }
            this.widgetPositions[position].push(widget);
        });

        // Sort widgets by order within each position
        Object.keys(this.widgetPositions).forEach(position => {
            this.widgetPositions[position].sort((a, b) => a.order - b.order);
        });

        // Load widgets for each position
        for (const position of Object.keys(this.widgetPositions)) {
            const container = document.getElementById(`widget-container-${position}`);
            if (!container) continue;

            for (const widget of this.widgetPositions[position]) {
                await this.loadWidget(widget, container);
            }
        }
    }

    /**
     * Load a single widget
     */
    async loadWidget(widget, container) {
        try {
            console.log(`Loading widget: ${widget.widget_id} (${widget.title})`);

            // Create widget wrapper
            const widgetWrapper = document.createElement('div');
            widgetWrapper.id = `widget-wrapper-${widget.widget_id}`;
            widgetWrapper.className = `widget-wrapper widget-size-${widget.size}`;
            widgetWrapper.setAttribute('data-widget-id', widget.widget_id);
            widgetWrapper.setAttribute('data-plugin', widget.plugin_name);

            // Create widget container
            const widgetContainer = document.createElement('div');
            widgetContainer.id = widget.template_id;
            widgetContainer.className = 'widget-container';

            // Add loading state
            widgetContainer.innerHTML = `
                <div class="widget-loading">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>Loading ${widget.title}...</p>
                </div>
            `;

            widgetWrapper.appendChild(widgetContainer);
            container.appendChild(widgetWrapper);

            // Load widget script if not already loaded
            if (!this.loadedScripts.has(widget.script_path)) {
                console.log(`Loading script: ${widget.script_path}`);
                await this.loadScript(widget.script_path);
                this.loadedScripts.add(widget.script_path);
            }

            // Initialize widget
            await this.initializeWidget(widget);

        } catch (error) {
            console.error(`Error loading widget ${widget.widget_id}:`, error);
            this.showWidgetError(widget.widget_id, error.message);
        }
    }

    /**
     * Load a JavaScript file dynamically
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.type = 'module';
            script.src = src;
            script.onload = resolve;
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            document.head.appendChild(script);
        });
    }

    /**
     * Initialize a widget after its script has loaded
     */
    async initializeWidget(widget) {
        try {
            console.log(`Initializing widget: ${widget.widget_id}`);

            // Wait for the widget class to be available
            const maxAttempts = 20;
            let attempts = 0;

            while (attempts < maxAttempts) {
                if (window.DashboardWidgets && window.DashboardWidgets[widget.widget_id]) {
                    console.log(`Found widget class for: ${widget.widget_id}`);
                    const WidgetClass = window.DashboardWidgets[widget.widget_id];
                    const instance = new WidgetClass(widget);

                    this.widgetInstances.set(widget.widget_id, instance);

                    // Initialize the widget
                    console.log(`Calling init() for widget: ${widget.widget_id}`);
                    await instance.init();
                    console.log(`Widget initialized successfully: ${widget.widget_id}`);

                    // Set up auto-refresh if configured
                    if (widget.refresh_interval) {
                        this.setupAutoRefresh(widget.widget_id, widget.refresh_interval);
                    }

                    return;
                }

                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            console.error(`Widget class not found for: ${widget.widget_id}. Available widgets:`, Object.keys(window.DashboardWidgets || {}));
            throw new Error('Widget class not found');

        } catch (error) {
            console.error(`Error initializing widget ${widget.widget_id}:`, error);
            this.showWidgetError(widget.widget_id, error.message);
        }
    }

    /**
     * Set up auto-refresh for a widget
     */
    setupAutoRefresh(widgetId, interval) {
        const instance = this.widgetInstances.get(widgetId);
        if (!instance) return;

        setInterval(() => {
            if (instance.refresh) {
                instance.refresh();
            }
        }, interval * 1000);
    }

    /**
     * Start global auto-refresh timer
     */
    startAutoRefresh() {
        // Check for widgets that need refreshing every second
        setInterval(() => {
            this.widgetInstances.forEach((instance, widgetId) => {
                if (instance.shouldRefresh && instance.shouldRefresh()) {
                    instance.refresh();
                }
            });
        }, 1000);
    }

    /**
     * Show error message for a widget
     */
    showWidgetError(widgetId, message) {
        // Find the widget by its widget_id and get its template_id
        const widget = this.widgets.find(w => w.widget_id === widgetId);
        if (widget) {
            const container = document.getElementById(widget.template_id);
            if (container) {
                container.innerHTML = `
                    <div class="widget-error">
                        <i class="fas fa-exclamation-triangle text-red-500"></i>
                        <p>Error loading widget: ${message}</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Refresh all widgets
     */
    async refreshAll() {
        for (const [widgetId, instance] of this.widgetInstances) {
            if (instance.refresh) {
                await instance.refresh();
            }
        }
    }

    /**
     * Remove a widget from the dashboard
     */
    removeWidget(widgetId) {
        const wrapper = document.getElementById(`widget-wrapper-${widgetId}`);
        if (wrapper) {
            wrapper.remove();
        }

        const instance = this.widgetInstances.get(widgetId);
        if (instance && instance.destroy) {
            instance.destroy();
        }

        this.widgetInstances.delete(widgetId);
    }

    /**
     * Reload all widgets (useful when plugin status changes)
     */
    async reload() {
        // Clear all widgets
        this.widgetInstances.forEach((instance, widgetId) => {
            this.removeWidget(widgetId);
        });

        // Clear containers
        Object.keys(this.widgetPositions).forEach(position => {
            const container = document.getElementById(`widget-container-${position}`);
            if (container) {
                container.innerHTML = '';
            }
        });

        // Reset state
        this.widgets = [];
        this.widgetPositions = {
            header: [],
            main: [],
            sidebar: [],
            footer: []
        };

        // Reinitialize
        await this.init();
    }
}

// Base widget class that all widgets should extend
class DashboardWidget {
    constructor(config) {
        this.config = config;
        this.container = document.getElementById(config.template_id);
        this.data = null;
        this.lastRefresh = null;
    }

    /**
     * Initialize the widget
     */
    async init() {
        await this.loadData();
        this.render();
    }

    /**
     * Load data from the server
     */
    async loadData() {
        try {
            const response = await fetch(this.config.data_endpoint);
            const result = await response.json();
            
            if (result.success) {
                this.data = result.data;
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load data');
            }
        } catch (error) {
            console.error(`Error loading data for widget ${this.config.widget_id}:`, error);
            this.showError(error.message);
        }
    }

    /**
     * Render the widget
     */
    render() {
        // Override in subclass
    }

    /**
     * Refresh the widget data
     */
    async refresh() {
        await this.loadData();
        this.render();
    }

    /**
     * Show error message
     */
    showError(message) {
        if (this.container) {
            this.container.innerHTML = `
                <div class="widget-error p-4">
                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                    <p class="text-red-600">${message}</p>
                </div>
            `;
        }
    }

    /**
     * Show loading state
     */
    showLoading() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="widget-loading p-4 text-center">
                    <i class="fas fa-spinner fa-spin fa-2x text-gray-400"></i>
                    <p class="mt-2 text-gray-600">Loading...</p>
                </div>
            `;
        }
    }

    /**
     * Destroy the widget
     */
    destroy() {
        // Override in subclass if cleanup is needed
    }
}

// Export for use in other modules
window.DashboardWidgetLoader = DashboardWidgetLoader;
window.DashboardWidget = DashboardWidget;
window.DashboardWidgets = {};

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardLoader = new DashboardWidgetLoader();
    window.dashboardLoader.init();
}); 