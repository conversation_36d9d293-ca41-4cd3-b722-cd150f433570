<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Redemption Links - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        .crypto-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .crypto-button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .crypto-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
        .crypto-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .crypto-button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        .crypto-button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .crypto-input {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.2s ease;
        }
        .crypto-input:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #2563eb);
            height: 8px;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Bulk Redemption Links</h1>
                    <p class="text-gray-600">Create multiple redemption links at once</p>
                </div>
                <div class="flex space-x-2">
                    <a href="/vpn-config-generator/admin/redemption-links" class="crypto-button secondary">← Back to Links</a>
                    <button onclick="downloadCSVTemplate()" class="crypto-button secondary">Download CSV Template</button>
                </div>
            </div>
        </div>

        <!-- Bulk Creation Form -->
        <div class="crypto-card p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Bulk Creation Settings</h2>
            
            <!-- Customer Input Methods -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Customer Input Method</label>
                <div class="flex space-x-4 mb-4">
                    <label class="flex items-center">
                        <input type="radio" name="inputMethod" value="manual" checked class="mr-2">
                        <span>Manual List</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="inputMethod" value="csv" class="mr-2">
                        <span>CSV Upload</span>
                    </label>
                </div>
                
                <!-- Manual Input -->
                <div id="manualInput" class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Customer Usernames (one per line)</label>
                    <textarea id="customerUsernames" class="crypto-input w-full h-32" placeholder="customer1&#10;customer2&#10;customer3"></textarea>
                </div>
                
                <!-- CSV Input -->
                <div id="csvInput" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">CSV Data</label>
                    <textarea id="csvData" class="crypto-input w-full h-32" placeholder="Paste CSV data here (first column should contain usernames)"></textarea>
                    <button type="button" onclick="parseCsvData()" class="crypto-button secondary mt-2">Parse CSV</button>
                </div>
                
                <!-- Parsed Results -->
                <div id="parsedResults" class="hidden mt-4 p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-semibold text-blue-900 mb-2">Parsed Results</h4>
                    <div id="parsedContent"></div>
                </div>
            </div>

            <form id="bulkCreateForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Validity Days *</label>
                    <input type="number" id="validityDays" class="crypto-input w-full" value="30" min="1" max="365" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Server ID</label>
                    <input type="text" id="serverId" class="crypto-input w-full" placeholder="auto or specific server ID">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Telco</label>
                    <select id="telco" class="crypto-input w-full">
                        <option value="">Select Telco (optional)</option>
                        <option value="digi">Digi</option>
                        <option value="maxis">Maxis</option>
                        <option value="celcom">Celcom</option>
                        <option value="umobile">U Mobile</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Plan</label>
                    <select id="plan" class="crypto-input w-full">
                        <option value="">Select Plan (optional)</option>
                        <option value="unlimited">Unlimited</option>
                        <option value="basic">Basic</option>
                        <option value="premium">Premium</option>
                        <option value="booster">Booster</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Variable SKU</label>
                    <input type="text" id="varSku" class="crypto-input w-full" placeholder="e.g., VPN_30D">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Link Expiry (optional)</label>
                    <input type="datetime-local" id="expiresAt" class="crypto-input w-full">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Uses</label>
                    <input type="number" id="maxUses" class="crypto-input w-full" value="1" min="1" max="100">
                </div>
                <div class="flex items-center">
                    <label class="flex items-center">
                        <input type="checkbox" id="sendViaChat" class="mr-2">
                        <span class="text-sm font-medium text-gray-700">Send via Chat automatically</span>
                    </label>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" class="crypto-input w-full" rows="3" placeholder="Optional notes for all redemption links"></textarea>
                </div>
                <div class="md:col-span-2">
                    <button type="submit" id="createBulkButton" class="crypto-button">
                        <span id="createBulkButtonText">Create Bulk Redemption Links</span>
                        <span id="createBulkButtonSpinner" class="hidden">Creating...</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Progress Section -->
        <div id="progressSection" class="crypto-card p-6 mb-8 hidden">
            <h2 class="text-xl font-semibold mb-4">Creation Progress</h2>
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                    <span id="progressText">Processing...</span>
                    <span id="progressPercent">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="progress-bar w-0"></div>
                </div>
            </div>
            <div id="progressDetails" class="text-sm text-gray-600"></div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="crypto-card p-6 hidden">
            <h2 class="text-xl font-semibold mb-4">Creation Results</h2>
            <div id="resultsContent"></div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

    <script>
        let parsedUsernames = [];

        // Handle input method switching
        document.querySelectorAll('input[name="inputMethod"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const manualInput = document.getElementById('manualInput');
                const csvInput = document.getElementById('csvInput');
                const parsedResults = document.getElementById('parsedResults');
                
                if (this.value === 'manual') {
                    manualInput.classList.remove('hidden');
                    csvInput.classList.add('hidden');
                    parsedResults.classList.add('hidden');
                } else {
                    manualInput.classList.add('hidden');
                    csvInput.classList.remove('hidden');
                }
            });
        });

        // Parse CSV data
        async function parseCsvData() {
            const csvData = document.getElementById('csvData').value.trim();
            if (!csvData) {
                showMessage('Please enter CSV data first', 'error');
                return;
            }

            try {
                const response = await axios.post('/vpn-config-generator/api/redemption-links/bulk/parse-csv', {
                    csv_data: csvData
                });

                if (response.data.success) {
                    parsedUsernames = response.data.usernames;
                    const resultsDiv = document.getElementById('parsedResults');
                    const contentDiv = document.getElementById('parsedContent');
                    
                    let content = `<p class="text-green-700 mb-2">✅ Found ${response.data.total_count} usernames</p>`;
                    
                    if (response.data.errors.length > 0) {
                        content += `<p class="text-yellow-700 mb-2">⚠️ ${response.data.errors.length} warnings:</p>`;
                        content += `<ul class="text-sm text-yellow-600 mb-2">`;
                        response.data.errors.forEach(error => {
                            content += `<li>• ${error}</li>`;
                        });
                        content += `</ul>`;
                    }
                    
                    content += `<details class="mt-2"><summary class="cursor-pointer text-blue-600">Show parsed usernames</summary>`;
                    content += `<div class="mt-2 p-2 bg-white rounded border text-sm font-mono">${parsedUsernames.join(', ')}</div></details>`;
                    
                    contentDiv.innerHTML = content;
                    resultsDiv.classList.remove('hidden');
                    
                    showMessage('CSV parsed successfully!', 'success');
                } else {
                    showMessage('Error parsing CSV: ' + response.data.error, 'error');
                }
            } catch (error) {
                showMessage('Error parsing CSV: ' + (error.response?.data?.error || error.message), 'error');
            }
        }

        // Download CSV template
        function downloadCSVTemplate() {
            const csvContent = "customer_username\ncustomer1\ncustomer2\ncustomer3";
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'redemption_links_template.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Handle bulk creation form submission
        document.getElementById('bulkCreateForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get customer usernames
            let customerUsernames = [];
            const inputMethod = document.querySelector('input[name="inputMethod"]:checked').value;
            
            if (inputMethod === 'manual') {
                const manualInput = document.getElementById('customerUsernames').value.trim();
                if (!manualInput) {
                    showMessage('Please enter customer usernames', 'error');
                    return;
                }
                customerUsernames = manualInput.split('\n').map(u => u.trim()).filter(u => u);
            } else {
                if (parsedUsernames.length === 0) {
                    showMessage('Please parse CSV data first', 'error');
                    return;
                }
                customerUsernames = parsedUsernames;
            }
            
            if (customerUsernames.length === 0) {
                showMessage('No valid customer usernames found', 'error');
                return;
            }

            const button = document.getElementById('createBulkButton');
            const buttonText = document.getElementById('createBulkButtonText');
            const buttonSpinner = document.getElementById('createBulkButtonSpinner');
            const progressSection = document.getElementById('progressSection');
            const resultsSection = document.getElementById('resultsSection');
            
            // Show loading state
            button.disabled = true;
            buttonText.classList.add('hidden');
            buttonSpinner.classList.remove('hidden');
            progressSection.classList.remove('hidden');
            resultsSection.classList.add('hidden');
            
            try {
                const formData = {
                    customer_usernames: customerUsernames,
                    validity_days: parseInt(document.getElementById('validityDays').value),
                    server_id: document.getElementById('serverId').value || null,
                    telco: document.getElementById('telco').value || null,
                    plan: document.getElementById('plan').value || null,
                    var_sku: document.getElementById('varSku').value || null,
                    expires_at: document.getElementById('expiresAt').value || null,
                    max_uses: parseInt(document.getElementById('maxUses').value),
                    notes: document.getElementById('notes').value || null,
                    send_via_chat: document.getElementById('sendViaChat').checked,
                    created_by: 'admin',
                    base_url: window.location.origin
                };

                // Update progress
                updateProgress(0, `Creating ${customerUsernames.length} redemption links...`);

                const response = await axios.post('/vpn-config-generator/api/redemption-links/bulk', formData);
                
                if (response.data.success) {
                    const result = response.data.result;
                    updateProgress(100, 'Bulk creation completed!');
                    
                    // Show results
                    displayBulkResults(result);
                    resultsSection.classList.remove('hidden');
                    
                    showMessage(`Successfully created ${result.successful_links.length}/${result.total_requested} redemption links!`, 'success');
                } else {
                    showMessage('Error: ' + (response.data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                showMessage('Error creating bulk redemption links: ' + (error.response?.data?.error || error.message), 'error');
            } finally {
                // Reset button state
                button.disabled = false;
                buttonText.classList.remove('hidden');
                buttonSpinner.classList.add('hidden');
            }
        });

        // Update progress bar
        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressPercent').textContent = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        // Display bulk creation results
        function displayBulkResults(result) {
            const container = document.getElementById('resultsContent');
            
            let html = `
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-green-800">Successful</h3>
                        <p class="text-2xl font-bold text-green-600">${result.successful_links.length}</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-red-800">Failed</h3>
                        <p class="text-2xl font-bold text-red-600">${result.failed_links.length}</p>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-blue-800">Total</h3>
                        <p class="text-2xl font-bold text-blue-600">${result.total_requested}</p>
                    </div>
                </div>
            `;

            if (result.successful_links.length > 0) {
                html += `
                    <div class="mb-6">
                        <h3 class="font-semibold text-green-800 mb-2">✅ Successfully Created Links</h3>
                        <div class="max-h-64 overflow-y-auto">
                            <table class="w-full text-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-left p-2">Customer</th>
                                        <th class="text-left p-2">Link ID</th>
                                        <th class="text-left p-2">Redemption URL</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;
                
                result.successful_links.forEach(link => {
                    const url = `${window.location.origin}/vpn-config-generator/redeem/${link.id}`;
                    html += `
                        <tr class="border-t">
                            <td class="p-2">${link.customer_username}</td>
                            <td class="p-2 font-mono text-xs">${link.id}</td>
                            <td class="p-2">
                                <button onclick="copyToClipboard('${url}')" class="text-blue-600 hover:text-blue-800 text-xs">Copy URL</button>
                            </td>
                        </tr>
                    `;
                });
                
                html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }

            if (result.failed_links.length > 0) {
                html += `
                    <div class="mb-6">
                        <h3 class="font-semibold text-red-800 mb-2">❌ Failed Links</h3>
                        <div class="max-h-32 overflow-y-auto">
                            <ul class="text-sm">
                `;
                
                result.failed_links.forEach(failed => {
                    html += `<li class="text-red-600">• ${failed.username}: ${failed.error}</li>`;
                });
                
                html += `
                            </ul>
                        </div>
                    </div>
                `;
            }

            if (result.chat_results) {
                const chatSuccess = Object.values(result.chat_results).filter(Boolean).length;
                const chatTotal = Object.keys(result.chat_results).length;
                
                html += `
                    <div class="mb-6">
                        <h3 class="font-semibold text-blue-800 mb-2">💬 Chat Delivery Results</h3>
                        <p class="text-sm text-gray-600">Successfully sent ${chatSuccess}/${chatTotal} messages via chat</p>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // Copy to clipboard helper
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showMessage('URL copied to clipboard!', 'success');
            }).catch(() => {
                showMessage('Failed to copy URL', 'error');
            });
        }

        // Show success/error messages
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded-lg mb-2 ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            messageDiv.textContent = message;
            
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
