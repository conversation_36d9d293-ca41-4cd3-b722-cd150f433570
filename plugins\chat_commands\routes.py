"""
Routes for Chat Commands Plugin
"""

import logging
import os
from flask import Blueprint, request, jsonify, render_template, session, redirect, url_for
from functools import wraps
from typing import Dict, Any
import json

from .services import ChatCommandService, MessageProcessor, WebhookManager
from .models import ChatCommand, WebhookConfig, DebugConfig, CommandConfig

logger = logging.getLogger(__name__)


def create_routes(plugin_instance) -> Blueprint:
    """Create and return the blueprint for chat commands routes"""

    # Don't specify template_folder to use main app's templates directory
    bp = Blueprint('chat_commands', __name__,
                   url_prefix='/chat-commands')
    command_service = plugin_instance.command_service
    message_processor = plugin_instance.message_processor
    webhook_manager = WebhookManager(command_service)

    # ========== Helper Functions ==========
    def login_required(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'admin_logged_in' not in session:
                # Assuming your main admin login route is 'admin.login'
                # And the session key for login status is 'admin_logged_in'
                return redirect(url_for('admin.login', next=request.url))
            return f(*args, **kwargs)
        return decorated_function
    
    @bp.route('/')
    @login_required
    def index():
        """Main chat commands management page"""
        try:
            commands = command_service.get_all_commands()  # This is already Dict[str, Dict[str, Any]]
            webhook_config_obj = command_service.get_webhook_config()
            debug_config_obj = command_service.get_debug_config()
            command_config_obj = command_service.get_command_config()

            # Convert config objects to dictionaries for the template and logging
            webhook_config_dict = webhook_config_obj.to_dict() if webhook_config_obj else {}
            debug_config_dict = debug_config_obj.to_dict() if debug_config_obj else {}
            command_config_dict = command_config_obj.to_dict() if command_config_obj else {}

            # Log the data being passed to the template
            logger.info("Rendering chat_commands.html with data:")
            logger.info(f"  commands type: {type(commands)}")
            logger.info(f"  Number of commands: {len(commands) if commands else 0}")
            if commands:
                # commands is already Dict[str, Dict[str, Any]], so list(commands.values())[0] is a dict
                logger.info(f"  First command (if any): {list(commands.values())[0] if commands else 'No commands'}")

            logger.info(f"  webhook_config_dict: {webhook_config_dict}")
            logger.info(f"  debug_config_dict: {debug_config_dict}")
            logger.info(f"  command_config_dict: {command_config_dict}")
            
            if command_config_dict:
                logger.info(f"  command_config_dict.command_prefix: {command_config_dict.get('command_prefix', 'Not found')}")
            if webhook_config_dict:
                logger.info(f"  webhook_config_dict.shopee_api_base_url: {webhook_config_dict.get('shopee_api_base_url', 'Not found')}")
                logger.info(f"  webhook_config_dict.steamcodetool_base_url: {webhook_config_dict.get('steamcodetool_base_url', 'Not found')}")
                # Check for any secret key like attribute
                for key, value in webhook_config_dict.items():
                    if 'secret' in key.lower() or 'key' in key.lower():
                        logger.info(f"  Potential secret in webhook_config_dict: {key} = {value}")

            return render_template('chat_commands.html',
                                 commands=commands,
                                 webhook_config=webhook_config_dict,
                                 debug_config=debug_config_dict,
                                 command_config=command_config_dict)
        except Exception as e:
            logger.error(f"Error loading chat commands page: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return jsonify({'error': str(e)}), 500
    
    @bp.route('/api/commands', methods=['GET'])
    @login_required
    def get_commands():
        """Get all commands"""
        try:
            commands_data = command_service.get_all_commands() # This now returns Dict[str, Dict[str, Any]]
            return jsonify({
                'success': True,
                'commands': commands_data # Already a dictionary of dicts
            })
        except Exception as e:
            logger.error(f"Error getting commands: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/commands/<command_name>', methods=['GET'])
    @login_required
    def get_command(command_name: str):
        """Get a specific command"""
        try:
            command = command_service.get_command(command_name)
            if command:
                return jsonify({
                    'success': True,
                    'command': command.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Command not found'}), 404
        except Exception as e:
            logger.error(f"Error getting command {command_name}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/commands', methods=['POST'])
    @login_required
    def create_command():
        """Create a new command"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400
            
            # Validate required fields
            required_fields = ['command', 'description', 'response_text']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400
            
            # Create command object
            command = ChatCommand.from_dict(data)
            
            # Check if command already exists
            if command_service.get_command(command.command):
                return jsonify({'success': False, 'error': 'Command already exists'}), 409
            
            # Add command
            if command_service.add_command(command):
                return jsonify({
                    'success': True,
                    'message': 'Command created successfully',
                    'command': command.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to create command'}), 500
                
        except Exception as e:
            logger.error(f"Error creating command: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/commands/<command_name>', methods=['PUT'])
    @login_required
    def update_command(command_name: str):
        """Update an existing command"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400
            
            # Check if command exists
            if not command_service.get_command(command_name):
                return jsonify({'success': False, 'error': 'Command not found'}), 404
            
            # Create command object
            command = ChatCommand.from_dict(data)
            
            # Update command
            if command_service.update_command(command_name, command):
                return jsonify({
                    'success': True,
                    'message': 'Command updated successfully',
                    'command': command.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update command'}), 500
                
        except Exception as e:
            logger.error(f"Error updating command {command_name}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/commands/<command_name>', methods=['DELETE'])
    @login_required
    def delete_command(command_name: str):
        """Delete a command"""
        try:
            # Check if command exists
            if not command_service.get_command(command_name):
                return jsonify({'success': False, 'error': 'Command not found'}), 404
            
            # Delete command
            if command_service.delete_command(command_name):
                return jsonify({
                    'success': True,
                    'message': 'Command deleted successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to delete command'}), 500
                
        except Exception as e:
            logger.error(f"Error deleting command {command_name}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    


    @bp.route('/api/webhook-config', methods=['GET'])
    @login_required
    def get_webhook_config():
        """Get webhook configuration"""
        try:
            config = command_service.get_webhook_config()
            return jsonify({
                'success': True,
                'config': config.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting webhook config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/webhook-config', methods=['PUT'])
    @login_required
    def update_webhook_config():
        """Update webhook configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Create config object
            config = WebhookConfig.from_dict(data)

            # Update config
            if command_service.update_webhook_config(config):
                return jsonify({
                    'success': True,
                    'message': 'Webhook configuration updated successfully',
                    'config': config.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update webhook configuration'}), 500

        except Exception as e:
            logger.error(f"Error updating webhook config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/debug-config', methods=['GET'])
    @login_required
    def get_debug_config():
        """Get debug configuration"""
        try:
            config = command_service.get_debug_config()
            return jsonify({
                'success': True,
                'config': config.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting debug config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/debug-config', methods=['PUT'])
    @login_required
    def update_debug_config():
        """Update debug configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Create config object
            config = DebugConfig.from_dict(data)

            # Update config
            if command_service.update_debug_config(config):
                return jsonify({
                    'success': True,
                    'message': 'Debug configuration updated successfully',
                    'config': config.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update debug configuration'}), 500

        except Exception as e:
            logger.error(f"Error updating debug config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/command-config', methods=['GET'])
    @login_required
    def get_command_config():
        """Get command configuration"""
        try:
            config = command_service.get_command_config()
            return jsonify({
                'success': True,
                'config': config.to_dict()
            })
        except Exception as e:
            logger.error(f"Error getting command config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/command-config', methods=['PUT'])
    @login_required
    def update_command_config():
        """Update command configuration"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            # Create config object
            config = CommandConfig.from_dict(data)

            # Update config
            if command_service.update_command_config(config):
                return jsonify({
                    'success': True,
                    'message': 'Command configuration updated successfully',
                    'config': config.to_dict()
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to update command configuration'}), 500

        except Exception as e:
            logger.error(f"Error updating command config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/webhook/status', methods=['GET'])
    @login_required
    def get_webhook_status():
        """Get webhook status and connectivity"""
        try:
            status = webhook_manager.get_webhook_status()
            return jsonify({
                'success': True,
                'status': status
            })
        except Exception as e:
            logger.error(f"Error getting webhook status: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/webhook/register', methods=['POST'])
    @login_required
    def register_webhook():
        """Register webhook with ShopeeAPI"""
        try:
            result = webhook_manager.register_webhook()
            if result['status'] == 'success':
                return jsonify({
                    'success': True,
                    'message': result['message'],
                    'webhook_url': result.get('webhook_url')
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['message'],
                    'details': result.get('details')
                }), 400
        except Exception as e:
            logger.error(f"Error registering webhook: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/webhook/unregister', methods=['POST'])
    @login_required
    def unregister_webhook():
        """Unregister webhook from ShopeeAPI"""
        try:
            result = webhook_manager.unregister_webhook()
            if result['status'] == 'success':
                return jsonify({
                    'success': True,
                    'message': result['message']
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['message'],
                    'details': result.get('details')
                }), 400
        except Exception as e:
            logger.error(f"Error unregistering webhook: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/webhook/test', methods=['POST'])
    @login_required
    def test_webhook():
        """Test webhook connectivity and functionality"""
        try:
            # Test ShopeeAPI connectivity
            shopee_test = webhook_manager.test_shopee_api_connectivity()

            # Test webhook endpoint
            endpoint_test = webhook_manager.test_webhook_endpoint()

            return jsonify({
                'success': True,
                'tests': {
                    'shopee_api': shopee_test,
                    'webhook_endpoint': endpoint_test
                }
            })
        except Exception as e:
            logger.error(f"Error testing webhook: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/webhook', methods=['POST'])
    # This route is called by an external service (ShopeeAPI), so it should NOT require login.
    # @login_required
    def handle_webhook():
        """Handle incoming webhook from ShopeeAPI"""
        try:
            data = request.get_json()
            if not data:
                logger.error("No data provided in webhook request")
                return jsonify({'success': False, 'error': 'No data provided'}), 400
            
            # Use debug config to control logging
            if command_service.is_debug_enabled('webhook_data'):
                logger.info(f"Received webhook: {data}")
            
            # Add detailed logging for debugging
            webhook_type = data.get('type', 'unknown')
            if command_service.is_debug_enabled('webhook_data'):
                logger.info(f"Webhook type: {webhook_type}")
            
            if webhook_type == 'shopee_message':
                message_data = data.get('data', {})
                message_type = message_data.get('message_type', 'unknown')
                if command_service.is_debug_enabled('webhook_data'):
                    logger.info(f"Message type: {message_type}")
                
                # Try to parse message content for debugging
                message_content = message_data.get('message_content', '')
                if message_content and command_service.is_debug_enabled('webhook_data'):
                    try:
                        import json as json_lib
                        parsed = json_lib.loads(message_content) if isinstance(message_content, str) else message_content
                        content_type = parsed.get('type', 'unknown')
                        logger.info(f"Message content type: {content_type}")
                        
                        if content_type == 'chat_update_msg_status':
                            logger.info("This is a status update, not a message - no response needed")
                        elif 'text' in str(parsed):
                            logger.info("This appears to contain text content")
                        else:
                            logger.info("No text content found in message")
                            
                    except Exception as parse_error:
                        logger.error(f"Error parsing message content: {parse_error}")
            
            # Process the message
            responses = message_processor.process_webhook_message(data)
            
            if responses:
                if command_service.is_debug_enabled('response_generation'):
                    logger.info(f"Generated {len(responses)} responses")
                # Send responses back to Shopee
                success_count = 0
                sender_name = None
                
                for response in responses:
                    if message_processor.shopee_api_client:
                        try:
                            # Extract sender info from webhook data (only once)
                            if sender_name is None:
                                # Try different paths to find sender name
                                if webhook_type == 'shopee_message':
                                    # Parse the message content to get sender info
                                    message_data = data.get('data', {})
                                    message_content = message_data.get('message_content', '')
                                    if isinstance(message_content, str):
                                        try:
                                            import json as json_lib
                                            parsed_content = json_lib.loads(message_content)

                                            # Determine the correct sender name based on message direction
                                            send_by_yourself = parsed_content.get('send_by_yourself', False)

                                            if send_by_yourself:
                                                # This is a message we sent, so the customer is the recipient (to_user_name)
                                                sender_name = parsed_content.get('to_user_name', '')
                                                if command_service.is_debug_enabled('response_generation'):
                                                    logger.info(f"MESSAGE_SENT detected: using to_user_name '{sender_name}' as customer")
                                            else:
                                                # This is a message we received, so the customer is the sender (from_user_name)
                                                sender_name = parsed_content.get('from_user_name', '')
                                                if command_service.is_debug_enabled('response_generation'):
                                                    logger.info(f"MESSAGE_RECEIVED detected: using from_user_name '{sender_name}' as customer")

                                            # Fallback to from_name if no username found
                                            if not sender_name:
                                                sender_name = parsed_content.get('from_name', '')

                                        except json.JSONDecodeError:
                                            if command_service.is_debug_enabled('response_generation'):
                                                logger.warning("Failed to parse message_content for sender info")

                                # Fallback to legacy format
                                if not sender_name:
                                    message = data.get('message', {})
                                    # For legacy format, also check send_by_yourself
                                    send_by_yourself = message.get('send_by_yourself', False)
                                    if send_by_yourself:
                                        sender_name = message.get('to_user_name', '') or message.get('to_name', '')
                                    else:
                                        sender_name = message.get('from_user_name', '') or message.get('from_name', '')
                                
                                # Final fallback
                                if not sender_name:
                                    sender_name = 'unknown_user'
                                    if command_service.is_debug_enabled('response_generation'):
                                        logger.warning("Could not determine sender name, using fallback")
                            
                            # Get command configuration to check send order
                            command_config = command_service.get_command_config()
                            send_images_first = command_config.send_images_first if command_config else False

                            if command_service.is_debug_enabled('response_generation'):
                                order_msg = "images first, then text" if send_images_first else "text first, then images"
                                logger.info(f"Sending response to {sender_name} ({order_msg})")

                            # Define functions for sending text and images
                            def send_text():
                                if response.text:
                                    payload = {
                                        'text': response.text,
                                        'username': sender_name
                                    }
                                    result = message_processor.shopee_api_client.send_chat_message(payload)
                                    if result[1] == 200:
                                        nonlocal success_count
                                        success_count += 1
                                        if command_service.is_debug_enabled('response_generation'):
                                            logger.info(f"Successfully sent text response to {sender_name}")
                                    else:
                                        logger.error(f"Failed to send text response: {result[1]}")

                            def send_images():
                                for image_url in response.image_urls:
                                    payload = {
                                        'username': sender_name,
                                        'image_url': image_url
                                    }
                                    result = message_processor.shopee_api_client.send_image_message(payload)
                                    if result[1] == 200:
                                        nonlocal success_count
                                        success_count += 1
                                        if command_service.is_debug_enabled('response_generation'):
                                            logger.info(f"Successfully sent image response to {sender_name}")
                                    else:
                                        logger.error(f"Failed to send image response: {result[1]}")

                            # Send in configured order
                            if send_images_first:
                                send_images()
                                send_text()
                            else:
                                send_text()
                                send_images()
                                    
                        except Exception as e:
                            logger.error(f"Error sending response: {e}")
                            import traceback
                            logger.error(f"Response sending error details: {traceback.format_exc()}")
                
                # Mark conversation as unread AFTER successfully sending all responses
                # Only mark as unread if at least one response has mark_as_unread=True
                should_mark_unread = False
                if success_count > 0 and sender_name and sender_name != 'unknown_user':
                    # Check if any of the responses should mark the conversation as unread
                    for response in responses:
                        if hasattr(response, 'mark_as_unread') and response.mark_as_unread:
                            should_mark_unread = True
                            break

                    if should_mark_unread:
                        try:
                            if command_service.is_debug_enabled('response_generation'):
                                logger.info(f"Marking conversation with {sender_name} as unread after sending {success_count} responses")

                            result = message_processor.shopee_api_client.set_conversation_unread_by_username(sender_name)
                            if result[1] == 200:
                                if command_service.is_debug_enabled('response_generation'):
                                    logger.info(f"Successfully marked conversation with {sender_name} as unread")
                            else:
                                logger.warning(f"Failed to mark conversation as unread for {sender_name}: HTTP {result[1]} - {result[0]}")
                        except Exception as e:
                            logger.error(f"Error marking conversation as unread for {sender_name}: {e}")
                            import traceback
                            logger.error(f"Unread marking error details: {traceback.format_exc()}")
                    else:
                        if command_service.is_debug_enabled('response_generation'):
                            logger.info(f"Not marking conversation with {sender_name} as unread - no responses requested it")
                
                return jsonify({
                    'success': True,
                    'message': f'Processed webhook and sent {success_count} responses'
                })
            else:
                if command_service.is_debug_enabled('webhook_data'):
                    logger.info("No responses generated - webhook processed successfully")
                return jsonify({
                    'success': True,
                    'message': 'Webhook processed, no responses needed'
                })
                
        except Exception as e:
            logger.error(f"Error handling webhook: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @bp.route('/api/test-command', methods=['POST'])
    @login_required
    def test_command():
        """Test a command with sample data"""
        try:
            data = request.get_json()
            if not data or 'command' not in data:
                return jsonify({'success': False, 'error': 'No data or command provided'}), 400

            command_name = data['command']

            # Use a mock message for testing
            mock_message = {
                'type': 'shopee_message',
                'data': {
                    'message_type': 'text',
                    'message_content': json.dumps({'text': f'#{command_name}'})
                }
            }

            responses = message_processor.process_webhook_message(mock_message)

            return jsonify({
                'success': True,
                'responses': [resp.to_dict() for resp in responses] if responses else []
            })

        except Exception as e:
            logger.error(f"Error testing command {data.get('command')}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @bp.route('/api/reload-commands', methods=['POST'])
    @login_required
    def reload_commands():
        """Reload commands from JSON file"""
        try:
            success = command_service.reload_commands()
            if success:
                commands = command_service.get_all_commands()
                return jsonify({
                    'success': True,
                    'message': f'Successfully reloaded {len(commands)} commands',
                    'commands_count': len(commands)
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to reload commands'}), 500

        except Exception as e:
            logger.error(f"Error reloading commands: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    return bp
