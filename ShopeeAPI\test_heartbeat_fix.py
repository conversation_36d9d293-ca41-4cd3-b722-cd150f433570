#!/usr/bin/env python3
"""
Simple test to verify the WebSocket heartbeat fix.
"""

import asyncio
import logging
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_socketio_import():
    """Test if Socket.IO can be imported and initialized."""
    try:
        import socketio
        logger.info("✅ Socket.IO import successful")
        
        # Test creating a client
        client = socketio.AsyncClient(
            logger=False,
            engineio_logger=False
        )
        logger.info("✅ Socket.IO client creation successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Socket.IO test failed: {e}")
        return False

async def test_websocket_service():
    """Test if WebSocket service can be imported and initialized."""
    try:
        from services.websocket import WebSocketService
        from core.session import ShopeeSession
        from core.config import ShopeeConfig
        from services.chat import ChatService
        
        logger.info("✅ All imports successful")
        
        # Test creating services
        config = ShopeeConfig()
        session = ShopeeSession(config)
        chat_service = ChatService(session, config)
        websocket_service = WebSocketService(session, config, chat_service)
        
        logger.info("✅ WebSocket service creation successful")
        logger.info(f"✅ Shopee heartbeat task attribute exists: {hasattr(websocket_service, 'shopee_heartbeat_task')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ WebSocket service test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Main test function."""
    logger.info("🧪 Testing WebSocket heartbeat fix...")
    logger.info("=" * 50)
    
    tests = [
        ("Socket.IO Import", test_socketio_import),
        ("WebSocket Service", test_websocket_service)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! WebSocket heartbeat fix is ready.")
        logger.info("\nKey improvements:")
        logger.info("  • Socket.IO client initialization fixed")
        logger.info("  • Shopee heartbeat task ready")
        logger.info("  • No more parameter errors")
    else:
        logger.error("⚠️ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Test crashed: {e}")
        sys.exit(1)
