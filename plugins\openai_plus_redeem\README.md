# OpenAI Plus Redeem Plugin

Automated ChatGPT Plus account redemption system that integrates with Shopee orders to provide seamless account provisioning and management for customers.

## 🚀 Features

- **Automated Order Processing**: Seamlessly processes Shopee orders for ChatGPT Plus subscriptions
- **Account Management**: Comprehensive ChatGPT account lifecycle management with status tracking
- **Email Verification**: Automated email verification code retrieval via Gmail IMAP integration
- **Cooldown Management**: Time-based access control to prevent abuse and manage account usage
- **Admin Interface**: Web-based administration panel for account and order management
- **Customer Portal**: User-friendly interface for customers to check redemption status
- **Shopee Integration**: Direct integration with existing Shopee API services for order validation
- **Backup & Recovery**: Automated data backup and recovery mechanisms
- **Rate Limiting**: Built-in rate limiting and abuse prevention
- **Comprehensive Logging**: Detailed logging for monitoring and troubleshooting

## 📋 Requirements

- Python 3.8+
- Required dependencies:
  - Flask>=2.0.0
  - requests>=2.25.0
  - dataclasses-json>=0.5.0
  - python-dateutil>=2.8.0
- Optional dependencies:
  - psutil>=5.8.0 (for performance monitoring)
  - pytest>=6.0.0 (for testing)

## ⚙️ Installation

### Automatic Installation
1. Copy plugin to `plugins/openai_plus_redeem/` directory
2. Update `configs/core/plugin_config.json`:
   ```json
   {
     "openai_plus_redeem": {
       "enabled": true,
       "email_config": {
         "imap_server": "imap.gmail.com",
         "imap_port": 993,
         "use_ssl": true,
         "require_verification": true,
         "global_credentials": {
           "email": "<EMAIL>",
           "password": "your-app-password"
         }
       },
       "cooldown_config": {
         "default_cooldown_hours": 24,
         "max_reset_attempts": 3,
         "enable_cooldown_management": true
       },
       "security_config": {
         "max_redemptions_per_user": 5,
         "enable_abuse_prevention": true,
         "rate_limit_requests_per_minute": 10
       }
     }
   }
   ```
3. Restart the application

### Manual Installation
1. Ensure all dependencies are installed
2. Configure Gmail app password for email verification
3. Set up data directory permissions
4. Initialize plugin through admin interface

## 🔧 Configuration

### Basic Configuration
```json
{
  "enabled": true,
  "debug": false,
  "email_config": {
    "imap_server": "imap.gmail.com",
    "imap_port": 993,
    "use_ssl": true,
    "require_verification": true,
    "global_credentials": {
      "email": "<EMAIL>",
      "password": "your-app-password"
    }
  },
  "cooldown_config": {
    "default_cooldown_hours": 24,
    "max_reset_attempts": 3,
    "enable_cooldown_management": true
  }
}
```

### Advanced Configuration
See [CONFIGURATION.md](CONFIGURATION.md) for detailed configuration options.

## 🌐 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/openai-plus-redeem/health` | Health check |
| GET | `/api/openai-plus-redeem/accounts` | List ChatGPT accounts (Admin) |
| POST | `/api/openai-plus-redeem/accounts` | Add new account (Admin) |
| GET | `/api/openai-plus-redeem/redemptions` | List redemptions (Admin) |
| POST | `/api/openai-plus-redeem/redeem` | Process order redemption |
| GET | `/api/openai-plus-redeem/status/{order_id}` | Check redemption status |
| POST | `/api/openai-plus-redeem/cooldown/reset` | Reset user cooldown (Admin) |

See [API.md](API.md) for detailed API documentation.

## 📖 Usage Examples

### Basic Usage - Customer Redemption
```python
import requests

# Customer checks redemption status
response = requests.get("http://localhost:5000/api/openai-plus-redeem/status/ORDER_123")
status = response.json()
print(f"Redemption status: {status['status']}")
```

### Admin Usage - Account Management
```python
import requests

# Admin adds new ChatGPT account
account_data = {
    "account_id": "CHATGPT_001",
    "email": "<EMAIL>",
    "password": "secure_password",
    "max_concurrent_users": 5,
    "expiration_date": "2024-12-31T23:59:59"
}

response = requests.post(
    "http://localhost:5000/api/openai-plus-redeem/accounts",
    json=account_data,
    headers={"Authorization": "Bearer admin_token"}
)
```

### Web Interface Usage
1. **Customer Portal**: Navigate to `/openai-plus-redeem/` for redemption status
2. **Admin Panel**: Navigate to `/admin/openai-plus-redeem/` for management interface

## 🧪 Testing

Run tests for this plugin:
```bash
pytest plugins/openai_plus_redeem/tests/
```

Run with coverage:
```bash
pytest --cov=plugins/openai_plus_redeem plugins/openai_plus_redeem/tests/
```

Run performance tests:
```bash
python plugins/openai_plus_redeem/run_performance_tests.py
```

Run security tests:
```bash
python plugins/openai_plus_redeem/run_security_tests.py
```

## 🐛 Troubleshooting

### Common Issues

**Issue**: Plugin fails to initialize
- **Cause**: Missing or invalid email configuration
- **Solution**: Check Gmail credentials and app password setup

**Issue**: Email verification fails
- **Cause**: IMAP connection issues or incorrect credentials
- **Solution**: Verify Gmail IMAP settings and app password

**Issue**: Order redemption fails
- **Cause**: Shopee API integration issues
- **Solution**: Check Shopee API service status and configuration

**Issue**: Account allocation fails
- **Cause**: No available accounts or all accounts at capacity
- **Solution**: Add more ChatGPT accounts or check account status

See [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for more issues and solutions.

## 📊 Architecture Overview

### Service Layer
- **ServiceManager**: Central service coordination and lifecycle management
- **ChatGPTAccountService**: Account management and allocation
- **OrderRedemptionService**: Order processing and workflow orchestration
- **EmailService**: Gmail IMAP integration for verification
- **CooldownService**: Time-based access control

### Data Models
- **ChatGPTAccount**: Account information and status tracking
- **OrderRedemption**: Redemption workflow state management
- **EmailVerification**: Email verification attempt logging
- **AccountCooldown**: User cooldown state management

### Web Interface
- **Customer Portal**: Responsive web interface for status checking
- **Admin Panel**: Comprehensive management interface
- **API Routes**: RESTful API endpoints with authentication

## 🔒 Security Features

- **Rate Limiting**: IP-based rate limiting for customer endpoints
- **Input Validation**: Comprehensive input sanitization and validation
- **Authentication**: Admin route protection with token-based auth
- **Data Encryption**: Sensitive data masking in logs and responses
- **Abuse Prevention**: Cooldown management and redemption limits
- **Audit Logging**: Comprehensive audit trail for all operations

## 📝 Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and changes.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following the plugin development standards
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## 📄 License

This plugin is part of the SteamCodeTool project and follows the same license.

## 📞 Support

- Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
- Review the [Configuration Guide](CONFIGURATION.md)
- Check the [API Documentation](API.md)
- Contact the development team for additional support
