{% extends "base.html" %}

{% block title %}VPN Redemption Links - Admin{% endblock %}

{% block header %}VPN Redemption Links{% endblock %}

{% block extra_head %}
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    * { font-family: 'Inter', sans-serif; }
    .crypto-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 20px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .crypto-button {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        border: none;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        padding: 10px 20px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .crypto-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }
    .crypto-button.secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
    }
    .crypto-button.danger {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }
    .crypto-input {
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 10px 15px;
        transition: all 0.2s ease;
    }
    .crypto-input:focus {
        border-color: #3b82f6;
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    .pagination-button {
        @apply px-3 py-2 mx-1 text-sm rounded-md transition-colors duration-200;
    }
    .pagination-button.active {
        @apply bg-blue-600 text-white;
    }
    .pagination-button:not(.active) {
        @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50;
    }
    .pagination-button:disabled {
        @apply opacity-50 cursor-not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Base URL Configuration -->
    <div class="crypto-card p-6">
        <h2 class="text-xl font-semibold mb-4">Base URL Configuration</h2>
        <p class="text-gray-600 text-sm mb-4">Configure the base URL for redemption links. This will be used when generating redemption URLs.</p>
        <div class="flex gap-4 items-end">
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700 mb-2">Base URL</label>
                <input type="url" id="baseUrlInput" class="crypto-input w-full" placeholder="https://yourdomain.com" 
                       value="http://localhost:5000">
            </div>
            <div>
                <button onclick="saveBaseUrl()" class="crypto-button">
                    <span id="saveUrlButtonText">Save URL</span>
                    <span id="saveUrlButtonSpinner" class="hidden">Saving...</span>
                </button>
            </div>
            <div>
                <button onclick="loadBaseUrl()" class="crypto-button secondary">Reload</button>
            </div>
        </div>
        <div class="mt-3">
            <p class="text-sm text-gray-500">
                <strong>Example:</strong> If you set the base URL to <code>https://yourdomain.com</code>, 
                redemption links will be generated as <code>https://yourdomain.com/vpn-config-generator/redeem/[link-id]</code>
            </p>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="crypto-card p-6">
        <h2 class="text-xl font-semibold mb-4">Search & Filters</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" id="searchInput" class="crypto-input w-full" placeholder="Username, ID, or notes...">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="statusFilter" class="crypto-input w-full">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Telco</label>
                <select id="telcoFilter" class="crypto-input w-full">
                    <option value="">All Telcos</option>
                    <option value="digi">Digi</option>
                    <option value="maxis">Maxis</option>
                    <option value="celcom">Celcom</option>
                    <option value="umobile">U Mobile</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Plan</label>
                <select id="planFilter" class="crypto-input w-full">
                    <option value="">All Plans</option>
                    <option value="unlimited">Unlimited</option>
                    <option value="basic">Basic</option>
                    <option value="premium">Premium</option>
                    <option value="booster">Booster</option>
                </select>
            </div>
        </div>
        <div class="mt-4 flex flex-wrap gap-2">
            <button onclick="applyFilters()" class="crypto-button">Apply Filters</button>
            <button onclick="clearFilters()" class="crypto-button secondary">Clear Filters</button>
            <button onclick="loadRedemptionLinks()" class="crypto-button secondary">Refresh</button>
        </div>
    </div>

        <!-- Create New Link Section -->
        <div class="crypto-card p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Create New Redemption Link</h2>
            <form id="createLinkForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Customer Username *</label>
                    <input type="text" id="customerUsername" class="crypto-input w-full" placeholder="shopee_username" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Validity Days *</label>
                    <input type="number" id="validityDays" class="crypto-input w-full" value="30" min="1" max="365" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Server ID</label>
                    <input type="text" id="serverId" class="crypto-input w-full" placeholder="auto or specific server ID">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Telco</label>
                    <select id="telco" class="crypto-input w-full">
                        <option value="">Select Telco (optional)</option>
                        <option value="digi">Digi</option>
                        <option value="maxis">Maxis</option>
                        <option value="celcom">Celcom</option>
                        <option value="umobile">U Mobile</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Plan</label>
                    <select id="plan" class="crypto-input w-full">
                        <option value="">Select Plan (optional)</option>
                        <option value="unlimited">Unlimited</option>
                        <option value="basic">Basic</option>
                        <option value="premium">Premium</option>
                        <option value="booster">Booster</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Variable SKU</label>
                    <input type="text" id="varSku" class="crypto-input w-full" placeholder="e.g., VPN_30D">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Link Expiry (optional)</label>
                    <input type="datetime-local" id="expiresAt" class="crypto-input w-full">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Uses</label>
                    <input type="number" id="maxUses" class="crypto-input w-full" value="1" min="1" max="100">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" class="crypto-input w-full" rows="3" placeholder="Optional notes about this redemption link"></textarea>
                </div>
                <div class="md:col-span-2">
                    <button type="submit" class="crypto-button">
                        <span id="createButtonText">Create Redemption Link</span>
                        <span id="createButtonSpinner" class="hidden">Creating...</span>
                    </button>
                </div>
            </form>
        </div>

    <!-- Existing Links Section -->
    <div class="crypto-card p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">Existing Redemption Links</h2>
            <div class="flex items-center gap-2">
                <span class="text-sm text-gray-600" id="resultsInfo"></span>
                <div>
                    <label for="itemsPerPage" class="text-sm text-gray-600 mr-2">Items per page:</label>
                    <select id="itemsPerPage" class="crypto-input py-1 px-2 text-sm" onchange="changeItemsPerPage()">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div id="linksContainer">
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-2 text-gray-600">Loading redemption links...</p>
            </div>
        </div>

        <!-- Pagination -->
        <div id="paginationContainer" class="mt-6 flex justify-center items-center space-x-2" style="display: none;">
            <button id="prevBtn" class="pagination-button" onclick="prevPage()">
                <i class="fas fa-chevron-left"></i> Previous
            </button>
            <div id="pageNumbers" class="flex space-x-1"></div>
            <button id="nextBtn" class="pagination-button" onclick="nextPage()">
                Next <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer" class="fixed top-4 right-4 z-50"></div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Global variables for pagination and filtering
    let allRedemptionLinks = [];
    let filteredLinks = [];
    let currentPage = 1;
    let itemsPerPage = 25;
    let currentBaseUrl = 'http://localhost:5000'; // Default fallback

    // Load redemption links on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadBaseUrl();
        loadRedemptionLinks();
        
        // Add search input listener
        document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
    });

    // Load base URL from config
    async function loadBaseUrl() {
        try {
            const response = await axios.get('/vpn-config-generator/api/config/base-url');
            if (response.data.success) {
                currentBaseUrl = response.data.base_url;
                document.getElementById('baseUrlInput').value = currentBaseUrl;
            }
        } catch (error) {
            console.warn('Failed to load base URL from config, using current origin');
            currentBaseUrl = window.location.origin;
            document.getElementById('baseUrlInput').value = currentBaseUrl;
        }
    }

    // Save base URL to config
    async function saveBaseUrl() {
        const button = document.getElementById('saveUrlButtonText');
        const spinner = document.getElementById('saveUrlButtonSpinner');
        const urlInput = document.getElementById('baseUrlInput');
        
        const newBaseUrl = urlInput.value.trim();
        if (!newBaseUrl) {
            showMessage('Please enter a valid base URL', 'error');
            return;
        }

        // Validate URL format
        try {
            new URL(newBaseUrl);
        } catch (e) {
            showMessage('Please enter a valid URL (e.g., https://yourdomain.com)', 'error');
            return;
        }

        button.classList.add('hidden');
        spinner.classList.remove('hidden');
        
        try {
            const response = await axios.post('/vpn-config-generator/api/config/base-url', {
                base_url: newBaseUrl
            });
            
            if (response.data.success) {
                currentBaseUrl = newBaseUrl;
                showMessage('Base URL saved successfully!', 'success');
                // Refresh the redemption links display to show new URLs
                displayRedemptionLinks();
            } else {
                showMessage('Error: ' + response.data.error, 'error');
            }
        } catch (error) {
            showMessage('Error saving base URL: ' + (error.response?.data?.error || error.message), 'error');
        } finally {
            button.classList.remove('hidden');
            spinner.classList.add('hidden');
        }
    }

    // Debounce function for search input
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Create new redemption link
    document.getElementById('createLinkForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const button = document.getElementById('createButtonText');
        const spinner = document.getElementById('createButtonSpinner');
        
        button.classList.add('hidden');
        spinner.classList.remove('hidden');
        
        try {
            const formData = {
                customer_username: document.getElementById('customerUsername').value,
                validity_days: parseInt(document.getElementById('validityDays').value),
                server_id: document.getElementById('serverId').value || null,
                telco: document.getElementById('telco').value || null,
                plan: document.getElementById('plan').value || null,
                var_sku: document.getElementById('varSku').value || null,
                expires_at: document.getElementById('expiresAt').value || null,
                max_uses: parseInt(document.getElementById('maxUses').value),
                notes: document.getElementById('notes').value || null,
                created_by: 'admin'
            };

            const response = await axios.post('/vpn-config-generator/api/redemption-links', formData);
            
            if (response.data.success) {
                showMessage('Redemption link created successfully!', 'success');
                document.getElementById('createLinkForm').reset();
                document.getElementById('validityDays').value = '30';
                document.getElementById('maxUses').value = '1';
                loadRedemptionLinks();
            } else {
                showMessage('Error: ' + response.data.error, 'error');
            }
        } catch (error) {
            showMessage('Error creating redemption link: ' + (error.response?.data?.error || error.message), 'error');
        } finally {
            button.classList.remove('hidden');
            spinner.classList.add('hidden');
        }
    });

    // Load and display redemption links
    async function loadRedemptionLinks() {
        try {
            const response = await axios.get('/vpn-config-generator/api/redemption-links');
            
            if (response.data.success) {
                allRedemptionLinks = Object.values(response.data.links);
                // Sort by creation date (newest first)
                allRedemptionLinks.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                applyFilters();
            } else {
                document.getElementById('linksContainer').innerHTML = 
                    '<div class="text-center py-8 text-red-600">Error loading redemption links</div>';
            }
        } catch (error) {
            document.getElementById('linksContainer').innerHTML = 
                '<div class="text-center py-8 text-red-600">Error loading redemption links</div>';
        }
    }

    // Apply filters and search
    function applyFilters() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const telcoFilter = document.getElementById('telcoFilter').value;
        const planFilter = document.getElementById('planFilter').value;

        filteredLinks = allRedemptionLinks.filter(link => {
            // Search filter
            const matchesSearch = !searchTerm || 
                link.customer_username.toLowerCase().includes(searchTerm) ||
                link.id.toLowerCase().includes(searchTerm) ||
                (link.notes && link.notes.toLowerCase().includes(searchTerm));

            // Status filter
            const matchesStatus = !statusFilter || 
                (statusFilter === 'active' && link.is_active) ||
                (statusFilter === 'inactive' && !link.is_active);

            // Telco filter
            const matchesTelco = !telcoFilter || link.telco === telcoFilter;

            // Plan filter
            const matchesPlan = !planFilter || link.plan === planFilter;

            return matchesSearch && matchesStatus && matchesTelco && matchesPlan;
        });

        currentPage = 1;
        displayRedemptionLinks();
        updatePagination();
    }

    // Clear all filters
    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('telcoFilter').value = '';
        document.getElementById('planFilter').value = '';
        applyFilters();
    }

    // Change items per page
    function changeItemsPerPage() {
        itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
        currentPage = 1;
        displayRedemptionLinks();
        updatePagination();
    }

    // Display redemption links with pagination
    function displayRedemptionLinks() {
        const container = document.getElementById('linksContainer');
        
        if (filteredLinks.length === 0) {
            container.innerHTML = '<div class="text-center py-8 text-gray-500">No redemption links found</div>';
            document.getElementById('paginationContainer').style.display = 'none';
            document.getElementById('resultsInfo').textContent = '';
            return;
        }

        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageLinks = filteredLinks.slice(startIndex, endIndex);

        // Update results info
        const totalItems = filteredLinks.length;
        const startItem = startIndex + 1;
        const endItem = Math.min(endIndex, totalItems);
        document.getElementById('resultsInfo').textContent = `Showing ${startItem}-${endItem} of ${totalItems} results`;

        container.innerHTML = pageLinks.map(link => `
            <div class="border rounded-lg p-4 mb-4 ${link.is_active ? 'bg-white' : 'bg-gray-50'}">
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <h3 class="font-semibold text-lg">${link.customer_username}</h3>
                        <p class="text-sm text-gray-600">ID: ${link.id}</p>
                    </div>
                    <div class="flex space-x-2">
                        <span class="px-2 py-1 rounded text-xs ${link.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${link.is_active ? 'Active' : 'Inactive'}
                        </span>
                        <span class="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                            ${link.current_uses}/${link.max_uses} uses
                        </span>
                    </div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm mb-3">
                    <div><strong>Validity:</strong> ${link.validity_days} days</div>
                    <div><strong>Server:</strong> ${link.server_id || 'auto'}</div>
                    <div><strong>Telco:</strong> ${link.telco || 'any'}</div>
                    <div><strong>Plan:</strong> ${link.plan || 'any'}</div>
                </div>
                <div class="text-sm text-gray-600 mb-3">
                    <div><strong>Created:</strong> ${new Date(link.created_at).toLocaleString()}</div>
                    ${link.expires_at ? `<div><strong>Expires:</strong> ${new Date(link.expires_at).toLocaleString()}</div>` : ''}
                    ${link.used_at ? `<div><strong>Used:</strong> ${new Date(link.used_at).toLocaleString()}</div>` : ''}
                </div>
                <div class="flex justify-between items-center">
                    <div class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                        ${currentBaseUrl}/vpn-config-generator/redeem/${link.id}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="copyRedemptionUrl('${link.id}')" class="crypto-button secondary text-xs px-3 py-1">Copy URL</button>
                        ${link.is_active ? `<button onclick="sendViaChat('${link.id}')" class="crypto-button text-xs px-3 py-1" style="background: linear-gradient(135deg, #10b981, #059669);">Send via Chat</button>` : ''}
                        ${link.is_active ? `<button onclick="deactivateLink('${link.id}')" class="crypto-button danger text-xs px-3 py-1">Deactivate</button>` : ''}
                        <button onclick="deleteLink('${link.id}')" class="crypto-button danger text-xs px-3 py-1">Delete</button>
                    </div>
                </div>
                ${link.notes ? `<div class="mt-2 text-sm text-gray-600"><strong>Notes:</strong> ${link.notes}</div>` : ''}
            </div>
        `).join('');

        // Show pagination if needed
        document.getElementById('paginationContainer').style.display = totalItems > itemsPerPage ? 'flex' : 'none';
    }

    // Update pagination controls
    function updatePagination() {
        const totalPages = Math.ceil(filteredLinks.length / itemsPerPage);
        
        if (totalPages <= 1) {
            document.getElementById('paginationContainer').style.display = 'none';
            return;
        }

        // Update prev/next buttons
        document.getElementById('prevBtn').disabled = currentPage === 1;
        document.getElementById('nextBtn').disabled = currentPage === totalPages;

        // Generate page numbers
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // Add first page and ellipsis if needed
        if (startPage > 1) {
            pageNumbers.appendChild(createPageButton(1, 1));
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'px-2 py-2 text-gray-500';
                pageNumbers.appendChild(ellipsis);
            }
        }

        // Add visible page numbers
        for (let i = startPage; i <= endPage; i++) {
            pageNumbers.appendChild(createPageButton(i, i));
        }

        // Add last page and ellipsis if needed
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'px-2 py-2 text-gray-500';
                pageNumbers.appendChild(ellipsis);
            }
            pageNumbers.appendChild(createPageButton(totalPages, totalPages));
        }
    }

    // Create page button
    function createPageButton(pageNum, displayNum) {
        const button = document.createElement('button');
        button.textContent = displayNum;
        button.className = `pagination-button ${currentPage === pageNum ? 'active' : ''}`;
        button.onclick = () => goToPage(pageNum);
        return button;
    }

    // Navigate to specific page
    function goToPage(page) {
        currentPage = page;
        displayRedemptionLinks();
        updatePagination();
    }

    // Previous page
    function prevPage() {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    }

    // Next page
    function nextPage() {
        const totalPages = Math.ceil(filteredLinks.length / itemsPerPage);
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    }

    // Copy redemption URL to clipboard
    function copyRedemptionUrl(linkId) {
        const url = `${currentBaseUrl}/vpn-config-generator/redeem/${linkId}`;
        navigator.clipboard.writeText(url).then(() => {
            showMessage('Redemption URL copied to clipboard!', 'success');
        }).catch(() => {
            showMessage('Failed to copy URL to clipboard', 'error');
        });
    }

    // Send redemption link via chat
    async function sendViaChat(linkId) {
        if (!confirm('Send this redemption link to the customer via chat?')) return;

        try {
            const response = await axios.post(`/vpn-config-generator/api/redemption-links/${linkId}/send-chat`, {
                base_url: currentBaseUrl
            });

            if (response.data.success) {
                showMessage('Redemption link sent via chat successfully!', 'success');
            } else {
                showMessage('Error: ' + response.data.error, 'error');
            }
        } catch (error) {
            showMessage('Error sending via chat: ' + (error.response?.data?.error || error.message), 'error');
        }
    }

    // Deactivate redemption link
    async function deactivateLink(linkId) {
        if (!confirm('Are you sure you want to deactivate this redemption link?')) return;
        
        try {
            const response = await axios.post(`/vpn-config-generator/api/redemption-links/${linkId}/deactivate`);
            
            if (response.data.success) {
                showMessage('Redemption link deactivated successfully!', 'success');
                loadRedemptionLinks();
            } else {
                showMessage('Error: ' + response.data.error, 'error');
            }
        } catch (error) {
            showMessage('Error deactivating redemption link: ' + (error.response?.data?.error || error.message), 'error');
        }
    }

    // Delete redemption link
    async function deleteLink(linkId) {
        if (!confirm('Are you sure you want to delete this redemption link? This action cannot be undone.')) return;
        
        try {
            const response = await axios.delete(`/vpn-config-generator/api/redemption-links/${linkId}`);
            
            if (response.data.success) {
                showMessage('Redemption link deleted successfully!', 'success');
                loadRedemptionLinks();
            } else {
                showMessage('Error: ' + response.data.error, 'error');
            }
        } catch (error) {
            showMessage('Error deleting redemption link: ' + (error.response?.data?.error || error.message), 'error');
        }
    }

    // Show success/error messages
    function showMessage(message, type) {
        const container = document.getElementById('messageContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = `p-4 rounded-lg mb-2 ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
        messageDiv.textContent = message;
        
        container.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }
</script>
{% endblock %}
