{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <form method="post" action="{{ url_for('vpn.sync_all_clients') }}" style="display: inline;">
                            <button type="submit" class="btn btn-primary btn-sm" onclick="return confirm('Sync all servers? This may take a while.')">
                                <i class="fas fa-sync"></i> Sync All Servers
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    {% if servers %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Server</th>
                                        <th>Status</th>
                                        <th>Config Only</th>
                                        <th>Database Only</th>
                                        <th>Mismatched</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for server in servers %}
                                    {% set status = sync_status.get(server.id, {}) %}
                                    <tr>
                                        <td>
                                            <strong>{{ server.name }}</strong><br>
                                            <small class="text-muted">{{ server.host }}</small>
                                        </td>
                                        <td>
                                            {% if status.in_sync %}
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check"></i> In Sync
                                                </span>
                                            {% else %}
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-exclamation-triangle"></i> Out of Sync
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if status.only_in_config > 0 %}
                                                <span class="badge badge-info">{{ status.only_in_config }}</span>
                                            {% else %}
                                                <span class="text-muted">0</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if status.only_in_database > 0 %}
                                                <span class="badge badge-secondary">{{ status.only_in_database }}</span>
                                            {% else %}
                                                <span class="text-muted">0</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if status.mismatched > 0 %}
                                                <span class="badge badge-danger">{{ status.mismatched }}</span>
                                            {% else %}
                                                <span class="text-muted">0</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <form method="post" action="{{ url_for('vpn.sync_server_clients', server_id=server.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-primary" title="Sync Server">
                                                        <i class="fas fa-sync"></i>
                                                    </button>
                                                </form>
                                                <form method="post" action="{{ url_for('vpn.cleanup_orphaned_clients', server_id=server.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-warning" title="Cleanup Orphaned" onclick="return confirm('Remove orphaned clients from database?')">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </form>
                                                <a href="{{ url_for('vpn.api_compare_sync', server_id=server.id) }}" class="btn btn-sm btn-info" title="View Details" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            No servers configured. <a href="{{ url_for('vpn.create_server') }}">Create a server</a> first.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Sync Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Sync Information</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-server"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Config Only</span>
                                    <span class="info-box-number">
                                        {{ sync_status.values() | selectattr('only_in_config') | map(attribute='only_in_config') | sum }}
                                    </span>
                                    <span class="progress-description">Clients only in server config</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-secondary"><i class="fas fa-database"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Database Only</span>
                                    <span class="info-box-number">
                                        {{ sync_status.values() | selectattr('only_in_database') | map(attribute='only_in_database') | sum }}
                                    </span>
                                    <span class="progress-description">Clients only in database</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-exclamation-triangle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Mismatched</span>
                                    <span class="info-box-number">
                                        {{ sync_status.values() | selectattr('mismatched') | map(attribute='mismatched') | sum }}
                                    </span>
                                    <span class="progress-description">Clients with differences</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">In Sync</span>
                                    <span class="info-box-number">
                                        {{ sync_status.values() | selectattr('in_sync') | list | length }}
                                    </span>
                                    <span class="progress-description">Servers synchronized</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);

// Add loading states to buttons
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const button = form.querySelector('button[type="submit"]');
            if (button) {
                button.disabled = true;
                const icon = button.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-spinner fa-spin';
                }
            }
        });
    });
});
</script>
{% endblock %}
