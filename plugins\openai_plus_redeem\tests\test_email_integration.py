"""
Email Service Integration Tests

Tests Gmail IMAP integration with various scenarios:
- Real email account connections
- Verification code extraction
- Error handling for different email formats
- Connection failure scenarios
- Authentication issues
"""

import unittest
import imaplib
import email
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Import email service
from ..services.email_service import EmailService
from ..models.email_verification import EmailVerification, VerificationStatus


class TestEmailServiceIntegration(unittest.TestCase):
    """Test email service integration with Gmail IMAP"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config = {
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'use_ssl': True,
                'require_verification': True,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_app_password'
                }
            }
        }
        
        self.logger = Mock()
        self.email_service = EmailService(self.test_config, self.logger)
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_successful_imap_connection(self, mock_imaplib):
        """Test successful IMAP connection to Gmail"""
        # Mock successful IMAP connection
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.select.return_value = ('OK', [])
        mock_imap.logout.return_value = ('OK', [])
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test connection
        connection_result = self.email_service._test_email_connection(
            '<EMAIL>', 'test_password'
        )
        
        self.assertTrue(connection_result['success'])
        
        # Verify IMAP calls
        mock_imaplib.IMAP4_SSL.assert_called_with('imap.gmail.com', 993)
        mock_imap.login.assert_called_with('<EMAIL>', 'test_password')
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_imap_authentication_failure(self, mock_imaplib):
        """Test IMAP authentication failure handling"""
        # Mock authentication failure
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.side_effect = imaplib.IMAP4.error("Authentication failed")
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test connection with invalid credentials
        connection_result = self.email_service._test_email_connection(
            '<EMAIL>', 'wrong_password'
        )
        
        self.assertFalse(connection_result['success'])
        self.assertIn('Authentication failed', connection_result['error'])
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_verification_code_extraction_openai(self, mock_imaplib):
        """Test verification code extraction from OpenAI emails"""
        # Mock IMAP connection and email data
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.select.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [b'1', b'2'])
        
        # Mock OpenAI verification email
        openai_email_content = """From: <EMAIL>
To: <EMAIL>
Subject: Your OpenAI verification code
Date: Mon, 1 Jan 2024 12:00:00 +0000

Your verification code is: 123456

This code will expire in 10 minutes.
"""
        
        mock_imap.fetch.return_value = ('OK', [
            (b'1', openai_email_content.encode()),
            (b'2', b'Subject: Other email\r\n\r\nNot relevant')
        ])
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test verification code extraction
        verification_result = self.email_service.get_verification_code('<EMAIL>')
        
        self.assertTrue(verification_result['success'])
        self.assertEqual(verification_result['code'], '123456')
        self.assertEqual(verification_result['source'], 'openai')
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_verification_code_extraction_chatgpt(self, mock_imaplib):
        """Test verification code extraction from ChatGPT emails"""
        # Mock IMAP connection and email data
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.select.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [b'1'])
        
        # Mock ChatGPT verification email
        chatgpt_email_content = """From: <EMAIL>
To: <EMAIL>
Subject: ChatGPT Login Verification
Date: Mon, 1 Jan 2024 12:00:00 +0000

Hello,

Someone is trying to sign in to your ChatGPT account.

Verification code: 789012

If this wasn't you, please ignore this email.
"""
        
        mock_imap.fetch.return_value = ('OK', [
            (b'1', chatgpt_email_content.encode())
        ])
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test verification code extraction
        verification_result = self.email_service.get_verification_code('<EMAIL>')
        
        self.assertTrue(verification_result['success'])
        self.assertEqual(verification_result['code'], '789012')
        self.assertEqual(verification_result['source'], 'chatgpt')
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_no_verification_emails_found(self, mock_imaplib):
        """Test handling when no verification emails are found"""
        # Mock IMAP connection with no relevant emails
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.select.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [])  # No emails found
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test verification code extraction
        verification_result = self.email_service.get_verification_code('<EMAIL>')
        
        self.assertFalse(verification_result['success'])
        self.assertIn('No verification emails found', verification_result['error'])
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_malformed_email_handling(self, mock_imaplib):
        """Test handling of malformed email content"""
        # Mock IMAP connection and malformed email data
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.select.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [b'1'])
        
        # Mock malformed email
        mock_imap.fetch.return_value = ('OK', [
            (b'1', b'Malformed email content without proper headers')
        ])
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test verification code extraction
        verification_result = self.email_service.get_verification_code('<EMAIL>')
        
        # Should handle gracefully
        self.assertFalse(verification_result['success'])
        self.assertIn('error', verification_result)
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_connection_timeout_handling(self, mock_imaplib):
        """Test handling of connection timeouts"""
        # Mock connection timeout
        mock_imaplib.IMAP4_SSL.side_effect = TimeoutError("Connection timed out")
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test connection with timeout
        connection_result = self.email_service._test_email_connection(
            '<EMAIL>', 'test_password'
        )
        
        self.assertFalse(connection_result['success'])
        self.assertIn('timeout', connection_result['error'].lower())
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_multiple_verification_codes(self, mock_imaplib):
        """Test handling multiple verification codes (should return latest)"""
        # Mock IMAP connection and multiple emails
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.select.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [b'1', b'2'])
        
        # Mock multiple verification emails (older first)
        older_email = """From: <EMAIL>
To: <EMAIL>
Subject: Your OpenAI verification code
Date: Mon, 1 Jan 2024 11:00:00 +0000

Your verification code is: 111111
"""
        
        newer_email = """From: <EMAIL>
To: <EMAIL>
Subject: Your OpenAI verification code
Date: Mon, 1 Jan 2024 12:00:00 +0000

Your verification code is: 222222
"""
        
        mock_imap.fetch.side_effect = [
            ('OK', [(b'1', older_email.encode())]),
            ('OK', [(b'2', newer_email.encode())])
        ]
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test verification code extraction
        verification_result = self.email_service.get_verification_code('<EMAIL>')
        
        self.assertTrue(verification_result['success'])
        # Should return the latest code
        self.assertEqual(verification_result['code'], '222222')
    
    def test_verification_logging(self):
        """Test verification attempt logging"""
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Log a verification attempt
        self.email_service._log_verification_attempt(
            '<EMAIL>',
            VerificationStatus.SUCCESS,
            '123456',
            'openai'
        )
        
        # Check logs
        logs = self.email_service.get_verification_logs()
        self.assertGreater(len(logs), 0)
        
        latest_log = logs[-1]
        self.assertEqual(latest_log.email, '<EMAIL>')
        self.assertEqual(latest_log.status, VerificationStatus.SUCCESS)
        self.assertEqual(latest_log.verification_code, '123456')
        self.assertEqual(latest_log.source, 'openai')
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_email_search_filters(self, mock_imaplib):
        """Test email search filters for verification emails"""
        # Mock IMAP connection
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.select.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [b'1'])
        
        # Mock verification email
        verification_email = """From: <EMAIL>
To: <EMAIL>
Subject: Your OpenAI verification code
Date: Mon, 1 Jan 2024 12:00:00 +0000

Your verification code is: 555555
"""
        
        mock_imap.fetch.return_value = ('OK', [
            (b'1', verification_email.encode())
        ])
        
        # Initialize service
        result = self.email_service.initialize()
        self.assertTrue(result)
        
        # Test verification code extraction
        verification_result = self.email_service.get_verification_code('<EMAIL>')
        
        self.assertTrue(verification_result['success'])
        
        # Verify search was called with proper filters
        mock_imap.search.assert_called()
        search_args = mock_imap.search.call_args[0]
        
        # Should search for recent emails from OpenAI
        self.assertIn('FROM', search_args[1])
        self.assertIn('SINCE', search_args[1])


if __name__ == '__main__':
    unittest.main()
