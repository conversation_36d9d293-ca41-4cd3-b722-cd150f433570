from services.shopee_api_client import shopee_client
from utils.api_utils import generate_request_id
from utils.session import session
import config
import uuid
from urllib.parse import quote
from typing import List, Dict, Any
from services.deepseek_service import generate_reply
import json
import time
import logging

logger = logging.getLogger(__name__)

def send_order_message(order_sn):
    """Send an order message using centralized Shopee API."""
    try:
        response_data, status_code = shopee_client.send_order_message(order_sn)

        if status_code == 200:
            return response_data
        else:
            logger.error(f"Failed to send order message for {order_sn}: {response_data}")
            return {"error": response_data.get('error', 'Unknown error')}, status_code

    except Exception as e:
        logger.error(f"Exception in send_order_message: {str(e)}")
        return {"error": f"Failed to send order message: {str(e)}"}, 500

def send_image_message(payload):
    """Send an image message using the centralized Shopee API client."""
    try:
        # The shopee_client will handle the correct endpoint and auth
        response_data, status_code = shopee_client.send_image_message(payload)

        if status_code == 200:
            logger.info(f"Successfully sent image to {payload.get('username')}")
            return response_data, status_code
        else:
            logger.error(f"Failed to send image message to {payload.get('username')}: {response_data}")
            return {"error": response_data.get('error', 'Unknown error')}, status_code

    except Exception as e:
        logger.error(f"Exception in send_image_message: {str(e)}")
        return {"error": f"Failed to send image message: {str(e)}"}, 500


def send_chat_message(payload):
    """Send a chat message using centralized Shopee API."""
    try:
        # Extract required parameters
        text = payload.get('text')
        username = payload.get('username')
        force_send_cancel_order_warning = payload.get('force_send_cancel_order_warning', False)
        comply_cancel_order_warning = payload.get('comply_cancel_order_warning', False)

        if not text or not username:
            return {"error": "Both 'text' and 'username' are required"}, 400

        response_data, status_code = shopee_client.send_chat_message(
            {
                'text': text,
                'username': username,
                'force_send_cancel_order_warning': force_send_cancel_order_warning,
                'comply_cancel_order_warning': comply_cancel_order_warning
            }
        )

        if status_code == 200:
            return response_data, 200
        else:
            logger.error(f"Failed to send chat message to {username}: {response_data}")
            return {"error": response_data.get('error', 'Unknown error')}, status_code

    except Exception as e:
        logger.error(f"Exception in send_chat_message: {str(e)}")
        return {"error": f"Failed to send chat message: {str(e)}"}, 500

def get_conversation_info_by_username_helper(username):
    if not username:
        return None, {"error": "username parameter is required"}

    # Prepare the params for the conversation search request
    params = {
        "per_page": "20",
        "keyword": username,
        "type": "3",
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }

    # Send the GET request to search for conversation info
    conversation_response = session.get(
        config.CONVERSATION_SEARCH_URL,
        params=params
    )

    if conversation_response.status_code != 200:
        return None, {"error": "Failed to retrieve conversation info"}

    return conversation_response.json(), None

def get_conversation_info_by_ordersn_helper(order_sn):
    if not order_sn:
        return None, {"error": "order_sn parameter is required"}

    # Search for the order to get user_id
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    # Check if order_data has the expected structure
    if 'data' not in order_data or 'card_list' not in order_data['data']:
        return None, {"error": "Unexpected order data structure"}

    # Find the matching order in the card_list
    matching_order = None
    for card in order_data['data']['card_list']:
        if isinstance(card, dict) and 'order_card' in card:
            if card['order_card']['card_header']['order_sn'] == order_sn:
                matching_order = card['order_card']
                break
        elif isinstance(card, dict) and 'card_header' in card:
            if card['card_header']['order_sn'] == order_sn:
                matching_order = card
                break
        elif isinstance(card, dict) and 'package_level_order_card' in card:
            if card['package_level_order_card']['card_header']['order_sn'] == order_sn:
                matching_order = card['package_level_order_card']
                break

    if not matching_order:
        return None, {"error": "Order not found"}

    user_id = matching_order['order_ext_info']['buyer_user_id']

    # Prepare the payload and params for the conversation request
    payload = {
        "user_id": user_id,
        "shop_id": config.SHOP_ID
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": config.CSRF_TOKEN,
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "_api_source": "sc"
    }

    # Send the POST request to get conversation info using the new session
    conversation_response = session.post(
        config.CONVERSATION_URL,
        params=params,
        json=payload
    )

    if conversation_response.status_code != 200:
        return None, {"error": "Failed to retrieve conversation info"}

    return conversation_response.json(), None

def get_recent_conversations(unread_only=False):
    """Get recent conversations using centralized Shopee API."""
    try:
        response_data, status_code = shopee_client.get_recent_conversations(unread_only=unread_only)

        if status_code == 200:
            # Handle different response formats from ShopeeAPI
            conversations_data = []

            if isinstance(response_data, list):
                # Direct list of conversations
                conversations_data = response_data
            elif isinstance(response_data, dict):
                # Check for different possible keys in nested structure
                if 'data' in response_data:
                    # API endpoint wraps response in {"data": ...}
                    inner_data = response_data['data']
                    if isinstance(inner_data, list):
                        conversations_data = inner_data
                    elif isinstance(inner_data, dict) and 'conversations' in inner_data:
                        conversations_data = inner_data['conversations']
                    else:
                        logger.warning(f"Unknown inner data format: {type(inner_data)}")
                        conversations_data = []
                elif 'conversations' in response_data:
                    # Direct conversations key
                    conversations_data = response_data['conversations']
                else:
                    # If it's a dict but no known keys, log and return empty
                    logger.warning(f"Unknown response format: {list(response_data.keys())}")
                    conversations_data = []

            logger.debug(f"Successfully retrieved {len(conversations_data) if isinstance(conversations_data, list) else 'unknown'} conversations")
            return conversations_data if isinstance(conversations_data, list) else []
        else:
            logger.error(f"Failed to get recent conversations: {response_data}")
            # Return empty list instead of error dict to maintain consistency
            return []

    except Exception as e:
        logger.error(f"Exception in get_recent_conversations: {str(e)}")
        # Return empty list instead of error dict to maintain consistency
        return []

def get_recent_latest_messages() -> List[Dict[str, Any]]:
    """Get the latest message from each recent conversation using centralized Shopee API."""
    try:
        response_data, status_code = shopee_client.get_recent_latest_messages()

        if status_code == 200:
            return response_data.get('data', [])
        else:
            logger.error(f"Failed to get recent latest messages: {response_data}")
            return []

    except Exception as e:
        logger.error(f"Exception in get_recent_latest_messages: {str(e)}")
        return []

def get_conversation_messages(conversation_id: str, offset: int = 0, limit: int = 20, direction: str = "older") -> List[Dict[str, Any]]:
    """Get messages from a specific conversation using centralized Shopee API."""
    try:
        response_data, status_code = shopee_client.get_conversation_messages(
            conversation_id=conversation_id,
            offset=offset,
            limit=limit,
            direction=direction
        )

        if status_code == 200:
            return response_data.get('data', [])
        else:
            logger.error(f"Failed to get conversation messages for {conversation_id}: {response_data}")
            return {"error": response_data.get('error', 'Unknown error')}, 500

    except Exception as e:
        logger.error(f"Exception in get_conversation_messages: {str(e)}")
        return {"error": f"Failed to retrieve conversation messages: {str(e)}"}, 500



def handle_ai_action(action_results: List[Dict[str, Any]], username: str) -> Dict[str, Any]:
    """Handle multiple AI actions and execute appropriate responses"""
    responses = []
    
    for action_result in action_results:
        action_type = action_result.get('type')
        
        if action_type == 'chat':
            # Standard chat message
            chat_payload = {
                "text": action_result.get('reply', ''),
                "username": username,
                "force_send_cancel_order_warning": False,
                "comply_cancel_order_warning": False
            }
            chat_response = send_chat_message(chat_payload)
            responses.append({
                "type": "chat",
                "response": chat_response
            })
            
        elif action_type == 'send_order':
            if action_result.get('status') == 'success':
                # First send chat message about the order
                chat_payload = {
                    "text": f"Here are the details for order {action_result['order_sn']} [MTYB]",
                    "username": username,
                    "force_send_cancel_order_warning": False,
                    "comply_cancel_order_warning": False
                }
                chat_response = send_chat_message(chat_payload)
                
                # Then send the order message
                order_response = send_order_message(action_result['order_sn'])
                
                responses.append({
                    "type": "send_order",
                    "chat_response": chat_response,
                    "order_response": order_response
                })
            else:
                # Send error message if order lookup failed
                chat_payload = {
                    "text": f"Sorry, I couldn't find the order details. {action_result.get('error', '')} [MTYB]",
                    "username": username,
                    "force_send_cancel_order_warning": False,
                    "comply_cancel_order_warning": False
                }
                chat_response = send_chat_message(chat_payload)
                responses.append({
                    "type": "send_order",
                    "error": action_result.get('error'),
                    "chat_response": chat_response
                })
    
    return responses

def process_and_reply_to_conversation(conversation_id: str, username: str) -> Dict[str, Any]:
    """Process and reply to unreplied messages with cooldown check"""
    try:
        # Load cooldown data
        cooldown_file = 'configs/cache/ai_reply_cooldown.json'
        try:
            with open(cooldown_file, 'r') as f:
                cooldown_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            cooldown_data = {}
            # Create the file if it doesn't exist
            with open(cooldown_file, 'w') as f:
                json.dump(cooldown_data, f, indent=4)

        # Get conversation messages
        messages = get_conversation_messages(conversation_id)
        if isinstance(messages, tuple) and len(messages) == 2 and messages[1] == 500:
            print(f"❌ Error getting messages for conversation {conversation_id}: {messages[0]['error']}")
            return messages
            
        if not messages or len(messages) == 0:
            print(f"No messages found for conversation {conversation_id}")
            return {"error": "No messages found"}, 404

        # Check cooldown
        last_message = messages[0]
        user_id = str(last_message['from_id'])
        current_time = time.time()
        cooldown_minutes = float(config.AI_REPLY_COOLDOWN_MINUTES or 60)

        if user_id in cooldown_data:
            last_reply_time = cooldown_data[user_id]
            if current_time - last_reply_time < (cooldown_minutes * 60):
                print(f"Cooldown period not elapsed for user {user_id}")
                return {"error": "Cooldown period not elapsed"}, 429

        # Generate AI reply
        ai_response = generate_reply(messages)
        
        if not ai_response.get("success"):
            error_msg = f"🤖 AI reply generation failed for conversation {conversation_id}"
            print(error_msg)
            return {"error": error_msg}, 500

        # Handle all AI actions and send appropriate responses
        responses = handle_ai_action(ai_response.get("action_results", []), username)
        
        # Update cooldown
        cooldown_data[user_id] = current_time
        with open(cooldown_file, 'w') as f:
            json.dump(cooldown_data, f, indent=4)
        
        return {
            "success": True,
            "data": {
                "responses": responses,
                "ai_response": ai_response["data"]
            }
        }

    except Exception as e:
        error_msg = f"❌ Failed to process and reply to conversation {conversation_id}: {str(e)}"
        print(error_msg)
        return {"error": error_msg}, 500