# Message Splitting Feature

## Overview

The ShopeeAPI now automatically handles long messages by splitting them into multiple parts when they exceed the 600-character limit. This feature ensures that all messages are delivered successfully without manual intervention.

## How It Works

### Automatic Splitting
- Messages longer than 600 characters are automatically detected
- The system intelligently splits messages at word boundaries when possible
- Each part is sent as a separate message with a 500ms delay between sends
- All parts are delivered to the same conversation in sequence

### Smart Word Boundary Detection
The splitting algorithm:
1. Looks for natural break points (spaces, punctuation) within the last 50 characters of the 500-character limit
2. Splits at word boundaries to maintain readability
3. Falls back to hard splitting at 500 characters if no good break point is found
4. Trims whitespace from each part

## API Response Format

### Single Message (≤ 500 characters)
```json
{
  "data": {
    "message_id": "2347556398049968497",
    "conversation_id": "1484437065047388532",
    "status": "sent"
  }
}
```

### Split Message (> 600 characters)
```json
{
  "data": {
    "message_id": "2347556398049968497",
    "conversation_id": "1484437065047388532",
    "status": "sent"
  },
  "message_parts_sent": 3,
  "all_responses": [
    {"data": {"message_id": "2347556398049968497", "status": "sent"}},
    {"data": {"message_id": "2347556398049968498", "status": "sent"}},
    {"data": {"message_id": "2347556398049968499", "status": "sent"}}
  ]
}
```

## Usage Examples

### Python Client
```python
from ShopeeAPI import ShopeeAPI

api = ShopeeAPI(authorization_code="...", cookie="...")

# Send a long message - automatically split
message_payload = {
    "text": "This is a very long message..." * 20,  # > 600 characters
    "username": "customer_username"
}

response, status_code = api.send_chat_message(message_payload)

if status_code == 200:
    if response.get('message_parts_sent', 1) > 1:
        print(f"Message split into {response['message_parts_sent']} parts")
    else:
        print("Message sent as single part")
```

### REST API
```bash
curl -X POST "http://localhost:8000/chat/send_message" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Very long message content...",
    "username": "customer_username"
  }'
```

## Technical Implementation

### Core Function
The `_split_message()` method in `services/chat.py`:
- Takes text and max_length (default: 600) as parameters
- Returns a list of message parts
- Handles edge cases like exact limit and just-over-limit messages

### Integration Points
- **ChatService.send_chat_message()**: Automatically calls `_split_message()` before sending
- **API Endpoint**: `/chat/send_message` transparently handles splitting
- **Response Enhancement**: Adds metadata about split messages

## Benefits

1. **Seamless Experience**: Users don't need to manually split long messages
2. **Reliable Delivery**: Ensures all content is delivered within Shopee's limits
3. **Readable Splits**: Intelligent word boundary detection maintains message readability
4. **Transparent Operation**: API response indicates when splitting occurred
5. **Backward Compatibility**: Existing code continues to work without changes

## Testing

The feature has been thoroughly tested with:
- Short messages (< 500 characters) - no splitting
- Long messages (> 500 characters) - proper splitting
- Edge cases (exactly 500, 501 characters)
- Real-world scenarios with natural text

All tests pass successfully, confirming the feature works as expected.

## Configuration

No additional configuration is required. The feature is enabled by default for all chat message sending operations.

## Rate Limiting Considerations

- 500ms delay between message parts prevents rate limiting
- Each part counts as a separate API call to Shopee
- Consider this when sending very long messages frequently

## Future Enhancements

Potential improvements could include:
- Configurable delay between message parts
- Custom split indicators (e.g., "1/3", "2/3", "3/3")
- Maximum number of parts limit
- Alternative splitting strategies (sentence-based, paragraph-based)
