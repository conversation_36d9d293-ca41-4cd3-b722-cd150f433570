# Plugin Configuration Migration Guide

## 🎯 Problem Solved

Previously, plugin configurations were saved in the plugin directories (e.g., `plugins/chat_commands/config.json`), which caused configurations to be lost every time the Docker container was updated. This was because plugin directories are not mounted as persistent volumes in Docker.

## ✅ Solution

Plugin configurations are now automatically migrated to the `configs/plugins/` directory, which is properly mounted as a persistent volume in Docker deployments.

## 📁 New Directory Structure

```
configs/
├── plugins/                    # 🆕 Plugin configurations (Docker persistent)
│   ├── chat_commands/
│   │   ├── config.json        # Plugin settings
│   │   └── commands.json      # Chat command definitions
│   └── vpn_config_generator/
│       ├── config.json        # Plugin settings
│       ├── templates.json     # VPN templates
│       └── telco_configs.json # Telco configurations
├── core/                      # Core application configs
├── services/                  # Service configurations
├── data/                      # Application data
└── cache/                     # Cache files
```

## 🔄 Automatic Migration

### What Happens Automatically

1. **First Run**: When plugins start, they check for legacy config files in their plugin directories
2. **Migration**: If legacy files exist and new files don't, they are automatically copied to `configs/plugins/`
3. **Persistence**: All future configuration changes are saved to the persistent directory

### Affected Plugins

- **chat_commands**: Migrates `config.json` and `commands.json`
- **vpn_config_generator**: Migrates `config.json`, `templates.json`, and `telco_configs.json`

## 🐳 Docker Configuration

### Volume Mounts Required

```yaml
volumes:
  - /www/wwwroot/steamcodetool-data/config:/app/configs
  - /www/wwwroot/steamcodetool-data/logs:/app/logs
  - /www/wwwroot/steamcodetool-data/data:/app/data
```

### What Gets Persisted

✅ **Persistent (survives container updates)**:
- Plugin configurations in `configs/plugins/`
- Core application settings in `configs/core/`
- Service configurations in `configs/services/`
- Application data in `data/`
- Log files in `logs/`

❌ **Non-persistent (lost on container update)**:
- Plugin code and templates
- Application code
- Temporary files

## 🛠️ Manual Migration (if needed)

If automatic migration doesn't work, you can manually copy files:

```bash
# For chat_commands plugin
cp plugins/chat_commands/config.json configs/plugins/chat_commands/
cp plugins/chat_commands/commands.json configs/plugins/chat_commands/

# For vpn_config_generator plugin
cp plugins/vpn_config_generator/config.json configs/plugins/vpn_config_generator/
cp plugins/vpn_config_generator/templates.json configs/plugins/vpn_config_generator/
cp plugins/vpn_config_generator/telco_configs.json configs/plugins/vpn_config_generator/
```

## 🔍 Verification

### Check Migration Status

1. **Start the application** and check logs for migration messages:
   ```
   INFO: Migrating config from plugins/chat_commands/config.json to configs/plugins/chat_commands/config.json
   INFO: Config migration completed
   ```

2. **Verify files exist**:
   ```bash
   ls -la configs/plugins/chat_commands/
   ls -la configs/plugins/vpn_config_generator/
   ```

3. **Test configuration changes**: Make changes through the web interface and verify they persist after container restart

### Troubleshooting

**Problem**: Configuration changes still lost after container update
- **Solution**: Verify Docker volume mounts are correct
- **Check**: Ensure `/app/configs` is mounted to persistent storage

**Problem**: Migration not happening automatically
- **Solution**: Check file permissions and ensure `configs/plugins/` directory exists
- **Fallback**: Use manual migration commands above

## 📝 For Plugin Developers

### New Plugin Configuration Pattern

```python
class MyPluginService:
    def __init__(self, plugin_dir: str):
        # Use configs directory for persistent storage
        self.configs_dir = os.path.join(
            os.path.dirname(os.path.dirname(plugin_dir)), 
            'configs', 'plugins', 'my_plugin'
        )
        os.makedirs(self.configs_dir, exist_ok=True)
        
        # Config files in persistent directory
        self.config_file = os.path.join(self.configs_dir, 'config.json')
        
        # Legacy files for migration
        self.legacy_config_file = os.path.join(plugin_dir, 'config.json')
        
        # Migrate if needed
        self._migrate_legacy_files()
    
    def _migrate_legacy_files(self):
        """Migrate legacy config files"""
        if os.path.exists(self.legacy_config_file) and not os.path.exists(self.config_file):
            import shutil
            shutil.copy2(self.legacy_config_file, self.config_file)
```

## 🎉 Benefits

1. **Docker Persistence**: Configurations survive container updates
2. **Automatic Migration**: No manual intervention required
3. **Backward Compatibility**: Existing setups continue to work
4. **Centralized Storage**: All configurations in one place
5. **Better Organization**: Clear separation between code and configuration

## 📞 Support

If you encounter issues with configuration migration:

1. Check the application logs for migration messages
2. Verify Docker volume mounts are correct
3. Ensure proper file permissions
4. Use manual migration as fallback
5. Report issues with detailed logs and configuration details
