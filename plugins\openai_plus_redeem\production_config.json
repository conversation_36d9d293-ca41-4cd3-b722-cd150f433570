{"enabled": true, "debug": false, "environment": "production", "email_config": {"imap_server": "imap.gmail.com", "imap_port": 993, "use_ssl": true, "connection_timeout": 30, "read_timeout": 60, "require_verification": true, "max_retries": 3, "retry_delay": 2.0, "global_credentials": {"email": "${GMAIL_EMAIL}", "password": "${GMAIL_APP_PASSWORD}"}, "rate_limiting": {"max_connections_per_hour": 100, "max_emails_per_hour": 200}}, "cooldown_config": {"default_cooldown_hours": 24, "enable_cooldown_management": true, "max_cooldown_hours": 168, "cleanup_interval_hours": 6, "admin_reset_enabled": true}, "data_config": {"data_directory": "configs/data/openai_plus_redeem", "backup_enabled": true, "backup_interval_hours": 6, "backup_retention_days": 30, "max_backup_files": 120, "compression_enabled": true, "encryption_enabled": false, "file_permissions": "0600"}, "rate_limiting": {"customer_endpoints": {"redeem_per_hour": 10, "status_check_per_hour": 50, "verification_search_per_hour": 20, "cooldown_check_per_hour": 30, "account_test_per_hour": 5, "shopee_redeem_per_hour": 15}, "admin_endpoints": {"general_per_hour": 200, "bulk_operations_per_hour": 20, "service_restart_per_hour": 5}, "global_limits": {"max_concurrent_requests": 50, "request_timeout": 30}}, "logging_config": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_logging": {"enabled": true, "filename": "logs/openai_plus_redeem.log", "max_bytes": ********, "backup_count": 5, "rotation": "size"}, "console_logging": {"enabled": false}, "sensitive_data_masking": true, "log_requests": true, "log_responses": false, "log_performance": true}, "security_config": {"admin_authentication": {"enabled": true, "token_expiry_hours": 8, "max_failed_attempts": 5, "lockout_duration_minutes": 30}, "input_validation": {"strict_mode": true, "max_request_size": 1048576, "allowed_file_types": [], "sanitize_inputs": true}, "cors": {"enabled": true, "allowed_origins": ["https://yourdomain.com"], "allowed_methods": ["GET", "POST", "PUT", "DELETE"], "allowed_headers": ["Content-Type", "Authorization"], "max_age": 3600}, "ssl": {"enforce_https": true, "hsts_enabled": true, "hsts_max_age": 31536000}}, "performance_config": {"caching": {"enabled": true, "cache_ttl": 300, "max_cache_size": 1000}, "connection_pooling": {"enabled": true, "max_connections": 20, "connection_timeout": 30}, "async_processing": {"enabled": true, "max_workers": 4, "queue_size": 100}, "resource_limits": {"max_memory_mb": 512, "max_cpu_percent": 80, "max_disk_usage_mb": 1024}}, "monitoring_config": {"health_checks": {"enabled": true, "interval_seconds": 60, "timeout_seconds": 10, "failure_threshold": 3}, "metrics": {"enabled": true, "collection_interval": 30, "retention_days": 7}, "alerts": {"enabled": true, "email_notifications": true, "webhook_url": "${MONITORING_WEBHOOK_URL}", "alert_thresholds": {"error_rate_percent": 5, "response_time_ms": 5000, "memory_usage_percent": 90, "disk_usage_percent": 85}}}, "shopee_integration": {"api_timeout": 30, "max_retries": 3, "retry_delay": 1.0, "rate_limit_per_minute": 60, "cache_order_data": true, "cache_ttl_minutes": 15}, "features": {"enable_customer_portal": true, "enable_admin_interface": true, "enable_api_endpoints": true, "enable_email_verification": true, "enable_cooldown_management": true, "enable_shopee_integration": true, "enable_backup_automation": true, "enable_performance_monitoring": true, "enable_security_logging": true}, "maintenance": {"scheduled_maintenance": {"enabled": false, "start_time": "02:00", "duration_minutes": 30, "notification_enabled": true}, "auto_cleanup": {"enabled": true, "cleanup_interval_hours": 24, "retention_days": 90, "cleanup_logs": true, "cleanup_temp_files": true}}, "deployment": {"version": "1.0.0", "deployment_date": "${DEPLOYMENT_DATE}", "environment_checks": {"verify_dependencies": true, "verify_permissions": true, "verify_connectivity": true, "verify_disk_space": true}, "rollback": {"enabled": true, "backup_before_deploy": true, "rollback_timeout_minutes": 10}}}