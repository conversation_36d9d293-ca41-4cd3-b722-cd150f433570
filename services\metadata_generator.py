"""
Metadata Generator

This module provides enhanced metadata generation capabilities for fake orders,
creating realistic and varied sample data for different product types.
"""

import random
import string
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

class MetadataGenerator:
    """Enhanced metadata generator for realistic fake order data"""
    
    def __init__(self):
        self._initialize_sample_data()
    
    def _initialize_sample_data(self):
        """Initialize sample data pools for realistic generation"""
        self.sample_data = {
            "gaming": {
                "regions": ["global", "us", "eu", "asia", "sea"],
                "auth_methods": ["mobile", "email", "sms", "authenticator"],
                "account_types": ["standard", "premium", "family", "developer"],
                "game_genres": ["action", "rpg", "strategy", "simulation", "sports", "racing"],
                "platforms": ["pc", "mobile", "console", "cross_platform"]
            },
            "streaming": {
                "regions": ["malaysia", "singapore", "thailand", "indonesia", "global", "usa", "uk"],
                "subscription_types": ["basic", "standard", "premium", "family", "student"],
                "video_quality": ["sd", "hd", "uhd", "4k"],
                "device_limits": [1, 2, 4, 6, 10],
                "content_types": ["movies", "series", "documentaries", "kids", "sports"]
            },
            "design": {
                "subscription_types": ["pro", "teams", "enterprise", "education"],
                "features": [
                    "premium_templates", "background_remover", "brand_kit", 
                    "resize_magic", "animation", "video_editor", "photo_effects",
                    "team_collaboration", "brand_management", "content_planner"
                ],
                "storage_tiers": [100, 500, 1000, 5000, "unlimited"],
                "team_sizes": [1, 5, 10, 25, 50, 100]
            },
            "security": {
                "protocols": ["wireguard", "openvpn", "ikev2", "sstp", "l2tp"],
                "server_locations": [
                    "singapore", "malaysia", "thailand", "japan", "south_korea",
                    "usa", "uk", "germany", "france", "australia", "canada"
                ],
                "device_limits": [1, 3, 5, 10, "unlimited"],
                "bandwidth_options": ["1gb", "10gb", "100gb", "unlimited"],
                "encryption_levels": ["128bit", "256bit", "military_grade"]
            }
        }
        
        # Common data pools
        self.common_data = {
            "durations": {
                "short": [7, 10, 14, 15],
                "medium": [30, 60, 90],
                "long": [180, 365, 730],
                "lifetime": [-1]
            },
            "currencies": ["USD", "MYR", "SGD", "THB", "IDR"],
            "languages": ["en", "ms", "zh", "th", "id", "ja", "ko"],
            "timezones": ["UTC+8", "UTC+7", "UTC+9", "UTC+0", "UTC-5"]
        }
    
    def generate_gaming_metadata(self, var_sku: str, base_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced gaming product metadata"""
        metadata = base_metadata.copy()
        
        # Add realistic variations
        if "steam" in var_sku.lower():
            metadata.update({
                "platform": "steam",
                "region": random.choice(self.sample_data["gaming"]["regions"]),
                "account_level": random.randint(1, 50),
                "games_library_size": random.randint(0, 200),
                "wallet_currency": random.choice(self.common_data["currencies"]),
                "creation_year": random.randint(2010, 2024),
                "last_login": self._generate_recent_timestamp(),
                "security_features": {
                    "two_factor_enabled": random.choice([True, False]),
                    "mobile_auth": random.choice([True, False]),
                    "email_verified": True
                }
            })
            
            if "auth_code" in var_sku:
                metadata.update({
                    "auth_method": random.choice(self.sample_data["gaming"]["auth_methods"]),
                    "code_length": random.choice([6, 8, 16]),
                    "validity_hours": random.choice([1, 6, 12, 24, 72]),
                    "usage_limit": random.choice([1, 3, 5, "unlimited"])
                })
        
        return metadata
    
    def generate_streaming_metadata(self, var_sku: str, base_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced streaming service metadata"""
        metadata = base_metadata.copy()
        
        if "netflix" in var_sku.lower():
            metadata.update({
                "platform": "netflix",
                "max_screens": random.choice([1, 2, 4, 6]),
                "video_quality": random.choice(self.sample_data["streaming"]["video_quality"]),
                "download_devices": random.choice([1, 2, 4, 6]),
                "content_rating": random.choice(["all", "teen", "mature"]),
                "language_preferences": random.sample(self.common_data["languages"], k=random.randint(1, 3)),
                "auto_renewal": random.choice([True, False]),
                "gift_subscription": random.choice([True, False])
            })
        elif "hulu" in var_sku.lower():
            metadata.update({
                "platform": "hulu",
                "ads_included": random.choice([True, False]),
                "live_tv": random.choice([True, False]),
                "offline_downloads": random.choice([True, False]),
                "simultaneous_streams": random.choice([1, 2, 4]),
                "dvr_hours": random.choice([0, 50, 200, "unlimited"])
            })
        
        # Add common streaming metadata
        metadata.update({
            "activation_region": random.choice(self.sample_data["streaming"]["regions"]),
            "parental_controls": random.choice([True, False]),
            "subtitle_languages": random.sample(self.common_data["languages"], k=random.randint(2, 4)),
            "created_date": self._generate_past_date(days_back=random.randint(1, 30))
        })
        
        return metadata
    
    def generate_design_metadata(self, var_sku: str, base_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced design tool metadata"""
        metadata = base_metadata.copy()
        
        if "canva" in var_sku.lower():
            # Determine if it's lifetime or time-limited
            is_lifetime = "lifetime" in var_sku.lower()
            
            metadata.update({
                "platform": "canva",
                "subscription_tier": random.choice(self.sample_data["design"]["subscription_types"]),
                "storage_gb": random.choice(self.sample_data["design"]["storage_tiers"]),
                "team_members_limit": random.choice(self.sample_data["design"]["team_sizes"]),
                "brand_kits_limit": random.choice([1, 3, 5, 10, "unlimited"]),
                "template_access": "premium" if not is_lifetime else "lifetime_premium",
                "export_formats": ["jpg", "png", "pdf", "mp4", "gif"],
                "collaboration_features": random.choice([True, False]),
                "api_access": random.choice([True, False]),
                "priority_support": is_lifetime or random.choice([True, False])
            })
            
            # Add feature set based on subscription
            available_features = self.sample_data["design"]["features"]
            feature_count = len(available_features) if is_lifetime else random.randint(3, 8)
            metadata["features"] = random.sample(available_features, k=min(feature_count, len(available_features)))
        
        return metadata
    
    def generate_security_metadata(self, var_sku: str, base_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced VPN/security service metadata"""
        metadata = base_metadata.copy()
        
        if "vpn" in var_sku.lower():
            # Determine subscription length
            is_yearly = "yearly" in var_sku.lower() or "annual" in var_sku.lower()
            
            metadata.update({
                "service_type": "vpn",
                "protocol": random.choice(self.sample_data["security"]["protocols"]),
                "encryption": random.choice(self.sample_data["security"]["encryption_levels"]),
                "max_devices": random.choice(self.sample_data["security"]["device_limits"]),
                "bandwidth_limit": random.choice(self.sample_data["security"]["bandwidth_options"]),
                "kill_switch": random.choice([True, False]),
                "dns_leak_protection": random.choice([True, False]),
                "split_tunneling": random.choice([True, False]),
                "p2p_support": random.choice([True, False]),
                "streaming_optimized": random.choice([True, False]),
                "no_logs_policy": True,
                "customer_support": "24/7" if is_yearly else random.choice(["business_hours", "24/7"])
            })
            
            # Server locations based on subscription tier
            location_count = random.randint(10, 20) if is_yearly else random.randint(3, 8)
            metadata["server_locations"] = random.sample(
                self.sample_data["security"]["server_locations"], 
                k=min(location_count, len(self.sample_data["security"]["server_locations"]))
            )
            
            metadata["server_count"] = len(metadata["server_locations"]) * random.randint(5, 50)
        
        return metadata
    
    def generate_custom_metadata(self, category: str, var_sku: str, base_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate custom metadata for extensibility"""
        metadata = base_metadata.copy()
        
        # Add common metadata for all products
        metadata.update({
            "product_id": self._generate_product_id(var_sku),
            "activation_code": self._generate_activation_code(),
            "license_type": random.choice(["personal", "commercial", "educational"]),
            "support_level": random.choice(["basic", "standard", "premium"]),
            "auto_renewal": random.choice([True, False]),
            "refund_policy_days": random.choice([7, 14, 30, 60]),
            "terms_version": f"v{random.randint(1, 5)}.{random.randint(0, 9)}",
            "privacy_policy_accepted": True,
            "marketing_consent": random.choice([True, False])
        })
        
        # Add category-specific enhancements
        if category == "gaming":
            metadata = self.generate_gaming_metadata(var_sku, metadata)
        elif category == "streaming":
            metadata = self.generate_streaming_metadata(var_sku, metadata)
        elif category == "design":
            metadata = self.generate_design_metadata(var_sku, metadata)
        elif category == "security":
            metadata = self.generate_security_metadata(var_sku, metadata)
        
        # Add generation metadata
        metadata.update({
            "metadata_generated_at": datetime.now().isoformat(),
            "metadata_version": "2.0",
            "generator": "enhanced_metadata_generator"
        })
        
        return metadata
    
    def _generate_product_id(self, var_sku: str) -> str:
        """Generate a realistic product ID"""
        prefix = var_sku.upper()[:4]
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        return f"{prefix}-{random_part}"
    
    def _generate_activation_code(self) -> str:
        """Generate a realistic activation code"""
        parts = []
        for _ in range(4):
            part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
            parts.append(part)
        return '-'.join(parts)
    
    def _generate_recent_timestamp(self, hours_back: int = 72) -> str:
        """Generate a recent timestamp"""
        now = datetime.now()
        random_hours = random.randint(1, hours_back)
        past_time = now - timedelta(hours=random_hours)
        return past_time.isoformat()
    
    def _generate_past_date(self, days_back: int = 30) -> str:
        """Generate a past date"""
        now = datetime.now()
        random_days = random.randint(1, days_back)
        past_date = now - timedelta(days=random_days)
        return past_date.date().isoformat()
    
    def generate_realistic_buyer_data(self) -> Dict[str, Any]:
        """Generate realistic buyer information"""
        first_names = ["Ahmad", "Siti", "Lim", "Tan", "Wong", "Lee", "Kumar", "Devi", "Ali", "Fatimah"]
        last_names = ["Abdullah", "Rahman", "Wei", "Ming", "Chong", "Krishnan", "Hassan", "Yusof"]
        
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        
        return {
            "buyer_name": f"{first_name} {last_name}",
            "buyer_username": f"{first_name.lower()}{random.randint(100, 999)}",
            "buyer_email": f"{first_name.lower()}.{last_name.lower()}@{random.choice(['gmail.com', 'yahoo.com', 'hotmail.com'])}",
            "buyer_phone": f"+60{random.randint(100000000, 199999999)}",
            "buyer_address": f"{random.randint(1, 999)} Jalan {random.choice(['Bukit', 'Taman', 'Bandar'])}, {random.choice(['Kuala Lumpur', 'Selangor', 'Penang', 'Johor'])}"
        }


# Global metadata generator instance
metadata_generator = MetadataGenerator()