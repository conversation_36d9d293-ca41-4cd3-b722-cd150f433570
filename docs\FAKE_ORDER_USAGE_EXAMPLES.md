# Fake Order System - Usage Examples

## Overview

This document provides comprehensive examples for using the Universal Fake Order System in different testing scenarios. Each example includes complete request/response cycles and explains the testing purpose.

## Table of Contents

1. [Basic Order Generation](#basic-order-generation)
2. [E-commerce Testing Scenarios](#e-commerce-testing-scenarios)
3. [Load Testing Examples](#load-testing-examples)
4. [Integration Testing](#integration-testing)
5. [Error Handling Testing](#error-handling-testing)
6. [Maintenance and Cleanup](#maintenance-and-cleanup)
7. [Python SDK Examples](#python-sdk-examples)
8. [JavaScript Examples](#javascript-examples)

## Basic Order Generation

### Single Order Creation

**Scenario:** Create a basic fake order for Canva Pro testing

```bash
curl -X POST "https://your-domain/api/fake-orders/generate" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "product_config": {
      "var_sku": "canva_30",
      "quantity": 1
    },
    "buyer_config": {
      "username": "test_user_001",
      "name": "<PERSON>",
      "email": "<EMAIL>"
    },
    "test_scenario": "canva_basic_flow"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Fake order FAKE_CANVA_20250116143000 created successfully",
  "order_sn": "FAKE_CANVA_20250116143000",
  "order_data": {
    "success": true,
    "data": {
      "order_sn": "FAKE_CANVA_20250116143000",
      "var_sku": "canva_30",
      "buyer_username": "test_user_001",
      "status": "To Ship",
      "payment_status": "paid",
      "total_price": 15.00
    }
  }
}
```

### Custom Order with Specific Configuration

**Scenario:** Create a fake order with custom pricing and metadata

```bash
curl -X POST "https://your-domain/api/fake-orders/generate" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "order_sn": "CUSTOM_TEST_001",
    "product_config": {
      "var_sku": "netflix_premium",
      "quantity": 2,
      "price_override": 25.99,
      "custom_metadata": {
        "test_type": "pricing_validation",
        "expected_discount": 10,
        "test_environment": "staging"
      }
    },
    "buyer_config": {
      "username": "premium_tester",
      "name": "Premium Test User",
      "phone": "+60123456789",
      "email": "<EMAIL>",
      "address": "123 Premium Street, Test City, 12345"
    },
    "order_config": {
      "status": "Completed",
      "payment_status": "paid",
      "created_date": "2025-01-15T10:00:00Z",
      "custom_metadata": {
        "payment_method": "credit_card",
        "promo_code": "TEST10"
      }
    },
    "test_scenario": "premium_pricing_test"
  }'
```

## E-commerce Testing Scenarios

### Order Processing Flow Test

**Scenario:** Test complete order processing workflow

```python
import requests
import time

API_BASE = "https://your-domain/api"
API_KEY = "your-api-key"
headers = {"X-API-Key": API_KEY, "Content-Type": "application/json"}

# Step 1: Create fake order
order_data = {
    "product_config": {"var_sku": "canva_30", "quantity": 1},
    "buyer_config": {"username": "workflow_test", "email": "<EMAIL>"},
    "test_scenario": "order_processing_workflow"
}

response = requests.post(f"{API_BASE}/fake-orders/generate", 
                        json=order_data, headers=headers)
order_sn = response.json()["order_sn"]

# Step 2: Process the order
process_response = requests.post(f"{API_BASE}/order/process_order",
                               json={"order_sn": order_sn}, headers=headers)

# Step 3: Check order status
status_response = requests.get(f"{API_BASE}/order/get_order_status",
                              params={"order_sn": order_sn}, headers=headers)

# Step 4: Verify completion
details_response = requests.get(f"{API_BASE}/order/get_order_details",
                               params={"order_sn": order_sn}, headers=headers)

print(f"Order {order_sn} processing completed")
print(f"Final status: {status_response.json()}")
```

### Multi-Product Order Testing

**Scenario:** Test orders with different product types

```bash
# Create orders for different product categories
for sku in canva_30 netflix_premium vpn_monthly steam_wallet; do
  curl -X POST "https://your-domain/api/fake-orders/generate" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: your-api-key" \
    -d "{
      \"product_config\": {\"var_sku\": \"$sku\", \"quantity\": 1},
      \"buyer_config\": {\"username\": \"multi_product_test\"},
      \"test_scenario\": \"multi_product_validation\"
    }"
  sleep 1
done
```

### Payment Status Testing

**Scenario:** Test different payment statuses and their handling

```json
{
  "orders": [
    {
      "product_config": {"var_sku": "canva_30", "quantity": 1},
      "buyer_config": {"username": "payment_test_1"},
      "order_config": {"payment_status": "paid"},
      "test_scenario": "payment_status_paid"
    },
    {
      "product_config": {"var_sku": "canva_30", "quantity": 1},
      "buyer_config": {"username": "payment_test_2"},
      "order_config": {"payment_status": "pending"},
      "test_scenario": "payment_status_pending"
    },
    {
      "product_config": {"var_sku": "canva_30", "quantity": 1},
      "buyer_config": {"username": "payment_test_3"},
      "order_config": {"payment_status": "failed"},
      "test_scenario": "payment_status_failed"
    }
  ],
  "batch_config": {
    "continue_on_error": true,
    "default_test_scenario": "payment_status_testing"
  }
}
```

## Load Testing Examples

### Batch Order Generation for Load Testing

**Scenario:** Generate 100 orders for load testing

```python
import requests
import concurrent.futures
import time

def create_batch_orders(batch_size=50):
    """Create a batch of fake orders for load testing"""
    
    orders = []
    for i in range(batch_size):
        orders.append({
            "product_config": {"var_sku": "canva_30", "quantity": 1},
            "buyer_config": {
                "username": f"load_test_user_{i:03d}",
                "email": f"load_test_{i:03d}@example.com"
            },
            "test_scenario": "load_testing"
        })
    
    batch_data = {
        "orders": orders,
        "batch_config": {
            "continue_on_error": true,
            "generate_unique_order_sns": True,
            "default_test_scenario": "load_testing"
        }
    }
    
    response = requests.post(
        "https://your-domain/api/fake-orders/batch-generate",
        json=batch_data,
        headers={"X-API-Key": "your-api-key", "Content-Type": "application/json"}
    )
    
    return response.json()

# Generate 100 orders in 2 batches
batch1 = create_batch_orders(50)
batch2 = create_batch_orders(50)

print(f"Batch 1: {batch1['batch_summary']['successful']} successful")
print(f"Batch 2: {batch2['batch_summary']['successful']} successful")
```

### Concurrent Order Processing

**Scenario:** Test system under concurrent load

```python
import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor

def process_order_concurrently(order_sn):
    """Process a single order"""
    try:
        response = requests.post(
            "https://your-domain/api/order/process_order",
            json={"order_sn": order_sn},
            headers={"X-API-Key": "your-api-key"},
            timeout=30
        )
        return {"order_sn": order_sn, "success": response.status_code == 200}
    except Exception as e:
        return {"order_sn": order_sn, "success": False, "error": str(e)}

# First, create orders for concurrent processing
order_sns = []
for i in range(20):
    order_data = {
        "product_config": {"var_sku": "canva_30", "quantity": 1},
        "buyer_config": {"username": f"concurrent_test_{i}"},
        "test_scenario": "concurrent_processing"
    }
    
    response = requests.post(
        "https://your-domain/api/fake-orders/generate",
        json=order_data,
        headers={"X-API-Key": "your-api-key"}
    )
    
    if response.status_code == 201:
        order_sns.append(response.json()["order_sn"])

# Process orders concurrently
start_time = time.time()
with ThreadPoolExecutor(max_workers=5) as executor:
    results = list(executor.map(process_order_concurrently, order_sns))

end_time = time.time()
successful = sum(1 for r in results if r["success"])

print(f"Processed {len(order_sns)} orders in {end_time - start_time:.2f}s")
print(f"Success rate: {successful}/{len(order_sns)} ({successful/len(order_sns)*100:.1f}%)")
```

## Integration Testing

### API Integration Test Suite

**Scenario:** Complete API integration test

```python
import requests
import pytest
import time

class TestFakeOrderAPI:
    def __init__(self):
        self.base_url = "https://your-domain/api"
        self.headers = {"X-API-Key": "your-api-key", "Content-Type": "application/json"}
        self.test_orders = []
    
    def test_order_generation(self):
        """Test basic order generation"""
        order_data = {
            "product_config": {"var_sku": "canva_30", "quantity": 1},
            "buyer_config": {"username": "integration_test"},
            "test_scenario": "integration_testing"
        }
        
        response = requests.post(
            f"{self.base_url}/fake-orders/generate",
            json=order_data,
            headers=self.headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] == True
        assert "order_sn" in data
        
        self.test_orders.append(data["order_sn"])
        return data["order_sn"]
    
    def test_batch_generation(self):
        """Test batch order generation"""
        batch_data = {
            "orders": [
                {
                    "product_config": {"var_sku": "canva_30", "quantity": 1},
                    "buyer_config": {"username": f"batch_test_{i}"},
                    "test_scenario": "batch_integration_test"
                }
                for i in range(5)
            ],
            "batch_config": {"continue_on_error": True}
        }
        
        response = requests.post(
            f"{self.base_url}/fake-orders/batch-generate",
            json=batch_data,
            headers=self.headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["batch_summary"]["successful"] == 5
        
        # Store order SNs for cleanup
        for order in data["successful_orders"]:
            self.test_orders.append(order["order_sn"])
    
    def test_order_listing(self):
        """Test order listing and filtering"""
        response = requests.get(
            f"{self.base_url}/fake-orders/list",
            params={"test_scenario": "integration_testing", "limit": 10},
            headers=self.headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        assert len(data["fake_orders"]) > 0
    
    def test_template_management(self):
        """Test template operations"""
        # Get templates
        response = requests.get(
            f"{self.base_url}/fake-orders/templates",
            headers=self.headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "templates" in data
        
        # Save custom template
        template_data = {
            "var_sku": "integration_test_product",
            "product_name": "Integration Test Product",
            "category": "testing",
            "default_price": 10.00
        }
        
        response = requests.post(
            f"{self.base_url}/fake-orders/templates/save",
            json=template_data,
            headers=self.headers
        )
        
        assert response.status_code == 201
    
    def test_maintenance_operations(self):
        """Test maintenance operations"""
        # Get maintenance status
        response = requests.get(
            f"{self.base_url}/fake-orders/maintenance/status",
            headers=self.headers
        )
        
        assert response.status_code == 200
        
        # Run optimization
        maintenance_data = {
            "maintenance_type": "optimization"
        }
        
        response = requests.post(
            f"{self.base_url}/fake-orders/maintenance/run",
            json=maintenance_data,
            headers=self.headers
        )
        
        assert response.status_code == 200
    
    def cleanup_test_orders(self):
        """Clean up test orders"""
        if self.test_orders:
            cleanup_data = {
                "order_sns": self.test_orders,
                "confirm": True
            }
            
            requests.delete(
                f"{self.base_url}/fake-orders/cleanup",
                json=cleanup_data,
                headers=self.headers
            )

# Run integration tests
if __name__ == "__main__":
    test_suite = TestFakeOrderAPI()
    
    try:
        test_suite.test_order_generation()
        test_suite.test_batch_generation()
        test_suite.test_order_listing()
        test_suite.test_template_management()
        test_suite.test_maintenance_operations()
        print("All integration tests passed!")
    finally:
        test_suite.cleanup_test_orders()
```

## Error Handling Testing

### Testing Error Scenarios

**Scenario:** Test various error conditions

```python
import requests

def test_error_scenarios():
    base_url = "https://your-domain/api/fake-orders"
    headers = {"X-API-Key": "your-api-key", "Content-Type": "application/json"}
    
    # Test missing required fields
    response = requests.post(f"{base_url}/generate", 
                           json={}, headers=headers)
    assert response.status_code == 400
    assert "MISSING_PRODUCT_CONFIG" in response.json()["error_code"]
    
    # Test invalid SKU
    response = requests.post(f"{base_url}/generate",
                           json={
                               "product_config": {"var_sku": "invalid_sku"},
                               "test_scenario": "error_testing"
                           }, headers=headers)
    assert response.status_code == 400
    
    # Test invalid batch size
    large_batch = {
        "orders": [{"product_config": {"var_sku": "canva_30"}}] * 100,
        "batch_config": {"continue_on_error": True}
    }
    response = requests.post(f"{base_url}/batch-generate",
                           json=large_batch, headers=headers)
    assert response.status_code == 400
    assert "BATCH_SIZE_EXCEEDED" in response.json()["error_code"]
    
    # Test unauthorized access
    response = requests.get(f"{base_url}/list")
    assert response.status_code == 401
    
    print("Error handling tests completed successfully")

test_error_scenarios()
```

## Maintenance and Cleanup

### Scheduled Maintenance Example

**Scenario:** Set up and monitor scheduled maintenance

```python
import requests
import time

def setup_maintenance_monitoring():
    base_url = "https://your-domain/api/fake-orders"
    headers = {"X-API-Key": "your-api-key", "Content-Type": "application/json"}
    
    # Check scheduler status
    response = requests.get(f"{base_url}/scheduler/status", headers=headers)
    scheduler_status = response.json()["scheduler_status"]
    
    print(f"Scheduler running: {scheduler_status['scheduler_running']}")
    print(f"Enabled tasks: {scheduler_status['enabled_tasks']}")
    
    # Enable daily cleanup if disabled
    if not scheduler_status["tasks"]["daily_cleanup"]["enabled"]:
        requests.post(f"{base_url}/scheduler/task/daily_cleanup/toggle",
                     json={"enable": True}, headers=headers)
        print("Enabled daily cleanup task")
    
    # Run maintenance immediately for testing
    maintenance_data = {
        "maintenance_type": "full",
        "config": {
            "older_than_days": 30,
            "max_orders_per_cleanup": 100,
            "archive_before_delete": True
        }
    }
    
    response = requests.post(f"{base_url}/maintenance/run",
                           json=maintenance_data, headers=headers)
    
    if response.status_code == 200:
        stats = response.json()["stats"]
        print(f"Maintenance completed:")
        print(f"- Orders cleaned: {stats['cleanup_stats'].get('orders_deleted', 0)}")
        print(f"- Orders archived: {stats['archive_stats'].get('archived_count', 0)}")
        print(f"- Runtime: {stats['total_runtime_seconds']:.2f}s")

setup_maintenance_monitoring()
```

### Custom Cleanup Operations

**Scenario:** Clean up orders by specific criteria

```bash
# Clean up orders older than 14 days
curl -X DELETE "https://your-domain/api/fake-orders/cleanup?older_than_days=14&confirm=true" \
  -H "X-API-Key: your-api-key"

# Clean up orders with specific test scenario
curl -X DELETE "https://your-domain/api/fake-orders/cleanup?test_scenario=load_testing&confirm=true" \
  -H "X-API-Key: your-api-key"

# Clean up specific orders
curl -X DELETE "https://your-domain/api/fake-orders/cleanup" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "order_sns": ["FAKE_ORDER_001", "FAKE_ORDER_002"],
    "confirm": true
  }'
```

## Python SDK Examples

### Using Python Requests Library

```python
import requests
from datetime import datetime, timedelta

class FakeOrderClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
    
    def create_order(self, var_sku, buyer_username=None, test_scenario=None, **kwargs):
        """Create a single fake order"""
        order_data = {
            "product_config": {"var_sku": var_sku, **kwargs.get("product_config", {})},
            "buyer_config": {"username": buyer_username or "default_test_user", 
                           **kwargs.get("buyer_config", {})},
            "test_scenario": test_scenario or "api_client_test"
        }
        
        if "order_config" in kwargs:
            order_data["order_config"] = kwargs["order_config"]
        
        response = requests.post(
            f"{self.base_url}/api/fake-orders/generate",
            json=order_data,
            headers=self.headers
        )
        
        response.raise_for_status()
        return response.json()
    
    def create_batch_orders(self, orders_config, **batch_config):
        """Create multiple fake orders"""
        batch_data = {
            "orders": orders_config,
            "batch_config": batch_config
        }
        
        response = requests.post(
            f"{self.base_url}/api/fake-orders/batch-generate",
            json=batch_data,
            headers=self.headers
        )
        
        response.raise_for_status()
        return response.json()
    
    def list_orders(self, **filters):
        """List fake orders with filters"""
        response = requests.get(
            f"{self.base_url}/api/fake-orders/list",
            params=filters,
            headers=self.headers
        )
        
        response.raise_for_status()
        return response.json()
    
    def cleanup_orders(self, older_than_days=7, **kwargs):
        """Clean up old orders"""
        params = {"older_than_days": older_than_days, "confirm": "true", **kwargs}
        
        response = requests.delete(
            f"{self.base_url}/api/fake-orders/cleanup",
            params=params,
            headers=self.headers
        )
        
        response.raise_for_status()
        return response.json()

# Usage example
client = FakeOrderClient("https://your-domain", "your-api-key")

# Create single order
order = client.create_order(
    var_sku="canva_30",
    buyer_username="python_client_test",
    test_scenario="python_sdk_example"
)
print(f"Created order: {order['order_sn']}")

# Create batch orders
batch_orders = [
    {"product_config": {"var_sku": "canva_30"}, "buyer_config": {"username": f"batch_user_{i}"}}
    for i in range(5)
]

batch_result = client.create_batch_orders(
    batch_orders,
    continue_on_error=True,
    default_test_scenario="python_batch_test"
)
print(f"Batch created: {batch_result['batch_summary']['successful']} successful")

# List orders
orders = client.list_orders(test_scenario="python_sdk_example", limit=10)
print(f"Found {orders['count']} orders")

# Cleanup
cleanup_result = client.cleanup_orders(older_than_days=1)
print(f"Cleaned up {cleanup_result['cleanup_summary']['removed_count']} orders")
```

## JavaScript Examples

### Node.js Integration

```javascript
const axios = require('axios');

class FakeOrderClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.headers = {
            'X-API-Key': apiKey,
            'Content-Type': 'application/json'
        };
    }
    
    async createOrder(varSku, options = {}) {
        const orderData = {
            product_config: { var_sku: varSku, ...options.productConfig },
            buyer_config: { 
                username: options.buyerUsername || 'js_test_user',
                ...options.buyerConfig 
            },
            test_scenario: options.testScenario || 'javascript_client_test'
        };
        
        if (options.orderConfig) {
            orderData.order_config = options.orderConfig;
        }
        
        try {
            const response = await axios.post(
                `${this.baseUrl}/api/fake-orders/generate`,
                orderData,
                { headers: this.headers }
            );
            return response.data;
        } catch (error) {
            throw new Error(`Failed to create order: ${error.response?.data?.error || error.message}`);
        }
    }
    
    async createBatchOrders(ordersConfig, batchConfig = {}) {
        const batchData = {
            orders: ordersConfig,
            batch_config: batchConfig
        };
        
        try {
            const response = await axios.post(
                `${this.baseUrl}/api/fake-orders/batch-generate`,
                batchData,
                { headers: this.headers }
            );
            return response.data;
        } catch (error) {
            throw new Error(`Failed to create batch orders: ${error.response?.data?.error || error.message}`);
        }
    }
    
    async listOrders(filters = {}) {
        try {
            const response = await axios.get(
                `${this.baseUrl}/api/fake-orders/list`,
                { 
                    params: filters,
                    headers: this.headers 
                }
            );
            return response.data;
        } catch (error) {
            throw new Error(`Failed to list orders: ${error.response?.data?.error || error.message}`);
        }
    }
    
    async runMaintenance(maintenanceType = 'cleanup', config = {}) {
        const maintenanceData = {
            maintenance_type: maintenanceType,
            config: config
        };
        
        try {
            const response = await axios.post(
                `${this.baseUrl}/api/fake-orders/maintenance/run`,
                maintenanceData,
                { headers: this.headers }
            );
            return response.data;
        } catch (error) {
            throw new Error(`Failed to run maintenance: ${error.response?.data?.error || error.message}`);
        }
    }
}

// Usage example
async function exampleUsage() {
    const client = new FakeOrderClient('https://your-domain', 'your-api-key');
    
    try {
        // Create single order
        const order = await client.createOrder('canva_30', {
            buyerUsername: 'js_client_test',
            testScenario: 'javascript_example'
        });
        console.log(`Created order: ${order.order_sn}`);
        
        // Create batch orders
        const batchOrders = Array.from({ length: 3 }, (_, i) => ({
            product_config: { var_sku: 'canva_30' },
            buyer_config: { username: `js_batch_user_${i}` },
            test_scenario: 'javascript_batch_test'
        }));
        
        const batchResult = await client.createBatchOrders(batchOrders, {
            continue_on_error: true
        });
        console.log(`Batch created: ${batchResult.batch_summary.successful} successful`);
        
        // List orders
        const orders = await client.listOrders({
            test_scenario: 'javascript_example',
            limit: 5
        });
        console.log(`Found ${orders.count} orders`);
        
        // Run maintenance
        const maintenanceResult = await client.runMaintenance('optimization');
        console.log(`Maintenance completed in ${maintenanceResult.stats.total_runtime_seconds}s`);
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Run example
exampleUsage();
```

### Browser JavaScript Example

```html
<!DOCTYPE html>
<html>
<head>
    <title>Fake Order System - Browser Example</title>
</head>
<body>
    <div id="app">
        <h1>Fake Order System Test</h1>
        <button onclick="createTestOrder()">Create Test Order</button>
        <button onclick="listOrders()">List Orders</button>
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'https://your-domain/api/fake-orders';
        const API_KEY = 'your-api-key';
        
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'X-API-Key': API_KEY,
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            };
            
            try {
                const response = await fetch(url, config);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'API request failed');
                }
                
                return data;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }
        
        async function createTestOrder() {
            const orderData = {
                product_config: { var_sku: 'canva_30', quantity: 1 },
                buyer_config: { 
                    username: 'browser_test_user',
                    email: '<EMAIL>'
                },
                test_scenario: 'browser_javascript_test'
            };
            
            try {
                const result = await apiRequest('/generate', {
                    method: 'POST',
                    body: JSON.stringify(orderData)
                });
                
                displayResult(`Order created: ${result.order_sn}`, 'success');
            } catch (error) {
                displayResult(`Error creating order: ${error.message}`, 'error');
            }
        }
        
        async function listOrders() {
            try {
                const result = await apiRequest('/list?limit=5&test_scenario=browser_javascript_test');
                
                const ordersList = result.fake_orders.map(order => 
                    `${order.order_sn} - ${order.var_sku} - ${order.status}`
                ).join('<br>');
                
                displayResult(`Orders found (${result.count}):<br>${ordersList}`, 'success');
            } catch (error) {
                displayResult(`Error listing orders: ${error.message}`, 'error');
            }
        }
        
        function displayResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const className = type === 'error' ? 'error' : 'success';
            resultsDiv.innerHTML = `<div class="${className}">${message}</div>`;
        }
    </script>
    
    <style>
        .success { color: green; margin: 10px 0; }
        .error { color: red; margin: 10px 0; }
        button { margin: 5px; padding: 10px; }
    </style>
</body>
</html>
```

## Testing Framework Integration

### Jest Testing Example

```javascript
// fake-order-api.test.js
const axios = require('axios');

describe('Fake Order API Tests', () => {
    const client = axios.create({
        baseURL: 'https://your-domain/api/fake-orders',
        headers: {
            'X-API-Key': process.env.API_KEY || 'test-api-key',
            'Content-Type': 'application/json'
        }
    });
    
    let testOrderSns = [];
    
    afterAll(async () => {
        // Cleanup test orders
        if (testOrderSns.length > 0) {
            await client.delete('/cleanup', {
                data: { order_sns: testOrderSns, confirm: true }
            });
        }
    });
    
    test('should create a fake order', async () => {
        const orderData = {
            product_config: { var_sku: 'canva_30', quantity: 1 },
            buyer_config: { username: 'jest_test_user' },
            test_scenario: 'jest_testing'
        };
        
        const response = await client.post('/generate', orderData);
        
        expect(response.status).toBe(201);
        expect(response.data.success).toBe(true);
        expect(response.data.order_sn).toBeDefined();
        
        testOrderSns.push(response.data.order_sn);
    });
    
    test('should create batch orders', async () => {
        const batchData = {
            orders: [
                {
                    product_config: { var_sku: 'canva_30' },
                    buyer_config: { username: 'jest_batch_1' },
                    test_scenario: 'jest_batch_testing'
                },
                {
                    product_config: { var_sku: 'netflix_premium' },
                    buyer_config: { username: 'jest_batch_2' },
                    test_scenario: 'jest_batch_testing'
                }
            ],
            batch_config: { continue_on_error: true }
        };
        
        const response = await client.post('/batch-generate', batchData);
        
        expect(response.status).toBe(201);
        expect(response.data.batch_summary.successful).toBe(2);
        
        // Store order SNs for cleanup
        response.data.successful_orders.forEach(order => {
            testOrderSns.push(order.order_sn);
        });
    });
    
    test('should list orders with filters', async () => {
        const response = await client.get('/list', {
            params: { test_scenario: 'jest_testing', limit: 10 }
        });
        
        expect(response.status).toBe(200);
        expect(response.data.success).toBe(true);
        expect(Array.isArray(response.data.fake_orders)).toBe(true);
    });
    
    test('should handle validation errors', async () => {
        const invalidOrderData = {
            product_config: {}, // Missing var_sku
            test_scenario: 'jest_error_testing'
        };
        
        await expect(client.post('/generate', invalidOrderData))
            .rejects.toMatchObject({
                response: { status: 400 }
            });
    });
});
```

This comprehensive documentation provides examples for all major use cases of the Universal Fake Order System, from basic order generation to complex integration testing scenarios. Each example includes complete code and explanations to help developers integrate the system effectively into their testing workflows.