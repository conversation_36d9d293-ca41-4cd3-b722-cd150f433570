# Plugin Development Guidelines

This document provides comprehensive guidelines for developing plugins in the SteamCodeTool system, covering lifecycle management, best practices, and development workflows.

## 🚀 Plugin Development Lifecycle

### 1. Planning Phase
- **Requirements Analysis**: Define plugin functionality and scope
- **Dependency Review**: Identify required plugins and external dependencies
- **Architecture Design**: Plan service structure and API endpoints
- **Configuration Planning**: Define configuration schema and defaults

### 2. Development Phase
- **Setup**: Create plugin structure from template
- **Core Implementation**: Implement plugin class and core functionality
- **Service Development**: Build business logic services
- **Route Implementation**: Create API endpoints and web interfaces
- **Testing**: Write and run unit tests

### 3. Integration Phase
- **Configuration**: Set up plugin configuration
- **Registration**: Register plugin with plugin manager
- **Testing**: Integration testing with other plugins
- **Documentation**: Complete plugin documentation

### 4. Deployment Phase
- **Validation**: Final validation and testing
- **Deployment**: Deploy to target environment
- **Monitoring**: Set up monitoring and logging
- **Maintenance**: Ongoing maintenance and updates

## 📋 Development Best Practices

### Core Principles

#### 1. Single Responsibility Principle
```python
# ✅ Good - Each service has a single responsibility
class EmailService:
    def send_email(self, message): pass

class AuthService:
    def authenticate(self, credentials): pass

# ❌ Bad - Service doing too many things
class UtilityService:
    def send_email(self, message): pass
    def authenticate(self, credentials): pass
    def process_payment(self, amount): pass
```

#### 2. Dependency Injection
```python
# ✅ Good - Dependencies injected through constructor
class OrderService:
    def __init__(self, email_service, payment_service):
        self.email_service = email_service
        self.payment_service = payment_service

# ❌ Bad - Hard-coded dependencies
class OrderService:
    def __init__(self):
        self.email_service = EmailService()  # Hard-coded!
        self.payment_service = PaymentService()  # Hard-coded!
```

#### 3. Configuration Management
```python
# ✅ Good - Centralized configuration
class Plugin(PluginInterface):
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.config = self.load_config()
        
    def load_config(self):
        return self.plugin_manager.get_plugin_config(self.name)

# ❌ Bad - Scattered configuration
class SomeService:
    def __init__(self):
        self.api_key = "hardcoded-key"  # Bad!
        self.timeout = 30  # Should be configurable!
```

### Error Handling Standards

#### 1. Comprehensive Exception Handling
```python
def initialize(self) -> bool:
    """Initialize plugin with proper error handling"""
    try:
        self.logger.info(f"Initializing {self.name} plugin...")
        
        # Initialize services
        self._initialize_services()
        
        # Create routes
        self._create_routes()
        
        self.logger.info(f"{self.name} plugin initialized successfully")
        return True
        
    except ConfigurationError as e:
        self.logger.error(f"Configuration error in {self.name}: {e}")
        return False
    except ServiceInitializationError as e:
        self.logger.error(f"Service initialization failed in {self.name}: {e}")
        return False
    except Exception as e:
        self.logger.error(f"Unexpected error initializing {self.name}: {e}")
        self.logger.error(traceback.format_exc())
        return False
```

#### 2. Graceful Degradation
```python
def get_data(self):
    """Get data with fallback options"""
    try:
        # Try primary data source
        return self.primary_service.get_data()
    except PrimaryServiceError:
        self.logger.warning("Primary service failed, trying fallback")
        try:
            # Try fallback data source
            return self.fallback_service.get_data()
        except FallbackServiceError:
            self.logger.error("All data sources failed")
            # Return default/cached data
            return self.get_cached_data()
```

### Logging Standards

#### 1. Structured Logging
```python
import logging

class Plugin(PluginInterface):
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.logger = logging.getLogger(f"plugins.{self.name}")
        
    def process_request(self, request_id, data):
        self.logger.info(
            "Processing request",
            extra={
                "plugin": self.name,
                "request_id": request_id,
                "data_size": len(data)
            }
        )
```

#### 2. Log Levels Usage
```python
# DEBUG - Detailed diagnostic information
self.logger.debug(f"Processing item {item_id} with config {config}")

# INFO - General information about plugin operation
self.logger.info(f"Plugin {self.name} started successfully")

# WARNING - Something unexpected happened but plugin continues
self.logger.warning(f"Retrying failed operation, attempt {attempt}")

# ERROR - Serious problem that prevented operation
self.logger.error(f"Failed to process request: {error}")

# CRITICAL - Very serious error that may cause plugin to stop
self.logger.critical(f"Database connection lost, plugin shutting down")
```

## 🔧 Service Development Guidelines

### Service Base Class Pattern
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging

class BaseService(ABC):
    """Base class for all plugin services"""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self._initialized = False
        self._shutdown = False
    
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the service"""
        pass
    
    @abstractmethod
    def shutdown(self) -> bool:
        """Shutdown the service"""
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        pass
    
    def is_initialized(self) -> bool:
        """Check if service is initialized"""
        return self._initialized and not self._shutdown
    
    def update_config(self, config: Dict[str, Any]):
        """Update service configuration"""
        self.config.update(config)
        self.logger.info("Service configuration updated")
```

### Service Implementation Example
```python
class EmailService(BaseService):
    """Email service implementation"""
    
    def initialize(self) -> bool:
        try:
            self.smtp_host = self.config.get('smtp_host', 'localhost')
            self.smtp_port = self.config.get('smtp_port', 587)
            self.username = self.config.get('username')
            self.password = self.config.get('password')
            
            # Test connection
            self._test_connection()
            
            self._initialized = True
            self.logger.info("Email service initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize email service: {e}")
            return False
    
    def shutdown(self) -> bool:
        try:
            # Cleanup resources
            if hasattr(self, 'connection'):
                self.connection.close()
            
            self._shutdown = True
            self.logger.info("Email service shutdown successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error shutting down email service: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Check email service health"""
        if not self.is_initialized():
            return {"status": "unhealthy", "reason": "Service not initialized"}
        
        try:
            # Perform health check
            self._test_connection()
            return {"status": "healthy", "last_check": datetime.now().isoformat()}
        except Exception as e:
            return {"status": "unhealthy", "reason": str(e)}
```

## 🌐 Route Development Guidelines

### Blueprint Creation Pattern
```python
from flask import Blueprint, jsonify, request, abort
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..plugin import Plugin

def create_plugin_blueprint(plugin: 'Plugin') -> Blueprint:
    """Create plugin blueprint with standard endpoints"""
    
    bp = Blueprint(plugin.name, __name__)
    
    # Standard health check endpoint
    @bp.route('/health', methods=['GET'])
    def health_check():
        """Plugin health check"""
        try:
            status = plugin.get_health_status()
            return jsonify(status)
        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500
    
    # Standard info endpoint
    @bp.route('/info', methods=['GET'])
    def plugin_info():
        """Plugin information"""
        return jsonify(plugin.get_info())
    
    # Business logic endpoints
    @bp.route('/api/data', methods=['GET'])
    def get_data():
        """Get plugin data"""
        try:
            data = plugin.services['data'].get_all()
            return jsonify({"data": data, "status": "success"})
        except Exception as e:
            plugin.logger.error(f"Error getting data: {e}")
            return jsonify({"error": str(e)}), 500
    
    @bp.route('/api/data', methods=['POST'])
    def create_data():
        """Create new data"""
        try:
            data = request.get_json()
            if not data:
                abort(400, description="No data provided")
            
            result = plugin.services['data'].create(data)
            return jsonify({"result": result, "status": "success"}), 201
            
        except ValidationError as e:
            return jsonify({"error": str(e)}), 400
        except Exception as e:
            plugin.logger.error(f"Error creating data: {e}")
            return jsonify({"error": str(e)}), 500
    
    return bp
```

### API Response Standards
```python
# Success Response
{
    "status": "success",
    "data": {...},
    "message": "Operation completed successfully"
}

# Error Response
{
    "status": "error",
    "error": "Error description",
    "code": "ERROR_CODE",
    "details": {...}
}

# Validation Error Response
{
    "status": "validation_error",
    "errors": [
        {"field": "email", "message": "Invalid email format"},
        {"field": "age", "message": "Must be a positive integer"}
    ]
}
```

## 🧪 Testing Guidelines

### Unit Test Structure
```python
import unittest
from unittest.mock import Mock, patch
from plugins.your_plugin.plugin import Plugin
from plugins.your_plugin.services.email_service import EmailService

class TestEmailService(unittest.TestCase):
    def setUp(self):
        self.config = {
            'smtp_host': 'test.example.com',
            'smtp_port': 587,
            'username': '<EMAIL>',
            'password': 'testpass'
        }
        self.service = EmailService(self.config)
    
    def test_initialization(self):
        """Test service initialization"""
        with patch.object(self.service, '_test_connection'):
            result = self.service.initialize()
            self.assertTrue(result)
            self.assertTrue(self.service.is_initialized())
    
    def test_send_email(self):
        """Test email sending"""
        with patch.object(self.service, '_send_smtp_email') as mock_send:
            mock_send.return_value = True
            
            result = self.service.send_email('<EMAIL>', 'Subject', 'Body')
            self.assertTrue(result)
            mock_send.assert_called_once()
```

## 📚 Documentation Requirements

### Plugin README Template
```markdown
# Plugin Name

Brief description of what the plugin does.

## Features
- Feature 1
- Feature 2
- Feature 3

## Configuration
```json
{
  "enabled": true,
  "service_config": {
    "timeout": 30
  }
}
```

## API Endpoints
- `GET /api/plugin/health` - Health check
- `GET /api/plugin/data` - Get data
- `POST /api/plugin/data` - Create data

## Installation
1. Copy plugin to plugins directory
2. Update configuration
3. Restart application

## Troubleshooting
Common issues and solutions.
```

This covers the essential development guidelines. The next standards documents will cover configuration, testing, documentation, and code style in detail.
