{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Sync Configurations</h2>
                <a href="{{ url_for('vpn.configurations') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Configurations
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Sync Settings</h3>
                </div>
                <form method="POST" action="{{ url_for('vpn.sync_configurations') }}">
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> 
                            Synchronization ensures that your database and server configurations are in sync.
                            Choose the sync direction carefully.
                        </div>
                        
                        <div class="form-group">
                            <label>Select Servers to Sync:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select-all">
                                <label class="form-check-label" for="select-all">
                                    <strong>Select All Servers</strong>
                                </label>
                            </div>
                            <hr>
                            {% for server in servers %}
                            <div class="form-check">
                                <input class="form-check-input server-checkbox" type="checkbox" 
                                       name="server_ids" value="{{ server.id }}" id="server-{{ server.id }}">
                                <label class="form-check-label" for="server-{{ server.id }}">
                                    {{ server.name }} ({{ server.host }})
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="form-group">
                            <label for="sync_direction">Sync Direction:</label>
                            <select class="form-control" id="sync_direction" name="sync_direction" required>
                                <option value="config_to_db">Server Config → Database (Import from servers)</option>
                                <option value="db_to_config">Database → Server Config (Export to servers)</option>
                            </select>
                            <small class="form-text text-muted">
                                Choose the direction of synchronization carefully. This will overwrite data in the destination.
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="remove_orphaned" name="remove_orphaned">
                                <label class="custom-control-label" for="remove_orphaned">
                                    Remove orphaned entries
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                Remove entries that exist in the destination but not in the source
                            </small>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            <strong>Warning:</strong> This operation will modify your configurations. 
                            Make sure you have a backup before proceeding.
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary" onclick="return confirmSync()">
                            <i class="fas fa-sync"></i> Start Sync
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewSync()">
                            <i class="fas fa-eye"></i> Preview Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Sync Preview</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="preview-content">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Loading preview...
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Select all checkbox
$('#select-all').change(function() {
    $('.server-checkbox').prop('checked', $(this).prop('checked'));
});

// Update select all when individual checkboxes change
$('.server-checkbox').change(function() {
    var allChecked = $('.server-checkbox:checked').length === $('.server-checkbox').length;
    $('#select-all').prop('checked', allChecked);
});

function confirmSync() {
    var selectedServers = $('.server-checkbox:checked').length;
    if (selectedServers === 0) {
        alert('Please select at least one server to sync');
        return false;
    }
    
    var direction = $('#sync_direction').val();
    var directionText = direction === 'config_to_db' ? 
        'from server configurations to database' : 
        'from database to server configurations';
    
    return confirm(`Are you sure you want to sync ${selectedServers} server(s) ${directionText}?`);
}

function previewSync() {
    var selectedServers = $('.server-checkbox:checked').map(function() {
        return $(this).val();
    }).get();
    
    if (selectedServers.length === 0) {
        alert('Please select at least one server to preview');
        return;
    }
    
    $('#previewModal').modal('show');
    
    // Load preview for each selected server
    var previewHtml = '';
    var completed = 0;
    
    selectedServers.forEach(function(serverId) {
        $.get(`/admin/vpn/api/sync/compare/${serverId}`, function(data) {
            previewHtml += generatePreviewHtml(serverId, data);
            completed++;
            
            if (completed === selectedServers.length) {
                $('#preview-content').html(previewHtml);
            }
        }).fail(function() {
            previewHtml += `<div class="alert alert-danger">Failed to load preview for server ${serverId}</div>`;
            completed++;
            
            if (completed === selectedServers.length) {
                $('#preview-content').html(previewHtml);
            }
        });
    });
}

function generatePreviewHtml(serverId, data) {
    var html = `<div class="mb-4">
        <h5>Server: ${data.server_name} (ID: ${serverId})</h5>`;
    
    if (data.in_sync) {
        html += '<div class="alert alert-success">Configuration is already in sync!</div>';
    } else {
        html += `
        <div class="row">
            <div class="col-md-6">
                <h6>Only in Config (${data.only_in_config.length})</h6>
                <ul class="list-unstyled">`;
        
        data.only_in_config.forEach(function(client) {
            html += `<li><i class="fas fa-plus text-success"></i> ${client.email}</li>`;
        });
        
        html += `</ul>
            </div>
            <div class="col-md-6">
                <h6>Only in Database (${data.only_in_database.length})</h6>
                <ul class="list-unstyled">`;
        
        data.only_in_database.forEach(function(client) {
            html += `<li><i class="fas fa-minus text-danger"></i> ${client.email}</li>`;
        });
        
        html += `</ul>
            </div>
        </div>`;
        
        if (data.mismatched.length > 0) {
            html += `<h6>Mismatched (${data.mismatched.length})</h6>
                     <ul class="list-unstyled">`;
            data.mismatched.forEach(function(mismatch) {
                html += `<li><i class="fas fa-exclamation-triangle text-warning"></i> ${mismatch.email}</li>`;
            });
            html += '</ul>';
        }
    }
    
    html += '</div><hr>';
    return html;
}
</script>
{% endblock %}