"""
SteamCodeTool Plugin Manager
Manages plugin lifecycle, registration, and communication
"""

import os
import sys
import json
import importlib
import logging
from typing import Dict, List, Any, Optional, Type
from abc import ABC, abstractmethod
from flask import Flask, Blueprint
from threading import Lock
import traceback

logger = logging.getLogger(__name__)

class DashboardWidget:
    """Represents a dashboard widget configuration"""
    def __init__(self, widget_id: str, title: str, position: str = "main", 
                 order: int = 100, size: str = "medium"):
        self.widget_id = widget_id
        self.title = title
        self.position = position  # main, sidebar, header, footer
        self.order = order  # Display order (lower = higher priority)
        self.size = size  # small, medium, large, full
        self.script_path = f"/static/js/widgets/{widget_id}.js"
        self.template_id = f"widget-{widget_id}"
        self.data_endpoint = None
        self.refresh_interval = None  # In seconds, None = no auto-refresh
        self.dependencies = []  # Other widgets this depends on
        self.permissions = []  # Required permissions to view this widget

class PluginInterface(ABC):
    """Base interface that all plugins must implement"""
    
    def __init__(self, plugin_manager: 'PluginManager'):
        self.plugin_manager = plugin_manager
        self.name = self.__class__.__name__
        self.version = "1.0.0"
        self.description = ""
        self.dependencies = []
        self.config = {}
        self.enabled = True
        
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the plugin. Return True if successful."""
        pass
        
    @abstractmethod
    def shutdown(self) -> bool:
        """Shutdown the plugin. Return True if successful."""
        pass
        
    @abstractmethod
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return Flask blueprint for this plugin's routes"""
        pass
        
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """Return configuration schema for this plugin"""
        pass
        
    def get_dashboard_widgets(self) -> List[DashboardWidget]:
        """
        Return a list of dashboard widgets provided by this plugin.
        Override this method to provide custom dashboard widgets.
        """
        return []
        
    def get_widget_data(self, widget_id: str) -> Dict[str, Any]:
        """
        Return data for a specific widget.
        This method is called when a widget needs to fetch its data.
        """
        return {}
        
    def get_info(self) -> Dict[str, Any]:
        """Return plugin information"""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'dependencies': self.dependencies,
            'enabled': self.enabled
        }
        
    def load_config(self, config: Dict[str, Any]):
        """Load plugin configuration"""
        self.config = config
        
    def get_status(self) -> Dict[str, Any]:
        """Return plugin status information"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'initialized': hasattr(self, '_initialized') and self._initialized
        }

class PluginManager:
    """Manages all plugins in the system"""
    
    def __init__(self, app: Flask, plugins_dir: str = "plugins"):
        self.app = app
        self.plugins_dir = plugins_dir
        self.plugins: Dict[str, PluginInterface] = {}
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}
        self.plugin_methods: Dict[str, Dict[str, Any]] = {}  # For inter-plugin communication
        self.lock = Lock()
        self._ensure_plugins_dir()
        
    def _ensure_plugins_dir(self):
        """Ensure plugins directory exists"""
        if not os.path.exists(self.plugins_dir):
            os.makedirs(self.plugins_dir)
            
        # Add plugins directory to Python path
        if self.plugins_dir not in sys.path:
            sys.path.insert(0, self.plugins_dir)
            
    def load_plugin_configs(self, config_file: str = "configs/core/plugin_config.json"):
        """Load plugin configurations from file"""
        config_path = os.path.join(os.path.dirname(__file__), '..', config_file)
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.plugin_configs = json.load(f)
                logger.info(f"Loaded plugin configurations from {config_path}")
            else:
                self.plugin_configs = {}
                logger.info("No plugin configuration file found, using defaults")
        except Exception as e:
            logger.error(f"Error loading plugin configurations: {e}")
            self.plugin_configs = {}

    def save_plugin_configs(self, config_file: str = "configs/core/plugin_config.json"):
        """Save plugin configurations to file"""
        config_path = os.path.join(os.path.dirname(__file__), '..', config_file)
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.plugin_configs, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved plugin configurations to {config_path}")
        except Exception as e:
            logger.error(f"Error saving plugin configurations: {e}")
            
    def discover_plugins(self) -> List[str]:
        """Discover available plugins in the plugins directory"""
        plugins = []
        for item in os.listdir(self.plugins_dir):
            plugin_path = os.path.join(self.plugins_dir, item)
            if os.path.isdir(plugin_path):
                # Check if it's a valid plugin directory
                init_file = os.path.join(plugin_path, '__init__.py')
                plugin_file = os.path.join(plugin_path, 'plugin.py')
                if os.path.exists(init_file) and os.path.exists(plugin_file):
                    plugins.append(item)
        return plugins
        
    def load_plugin(self, plugin_name: str) -> bool:
        """Load a specific plugin"""
        with self.lock:
            try:
                if plugin_name in self.plugins:
                    logger.warning(f"Plugin {plugin_name} is already loaded")
                    return True
                    
                # Import the plugin module
                module_name = f"{plugin_name}.plugin"
                plugin_module = importlib.import_module(module_name)
                
                # Get the plugin class (should be named Plugin)
                if not hasattr(plugin_module, 'Plugin'):
                    logger.error(f"Plugin {plugin_name} does not have a Plugin class")
                    return False
                    
                plugin_class = getattr(plugin_module, 'Plugin')
                
                # Instantiate the plugin
                plugin_instance = plugin_class(self)
                
                # Load plugin configuration
                plugin_config = self.plugin_configs.get(plugin_name, {})
                plugin_instance.load_config(plugin_config)
                
                # Initialize the plugin
                if not plugin_instance.initialize():
                    logger.error(f"Failed to initialize plugin {plugin_name}")
                    return False
                    
                # Register plugin routes
                blueprint = plugin_instance.get_blueprint()
                if blueprint:
                    # Check if plugin specifies custom URL prefix
                    custom_prefix = getattr(plugin_instance, 'url_prefix', None)
                    if custom_prefix:
                        self.app.register_blueprint(blueprint, url_prefix=custom_prefix)
                        logger.info(f"Registered blueprint for plugin {plugin_name} with custom prefix {custom_prefix}")
                    else:
                        self.app.register_blueprint(blueprint, url_prefix=f'/api/{plugin_name}')
                        logger.info(f"Registered blueprint for plugin {plugin_name} with default prefix /api/{plugin_name}")
                    
                # Store the plugin
                self.plugins[plugin_name] = plugin_instance
                plugin_instance._initialized = True
                
                logger.info(f"Successfully loaded plugin: {plugin_name}")
                return True
                
            except Exception as e:
                logger.error(f"Error loading plugin {plugin_name}: {e}")
                logger.error(traceback.format_exc())
                return False
                
    def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a specific plugin"""
        with self.lock:
            try:
                if plugin_name not in self.plugins:
                    logger.warning(f"Plugin {plugin_name} is not loaded")
                    return True
                    
                plugin = self.plugins[plugin_name]
                
                # Shutdown the plugin
                if not plugin.shutdown():
                    logger.warning(f"Plugin {plugin_name} shutdown returned False")
                    
                # Remove from plugins dict
                del self.plugins[plugin_name]
                
                logger.info(f"Successfully unloaded plugin: {plugin_name}")
                return True
                
            except Exception as e:
                logger.error(f"Error unloading plugin {plugin_name}: {e}")
                return False
                
    def load_all_plugins(self):
        """Load all discovered plugins"""
        plugins = self.discover_plugins()
        logger.info(f"Discovered plugins: {plugins}")
        
        for plugin_name in plugins:
            # Check if plugin is enabled in config
            plugin_config = self.plugin_configs.get(plugin_name, {})
            if plugin_config.get('enabled', True):
                self.load_plugin(plugin_name)
            else:
                logger.info(f"Plugin {plugin_name} is disabled in configuration")
                
    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """Get a loaded plugin by name"""
        return self.plugins.get(plugin_name)
        
    def get_all_plugins(self) -> Dict[str, PluginInterface]:
        """Get all loaded plugins"""
        return self.plugins.copy()
        
    def get_plugin_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all plugins"""
        status = {}
        for name, plugin in self.plugins.items():
            status[name] = plugin.get_status()
        return status
        
    def enable_plugin(self, plugin_name: str) -> bool:
        """Enable a plugin"""
        if plugin_name not in self.plugin_configs:
            self.plugin_configs[plugin_name] = {}
        self.plugin_configs[plugin_name]['enabled'] = True
        self.save_plugin_configs()
        
        # Load the plugin if it's not already loaded
        if plugin_name not in self.plugins:
            return self.load_plugin(plugin_name)
        return True
        
    def disable_plugin(self, plugin_name: str) -> bool:
        """Disable a plugin"""
        if plugin_name not in self.plugin_configs:
            self.plugin_configs[plugin_name] = {}
        self.plugin_configs[plugin_name]['enabled'] = False
        self.save_plugin_configs()
        
        # Unload the plugin if it's loaded
        if plugin_name in self.plugins:
            return self.unload_plugin(plugin_name)
        return True
        
    def update_plugin_config(self, plugin_name: str, config: Dict[str, Any]) -> bool:
        """Update plugin configuration"""
        try:
            self.plugin_configs[plugin_name] = config
            self.save_plugin_configs()
            
            # Reload plugin configuration if it's loaded
            if plugin_name in self.plugins:
                self.plugins[plugin_name].load_config(config)
                
            return True
        except Exception as e:
            logger.error(f"Error updating plugin config for {plugin_name}: {e}")
            return False
            
    def shutdown_all_plugins(self):
        """Shutdown all loaded plugins"""
        for plugin_name in list(self.plugins.keys()):
            self.unload_plugin(plugin_name)

    def register_plugin_method(self, plugin_name: str, method_name: str, method_callable) -> bool:
        """Register a method for inter-plugin communication"""
        try:
            if plugin_name not in self.plugin_methods:
                self.plugin_methods[plugin_name] = {}

            self.plugin_methods[plugin_name][method_name] = method_callable
            logger.debug(f"Registered method {method_name} for plugin {plugin_name}")
            return True
        except Exception as e:
            logger.error(f"Error registering plugin method {plugin_name}.{method_name}: {e}")
            return False

    def call_plugin_method(self, plugin_name: str, method_name: str, *args, **kwargs) -> Any:
        """Call a method from another plugin"""
        try:
            if plugin_name not in self.plugin_methods:
                raise ValueError(f"Plugin {plugin_name} has no registered methods")

            if method_name not in self.plugin_methods[plugin_name]:
                raise ValueError(f"Method {method_name} not found in plugin {plugin_name}")

            method = self.plugin_methods[plugin_name][method_name]
            return method(*args, **kwargs)

        except Exception as e:
            logger.error(f"Error calling plugin method {plugin_name}.{method_name}: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def is_plugin_enabled(self, plugin_name: str) -> bool:
        """Check if a plugin is enabled and loaded"""
        return plugin_name in self.plugins and self.plugins[plugin_name].enabled

    def get_all_dashboard_widgets(self) -> List[Dict[str, Any]]:
        """Get all dashboard widgets from enabled plugins"""
        all_widgets = []
        
        for plugin_name, plugin in self.plugins.items():
            if not plugin.enabled:
                continue
                
            try:
                widgets = plugin.get_dashboard_widgets()
                for widget in widgets:
                    widget_data = {
                        'widget_id': widget.widget_id,
                        'plugin_name': plugin_name,
                        'title': widget.title,
                        'position': widget.position,
                        'order': widget.order,
                        'size': widget.size,
                        'script_path': widget.script_path,
                        'template_id': widget.template_id,
                        'data_endpoint': widget.data_endpoint or f'/api/{plugin_name}/widget/{widget.widget_id}',
                        'refresh_interval': widget.refresh_interval,
                        'dependencies': widget.dependencies,
                        'permissions': widget.permissions
                    }
                    all_widgets.append(widget_data)
            except Exception as e:
                logger.error(f"Error getting widgets from plugin {plugin_name}: {e}")
                
        # Sort widgets by position and order
        all_widgets.sort(key=lambda x: (x['position'], x['order']))
        return all_widgets
        
    def get_widget_data(self, plugin_name: str, widget_id: str) -> Dict[str, Any]:
        """Get data for a specific widget from a plugin"""
        try:
            if plugin_name not in self.plugins:
                return {'error': f'Plugin {plugin_name} not found'}
                
            plugin = self.plugins[plugin_name]
            if not plugin.enabled:
                return {'error': f'Plugin {plugin_name} is disabled'}
                
            return plugin.get_widget_data(widget_id)
        except Exception as e:
            logger.error(f"Error getting widget data from {plugin_name}.{widget_id}: {e}")
            return {'error': str(e)}
