#!/bin/bash
# Run script for ShopeeAPI in Baota Panel

# Set the Python path to include the current directory
export PYTHONPATH=/app

# Change to the ShopeeAPI directory
cd /app

# Print environment variables for debugging
echo "Current PORT setting: ${PORT:-8000}"

# Run the application (now we can use api:app directly since we're in the ShopeeAPI directory)
exec uvicorn api:app --host 0.0.0.0 --port ${PORT:-8000}
