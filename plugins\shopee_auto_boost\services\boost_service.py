"""
Boost Service for Shopee Auto Boost Plugin

Handles the actual product boosting logic via local ShopeeAPI service.
"""

import logging
import json
import os
import requests
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import random

logger = logging.getLogger(__name__)


class BoostService:
    """Service for boosting products via local ShopeeAPI"""

    def __init__(self, shopee_plugin, config: Dict[str, Any]):
        # shopee_plugin is now unused (kept for compatibility)
        self.shopee_plugin = shopee_plugin
        self.config = config
        self.api_base_url = config.get("shopee_api_url", "http://localhost:8000")
        self.session = requests.Session()
        # Set timeout for API calls
        self.timeout = 30

    def boost_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Boost multiple products via local ShopeeAPI"""
        try:
            # Extract product IDs
            product_ids = [product.get("id") for product in products if product.get("id")]

            if not product_ids:
                logger.warning("No valid product IDs found for boosting")
                return {
                    "success": [],
                    "failed": [],
                    "total_attempted": 0
                }

            # Call local API to boost multiple products
            url = f"{self.api_base_url}/products/boost"
            payload = {"product_ids": product_ids}

            response = self.session.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()

            data = response.json()
            results = data.get("data", {})

            # Add product names to results for better logging
            product_map = {product.get("id"): product.get("name", "Unknown") for product in products}

            for success_item in results.get("success", []):
                product_id = success_item.get("id")
                success_item["name"] = product_map.get(product_id, "Unknown")
                success_item["boosted_at"] = datetime.now().isoformat()
                logger.info(f"Successfully boosted product: {success_item['name']} (ID: {product_id})")

            for failed_item in results.get("failed", []):
                product_id = failed_item.get("id")
                failed_item["name"] = product_map.get(product_id, "Unknown")
                logger.warning(f"Failed to boost product: {failed_item['name']} (ID: {product_id}) - {failed_item.get('error', 'Unknown error')}")

            return results

        except Exception as e:
            logger.error(f"Error boosting products via local API: {e}")
            return {
                "success": [],
                "failed": [{"id": product.get("id"), "name": product.get("name", "Unknown"), "error": str(e)} for product in products],
                "total_attempted": len(products)
            }

    def boost_single_product(self, product_id: int) -> bool:
        """Boost a single product via local ShopeeAPI"""
        try:
            url = f"{self.api_base_url}/products/{product_id}/boost"

            response = self.session.post(url, timeout=self.timeout)
            response.raise_for_status()

            data = response.json()
            success = data.get("success", False)

            if success:
                logger.info(f"Successfully boosted product {product_id}")
            else:
                logger.warning(f"Failed to boost product {product_id}: {data.get('message', 'Unknown error')}")

            return success

        except Exception as e:
            logger.error(f"Error boosting product {product_id} via local API: {e}")
            return False

    def select_products_for_boost(self, products: List[Dict[str, Any]], boost_history: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Select products for boosting with strict pinned-first priority and smart fill.

        Rules:
        - Always select PINNED products first (up to slot limit)
        - If slots remain, fill with normal selection from NON-PINNED products
        - If pinned count >= slots, do NOT include any non-pinned products
        """
        products_per_boost = self.config.get("products_per_boost", 5)

        pinned_config = self.config.get("pinned_products", {})
        pinned_ids = set(pinned_config.get("product_ids", []) or [])

        # 1) Select pinned products first (if any are configured and available)
        pinned_selected: List[Dict[str, Any]] = []
        if pinned_ids:
            pinned_selected = self._select_pinned_products_for_boost(
                products, boost_history, products_per_boost
            )

        # If pinned meet or exceed the slot limit, return them directly
        if len(pinned_selected) >= products_per_boost:
            return pinned_selected[:products_per_boost]

        # 2) If we still have room, fill with normal selection from NON-PINNED products
        remaining_slots = products_per_boost - len(pinned_selected)
        if remaining_slots <= 0:
            return pinned_selected

        # Filter out pinned products from the candidate pool for normal selection
        non_pinned_candidates = [p for p in products if p.get("id") not in pinned_ids]

        # Original strategies apply for the remaining slots
        strategy = self.config.get("rotation_strategy", "least_recently_boosted")

        if len(non_pinned_candidates) <= remaining_slots:
            filler = non_pinned_candidates
        else:
            if strategy == "random":
                filler = random.sample(non_pinned_candidates, remaining_slots)
            elif strategy == "highest_sold":
                filler = self._select_by_highest_sold(non_pinned_candidates, remaining_slots)
            else:  # least_recently_boosted (default)
                filler = self._select_by_least_recently_boosted(non_pinned_candidates, boost_history, remaining_slots)

        # Combine pinned-first with filler to reach the slot limit
        return pinned_selected + filler

    def _select_by_least_recently_boosted(self, products: List[Dict[str, Any]], 
                                        boost_history: Dict[str, Any], 
                                        count: int) -> List[Dict[str, Any]]:
        """Select products that were boosted least recently"""
        # Get last boost times for each product
        product_boost_times = {}
        for product in products:
            product_id = str(product.get("id"))
            last_boost = boost_history.get("product_history", {}).get(product_id, {}).get("last_boosted")
            if last_boost:
                product_boost_times[product_id] = datetime.fromisoformat(last_boost)
            else:
                # Never boosted - highest priority
                product_boost_times[product_id] = datetime.min
        
        # Sort by last boost time (oldest first)
        sorted_products = sorted(products, 
                               key=lambda p: product_boost_times.get(str(p.get("id")), datetime.min))
        
        return sorted_products[:count]

    def _select_by_highest_sold(self, products: List[Dict[str, Any]], count: int) -> List[Dict[str, Any]]:
        """Select products with highest sold count"""
        sorted_products = sorted(products, 
                               key=lambda p: p.get("statistics", {}).get("sold_count", 0), 
                               reverse=True)
        return sorted_products[:count]

    def update_boost_history(self, boost_results: Dict[str, Any], history_file: str) -> None:
        """Update boost history with latest results"""
        try:
            # Load existing history
            history = self._load_boost_history(history_file)
            
            # Update history with new boosts
            now = datetime.now().isoformat()
            
            for success_item in boost_results.get("success", []):
                product_id = success_item.get("id")

                # Update legacy product history
                if "product_history" not in history:
                    history["product_history"] = {}

                if str(product_id) not in history["product_history"]:
                    history["product_history"][str(product_id)] = {
                        "total_boosts": 0,
                        "first_boosted": now,
                        "last_boosted": None
                    }

                history["product_history"][str(product_id)]["total_boosts"] += 1
                history["product_history"][str(product_id)]["last_boosted"] = now

                # Update per-product cooldown (new PIN functionality)
                self.update_product_cooldown(product_id, True, history)

            # Update failed boosts
            for failed_item in boost_results.get("failed", []):
                product_id = failed_item.get("id")

                # Update cooldown tracking for failed attempts (don't start cooldown)
                cooldowns = history.setdefault("product_cooldowns", {})
                product_key = str(product_id)

                if product_key not in cooldowns:
                    cooldowns[product_key] = {
                        "total_successful_boosts": 0,
                        "total_failed_attempts": 0
                    }

                cooldowns[product_key]["total_failed_attempts"] = cooldowns[product_key].get("total_failed_attempts", 0) + 1
            
            # Update global stats
            if "global_stats" not in history:
                history["global_stats"] = {
                    "total_boost_sessions": 0,
                    "total_products_boosted": 0,
                    "total_failures": 0,
                    "first_boost_session": now,
                    "last_boost_session": None
                }
            
            history["global_stats"]["total_boost_sessions"] += 1
            history["global_stats"]["total_products_boosted"] += len(boost_results.get("success", []))
            history["global_stats"]["total_failures"] += len(boost_results.get("failed", []))
            history["global_stats"]["last_boost_session"] = now
            
            # Save updated history
            self._save_boost_history(history, history_file)
            
        except Exception as e:
            logger.error(f"Error updating boost history: {e}")

    def _select_pinned_products_for_boost(self, products: List[Dict[str, Any]], boost_history: Dict[str, Any], max_products: int) -> List[Dict[str, Any]]:
        """Select pinned products with absolute priority, ignoring other products"""
        try:
            pinned_config = self.config.get("pinned_products", {})
            pinned_product_ids = pinned_config.get("product_ids", [])

            if not pinned_product_ids:
                logger.warning("No products are pinned for auto-boost")
                return []

            # Filter products to only include pinned ones that have available boost slots
            pinned_products = []
            for product in products:
                product_id = product.get("id")
                if product_id in pinned_product_ids:
                    # Check if boost slot is available (this was already filtered in get_boostable_products)
                    if product.get("boost_slot_available", True):
                        pinned_products.append(product)
                    else:
                        logger.debug(f"Pinned product {product.get('name', 'Unknown')} (ID: {product_id}) has no available boost slot")

            if not pinned_products:
                logger.warning("None of the pinned products have available boost slots")
                return []

            # Filter out products that are still on plugin-level cooldown
            available_products = []
            for product in pinned_products:
                if not self._is_product_on_cooldown(product.get("id"), boost_history):
                    available_products.append(product)
                else:
                    logger.debug(f"Pinned product {product.get('name', 'Unknown')} (ID: {product.get('id')}) is on plugin cooldown")

            if not available_products:
                logger.info("All pinned products are either in boost slots or on plugin cooldown")
                return []

            # Prioritize pinned products by their order in the pinned list
            available_products.sort(key=lambda p: pinned_product_ids.index(p.get("id")))
            
            # Limit to max_products but prioritize pinned products
            selected_products = available_products[:max_products]
            
            logger.info(f"Selected {len(selected_products)} pinned products for priority boosting")
            for product in selected_products:
                logger.info(f"  - Priority: {product.get('name', 'Unknown')} (ID: {product.get('id')})")
                
            return selected_products

        except Exception as e:
            logger.error(f"Error selecting pinned products: {e}")
            return []

    def _is_product_on_cooldown(self, product_id: int, boost_history: Dict[str, Any]) -> bool:
        """Check if a product is currently on cooldown"""
        try:
            cooldowns = boost_history.get("product_cooldowns", {})
            product_cooldown = cooldowns.get(str(product_id), {})

            if not product_cooldown.get("last_successful_boost"):
                return False  # Never boosted, not on cooldown

            next_available = product_cooldown.get("next_available_boost")
            if not next_available:
                return False  # No next available time set

            next_available_dt = datetime.fromisoformat(next_available)
            current_time = datetime.now()

            is_on_cooldown = current_time < next_available_dt
            if is_on_cooldown:
                remaining_time = next_available_dt - current_time
                logger.debug(f"Product {product_id} on cooldown for {remaining_time}")

            return is_on_cooldown

        except Exception as e:
            logger.error(f"Error checking cooldown for product {product_id}: {e}")
            return False  # If error, assume not on cooldown

    def update_product_cooldown(self, product_id: int, success: bool, boost_history: Dict[str, Any]) -> None:
        """Update cooldown information for a specific product"""
        try:
            if not success:
                return  # Only update cooldown for successful boosts

            cooldowns = boost_history.setdefault("product_cooldowns", {})
            product_key = str(product_id)

            current_time = datetime.now()
            cooldown_hours = self.config.get("pinned_products", {}).get("cooldown_hours", 4)
            next_available = current_time + timedelta(hours=cooldown_hours)

            if product_key not in cooldowns:
                cooldowns[product_key] = {
                    "total_successful_boosts": 0,
                    "total_failed_attempts": 0
                }

            cooldowns[product_key].update({
                "last_successful_boost": current_time.isoformat(),
                "next_available_boost": next_available.isoformat(),
                "total_successful_boosts": cooldowns[product_key].get("total_successful_boosts", 0) + 1
            })

            logger.info(f"Updated cooldown for product {product_id}, next available: {next_available}")

        except Exception as e:
            logger.error(f"Error updating cooldown for product {product_id}: {e}")

    def get_pinned_products_status(self, boost_history: Dict[str, Any]) -> Dict[str, Any]:
        """Get status of all pinned products including cooldown information"""
        try:
            pinned_config = self.config.get("pinned_products", {})
            pinned_product_ids = pinned_config.get("product_ids", [])
            cooldowns = boost_history.get("product_cooldowns", {})

            status = {
                "pinned_products": [],
                "total_pinned": len(pinned_product_ids),
                "available_for_boost": 0,
                "on_cooldown": 0
            }

            current_time = datetime.now()

            for product_id in pinned_product_ids:
                product_key = str(product_id)
                cooldown_info = cooldowns.get(product_key, {})

                is_on_cooldown = self._is_product_on_cooldown(product_id, boost_history)

                product_status = {
                    "product_id": product_id,
                    "on_cooldown": is_on_cooldown,
                    "last_successful_boost": cooldown_info.get("last_successful_boost"),
                    "next_available_boost": cooldown_info.get("next_available_boost"),
                    "total_successful_boosts": cooldown_info.get("total_successful_boosts", 0),
                    "total_failed_attempts": cooldown_info.get("total_failed_attempts", 0)
                }

                if is_on_cooldown:
                    status["on_cooldown"] += 1
                else:
                    status["available_for_boost"] += 1

                status["pinned_products"].append(product_status)

            return status

        except Exception as e:
            logger.error(f"Error getting pinned products status: {e}")
            return {"error": str(e)}

    def _load_boost_history(self, history_file: str) -> Dict[str, Any]:
        """Load boost history from file"""
        try:
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading boost history: {e}")
        
        return {}

    def _save_boost_history(self, history: Dict[str, Any], history_file: str) -> None:
        """Save boost history to file"""
        try:
            os.makedirs(os.path.dirname(history_file), exist_ok=True)
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving boost history: {e}")
