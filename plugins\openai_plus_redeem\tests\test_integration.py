"""
Integration Tests for OpenAI Plus Redeem Plugin

Tests end-to-end workflows including:
- Complete order redemption process
- Email verification workflows
- Cooldown management
- Admin operations
- Shopee API integration
- Service coordination
"""

import unittest
import json
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from pathlib import Path

# Import Flask for testing
from flask import Flask

# Import plugin components
from ..plugin import Plugin
from ..services.service_manager import ServiceManager
from ..models.chatgpt_account import ChatGPTAccount, AccountStatus
from ..models.order_redemption import OrderRedemption, RedemptionStatus
from ..models.email_verification import EmailVerification, VerificationStatus
from ..models.account_cooldown import AccountCooldown, CooldownType


class TestPluginIntegration(unittest.TestCase):
    """Test complete plugin integration scenarios"""
    
    def setUp(self):
        """Set up test environment"""
        # Create temporary directory for test data
        self.temp_dir = tempfile.mkdtemp()
        
        # Create Flask app for testing
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        # Mock plugin manager
        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.app = self.app
        
        # Test configuration
        self.test_config = {
            'enabled': True,
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'require_verification': True,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            },
            'cooldown_config': {
                'default_cooldown_hours': 24,
                'max_reset_attempts': 3
            },
            'shopee_config': {
                'message_template': 'Your account: {email}, {password}',
                'auto_send_enabled': True
            },
            'redemption_config': {
                'auto_assign_accounts': True,
                'max_concurrent_users_per_account': 5
            },
            'security_config': {
                'max_redemptions_per_user': 5,
                'enable_abuse_prevention': True
            }
        }
        
        # Initialize plugin
        self.plugin = Plugin(self.mock_plugin_manager)
        
        # Mock data files to use temp directory
        self.data_file_patches = []
        
    def tearDown(self):
        """Clean up test environment"""
        # Stop all patches
        for patcher in self.data_file_patches:
            patcher.stop()
        
        # Clean up temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
        # Shutdown plugin
        if hasattr(self.plugin, 'service_manager') and self.plugin.service_manager:
            self.plugin.service_manager.shutdown_all_services()
    
    def _setup_data_file_mocks(self):
        """Setup mocks for data file operations"""
        # Mock data file paths to use temp directory
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.temp_dir) / filename)
        
        patcher = patch('plugins.openai_plus_redeem.models.utils.get_data_file_path', 
                       side_effect=mock_get_data_file_path)
        patcher.start()
        self.data_file_patches.append(patcher)
        
        # Create initial data files
        initial_data = {
            'accounts': [],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }
        
        files_to_create = [
            'chatgpt_accounts.json',
            'order_redemptions.json', 
            'email_verifications.json',
            'account_cooldowns.json'
        ]
        
        for filename in files_to_create:
            file_path = Path(self.temp_dir) / filename
            with open(file_path, 'w') as f:
                if 'redemptions' in filename:
                    json.dump({'redemptions': [], 'metadata': initial_data['metadata']}, f)
                elif 'verifications' in filename:
                    json.dump({'verification_logs': []}, f)
                elif 'cooldowns' in filename:
                    json.dump({'cooldowns': [], 'metadata': initial_data['metadata']}, f)
                else:
                    json.dump(initial_data, f)
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    @patch('plugins.openai_plus_redeem.services.shopee_messaging_service.ShopeeAPIClient')
    def test_complete_redemption_workflow(self, mock_shopee_api, mock_imaplib):
        """Test complete end-to-end redemption workflow"""
        # Setup data file mocks
        self._setup_data_file_mocks()
        
        # Mock email service
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [b'1'])
        mock_imap.fetch.return_value = ('OK', [(b'1', b'Subject: Verification Code\r\nCode: 123456')])
        
        # Mock Shopee API
        mock_shopee_client = Mock()
        mock_shopee_api.return_value = mock_shopee_client
        mock_shopee_client.send_chat_message.return_value = ({'success': True}, 200)
        
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)
        
        # Add test account to system
        account_service = self.plugin.service_manager.get_service('chatgpt_account')
        test_account = ChatGPTAccount(
            account_id='TEST_ACCOUNT_001',
            email='<EMAIL>',
            password='test_password',
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        account_service.add_account(test_account)
        
        # Test complete redemption process
        order_redeem_service = self.plugin.service_manager.get_service('order_redeem')
        
        # Process redemption
        result = order_redeem_service.process_order_redemption(
            order_id='ORDER_123',
            buyer_username='test_user',
            sku='chatgpt_plus',
            var_sku='chatgpt_5_30'
        )
        
        # Verify redemption success
        self.assertTrue(result['success'])
        self.assertIn('redemption_id', result)
        self.assertIn('account_info', result)
        
        # Verify account assignment
        redemption_service = self.plugin.service_manager.get_service('order_redemption')
        redemption = redemption_service.get_redemption_by_id(result['redemption_id'])
        self.assertIsNotNone(redemption)
        self.assertEqual(redemption.status, RedemptionStatus.COMPLETED)
        
        # Verify account usage updated
        updated_account = account_service.get_account_by_id('TEST_ACCOUNT_001')
        self.assertEqual(updated_account.current_users, 1)
    
    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_email_verification_workflow(self, mock_imaplib):
        """Test email verification workflow"""
        # Setup data file mocks
        self._setup_data_file_mocks()
        
        # Mock email service
        mock_imap = Mock()
        mock_imaplib.IMAP4_SSL.return_value = mock_imap
        mock_imap.login.return_value = ('OK', [])
        mock_imap.search.return_value = ('OK', [b'1'])
        mock_imap.fetch.return_value = ('OK', [(b'1', b'Subject: Verification Code\r\nCode: 123456')])
        
        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)
        
        # Get email service
        email_service = self.plugin.service_manager.get_service('email')
        
        # Test verification code retrieval
        verification_result = email_service.get_verification_code('<EMAIL>')
        
        self.assertTrue(verification_result['success'])
        self.assertEqual(verification_result['code'], '123456')
        
        # Test verification logging
        verification_logs = email_service.get_verification_logs()
        self.assertGreater(len(verification_logs), 0)
        
        # Verify log entry
        latest_log = verification_logs[-1]
        self.assertEqual(latest_log.email, '<EMAIL>')
        self.assertEqual(latest_log.status, VerificationStatus.SUCCESS)

    def test_cooldown_management_workflow(self):
        """Test cooldown management workflow"""
        # Setup data file mocks
        self._setup_data_file_mocks()

        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Get cooldown service
        cooldown_service = self.plugin.service_manager.get_service('cooldown')

        # Test setting cooldown
        cooldown_result = cooldown_service.set_user_cooldown(
            'test_user',
            hours=24,
            cooldown_type=CooldownType.REDEMPTION_LIMIT
        )

        self.assertTrue(cooldown_result['success'])

        # Test checking cooldown status
        is_on_cooldown = cooldown_service.is_user_on_cooldown('test_user')
        self.assertTrue(is_on_cooldown)

        # Test getting cooldown info
        cooldown_info = cooldown_service.get_user_cooldown_info('test_user')
        self.assertIsNotNone(cooldown_info)
        self.assertEqual(cooldown_info.username, 'test_user')
        self.assertEqual(cooldown_info.cooldown_type, CooldownType.REDEMPTION_LIMIT)

        # Test admin cooldown reset
        reset_result = cooldown_service.reset_user_cooldown('test_user', 'admin_reset')
        self.assertTrue(reset_result['success'])

        # Verify cooldown is cleared
        is_on_cooldown_after_reset = cooldown_service.is_user_on_cooldown('test_user')
        self.assertFalse(is_on_cooldown_after_reset)

    @patch('plugins.openai_plus_redeem.services.order_redeem_service.search_order')
    def test_shopee_integration_workflow(self, mock_search_order):
        """Test Shopee API integration workflow"""
        # Setup data file mocks
        self._setup_data_file_mocks()

        # Mock Shopee API response
        mock_search_order.return_value = {
            'data': {
                'card_list': [{
                    'order_card': {
                        'status_info': {'status': 'Completed'},
                        'card_header': {
                            'order_sn': 'ORDER_123',
                            'buyer_username': 'test_user'
                        },
                        'order_ext_info': {}
                    }
                }]
            }
        }

        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Get order redeem service
        order_redeem_service = self.plugin.service_manager.get_service('order_redeem')

        # Test Shopee order validation
        validation_result = order_redeem_service._validate_order_with_shopee(
            'ORDER_123', 'test_user'
        )

        self.assertTrue(validation_result['valid'])
        self.assertIn('order_info', validation_result)
        self.assertEqual(validation_result['order_info']['status'], 'Completed')

    def test_admin_operations_workflow(self):
        """Test admin operations workflow"""
        # Setup data file mocks
        self._setup_data_file_mocks()

        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Get services
        account_service = self.plugin.service_manager.get_service('chatgpt_account')
        redemption_service = self.plugin.service_manager.get_service('order_redemption')
        cooldown_service = self.plugin.service_manager.get_service('cooldown')

        # Test admin account management
        test_account = ChatGPTAccount(
            account_id='ADMIN_TEST_001',
            email='<EMAIL>',
            password='admin_password',
            max_concurrent_users=3,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )

        # Add account
        add_result = account_service.add_account(test_account)
        self.assertTrue(add_result['success'])

        # Update account
        test_account.max_concurrent_users = 5
        update_result = account_service.update_account(test_account)
        self.assertTrue(update_result['success'])

        # Verify update
        updated_account = account_service.get_account_by_id('ADMIN_TEST_001')
        self.assertEqual(updated_account.max_concurrent_users, 5)

        # Test bulk operations
        accounts = account_service.get_all_accounts()
        self.assertGreater(len(accounts), 0)

        # Test admin statistics
        stats = {
            'total_accounts': len(account_service.get_all_accounts()),
            'active_accounts': len(account_service.get_accounts_by_status(AccountStatus.ACTIVE)),
            'total_redemptions': len(redemption_service.get_all_redemptions()),
            'active_cooldowns': len(cooldown_service.get_active_cooldowns())
        }

        self.assertIsInstance(stats['total_accounts'], int)
        self.assertGreaterEqual(stats['active_accounts'], 0)

    def test_service_coordination_and_health(self):
        """Test service coordination and health monitoring"""
        # Setup data file mocks
        self._setup_data_file_mocks()

        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Test service manager health
        service_manager = self.plugin.service_manager
        self.assertTrue(service_manager.is_initialized())

        # Test individual service health
        services = ['chatgpt_account', 'order_redemption', 'email', 'cooldown', 'order_redeem']

        for service_name in services:
            service = service_manager.get_service(service_name)
            self.assertIsNotNone(service, f"Service {service_name} should be available")

            health = service.health_check()
            self.assertEqual(health['status'], 'healthy', f"Service {service_name} should be healthy")

        # Test service dependencies
        order_redeem_service = service_manager.get_service('order_redeem')
        self.assertIsNotNone(order_redeem_service.account_service)
        self.assertIsNotNone(order_redeem_service.redemption_service)

        # Test graceful shutdown
        shutdown_result = service_manager.shutdown_all_services()
        self.assertTrue(shutdown_result)


class TestErrorHandlingIntegration(unittest.TestCase):
    """Test error handling in integration scenarios"""

    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True

        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.app = self.app

        self.test_config = {
            'enabled': True,
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'require_verification': True,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            },
            'cooldown_config': {
                'default_cooldown_hours': 24
            }
        }

        self.plugin = Plugin(self.mock_plugin_manager)
        self.data_file_patches = []

    def tearDown(self):
        """Clean up test environment"""
        for patcher in self.data_file_patches:
            patcher.stop()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        if hasattr(self.plugin, 'service_manager') and self.plugin.service_manager:
            self.plugin.service_manager.shutdown_all_services()

    def _setup_data_file_mocks(self):
        """Setup mocks for data file operations"""
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.temp_dir) / filename)

        patcher = patch('plugins.openai_plus_redeem.models.utils.get_data_file_path',
                       side_effect=mock_get_data_file_path)
        patcher.start()
        self.data_file_patches.append(patcher)

        # Create initial data files
        initial_data = {
            'accounts': [],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }

        files_to_create = [
            'chatgpt_accounts.json',
            'order_redemptions.json',
            'email_verifications.json',
            'account_cooldowns.json'
        ]

        for filename in files_to_create:
            file_path = Path(self.temp_dir) / filename
            with open(file_path, 'w') as f:
                if 'redemptions' in filename:
                    json.dump({'redemptions': [], 'metadata': initial_data['metadata']}, f)
                elif 'verifications' in filename:
                    json.dump({'verification_logs': []}, f)
                elif 'cooldowns' in filename:
                    json.dump({'cooldowns': [], 'metadata': initial_data['metadata']}, f)
                else:
                    json.dump(initial_data, f)

    def test_no_available_accounts_scenario(self):
        """Test redemption when no accounts are available"""
        # Setup data file mocks
        self._setup_data_file_mocks()

        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Try to redeem without any accounts in system
        order_redeem_service = self.plugin.service_manager.get_service('order_redeem')

        result = order_redeem_service.process_order_redemption(
            order_id='ORDER_NO_ACCOUNTS',
            buyer_username='test_user',
            sku='chatgpt_plus',
            var_sku='chatgpt_5_30'
        )

        # Should fail gracefully
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        self.assertIn('No available accounts', result['error'])

    @patch('plugins.openai_plus_redeem.services.email_service.imaplib')
    def test_email_service_failure_scenario(self, mock_imaplib):
        """Test handling of email service failures"""
        # Setup data file mocks
        self._setup_data_file_mocks()

        # Mock email service failure
        mock_imaplib.IMAP4_SSL.side_effect = Exception("IMAP connection failed")

        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Try to get verification code
        email_service = self.plugin.service_manager.get_service('email')

        verification_result = email_service.get_verification_code('<EMAIL>')

        # Should handle failure gracefully
        self.assertFalse(verification_result['success'])
        self.assertIn('error', verification_result)

    def test_duplicate_redemption_scenario(self):
        """Test handling of duplicate redemption attempts"""
        # Setup data file mocks
        self._setup_data_file_mocks()

        # Initialize plugin
        result = self.plugin.initialize(self.test_config)
        self.assertTrue(result)

        # Add test account
        account_service = self.plugin.service_manager.get_service('chatgpt_account')
        test_account = ChatGPTAccount(
            account_id='TEST_DUPLICATE',
            email='<EMAIL>',
            password='test_password',
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        account_service.add_account(test_account)

        # First redemption
        order_redeem_service = self.plugin.service_manager.get_service('order_redeem')

        first_result = order_redeem_service.process_order_redemption(
            order_id='DUPLICATE_ORDER',
            buyer_username='test_user',
            sku='chatgpt_plus',
            var_sku='chatgpt_5_30'
        )

        self.assertTrue(first_result['success'])

        # Second redemption with same order ID
        second_result = order_redeem_service.process_order_redemption(
            order_id='DUPLICATE_ORDER',
            buyer_username='test_user',
            sku='chatgpt_plus',
            var_sku='chatgpt_5_30'
        )

        # Should fail due to duplicate
        self.assertFalse(second_result['success'])
        self.assertIn('already been redeemed', second_result['error'])


class TestPerformanceIntegration(unittest.TestCase):
    """Test performance aspects of integration"""

    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True

        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.app = self.app

        self.test_config = {
            'enabled': True,
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }

        self.plugin = Plugin(self.mock_plugin_manager)
        self.data_file_patches = []

    def tearDown(self):
        """Clean up test environment"""
        for patcher in self.data_file_patches:
            patcher.stop()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        if hasattr(self.plugin, 'service_manager') and self.plugin.service_manager:
            self.plugin.service_manager.shutdown_all_services()

    def _setup_data_file_mocks(self):
        """Setup mocks for data file operations"""
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.temp_dir) / filename)

        patcher = patch('plugins.openai_plus_redeem.models.utils.get_data_file_path',
                       side_effect=mock_get_data_file_path)
        patcher.start()
        self.data_file_patches.append(patcher)

        # Create initial data files with large datasets
        accounts_data = {
            'accounts': [
                {
                    'account_id': f'PERF_TEST_{i:03d}',
                    'email': f'test{i}@example.com',
                    'password': f'password{i}',
                    'max_concurrent_users': 5,
                    'current_users': 0,
                    'expiration_date': (datetime.now() + timedelta(days=30)).isoformat(),
                    'status': 'active',
                    'created_date': datetime.now().isoformat()
                }
                for i in range(100)  # Create 100 test accounts
            ],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }

        # Create data files
        files_data = {
            'chatgpt_accounts.json': accounts_data,
            'order_redemptions.json': {'redemptions': [], 'metadata': accounts_data['metadata']},
            'email_verifications.json': {'verification_logs': []},
            'account_cooldowns.json': {'cooldowns': [], 'metadata': accounts_data['metadata']}
        }

        for filename, data in files_data.items():
            file_path = Path(self.temp_dir) / filename
            with open(file_path, 'w') as f:
                json.dump(data, f)

    def test_large_dataset_performance(self):
        """Test performance with large datasets"""
        # Setup data file mocks with large dataset
        self._setup_data_file_mocks()

        # Initialize plugin
        start_time = datetime.now()
        result = self.plugin.initialize(self.test_config)
        init_time = (datetime.now() - start_time).total_seconds()

        self.assertTrue(result)
        self.assertLess(init_time, 5.0, "Plugin initialization should complete within 5 seconds")

        # Test account service performance
        account_service = self.plugin.service_manager.get_service('chatgpt_account')

        start_time = datetime.now()
        all_accounts = account_service.get_all_accounts()
        query_time = (datetime.now() - start_time).total_seconds()

        self.assertEqual(len(all_accounts), 100)
        self.assertLess(query_time, 1.0, "Account query should complete within 1 second")

        # Test account assignment performance
        start_time = datetime.now()
        available_account = account_service.get_available_account(5, 30)
        assignment_time = (datetime.now() - start_time).total_seconds()

        self.assertIsNotNone(available_account)
        self.assertLess(assignment_time, 0.5, "Account assignment should complete within 0.5 seconds")


if __name__ == '__main__':
    unittest.main()
