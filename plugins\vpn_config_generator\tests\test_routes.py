"""
Tests for VPN Config Generator routes.
"""

import unittest
import sys
import os
import json
import tempfile
from unittest.mock import Mock, patch, MagicMock

# Add plugin directory to path
plugin_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, plugin_dir)

# Add Flask app directory to path
app_dir = os.path.dirname(os.path.dirname(os.path.dirname(plugin_dir)))
sys.path.insert(0, app_dir)

try:
    from flask import Flask
    from routes import create_routes
    from services import VPNConfigGeneratorService
    from models import VPNConfigRequest, VPNConfigResponse, ConfigTemplate
except ImportError as e:
    print(f"Import error: {e}")
    print("Skipping route tests due to missing dependencies")
    sys.exit(0)


class TestVPNConfigGeneratorRoutes(unittest.TestCase):
    """Test VPN Config Generator routes"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        # Create temporary directory for plugin
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock plugin instance
        self.mock_plugin = Mock()
        self.mock_plugin.plugin_dir = self.temp_dir
        self.mock_plugin.config_service = Mock()
        self.mock_plugin.chat_command_service = Mock()
        self.mock_plugin.plugin_manager = Mock()
        
        # Create blueprint
        self.blueprint = create_routes(self.mock_plugin)
        self.app.register_blueprint(self.blueprint, url_prefix='/vpn-config-generator')
        
        self.client = self.app.test_client()
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_dashboard_route(self):
        """Test dashboard route"""
        # Mock service responses
        self.mock_plugin.config_service.get_api_config.return_value = Mock()
        self.mock_plugin.config_service.get_generator_settings.return_value = Mock()
        self.mock_plugin.config_service.get_all_templates.return_value = []
        
        response = self.client.get('/vpn-config-generator/')
        
        self.assertEqual(response.status_code, 200)
        self.mock_plugin.config_service.get_api_config.assert_called_once()
        self.mock_plugin.config_service.get_generator_settings.assert_called_once()
        self.mock_plugin.config_service.get_all_templates.assert_called_once()
    
    def test_test_connection_route(self):
        """Test connection test route"""
        # Mock successful connection test
        self.mock_plugin.config_service.test_connection.return_value = {
            'success': True,
            'message': 'Connection successful',
            'api_url': 'https://test.api.com'
        }
        
        response = self.client.post('/vpn-config-generator/api/test-connection')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['message'], 'Connection successful')
        self.mock_plugin.config_service.test_connection.assert_called_once()
    
    def test_test_connection_route_failure(self):
        """Test connection test route with failure"""
        # Mock failed connection test
        self.mock_plugin.config_service.test_connection.return_value = {
            'success': False,
            'message': 'Connection failed'
        }
        
        response = self.client.post('/vpn-config-generator/api/test-connection')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertEqual(data['message'], 'Connection failed')
    
    def test_generate_config_route_success(self):
        """Test config generation route with success"""
        # Mock successful config generation
        mock_response = VPNConfigResponse(
            success=True,
            config='vmess://test-config',
            created_date='2024-01-01',
            expired_date='2024-01-31',
            message='Config generated successfully'
        )
        self.mock_plugin.config_service.generate_config.return_value = mock_response
        
        request_data = {
            'server': 'server11',
            'days': '30',
            'telco': 'digi',
            'plan': 'unlimited',
            'username': 'testuser'
        }
        
        response = self.client.post(
            '/vpn-config-generator/api/generate-config',
            data=json.dumps(request_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['config'], 'vmess://test-config')
        self.assertEqual(data['created_date'], '2024-01-01')
        self.assertEqual(data['expired_date'], '2024-01-31')
    
    def test_generate_config_route_failure(self):
        """Test config generation route with failure"""
        # Mock failed config generation
        mock_response = VPNConfigResponse(
            success=False,
            error='API connection failed'
        )
        self.mock_plugin.config_service.generate_config.return_value = mock_response
        
        request_data = {
            'server': 'server11',
            'days': '30',
            'telco': 'digi',
            'plan': 'unlimited',
            'username': 'testuser'
        }
        
        response = self.client.post(
            '/vpn-config-generator/api/generate-config',
            data=json.dumps(request_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertEqual(data['error'], 'API connection failed')
    
    def test_generate_config_route_missing_data(self):
        """Test config generation route with missing data"""
        response = self.client.post(
            '/vpn-config-generator/api/generate-config',
            data=json.dumps({}),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('Missing required fields', data['error'])
    
    def test_get_api_config_route(self):
        """Test get API config route"""
        # Mock API config
        mock_config = Mock()
        mock_config.to_dict.return_value = {
            'enabled': True,
            'use_vpn_plugin_api': False,
            'timeout': 30
        }
        self.mock_plugin.config_service.get_api_config.return_value = mock_config
        
        response = self.client.get('/vpn-config-generator/api/config/api')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('config', data)
        self.assertTrue(data['config']['enabled'])
    
    def test_update_api_config_route(self):
        """Test update API config route"""
        # Mock successful config update
        self.mock_plugin.config_service.update_api_config.return_value = True
        
        config_data = {
            'enabled': True,
            'use_vpn_plugin_api': True,
            'timeout': 60
        }
        
        response = self.client.post(
            '/vpn-config-generator/api/config/api',
            data=json.dumps(config_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.mock_plugin.config_service.update_api_config.assert_called_once()
    
    def test_get_templates_route(self):
        """Test get templates route"""
        # Mock templates
        mock_template = Mock()
        mock_template.to_dict.return_value = {
            'id': 'template1',
            'name': 'Test Template',
            'server': 'server11'
        }
        self.mock_plugin.config_service.get_all_templates.return_value = [mock_template]
        
        response = self.client.get('/vpn-config-generator/api/templates')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('templates', data)
        self.assertEqual(len(data['templates']), 1)
    
    def test_create_template_route(self):
        """Test create template route"""
        # Mock successful template creation
        self.mock_plugin.config_service.add_template.return_value = True
        
        template_data = {
            'name': 'New Template',
            'description': 'Test description',
            'server': 'server11',
            'days': '30',
            'telco': 'digi',
            'plan': 'unlimited'
        }
        
        response = self.client.post(
            '/vpn-config-generator/api/templates',
            data=json.dumps(template_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.mock_plugin.config_service.add_template.assert_called_once()
    
    def test_delete_template_route(self):
        """Test delete template route"""
        # Mock successful template deletion
        self.mock_plugin.config_service.delete_template.return_value = True
        
        response = self.client.delete('/vpn-config-generator/api/templates/template1')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.mock_plugin.config_service.delete_template.assert_called_once_with('template1')


if __name__ == '__main__':
    unittest.main()
