"""
VPN Configuration Service
Handles VPN configuration templates and generation
"""

import logging
import json
import os
import uuid
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class VPNConfigService:
    """Service for managing VPN configuration templates"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.templates_file = config.get('config_templates', {}).get('templates_file', 'configs/services/config_templates.json')
        
    def load_templates(self) -> List[Dict[str, Any]]:
        """Load configuration templates from file"""
        try:
            if os.path.exists(self.templates_file):
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Error loading config templates: {e}")
            return []
    
    def save_templates(self, templates: List[Dict[str, Any]]) -> bool:
        """Save configuration templates to file"""
        try:
            os.makedirs(os.path.dirname(self.templates_file), exist_ok=True)
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Error saving config templates: {e}")
            return False
    
    def add_template(self, template: Dict[str, Any]) -> bool:
        """Add a new configuration template"""
        try:
            templates = self.load_templates()
            
            # Generate ID if not provided
            if 'id' not in template:
                template['id'] = str(uuid.uuid4())
            
            templates.append(template)
            return self.save_templates(templates)
            
        except Exception as e:
            logger.error(f"Error adding config template: {e}")
            return False
    
    def update_template(self, template_id: str, template: Dict[str, Any]) -> bool:
        """Update an existing configuration template"""
        try:
            templates = self.load_templates()
            
            for i, t in enumerate(templates):
                if t.get('id') == template_id:
                    template['id'] = template_id  # Ensure ID is preserved
                    templates[i] = template
                    return self.save_templates(templates)
            
            return False
        except Exception as e:
            logger.error(f"Error updating config template: {e}")
            return False
    
    def delete_template(self, template_id: str) -> bool:
        """Delete a configuration template"""
        try:
            templates = self.load_templates()
            templates = [t for t in templates if t.get('id') != template_id]
            return self.save_templates(templates)
        except Exception as e:
            logger.error(f"Error deleting config template: {e}")
            return False
    
    def get_template_by_id(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific template by ID"""
        templates = self.load_templates()
        for template in templates:
            if template.get('id') == template_id:
                return template
        return None
    
    def generate_config(self, template_id: str, variables: Dict[str, str]) -> str:
        """Generate configuration using a template and variables"""
        try:
            template = self.get_template_by_id(template_id)
            if not template:
                return ""
            
            template_string = template.get('template', '')
            
            # Replace variables in template
            for key, value in variables.items():
                placeholder = f"{{{key}}}"
                template_string = template_string.replace(placeholder, str(value))
            
            return template_string
            
        except Exception as e:
            logger.error(f"Error generating config: {e}")
            return ""
    
    def validate_template(self, template: Dict[str, Any]) -> Dict[str, str]:
        """Validate template data and return any errors"""
        errors = {}
        
        # Validate name
        if not template.get('name', '').strip():
            errors['name'] = 'Template name is required'
        
        # Validate template string
        template_str = template.get('template', '').strip()
        if not template_str:
            errors['template'] = 'Template string is required'
        
        # Check for common template variables
        required_vars = ['{uuid}', '{server}', '{port}']
        missing_vars = []
        for var in required_vars:
            if var not in template_str:
                missing_vars.append(var)
        
        if missing_vars:
            errors['template'] = f"Template should include variables: {', '.join(missing_vars)}"
        
        return errors
    
    def get_template_variables(self, template_string: str) -> List[str]:
        """Extract variables from a template string"""
        import re
        variables = re.findall(r'\{(\w+)\}', template_string)
        return list(set(variables))  # Remove duplicates
    
    def create_default_templates(self) -> bool:
        """Create default configuration templates if none exist"""
        try:
            templates = self.load_templates()
            if templates:
                return True  # Templates already exist
            
            default_templates = [
                {
                    "id": str(uuid.uuid4()),
                    "name": "VLESS WebSocket",
                    "template": "vless://{uuid}@{server}:{port}?encryption=none&type=ws&path={path}#{identity}"
                },
                {
                    "id": str(uuid.uuid4()),
                    "name": "VLESS TCP",
                    "template": "vless://{uuid}@{server}:{port}?encryption=none&type=tcp#{identity}"
                },
                {
                    "id": str(uuid.uuid4()),
                    "name": "VMess WebSocket",
                    "template": "vmess://{base64_config}"
                }
            ]
            
            return self.save_templates(default_templates)
            
        except Exception as e:
            logger.error(f"Error creating default templates: {e}")
            return False
