from openai import OpenAI
from typing import List, Dict, Any
import json
import config

def get_client():
    """Get OpenAI client with current API key"""
    return OpenAI(
        api_key=config.DEEPSEEK_API_KEY,
        base_url="https://api.deepseek.com"
    )

def format_messages_for_context(messages: List[Dict[str, Any]], max_messages: int = 5) -> str:
    """Format recent messages into a readable context for the AI"""
    formatted = []
    # Take last N messages (they're already in reverse chronological order)
    for msg in messages[:max_messages]:
        sender = "Seller" if msg['from_shop_id'] == config.SHOP_ID else "Customer"
        text = msg['content'].get('text', '')
        timestamp = msg['created_at']
        
        # Add source_content information if available
        source_info = ""
        if msg.get('source_content'):
            product_id = msg['source_content'].get('product_id')
            if product_id:
                source_info = f" [Product ID: {product_id}]"
        
        formatted.append(f"{sender} ({timestamp}){source_info}: {text}")
    print(formatted)
    return "\n".join(formatted)

def process_action(actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process multiple actions based on AI response"""
    results = []
    
    for action in actions:
        action_type = action.get('type')
        
        if action_type == 'chat':
            results.append({
                "type": "chat",
                "status": "sent",
                "reply": action.get('reply', '')
            })
            
        elif action_type == 'send_order':
            order_sn = action.get('order_sn')
            if order_sn:
                from services.order_service import get_order_details
                order_result, status_code = get_order_details(order_sn)
                results.append({
                    "type": "send_order",
                    "status": "success" if status_code == 200 else "failed",
                    "order_sn": order_sn,
                    "order_data": order_result if status_code == 200 else None,
                    "error": order_result.get('error') if status_code != 200 else None
                })
            else:
                results.append({
                    "type": "send_order",
                    "status": "failed",
                    "error": "Missing order_sn"
                })
    
    return results

def generate_reply(conversation_messages: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate an AI response based on conversation history"""
    try:
        context = format_messages_for_context(conversation_messages)
        client = get_client()
        
        system_prompt = config.AI_SYSTEM_PROMPT if hasattr(config, 'AI_SYSTEM_PROMPT') else config.DEFAULT_SYSTEM_PROMPT
        temperature = float(config.AI_TEMPERATURE if hasattr(config, 'AI_TEMPERATURE') else 1.0)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Here is the recent conversation history:\n\n{context}\n\nPlease analyze and generate an appropriate response:"}
        ]

        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            temperature=temperature,
            stream=False,
            response_format={"type": "json_object"}
        )

        try:
            response_content = response.choices[0].message.content
            print(response_content)
            parsed_response = json.loads(response_content)
            
            # Process all actions and get results
            action_results = process_action(parsed_response.get("actions", []))
            
            return {
                "success": True,
                "data": parsed_response,
                "action_results": action_results
            }
        except json.JSONDecodeError:
            return {
                "success": False,
                "error": "Failed to parse AI response as JSON",
                "raw_response": response_content
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"AI generation failed: {str(e)}"
        }

def get_temperature_for_context(message_type: str) -> float:
    """Return appropriate temperature based on context"""
    temperature_map = {
        "technical": 0.0,  # For technical/coding responses
        "data": 1.0,      # For data analysis
        "general": 1.3,   # For general conversation
        "translation": 1.3,# For translations
        "creative": 1.0   # For creative writing
    }
    return temperature_map.get(message_type, float(config.AI_TEMPERATURE if hasattr(config, 'AI_TEMPERATURE') else 1.0))
