# Deployment Checklist

This document provides a comprehensive checklist for deploying the OpenAI Plus Redeem Plugin to production environments.

## Pre-Deployment Requirements

### 1. System Requirements

- [ ] **Python 3.8+** installed and configured
- [ ] **SteamCodeTool** main application running
- [ ] **Required Python packages** installed (see requirements.txt)
- [ ] **Sufficient disk space** (minimum 1GB for data and backups)
- [ ] **Network connectivity** to Gmail IMAP servers
- [ ] **SSL/TLS certificates** configured (for HTTPS endpoints)

### 2. Dependencies Check

```bash
# Verify Python version
python --version  # Should be 3.8+

# Install required packages
pip install -r requirements.txt

# Verify critical packages
python -c "import imaplib, ssl, flask, dataclasses, json, datetime"
```

### 3. Environment Preparation

- [ ] **Production server** configured and accessible
- [ ] **Domain name** configured (for webhook endpoints)
- [ ] **Firewall rules** configured for required ports
- [ ] **Backup storage** configured and tested
- [ ] **Monitoring systems** ready to track plugin health

## Configuration Setup

### 1. Gmail Configuration

- [ ] **Gmail account** created for verification emails
- [ ] **2-Factor Authentication** enabled on Gmail account
- [ ] **App Password** generated for IMAP access
- [ ] **IMAP access** enabled in Gmail settings
- [ ] **Test IMAP connection** successful

**Test IMAP Connection:**
```bash
python -c "
import imaplib, ssl
try:
    context = ssl.create_default_context()
    server = imaplib.IMAP4_SSL('imap.gmail.com', 993, ssl_context=context)
    server.login('<EMAIL>', 'your-app-password')
    print('✓ IMAP connection successful')
    server.logout()
except Exception as e:
    print(f'✗ IMAP connection failed: {e}')
"
```

### 2. Plugin Configuration

- [ ] **Main configuration** updated in `configs/core/plugin_config.json`
- [ ] **Email credentials** configured securely
- [ ] **Rate limiting** configured appropriately for production load
- [ ] **Security settings** configured (IP whitelisting, abuse prevention)
- [ ] **Shopee integration** configured (if applicable)
- [ ] **Backup settings** configured
- [ ] **Configuration validation** passed

**Configuration Template:**
```json
{
  "openai_plus_redeem": {
    "enabled": true,
    "debug": false,
    "email_config": {
      "imap_server": "imap.gmail.com",
      "imap_port": 993,
      "use_ssl": true,
      "require_verification": true,
      "global_credentials": {
        "email": "<EMAIL>",
        "password": "production-app-password"
      },
      "search_config": {
        "max_search_results": 20,
        "search_timeout_seconds": 45,
        "verification_keywords": ["openai", "chatgpt", "verification", "code"]
      }
    },
    "cooldown_config": {
      "default_cooldown_hours": 24,
      "max_reset_attempts": 3,
      "enable_cooldown_management": true,
      "admin_can_override": true
    },
    "security_config": {
      "max_redemptions_per_user": 10,
      "enable_abuse_prevention": true,
      "rate_limit_requests_per_minute": 15,
      "admin_rate_limit_requests_per_minute": 200,
      "enable_ip_whitelist": true,
      "whitelisted_ips": ["your-admin-ip-range"]
    },
    "shopee_integration": {
      "enabled": true,
      "webhook_secret": "production-webhook-secret",
      "auto_process_orders": true,
      "supported_skus": ["chatgpt_plus", "chatgpt_premium"],
      "order_validation": {
        "require_buyer_username": true,
        "validate_order_status": true,
        "allowed_order_statuses": ["COMPLETED", "SHIPPED", "DELIVERED"]
      }
    },
    "account_management": {
      "auto_cleanup_expired": true,
      "cleanup_interval_hours": 12,
      "max_concurrent_users_default": 5,
      "account_expiry_warning_days": 14
    },
    "data_persistence": {
      "backup_enabled": true,
      "backup_interval_hours": 4,
      "max_backup_files": 20,
      "data_encryption": true
    }
  }
}
```

### 3. Data Directory Setup

- [ ] **Data directories** created with proper permissions
- [ ] **Backup directories** created and accessible
- [ ] **File permissions** set correctly
- [ ] **Disk space monitoring** configured

**Setup Commands:**
```bash
# Create data directories
mkdir -p configs/data/openai_plus_redeem
mkdir -p configs/data/openai_plus_redeem/backups

# Set permissions
chmod 755 configs/data/openai_plus_redeem
chmod 755 configs/data/openai_plus_redeem/backups

# For Docker environments
chown -R app:app configs/data/openai_plus_redeem

# Verify permissions
ls -la configs/data/openai_plus_redeem/
```

## Plugin Installation

### 1. File Deployment

- [ ] **Plugin files** copied to production server
- [ ] **File integrity** verified (checksums match)
- [ ] **File permissions** set correctly
- [ ] **Plugin directory structure** complete

**Verification Commands:**
```bash
# Verify plugin structure
ls -la plugins/openai_plus_redeem/
find plugins/openai_plus_redeem/ -name "*.py" | wc -l  # Should be 20+ files

# Check critical files exist
test -f plugins/openai_plus_redeem/__init__.py && echo "✓ Plugin init file exists"
test -f plugins/openai_plus_redeem/plugin.py && echo "✓ Main plugin file exists"
test -f plugins/openai_plus_redeem/config_schema.json && echo "✓ Config schema exists"
```

### 2. Plugin Registration

- [ ] **Plugin registered** in main application configuration
- [ ] **Plugin enabled** in configuration
- [ ] **Plugin initialization** successful
- [ ] **Plugin endpoints** accessible

**Registration Verification:**
```bash
# Check plugin is registered
grep -i "openai_plus_redeem" configs/core/plugin_config.json

# Test plugin initialization
python -c "
from plugins.openai_plus_redeem.plugin import OpenAIPlusRedeemPlugin
plugin = OpenAIPlusRedeemPlugin()
print('✓ Plugin initialization successful')
"
```

### 3. Service Initialization

- [ ] **All services** initialized successfully
- [ ] **Service health checks** passing
- [ ] **Service dependencies** resolved
- [ ] **Service endpoints** responding

**Service Verification:**
```bash
# Test service health (after application start)
curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/services/health \
  -H "Authorization: Bearer admin_token"
```

## Security Configuration

### 1. Authentication Setup

- [ ] **Admin authentication** configured and tested
- [ ] **API tokens** generated and secured
- [ ] **Token expiration** configured appropriately
- [ ] **Authentication endpoints** protected

### 2. Network Security

- [ ] **HTTPS** enabled for all endpoints
- [ ] **SSL certificates** valid and configured
- [ ] **Firewall rules** configured
- [ ] **IP whitelisting** configured (if applicable)
- [ ] **Rate limiting** configured and tested

### 3. Data Security

- [ ] **Sensitive data encryption** enabled (if configured)
- [ ] **Backup encryption** configured
- [ ] **Access logs** configured
- [ ] **Data retention policies** implemented

## Integration Setup

### 1. Shopee Integration (if applicable)

- [ ] **Webhook URL** configured in Shopee dashboard
- [ ] **Webhook secret** configured and secured
- [ ] **Webhook endpoint** accessible from Shopee servers
- [ ] **Webhook signature validation** working
- [ ] **Test webhook** successful

**Webhook Test:**
```bash
# Test webhook endpoint
curl -X POST https://your-domain.com/openai-plus-redeem/api/shopee/redeem \
  -H "Content-Type: application/json" \
  -d '{
    "order_data": {
      "order_sn": "TEST123",
      "buyer_username": "testuser",
      "items": [{"item_sku": "chatgpt_plus", "variation_sku": "chatgpt_5_30"}]
    }
  }'
```

### 2. Email Integration

- [ ] **Email service** configured and tested
- [ ] **IMAP connectivity** verified
- [ ] **Email search** working correctly
- [ ] **Verification code retrieval** tested

## Testing and Validation

### 1. Functional Testing

- [ ] **Customer redemption flow** tested end-to-end
- [ ] **Admin management functions** tested
- [ ] **Email verification** tested with real emails
- [ ] **Account allocation** tested
- [ ] **Cooldown management** tested
- [ ] **Error handling** tested

**Test Commands:**
```bash
# Run built-in tests
python plugins/openai_plus_redeem/tests/test_integration.py
python plugins/openai_plus_redeem/validate_error_handling.py
python plugins/openai_plus_redeem/validate_data_persistence.py
```

### 2. Performance Testing

- [ ] **Load testing** completed
- [ ] **Response times** within acceptable limits
- [ ] **Memory usage** stable
- [ ] **Concurrent user handling** tested
- [ ] **Rate limiting** functioning correctly

**Performance Test:**
```bash
# Run performance tests
python plugins/openai_plus_redeem/run_performance_tests.py
```

### 3. Security Testing

- [ ] **Authentication bypass** attempts blocked
- [ ] **Rate limiting** enforced
- [ ] **Input validation** working
- [ ] **SQL injection** protection verified
- [ ] **XSS protection** verified

## Monitoring and Alerting

### 1. Health Monitoring

- [ ] **Service health checks** configured
- [ ] **Endpoint monitoring** set up
- [ ] **Database/file monitoring** configured
- [ ] **Performance metrics** tracked

### 2. Log Monitoring

- [ ] **Application logs** configured
- [ ] **Error log monitoring** set up
- [ ] **Log rotation** configured
- [ ] **Log aggregation** set up (if applicable)

### 3. Alert Configuration

- [ ] **Service down alerts** configured
- [ ] **Error rate alerts** configured
- [ ] **Performance degradation alerts** configured
- [ ] **Disk space alerts** configured
- [ ] **Backup failure alerts** configured

## Backup and Recovery

### 1. Backup Configuration

- [ ] **Automatic backups** enabled and tested
- [ ] **Backup storage** configured and accessible
- [ ] **Backup retention** configured
- [ ] **Backup encryption** enabled (if required)
- [ ] **Backup monitoring** configured

### 2. Recovery Testing

- [ ] **Data recovery** tested from backup
- [ ] **Service recovery** procedures documented
- [ ] **Recovery time objectives** defined
- [ ] **Recovery point objectives** defined

## Documentation

### 1. Deployment Documentation

- [ ] **Configuration documentation** updated
- [ ] **API documentation** available
- [ ] **Troubleshooting guide** available
- [ ] **Operational procedures** documented

### 2. Team Training

- [ ] **Operations team** trained on plugin management
- [ ] **Support team** trained on troubleshooting
- [ ] **Development team** briefed on architecture
- [ ] **Emergency procedures** communicated

## Go-Live Checklist

### 1. Final Pre-Launch Verification

- [ ] **All previous checklist items** completed
- [ ] **Production configuration** reviewed and approved
- [ ] **Security review** completed
- [ ] **Performance benchmarks** met
- [ ] **Backup and recovery** tested
- [ ] **Monitoring and alerting** active
- [ ] **Team notifications** sent

### 2. Launch Sequence

1. **Application Restart**
   ```bash
   # Stop application
   systemctl stop steamcodetool

   # Verify plugin files are in place
   ls -la plugins/openai_plus_redeem/

   # Start application
   systemctl start steamcodetool

   # Verify startup
   tail -f logs/application.log | grep "openai_plus_redeem"
   ```

2. **Plugin Verification**
   ```bash
   # Check plugin initialization
   curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/services/health \
     -H "Authorization: Bearer admin_token"

   # Verify endpoints are accessible
   curl -X GET http://localhost:5000/openai-plus-redeem/
   ```

3. **Functional Testing**
   ```bash
   # Test customer redemption
   curl -X POST http://localhost:5000/openai-plus-redeem/api/redeem \
     -H "Content-Type: application/json" \
     -d '{
       "order_id": "PROD_TEST_001",
       "buyer_username": "test_user",
       "sku": "chatgpt_plus",
       "var_sku": "chatgpt_5_30"
     }'

   # Test admin functions
   curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/accounts \
     -H "Authorization: Bearer admin_token"
   ```

### 3. Post-Launch Monitoring

- [ ] **Monitor application logs** for 30 minutes
- [ ] **Check service health** every 5 minutes for first hour
- [ ] **Verify email connectivity** with test verification
- [ ] **Monitor performance metrics** for first 24 hours
- [ ] **Check backup creation** after first interval

## Post-Deployment Tasks

### 1. Initial Data Setup

- [ ] **Add initial ChatGPT accounts** to the system
- [ ] **Configure account capacities** based on expected load
- [ ] **Set up initial cooldown policies**
- [ ] **Test account allocation** with real redemptions

**Add Initial Accounts:**
```bash
# Add production ChatGPT accounts
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/accounts \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "secure_password_1",
    "expiration_date": "2024-12-31T23:59:59Z",
    "max_concurrent_users": 5
  }'
```

### 2. Operational Procedures

- [ ] **Daily health checks** scheduled
- [ ] **Weekly backup verification** scheduled
- [ ] **Monthly security review** scheduled
- [ ] **Quarterly performance review** scheduled

### 3. Maintenance Schedule

- [ ] **Log rotation** configured
- [ ] **Data cleanup** scheduled
- [ ] **Security updates** planned
- [ ] **Performance optimization** scheduled

## Rollback Procedures

### 1. Emergency Rollback

If critical issues are discovered post-deployment:

```bash
# Immediate rollback steps
# 1. Disable plugin
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/disable \
  -H "Authorization: Bearer admin_token"

# 2. Stop application
systemctl stop steamcodetool

# 3. Remove plugin configuration
cp configs/core/plugin_config.json configs/core/plugin_config.json.backup
# Edit plugin_config.json to set "enabled": false

# 4. Restart application
systemctl start steamcodetool
```

### 2. Data Rollback

If data corruption is detected:

```bash
# Restore from latest backup
cp configs/data/openai_plus_redeem/backups/chatgpt_accounts_backup_latest.json \
   configs/data/openai_plus_redeem/chatgpt_accounts.json

# Restart services
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/services/restart-all \
  -H "Authorization: Bearer admin_token"
```

## Troubleshooting Quick Reference

### Common Post-Deployment Issues

| Issue | Quick Fix | Reference |
|-------|-----------|-----------|
| Plugin not loading | Check configuration syntax | TROUBLESHOOTING.md |
| Email verification fails | Verify Gmail app password | TROUBLESHOOTING.md |
| No available accounts | Add more accounts via admin API | API.md |
| Rate limit errors | Adjust rate limits in config | CONFIGURATION.md |
| Webhook not working | Check webhook URL and secret | TROUBLESHOOTING.md |

### Emergency Contacts

- **Primary Support**: <EMAIL>
- **Secondary Support**: <EMAIL>
- **Emergency Hotline**: +1-XXX-XXX-XXXX

## Deployment Sign-off

### Technical Sign-off

- [ ] **System Administrator**: _________________ Date: _______
- [ ] **Security Officer**: _________________ Date: _______
- [ ] **Development Lead**: _________________ Date: _______

### Business Sign-off

- [ ] **Product Owner**: _________________ Date: _______
- [ ] **Operations Manager**: _________________ Date: _______

### Final Approval

- [ ] **Deployment Manager**: _________________ Date: _______

---

## Deployment Completion

**Deployment Date**: _______________
**Deployment Time**: _______________
**Deployed By**: _______________
**Version**: _______________

**Post-Deployment Notes**:
_________________________________________________
_________________________________________________
_________________________________________________

**Next Review Date**: _______________

---

*This checklist should be completed in its entirety before considering the OpenAI Plus Redeem Plugin deployment successful. Keep this document for audit and compliance purposes.*
