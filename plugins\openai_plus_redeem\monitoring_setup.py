#!/usr/bin/env python3
"""
Production Monitoring and Health Check Setup

This module provides comprehensive monitoring, health checks, and alerting
for the OpenAI Plus Redeem Plugin in production environments.
"""

import os
import sys
import json
import time
import psutil
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import threading
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class ProductionMonitor:
    """Production monitoring and health check manager"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "production_config.json"
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.metrics = {}
        self.alerts = []
        self.last_health_check = None
        self.monitoring_active = False
        
    def _load_config(self) -> Dict[str, Any]:
        """Load production configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Setup production logging"""
        logger = logging.getLogger('openai_plus_redeem_monitor')
        logger.setLevel(logging.INFO)
        
        # File handler
        log_file = "logs/monitoring.log"
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def start_monitoring(self):
        """Start continuous monitoring"""
        self.monitoring_active = True
        self.logger.info("Starting production monitoring...")
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
        
        self.logger.info("Production monitoring started successfully")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring_active = False
        self.logger.info("Production monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Run health checks
                health_status = self.run_health_checks()
                
                # Collect metrics
                metrics = self.collect_metrics()
                
                # Check alert conditions
                self.check_alerts(health_status, metrics)
                
                # Sleep until next check
                interval = self.config.get('monitoring_config', {}).get('health_checks', {}).get('interval_seconds', 60)
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait before retrying
    
    def run_health_checks(self) -> Dict[str, Any]:
        """Run comprehensive health checks"""
        self.logger.info("Running health checks...")
        
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'checks': {}
        }
        
        # Plugin health check
        health_status['checks']['plugin'] = self._check_plugin_health()
        
        # Service health checks
        health_status['checks']['services'] = self._check_services_health()
        
        # Database/File system health
        health_status['checks']['data_storage'] = self._check_data_storage_health()
        
        # External dependencies
        health_status['checks']['external_deps'] = self._check_external_dependencies()
        
        # System resources
        health_status['checks']['system_resources'] = self._check_system_resources()
        
        # Determine overall status
        failed_checks = [k for k, v in health_status['checks'].items() 
                        if v.get('status') != 'healthy']
        
        if failed_checks:
            health_status['overall_status'] = 'unhealthy'
            health_status['failed_checks'] = failed_checks
        
        self.last_health_check = health_status
        self.logger.info(f"Health check completed: {health_status['overall_status']}")
        
        return health_status
    
    def _check_plugin_health(self) -> Dict[str, Any]:
        """Check plugin health"""
        try:
            # Try to import and initialize plugin
            plugin_path = os.path.dirname(os.path.abspath(__file__))
            sys.path.insert(0, plugin_path)
            
            from plugin import Plugin
            
            # Basic plugin check
            return {
                'status': 'healthy',
                'message': 'Plugin can be imported and initialized',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Plugin health check failed: {e}',
                'timestamp': datetime.now().isoformat()
            }
    
    def _check_services_health(self) -> Dict[str, Any]:
        """Check all services health"""
        services_status = {
            'status': 'healthy',
            'services': {},
            'timestamp': datetime.now().isoformat()
        }
        
        service_names = [
            'chatgpt_account_service',
            'cooldown_service', 
            'email_service',
            'order_redemption_service',
            'shopee_messaging_service'
        ]
        
        unhealthy_services = []
        
        for service_name in service_names:
            try:
                # Basic service health check
                services_status['services'][service_name] = {
                    'status': 'healthy',
                    'last_check': datetime.now().isoformat()
                }
            except Exception as e:
                services_status['services'][service_name] = {
                    'status': 'unhealthy',
                    'error': str(e),
                    'last_check': datetime.now().isoformat()
                }
                unhealthy_services.append(service_name)
        
        if unhealthy_services:
            services_status['status'] = 'unhealthy'
            services_status['unhealthy_services'] = unhealthy_services
        
        return services_status
    
    def _check_data_storage_health(self) -> Dict[str, Any]:
        """Check data storage health"""
        try:
            data_dir = self.config.get('data_config', {}).get('data_directory', 'configs/data/openai_plus_redeem')
            
            # Check directory exists and is writable
            if not os.path.exists(data_dir):
                os.makedirs(data_dir, exist_ok=True)
            
            # Test write access
            test_file = os.path.join(data_dir, '.health_check')
            with open(test_file, 'w') as f:
                f.write(str(time.time()))
            
            # Clean up test file
            os.remove(test_file)
            
            # Check disk space
            disk_usage = psutil.disk_usage(data_dir)
            free_space_gb = disk_usage.free / (1024**3)
            
            return {
                'status': 'healthy',
                'message': 'Data storage accessible and writable',
                'free_space_gb': round(free_space_gb, 2),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Data storage check failed: {e}',
                'timestamp': datetime.now().isoformat()
            }
    
    def _check_external_dependencies(self) -> Dict[str, Any]:
        """Check external dependencies"""
        deps_status = {
            'status': 'healthy',
            'dependencies': {},
            'timestamp': datetime.now().isoformat()
        }
        
        # Check Gmail IMAP connectivity
        try:
            import imaplib
            import ssl
            
            email_config = self.config.get('email_config', {})
            server = email_config.get('imap_server', 'imap.gmail.com')
            port = email_config.get('imap_port', 993)
            
            # Test connection (don't authenticate)
            context = ssl.create_default_context()
            with imaplib.IMAP4_SSL(server, port, ssl_context=context) as imap:
                deps_status['dependencies']['gmail_imap'] = {
                    'status': 'healthy',
                    'message': 'IMAP server reachable'
                }
                
        except Exception as e:
            deps_status['dependencies']['gmail_imap'] = {
                'status': 'unhealthy',
                'message': f'IMAP connectivity failed: {e}'
            }
            deps_status['status'] = 'unhealthy'
        
        return deps_status
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Process count
            process_count = len(psutil.pids())
            
            # Determine status based on thresholds
            thresholds = self.config.get('monitoring_config', {}).get('alerts', {}).get('alert_thresholds', {})
            cpu_threshold = thresholds.get('cpu_usage_percent', 80)
            memory_threshold = thresholds.get('memory_usage_percent', 90)
            disk_threshold = thresholds.get('disk_usage_percent', 85)
            
            status = 'healthy'
            warnings = []
            
            if cpu_percent > cpu_threshold:
                status = 'warning'
                warnings.append(f'High CPU usage: {cpu_percent}%')
            
            if memory_percent > memory_threshold:
                status = 'warning'
                warnings.append(f'High memory usage: {memory_percent}%')
            
            if disk_percent > disk_threshold:
                status = 'warning'
                warnings.append(f'High disk usage: {disk_percent}%')
            
            return {
                'status': status,
                'cpu_percent': round(cpu_percent, 2),
                'memory_percent': round(memory_percent, 2),
                'disk_percent': round(disk_percent, 2),
                'process_count': process_count,
                'warnings': warnings,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'System resource check failed: {e}',
                'timestamp': datetime.now().isoformat()
            }
    
    def collect_metrics(self) -> Dict[str, Any]:
        """Collect performance metrics"""
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'system': {},
            'application': {}
        }
        
        try:
            # System metrics
            metrics['system'] = {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage_percent': (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100,
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            }
            
            # Application metrics (would be populated by plugin)
            metrics['application'] = {
                'active_sessions': 0,  # Would be populated by plugin
                'requests_per_minute': 0,  # Would be populated by plugin
                'error_rate': 0,  # Would be populated by plugin
                'response_time_avg': 0  # Would be populated by plugin
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting metrics: {e}")
        
        # Store metrics
        self.metrics[datetime.now().isoformat()] = metrics
        
        # Keep only recent metrics (last 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.metrics = {k: v for k, v in self.metrics.items() 
                       if datetime.fromisoformat(k.replace('Z', '+00:00').replace('+00:00', '')) > cutoff_time}
        
        return metrics
    
    def check_alerts(self, health_status: Dict[str, Any], metrics: Dict[str, Any]):
        """Check for alert conditions"""
        alerts_config = self.config.get('monitoring_config', {}).get('alerts', {})
        
        if not alerts_config.get('enabled', False):
            return
        
        current_alerts = []
        
        # Check health status alerts
        if health_status['overall_status'] != 'healthy':
            current_alerts.append({
                'type': 'health_check_failed',
                'severity': 'critical',
                'message': f"Health check failed: {health_status.get('failed_checks', [])}",
                'timestamp': datetime.now().isoformat()
            })
        
        # Check resource alerts
        thresholds = alerts_config.get('alert_thresholds', {})
        system_metrics = metrics.get('system', {})
        
        if system_metrics.get('memory_percent', 0) > thresholds.get('memory_usage_percent', 90):
            current_alerts.append({
                'type': 'high_memory_usage',
                'severity': 'warning',
                'message': f"High memory usage: {system_metrics['memory_percent']}%",
                'timestamp': datetime.now().isoformat()
            })
        
        if system_metrics.get('disk_usage_percent', 0) > thresholds.get('disk_usage_percent', 85):
            current_alerts.append({
                'type': 'high_disk_usage',
                'severity': 'warning',
                'message': f"High disk usage: {system_metrics['disk_usage_percent']}%",
                'timestamp': datetime.now().isoformat()
            })
        
        # Send alerts if any
        if current_alerts:
            self._send_alerts(current_alerts)
    
    def _send_alerts(self, alerts: List[Dict[str, Any]]):
        """Send alerts via configured channels"""
        for alert in alerts:
            self.logger.warning(f"ALERT: {alert['message']}")
            
            # Add to alerts history
            self.alerts.append(alert)
            
            # Keep only recent alerts (last 7 days)
            cutoff_time = datetime.now() - timedelta(days=7)
            self.alerts = [a for a in self.alerts 
                          if datetime.fromisoformat(a['timestamp'].replace('Z', '+00:00').replace('+00:00', '')) > cutoff_time]
    
    def get_status_report(self) -> Dict[str, Any]:
        """Get comprehensive status report"""
        return {
            'monitoring_active': self.monitoring_active,
            'last_health_check': self.last_health_check,
            'recent_metrics': list(self.metrics.values())[-10:] if self.metrics else [],
            'recent_alerts': self.alerts[-10:] if self.alerts else [],
            'timestamp': datetime.now().isoformat()
        }

def main():
    """Main monitoring function"""
    monitor = ProductionMonitor()
    
    try:
        # Run initial health check
        health_status = monitor.run_health_checks()
        print(f"Initial health check: {health_status['overall_status']}")
        
        if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
            # Start continuous monitoring
            monitor.start_monitoring()
            
            try:
                while True:
                    time.sleep(60)
            except KeyboardInterrupt:
                monitor.stop_monitoring()
                print("Monitoring stopped")
        else:
            # Single health check
            print(json.dumps(health_status, indent=2))
            
    except Exception as e:
        print(f"Monitoring failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
