import json
import os

MANUAL_ORDERS_FILE = 'configs/data/manual_orders.json'

def get_all_manual_orders():
    if not os.path.exists(MANUAL_ORDERS_FILE):
        return []

    with open(MANUAL_ORDERS_FILE, 'r', encoding='utf-8') as f:
        manual_orders = json.load(f)

    return manual_orders

def add_manual_order(order):
    manual_orders = get_all_manual_orders()
    manual_orders.append(order)

    with open(MANUAL_ORDERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(manual_orders, f, indent=2, ensure_ascii=False)

def update_manual_order(order_sn, new_status):
    manual_orders = get_all_manual_orders()

    for order in manual_orders:
        if order['order_sn'] == order_sn:
            order['status'] = new_status
            break

    with open(MANUAL_ORDERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(manual_orders, f, indent=2, ensure_ascii=False)