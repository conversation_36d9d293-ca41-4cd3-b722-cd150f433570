# Search Conversation Endpoint Documentation

## Overview

The `/chat/search_conversation` endpoint uses <PERSON><PERSON>'s combined search API to find conversations and related information by username. This endpoint has been updated to use the correct Shopee API endpoint and supports both raw and processed response formats.

## Endpoint Details

**URL:** `GET /chat/search_conversation`

**Parameters:**
- `username` (required): Username to search for
- `raw` (optional, default: false): Return raw search results from Shopee API

## Shopee API Integration

### API Endpoint Used
```
https://seller.shopee.com.my/webchat/api/coreapi/v1.2/search/combined_search
```

### Request Parameters
The endpoint sends the following parameters to Shopee's API:
```json
{
  "per_page": 20,
  "keyword": "username_to_search",
  "type": 3,
  "biz_id": 0,
  "_uid": "0-{shop_id}",
  "_v": "8.8.9",
  "csrf_token": "csrf_token_from_cookies",
  "SPC_CDS_CHAT": "spc_cds_chat_token",
  "x-shop-region": "MY",
  "_api_source": "webchat"
}
```

## Response Formats

### Raw Response (`raw=true`)

When `raw=true`, the endpoint returns the complete response from Shopee's combined search API:

```json
{
  "data": {
    "order_search_result": {
      "orders": [],
      "per_page": 20,
      "total": 0
    },
    "conversation_search_result": {
      "conversations": [
        {
          "buyer_id": **********,
          "buyer_avatar": "https://cf.shopee.com.my/file/...",
          "buyer_avatar_hash": "my-********-7rask-...",
          "buyer_name": "me0tn_14qo",
          "shop_id": *********,
          "shop_name": "mtyb_official",
          "shop_region": "MY",
          "order_id": 0,
          "conversation_id": "************7388532",
          "to_id": **********,
          "to_account_id": 0,
          "to_avatar": "https://cf.shopee.com.my/file/...",
          "to_avatar_hash": "my-********-7rask-...",
          "to_name": "me0tn_14qo",
          "message_id": "2316066823087669617",
          "message_type": "text",
          "message_time": **********,
          "text": "Hi me0tn_14qo...",
          "msg_tag": {
            "is_chatbot_session": false,
            "is_rcmd_reply": false
          },
          "biz_id": 0,
          "user_role": 0,
          "opposite_user_role": 0
        }
      ],
      "per_page": 20,
      "total": 26,
      "next_offset": "{\"1\":1601,\"2\":0,\"3\":0}",
      "has_more": false
    },
    "agent_msg_search_result": {
      "messages": [],
      "total": 0,
      "next_offset": "",
      "has_more": false
    }
  }
}
```

### Processed Response (`raw=false`, default)

When `raw=false` (default), the endpoint processes the search results and returns conversation information:

```json
{
  "data": {
    "id": "************7388532",
    "conversation_id": "************7388532",
    "to_id": **********,
    "to_name": "me0tn_14qo",
    "to_avatar": "https://cf.shopee.com.my/file/...",
    "to_avatar_hash": "my-********-7rask-...",
    "shop_id": *********,
    "shop_name": "mtyb_official",
    "shop_region": "MY",
    "buyer_id": **********,
    "buyer_name": "me0tn_14qo"
  }
}
```

## Usage Examples

### Example 1: Get Raw Search Results
```bash
curl "http://localhost:8000/chat/search_conversation?username=me0tn_14qo&raw=true"
```

### Example 2: Get Processed Conversation Info
```bash
curl "http://localhost:8000/chat/search_conversation?username=me0tn_14qo"
```

### Example 3: Python Usage
```python
import requests

# Raw search results
response = requests.get(
    "http://localhost:8000/chat/search_conversation",
    params={"username": "me0tn_14qo", "raw": True}
)
raw_data = response.json()

# Processed conversation info
response = requests.get(
    "http://localhost:8000/chat/search_conversation",
    params={"username": "me0tn_14qo"}
)
conversation_info = response.json()
```

## Search Logic

The endpoint searches for conversations in the following order:

1. **Cache Check**: First checks if the username is cached
2. **Recent Conversations**: Searches recent conversations for the username
3. **Combined Search API**: Uses Shopee's combined search API
4. **Conversation Results**: Looks for matches in `conversation_search_result.conversations`
5. **Order Results**: If not found in conversations, checks `order_search_result.orders`
6. **User ID Lookup**: If found in orders, uses buyer_id to get conversation info

## Error Handling

The endpoint handles various error scenarios:

- **404 Not Found**: Username not found in search results
- **401 Unauthorized**: Authentication credentials expired or invalid
- **500 Internal Server Error**: API communication errors

## Implementation Notes

- The endpoint uses the exact parameter format that works with Shopee's API
- Parameters like `per_page`, `type`, and `biz_id` are sent as integers, not strings
- The `csrf_token` is used without URL encoding for better compatibility
- The search supports both `to_name` and `buyer_name` fields for username matching
- Results are cached to improve performance for subsequent requests

## Testing

Use the provided test script to verify the endpoint functionality:

```bash
cd ShopeeAPI
python test_search_endpoint.py
```

This will test both raw and processed response formats and display the results.
