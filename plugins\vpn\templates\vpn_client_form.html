{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <form method="POST" action="">
                    <div class="card-body">
                        {% if action == 'create' %}
                        <div class="form-group">
                            <label for="server_id">Server <span class="text-danger">*</span></label>
                            <select class="form-control" id="server_id" name="server_id" required>
                                <option value="">Select a server...</option>
                                {% for server in servers %}
                                <option value="{{ server.id }}"
                                        {% if request.args.get('server_id') == server.id|string %}selected{% endif %}>
                                    {{ server.name }} ({{ server.host }})
                                </option>
                                {% endfor %}
                            </select>
                            {% if request.args.get('server_id') %}
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i>
                                Pre-selected server from context. Xray service will be automatically restarted after client creation.
                            </small>
                            {% endif %}
                        </div>
                        {% endif %}
                        
                        <div class="form-group">
                            <label for="email">Email/Identifier <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="email" name="email" 
                                   value="{{ client.email if client else '' }}" required>
                            <small class="form-text text-muted">
                                Unique identifier for the client (email or custom ID)
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label for="expired_date">Expiry Date <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="expired_date" name="expired_date" 
                                   value="{{ client.expired_date if client else '' }}" 
                                   placeholder="DD-MM-YYYY or 'lifetime'" required>
                            <small class="form-text text-muted">
                                Format: DD-MM-YYYY (e.g., 31-12-2025) or 'lifetime' for no expiry
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label for="shopee_username">Shopee Username</label>
                            <input type="text" class="form-control" id="shopee_username" name="shopee_username" 
                                   value="{{ client.shopee_username if client else '' }}">
                        </div>
                        
                        {% if action == 'create' %}
                        <div class="form-group">
                            <label for="client_id">Custom Client ID</label>
                            <input type="text" class="form-control" id="client_id" name="client_id" 
                                   placeholder="Leave blank to auto-generate">
                            <small class="form-text text-muted">
                                Optional: Specify a custom UUID for the client
                            </small>
                        </div>
                        {% endif %}
                        
                        <div class="form-group">
                            <label for="description">Description</label>
                            <input type="text" class="form-control" id="description" name="description" 
                                   value="{{ client.description if client else '' }}"
                                   placeholder="Brief description of the client">
                        </div>
                        
                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="Additional notes or comments">{{ client.notes if client else '' }}</textarea>
                        </div>
                        
                        {% if action == 'edit' %}
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active"
                                       {{ 'checked' if client and client.is_active else '' }}>
                                <label class="custom-control-label" for="is_active">Client Active</label>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <strong>Client Info:</strong><br>
                            Client ID: {{ client.client_id }}<br>
                            Server: {{ client.server_id }}<br>
                            Created: {{ client.created_at }}<br>
                            Last Updated: {{ client.updated_at }}<br>
                            {% if client.last_connected %}
                            Last Connected: {{ client.last_connected }}<br>
                            {% endif %}
                            Data Usage: {{ client.data_usage }} bytes<br>
                            Days Until Expiry: {{ client.days_until_expiry }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ 'Update' if action == 'edit' else 'Create' }} Client
                        </button>
                        <a href="{{ url_for('vpn.clients') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Date picker for expiry date
    $('#expired_date').on('focus', function() {
        if (!$(this).val() || $(this).val() === 'lifetime') {
            // Suggest a date 30 days from now
            var date = new Date();
            date.setDate(date.getDate() + 30);
            var dd = String(date.getDate()).padStart(2, '0');
            var mm = String(date.getMonth() + 1).padStart(2, '0');
            var yyyy = date.getFullYear();
            $(this).attr('placeholder', dd + '-' + mm + '-' + yyyy + ' or "lifetime"');
        }
    });
    
    // Validate date format
    $('#expired_date').on('blur', function() {
        var value = $(this).val();
        if (value && value !== 'lifetime') {
            var regex = /^\d{2}-\d{2}-\d{4}$/;
            if (!regex.test(value)) {
                alert('Please use DD-MM-YYYY format or "lifetime"');
                $(this).focus();
            }
        }
    });
});
</script>
{% endblock %}