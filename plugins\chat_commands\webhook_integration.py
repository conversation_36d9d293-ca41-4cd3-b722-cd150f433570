"""
Webhook Integration for Chat Commands Plugin

This module handles the integration with ShopeeAPI webhooks
and provides a bridge between the webhook events and the chat command processor.
"""

import logging
import requests
import json
from typing import Dict, Any, Optional, Callable
from threading import Thread
import time

logger = logging.getLogger(__name__)


class WebhookIntegration:
    """Handles webhook integration with ShopeeAPI"""
    
    def __init__(self, plugin_instance):
        self.plugin = plugin_instance
        self.webhook_url = None
        self.is_registered = False
        self.shopee_api_base_url = None
        
    def setup_integration(self, shopee_api_base_url: str = None):
        """Setup integration with ShopeeAPI"""
        try:
            if shopee_api_base_url:
                self.shopee_api_base_url = shopee_api_base_url
            else:
                # Try to detect ShopeeAPI URL from environment or config
                self.shopee_api_base_url = self._detect_shopee_api_url()

            if self.shopee_api_base_url:
                # Get SteamCodeTool base URL from plugin config
                webhook_config = self.plugin.command_service.get_webhook_config()
                steamcodetool_base_url = webhook_config.steamcodetool_base_url if webhook_config else "http://localhost:5000"

                self.webhook_url = f"{steamcodetool_base_url}/chat-commands/api/webhook"
                logger.info(f"Webhook integration setup with URL: {self.webhook_url}")
                logger.info(f"ShopeeAPI base URL: {self.shopee_api_base_url}")
                return True
            else:
                logger.warning("Could not determine ShopeeAPI base URL")
                return False

        except Exception as e:
            logger.error(f"Error setting up webhook integration: {e}")
            return False
    
    def register_webhook(self) -> bool:
        """Register webhook with ShopeeAPI"""
        try:
            if not self.shopee_api_base_url:
                logger.warning("ShopeeAPI base URL not configured")
                return False
            
            # Register webhook endpoint for message_received events
            webhook_config = {
                "url": self.webhook_url,
                "events": ["message_received"],
                "enabled": True
            }
            
            # This would be the actual API call to register the webhook
            # For now, we'll simulate it and log the configuration
            logger.info(f"Registering webhook with ShopeeAPI: {webhook_config}")
            
            # TODO: Implement actual webhook registration API call
            # response = requests.post(
            #     f"{self.shopee_api_base_url}/api/webhooks/register",
            #     json=webhook_config
            # )
            
            self.is_registered = True
            logger.info("Webhook registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error registering webhook: {e}")
            return False
    
    def unregister_webhook(self) -> bool:
        """Unregister webhook from ShopeeAPI"""
        try:
            if not self.is_registered:
                return True
            
            # TODO: Implement actual webhook unregistration API call
            # response = requests.delete(
            #     f"{self.shopee_api_base_url}/api/webhooks/unregister",
            #     json={"url": self.webhook_url}
            # )
            
            self.is_registered = False
            logger.info("Webhook unregistered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error unregistering webhook: {e}")
            return False
    
    def _detect_shopee_api_url(self) -> Optional[str]:
        """Try to detect ShopeeAPI base URL"""
        try:
            # Try common URLs
            possible_urls = [
                "https://shop.api.limjianhui.com",
                "http://localhost:8000",
                "http://127.0.0.1:8000",
                "http://localhost:5000",
                "http://127.0.0.1:5000"
            ]
            
            for url in possible_urls:
                try:
                    # Use root endpoint instead of /health since ShopeeAPI doesn't have /health
                    response = requests.get(f"{url}/", timeout=2)
                    if response.status_code == 200:
                        try:
                            response_data = response.json()
                            # Check if this is the expected ShopeeAPI response
                            if response_data.get('message') == 'Shopee API Service':
                                logger.info(f"Detected ShopeeAPI at: {url}")
                                return url
                        except ValueError:
                            # Not JSON or not the expected response
                            pass
                except:
                    continue
            
            logger.warning("Could not auto-detect ShopeeAPI URL")
            return None
            
        except Exception as e:
            logger.error(f"Error detecting ShopeeAPI URL: {e}")
            return None
    
    def process_webhook_event(self, event_data: Dict[str, Any]) -> bool:
        """Process incoming webhook event"""
        try:
            event_type = event_data.get('event', '')
            
            if event_type == 'message_received':
                # Process message_received event
                return self._process_message_received(event_data)
            else:
                logger.debug(f"Ignoring webhook event type: {event_type}")
                return True
                
        except Exception as e:
            logger.error(f"Error processing webhook event: {e}")
            return False
    
    def _process_message_received(self, event_data: Dict[str, Any]) -> bool:
        """Process message_received webhook event"""
        try:
            # Extract message data
            message = event_data.get('message', {})
            conversation_id = event_data.get('conversation_id', '')
            
            # Check if message is from customer (not from us)
            send_by_yourself = message.get('send_by_yourself', False)
            if send_by_yourself:
                logger.debug("Ignoring message sent by ourselves")
                return True
            
            # Get message content
            content = message.get('content', {})
            text = content.get('text', '')
            
            # Only process messages that start with #
            if not text.startswith('#'):
                logger.debug("Message doesn't start with #, ignoring")
                return True
            
            logger.info(f"Processing command message: {text}")
            
            # Use the plugin's message processor
            if self.plugin and self.plugin.message_processor:
                responses = self.plugin.message_processor.process_webhook_message(event_data)
                
                if responses:
                    # Send responses using ShopeeAPI
                    return self._send_responses(event_data, responses)
                else:
                    logger.debug("No responses generated for message")
                    return True
            else:
                logger.error("Plugin message processor not available")
                return False
                
        except Exception as e:
            logger.error(f"Error processing message_received event: {e}")
            return False
    
    def _send_responses(self, original_event: Dict[str, Any], responses) -> bool:
        """Send responses back to Shopee"""
        try:
            if not self.shopee_api_base_url:
                logger.error("ShopeeAPI base URL not configured")
                return False

            # Extract sender information using the same logic as WebhookMessage
            sender_name = None

            # Try to get sender name from the new webhook format
            if original_event.get('type') == 'shopee_message':
                data = original_event.get('data', {})
                message_content = data.get('message_content', '')
                if isinstance(message_content, str):
                    try:
                        import json
                        parsed_content = json.loads(message_content)

                        # Determine the correct sender name based on message direction
                        send_by_yourself = parsed_content.get('send_by_yourself', False)

                        if send_by_yourself:
                            # This is a message we sent, so the customer is the recipient (to_user_name)
                            sender_name = parsed_content.get('to_user_name', '')
                        else:
                            # This is a message we received, so the customer is the sender (from_user_name)
                            sender_name = parsed_content.get('from_user_name', '')

                        # Fallback to from_name if no username found
                        if not sender_name:
                            sender_name = parsed_content.get('from_name', '')

                    except json.JSONDecodeError:
                        logger.warning("Failed to parse message_content for sender info in webhook_integration")

            # Fallback to legacy format
            if not sender_name:
                message = original_event.get('message', {})
                # For legacy format, also check send_by_yourself
                send_by_yourself = message.get('send_by_yourself', False)
                if send_by_yourself:
                    sender_name = message.get('to_user_name', '') or message.get('to_name', '')
                else:
                    sender_name = message.get('from_user_name', '') or message.get('from_name', '')

            if not sender_name:
                logger.error("Could not determine sender name")
                return False
            
            success_count = 0

            for response in responses:
                try:
                    # Send text message
                    if response.text:
                        payload = {
                            'username': sender_name,
                            'text': response.text
                        }

                        result = requests.post(
                            f"{self.shopee_api_base_url}/api/chat/send-message",
                            json=payload,
                            timeout=30
                        )

                        if result.status_code == 200:
                            success_count += 1
                            logger.info(f"Sent text response to {sender_name}")
                        else:
                            logger.error(f"Failed to send text response: {result.status_code}")

                    # Send image messages
                    for image_url in response.image_urls:
                        payload = {
                            'username': sender_name,
                            'image_url': image_url
                        }

                        result = requests.post(
                            f"{self.shopee_api_base_url}/chat/send_image",
                            json=payload,
                            timeout=30
                        )

                        if result.status_code == 200:
                            success_count += 1
                            logger.info(f"Sent image response to {sender_name}")
                        else:
                            logger.error(f"Failed to send image response: {result.status_code}")

                    # Mark conversation as unread if requested
                    if hasattr(response, 'mark_as_unread') and response.mark_as_unread:
                        try:
                            unread_payload = {
                                'username': sender_name
                            }

                            unread_result = requests.post(
                                f"{self.shopee_api_base_url}/api/chat/mark-unread",
                                json=unread_payload,
                                timeout=30
                            )

                            if unread_result.status_code == 200:
                                logger.info(f"Marked conversation as unread for {sender_name}")
                            else:
                                logger.error(f"Failed to mark conversation as unread: {unread_result.status_code}")
                        except Exception as e:
                            logger.error(f"Error marking conversation as unread: {e}")

                except Exception as e:
                    logger.error(f"Error sending individual response: {e}")
            
            logger.info(f"Successfully sent {success_count} responses")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending responses: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get webhook integration status"""
        return {
            'webhook_url': self.webhook_url,
            'is_registered': self.is_registered,
            'shopee_api_base_url': self.shopee_api_base_url,
            'integration_active': self.webhook_url is not None and self.is_registered
        }


class WebhookServer:
    """Simple webhook server for receiving events"""
    
    def __init__(self, webhook_integration: WebhookIntegration, port: int = 8080):
        self.webhook_integration = webhook_integration
        self.port = port
        self.server_thread = None
        self.is_running = False
    
    def start(self):
        """Start the webhook server"""
        try:
            from flask import Flask, request, jsonify
            
            app = Flask(__name__)
            
            @app.route('/webhook', methods=['POST'])
            def handle_webhook():
                try:
                    data = request.get_json()
                    if data:
                        success = self.webhook_integration.process_webhook_event(data)
                        return jsonify({'success': success})
                    else:
                        return jsonify({'error': 'No data provided'}), 400
                except Exception as e:
                    logger.error(f"Error in webhook handler: {e}")
                    return jsonify({'error': str(e)}), 500
            
            @app.route('/health', methods=['GET'])
            def health_check():
                return jsonify({'status': 'ok'})
            
            def run_server():
                app.run(host='0.0.0.0', port=self.port, debug=False)
            
            self.server_thread = Thread(target=run_server, daemon=True)
            self.server_thread.start()
            self.is_running = True
            
            logger.info(f"Webhook server started on port {self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting webhook server: {e}")
            return False
    
    def stop(self):
        """Stop the webhook server"""
        try:
            self.is_running = False
            logger.info("Webhook server stopped")
            return True
        except Exception as e:
            logger.error(f"Error stopping webhook server: {e}")
            return False
