# Main Files Consolidation

## Overview
The three confusing main files have been consolidated into a single, unified main application with plugin architecture.

## Previous Files (moved to backup/)
- `main.py` → `backup/main-original.py` - Original monolithic Flask app
- `main-full.py` → `backup/main-full.py` - Shopee API-focused version  
- `main_plugin.py` → **NEW `main.py`** - Plugin-based architecture (now the main file)

## New Structure

### Main Application (`main.py`)
- **Plugin-based architecture** for better modularity
- **All legacy routes preserved** for backward compatibility
- **Integrated scheduler and credential manager**
- **Unified error handling**
- **CORS enabled**

### Key Features
1. **Plugin System**: Modular architecture with plugins for Steam, Netflix, VPN, Canva, and Shopee
2. **Legacy Compatibility**: All original routes from main.py preserved
3. **Shopee Integration**: Shopee API functionality moved to dedicated plugin
4. **Configuration Management**: Centralized plugin configuration
5. **Health Monitoring**: Built-in health check and plugin status endpoints

## New Plugin: Shopee
The Shopee functionality from `main-full.py` has been converted to a plugin:

### Routes (now under `/api/shopee/`)
- `/api/shopee/orders/to_ship` - Get orders to ship
- `/api/shopee/orders/shipped` - Get shipped orders  
- `/api/shopee/orders/completed` - Get completed orders
- `/api/shopee/orders/search` - Search orders by SN
- `/api/shopee/orders/ship` - Ship an order
- `/api/shopee/orders/status` - Get order status
- `/api/shopee/conversation/by_order` - Get conversation by order
- `/api/shopee/conversation/by_username` - Get conversation by username
- `/api/shopee/chat/send` - Send chat message

### Configuration
Plugin configuration added to `plugin_config.json` under the "shopee" section.

## Migration Benefits
1. **No More Confusion**: Single main.py file
2. **Better Organization**: Plugin-based architecture
3. **Easier Maintenance**: Modular code structure
4. **Backward Compatibility**: All existing functionality preserved
5. **Future-Proof**: Easy to add new plugins

## Usage
```bash
# Run the application (same as before)
python main.py

# Or use the factory function
from main import create_app
app = create_app()
```

## Plugin Management
- View plugins: `GET /api/plugins`
- Plugin status: `GET /api/plugins/status`  
- Enable plugin: `POST /api/plugins/{name}/enable`
- Disable plugin: `POST /api/plugins/{name}/disable`
- Plugin config: `GET/PUT /api/plugins/{name}/config`

## Notes
- All original functionality is preserved
- Docker configurations remain unchanged
- Extension compatibility maintained
- No breaking changes to existing APIs
