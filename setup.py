"""
Setup script for ShopeeAPI package.
"""
from setuptools import setup, find_packages
import os

# Read requirements from requirements.txt
with open(os.path.join('ShopeeAPI', 'requirements.txt'), 'r') as f:
    requirements = [line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')]

# Read long description from README.md
with open('ShopeeAPI/README.md', 'r', encoding='utf-8') as f:
    long_description = f.read()

setup(
    name="shopee-api",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A FastAPI application for interacting with the Shopee Seller API",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/shopee-api",
    packages=find_packages(),
    include_package_data=True,
    package_data={
        'ShopeeAPI': [
            'requirements.txt',
            'config.json.example',
            'templates/*.html',
            'static/css/*.css',
            'static/js/*.js',
        ],
    },
    install_requires=requirements,
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Private :: Do Not Upload",
    ],
    python_requires='>=3.8',
    entry_points={
        'console_scripts': [
            'shopee-api=ShopeeAPI.main:main',
        ],
    },
)
