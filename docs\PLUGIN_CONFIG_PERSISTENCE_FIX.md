# Plugin Configuration Persistence Fix

## 🎯 Problem Description

你遇到的问题是 `@plugins/chat_commands/` 插件的全部配置都没有保存到 Docker 挂载的持久化目录 `/www/wwwroot/steamcodetool-data/config:/app/configs`，导致每次更新 Docker 容器时所有设置都需要重新配置。

## 🔍 Root Cause Analysis

### 原始问题
1. **错误的配置保存路径**: 插件配置保存在 `plugins/chat_commands/config.json`
2. **非持久化目录**: 插件目录不在 Docker 挂载的持久化路径中
3. **容器更新丢失**: 每次 Docker 容器更新，插件目录被重置，配置丢失

### 影响的插件
- `chat_commands` - 聊天命令插件
- `vpn_config_generator` - VPN 配置生成器插件

## ✅ Solution Implemented

### 1. 配置路径重定向
修改插件服务类，将配置文件保存到 `configs/plugins/` 目录：

```python
# 旧路径 (非持久化)
self.config_file = os.path.join(plugin_dir, 'config.json')

# 新路径 (持久化)
self.configs_dir = os.path.join(os.path.dirname(os.path.dirname(plugin_dir)), 'configs', 'plugins', 'chat_commands')
self.config_file = os.path.join(self.configs_dir, 'config.json')
```

### 2. 自动迁移机制
实现了自动迁移功能，将现有配置从插件目录迁移到持久化目录：

```python
def _migrate_legacy_files(self):
    """Migrate legacy config files from plugin directory to configs directory"""
    if os.path.exists(self.legacy_config_file) and not os.path.exists(self.config_file):
        shutil.copy2(self.legacy_config_file, self.config_file)
```

### 3. 目录结构优化
创建了新的配置目录结构：

```
configs/
├── plugins/                    # 🆕 插件配置 (Docker 持久化)
│   ├── chat_commands/
│   │   ├── config.json        # Webhook、调试、命令设置
│   │   └── commands.json      # 聊天命令定义
│   └── vpn_config_generator/
│       ├── config.json        # API 设置、生成器设置
│       ├── templates.json     # VPN 配置模板
│       └── telco_configs.json # 电信运营商配置
```

## 🔧 Files Modified

### Core Plugin Services
1. **`plugins/chat_commands/services.py`**
   - 修改配置文件路径到 `configs/plugins/chat_commands/`
   - 添加自动迁移功能
   - 保持向后兼容性

2. **`plugins/vpn_config_generator/services.py`**
   - 修改配置文件路径到 `configs/plugins/vpn_config_generator/`
   - 添加自动迁移功能
   - 支持多个配置文件迁移

### Documentation Updates
3. **`configs/README.md`**
   - 添加插件配置目录说明
   - 更新目录结构图

4. **`docs/STEAMCODETOOL_DOCKER.md`**
   - 添加插件配置持久化说明
   - 更新 Docker 挂载要求

5. **`docs/PLUGIN_CONFIG_MIGRATION.md`** (新文件)
   - 详细的迁移指南
   - 故障排除说明
   - 开发者指南

## 🧪 Testing Results

运行了完整的测试验证：

```
🔧 Testing Plugin Configuration Migration
==================================================
✅ configs directory exists
✅ configs/plugins directory exists  
✅ configs/plugins/chat_commands directory exists
✅ configs/plugins/vpn_config_generator directory exists
✅ Config file migrated successfully
✅ Config content migrated correctly
✅ VPN config file migrated successfully
✅ VPN config content migrated correctly

🎉 All tests passed! Plugin configuration migration is working correctly.
```

## 🐳 Docker Persistence Verification

### 现有挂载配置
```yaml
volumes:
  - /www/wwwroot/steamcodetool-data/config:/app/configs
```

### 持久化的配置文件
✅ **现在会持久化**:
- `configs/plugins/chat_commands/config.json` - 你的 Webhook 设置
- `configs/plugins/chat_commands/commands.json` - 聊天命令定义
- `configs/plugins/vpn_config_generator/config.json` - VPN 插件设置
- `configs/plugins/vpn_config_generator/templates.json` - VPN 模板
- `configs/plugins/vpn_config_generator/telco_configs.json` - 电信配置

## 🎉 Benefits Achieved

1. **配置持久化**: Docker 容器更新时配置不再丢失
2. **自动迁移**: 现有配置自动迁移，无需手动操作
3. **向后兼容**: 现有部署继续正常工作
4. **统一管理**: 所有配置集中在 `configs/` 目录
5. **更好的组织**: 清晰的配置文件分类

## 🚀 Next Steps

1. **部署更新**: 更新 Docker 容器到新版本
2. **验证迁移**: 检查配置是否自动迁移到 `configs/plugins/`
3. **测试持久化**: 更新容器后验证配置是否保持
4. **清理旧文件**: 确认迁移成功后可以删除插件目录中的旧配置文件

## 📝 Important Notes

- **自动迁移**: 首次启动时会自动迁移现有配置
- **无需手动操作**: 迁移过程完全自动化
- **安全保障**: 只有在新位置不存在文件时才会迁移
- **日志记录**: 迁移过程会记录在应用日志中

现在你的 chat_commands 插件配置将会正确保存到 Docker 挂载的持久化目录中，再也不会因为容器更新而丢失配置了！🎉
