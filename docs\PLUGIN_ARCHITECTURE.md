# SteamCodeTool Plugin Architecture

## 概述

SteamCodeTool 已重构为基于插件的架构，将 Steam、Netflix、VPN、Canva 等功能模块化为独立插件。这种设计提高了系统的可扩展性、维护性和灵活性。

## 架构特点

### 🔌 插件化设计
- **模块化**：每个产品功能（Steam、Netflix、VPN、Canva）都是独立插件
- **热插拔**：支持运行时启用/禁用插件
- **独立配置**：每个插件有自己的配置和数据存储
- **易扩展**：添加新产品只需开发新插件

### 🏗️ 核心架构
```
SteamCodeTool/
├── core/                    # 核心插件管理系统
│   ├── plugin_manager.py    # 插件管理器
│   └── __init__.py
├── plugins/                 # 插件目录
│   ├── steam/              # Steam 插件
│   ├── netflix/            # Netflix 插件
│   ├── vpn/                # VPN 插件
│   └── canva/              # Canva 插件
├── main_plugin.py          # 新的主应用（插件宿主）
├── plugin_config.json     # 插件配置文件
└── migrate_to_plugins.py   # 迁移脚本
```

## 插件结构

每个插件都遵循标准结构：

```
plugins/[plugin_name]/
├── __init__.py             # 插件信息
├── plugin.py               # 插件主类
├── services/               # 业务逻辑服务
│   ├── service1.py
│   └── service2.py
└── routes/                 # API 路由
    └── routes.py
```

## 快速开始

### 1. 迁移现有系统

```bash
# 运行迁移脚本
python migrate_to_plugins.py
```

迁移脚本会：
- 备份现有数据到 `backup_pre_plugin/`
- 创建插件数据目录结构
- 转换现有数据格式
- 生成插件配置文件

### 2. 启动插件系统

```bash
# 使用统一的主应用
python main.py
```

### 3. 管理插件

访问插件管理界面：
```
http://localhost:5000/admin/plugins
```

## 插件管理

### API 端点

- `GET /api/plugins` - 获取所有插件信息
- `GET /api/plugins/status` - 获取插件状态
- `POST /api/plugins/{name}/enable` - 启用插件
- `POST /api/plugins/{name}/disable` - 禁用插件
- `GET /api/plugins/{name}/config` - 获取插件配置
- `PUT /api/plugins/{name}/config` - 更新插件配置

### 插件状态管理

```python
# 启用插件
curl -X POST http://localhost:5000/api/plugins/steam/enable

# 禁用插件
curl -X POST http://localhost:5000/api/plugins/steam/disable

# 获取插件状态
curl http://localhost:5000/api/plugins/status
```

## 插件开发

### 创建新插件

1. **创建插件目录**
```bash
mkdir plugins/my_plugin
```

2. **实现插件类**
```python
# plugins/my_plugin/plugin.py
from core.plugin_manager import PluginInterface

class Plugin(PluginInterface):
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "my_plugin"
        self.version = "1.0.0"
        self.description = "My custom plugin"
        
    def initialize(self) -> bool:
        # 初始化逻辑
        return True
        
    def shutdown(self) -> bool:
        # 清理逻辑
        return True
        
    def get_blueprint(self):
        # 返回 Flask 蓝图
        return my_blueprint
        
    def get_config_schema(self):
        # 返回配置模式
        return {...}
```

3. **添加到配置**
```json
{
  "my_plugin": {
    "enabled": true,
    "custom_config": "value"
  }
}
```

## 现有插件

### Steam 插件
- **功能**：Steam 认证码获取、库存管理
- **端点**：`/api/steam/*`
- **配置**：邮箱凭据、库存设置、会话管理

### Netflix 插件
- **功能**：Netflix 登录码获取、会话管理
- **端点**：`/api/netflix/*`
- **配置**：账户信息、会话设置、订单处理

### VPN 插件
- **功能**：VPN 服务器管理、客户端配置
- **端点**：`/api/vpn/*`
- **配置**：服务器列表、客户端设置

### Canva 插件
- **功能**：Canva 邀请链接生成、订单管理
- **端点**：`/api/canva/*`
- **配置**：API 密钥、订单设置

## 配置管理

### 插件配置文件 (plugin_config.json)

```json
{
  "steam": {
    "enabled": true,
    "email_config": {
      "imap_server": "imap.gmail.com",
      "credentials": [...]
    },
    "inventory_config": {...},
    "order_config": {...}
  },
  "netflix": {
    "enabled": true,
    "accounts": [...],
    "session_config": {...}
  }
}
```

### 动态配置更新

```python
# 更新插件配置
import requests

config = {
    "enabled": True,
    "new_setting": "value"
}

response = requests.put(
    'http://localhost:5000/api/plugins/steam/config',
    json=config
)
```

## 部署

### Docker 部署

使用新的 Docker Compose 文件：

```bash
# 使用插件版本
docker-compose -f docker-compose.plugins.yml up -d
```

### 数据持久化

插件系统使用统一的数据目录：
```
data/
├── steam/          # Steam 插件数据
├── netflix/        # Netflix 插件数据
├── vpn/           # VPN 插件数据
└── canva/         # Canva 插件数据
```

## 优势

### 🚀 开发效率
- **专注开发**：为新产品只需开发对应插件
- **独立测试**：每个插件可独立测试和部署
- **代码复用**：核心功能可在插件间共享

### 🔧 维护性
- **模块化**：问题定位更容易
- **独立更新**：可单独更新特定插件
- **配置隔离**：插件配置互不影响

### 📈 可扩展性
- **水平扩展**：轻松添加新产品插件
- **功能扩展**：现有插件功能可独立扩展
- **第三方插件**：支持第三方插件开发

## 迁移注意事项

1. **数据备份**：迁移前会自动备份所有数据
2. **配置检查**：检查生成的 `plugin_config.json`
3. **功能测试**：迁移后测试所有功能
4. **部署更新**：更新部署脚本使用新的 Docker 配置

## 故障排除

### 插件加载失败
```bash
# 检查插件日志
tail -f logs/plugin_manager.log

# 检查插件状态
curl http://localhost:5000/api/plugins/status
```

### 配置问题
```bash
# 验证配置文件
python -c "import json; json.load(open('plugin_config.json'))"

# 重置插件配置
python migrate_to_plugins.py
```

## 未来规划

- **插件市场**：支持插件分发和安装
- **可视化配置**：Web 界面配置插件
- **插件依赖**：支持插件间依赖管理
- **性能监控**：插件性能监控和分析
