#!/usr/bin/env python3
"""
Integration Test Runner for OpenAI Plus Redeem Plugin

This script runs comprehensive integration tests for the plugin,
including end-to-end workflows, error handling, and performance tests.
"""

import sys
import os
import unittest
import time
from pathlib import Path

# Add plugin directory to Python path
plugin_dir = Path(__file__).parent
sys.path.insert(0, str(plugin_dir.parent.parent))

def run_integration_tests():
    """Run all integration tests"""
    print("=" * 60)
    print("OpenAI Plus Redeem Plugin - Integration Test Suite")
    print("=" * 60)
    
    # Discover and load integration tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover(
        start_dir=str(plugin_dir / 'tests'),
        pattern='test_integration.py',
        top_level_dir=str(plugin_dir)
    )
    
    # Configure test runner
    test_runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    print(f"\nRunning integration tests from: {plugin_dir / 'tests'}")
    print(f"Test discovery pattern: test_integration.py")
    print("-" * 60)
    
    # Run tests
    start_time = time.time()
    result = test_runner.run(test_suite)
    end_time = time.time()
    
    # Print summary
    print("\n" + "=" * 60)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    
    # Print detailed failure information
    if result.failures:
        print("\nFAILURES:")
        print("-" * 40)
        for test, traceback in result.failures:
            print(f"FAIL: {test}")
            print(traceback)
            print("-" * 40)
    
    if result.errors:
        print("\nERRORS:")
        print("-" * 40)
        for test, traceback in result.errors:
            print(f"ERROR: {test}")
            print(traceback)
            print("-" * 40)
    
    # Determine success
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n✅ All integration tests passed!")
    else:
        print("\n❌ Some integration tests failed!")
    
    print("=" * 60)
    
    return success

def run_specific_test_class(class_name):
    """Run a specific test class"""
    print(f"Running specific test class: {class_name}")
    
    # Import the test module
    from tests.test_integration import (
        TestPluginIntegration,
        TestErrorHandlingIntegration, 
        TestPerformanceIntegration
    )
    
    # Map class names to classes
    test_classes = {
        'TestPluginIntegration': TestPluginIntegration,
        'TestErrorHandlingIntegration': TestErrorHandlingIntegration,
        'TestPerformanceIntegration': TestPerformanceIntegration
    }
    
    if class_name not in test_classes:
        print(f"Error: Test class '{class_name}' not found.")
        print(f"Available classes: {', '.join(test_classes.keys())}")
        return False
    
    # Create test suite for specific class
    test_loader = unittest.TestLoader()
    test_suite = test_loader.loadTestsFromTestCase(test_classes[class_name])
    
    # Run tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0

def run_specific_test_method(class_name, method_name):
    """Run a specific test method"""
    print(f"Running specific test method: {class_name}.{method_name}")
    
    # Import the test module
    from tests.test_integration import (
        TestPluginIntegration,
        TestErrorHandlingIntegration,
        TestPerformanceIntegration
    )
    
    # Map class names to classes
    test_classes = {
        'TestPluginIntegration': TestPluginIntegration,
        'TestErrorHandlingIntegration': TestErrorHandlingIntegration,
        'TestPerformanceIntegration': TestPerformanceIntegration
    }
    
    if class_name not in test_classes:
        print(f"Error: Test class '{class_name}' not found.")
        return False
    
    # Create test suite for specific method
    test_suite = unittest.TestSuite()
    test_suite.addTest(test_classes[class_name](method_name))
    
    # Run test
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0

def print_usage():
    """Print usage information"""
    print("Usage:")
    print("  python run_integration_tests.py                    # Run all integration tests")
    print("  python run_integration_tests.py <TestClass>        # Run specific test class")
    print("  python run_integration_tests.py <TestClass> <method> # Run specific test method")
    print()
    print("Available test classes:")
    print("  - TestPluginIntegration")
    print("  - TestErrorHandlingIntegration") 
    print("  - TestPerformanceIntegration")
    print()
    print("Example:")
    print("  python run_integration_tests.py TestPluginIntegration")
    print("  python run_integration_tests.py TestPluginIntegration test_complete_redemption_workflow")

def main():
    """Main entry point"""
    args = sys.argv[1:]
    
    if len(args) == 0:
        # Run all integration tests
        success = run_integration_tests()
    elif len(args) == 1:
        if args[0] in ['--help', '-h', 'help']:
            print_usage()
            return 0
        else:
            # Run specific test class
            success = run_specific_test_class(args[0])
    elif len(args) == 2:
        # Run specific test method
        success = run_specific_test_method(args[0], args[1])
    else:
        print("Error: Too many arguments.")
        print_usage()
        return 1
    
    # Exit with appropriate code
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
