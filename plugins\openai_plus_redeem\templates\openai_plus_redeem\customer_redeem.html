<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT Plus Account Redemption - OpenAI Plus Redeem</title>
    <meta name="description" content="Redeem your ChatGPT Plus account using your Shopee order details">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/openai_plus_redeem.css') }}"
    <style>
        * { font-family: 'Inter', sans-serif; }
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .step-indicator {
            transition: all 0.3s ease;
        }
        .step-indicator.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .step-indicator.completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="p-4 opr-keyboard-nav">
    <!-- Skip Navigation Link -->
    <a href="#main-content" class="opr-skip-link">Skip to main content</a>

    <!-- Live Region for Screen Reader Announcements -->
    <div id="live-region" class="opr-live-region" aria-live="polite" aria-atomic="true"></div>

    <div class="container mx-auto max-w-4xl">
        <!-- Header -->
        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">🤖 ChatGPT Plus Account Redemption</h1>
            <p class="text-white/80">Redeem your ChatGPT Plus account from your Shopee order</p>
        </header>

        <!-- Step Indicator -->
        <nav aria-label="Redemption progress" class="flex justify-center mb-8">
            <ol class="flex space-x-4" role="list">
                <li class="step-indicator active flex items-center px-4 py-2 rounded-full text-sm font-medium"
                    aria-current="step" role="listitem">
                    <span class="mr-2" aria-hidden="true">1</span> Order Details
                </li>
                <li class="step-indicator flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gray-200"
                    role="listitem">
                    <span class="mr-2" aria-hidden="true">2</span> Processing
                </li>
                <li class="step-indicator flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gray-200"
                    role="listitem">
                    <span class="mr-2" aria-hidden="true">3</span> Account Details
                </li>
            </ol>
        </nav>

        <!-- Main Card -->
        <main id="main-content" class="card p-8">
            <!-- Step 1: Order Input Form -->
            <section id="orderInputStep" class="step-content" aria-labelledby="order-details-heading">
                <div class="text-center mb-6">
                    <h2 id="order-details-heading" class="text-2xl font-bold text-gray-800 mb-2">Enter Your Order Details</h2>
                    <p class="text-gray-600">Please provide your Shopee order information to redeem your ChatGPT Plus account</p>
                </div>

                <form id="redemptionForm" class="space-y-6" novalidate aria-describedby="form-instructions">
                    <div id="form-instructions" class="opr-sr-only">
                        Fill out the form below to redeem your ChatGPT Plus account. All required fields are marked with an asterisk.
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="opr-form-group">
                            <label for="orderId" class="block text-sm font-medium text-gray-700 mb-2 opr-label required">
                                Order ID
                            </label>
                            <input type="text" id="orderId" name="order_id"
                                   value="{{ order_id or '' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent opr-input"
                                   placeholder="Enter your Shopee order ID"
                                   required
                                   aria-describedby="orderId-help orderId-error"
                                   aria-invalid="false">
                            <div id="orderId-help" class="text-sm text-gray-500 mt-1">
                                Enter the order ID from your Shopee purchase confirmation
                            </div>
                            <div id="orderId-error" class="opr-error-message" role="alert" aria-live="polite" style="display: none;"></div>
                        </div>

                        <div class="opr-form-group">
                            <label for="buyerUsername" class="block text-sm font-medium text-gray-700 mb-2 opr-label required">
                                Username
                            </label>
                            <input type="text" id="buyerUsername" name="buyer_username"
                                   value="{{ username or '' }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent opr-input"
                                   placeholder="Your Shopee username"
                                   required
                                   aria-describedby="buyerUsername-help buyerUsername-error"
                                   aria-invalid="false">
                            <div id="buyerUsername-help" class="text-sm text-gray-500 mt-1">
                                Enter your Shopee account username
                            </div>
                            <div id="buyerUsername-error" class="opr-error-message" role="alert" aria-live="polite" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="sku" class="block text-sm font-medium text-gray-700 mb-2">
                                Product SKU <span class="text-red-500">*</span>
                            </label>
                            <select id="sku" name="sku" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    required>
                                <option value="">Select product type</option>
                                <option value="chatgpt_plus">ChatGPT Plus</option>
                                <option value="chatgpt_team">ChatGPT Team</option>
                                <option value="chatgpt_enterprise">ChatGPT Enterprise</option>
                            </select>
                        </div>

                        <div>
                            <label for="varSku" class="block text-sm font-medium text-gray-700 mb-2">
                                Variant SKU <span class="text-red-500">*</span>
                            </label>
                            <select id="varSku" name="var_sku"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    required>
                                <option value="">Select variant</option>
                                <option value="chatgpt_1_30">1 User - 30 Days</option>
                                <option value="chatgpt_3_30">3 Users - 30 Days</option>
                                <option value="chatgpt_5_30">5 Users - 30 Days</option>
                                <option value="chatgpt_10_30">10 Users - 30 Days</option>
                            </select>
                        </div>
                    </div>

                    <!-- Auto-populate notice -->
                    {% if auto_populate %}
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span class="text-blue-700 font-medium">Order details auto-populated from Shopee</span>
                        </div>
                    </div>
                    {% endif %}

                    <div class="flex justify-center">
                        <button type="submit" id="redeemBtn" 
                                class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center">
                            <span id="redeemBtnText">Start Redemption</span>
                            <div id="redeemBtnSpinner" class="loading-spinner ml-2 hidden"></div>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Step 2: Processing -->
            <div id="processingStep" class="step-content hidden">
                <div class="text-center">
                    <div class="loading-spinner mx-auto mb-4" style="width: 48px; height: 48px; border-width: 4px;"></div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">Processing Your Redemption</h2>
                    <p class="text-gray-600 mb-4">Please wait while we process your order and assign your ChatGPT account...</p>
                    
                    <div id="processingStatus" class="text-left bg-gray-50 rounded-lg p-4 max-w-md mx-auto">
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <div class="loading-spinner mr-2" style="width: 16px; height: 16px; border-width: 2px;"></div>
                                <span class="text-sm text-gray-600">Validating order details...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Account Details -->
            <div id="accountDetailsStep" class="step-content hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-green-500 text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">🎉 Your ChatGPT Plus Account is Ready!</h2>
                    <p class="text-gray-600">Your account has been successfully assigned. Here are your login details:</p>
                </div>

                <div id="accountInfo" class="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 mb-6">
                    <!-- Account details will be populated here -->
                </div>

                <div id="verificationSection" class="hidden">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-envelope text-yellow-500 mr-2"></i>
                            <span class="font-medium text-yellow-800">Email Verification Required</span>
                        </div>
                        <p class="text-yellow-700 text-sm mb-4">
                            Please check your email for a verification code from OpenAI and enter it below.
                        </p>
                        
                        <div class="flex space-x-2">
                            <input type="text" id="verificationCode" 
                                   class="flex-1 px-3 py-2 border border-yellow-300 rounded-md focus:ring-2 focus:ring-yellow-500"
                                   placeholder="Enter 6-digit verification code"
                                   maxlength="6">
                            <button id="searchCodeBtn" 
                                    class="bg-yellow-500 text-white px-4 py-2 rounded-md hover:bg-yellow-600 transition-colors">
                                Auto-Find Code
                            </button>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button id="newRedemptionBtn" 
                            class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        Redeem Another Order
                    </button>
                </div>
            </div>

            <!-- Error Display -->
            <div id="errorStep" class="step-content hidden">
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">Redemption Failed</h2>
                    <div id="errorMessage" class="text-gray-600 mb-6"></div>
                    
                    <div class="flex justify-center space-x-4">
                        <button id="retryBtn" 
                                class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            Try Again
                        </button>
                        <button id="contactSupportBtn" 
                                class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            Contact Support
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Check Section -->
        <div class="card p-6 mt-8">
            <h3 class="text-lg font-bold text-gray-800 mb-4">Check Existing Redemption Status</h3>
            <div class="flex space-x-2">
                <input type="text" id="statusRedemptionId" 
                       class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                       placeholder="Enter redemption ID">
                <button id="checkStatusBtn" 
                        class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                    Check Status
                </button>
            </div>
            <div id="statusResult" class="mt-4 hidden"></div>
        </div>
    </div>

    <script>
        // Global variables
        let currentStep = 1;
        let redemptionId = null;
        let accountInfo = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            
            // Auto-populate form if data is available
            {% if auto_populate and redemption_data %}
            const redemptionData = {{ redemption_data | tojson }};
            if (redemptionData.auto_populate) {
                populateForm(redemptionData);
            }
            {% endif %}
        });

        function setupEventListeners() {
            // Form submission
            document.getElementById('redemptionForm').addEventListener('submit', handleRedemptionSubmit);
            
            // Status check
            document.getElementById('checkStatusBtn').addEventListener('click', handleStatusCheck);
            
            // Verification code search
            document.getElementById('searchCodeBtn').addEventListener('click', handleVerificationSearch);
            
            // Navigation buttons
            document.getElementById('newRedemptionBtn').addEventListener('click', resetForm);
            document.getElementById('retryBtn').addEventListener('click', resetForm);
        }

        async function handleRedemptionSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            // Validate form
            if (!validateForm(data)) {
                return;
            }
            
            // Show processing step
            showStep(2);
            updateProcessingStatus('Validating order details...');
            
            try {
                // Submit redemption request
                const response = await axios.post('/openai-plus-redeem/api/redeem', data);
                
                if (response.data.status === 'success') {
                    redemptionId = response.data.redemption_id;
                    
                    if (response.data.account_assigned) {
                        // Account assigned immediately
                        accountInfo = response.data.account_info;
                        showAccountDetails(response.data);
                    } else {
                        // Waiting for account assignment
                        showPendingAssignment(response.data);
                    }
                } else {
                    showError(response.data.error, response.data.step);
                }
            } catch (error) {
                console.error('Redemption error:', error);
                showError('Failed to process redemption. Please try again.', 'network');
            }
        }

        async function handleStatusCheck() {
            const redemptionId = document.getElementById('statusRedemptionId').value.trim();
            
            if (!redemptionId) {
                showAlert('Please enter a redemption ID', 'warning');
                return;
            }
            
            try {
                const response = await axios.get(`/openai-plus-redeem/api/status/${redemptionId}`);
                
                if (response.data.status === 'success') {
                    displayStatusResult(response.data.data);
                } else {
                    showAlert('Redemption not found', 'error');
                }
            } catch (error) {
                console.error('Status check error:', error);
                showAlert('Failed to check status', 'error');
            }
        }

        async function handleVerificationSearch() {
            if (!accountInfo || !redemptionId) {
                showAlert('No account information available', 'error');
                return;
            }
            
            const searchBtn = document.getElementById('searchCodeBtn');
            const originalText = searchBtn.textContent;
            
            searchBtn.textContent = 'Searching...';
            searchBtn.disabled = true;
            
            try {
                const response = await axios.post('/openai-plus-redeem/api/verification/search', {
                    account_email: accountInfo.email,
                    redemption_id: redemptionId
                });
                
                if (response.data.status === 'success') {
                    document.getElementById('verificationCode').value = response.data.verification_code;
                    showAlert('Verification code found!', 'success');
                } else {
                    showAlert('No verification code found. Please check manually.', 'warning');
                }
            } catch (error) {
                console.error('Verification search error:', error);
                showAlert('Failed to search for verification code', 'error');
            } finally {
                searchBtn.textContent = originalText;
                searchBtn.disabled = false;
            }
        }

        function showStep(stepNumber) {
            // Hide all steps
            document.querySelectorAll('.step-content').forEach(step => {
                step.classList.add('hidden');
            });
            
            // Update step indicators
            document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
                indicator.classList.remove('active', 'completed');
                if (index + 1 < stepNumber) {
                    indicator.classList.add('completed');
                } else if (index + 1 === stepNumber) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.add('bg-gray-200');
                }
            });
            
            // Show current step
            const stepMap = {
                1: 'orderInputStep',
                2: 'processingStep',
                3: 'accountDetailsStep',
                'error': 'errorStep'
            };
            
            const stepElement = document.getElementById(stepMap[stepNumber] || stepMap['error']);
            if (stepElement) {
                stepElement.classList.remove('hidden');
                stepElement.classList.add('fade-in');
            }
            
            currentStep = stepNumber;
        }

        function showAccountDetails(data) {
            const accountInfoDiv = document.getElementById('accountInfo');
            const verificationSection = document.getElementById('verificationSection');
            
            // Display account information
            accountInfoDiv.innerHTML = `
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="flex items-center">
                            <input type="text" value="${data.account_info.email}" readonly 
                                   class="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-800">
                            <button onclick="copyToClipboard('${data.account_info.email}')" 
                                    class="ml-2 text-blue-500 hover:text-blue-700">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="flex items-center">
                            <input type="password" id="passwordField" value="${data.account_info.password}" readonly 
                                   class="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-800">
                            <button onclick="togglePasswordVisibility()" 
                                    class="ml-2 text-blue-500 hover:text-blue-700">
                                <i id="passwordToggleIcon" class="fas fa-eye"></i>
                            </button>
                            <button onclick="copyToClipboard('${data.account_info.password}')" 
                                    class="ml-2 text-blue-500 hover:text-blue-700">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="mt-4 grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Valid Until</label>
                        <input type="text" value="${new Date(data.account_info.expiration_date).toLocaleDateString()}" readonly 
                               class="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-800">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Shared Users</label>
                        <input type="text" value="${data.account_info.current_users}/${data.account_info.max_users}" readonly 
                               class="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-800">
                    </div>
                </div>
                <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-1"></i>
                        <strong>Important:</strong> This account is shared with other users. Please do not change the password or account settings.
                    </p>
                </div>
            `;
            
            // Show verification section if required
            if (data.requires_verification) {
                verificationSection.classList.remove('hidden');
            }
            
            showStep(3);
        }

        function showError(message, step) {
            document.getElementById('errorMessage').innerHTML = `
                <p class="mb-2"><strong>Error:</strong> ${message}</p>
                ${step ? `<p class="text-sm text-gray-500">Failed at step: ${step}</p>` : ''}
            `;
            showStep('error');
        }

        function validateForm(data) {
            const required = ['order_id', 'buyer_username', 'sku', 'var_sku'];
            for (const field of required) {
                if (!data[field] || !data[field].trim()) {
                    showAlert(`Please fill in the ${field.replace('_', ' ')}`, 'warning');
                    return false;
                }
            }
            return true;
        }

        function resetForm() {
            document.getElementById('redemptionForm').reset();
            redemptionId = null;
            accountInfo = null;
            showStep(1);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showAlert('Copied to clipboard!', 'success');
            });
        }

        function togglePasswordVisibility() {
            const passwordField = document.getElementById('passwordField');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        function showAlert(message, type) {
            // Simple alert implementation
            const alertClass = {
                'success': 'bg-green-100 border-green-500 text-green-700',
                'warning': 'bg-yellow-100 border-yellow-500 text-yellow-700',
                'error': 'bg-red-100 border-red-500 text-red-700'
            }[type] || 'bg-blue-100 border-blue-500 text-blue-700';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 right-4 ${alertClass} border-l-4 p-4 rounded shadow-lg z-50`;
            alertDiv.innerHTML = `
                <div class="flex items-center">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg">&times;</button>
                </div>
            `;
            
            document.body.appendChild(alertDiv);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function updateProcessingStatus(message) {
            document.getElementById('processingStatus').innerHTML = `
                <div class="flex items-center">
                    <div class="loading-spinner mr-2" style="width: 16px; height: 16px; border-width: 2px;"></div>
                    <span class="text-sm text-gray-600">${message}</span>
                </div>
            `;
        }

        function populateForm(data) {
            if (data.order_id) document.getElementById('orderId').value = data.order_id;
            if (data.buyer_username) document.getElementById('buyerUsername').value = data.buyer_username;
        }

        function displayStatusResult(status) {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.classList.remove('hidden');
            
            const statusColor = {
                'pending': 'yellow',
                'active': 'green',
                'expired': 'red',
                'error': 'red'
            }[status.status] || 'gray';
            
            resultDiv.innerHTML = `
                <div class="bg-${statusColor}-50 border border-${statusColor}-200 rounded-lg p-4">
                    <h4 class="font-medium text-${statusColor}-800 mb-2">Redemption Status</h4>
                    <div class="text-sm text-${statusColor}-700">
                        <p><strong>Status:</strong> ${status.status}</p>
                        <p><strong>Order ID:</strong> ${status.order_id}</p>
                        <p><strong>Created:</strong> ${new Date(status.created_at).toLocaleString()}</p>
                        ${status.account_info ? `
                            <div class="mt-2 pt-2 border-t border-${statusColor}-200">
                                <p><strong>Account Email:</strong> ${status.account_info.email}</p>
                                <p><strong>Valid Until:</strong> ${new Date(status.account_info.expiration_date).toLocaleDateString()}</p>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
