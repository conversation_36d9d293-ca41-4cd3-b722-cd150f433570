from typing import List, Dict, Any
import requests
from config import CURLEC_API_KEY, CURLEC_SECRET_KEY, API_KEY
import json

class CurlecService:
    def __init__(self):
        self.base_url = "https://api.razorpay.com/v1"
        self.auth = (CURLEC_API_KEY, CURLEC_SECRET_KEY)

    def get_all_customers(self, count: int = 10, skip: int = 0) -> List[Dict[str, Any]]:
        """
        Fetch all customers with pagination support
        
        Args:
            count (int): Number of customers to retrieve (default: 10)
            skip (int): Number of customers to skip (default: 0)
            
        Returns:
            List[Dict[str, Any]]: List of customer objects
            
        Raises:
            Exception: If the API request fails
        """
        endpoint = f"{self.base_url}/customers"
        params = {
            "count": count,
            "skip": skip
        }
        
        headers = {
            "X-API-Key": API_KEY
        }
        
        response = requests.get(
            endpoint,
            headers=headers,
            params=params
        )
        
        if response.status_code != 200:
            error_data = response.json().get('error', {})
            raise Exception(error_data.get('description', 'Failed to fetch customers'))
            
        return response.json().get('items', [])

    def create_customer(
        self,
        name: str,
        contact: str,
        email: str,
        fail_existing: str = "0",
        notes: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """
        Create a new customer
        
        Args:
            name (str): Customer's name
            contact (str): Customer's contact number with country code
            email (str): Customer's email address
            fail_existing (str): Whether to fail if customer exists (default: "0")
            notes (Dict[str, str]): Additional notes for the customer (optional)
            
        Returns:
            Dict[str, Any]: Created customer object
            
        Raises:
            Exception: If the API request fails
        """
        endpoint = f"{self.base_url}/customers"
        
        payload = {
            "name": name,
            "contact": contact,
            "email": email,
            "fail_existing": fail_existing
        }
        
        if notes:
            payload["notes"] = notes
            
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": API_KEY
        }
        
        response = requests.post(
            endpoint,
            headers=headers,
            auth=self.auth,
            json=payload
        )
        
        if response.status_code != 200:
            error_data = response.json().get('error', {})
            raise Exception(error_data.get('description', 'Failed to create customer'))
            
        return response.json()

    def edit_customer(
        self,
        customer_id: str,
        name: str = None,
        contact: str = None,
        email: str = None,
        notes: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """
        Update an existing customer
        
        Args:
            customer_id (str): ID of the customer to update
            name (str): Customer's new name (optional)
            contact (str): Customer's new contact number with country code (optional)
            email (str): Customer's new email address (optional)
            notes (Dict[str, str]): Additional notes for the customer (optional)
            
        Returns:
            Dict[str, Any]: Updated customer object
            
        Raises:
            Exception: If the API request fails
        """
        endpoint = f"{self.base_url}/customers/{customer_id}"
        
        payload = {}
        if name:
            payload["name"] = name
        if contact:
            payload["contact"] = contact
        if email:
            payload["email"] = email
        if notes:
            payload["notes"] = notes
            
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": API_KEY
        }
        
        response = requests.put(
            endpoint,
            headers=headers,
            auth=self.auth,
            json=payload
        )
        
        if response.status_code != 200:
            error_data = response.json().get('error', {})
            raise Exception(error_data.get('description', 'Failed to update customer'))
            
        return response.json()

    def get_customer_by_name(self, name: str) -> Dict[str, Any]:
        """
        Fetch a customer by their name
        
        Args:
            name (str): Name of the customer to search for
            
        Returns:
            Dict[str, Any]: Customer object if found
            
        Raises:
            Exception: If the API request fails or customer not found
        """
        endpoint = f"{self.base_url}/customers/search"
        params = {
            "name": name
        }
        
        headers = {
            "X-API-Key": API_KEY
        }
        
        response = requests.get(
            endpoint,
            headers=headers,
            auth=self.auth,
            params=params
        )
        
        if response.status_code != 200:
            error_data = response.json().get('error', {})
            raise Exception(error_data.get('description', 'Failed to fetch customer'))
            
        customers = response.json().get('items', [])
        if not customers:
            raise Exception('Customer not found')
            
        return customers[0]  # Return the first matching customer

    def create_payment_link(
        self,
        amount: int,
        currency: str,
        customer: Dict[str, str],
        description: str,
        reference_id: str,
        var_sku: str,
        accept_partial: bool = False,
        first_min_partial_amount: int = None,
        expire_by: int = None,
        notify: Dict[str, bool] = None,
        reminder_enable: bool = False,
        notes: Dict[str, str] = None,
        callback_url: str = None,
        callback_method: str = "get",
        checkout_name: str = None
    ) -> Dict[str, Any]:
        """
        Create a payment link
        
        Args:
            amount (int): Amount in smallest currency unit (e.g., cents)
            currency (str): Currency code (e.g., 'MYR')
            customer (Dict[str, str]): Customer details with name, contact, and email
            description (str): Description of the payment
            reference_id (str): Your unique reference ID
            var_sku (str): The VAR SKU for the payment
            accept_partial (bool): Whether to accept partial payments
            first_min_partial_amount (int): Minimum amount for first partial payment
            expire_by (int): Timestamp when link expires
            notify (Dict[str, bool]): Notification preferences
            reminder_enable (bool): Whether to enable reminders
            notes (Dict[str, str]): Additional notes
            callback_url (str): URL for payment completion callback
            callback_method (str): HTTP method for callback
            checkout_name (str): Custom name to display on the checkout page (optional)
            
        Returns:
            Dict[str, Any]: Created payment link object
        """
        endpoint = f"{self.base_url}/payment_links"
        
        payload = {
            "amount": amount,
            "currency": currency,
            "accept_partial": accept_partial,
            "description": description,
            "customer": customer,
            "reference_id": reference_id,
            "reminder_enable": reminder_enable,
            "callback_method": callback_method
        }
        
        if checkout_name:
            payload["options"] = {
                "checkout": {
                    "name": checkout_name
                }
            }
        
        if first_min_partial_amount:
            payload["first_min_partial_amount"] = first_min_partial_amount
        if expire_by:
            payload["expire_by"] = expire_by
        if notify:
            payload["notify"] = notify
        if notes:
            payload["notes"] = notes
        if callback_url:
            payload["callback_url"] = callback_url
            
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": API_KEY
        }
        
        response = requests.post(
            endpoint,
            headers=headers,
            auth=self.auth,
            json=payload
        )
        
        if response.status_code != 200:
            error_data = response.json().get('error', {})
            raise Exception(error_data.get('description', 'Failed to create payment link'))
            
        response_data = response.json()
        
        # Save to manual_invoice.json with payment status
        try:
            with open('configs/data/manual_invoice.json', 'r') as f:
                try:
                    existing_data = json.load(f)
                except json.JSONDecodeError:
                    existing_data = {}
        except FileNotFoundError:
            existing_data = {}
        
        # Add payment status and var_sku info to track for auto message
        response_data['payment_status'] = 'pending'
        response_data['var_sku'] = var_sku
        existing_data[response_data['id']] = response_data
        
        with open('configs/data/manual_invoice.json', 'w') as f:
            json.dump(existing_data, f, indent=2)
        
        # Get username from customer name
        username = customer.get('name')
        
        # Send payment request message with fixed template
        try:
            payment_message = (
                f"Hi {username},\n\n"
                f"Please complete your payment of MYR {amount/100:.2f} through this link:\n"
                f"{response_data['short_url']}\n\n"
                "The payment link will expire in 3 days. "
                "Once payment is completed, you will receive your product details automatically.\n\n"
                "Thank you for your purchase!"
            )
            
            from services.chat_service import send_chat_message
            chat_payload = {
                'username': username,
                'text': payment_message
            }
            send_chat_message(chat_payload)
                
        except Exception as e:
            print(f"Failed to send payment request message: {str(e)}")
            
        return response_data
