"""
Fake Order Security and Isolation System

This module provides comprehensive security features for fake orders including:
- Clear identification and marking of fake orders
- Visual indicators for admin interfaces
- Audit logging for fake order operations
- Access control and permission management
- Production data contamination prevention
"""

import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from functools import wraps
from flask import request, g, session, current_app
from utils.audit_logger import audit_logger
import threading

logger = logging.getLogger(__name__)

class FakeOrderSecurityManager:
    """Comprehensive security manager for fake order operations"""
    
    def __init__(self):
        self.fake_order_markers = {
            'order_sn_prefix': 'FAKE_',
            'test_prefix': 'TEST_',
            'metadata_key': 'is_fake_order',
            'visual_indicator': '🧪',  # Test tube emoji for visual identification
            'log_prefix': '[FAKE_ORDER]',
            'isolation_tag': 'FAKE_ORDER_SYSTEM'
        }
        
        self.access_permissions = {
            'create_fake_orders': ['admin', 'developer', 'tester'],
            'view_fake_orders': ['admin', 'developer', 'tester', 'viewer'],
            'delete_fake_orders': ['admin', 'developer'],
            'bulk_operations': ['admin', 'developer'],
            'production_access': ['admin']
        }
        
        self.isolation_rules = {
            'exclude_from_reports': True,
            'exclude_from_metrics': True,
            'exclude_from_analytics': True,
            'separate_storage': True,
            'clear_visual_indicators': True,
            'audit_all_operations': True
        }
        
        self._lock = threading.RLock()
    
    def is_fake_order(self, order_data: Union[Dict[str, Any], str]) -> bool:
        """
        Determine if an order is a fake order based on various indicators
        
        Args:
            order_data: Order data dict or order_sn string
            
        Returns:
            bool: True if order is identified as fake
        """
        if isinstance(order_data, str):
            # Check order_sn for fake indicators
            order_sn = order_data
            return (order_sn.startswith(self.fake_order_markers['order_sn_prefix']) or
                   order_sn.startswith(self.fake_order_markers['test_prefix']) or
                   'FAKE' in order_sn.upper() or
                   'TEST' in order_sn.upper())
        
        if isinstance(order_data, dict):
            # Check multiple indicators in order data
            # 1. Check order_sn
            order_sn = order_data.get('order_sn', '')
            if self.is_fake_order(order_sn):
                return True
            
            # 2. Check explicit fake order metadata
            if order_data.get(self.fake_order_markers['metadata_key'], False):
                return True
            
            # 3. Check nested data structures
            if 'data' in order_data:
                nested_data = order_data['data']
                if nested_data.get(self.fake_order_markers['metadata_key'], False):
                    return True
                
                # Check fake_order_metadata presence
                if 'fake_order_metadata' in nested_data:
                    return True
                
                # Check order_sn in nested data
                nested_order_sn = nested_data.get('order_sn', '')
                if self.is_fake_order(nested_order_sn):
                    return True
            
            # 4. Check for test scenario indicators
            if order_data.get('test_scenario'):
                return True
            
            # 5. Check for fake order service markers
            if order_data.get('data_source') and 'fake' in order_data.get('data_source', '').lower():
                return True
        
        return False
    
    def add_fake_order_markers(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add comprehensive fake order markers to order data
        
        Args:
            order_data: Order data to mark as fake
            
        Returns:
            Dict: Order data with fake order markers added
        """
        # Add top-level markers
        order_data[self.fake_order_markers['metadata_key']] = True
        order_data['fake_order_system_version'] = '1.0'
        order_data['fake_order_created_at'] = datetime.now().isoformat()
        order_data['fake_order_isolation_tag'] = self.fake_order_markers['isolation_tag']
        
        # Add markers to nested data if present
        if 'data' in order_data and isinstance(order_data['data'], dict):
            order_data['data'][self.fake_order_markers['metadata_key']] = True
            
            # Ensure fake_order_metadata exists
            if 'fake_order_metadata' not in order_data['data']:
                order_data['data']['fake_order_metadata'] = {}
            
            order_data['data']['fake_order_metadata'].update({
                'system_version': '1.0',
                'created_by': 'fake_order_security_system',
                'isolation_level': 'full',
                'visual_indicator': self.fake_order_markers['visual_indicator'],
                'security_markers_applied': True
            })
        
        return order_data
    
    def get_visual_indicator(self, order_data: Union[Dict[str, Any], str]) -> str:
        """
        Get visual indicator for fake orders in UI
        
        Args:
            order_data: Order data or order_sn
            
        Returns:
            str: Visual indicator string
        """
        if self.is_fake_order(order_data):
            return f"{self.fake_order_markers['visual_indicator']} FAKE"
        return ""
    
    def get_log_prefix(self, order_data: Union[Dict[str, Any], str]) -> str:
        """
        Get log prefix for fake order operations
        
        Args:
            order_data: Order data or order_sn
            
        Returns:
            str: Log prefix string
        """
        if self.is_fake_order(order_data):
            return self.fake_order_markers['log_prefix']
        return ""
    
    def should_exclude_from_reports(self, order_data: Union[Dict[str, Any], str]) -> bool:
        """
        Determine if order should be excluded from production reports
        
        Args:
            order_data: Order data or order_sn
            
        Returns:
            bool: True if should be excluded
        """
        return (self.is_fake_order(order_data) and 
                self.isolation_rules['exclude_from_reports'])
    
    def should_exclude_from_metrics(self, order_data: Union[Dict[str, Any], str]) -> bool:
        """
        Determine if order should be excluded from production metrics
        
        Args:
            order_data: Order data or order_sn
            
        Returns:
            bool: True if should be excluded
        """
        return (self.is_fake_order(order_data) and 
                self.isolation_rules['exclude_from_metrics'])
    
    def log_fake_order_operation(self, operation: str, order_data: Dict[str, Any], 
                                details: Optional[Dict[str, Any]] = None):
        """
        Log fake order operations with security context
        
        Args:
            operation: Operation being performed
            order_data: Order data involved
            details: Additional operation details
        """
        if not self.is_fake_order(order_data):
            return
        
        log_details = {
            'operation': operation,
            'order_sn': order_data.get('order_sn', 'unknown'),
            'is_fake_order': True,
            'isolation_tag': self.fake_order_markers['isolation_tag'],
            'security_context': {
                'user_id': getattr(g, 'user_id', 'anonymous'),
                'session_id': session.get('session_id', 'anonymous'),
                'operation_timestamp': datetime.now().isoformat()
            }
        }
        
        if details:
            log_details['operation_details'] = details
        
        # Log to security audit system
        audit_logger.log_security_event(
            'fake_order_operation',
            log_details,
            'INFO'
        )
        
        # Also log with fake order prefix
        log_message = f"{self.fake_order_markers['log_prefix']} {operation}: {order_data.get('order_sn', 'unknown')}"
        logger.info(log_message, extra={'fake_order': True, 'operation_details': log_details})
    
    def get_fake_order_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about fake order usage and security
        
        Returns:
            Dict: Fake order statistics
        """
        try:
            # Import here to avoid circular imports
            from services.fake_order_generator_service import fake_order_service
            
            fake_orders = fake_order_service.get_fake_orders(limit=None)
            total_fake_orders = len(fake_orders)
            
            # Calculate today's fake orders
            today = datetime.now().date()
            fake_orders_today = sum(1 for order in fake_orders 
                                   if datetime.fromisoformat(order.get('created_at', '')).date() == today)
            
            return {
                'total_fake_orders': total_fake_orders,
                'fake_orders_today': fake_orders_today,
                'security_violations': 0,  # Would be calculated from audit logs
                'access_denied_count': 0,  # Would be calculated from audit logs
                'last_cleanup': None,  # Would be stored in metadata
                'isolation_status': 'active',
                'security_level': 'high'
            }
        except Exception as e:
            logger.error(f"Error getting fake order statistics: {e}")
            return {
                'total_fake_orders': 0,
                'fake_orders_today': 0,
                'security_violations': 0,
                'access_denied_count': 0,
                'last_cleanup': None,
                'isolation_status': 'active',
                'security_level': 'high'
            }
    
    def check_access_permission(self, operation: str, user_role: Optional[str] = None) -> bool:
        """
        Check if user has permission for fake order operation
        
        Args:
            operation: Operation to check permission for
            user_role: User role (if None, tries to get from session/context)
            
        Returns:
            bool: True if user has permission
        """
        if user_role is None:
            user_role = getattr(g, 'user_role', session.get('user_role', 'anonymous'))
        
        allowed_roles = self.access_permissions.get(operation, [])
        
        # Admin always has access
        if user_role == 'admin':
            return True
        
        return user_role in allowed_roles
    
    def validate_fake_order_safety(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that fake order won't contaminate production data
        
        Args:
            order_data: Order data to validate
            
        Returns:
            Dict: Validation result with safety status
        """
        validation_result = {
            'is_safe': True,
            'warnings': [],
            'errors': [],
            'safety_checks': []
        }
        
        # Check 1: Ensure fake order is properly marked
        if not self.is_fake_order(order_data):
            validation_result['errors'].append(
                "Order is not properly marked as fake - risk of production contamination"
            )
            validation_result['is_safe'] = False
        else:
            validation_result['safety_checks'].append("Fake order properly identified")
        
        # Check 2: Validate order_sn format
        order_sn = order_data.get('order_sn', '')
        if order_sn and not (order_sn.startswith(self.fake_order_markers['order_sn_prefix']) or
                           order_sn.startswith(self.fake_order_markers['test_prefix'])):
            validation_result['warnings'].append(
                f"Order SN '{order_sn}' doesn't follow fake order naming convention"
            )
        else:
            validation_result['safety_checks'].append("Order SN follows fake order convention")
        
        # Check 3: Validate no real customer data
        if 'data' in order_data:
            data = order_data['data']
            buyer_info = data.get('buyer_user', {})
            buyer_name = buyer_info.get('user_name', '')
            
            # Check for test/fake indicators in buyer info
            if buyer_name and not any(indicator in buyer_name.lower() 
                                    for indicator in ['test', 'fake', 'demo']):
                validation_result['warnings'].append(
                    f"Buyer name '{buyer_name}' doesn't indicate test data"
                )
            else:
                validation_result['safety_checks'].append("Buyer information indicates test data")
        
        # Check 4: Validate isolation metadata
        if 'data' in order_data and 'fake_order_metadata' in order_data['data']:
            validation_result['safety_checks'].append("Isolation metadata present")
        else:
            validation_result['warnings'].append("Missing isolation metadata")
        
        return validation_result
    
    def create_fake_order_report_filter(self, orders: List[Dict[str, Any]], 
                                      include_fake: bool = False) -> List[Dict[str, Any]]:
        """
        Filter orders for reports, excluding fake orders unless explicitly requested
        
        Args:
            orders: List of orders to filter
            include_fake: Whether to include fake orders in results
            
        Returns:
            List: Filtered orders
        """
        if include_fake:
            return orders
        
        filtered_orders = []
        fake_order_count = 0
        
        for order in orders:
            if self.is_fake_order(order):
                fake_order_count += 1
                continue
            filtered_orders.append(order)
        
        # Log filtering operation
        if fake_order_count > 0:
            logger.info(f"Filtered {fake_order_count} fake orders from report")
            audit_logger.log_security_event(
                'fake_order_report_filtering',
                {
                    'total_orders': len(orders),
                    'fake_orders_filtered': fake_order_count,
                    'remaining_orders': len(filtered_orders)
                }
            )
        
        return filtered_orders
    
    def enhance_admin_interface_data(self, orders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enhance order data for admin interface with fake order indicators
        
        Args:
            orders: List of orders to enhance
            
        Returns:
            List: Enhanced orders with visual indicators
        """
        enhanced_orders = []
        
        for order in orders:
            enhanced_order = order.copy()
            
            if self.is_fake_order(order):
                # Add visual indicators
                enhanced_order['_fake_order_indicator'] = self.fake_order_markers['visual_indicator']
                enhanced_order['_is_fake_order'] = True
                enhanced_order['_fake_order_css_class'] = 'fake-order-row'
                
                # Add safety information
                safety_validation = self.validate_fake_order_safety(order)
                enhanced_order['_fake_order_safety'] = safety_validation
                
                # Enhance order_sn display
                original_order_sn = enhanced_order.get('order_sn', '')
                enhanced_order['_display_order_sn'] = f"{self.fake_order_markers['visual_indicator']} {original_order_sn}"
            else:
                enhanced_order['_is_fake_order'] = False
            
            enhanced_orders.append(enhanced_order)
        
        return enhanced_orders
    



# Global security manager instance
fake_order_security = FakeOrderSecurityManager()


def require_fake_order_permission(operation: str):
    """
    Decorator to require specific permissions for fake order operations
    
    Args:
        operation: Operation requiring permission check
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not fake_order_security.check_access_permission(operation):
                audit_logger.log_security_event(
                    'fake_order_access_denied',
                    {
                        'operation': operation,
                        'user_role': getattr(g, 'user_role', 'anonymous'),
                        'endpoint': request.endpoint
                    },
                    'WARNING'
                )
                
                from flask import jsonify
                return jsonify({
                    'success': False,
                    'error': f'Insufficient permissions for operation: {operation}',
                    'error_code': 'ACCESS_DENIED'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def log_fake_order_operation(operation: str):
    """
    Decorator to automatically log fake order operations
    
    Args:
        operation: Operation description
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Execute the function
            result = f(*args, **kwargs)
            
            # Try to extract order data from request or result
            order_data = None
            if request.json:
                order_data = request.json
            elif hasattr(result, 'json') and result.json:
                order_data = result.json.get('order_data', {})
            
            # Log if we have order data and it's a fake order
            if order_data and fake_order_security.is_fake_order(order_data):
                fake_order_security.log_fake_order_operation(
                    operation,
                    order_data,
                    {
                        'endpoint': request.endpoint,
                        'method': request.method,
                        'status_code': getattr(result, 'status_code', 200)
                    }
                )
            
            return result
        return decorated_function
    return decorator


def enhance_order_for_display(order: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhance a single order for display in admin interfaces
    
    Args:
        order: Order data to enhance
        
    Returns:
        Dict: Enhanced order data
    """
    return fake_order_security.enhance_admin_interface_data([order])[0]


def filter_fake_orders_from_production(orders: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Filter fake orders from production data
    
    Args:
        orders: List of orders to filter
        
    Returns:
        List: Orders with fake orders removed
    """
    return fake_order_security.create_fake_order_report_filter(orders, include_fake=False)