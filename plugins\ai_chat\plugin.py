"""
AI Chat Plugin Implementation
Handles AI chat configuration and settings management
"""

import logging
from typing import Dict, Any, Optional
from flask import Blueprint
from core.plugin_manager import PluginInterface
from .services.ai_chat_service import AIChatService
from .routes.ai_chat_routes import create_ai_chat_blueprint

logger = logging.getLogger(__name__)

class Plugin(PluginInterface):
    """AI Chat Plugin Main Class"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "ai_chat"
        self.version = "1.0.0"
        self.description = "AI Chat integration for automated customer responses"
        self.dependencies = []
        self.url_prefix = "/admin"  # Custom URL prefix for backward compatibility

        # Services
        self.ai_chat_service = None
        self.blueprint = None
        
    def initialize(self) -> bool:
        """Initialize the AI Chat plugin"""
        try:
            logger.info("Initializing AI Chat plugin...")
            
            # Initialize services
            self.ai_chat_service = AIChatService(self.config)
            
            # Create blueprint with services
            self.blueprint = create_ai_chat_blueprint(self.ai_chat_service)
            
            logger.info("AI Chat plugin initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize AI Chat plugin: {e}")
            return False
            
    def shutdown(self) -> bool:
        """Shutdown the AI Chat plugin"""
        try:
            logger.debug("🤖 Shutting down AI Chat plugin...")

            if self.ai_chat_service:
                # Perform any cleanup if needed
                pass

            logger.debug("🤖 AI Chat plugin shutdown successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error shutting down AI Chat plugin: {e}")
            return False
            
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return the Flask blueprint for AI Chat routes"""
        return self.blueprint
        
    def get_config_schema(self) -> Dict[str, Any]:
        """Return the configuration schema for AI Chat plugin"""
        return {
            "enabled": {
                "type": "boolean",
                "default": True,
                "description": "Enable/disable AI Chat plugin"
            },
            "ai_config": {
                "type": "object",
                "properties": {
                    "ai_reply_enabled": {
                        "type": "boolean",
                        "default": False,
                        "description": "Enable AI automatic replies"
                    },
                    "ai_reply_cooldown_minutes": {
                        "type": "integer",
                        "default": 60,
                        "description": "Cooldown period between AI replies in minutes"
                    },
                    "ai_system_prompt": {
                        "type": "string",
                        "default": "You are a helpful customer service assistant.",
                        "description": "System prompt for AI responses"
                    },
                    "ai_temperature": {
                        "type": "number",
                        "default": 1.0,
                        "description": "AI response temperature (0.0-2.0)"
                    },
                    "deepseek_api_key": {
                        "type": "string",
                        "default": "",
                        "description": "DeepSeek API key for AI service"
                    }
                }
            }
        }
        
    def get_admin_routes(self) -> Dict[str, str]:
        """Return admin interface routes for this plugin"""
        return {
            "AI Chat Settings": "/admin/ai_chat"
        }
