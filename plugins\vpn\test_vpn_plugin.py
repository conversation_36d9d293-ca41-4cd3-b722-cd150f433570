#!/usr/bin/env python3
"""
VPN Plugin Functionality Test Script

This script tests the VPN plugin implementation to ensure all features work correctly.
Run this script to verify that the VPN plugin is properly implemented and functional.
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

class VPNPluginTester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.vpn_api_base = f"{base_url}/admin/vpn/api"
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} - {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
        
    def test_api_endpoint(self, endpoint: str, method: str = "GET", data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Test an API endpoint"""
        url = f"{self.vpn_api_base}{endpoint}"
        try:
            if method == "GET":
                response = self.session.get(url, timeout=10)
            elif method == "POST":
                response = self.session.post(url, json=data, timeout=10)
            elif method == "PUT":
                response = self.session.put(url, json=data, timeout=10)
            elif method == "DELETE":
                response = self.session.delete(url, timeout=10)
            else:
                return None

            if response.status_code in [200, 201]:
                return response.json() if response.content else {}
            else:
                return {"error": f"HTTP {response.status_code}", "response": response.text}

        except requests.exceptions.RequestException as e:
            return {"error": str(e)}

    def test_server_client_management(self):
        """Test server-specific client management functionality"""
        print("\n🧪 Testing Server-Client Management Features...")

        # Test 1: Get servers list to find a test server
        servers_response = self.test_api_endpoint("/servers")
        if servers_response and isinstance(servers_response, list) and len(servers_response) > 0:
            test_server = servers_response[0]
            server_id = test_server.get('id')
            self.log_test("Get servers for testing", True, f"Found server ID: {server_id}")

            # Test 2: Test detailed server clients endpoint
            detailed_response = self.test_api_endpoint(f"/servers/{server_id}/clients/detailed")
            if detailed_response:
                has_server = 'server' in detailed_response
                has_clients = 'clients' in detailed_response
                has_stats = 'total_clients' in detailed_response

                self.log_test("Server clients detailed API",
                            has_server and has_clients,
                            f"Server: {has_server}, Clients: {has_clients}, Stats: {has_stats}")
            else:
                self.log_test("Server clients detailed API", False, "No response received")

            # Test 3: Test server client stats endpoint
            stats_response = self.test_api_endpoint(f"/servers/{server_id}/clients/stats")
            if stats_response:
                required_stats = ['total_clients', 'active_clients', 'expired_clients', 'expiring_soon', 'lifetime_clients']
                has_all_stats = all(stat in stats_response for stat in required_stats)

                self.log_test("Server client stats API",
                            has_all_stats,
                            f"Stats fields: {list(stats_response.keys())}")
            else:
                self.log_test("Server client stats API", False, "No response received")

            # Test 4: Test UI route accessibility (basic check)
            ui_url = f"{self.base_url}/admin/vpn/servers/{server_id}/clients"
            try:
                ui_response = self.session.get(ui_url, timeout=10)
                ui_accessible = ui_response.status_code == 200
                self.log_test("Server clients UI route",
                            ui_accessible,
                            f"Status code: {ui_response.status_code}")
            except Exception as e:
                self.log_test("Server clients UI route", False, f"Error: {str(e)}")

        else:
            self.log_test("Get servers for testing", False, "No servers found or API error")

    def test_api_service_methods(self):
        """Test VPN API Service new methods"""
        print("\n🔧 Testing VPN API Service Methods...")

        try:
            # Import and test the VPN API service
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

            from plugins.vpn.services.vpn_api_service import VPNAPIService

            # Test service initialization
            config = {
                'vpn_api': {
                    'base_url': 'https://blueblue.api.limjianhui.com',
                    'username': 'admin',
                    'password': 'admin123',
                    'timeout': 30
                }
            }

            service = VPNAPIService(config)
            self.log_test("VPN API Service initialization", True, "Service created successfully")

            # Test method existence
            methods_to_test = [
                'get_server_clients_detailed',
                'reset_client_traffic',
                'get_server_client_stats'
            ]

            for method_name in methods_to_test:
                has_method = hasattr(service, method_name) and callable(getattr(service, method_name))
                self.log_test(f"Method {method_name} exists",
                            has_method,
                            "Method is callable" if has_method else "Method not found")

        except ImportError as e:
            self.log_test("VPN API Service import", False, f"Import error: {str(e)}")
        except Exception as e:
            self.log_test("VPN API Service testing", False, f"Error: {str(e)}")

    def test_template_accessibility(self):
        """Test template file accessibility"""
        print("\n📄 Testing Template Files...")

        import os

        # Check if new template exists
        template_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            'templates',
            'vpn_server_clients.html'
        )

        template_exists = os.path.exists(template_path)
        self.log_test("Server clients template exists",
                    template_exists,
                    f"Path: {template_path}")

        if template_exists:
            # Check template content
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check for key template elements
                has_server_header = 'server-header' in content
                has_stats_grid = 'stats-grid' in content
                has_client_table = 'client-table' in content
                has_filters = 'client-filters' in content

                self.log_test("Template content validation",
                            has_server_header and has_stats_grid and has_client_table and has_filters,
                            f"Header: {has_server_header}, Stats: {has_stats_grid}, Table: {has_client_table}, Filters: {has_filters}")

            except Exception as e:
                self.log_test("Template content reading", False, f"Error: {str(e)}")

    def test_route_integration(self):
        """Test route integration in vpn_routes.py"""
        print("\n🛣️ Testing Route Integration...")

        try:
            import os
            import ast

            routes_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                'routes',
                'vpn_routes.py'
            )

            if os.path.exists(routes_path):
                with open(routes_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check for new route function
                has_manage_server_clients = 'def manage_server_clients' in content
                has_route_decorator = '/servers/<int:server_id>/clients' in content
                has_api_detailed = 'api_get_server_clients_detailed' in content
                has_api_stats = 'api_get_server_client_stats' in content

                self.log_test("Route function exists",
                            has_manage_server_clients,
                            "manage_server_clients function found")

                self.log_test("Route decorator exists",
                            has_route_decorator,
                            "Route path decorator found")

                self.log_test("API detailed endpoint exists",
                            has_api_detailed,
                            "Detailed API endpoint found")

                self.log_test("API stats endpoint exists",
                            has_api_stats,
                            "Stats API endpoint found")

            else:
                self.log_test("Routes file exists", False, f"File not found: {routes_path}")

        except Exception as e:
            self.log_test("Route integration testing", False, f"Error: {str(e)}")
            
    def test_server_endpoints(self):
        """Test server management endpoints"""
        print("\n🔧 Testing Server Management Endpoints...")
        
        # Test get servers list
        result = self.test_api_endpoint("/servers")
        self.log_test("GET /servers", result is not None and "error" not in result)
        
        # Test server creation endpoint (without actually creating)
        test_server_data = {
            "name": "test-server",
            "host": "127.0.0.1",
            "port": 22,
            "username": "test"
        }
        result = self.test_api_endpoint("/servers/create", "POST", test_server_data)
        # This might fail due to invalid credentials, but endpoint should exist
        endpoint_exists = result is not None
        self.log_test("POST /servers/create", endpoint_exists, "Endpoint accessible")
        
        # Test other server endpoints (these will likely return 404 for non-existent server)
        endpoints_to_test = [
            ("/servers/1", "GET"),
            ("/servers/1/test-connection", "POST"),
            ("/servers/1/service-status", "GET"),
            ("/servers/1/config", "GET"),
            ("/servers/1/clients", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            result = self.test_api_endpoint(endpoint, method)
            # 404 is expected for non-existent servers, so we check if endpoint exists
            endpoint_exists = result is not None and not ("Connection" in str(result.get("error", "")))
            self.log_test(f"{method} {endpoint}", endpoint_exists, "Endpoint accessible")
            
    def test_client_endpoints(self):
        """Test client management endpoints"""
        print("\n👥 Testing Client Management Endpoints...")
        
        # Test get clients list
        result = self.test_api_endpoint("/clients")
        self.log_test("GET /clients", result is not None and "error" not in result)
        
        # Test client creation endpoint
        test_client_data = {
            "email": "<EMAIL>",
            "server_id": 1,
            "expired_date": "2024-12-31"
        }
        result = self.test_api_endpoint("/clients/create", "POST", test_client_data)
        endpoint_exists = result is not None
        self.log_test("POST /clients/create", endpoint_exists, "Endpoint accessible")
        
        # Test other client endpoints
        endpoints_to_test = [
            ("/clients/1", "GET"),
            ("/clients/by-email/<EMAIL>", "GET"),
            ("/clients/server/1/expiry", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            result = self.test_api_endpoint(endpoint, method)
            endpoint_exists = result is not None and not ("Connection" in str(result.get("error", "")))
            self.log_test(f"{method} {endpoint}", endpoint_exists, "Endpoint accessible")
            
    def test_config_endpoints(self):
        """Test configuration management endpoints"""
        print("\n⚙️ Testing Configuration Management Endpoints...")
        
        endpoints_to_test = [
            ("/config", "GET"),
            ("/config/server/1", "GET"),
            ("/config/validate/server/1", "GET"),
            ("/config/backups", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            result = self.test_api_endpoint(endpoint, method)
            endpoint_exists = result is not None and not ("Connection" in str(result.get("error", "")))
            self.log_test(f"{method} {endpoint}", endpoint_exists, "Endpoint accessible")
            
    def test_health_endpoints(self):
        """Test health monitoring endpoints"""
        print("\n🏥 Testing Health Monitoring Endpoints...")
        
        endpoints_to_test = [
            ("/health", "GET"),
            ("/health/detailed", "GET"),
            ("/health/expiry-summary", "GET"),
            ("/health/ssh-pool", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            result = self.test_api_endpoint(endpoint, method)
            endpoint_exists = result is not None and not ("Connection" in str(result.get("error", "")))
            self.log_test(f"{method} {endpoint}", endpoint_exists, "Endpoint accessible")
            
    def test_task_endpoints(self):
        """Test task management endpoints"""
        print("\n📋 Testing Task Management Endpoints...")
        
        endpoints_to_test = [
            ("/tasks", "GET"),
            ("/tasks/1", "GET"),
            ("/tasks/expiry/summary", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            result = self.test_api_endpoint(endpoint, method)
            endpoint_exists = result is not None and not ("Connection" in str(result.get("error", "")))
            self.log_test(f"{method} {endpoint}", endpoint_exists, "Endpoint accessible")
            
    def test_sync_endpoints(self):
        """Test sync functionality endpoints"""
        print("\n🔄 Testing Sync Endpoints...")
        
        # Test the previously broken sync endpoints
        endpoints_to_test = [
            ("/sync/server/1", "POST"),
            ("/sync/all", "POST"),
        ]
        
        for endpoint, method in endpoints_to_test:
            result = self.test_api_endpoint(endpoint, method)
            endpoint_exists = result is not None and not ("Connection" in str(result.get("error", "")))
            self.log_test(f"{method} {endpoint}", endpoint_exists, "Endpoint accessible (was broken before)")
            
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting VPN Plugin Functionality Tests...")
        print(f"Testing against: {self.base_url}")

        # Test new server-client management features first
        self.test_server_client_management()
        self.test_api_service_methods()
        self.test_template_accessibility()
        self.test_route_integration()

        # Test existing functionality
        self.test_server_endpoints()
        self.test_client_endpoints()
        self.test_config_endpoints()
        self.test_health_endpoints()
        self.test_task_endpoints()
        self.test_sync_endpoints()
        
        # Summary
        print("\n📊 Test Summary:")
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        print(f"Passed: {passed}/{total} tests")
        
        if passed == total:
            print("🎉 All tests passed! VPN plugin is fully functional.")
            return True
        else:
            print("⚠️ Some tests failed. Check the implementation.")
            return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test VPN Plugin Functionality")
    parser.add_argument("--url", default="http://localhost:5000", 
                       help="Base URL of the application (default: http://localhost:5000)")
    
    args = parser.parse_args()
    
    tester = VPNPluginTester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
