"""
Base Redemption Strategy
Abstract base class for all redemption strategies
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

from abc import ABC, abstractmethod
from typing import Dict, Any

class BaseRedemptionStrategy(ABC):
    """
    Abstract base class for all redemption strategies.
    Defines the interface that all redemption strategies must implement.
    """
    
    @abstractmethod
    def redeem(self, order_details: Dict[str, Any], product_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main redemption method that processes an order.
        
        Args:
            order_details (Dict[str, Any]): Order information including customer details
            product_details (Dict[str, Any]): Product information including SKU and configuration
            
        Returns:
            Dict[str, Any]: Redemption result with success/failure status and details
        """
        pass
    
    @abstractmethod
    def validate_user_data(self, order_details: Dict[str, Any], product_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and extract user data from order and product details.
        
        Args:
            order_details (Dict[str, Any]): Order information
            product_details (Dict[str, Any]): Product information
            
        Returns:
            Dict[str, Any]: Validation result with extracted user data or error information
        """
        pass
    
    def format_success_response(self, user_data: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format a success response.
        Can be overridden by specific strategies for custom formatting.
        
        Args:
            user_data (Dict[str, Any]): Validated user data
            result (Dict[str, Any]): Processing result
            
        Returns:
            Dict[str, Any]: Formatted success response
        """
        return {
            "success": True,
            "message": "Redemption successful",
            "data": {
                "order_id": user_data.get('order_id'),
                "customer_email": user_data.get('customer_email'),
                "result": result
            }
        }
    
    def format_error_response(self, error_message: str, user_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Format an error response.
        
        Args:
            error_message (str): Error message
            user_data (Dict[str, Any], optional): User data if available
            
        Returns:
            Dict[str, Any]: Formatted error response
        """
        response = {
            "success": False,
            "message": error_message
        }
        
        if user_data:
            response["data"] = {
                "order_id": user_data.get('order_id'),
                "customer_email": user_data.get('customer_email')
            }
        
        return response
