#!/usr/bin/env python3
"""
Test script to simulate the registration API endpoints
"""

import sys
import os
import json
import logging

# Add the main application directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def test_registration_endpoint():
    """Test the registration endpoint logic"""
    print("🧪 Testing Registration Endpoint Logic")
    print("=" * 50)
    
    try:
        # Import required components
        from plugins.chat_commands.models import ChatCommand
        from plugins.chat_commands.services import ChatCommandService
        from plugins.vpn_config_generator.services import VPNChatCommandService, VPNConfigGeneratorService
        from plugins.vpn_config_generator.models import VPNCommandConfig
        
        # Create mock app
        class MockApp:
            def __init__(self):
                self.config = {}
        
        # Create mock plugin manager
        class MockPluginManager:
            def __init__(self):
                self.chat_commands_plugin = None
                self.app = MockApp()
                
            def get_plugin(self, name):
                if name == 'chat_commands':
                    if not self.chat_commands_plugin:
                        # Create chat commands plugin
                        class MockChatCommandsPlugin:
                            def __init__(self):
                                chat_dir = os.path.join(os.path.dirname(__file__), '..', 'chat_commands')
                                self.command_service = ChatCommandService(chat_dir, MockApp())
                                self.name = 'chat_commands'
                                
                            def register_external_command(self, command, plugin_name, handler):
                                return self.command_service.register_external_command(command, plugin_name, handler)
                                
                            def unregister_external_command(self, command_name, plugin_name):
                                return self.command_service.unregister_external_command(command_name, plugin_name)
                                
                        self.chat_commands_plugin = MockChatCommandsPlugin()
                    return self.chat_commands_plugin
                return None
        
        # Create services
        plugin_dir = os.path.dirname(os.path.abspath(__file__))
        plugin_manager = MockPluginManager()
        
        config_service = VPNConfigGeneratorService(plugin_dir, {}, plugin_manager)
        chat_command_service = VPNChatCommandService(plugin_dir, config_service, plugin_manager)
        
        print("✅ Services initialized successfully")
        
        # Test current configuration
        command_config = chat_command_service.get_command_config()
        print(f"📋 Current command name: {command_config.command_name}")
        
        # Get all commands
        commands = chat_command_service.get_all_commands()
        print(f"📋 Loaded commands: {len(commands)}")
        
        for cmd_key, cmd_obj in commands.items():
            print(f"   {cmd_key}: {cmd_obj.command}")
        
        # Test registration
        print(f"\n🔧 Testing Registration...")
        
        # Get chat commands plugin
        chat_plugin = plugin_manager.get_plugin('chat_commands')
        if not chat_plugin:
            print("❌ Chat commands plugin not found")
            return False
            
        print("✅ Chat commands plugin found")
        
        # Try to register each command
        success_count = 0
        for cmd_key, cmd_obj in commands.items():
            # Create handler function (mock)
            def dummy_handler(message, params):
                return f"Handler for {cmd_obj.command}"
            
            # Register command
            success = chat_plugin.register_external_command(
                cmd_obj, 
                "vpn_config_generator", 
                dummy_handler
            )
            
            if success:
                success_count += 1
                print(f"✅ Registered: {cmd_obj.command}")
            else:
                print(f"❌ Failed to register: {cmd_obj.command}")
        
        print(f"\n📊 Registration Results: {success_count}/{len(commands)} successful")
        
        # Test status check
        print(f"\n🔍 Testing Status Check...")
        main_command = command_config.command_name
        
        # Check if main command is registered
        registered_command = chat_plugin.command_service.get_command(main_command)
        if registered_command:
            has_plugin_source = hasattr(registered_command, 'plugin_source')
            correct_source = has_plugin_source and registered_command.plugin_source == "vpn_config_generator"
            
            print(f"✅ Command '{main_command}' found in registry")
            print(f"   Has plugin_source: {has_plugin_source}")
            if has_plugin_source:
                print(f"   Plugin source: {registered_command.plugin_source}")
            print(f"   Correct source: {correct_source}")
            
            if correct_source:
                print(f"🎉 Registration status should show: REGISTERED")
                return True
            else:
                print(f"⚠️  Registration status will show: NOT REGISTERED (wrong plugin source)")
                return False
        else:
            print(f"❌ Command '{main_command}' not found in registry")
            print(f"⚠️  Registration status will show: NOT REGISTERED (command not found)")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing VPN Command Registration API Logic...")
    success = test_registration_endpoint()
    
    if success:
        print(f"\n🎉 SUCCESS: Registration system should work correctly!")
        print(f"💡 The buttons should now work properly.")
    else:
        print(f"\n⚠️  ISSUES FOUND: Registration may not work as expected.")
        print(f"🔧 Check the errors above for troubleshooting.")