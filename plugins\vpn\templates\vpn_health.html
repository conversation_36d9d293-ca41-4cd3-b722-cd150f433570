{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>VPN Health Dashboard</h2>
                <div>
                    <button class="btn btn-primary" onclick="refreshAllHealth()">
                        <i class="fas fa-sync"></i> Refresh All
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    {% if health %}
    <!-- Overall Health Status -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card {{ 'border-success' if health.overall_health == 'healthy' else 'border-danger' }}">
                <div class="card-header {{ 'bg-success text-white' if health.overall_health == 'healthy' else 'bg-danger text-white' }}">
                    <h4 class="mb-0">
                        <i class="fas {{ 'fa-check-circle' if health.overall_health == 'healthy' else 'fa-exclamation-circle' }}"></i>
                        Overall System Health: {{ health.overall_health|upper }}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5>Total Servers</h5>
                                <h3>{{ health.total_servers }}</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5>Healthy Servers</h5>
                                <h3 class="text-success">{{ health.healthy_servers }}</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5>Unhealthy Servers</h5>
                                <h3 class="text-danger">{{ health.unhealthy_servers }}</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5>Last Check</h5>
                                <h3>{{ health.last_check }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Server Health Details -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Server Health Details</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Server</th>
                                    <th>Status</th>
                                    <th>SSH Connection</th>
                                    <th>Xray Service</th>
                                    <th>Config Valid</th>
                                    <th>Total Clients</th>
                                    <th>Active Clients</th>
                                    <th>Expired Clients</th>
                                    <th>Response Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if health.servers %}
                                {% for server in health.servers %}
                                <tr class="{{ 'table-success' if server.is_healthy else 'table-danger' }}">
                                    <td>
                                        <strong>{{ server.name }}</strong>
                                        <br><small class="text-muted">{{ server.host }}</small>
                                    </td>
                                    <td>
                                        {% if server.is_healthy %}
                                        <span class="badge badge-success">Healthy</span>
                                        {% else %}
                                        <span class="badge badge-danger">Unhealthy</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if server.ssh_connected %}
                                        <i class="fas fa-check text-success"></i> Connected
                                        {% else %}
                                        <i class="fas fa-times text-danger"></i> Failed
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if server.xray_running %}
                                        <i class="fas fa-check text-success"></i> Running
                                        {% else %}
                                        <i class="fas fa-times text-danger"></i> Stopped
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if server.config_valid %}
                                        <i class="fas fa-check text-success"></i> Valid
                                        {% else %}
                                        <i class="fas fa-times text-danger"></i> Invalid
                                        {% endif %}
                                    </td>
                                    <td>{{ server.client_stats.total if server.client_stats else 0 }}</td>
                                    <td>{{ server.client_stats.active if server.client_stats else 0 }}</td>
                                    <td>{{ server.client_stats.expired if server.client_stats else 0 }}</td>
                                    <td>{{ server.response_time_ms }}ms</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="refreshServerHealth({{ server.id }})">
                                            <i class="fas fa-sync"></i> Refresh
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Expiry Summary -->
    {% if health.expiry_summary %}
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Client Expiry Summary</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-user-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Already Expired</span>
                                    <span class="info-box-number">{{ health.expiry_summary.expired }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Expiring in 7 days</span>
                                    <span class="info-box-number">{{ health.expiry_summary.expiring_7_days }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-calendar-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Expiring in 30 days</span>
                                    <span class="info-box-number">{{ health.expiry_summary.expiring_30_days }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-infinity"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Lifetime</span>
                                    <span class="info-box-number">{{ health.expiry_summary.lifetime }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- SSH Pool Status -->
    {% if health.ssh_pool_status %}
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">SSH Connection Pool</h3>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">Active Connections:</dt>
                        <dd class="col-sm-6">{{ health.ssh_pool_status.active_connections }}</dd>
                        
                        <dt class="col-sm-6">Idle Connections:</dt>
                        <dd class="col-sm-6">{{ health.ssh_pool_status.idle_connections }}</dd>
                        
                        <dt class="col-sm-6">Total Connections:</dt>
                        <dd class="col-sm-6">{{ health.ssh_pool_status.total_connections }}</dd>
                        
                        <dt class="col-sm-6">Pool Size:</dt>
                        <dd class="col-sm-6">{{ health.ssh_pool_status.pool_size }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">System Resources</h3>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">CPU Usage:</dt>
                        <dd class="col-sm-6">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ health.system_resources.cpu_percent }}%">
                                    {{ health.system_resources.cpu_percent }}%
                                </div>
                            </div>
                        </dd>
                        
                        <dt class="col-sm-6">Memory Usage:</dt>
                        <dd class="col-sm-6">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ health.system_resources.memory_percent }}%">
                                    {{ health.system_resources.memory_percent }}%
                                </div>
                            </div>
                        </dd>
                        
                        <dt class="col-sm-6">Disk Usage:</dt>
                        <dd class="col-sm-6">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ health.system_resources.disk_percent }}%">
                                    {{ health.system_resources.disk_percent }}%
                                </div>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> No health data available. Click "Refresh All" to fetch latest health status.
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshAllHealth() {
    toastr.info('Refreshing health status for all servers...');
    $.post('/admin/vpn/health/refresh', function(data) {
        toastr.success('Health status refreshed!');
        location.reload();
    }).fail(function() {
        toastr.error('Failed to refresh health status');
    });
}

function refreshServerHealth(serverId) {
    toastr.info('Refreshing server health...');
    $.post('/admin/vpn/health/refresh', { server_id: serverId }, function(data) {
        toastr.success('Server health refreshed!');
        location.reload();
    }).fail(function() {
        toastr.error('Failed to refresh server health');
    });
}

// Auto-refresh every 60 seconds
setInterval(function() {
    if (document.visibilityState === 'visible') {
        refreshAllHealth();
    }
}, 60000);
</script>
{% endblock %}