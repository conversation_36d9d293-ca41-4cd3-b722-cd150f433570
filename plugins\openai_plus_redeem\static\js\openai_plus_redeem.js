/**
 * OpenAI Plus Redeem Plugin JavaScript
 * 
 * Provides AJAX calls, real-time status updates, form validation,
 * and interactive features for the plugin interface.
 */

// ========== Global Configuration ==========
const OPR = {
    // API endpoints
    endpoints: {
        redeem: '/openai-plus-redeem/api/redeem',
        status: '/openai-plus-redeem/api/status',
        verification: '/openai-plus-redeem/api/verification',
        cooldown: '/openai-plus-redeem/api/cooldown',
        shopeeRedeem: '/openai-plus-redeem/api/shopee/redeem',
        admin: {
            accounts: '/admin/openai-plus-redeem/api/accounts',
            cooldowns: '/admin/openai-plus-redeem/api/cooldowns',
            dashboard: '/admin/openai-plus-redeem/api/dashboard/stats',
            services: '/admin/openai-plus-redeem/api/services',
            cleanup: '/admin/openai-plus-redeem/api/cleanup/expired'
        }
    },
    
    // Configuration
    config: {
        statusUpdateInterval: 30000, // 30 seconds
        verificationTimeout: 600000, // 10 minutes
        maxRetryAttempts: 3,
        requestTimeout: 30000 // 30 seconds
    },
    
    // State management
    state: {
        currentRedemption: null,
        statusUpdateTimer: null,
        verificationTimer: null,
        isProcessing: false
    }
};

// ========== Utility Functions ==========

/**
 * Make authenticated API request
 */
async function makeAPIRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        },
        timeout: OPR.config.requestTimeout
    };
    
    // Add admin authentication if needed
    if (url.includes('/admin/')) {
        defaultOptions.headers['X-Admin-Key'] = getAdminKey();
    }
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);
        
        const response = await fetch(url, {
            ...finalOptions,
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        if (error.name === 'AbortError') {
            throw new Error('Request timeout');
        }
        throw error;
    }
}

/**
 * Get admin API key from storage or prompt
 */
function getAdminKey() {
    // Try to get from localStorage first
    let adminKey = localStorage.getItem('opr_admin_key');
    
    if (!adminKey) {
        // Prompt for admin key
        adminKey = prompt('Enter admin API key:');
        if (adminKey) {
            localStorage.setItem('opr_admin_key', adminKey);
        }
    }
    
    return adminKey || 'admin_key_placeholder';
}

/**
 * Show notification alert
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertClass = {
        'success': 'opr-alert-success',
        'warning': 'opr-alert-warning',
        'error': 'opr-alert-error',
        'info': 'opr-alert-info'
    }[type] || 'opr-alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `opr-alert ${alertClass} fixed top-4 right-4 z-50 max-w-sm shadow-lg`;
    alertDiv.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" 
                    class="ml-4 text-lg font-bold opacity-70 hover:opacity-100">
                &times;
            </button>
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (alertDiv.parentElement) {
                alertDiv.remove();
            }
        }, duration);
    }
    
    return alertDiv;
}

/**
 * Validate form data
 */
function validateForm(formData, requiredFields) {
    const errors = [];
    
    for (const field of requiredFields) {
        const value = formData[field];
        if (!value || (typeof value === 'string' && !value.trim())) {
            errors.push(`${field.replace('_', ' ')} is required`);
        }
    }
    
    // Additional validation rules
    if (formData.email && !isValidEmail(formData.email)) {
        errors.push('Invalid email format');
    }
    
    if (formData.hours && (isNaN(formData.hours) || formData.hours < 1 || formData.hours > 168)) {
        errors.push('Hours must be between 1 and 168');
    }
    
    if (formData.verification_code && !/^\d{6}$/.test(formData.verification_code)) {
        errors.push('Verification code must be 6 digits');
    }
    
    return errors;
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Format duration from seconds
 */
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}

/**
 * Copy text to clipboard
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showAlert('Copied to clipboard!', 'success', 2000);
        return true;
    } catch (error) {
        console.error('Copy failed:', error);
        showAlert('Failed to copy to clipboard', 'error');
        return false;
    }
}

/**
 * Debounce function calls
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ========== Redemption Functions ==========

/**
 * Process order redemption
 */
async function processRedemption(orderData) {
    try {
        OPR.state.isProcessing = true;
        
        const response = await makeAPIRequest(OPR.endpoints.redeem, {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
        
        if (response.status === 'success') {
            OPR.state.currentRedemption = response.redemption_id;
            
            // Start status monitoring if account not immediately assigned
            if (!response.account_assigned) {
                startStatusMonitoring(response.redemption_id);
            }
            
            return response;
        } else {
            throw new Error(response.error || 'Redemption failed');
        }
    } catch (error) {
        console.error('Redemption error:', error);
        throw error;
    } finally {
        OPR.state.isProcessing = false;
    }
}

/**
 * Get redemption status
 */
async function getRedemptionStatus(redemptionId) {
    try {
        const response = await makeAPIRequest(`${OPR.endpoints.status}/${redemptionId}`);
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Status check failed');
        }
    } catch (error) {
        console.error('Status check error:', error);
        throw error;
    }
}

/**
 * Start monitoring redemption status
 */
function startStatusMonitoring(redemptionId) {
    if (OPR.state.statusUpdateTimer) {
        clearInterval(OPR.state.statusUpdateTimer);
    }
    
    OPR.state.statusUpdateTimer = setInterval(async () => {
        try {
            const status = await getRedemptionStatus(redemptionId);
            
            // Update UI with status
            updateRedemptionStatusDisplay(status);
            
            // Stop monitoring if completed or failed
            if (status.status === 'active' || status.status === 'error') {
                stopStatusMonitoring();
            }
        } catch (error) {
            console.error('Status monitoring error:', error);
        }
    }, OPR.config.statusUpdateInterval);
}

/**
 * Stop status monitoring
 */
function stopStatusMonitoring() {
    if (OPR.state.statusUpdateTimer) {
        clearInterval(OPR.state.statusUpdateTimer);
        OPR.state.statusUpdateTimer = null;
    }
}

/**
 * Update redemption status display
 */
function updateRedemptionStatusDisplay(status) {
    // This function would update the UI with current status
    // Implementation depends on the specific UI elements
    console.log('Status update:', status);
}

// ========== Email Verification Functions ==========

/**
 * Search for verification code
 */
async function searchVerificationCode(accountEmail, redemptionId, timeoutMinutes = 10) {
    try {
        const response = await makeAPIRequest(`${OPR.endpoints.verification}/search`, {
            method: 'POST',
            body: JSON.stringify({
                account_email: accountEmail,
                redemption_id: redemptionId,
                timeout_minutes: timeoutMinutes
            })
        });
        
        if (response.status === 'success') {
            return response;
        } else {
            throw new Error(response.error || 'Verification search failed');
        }
    } catch (error) {
        console.error('Verification search error:', error);
        throw error;
    }
}

/**
 * Get verification status
 */
async function getVerificationStatus(verificationId) {
    try {
        const response = await makeAPIRequest(`${OPR.endpoints.verification}/${verificationId}/status`);
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Verification status check failed');
        }
    } catch (error) {
        console.error('Verification status error:', error);
        throw error;
    }
}

// ========== Admin Functions ==========

/**
 * Load dashboard statistics
 */
async function loadDashboardStats() {
    try {
        const response = await makeAPIRequest(OPR.endpoints.admin.dashboard);
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to load dashboard stats');
        }
    } catch (error) {
        console.error('Dashboard stats error:', error);
        throw error;
    }
}

/**
 * Load accounts
 */
async function loadAccounts(filters = {}) {
    try {
        const queryParams = new URLSearchParams(filters).toString();
        const url = `${OPR.endpoints.admin.accounts}${queryParams ? '?' + queryParams : ''}`;
        
        const response = await makeAPIRequest(url);
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to load accounts');
        }
    } catch (error) {
        console.error('Load accounts error:', error);
        throw error;
    }
}

/**
 * Create account
 */
async function createAccount(accountData) {
    try {
        const response = await makeAPIRequest(OPR.endpoints.admin.accounts, {
            method: 'POST',
            body: JSON.stringify(accountData)
        });
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to create account');
        }
    } catch (error) {
        console.error('Create account error:', error);
        throw error;
    }
}

/**
 * Update account
 */
async function updateAccount(accountId, accountData) {
    try {
        const response = await makeAPIRequest(`${OPR.endpoints.admin.accounts}/${accountId}`, {
            method: 'PUT',
            body: JSON.stringify(accountData)
        });
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to update account');
        }
    } catch (error) {
        console.error('Update account error:', error);
        throw error;
    }
}

/**
 * Delete account
 */
async function deleteAccount(accountId) {
    try {
        const response = await makeAPIRequest(`${OPR.endpoints.admin.accounts}/${accountId}`, {
            method: 'DELETE'
        });
        
        if (response.status === 'success') {
            return true;
        } else {
            throw new Error(response.error || 'Failed to delete account');
        }
    } catch (error) {
        console.error('Delete account error:', error);
        throw error;
    }
}

/**
 * Load cooldowns
 */
async function loadCooldowns() {
    try {
        const response = await makeAPIRequest(OPR.endpoints.admin.cooldowns);
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to load cooldowns');
        }
    } catch (error) {
        console.error('Load cooldowns error:', error);
        throw error;
    }
}

/**
 * Set user cooldown
 */
async function setCooldown(cooldownData) {
    try {
        const response = await makeAPIRequest(OPR.endpoints.admin.cooldowns, {
            method: 'POST',
            body: JSON.stringify(cooldownData)
        });
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to set cooldown');
        }
    } catch (error) {
        console.error('Set cooldown error:', error);
        throw error;
    }
}

/**
 * Remove user cooldown
 */
async function removeCooldown(username, adminOverride = false) {
    try {
        const url = `${OPR.endpoints.admin.cooldowns}/${username}${adminOverride ? '?admin_override=true' : ''}`;
        
        const response = await makeAPIRequest(url, {
            method: 'DELETE'
        });
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to remove cooldown');
        }
    } catch (error) {
        console.error('Remove cooldown error:', error);
        throw error;
    }
}

/**
 * Cleanup expired data
 */
async function cleanupExpiredData() {
    try {
        const response = await makeAPIRequest(OPR.endpoints.admin.cleanup, {
            method: 'POST'
        });
        
        if (response.status === 'success') {
            return response.data;
        } else {
            throw new Error(response.error || 'Cleanup failed');
        }
    } catch (error) {
        console.error('Cleanup error:', error);
        throw error;
    }
}

// ========== Form Validation ==========

/**
 * Real-time form validation
 */
function setupFormValidation(formElement) {
    const inputs = formElement.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        // Add validation on blur
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        // Add validation on input for specific fields
        if (input.type === 'email' || input.name === 'verification_code') {
            input.addEventListener('input', debounce(function() {
                validateField(this);
            }, 500));
        }
    });
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';
    
    // Remove existing error styling
    field.classList.remove('error');
    removeFieldError(field);
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = `${fieldName.replace('_', ' ')} is required`;
    }
    
    // Specific field validations
    if (value) {
        switch (fieldName) {
            case 'email':
                if (!isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Invalid email format';
                }
                break;
                
            case 'verification_code':
                if (!/^\d{6}$/.test(value)) {
                    isValid = false;
                    errorMessage = 'Verification code must be 6 digits';
                }
                break;
                
            case 'hours':
                const hours = parseInt(value);
                if (isNaN(hours) || hours < 1 || hours > 168) {
                    isValid = false;
                    errorMessage = 'Hours must be between 1 and 168';
                }
                break;
                
            case 'order_id':
                if (value.length < 5) {
                    isValid = false;
                    errorMessage = 'Order ID seems too short';
                }
                break;
        }
    }
    
    // Apply error styling if invalid
    if (!isValid) {
        field.classList.add('error');
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

/**
 * Show field error message
 */
function showFieldError(field, message) {
    removeFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error text-sm text-red-600 mt-1';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * Remove field error message
 */
function removeFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

// ========== UI Helper Functions ==========

/**
 * Show loading state on button
 */
function setButtonLoading(button, loading = true) {
    if (loading) {
        button.disabled = true;
        button.dataset.originalText = button.textContent;
        button.innerHTML = '<div class="opr-spinner opr-spinner-sm mr-2"></div>Loading...';
    } else {
        button.disabled = false;
        button.textContent = button.dataset.originalText || 'Submit';
    }
}

/**
 * Update progress bar
 */
function updateProgressBar(progressElement, percentage, type = 'success') {
    const bar = progressElement.querySelector('.opr-progress-bar');
    if (bar) {
        bar.style.width = `${Math.max(0, Math.min(100, percentage))}%`;
        
        // Update color based on type
        bar.className = `opr-progress-bar opr-progress-${type}`;
    }
}

/**
 * Format account info for display
 */
function formatAccountInfo(account) {
    return {
        email: account.email,
        password: account.password,
        expirationDate: new Date(account.expiration_date).toLocaleDateString(),
        currentUsers: account.current_users || 0,
        maxUsers: account.max_concurrent_users || 1,
        capacityPercent: Math.round(((account.current_users || 0) / (account.max_concurrent_users || 1)) * 100)
    };
}

/**
 * Format cooldown info for display
 */
function formatCooldownInfo(cooldown) {
    const remainingSeconds = cooldown.remaining_seconds || 0;
    const totalSeconds = cooldown.total_seconds || 1;
    const progressPercent = Math.max(0, Math.min(100, ((totalSeconds - remainingSeconds) / totalSeconds) * 100));
    
    return {
        username: cooldown.username,
        type: cooldown.cooldown_type,
        remainingFormatted: formatDuration(remainingSeconds),
        progressPercent: progressPercent,
        reason: cooldown.reason || 'No reason provided',
        canOverride: cooldown.admin_override_available
    };
}

// ========== Event Handlers ==========

/**
 * Handle form submission with validation
 */
function handleFormSubmit(formElement, submitHandler) {
    formElement.addEventListener('submit', async function(event) {
        event.preventDefault();
        
        if (OPR.state.isProcessing) {
            return;
        }
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData.entries());
        
        // Get required fields from form
        const requiredFields = Array.from(this.querySelectorAll('[required]')).map(field => field.name);
        
        // Validate form
        const errors = validateForm(data, requiredFields);
        if (errors.length > 0) {
            showAlert(errors.join(', '), 'error');
            return;
        }
        
        // Find submit button
        const submitButton = this.querySelector('button[type="submit"]');
        
        try {
            if (submitButton) {
                setButtonLoading(submitButton, true);
            }
            
            await submitHandler(data);
        } catch (error) {
            showAlert(error.message || 'Operation failed', 'error');
        } finally {
            if (submitButton) {
                setButtonLoading(submitButton, false);
            }
        }
    });
}

// ========== Initialization ==========

/**
 * Initialize plugin JavaScript
 */
function initializeOPR() {
    console.log('Initializing OpenAI Plus Redeem Plugin JavaScript');
    
    // Setup form validation for all forms
    document.querySelectorAll('form').forEach(form => {
        setupFormValidation(form);
    });
    
    // Setup global error handling
    window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled promise rejection:', event.reason);
        showAlert('An unexpected error occurred', 'error');
    });
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        stopStatusMonitoring();
        
        if (OPR.state.verificationTimer) {
            clearTimeout(OPR.state.verificationTimer);
        }
    });
}

// ========== Auto-initialization ==========
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeOPR);
} else {
    initializeOPR();
}

// ========== Export for global access ==========
window.OPR = OPR;
window.OPRUtils = {
    makeAPIRequest,
    showAlert,
    validateForm,
    copyToClipboard,
    formatDuration,
    processRedemption,
    getRedemptionStatus,
    searchVerificationCode,
    loadDashboardStats,
    loadAccounts,
    createAccount,
    updateAccount,
    deleteAccount,
    loadCooldowns,
    setCooldown,
    removeCooldown,
    cleanupExpiredData
};
