"""
VPN Base Strategy
Base class for all VPN-related redemption strategies
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import os
import logging

from .base_strategy import BaseRedemptionStrategy
from ..services.vpn_api_service import get_vpn_api_service

logger = logging.getLogger(__name__)

class VPNBaseStrategy(BaseRedemptionStrategy):
    """
    Base class for all VPN redemption strategies.
    Provides common VPN functionality and defines the interface.
    """
    
    def __init__(self):
        self.vpn_service = None
        self._initialize_vpn_service()

    def _initialize_vpn_service(self):
        """Initialize VPN service lazily"""
        if not self.vpn_service:
            self.vpn_service = get_vpn_api_service()
            if not self.vpn_service:
                logger.warning("VPN API service not initialized. Some functionality may not work.")

    def _get_all_servers(self) -> List[Dict[str, Any]]:
        """
        Get all available VPN servers directly from the VPN service.
        """
        try:
            if not self.vpn_service:
                self._initialize_vpn_service()
                if not self.vpn_service:
                    logger.error("VPN API service not available")
                    return []

            servers = self.vpn_service.get_servers()
            if isinstance(servers, list):
                return servers
            elif isinstance(servers, dict) and 'servers' in servers:
                return servers['servers']
            else:
                logger.warning(f"Unexpected server response format: {type(servers)}")
                return []
        except Exception as e:
            logger.error(f"Error getting all servers: {str(e)}")
            return []

    def _filter_servers_by_tags(self, servers: List[Dict[str, Any]], required_tags: List[str]) -> List[Dict[str, Any]]:
        """
        Filter servers based on required tags.

        Args:
            servers (List[Dict[str, Any]]): List of servers to filter
            required_tags (List[str]): List of tags that servers must have

        Returns:
            List[Dict[str, Any]]: Filtered list of servers
        """
        if not required_tags:
            return servers

        filtered_servers = []

        for server in servers:
            server_tags = server.get('tags', [])

            # Convert tags to lowercase for case-insensitive matching
            server_tags_lower = [tag.lower().strip() for tag in server_tags if tag]
            required_tags_lower = [tag.lower().strip() for tag in required_tags if tag]

            # Check if server has any of the required tags
            has_required_tag = any(req_tag in server_tags_lower for req_tag in required_tags_lower)

            if has_required_tag:
                filtered_servers.append(server)
                logger.debug(f"Server {server.get('name', 'Unknown')} matches tags: {server_tags}")
            else:
                logger.debug(f"Server {server.get('name', 'Unknown')} does not match required tags. "
                           f"Server tags: {server_tags}, Required: {required_tags}")

        logger.info(f"Filtered {len(filtered_servers)} servers from {len(servers)} based on tags: {required_tags}")
        return filtered_servers

    def _get_servers_by_tags(self, required_tags: List[str]) -> List[Dict[str, Any]]:
        """
        Get servers filtered by required tags.

        Args:
            required_tags (List[str]): List of tags that servers must have

        Returns:
            List[Dict[str, Any]]: List of servers matching the tags
        """
        all_servers = self._get_all_servers()
        active_servers = [s for s in all_servers if s.get('is_active', False)]

        if not required_tags:
            logger.info(f"No tags specified, returning all {len(active_servers)} active servers")
            return active_servers

        filtered_servers = self._filter_servers_by_tags(active_servers, required_tags)

        if not filtered_servers:
            logger.warning(f"No servers found with tags {required_tags}. Falling back to all active servers.")
            return active_servers

        return filtered_servers

    def _extract_days_from_sku(self, product_sku: str) -> int:
        """
        Extract the validity days from the product SKU.
        First tries to get from configuration, then falls back to regex extraction.

        Args:
            product_sku (str): The product SKU (e.g., "my_highspeed_30", "sg_15")

        Returns:
            int: Number of validity days, defaults to 30 if not found
        """
        try:
            # First try to get validity from configuration
            from .strategy_factory import VPNStrategyFactory
            validity_days = VPNStrategyFactory.get_validity_days_for_sku(product_sku)
            logger.debug(f"Got validity for SKU {product_sku}: {validity_days} days")
            return validity_days
        except Exception as e:
            logger.error(f"Error getting validity from configuration for SKU {product_sku}: {str(e)}")

            # Fallback to regex extraction
            try:
                import re
                match = re.search(r'_(\d+)$', product_sku)
                if match:
                    days = int(match.group(1))
                    logger.debug(f"Extracted {days} days from SKU: {product_sku}")
                    return days
                else:
                    logger.warning(f"Could not extract days from SKU: {product_sku}, using default 30 days")
                    return 30
            except Exception as e2:
                logger.error(f"Error extracting days from SKU {product_sku}: {str(e2)}")
                return 30
    
    @abstractmethod
    def get_target_servers(self, product_sku: str) -> List[Dict[str, Any]]:
        """
        Get the target servers for this VPN product type.
        Must be implemented by each strategy.
        """
        pass
    
    @abstractmethod
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create VPN users on the target servers.
        Must be implemented by each strategy.
        """
        pass
    
    def validate_user_data(self, order_details: Dict[str, Any], product_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and extract user data from order and product details.
        """
        # Extract customer information
        customer_email = order_details.get('customer_email', '').strip()
        shopee_username = order_details.get('shopee_username', '').strip()
        order_id = order_details.get('id', order_details.get('order_id', ''))
        
        # Extract product information
        product_sku = product_details.get('var_sku', product_details.get('sku', '')).strip()
        product_name = product_details.get('name', '').strip()
        
        # Validation
        if not customer_email:
            return {"valid": False, "error": "Customer email is required"}
        if not shopee_username:
            return {"valid": False, "error": "Shopee username is required"}
        if not product_sku:
            return {"valid": False, "error": "Product SKU is required"}
        if not order_id:
            return {"valid": False, "error": "Order ID is required"}
        
        # Calculate expiry date - extract from SKU or use provided days
        expiry_days = product_details.get('vpn_days')

        # If no explicit days provided, try to extract from SKU
        if not expiry_days:
            expiry_days = self._extract_days_from_sku(product_sku)

        if 'lifetime' in product_sku.lower() or expiry_days == 0:
            expiry_date = "lifetime"
        else:
            expiry_date = (datetime.now() + timedelta(days=expiry_days)).strftime("%d-%m-%Y")
        
        return {
            "valid": True,
            "customer_email": customer_email,
            "shopee_username": shopee_username,
            "order_id": order_id,
            "product_sku": product_sku,
            "product_name": product_name,
            "expiry_date": expiry_date,
            "description": f"Order {order_id} - {shopee_username} - {product_name}"
        }
    
    def check_email_availability(self, email: str, servers: List[Dict[str, Any]], emit_progress=None) -> Dict[str, Any]:
        """
        Check if email is available across target servers with optional progress reporting.
        """
        try:
            if not self.vpn_service:
                self._initialize_vpn_service()
                if not self.vpn_service:
                    logger.error("VPN API service not available")
                    return {"available": True, "warning": "Could not verify email uniqueness: VPN API service not available"}

            result = self.vpn_service.check_email_exists(email, servers, emit_progress)
            if result.get("exists"):
                existing_server = result.get("server", {})
                checked_info = result.get("checked_servers", [])
                total_checked = result.get("total_checked", 0)

                return {
                    "available": False,
                    "error": f"Email {email} already exists on server {existing_server.get('name', 'Unknown')}",
                    "existing_client": result.get("client"),
                    "checked_servers": checked_info,
                    "total_checked": total_checked
                }

            checked_info = result.get("checked_servers", [])
            total_checked = result.get("total_checked", 0)

            return {
                "available": True,
                "checked_servers": checked_info,
                "total_checked": total_checked
            }
        except Exception as e:
            logger.error(f"Error checking email availability: {str(e)}")
            return {"available": True, "warning": f"Could not verify email uniqueness: {str(e)}"}

    def _generate_vpn_config_link(self, order_id: str, product_sku: str) -> str:
        """
        Generate VPN configuration link based on product SKU and order ID.
        Adapted for SteamCodeTool plugin architecture.
        """
        # Use the plugin route pattern
        if any(keyword in product_sku.lower() for keyword in ['my_', 'sg_', 'vpn']):
            return f"/api/vpn/config?orderid={order_id}&varSku={product_sku}"
        else:
            # For non-VPN products, use a generic generator link
            return f"https://generator.online-mtyb.com/index.html?orderid={order_id}"
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format a success response with VPN account details.
        Always include redemption links for VPN products, even if already redeemed.
        """
        order_id = user_data.get('order_id', '')
        product_sku = user_data.get('product_sku', '')
        expiry_date = user_data.get('expiry_date', '')
        
        # Check if this was an existing redemption
        is_existing_redemption = vpn_result.get('existing_redemption', False)
        created_clients = vpn_result.get('created_clients', [])
        
        # Generate the VPN configuration link based on the product SKU
        config_link = self._generate_vpn_config_link(order_id, product_sku)
        
        # For VPN products, always provide the redemption link
        redemption_message = [
            "Successfully Redeemed The Order",
            "<b>Access your VPN configuration:</b>",
            "",
            f"<a href='{config_link}' class='btn btn-primary-custom' style='display: inline-block; margin: 0.5rem 0; text-decoration: none;'>Get VPN Config</a>",
            "",
            f"Expired Date: {expiry_date}"
        ]
        
        base_response = {
            "success": True,
            "message": "VPN redemption successful" if not is_existing_redemption else "VPN already redeemed - returning access link",
            "redemption_type": "vpn",
            "status": "existing_redemption" if is_existing_redemption else "new_redemption",
            "data": {
                "order_id": order_id,
                "product_sku": product_sku,
                "expiry_date": expiry_date,
                "config_link": config_link,
                "redemption_result": redemption_message,
                "customer_email": user_data.get('customer_email'),
                "shopee_username": user_data.get('shopee_username'),
                "servers_created": len(created_clients),
                "creation_timestamp": datetime.now().isoformat()
            }
        }
        
        # Add server details if available
        if created_clients:
            base_response["data"]["server_details"] = [
                {
                    "server_name": client_info.get('server', {}).get('name', 'Unknown'),
                    "client_id": client_info.get('client', {}).get('id', 'Unknown'),
                    "status": "active"
                }
                for client_info in created_clients
            ]
        
        return base_response
    
    def format_existing_user_response(self, user_data: Dict[str, Any], email_check: Dict[str, Any]) -> Dict[str, Any]:
        """Format response for existing user (VPN products should provide config access)"""
        existing_client = email_check.get('existing_client', {})
        checked_servers = email_check.get('checked_servers', [])

        # Find the server where the user exists
        existing_server = None
        for server_info in checked_servers:
            if server_info.get('found'):
                existing_server = server_info.get('server')
                break

        if not existing_server:
            existing_server = {"name": "Unknown Server", "id": 0}

        # Generate the VPN configuration link
        order_id = user_data.get('order_id', '')
        product_sku = user_data.get('product_sku', '')
        expiry_date = user_data.get('expiry_date', '')
        config_link = self._generate_vpn_config_link(order_id, product_sku)

        # Create the redemption message with config button (same format as new users)
        redemption_message = [
            "Successfully Redeemed The Order",
            "<b>Access your VPN configuration:</b>",
            "",
            f"<a href='{config_link}' class='btn btn-primary-custom' style='display: inline-block; margin: 0.5rem 0; text-decoration: none;'>Get VPN Config</a>",
            "",
            f"Expired Date: {expiry_date}"
        ]

        return {
            "success": True,
            "status": "success",
            "message": f"VPN user already exists on server {existing_server.get('name', 'Unknown')}. Providing config access.",
            "existing_redemption": True,
            "server": existing_server,
            "client": existing_client,
            "order_id": order_id,
            "customer_email": user_data.get('customer_email'),
            "shopee_username": user_data.get('shopee_username'),
            "data": {
                "order_id": order_id,
                "product_sku": product_sku,
                "expiry_date": expiry_date,
                "config_link": config_link,
                "redemption_result": redemption_message,
                "customer_email": user_data.get('customer_email'),
                "shopee_username": user_data.get('shopee_username'),
                "servers_created": 1,  # Existing user counts as 1 server
                "creation_timestamp": datetime.now().isoformat()
            }
        }

    def redeem(self, order_details: Dict[str, Any], product_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main redemption method that orchestrates the VPN creation process.
        This method provides the common workflow for all VPN strategies.
        """
        try:
            # 1. Validate and extract user data
            validation_result = self.validate_user_data(order_details, product_details)
            if not validation_result.get('valid'):
                return self.format_error_response(validation_result.get('error', 'Validation failed'))

            user_data = validation_result
            logger.info(f"Processing VPN redemption for order {user_data['order_id']}, SKU: {user_data['product_sku']}")

            # 2. Get target servers for this product
            target_servers = self.get_target_servers(user_data['product_sku'])
            if not target_servers:
                return self.format_error_response(
                    f"No suitable servers found for SKU: {user_data['product_sku']}",
                    user_data
                )

            logger.info(f"Found {len(target_servers)} target servers for SKU {user_data['product_sku']}")

            # 3. Check email availability with progress reporting
            def emit_progress(progress_data):
                try:
                    # For plugin architecture, we can emit via logging or other mechanisms
                    logger.info(f"Progress: {progress_data}")
                except Exception as e:
                    logger.error(f"Progress emit failed: {e}")

            email_check = self.check_email_availability(user_data['customer_email'], target_servers, emit_progress)
            if not email_check.get('available'):
                # Check if this is due to existing user (which is OK for VPN products)
                if 'already exists' in email_check.get('error', '').lower():
                    logger.info(f"Email {user_data['customer_email']} already exists - this is OK for VPN products, will provide existing config")
                    # For VPN products, existing users should get config access, not error
                    existing_client = email_check.get('existing_client')
                    if existing_client:
                        # Format the existing user response
                        existing_response = self.format_existing_user_response(user_data, email_check)
                        # Return success with existing client info
                        return existing_response

                # Only fail if it's a real error (not just existing user)
                error_msg = email_check.get('error', 'Email not available')
                checked_count = email_check.get('total_checked', 0)
                if checked_count > 0:
                    error_msg += f" (Checked {checked_count} servers)"

                # If we couldn't check any servers due to API errors, continue anyway with a warning
                if checked_count == 0 or 'error' in str(email_check.get('warning', '')).lower():
                    logger.warning(f"Could not verify email uniqueness due to API errors. Continuing with VPN creation.")
                else:
                    return self.format_error_response(error_msg, user_data)

            # 4. Create VPN users using strategy-specific logic
            vpn_result = self.create_vpn_users(target_servers, user_data)

            if vpn_result.get('status') == 'error':
                return self.format_error_response(
                    vpn_result.get('message', 'Failed to create VPN user'),
                    user_data
                )

            # 5. Format and return success response
            return self.format_success_response(user_data, vpn_result)

        except Exception as e:
            logger.error(f"Error in VPN redemption: {str(e)}")
            return self.format_error_response(f"Internal error: {str(e)}", user_data if 'user_data' in locals() else None)
