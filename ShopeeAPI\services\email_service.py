"""
Email service for sending authentication and WebSocket status notifications.
"""
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import time

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending email notifications."""

    def __init__(self, smtp_host: str = "smtp.gmail.com", smtp_port: int = 587):
        """
        Initialize the email service.

        Args:
            smtp_host: SMTP server hostname
            smtp_port: SMTP server port
        """
        self.smtp_host = smtp_host
        self.smtp_port = smtp_port

        # Rate limiting for disconnect emails to prevent spam
        self._last_disconnect_email_time: Optional[datetime] = None
        self._disconnect_email_cooldown_minutes = 15  # Minimum 15 minutes between disconnect emails
        self._disconnect_email_count_in_hour = 0
        self._disconnect_email_hour_start: Optional[datetime] = None
        self._max_disconnect_emails_per_hour = 4  # Maximum 4 disconnect emails per hour

    def _is_disconnect_email_allowed(self) -> tuple[bool, str]:
        """
        Check if sending a disconnect email is allowed based on rate limiting.

        Returns:
            tuple: (is_allowed: bool, reason: str)
        """
        current_time = datetime.now()

        # Check cooldown period (minimum time between emails)
        if self._last_disconnect_email_time:
            time_since_last = current_time - self._last_disconnect_email_time
            if time_since_last < timedelta(minutes=self._disconnect_email_cooldown_minutes):
                remaining_minutes = self._disconnect_email_cooldown_minutes - int(time_since_last.total_seconds() / 60)
                return False, f"Cooldown active, {remaining_minutes} minutes remaining"

        # Check hourly limit
        if self._disconnect_email_hour_start:
            # Reset hourly counter if more than an hour has passed
            if current_time - self._disconnect_email_hour_start >= timedelta(hours=1):
                self._disconnect_email_count_in_hour = 0
                self._disconnect_email_hour_start = current_time
        else:
            # First email of the hour
            self._disconnect_email_hour_start = current_time
            self._disconnect_email_count_in_hour = 0

        # Check if we've exceeded the hourly limit
        if self._disconnect_email_count_in_hour >= self._max_disconnect_emails_per_hour:
            return False, f"Hourly limit reached ({self._max_disconnect_emails_per_hour} emails/hour)"

        return True, "Email allowed"

    def _record_disconnect_email_sent(self):
        """Record that a disconnect email was sent for rate limiting purposes."""
        current_time = datetime.now()
        self._last_disconnect_email_time = current_time
        self._disconnect_email_count_in_hour += 1

        # Initialize hour tracking if not set
        if not self._disconnect_email_hour_start:
            self._disconnect_email_hour_start = current_time

    def send_auth_status_email(
        self,
        from_email: str,
        from_password: str,
        to_email: str,
        auth_valid: bool,
        websocket_connected: bool,
        websocket_reconnected: bool = False,
        error_details: Optional[str] = None
    ) -> bool:
        """
        Send an email about authentication and WebSocket connection status.

        Args:
            from_email: Sender email address
            from_password: Sender email password (app password)
            to_email: Recipient email address
            auth_valid: Whether authentication is valid
            websocket_connected: Whether WebSocket is connected
            websocket_reconnected: Whether WebSocket was reconnected
            error_details: Any error details to include

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Determine emoji status for auth and websocket
            auth_emoji = "✅" if auth_valid else "❌"
            ws_emoji = "🔌✅" if websocket_connected else "🔌❌"
            
            # Create subject with emojis
            subject = f"{auth_emoji} Auth {ws_emoji} WebSocket Connect Status"
            
            # Create the email content
            body = self._create_email_body(
                auth_valid=auth_valid,
                websocket_connected=websocket_connected,
                websocket_reconnected=websocket_reconnected,
                error_details=error_details
            )

            # Send the email
            return self._send_email(
                from_email=from_email,
                from_password=from_password,
                to_email=to_email,
                subject=subject,
                body=body
            )

        except Exception as e:
            logger.error(f"Error sending auth status email: {e}")
            return False

    def _create_email_body(
        self,
        auth_valid: bool,
        websocket_connected: bool,
        websocket_reconnected: bool = False,
        error_details: Optional[str] = None
    ) -> str:
        """
        Create the email body content.

        Args:
            auth_valid: Whether authentication is valid
            websocket_connected: Whether WebSocket is connected
            websocket_reconnected: Whether WebSocket was reconnected
            error_details: Any error details to include

        Returns:
            str: The email body content
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Build email body components
        auth_status = "✅ Valid - Credentials successfully updated" if auth_valid else "❌ Invalid - Credentials may be expired or incorrect"
        ws_status = "✅ Connected - Real-time communication active" if websocket_connected else "❌ Disconnected - Real-time communication unavailable"
        ws_reconnect = "🔄 WebSocket Reconnection: ✅ Successfully reconnected after credential update" if websocket_reconnected else ""
        error_section = f"⚠️ Error Details:\n{error_details}" if error_details else ""
        system_status = "🎉 All systems operational!" if auth_valid and websocket_connected else "⚠️ Some systems may require attention."
        
        body = f"""🔐 Authentication & WebSocket Status Update

📅 Timestamp: {timestamp}

🔑 Authentication Status:
{auth_status}

🔌 WebSocket Status:
{ws_status}

{ws_reconnect}

{error_section}

📊 System Information:
- Service: Shopee API
- Component: Authentication & WebSocket Service
- Action: Credential Update

{system_status}

---
This is an automated notification from the Shopee API system."""
        return body.strip()

    def _send_email(
        self,
        from_email: str,
        from_password: str,
        to_email: str,
        subject: str,
        body: str
    ) -> bool:
        """
        Send an email using SMTP.

        Args:
            from_email: Sender email address
            from_password: Sender email password
            to_email: Recipient email address
            subject: Email subject
            body: Email body

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Create message
            message = MIMEMultipart()
            message["From"] = from_email
            message["To"] = to_email
            message["Subject"] = subject
            
            # Add body to email
            message.attach(MIMEText(body, "plain", "utf-8"))

            # Connect to server and send email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.starttls()  # Enable TLS encryption
                server.login(from_email, from_password)
                text = message.as_string()
                server.sendmail(from_email, to_email, text)

            logger.info(f"Auth status email sent successfully to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send auth status email: {e}")
            return False

    def send_simple_notification(
        self,
        from_email: str,
        from_password: str,
        to_email: str,
        subject: str,
        message: str
    ) -> bool:
        """
        Send a simple notification email.

        Args:
            from_email: Sender email address
            from_password: Sender email password
            to_email: Recipient email address
            subject: Email subject
            message: Email message

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            return self._send_email(
                from_email=from_email,
                from_password=from_password,
                to_email=to_email,
                subject=subject,
                body=message
            )
        except Exception as e:
            logger.error(f"Failed to send simple notification: {e}")
            return False 

    def send_cookie_expiration_warning_email(
        self,
        from_email: str,
        from_password: str,
        to_email: str,
        reason: str,
        details: Optional[str] = None
    ) -> bool:
        """
        Send an email warning about potential cookie expiration.

        Args:
            from_email: Sender email address
            from_password: Sender email password (app password)
            to_email: Recipient email address
            reason: The primary reason why the cookie is suspected to be expired.
            details: Additional details about the check.

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        subject = "⚠️ Shopee API Cookie Expiration Warning"
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        body = f"""
Shopee API Cookie Expiration Warning

📅 Timestamp: {timestamp}

Reason: {reason}

Details:
{details or "No additional details."}

Please update your Shopee authorization code and cookie in the config.json file as soon as possible.

---
This is an automated notification from the Shopee API system."""
        return self._send_email(from_email, from_password, to_email, subject, body.strip())

    def send_websocket_disconnect_email(
        self,
        from_email: str,
        from_password: str,
        to_email: str,
        disconnect_reason: str,
        last_message_time: datetime,
        connection_start_time: datetime,
        reconnect_attempt: int,
        reconnect_success: bool,
        health_metrics: Optional[Dict[str, Any]] = None,
        error_text: Optional[str] = None
    ) -> bool:
        """
        Send an email notification about WebSocket disconnection.

        Args:
            from_email: Sender email address
            from_password: Sender email password (app password)
            to_email: Recipient email address
            disconnect_reason: Reason for the WebSocket disconnection
            last_message_time: Timestamp of the last received message
            connection_start_time: Timestamp when the connection was established
            reconnect_attempt: Number of reconnection attempts made
            reconnect_success: Whether reconnection was successful
            health_metrics: Optional health metrics data (dict or converted to string)
            error_text: Optional additional error information

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        # Check rate limiting first to prevent spam
        is_allowed, reason = self._is_disconnect_email_allowed()
        if not is_allowed:
            logger.info(f"WebSocket disconnect email blocked by rate limiting: {reason}")
            return False

        # Determine the severity based on reconnection success
        status_icon = "✅" if reconnect_success else "❌"
        subject_prefix = "WebSocket Connection" if reconnect_success else "⚠️ WebSocket Disconnection Alert"
        
        subject = f"{subject_prefix} - Shopee API Service"
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Calculate connection duration
        connection_duration = datetime.now() - connection_start_time
        duration_str = str(connection_duration).split('.')[0]  # Remove microseconds
        
        # Format last message time
        last_msg_str = last_message_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # Format health metrics if provided
        health_info = ""
        if health_metrics:
            health_info = "\n\n📊 Health Metrics:\n"
            for key, value in health_metrics.items():
                health_info += f"  • {key}: {value}\n"
        
        # Format error details if provided
        error_info = ""
        if error_text:
            error_info = f"\n\n🔍 Error Details:\n{error_text}"
        
        # Reconnection status
        reconnect_status = "Successful" if reconnect_success else "Failed"
        reconnect_color = "🟢" if reconnect_success else "🔴"
        
        body = f"""
WebSocket Connection Status Report {status_icon}

📅 Timestamp: {timestamp}
📡 Disconnect Reason: {disconnect_reason}
⏰ Last Message Received: {last_msg_str}
⏳ Connection Duration: {duration_str}
🔄 Reconnection Attempts: {reconnect_attempt}
{reconnect_color} Reconnection Status: {reconnect_status}{health_info}{error_info}

{'🎯 Connection has been successfully restored and the service is operating normally.' if reconnect_success else '⚠️ Please check the service status and configuration. The system will continue attempting to reconnect automatically.'}

---
This is an automated notification from the Shopee API WebSocket monitoring system."""
        
        try:
            email_sent = self._send_email(from_email, from_password, to_email, subject, body.strip())

            # Record the email sending for rate limiting (only if successful)
            if email_sent:
                self._record_disconnect_email_sent()
                logger.info(f"WebSocket disconnect email sent successfully (Rate limit: {self._disconnect_email_count_in_hour}/{self._max_disconnect_emails_per_hour} this hour)")

            return email_sent
        except Exception as e:
            logger.error(f"Failed to send WebSocket disconnect email: {e}")
            return False

    def get_disconnect_email_rate_limit_status(self) -> Dict[str, Any]:
        """
        Get the current rate limiting status for disconnect emails.

        Returns:
            dict: Rate limiting status information
        """
        current_time = datetime.now()

        # Calculate time until next email is allowed
        next_email_allowed = None
        if self._last_disconnect_email_time:
            next_allowed_time = self._last_disconnect_email_time + timedelta(minutes=self._disconnect_email_cooldown_minutes)
            if next_allowed_time > current_time:
                next_email_allowed = next_allowed_time.isoformat()

        # Calculate time until hourly limit resets
        hourly_reset_time = None
        if self._disconnect_email_hour_start:
            hourly_reset_time = (self._disconnect_email_hour_start + timedelta(hours=1)).isoformat()

        return {
            "cooldown_minutes": self._disconnect_email_cooldown_minutes,
            "max_emails_per_hour": self._max_disconnect_emails_per_hour,
            "emails_sent_this_hour": self._disconnect_email_count_in_hour,
            "last_email_time": self._last_disconnect_email_time.isoformat() if self._last_disconnect_email_time else None,
            "next_email_allowed": next_email_allowed,
            "hourly_limit_reset_time": hourly_reset_time,
            "current_time": current_time.isoformat()
        }
