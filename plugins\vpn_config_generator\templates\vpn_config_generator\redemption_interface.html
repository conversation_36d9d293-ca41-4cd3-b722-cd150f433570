<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Configuration - Redemption</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        body {
            background: #f8fafc;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
            min-height: 100vh;
        }
        .crypto-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .crypto-button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 48px;
        }
        .crypto-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
        .crypto-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .crypto-input {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px 16px;
            transition: all 0.2s ease;
        }
        .crypto-input:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .crypto-input:disabled {
            background-color: #f9fafb;
            color: #6b7280;
        }
        .info-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8 max-w-2xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">VPN Configuration</h1>
            <p class="text-gray-600">Generate your VPN configuration using your redemption link</p>
        </div>

        <!-- Redemption Link Info -->
        <div class="crypto-card p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Redemption Details</h2>
                <span class="info-badge">Valid Link</span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Customer:</span>
                    <span class="font-medium">{{ link.customer_username }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Validity:</span>
                    <span class="font-medium">{{ link.validity_days }} days</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Server:</span>
                    <span class="font-medium">{{ link.server_id or 'Auto-selected' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Uses:</span>
                    <span class="font-medium">{{ link.current_uses }}/{{ link.max_uses }}</span>
                </div>
                {% if link.telco %}
                <div class="flex justify-between">
                    <span class="text-gray-600">Telco:</span>
                    <span class="font-medium">{{ link.telco|title }}</span>
                </div>
                {% endif %}
                {% if link.plan %}
                <div class="flex justify-between">
                    <span class="text-gray-600">Plan:</span>
                    <span class="font-medium">{{ link.plan|title }}</span>
                </div>
                {% endif %}
            </div>
            
            {% if link.notes %}
            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                <p class="text-sm text-blue-800"><strong>Notes:</strong> {{ link.notes }}</p>
            </div>
            {% endif %}
        </div>

        <!-- Configuration Form -->
        <div class="crypto-card p-6">
            <h2 class="text-xl font-semibold mb-4">Generate VPN Configuration</h2>
            
            <form id="redemptionForm">
                <input type="hidden" id="linkId" value="{{ link_id }}">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Telco</label>
                        <select id="telco" class="crypto-input w-full" {% if link.telco %}disabled{% endif %}>
                            {% if link.telco %}
                                <option value="{{ link.telco }}" selected>{{ link.telco|title }} (Pre-configured)</option>
                            {% else %}
                                <option value="">Select Telco</option>
                                <option value="digi">Digi</option>
                                <option value="maxis">Maxis</option>
                                <option value="celcom">Celcom</option>
                                <option value="umobile">U Mobile</option>
                            {% endif %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Plan</label>
                        <select id="plan" class="crypto-input w-full" {% if link.plan %}disabled{% endif %}>
                            {% if link.plan %}
                                <option value="{{ link.plan }}" selected>{{ link.plan|title }} (Pre-configured)</option>
                            {% else %}
                                <option value="">Select Plan</option>
                                <option value="unlimited">Unlimited</option>
                                <option value="basic">Basic</option>
                                <option value="premium">Premium</option>
                                <option value="booster">Booster</option>
                            {% endif %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Server</label>
                        <input type="text" id="server" class="crypto-input w-full" 
                               value="{{ link.server_id or 'auto' }}" 
                               {% if link.server_id %}disabled{% endif %}
                               placeholder="auto or specific server ID">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Validity Days</label>
                        <input type="number" id="days" class="crypto-input w-full" 
                               value="{{ link.validity_days }}" disabled>
                    </div>
                </div>

                <div class="flex justify-center">
                    <button type="submit" id="generateButton" class="crypto-button">
                        <span id="generateButtonText">Generate VPN Configuration</span>
                        <span id="generateButtonSpinner" class="hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating...
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Result Section -->
        <div id="resultSection" class="hidden crypto-card p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4 text-green-600">✅ Configuration Generated Successfully!</h2>
            <div id="resultContent"></div>
        </div>

        <!-- Error Section -->
        <div id="errorSection" class="hidden crypto-card p-6 mt-6 border-red-200">
            <h2 class="text-xl font-semibold mb-4 text-red-600">❌ Error</h2>
            <div id="errorContent" class="text-red-700"></div>
        </div>
    </div>

    <script>
        document.getElementById('redemptionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const button = document.getElementById('generateButton');
            const buttonText = document.getElementById('generateButtonText');
            const buttonSpinner = document.getElementById('generateButtonSpinner');
            const resultSection = document.getElementById('resultSection');
            const errorSection = document.getElementById('errorSection');
            
            // Show loading state
            button.disabled = true;
            buttonText.classList.add('hidden');
            buttonSpinner.classList.remove('hidden');
            resultSection.classList.add('hidden');
            errorSection.classList.add('hidden');
            
            try {
                const formData = {
                    link_id: document.getElementById('linkId').value,
                    telco: document.getElementById('telco').value,
                    plan: document.getElementById('plan').value,
                    server: document.getElementById('server').value,
                    days: document.getElementById('days').value
                };

                const response = await axios.post('/vpn-config-generator/api/redemption-links/process', formData);
                
                if (response.data.success) {
                    // Show success result
                    document.getElementById('resultContent').innerHTML = `
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Created:</span>
                                    <span class="font-medium">${response.data.created_date}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Expires:</span>
                                    <span class="font-medium">${response.data.expired_date}</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">VPN Configuration:</label>
                                <textarea class="crypto-input w-full h-32 font-mono text-xs" readonly>${response.data.config}</textarea>
                            </div>
                            <div class="flex justify-center">
                                <button onclick="copyConfig()" class="crypto-button">Copy Configuration</button>
                            </div>
                        </div>
                    `;
                    resultSection.classList.remove('hidden');
                    
                    // Store config for copying
                    window.vpnConfig = response.data.config;
                } else {
                    // Show error
                    document.getElementById('errorContent').textContent = response.data.error;
                    errorSection.classList.remove('hidden');
                }
            } catch (error) {
                // Show error
                const errorMessage = error.response?.data?.error || error.message || 'An unexpected error occurred';
                document.getElementById('errorContent').textContent = errorMessage;
                errorSection.classList.remove('hidden');
            } finally {
                // Reset button state
                button.disabled = false;
                buttonText.classList.remove('hidden');
                buttonSpinner.classList.add('hidden');
            }
        });

        // Copy configuration to clipboard
        function copyConfig() {
            if (window.vpnConfig) {
                navigator.clipboard.writeText(window.vpnConfig).then(() => {
                    alert('VPN configuration copied to clipboard!');
                }).catch(() => {
                    alert('Failed to copy configuration to clipboard');
                });
            }
        }

        // Auto-populate form based on link data
        document.addEventListener('DOMContentLoaded', function() {
            // If telco and plan are not pre-configured, set defaults
            const telcoSelect = document.getElementById('telco');
            const planSelect = document.getElementById('plan');
            
            if (!telcoSelect.disabled && !telcoSelect.value) {
                telcoSelect.value = 'digi';
            }
            
            if (!planSelect.disabled && !planSelect.value) {
                planSelect.value = 'unlimited';
            }
        });
    </script>
</body>
</html>
