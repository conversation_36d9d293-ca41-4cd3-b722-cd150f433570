{% extends "base.html" %}

{% block title %}System Configuration{% endblock %}
{% block header %}System Configuration{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="configData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Sidebar -->
        <div class="w-64 mr-8">
            <nav class="space-y-1">
                <template
                    x-for="(section, index) in ['General', 'API', 'Notification', 'Performance', 'Manual Orders']"
                    :key="index">
                    <a href="#" @click.prevent="currentSection = section; animateSection()"
                        :class="{'bg-gray-100 text-gray-900': currentSection === section, 'text-gray-600 hover:bg-gray-50 hover:text-gray-900': currentSection !== section}"
                        class="group flex items-center px-3 py-2 text-sm font-medium rounded-md sidebar-item">
                        <span x-text="section"></span>
                    </a>
                </template>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1">
            <div x-show="currentSection === 'General'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">General Settings</h2>
                <div class="mb-4 config-item">
                    <label for="api_key" class="block text-sm font-medium text-gray-700">API Key</label>
                    <input id="api_key" name="api_key" type="text" x-model="config.API_KEY"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="mb-4 config-item">
                    <label for="shop_id" class="block text-sm font-medium text-gray-700">Shop ID</label>
                    <input id="shop_id" name="shop_id" type="number" x-model="config.SHOP_ID"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <!-- 新增的时区配置项 -->
                <div class="mb-4 config-item">
                    <label for="time_zone" class="block text-sm font-medium text-gray-700">Time Zone</label>
                    <select id="time_zone" name="time_zone" x-model="config.TIME_ZONE"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="Asia/Kuala_Lumpur">Asia/Kuala_Lumpur</option>
                        <option value="Asia/Singapore">Asia/Singapore</option>
                        <option value="Asia/Jakarta">Asia/Jakarta</option>
                        <option value="Asia/Bangkok">Asia/Bangkok</option>
                        <option value="Asia/Manila">Asia/Manila</option>
                        <option value="Asia/Tokyo">Asia/Tokyo</option>
                        <option value="Europe/Tirane">Europe/Tirane</option>
                        <option value="UTC">UTC</option>
                    </select>
                </div>
            </div>

            <div x-show="currentSection === 'API'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">API Settings</h2>

                <!-- Shopee API Settings -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-4">Shopee API Settings</h3>
                    <div class="mb-4 config-item">
                        <label for="shopee_api_url" class="block text-sm font-medium text-gray-700">Shopee API Base URL</label>
                        <div class="mt-1 flex rounded-md shadow-sm">
                            <input id="shopee_api_url" name="shopee_api_url" type="url"
                                   x-model="config.SHOPEE_API_BASE_URL"
                                   placeholder="https://shop.api.limjianhui.com"
                                   class="flex-1 block w-full border border-gray-300 rounded-l-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <button type="button" @click="testShopeeApiConnection()"
                                    class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                                <i class="fas fa-plug mr-1"></i>Test
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">
                            Base URL for the centralized Shopee API service.
                            <br>For development: <code class="text-xs bg-gray-100 px-1 rounded">http://localhost:8000</code>
                            <br>For production: <code class="text-xs bg-gray-100 px-1 rounded">https://shop.api.limjianhui.com</code>
                        </p>
                        <div x-show="shopeeApiTestResult" x-html="shopeeApiTestResult" class="mt-2"></div>
                    </div>
                </div>

                <!-- Payment Gateway Settings -->
                <div class="mt-6">
                    <h3 class="text-xl font-semibold mb-4">Payment Gateway Settings</h3>
                    
                    <!-- Curlec Settings -->
                    <div class="mb-4 config-item">
                        <label class="block text-sm font-medium text-gray-700">Curlec API Credentials</label>
                        <div class="mt-2 space-y-2">
                            <input type="text" 
                                   x-model="config.CURLEC_API_KEY" 
                                   placeholder="Curlec API Key"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <input type="text" 
                                   x-model="config.CURLEC_SECRET_KEY" 
                                   placeholder="Curlec Secret Key"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                    </div>
                </div>
            </div>

            <div x-show="currentSection === 'Notification'" class="section-content">
                <h2 class="text-2xl font-bold mb-6">Notification Settings</h2>

                <!-- Email Notifications Section -->
                <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-envelope mr-2 text-blue-500"></i>Email Notifications
                        </h3>
                        <div class="flex items-center space-x-2">
                            <span id="emailStatus" class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Not Tested</span>
                            <button @click="testEmailConnection()"
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition duration-200">
                                <i class="fas fa-plug mr-1"></i>Test Connection
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="config-item">
                            <label for="notification_email" class="block text-sm font-medium text-gray-700 mb-1">
                                Notification Email Address
                            </label>
                            <input id="notification_email" name="notification_email" type="email"
                                x-model="config.NOTIFICATION_EMAIL.address"
                                placeholder="<EMAIL>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div class="config-item">
                            <label for="notification_app_password" class="block text-sm font-medium text-gray-700 mb-1">
                                App Password
                            </label>
                            <input id="notification_app_password" name="notification_app_password" type="password"
                                x-model="config.NOTIFICATION_EMAIL.app_password"
                                placeholder="Gmail App Password"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="enableEmailNotifications"
                                   x-model="config.NOTIFICATION_EMAIL.enabled"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="enableEmailNotifications" class="ml-2 block text-sm text-gray-700">
                                Enable email notifications for system alerts
                            </label>
                        </div>
                    </div>
                </div>



                <!-- Test Results Section -->
                <div id="testResults" class="bg-white border border-gray-200 rounded-lg p-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-clipboard-check mr-2 text-purple-500"></i>Test Results
                    </h3>
                    <div id="testResultsContent" class="space-y-2">
                        <!-- Test results will be populated here -->
                    </div>
                </div>
            </div>

            <div x-show="currentSection === 'Performance'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Performance Settings</h2>
                <div class="mb-4 config-item">
                    <label for="request_timeout" class="block text-sm font-medium text-gray-700">Request Timeout
                        (seconds)</label>
                    <input id="request_timeout" name="request_timeout" type="number" x-model="config.REQUEST_TIMEOUT"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="mb-4 config-item">
                    <label for="session_cooldown_time" class="block text-sm font-medium text-gray-700">Session Cooldown
                        Time (seconds)</label>
                    <input id="session_cooldown_time" name="session_cooldown_time" type="number"
                        x-model="config.SESSION_COOLDOWN_TIME"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
            </div>



            <!-- Manual Orders Section -->
            <div x-show="currentSection === 'Manual Orders'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Manual Order Management</h2>

                <!-- 创建手动订单的表单 -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Create Manual Order</h3>
                    <div class="config-item">
                        <label for="manual_order_sn" class="block text-sm font-medium text-gray-700">Order SN</label>
                        <input id="manual_order_sn" name="manual_order_sn" type="text" x-model="manualOrder.order_sn"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div class="config-item mt-4">
                        <label for="manual_var_sku" class="block text-sm font-medium text-gray-700">VAR SKU</label>
                        <input id="manual_var_sku" name="manual_var_sku" type="text" x-model="manualOrder.var_sku"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <button @click="createManualOrder"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Create Manual Order
                    </button>
                </div>

                <!-- 显示手动创建的订单列表 -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Manual Orders</h3>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Order SN</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    VAR SKU</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(order, index) in manualOrders" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.order_sn"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.var_sku"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click.prevent="deleteManualOrder(index)"
                                            class="text-red-600 hover:text-red-900">Delete</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="mt-4">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function configData() {
        return {
            config: {
                TIME_ZONE: 'Asia/Kuala_Lumpur',
            },
            autoRedeemSKUs: '',
            currentSection: 'General',
            isLoaded: false,
            shopeeApiTestResult: '',

            manualOrder: {
                order_sn: '',
                var_sku: ''
            },
            manualOrders: [],
            init() {
                this.loadConfig();
                this.loadManualOrders();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            saveConfig() {
                // Convert numeric fields to numbers
                const numericFields = ['REQUEST_TIMEOUT', 'SESSION_COOLDOWN_TIME', 'SHOP_ID'];
                
                numericFields.forEach(field => {
                    if (this.config[field] !== undefined) {
                        this.config[field] = Number(this.config[field]);
                    }
                });

                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        this.animateSaveButton();
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                        alert('An error occurred while saving the configuration.');
                    });
            },

            createManualOrder() {
                if (this.manualOrder.order_sn.trim() === '' || this.manualOrder.var_sku.trim() === '') {
                    alert('Order SN and VAR SKU cannot be empty.');
                    return;
                }
                this.manualOrders.push({ ...this.manualOrder });
                this.manualOrder = { order_sn: '', var_sku: '' };
                this.saveManualOrders();
            },
            deleteManualOrder(index) {
                if (confirm('Are you sure you want to delete this manual order?')) {
                    this.manualOrders.splice(index, 1);
                    this.saveManualOrders();
                }
            },
            saveManualOrders() {
                fetch('/admin/update_manual_orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.manualOrders),
                })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                        alert('An error occurred while saving manual orders.');
                    });
            },
            loadManualOrders() {
                fetch('/admin/get_manual_orders')
                    .then(response => response.json())
                    .then(data => {
                        this.manualOrders = data;
                    })
                    .catch(error => {
                        console.error('Error loading manual orders:', error);
                    });
            },

            animateInitialLoad() {
                anime({
                    targets: '.sidebar-item',
                    translateX: [-50, 0],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutQuad'
                });

                this.animateSection();
            },
            animateSection() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            },

            // Test email connection
            async testEmailConnection() {
                const emailConfig = this.config.NOTIFICATION_EMAIL || {};

                if (!emailConfig.address || !emailConfig.app_password) {
                    this.showTestResult('email', 'error', 'Please configure email address and app password first');
                    return;
                }

                this.updateConnectionStatus('email', 'testing');

                try {
                    const response = await fetch('/admin/test_email_notification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email: emailConfig.address,
                            app_password: emailConfig.app_password
                        }),
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        this.updateConnectionStatus('email', 'success');
                        this.showTestResult('email', 'success', 'Email connection successful! Test email sent.');
                    } else {
                        this.updateConnectionStatus('email', 'error');
                        this.showTestResult('email', 'error', result.error || 'Email connection failed');
                    }
                } catch (error) {
                    this.updateConnectionStatus('email', 'error');
                    this.showTestResult('email', 'error', `Connection error: ${error.message}`);
                }
            },



            // Update connection status indicators
            updateConnectionStatus(type, status) {
                const statusElement = document.getElementById(`${type}Status`);
                if (!statusElement) return;

                // Remove existing classes
                statusElement.className = 'px-2 py-1 text-xs font-medium rounded-full';

                switch (status) {
                    case 'testing':
                        statusElement.className += ' bg-yellow-100 text-yellow-800';
                        statusElement.textContent = 'Testing...';
                        break;
                    case 'success':
                        statusElement.className += ' bg-green-100 text-green-800';
                        statusElement.textContent = 'Connected';
                        break;
                    case 'error':
                        statusElement.className += ' bg-red-100 text-red-800';
                        statusElement.textContent = 'Failed';
                        break;
                    case 'warning':
                        statusElement.className += ' bg-orange-100 text-orange-800';
                        statusElement.textContent = 'Warning';
                        break;
                    default:
                        statusElement.className += ' bg-gray-100 text-gray-800';
                        statusElement.textContent = 'Not Tested';
                }
            },

            // Show test results
            showTestResult(type, status, message, details = null) {
                const resultsSection = document.getElementById('testResults');
                const resultsContent = document.getElementById('testResultsContent');

                if (!resultsSection || !resultsContent) return;

                // Show results section
                resultsSection.classList.remove('hidden');

                // Create result item
                const resultItem = document.createElement('div');
                resultItem.className = 'p-3 rounded-lg border-l-4';

                let iconClass, bgClass, borderClass;
                switch (status) {
                    case 'success':
                        iconClass = 'fas fa-check-circle text-green-500';
                        bgClass = 'bg-green-50';
                        borderClass = 'border-green-400';
                        break;
                    case 'error':
                        iconClass = 'fas fa-exclamation-circle text-red-500';
                        bgClass = 'bg-red-50';
                        borderClass = 'border-red-400';
                        break;
                    case 'warning':
                        iconClass = 'fas fa-exclamation-triangle text-orange-500';
                        bgClass = 'bg-orange-50';
                        borderClass = 'border-orange-400';
                        break;
                    default:
                        iconClass = 'fas fa-info-circle text-blue-500';
                        bgClass = 'bg-blue-50';
                        borderClass = 'border-blue-400';
                }

                resultItem.className += ` ${bgClass} ${borderClass}`;

                let detailsHtml = '';
                if (details) {
                    if (Array.isArray(details)) {
                        detailsHtml = '<ul class="mt-2 text-sm text-gray-600 list-disc list-inside">';
                        details.forEach(detail => {
                            detailsHtml += `<li>${detail}</li>`;
                        });
                        detailsHtml += '</ul>';
                    } else if (typeof details === 'object') {
                        detailsHtml = '<div class="mt-2 text-sm text-gray-600">';
                        Object.entries(details).forEach(([key, value]) => {
                            detailsHtml += `<div><strong>${key}:</strong> ${value}</div>`;
                        });
                        detailsHtml += '</div>';
                    } else {
                        detailsHtml = `<div class="mt-2 text-sm text-gray-600">${details}</div>`;
                    }
                }

                resultItem.innerHTML = `
                    <div class="flex items-start">
                        <i class="${iconClass} mr-2 mt-0.5"></i>
                        <div class="flex-1">
                            <div class="font-medium text-gray-800">${type.charAt(0).toUpperCase() + type.slice(1)} Test</div>
                            <div class="text-sm text-gray-600">${message}</div>
                            ${detailsHtml}
                        </div>
                        <span class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</span>
                    </div>
                `;

                // Add to results (prepend to show latest first)
                resultsContent.insertBefore(resultItem, resultsContent.firstChild);

                // Limit to last 5 results
                while (resultsContent.children.length > 5) {
                    resultsContent.removeChild(resultsContent.lastChild);
                }

                // Animate the new result
                anime({
                    targets: resultItem,
                    opacity: [0, 1],
                    translateY: [-20, 0],
                    duration: 300,
                    easing: 'easeOutQuad'
                });
            },

            // Test Shopee API connection
            async testShopeeApiConnection() {
                const apiUrl = this.config.SHOPEE_API_BASE_URL;

                if (!apiUrl) {
                    this.shopeeApiTestResult = '<div class="text-red-600 text-sm"><i class="fas fa-exclamation-circle mr-1"></i>Please enter a Shopee API URL first</div>';
                    return;
                }

                this.shopeeApiTestResult = '<div class="text-blue-600 text-sm"><i class="fas fa-spinner fa-spin mr-1"></i>Testing connection...</div>';

                try {
                    const response = await fetch('/admin/test_shopee_api', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ url: apiUrl }),
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        this.shopeeApiTestResult = `<div class="text-green-600 text-sm"><i class="fas fa-check-circle mr-1"></i>${result.message}</div>`;
                    } else {
                        this.shopeeApiTestResult = `<div class="text-red-600 text-sm"><i class="fas fa-times-circle mr-1"></i>${result.error}</div>`;
                    }
                } catch (error) {
                    this.shopeeApiTestResult = `<div class="text-red-600 text-sm"><i class="fas fa-times-circle mr-1"></i>Connection failed: ${error.message}</div>`;
                }

                // Clear the result after 5 seconds
                setTimeout(() => {
                    this.shopeeApiTestResult = '';
                }, 5000);
            }
        }
    }
</script>
{% endblock %}