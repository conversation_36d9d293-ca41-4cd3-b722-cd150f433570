#!/usr/bin/env python3
"""
Example: Send image using simplified payload

This example demonstrates how to use the new simplified send_image endpoint
that only requires username and image_url.
"""

import requests
import json

# ShopeeAPI endpoint
API_BASE_URL = "http://localhost:8000"  # Change to your ShopeeAPI URL
SEND_IMAGE_ENDPOINT = f"{API_BASE_URL}/chat/send_image"

def send_image(username: str, image_url: str):
    """
    Send an image using the simplified payload structure.
    
    Args:
        username: Recipient username
        image_url: URL of the image to send
    """
    payload = {
        "username": username,
        "image_url": image_url
    }
    
    print(f"Sending image to {username}: {image_url}")
    
    try:
        response = requests.post(SEND_IMAGE_ENDPOINT, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Image sent successfully!")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Failed to send image: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error sending image: {e}")

def send_multiple_images(username: str, image_urls: list):
    """
    Send multiple images to a user.
    
    Args:
        username: Recipient username
        image_urls: List of image URLs to send
    """
    print(f"Sending {len(image_urls)} images to {username}...")
    
    for i, image_url in enumerate(image_urls, 1):
        print(f"\n--- Sending image {i}/{len(image_urls)} ---")
        send_image(username, image_url)
        
        # Add a small delay between images to avoid rate limiting
        if i < len(image_urls):
            import time
            time.sleep(1)

def main():
    """
    Example usage of simplified image sending.
    """
    print("=== ShopeeAPI Simplified Image Sending Examples ===\n")
    
    # Example recipient and image URLs
    recipient_username = "test_buyer"  # Change to actual username
    
    # Example images
    example_images = [
        "https://via.placeholder.com/800x600.jpg",  # Landscape
        "https://via.placeholder.com/400x800.png",  # Portrait  
        "https://via.placeholder.com/500x500.gif",  # Square
        "https://cf.shopee.com.my/file/sg-11134209-7rdxa-mcgaxypsb8zy41"  # Real Shopee image
    ]
    
    print("1. Sending individual images:")
    print("-" * 40)
    
    for i, image_url in enumerate(example_images, 1):
        print(f"\nExample {i}:")
        send_image(recipient_username, image_url)
        print()
    
    print("\n2. Sending multiple images in batch:")
    print("-" * 45)
    batch_images = [
        "https://via.placeholder.com/300x200.jpg",
        "https://via.placeholder.com/200x300.png",
        "https://via.placeholder.com/250x250.gif"
    ]
    send_multiple_images(recipient_username, batch_images)
    
    print("\n=== Examples completed ===")
    print("\nKey benefits of the simplified payload:")
    print("✅ Only requires username and image_url")
    print("✅ Automatic dimension detection and thumbnail generation")
    print("✅ Simplified integration - no complex payload structure")
    print("✅ Better error handling and reliability")
    print("✅ Supports all common image formats (JPG, PNG, GIF, WebP)")

if __name__ == "__main__":
    main()
