#!/usr/bin/env python3
"""
Standards Compliance Validation Script

This script validates the OpenAI Plus Redeem Plugin against all plugin development standards.
"""

import os
import sys
import json
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Tuple
import ast
import re

class StandardsValidator:
    """Validates plugin compliance with development standards"""
    
    def __init__(self, plugin_path: str):
        self.plugin_path = Path(plugin_path)
        self.plugin_name = self.plugin_path.name
        self.validation_results = []
        self.errors = []
        self.warnings = []
        
    def validate_all(self) -> Dict[str, Any]:
        """Run all validation checks"""
        print(f"🔍 Validating {self.plugin_name} plugin against development standards...")
        print("=" * 80)
        
        # Architecture Standards
        self._validate_directory_structure()
        self._validate_plugin_class()
        self._validate_service_layer()
        self._validate_route_blueprints()
        
        # Configuration Standards
        self._validate_configuration_schema()
        
        # Code Style Standards
        self._validate_code_style()
        
        # Documentation Standards
        self._validate_documentation()
        
        # Testing Standards
        self._validate_testing()
        
        return self._generate_report()
    
    def _validate_directory_structure(self):
        """Validate plugin directory structure against standards"""
        print("📁 Validating Directory Structure...")
        
        required_files = [
            "__init__.py",
            "plugin.py",
            "README.md"
        ]
        
        recommended_dirs = [
            "services",
            "routes", 
            "models",
            "templates",
            "tests"
        ]
        
        # Check required files
        for file in required_files:
            file_path = self.plugin_path / file
            if file_path.exists():
                self._add_success(f"✓ Required file exists: {file}")
            else:
                self._add_error(f"✗ Missing required file: {file}")
        
        # Check recommended directories
        for dir_name in recommended_dirs:
            dir_path = self.plugin_path / dir_name
            if dir_path.exists() and dir_path.is_dir():
                self._add_success(f"✓ Recommended directory exists: {dir_name}/")
                
                # Check for __init__.py in Python packages
                if dir_name in ["services", "routes", "models", "tests"]:
                    init_file = dir_path / "__init__.py"
                    if init_file.exists():
                        self._add_success(f"✓ Package init file exists: {dir_name}/__init__.py")
                    else:
                        self._add_warning(f"⚠ Missing package init file: {dir_name}/__init__.py")
            else:
                self._add_warning(f"⚠ Missing recommended directory: {dir_name}/")
        
        # Check for additional documentation
        docs_files = ["API.md", "CONFIGURATION.md", "TROUBLESHOOTING.md", "DEPLOYMENT.md"]
        for doc_file in docs_files:
            doc_path = self.plugin_path / doc_file
            if doc_path.exists():
                self._add_success(f"✓ Documentation file exists: {doc_file}")
            else:
                self._add_warning(f"⚠ Missing documentation file: {doc_file}")
    
    def _validate_plugin_class(self):
        """Validate main plugin class implementation"""
        print("🏗️ Validating Plugin Class...")
        
        plugin_file = self.plugin_path / "plugin.py"
        if not plugin_file.exists():
            self._add_error("✗ plugin.py file not found")
            return
        
        try:
            # Parse the plugin file
            with open(plugin_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # Find plugin class
            plugin_class = None
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Check if class inherits from PluginInterface
                    for base in node.bases:
                        if isinstance(base, ast.Name) and base.id == 'PluginInterface':
                            plugin_class = node
                            break
                    if plugin_class:
                        break
            
            if plugin_class:
                self._add_success("✓ Plugin class inherits from PluginInterface")
                
                # Check required methods
                required_methods = ['initialize', 'shutdown', 'get_blueprint', 'get_config_schema']
                class_methods = [node.name for node in plugin_class.body if isinstance(node, ast.FunctionDef)]
                
                for method in required_methods:
                    if method in class_methods:
                        self._add_success(f"✓ Required method implemented: {method}()")
                    else:
                        self._add_error(f"✗ Missing required method: {method}()")
                
                # Check for proper initialization
                init_method = next((node for node in plugin_class.body 
                                  if isinstance(node, ast.FunctionDef) and node.name == '__init__'), None)
                if init_method:
                    self._add_success("✓ __init__ method found")
                    
                    # Check for required attributes
                    required_attrs = ['name', 'version', 'description']
                    for attr in required_attrs:
                        if f'self.{attr}' in content:
                            self._add_success(f"✓ Required attribute set: {attr}")
                        else:
                            self._add_warning(f"⚠ Missing recommended attribute: {attr}")
                else:
                    self._add_warning("⚠ No __init__ method found")
            else:
                self._add_error("✗ No plugin class inheriting from PluginInterface found")
                
        except Exception as e:
            self._add_error(f"✗ Error parsing plugin.py: {e}")
    
    def _validate_service_layer(self):
        """Validate service layer implementation"""
        print("🔧 Validating Service Layer...")
        
        services_dir = self.plugin_path / "services"
        if not services_dir.exists():
            self._add_warning("⚠ No services directory found")
            return
        
        # Check for base service
        base_service_file = services_dir / "base_service.py"
        if base_service_file.exists():
            self._add_success("✓ Base service file exists")
            
            try:
                with open(base_service_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for BaseService class
                if 'class BaseService' in content:
                    self._add_success("✓ BaseService class found")
                    
                    # Check for required methods
                    required_methods = ['initialize', 'shutdown', 'health_check']
                    for method in required_methods:
                        if f'def {method}(' in content:
                            self._add_success(f"✓ BaseService method found: {method}()")
                        else:
                            self._add_warning(f"⚠ Missing BaseService method: {method}()")
                else:
                    self._add_warning("⚠ BaseService class not found in base_service.py")
                    
            except Exception as e:
                self._add_error(f"✗ Error reading base_service.py: {e}")
        else:
            self._add_warning("⚠ No base_service.py found")
        
        # Check service implementations
        service_files = [f for f in services_dir.glob("*.py") 
                        if f.name not in ["__init__.py", "base_service.py"]]
        
        if service_files:
            self._add_success(f"✓ Found {len(service_files)} service implementation(s)")
            
            for service_file in service_files:
                try:
                    with open(service_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check if service inherits from BaseService
                    if 'BaseService' in content and 'class ' in content:
                        self._add_success(f"✓ Service uses BaseService pattern: {service_file.name}")
                    else:
                        self._add_warning(f"⚠ Service may not inherit from BaseService: {service_file.name}")
                        
                except Exception as e:
                    self._add_error(f"✗ Error reading {service_file.name}: {e}")
        else:
            self._add_warning("⚠ No service implementation files found")
    
    def _validate_route_blueprints(self):
        """Validate Flask blueprint implementation"""
        print("🌐 Validating Route Blueprints...")
        
        routes_dir = self.plugin_path / "routes"
        if not routes_dir.exists():
            self._add_warning("⚠ No routes directory found")
            return
        
        route_files = [f for f in routes_dir.glob("*.py") if f.name != "__init__.py"]
        
        if route_files:
            self._add_success(f"✓ Found {len(route_files)} route file(s)")
            
            for route_file in route_files:
                try:
                    with open(route_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for Blueprint usage
                    if 'Blueprint' in content:
                        self._add_success(f"✓ Blueprint pattern used: {route_file.name}")
                    else:
                        self._add_warning(f"⚠ No Blueprint found in: {route_file.name}")
                    
                    # Check for route decorators
                    if '@bp.route(' in content or '@blueprint.route(' in content:
                        self._add_success(f"✓ Route decorators found: {route_file.name}")
                    else:
                        self._add_warning(f"⚠ No route decorators found: {route_file.name}")
                        
                except Exception as e:
                    self._add_error(f"✗ Error reading {route_file.name}: {e}")
        else:
            self._add_warning("⚠ No route implementation files found")
    
    def _validate_configuration_schema(self):
        """Validate configuration schema implementation"""
        print("⚙️ Validating Configuration Schema...")
        
        # Check if plugin implements get_config_schema
        plugin_file = self.plugin_path / "plugin.py"
        if plugin_file.exists():
            try:
                with open(plugin_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'def get_config_schema(' in content:
                    self._add_success("✓ get_config_schema method implemented")
                    
                    # Check for standard schema structure
                    if '"type": "object"' in content:
                        self._add_success("✓ Schema uses object type")
                    
                    if '"properties"' in content:
                        self._add_success("✓ Schema defines properties")
                    
                    # Check for required properties
                    if '"enabled"' in content:
                        self._add_success("✓ Schema includes 'enabled' property")
                    else:
                        self._add_warning("⚠ Schema missing 'enabled' property")
                        
                else:
                    self._add_error("✗ get_config_schema method not found")
                    
            except Exception as e:
                self._add_error(f"✗ Error validating configuration schema: {e}")
        
        # Check for separate config schema file
        config_schema_file = self.plugin_path / "config_schema.json"
        if config_schema_file.exists():
            self._add_success("✓ Separate config schema file exists")
            
            try:
                with open(config_schema_file, 'r', encoding='utf-8') as f:
                    schema = json.load(f)
                
                if schema.get('type') == 'object':
                    self._add_success("✓ Config schema is valid JSON object")
                else:
                    self._add_warning("⚠ Config schema should be object type")
                    
            except json.JSONDecodeError as e:
                self._add_error(f"✗ Invalid JSON in config_schema.json: {e}")
            except Exception as e:
                self._add_error(f"✗ Error reading config_schema.json: {e}")
    
    def _validate_code_style(self):
        """Validate code style compliance"""
        print("📝 Validating Code Style...")
        
        python_files = list(self.plugin_path.rglob("*.py"))
        
        if python_files:
            self._add_success(f"✓ Found {len(python_files)} Python files to check")
            
            for py_file in python_files:
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for docstrings in classes and functions
                    tree = ast.parse(content)
                    
                    classes_with_docstrings = 0
                    total_classes = 0
                    functions_with_docstrings = 0
                    total_functions = 0
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.ClassDef):
                            total_classes += 1
                            if ast.get_docstring(node):
                                classes_with_docstrings += 1
                        elif isinstance(node, ast.FunctionDef):
                            total_functions += 1
                            if ast.get_docstring(node):
                                functions_with_docstrings += 1
                    
                    if total_classes > 0:
                        docstring_ratio = classes_with_docstrings / total_classes
                        if docstring_ratio >= 0.8:
                            self._add_success(f"✓ Good class docstring coverage: {py_file.name}")
                        else:
                            self._add_warning(f"⚠ Low class docstring coverage: {py_file.name}")
                    
                    if total_functions > 0:
                        docstring_ratio = functions_with_docstrings / total_functions
                        if docstring_ratio >= 0.6:
                            self._add_success(f"✓ Good function docstring coverage: {py_file.name}")
                        else:
                            self._add_warning(f"⚠ Low function docstring coverage: {py_file.name}")
                    
                except Exception as e:
                    self._add_warning(f"⚠ Could not analyze {py_file.name}: {e}")
        else:
            self._add_error("✗ No Python files found")
    
    def _validate_documentation(self):
        """Validate documentation compliance"""
        print("📚 Validating Documentation...")
        
        readme_file = self.plugin_path / "README.md"
        if readme_file.exists():
            self._add_success("✓ README.md exists")
            
            try:
                with open(readme_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for required sections (flexible matching with emojis)
                required_sections = ['Features', 'Configuration', 'Installation', 'API']
                for section in required_sections:
                    # More flexible matching to handle emojis and variations
                    pattern = rf"##?\s*[🚀⚙️📋🌐🔧]*\s*{section}"
                    if re.search(pattern, content, re.IGNORECASE):
                        self._add_success(f"✓ README contains {section} section")
                    else:
                        self._add_warning(f"⚠ README missing {section} section")
                        
            except Exception as e:
                self._add_error(f"✗ Error reading README.md: {e}")
        else:
            self._add_error("✗ README.md not found")
        
        # Check for additional documentation
        doc_files = ["API.md", "CONFIGURATION.md", "TROUBLESHOOTING.md"]
        for doc_file in doc_files:
            doc_path = self.plugin_path / doc_file
            if doc_path.exists():
                self._add_success(f"✓ {doc_file} documentation exists")
            else:
                self._add_warning(f"⚠ {doc_file} documentation missing")
    
    def _validate_testing(self):
        """Validate testing implementation"""
        print("🧪 Validating Testing...")
        
        tests_dir = self.plugin_path / "tests"
        if tests_dir.exists():
            self._add_success("✓ Tests directory exists")
            
            test_files = list(tests_dir.glob("test_*.py"))
            if test_files:
                self._add_success(f"✓ Found {len(test_files)} test files")
                
                for test_file in test_files:
                    try:
                        with open(test_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Check for unittest or pytest usage
                        if 'import unittest' in content or 'import pytest' in content:
                            self._add_success(f"✓ Test framework used: {test_file.name}")
                        else:
                            self._add_warning(f"⚠ No test framework detected: {test_file.name}")
                        
                        # Check for test methods
                        if 'def test_' in content:
                            self._add_success(f"✓ Test methods found: {test_file.name}")
                        else:
                            self._add_warning(f"⚠ No test methods found: {test_file.name}")
                            
                    except Exception as e:
                        self._add_error(f"✗ Error reading {test_file.name}: {e}")
            else:
                self._add_warning("⚠ No test files found in tests directory")
        else:
            self._add_warning("⚠ No tests directory found")
    
    def _add_success(self, message: str):
        """Add success message"""
        self.validation_results.append(('SUCCESS', message))
        print(f"  {message}")
    
    def _add_warning(self, message: str):
        """Add warning message"""
        self.validation_results.append(('WARNING', message))
        self.warnings.append(message)
        print(f"  {message}")
    
    def _add_error(self, message: str):
        """Add error message"""
        self.validation_results.append(('ERROR', message))
        self.errors.append(message)
        print(f"  {message}")
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate validation report"""
        successes = [r for r in self.validation_results if r[0] == 'SUCCESS']
        warnings = [r for r in self.validation_results if r[0] == 'WARNING']
        errors = [r for r in self.validation_results if r[0] == 'ERROR']
        
        print("\n" + "=" * 80)
        print("📊 VALIDATION SUMMARY")
        print("=" * 80)
        print(f"✅ Successes: {len(successes)}")
        print(f"⚠️  Warnings:  {len(warnings)}")
        print(f"❌ Errors:    {len(errors)}")
        
        # Calculate compliance score
        total_checks = len(self.validation_results)
        success_score = len(successes) / total_checks * 100 if total_checks > 0 else 0
        
        print(f"\n🎯 Compliance Score: {success_score:.1f}%")
        
        if errors:
            print(f"\n❌ CRITICAL ISSUES ({len(errors)}):")
            for error in errors:
                print(f"  • {error[1]}")
        
        if warnings:
            print(f"\n⚠️  RECOMMENDATIONS ({len(warnings)}):")
            for warning in warnings:
                print(f"  • {warning[1]}")
        
        # Overall assessment
        if not errors and len(warnings) <= 5:
            print(f"\n🎉 EXCELLENT! Plugin meets all critical standards with minimal recommendations.")
        elif not errors:
            print(f"\n✅ GOOD! Plugin meets all critical standards. Consider addressing recommendations.")
        elif len(errors) <= 3:
            print(f"\n⚠️  NEEDS IMPROVEMENT! Address critical issues before deployment.")
        else:
            print(f"\n❌ MAJOR ISSUES! Significant work needed to meet standards.")
        
        return {
            'plugin_name': self.plugin_name,
            'total_checks': total_checks,
            'successes': len(successes),
            'warnings': len(warnings),
            'errors': len(errors),
            'compliance_score': success_score,
            'results': self.validation_results
        }

def main():
    """Main validation function"""
    plugin_path = os.path.dirname(os.path.abspath(__file__))
    validator = StandardsValidator(plugin_path)
    
    try:
        report = validator.validate_all()
        
        # Save report to file
        report_file = os.path.join(plugin_path, 'standards_compliance_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Exit with appropriate code
        if report['errors'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        print(f"❌ Validation failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
