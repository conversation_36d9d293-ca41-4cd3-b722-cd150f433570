# Widget Development Quick Reference

## 🚀 Quick Start Checklist

### Phase 1: Planning (5-10 minutes)
- [ ] Define widget purpose and data requirements
- [ ] Choose widget ID: `[plugin-name]-[widget-name]`
- [ ] Determine position: `header`, `main`, `sidebar`, `footer`
- [ ] Set size: `small`, `medium`, `large`, `full`
- [ ] Decide refresh interval (seconds or `null`)

### Phase 2: Backend (15-30 minutes)
- [ ] Update `plugins/[plugin-name]/plugin.py`
- [ ] Add `DashboardWidget` import
- [ ] Implement `get_dashboard_widgets()` method
- [ ] Implement `get_widget_data()` method
- [ ] Add API route handler

### Phase 3: Frontend (20-45 minutes)
- [ ] Create `static/js/widgets/[widget-id].js`
- [ ] Extend `window.DashboardWidget` class
- [ ] Implement `loadData()`, `render()` methods
- [ ] Register widget: `window.DashboardWidgets['widget-id'] = WidgetClass`

### Phase 4: Testing (10-20 minutes)
- [ ] Test widget loads without errors
- [ ] Test data displays correctly
- [ ] Test plugin enable/disable behavior
- [ ] Test on different screen sizes

---

## ⚡ Code Templates

### Backend Template
```python
# In plugin.py
from core.plugin_manager import DashboardWidget

def get_dashboard_widgets(self) -> list:
    widgets = []
    widget = DashboardWidget(
        widget_id="my-widget",
        title="My Widget",
        position="main",
        order=50,
        size="medium"
    )
    widget.data_endpoint = f"/api/{self.name}/widget/my-widget"
    widget.refresh_interval = 300
    widgets.append(widget)
    return widgets

def get_widget_data(self, widget_id: str) -> dict:
    if widget_id == "my-widget":
        return {"value": 42, "status": "ok"}
    return {"error": "Unknown widget"}

# Add route
@self.blueprint.route('/widget/my-widget')
def widget_my_widget():
    data = self.get_widget_data("my-widget")
    return jsonify({"success": True, "data": data})
```

### Frontend Template
```javascript
// static/js/widgets/my-widget.js
class MyWidget extends window.DashboardWidget {
    async loadData() {
        try {
            const response = await fetch(this.config.data_endpoint);
            const result = await response.json();
            if (result.success) {
                this.data = result.data;
                this.lastRefresh = new Date();
            }
        } catch (error) {
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container || !this.data) return;
        this.container.innerHTML = `
            <div class="bg-white shadow rounded-lg p-5">
                <h3 class="text-lg font-medium">${this.config.title}</h3>
                <p class="text-2xl font-bold">${this.data.value}</p>
            </div>
        `;
    }
}

window.DashboardWidgets['my-widget'] = MyWidget;
```

---

## 🎨 Common Widget Patterns

### Statistics Card
```html
<div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
        <div class="flex items-center">
            <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <i class="fas fa-chart-bar text-white text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Metric Name
                    </dt>
                    <dd class="text-3xl font-semibold text-gray-900">
                        ${value}
                    </dd>
                </dl>
            </div>
        </div>
    </div>
</div>
```

### Data Table
```html
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Header
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Data
                </td>
            </tr>
        </tbody>
    </table>
</div>
```

### Status Badge
```html
<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor()}">
    ${status}
</span>

<!-- JavaScript for status colors -->
getStatusColor() {
    switch (this.data.status) {
        case 'success': return 'bg-green-100 text-green-800';
        case 'warning': return 'bg-yellow-100 text-yellow-800';
        case 'error': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}
```

---

## 🔧 Widget Configuration Options

| Property | Type | Description | Example |
|----------|------|-------------|---------|
| `widget_id` | string | Unique identifier | `"steam-cooldowns"` |
| `title` | string | Display title | `"Steam Session Cooldowns"` |
| `position` | string | Layout position | `"main"`, `"sidebar"` |
| `order` | number | Display order (lower = first) | `30` |
| `size` | string | Widget size | `"medium"`, `"large"` |
| `data_endpoint` | string | API endpoint | `"/api/steam/widget/cooldowns"` |
| `refresh_interval` | number/null | Auto-refresh seconds | `300` or `null` |

---

## 🐛 Troubleshooting

### Widget Not Appearing
1. Check plugin is enabled: Admin Panel → Plugins
2. Check browser console for JavaScript errors
3. Verify API endpoint works: Test in network tab
4. Check widget registration: `window.DashboardWidgets['widget-id']`

### Data Not Loading
1. Test API endpoint directly in browser
2. Check authentication (logged in as admin)
3. Verify data format matches expected structure
4. Check browser network tab for failed requests

### Widget Looks Broken
1. Use standard Tailwind CSS classes
2. Test on different screen sizes
3. Check for CSS conflicts in browser dev tools
4. Validate HTML structure

---

## ✅ Pre-Deployment Checklist

### Functionality
- [ ] Widget loads without JavaScript errors
- [ ] Data displays correctly
- [ ] Auto-refresh works (if enabled)
- [ ] Manual refresh works
- [ ] Plugin enable/disable works
- [ ] Error states display properly

### Design
- [ ] Follows existing design patterns
- [ ] Works on mobile/tablet/desktop
- [ ] Colors and spacing consistent
- [ ] Loading states are clear

### Code Quality
- [ ] Error handling implemented
- [ ] Logging added where appropriate
- [ ] Code follows project conventions
- [ ] No console errors or warnings

---

## 📞 Need Help?

### Documentation
- Full procedures: `standards/ADD_NEW_WIDGET_PROCEDURE.md`
- System architecture: `docs/PLUGGABLE_DASHBOARD_SYSTEM.md`

### Examples
- Core plugin: `plugins/core/plugin.py`
- Steam plugin: `plugins/steam/plugin.py`
- Widget examples: `static/js/widgets/`

### Common File Locations
```
plugins/[plugin-name]/plugin.py          # Backend implementation
static/js/widgets/[widget-id].js         # Frontend implementation
api/admin_routes.py                      # Dashboard API endpoints
core/plugin_manager.py                   # Plugin interface
templates/dashboard.html                 # Dashboard template
```

### Testing URLs
- Dashboard: `http://localhost:5005/admin/dashboard`
- Widget API: `http://localhost:5005/admin/api/dashboard/widgets`
- Plugin API: `http://localhost:5005/api/[plugin]/widget/[widget-id]` 