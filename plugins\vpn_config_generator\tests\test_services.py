"""
Tests for VPN Config Generator services.
"""

import unittest
import sys
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock

# Add plugin directory to path
plugin_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, plugin_dir)

from services import VPNConfigGeneratorService
from models import VPNConfigRequest, VPNConfigResponse, VPNAPIConfig, ConfigTemplate


class TestVPNConfigGeneratorService(unittest.TestCase):
    """Test VPNConfigGeneratorService"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.app_config = {
            'api_config': {
                'enabled': True,
                'use_vpn_plugin_api': False,
                'fallback_api_endpoint': 'https://test.api.com',
                'fallback_username': 'admin',
                'fallback_password': 'admin123',
                'timeout': 30
            },
            'generator_settings': {
                'default_server': 'server11',
                'default_days': '30',
                'default_telco': 'digi',
                'default_plan': 'unlimited',
                'auto_generate_username': True,
                'username_prefix': 'user'
            }
        }
        
        # Create mock plugin manager
        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.plugin_configs = {}
        
        self.service = VPNConfigGeneratorService(
            self.temp_dir, 
            self.app_config, 
            self.mock_plugin_manager
        )
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_service_initialization(self):
        """Test service initialization"""
        self.assertEqual(self.service.plugin_dir, self.temp_dir)
        self.assertEqual(self.service.app_config, self.app_config)
        self.assertIsNotNone(self.service._api_config)
        self.assertIsNotNone(self.service._generator_settings)
        self.assertIsInstance(self.service._templates, list)
    
    def test_load_config(self):
        """Test loading configuration"""
        # Create a test config file
        config_data = {
            'api_config': {
                'enabled': True,
                'use_vpn_plugin_api': True,
                'timeout': 60
            }
        }
        
        config_file = os.path.join(self.temp_dir, 'config.json')
        with open(config_file, 'w') as f:
            json.dump(config_data, f)
        
        # Reload service to test config loading
        service = VPNConfigGeneratorService(
            self.temp_dir, 
            self.app_config, 
            self.mock_plugin_manager
        )
        
        self.assertTrue(service._api_config.enabled)
        self.assertTrue(service._api_config.use_vpn_plugin_api)
        self.assertEqual(service._api_config.timeout, 60)
    
    def test_save_config(self):
        """Test saving configuration"""
        # Modify config
        self.service._api_config.timeout = 45
        self.service._generator_settings.default_server = 'server12'
        
        # Save config
        self.service.save_config()
        
        # Verify config file was created
        config_file = os.path.join(self.temp_dir, 'config.json')
        self.assertTrue(os.path.exists(config_file))
        
        # Verify config content
        with open(config_file, 'r') as f:
            data = json.load(f)
        
        self.assertEqual(data['api_config']['timeout'], 45)
        self.assertEqual(data['generator_settings']['default_server'], 'server12')
    
    def test_get_api_config(self):
        """Test getting API configuration"""
        config = self.service.get_api_config()
        
        self.assertIsInstance(config, VPNAPIConfig)
        self.assertTrue(config.enabled)
        self.assertEqual(config.fallback_api_endpoint, 'https://test.api.com')
    
    def test_update_api_config(self):
        """Test updating API configuration"""
        new_config = VPNAPIConfig(
            enabled=False,
            use_vpn_plugin_api=True,
            timeout=120
        )
        
        result = self.service.update_api_config(new_config)
        
        self.assertTrue(result)
        self.assertFalse(self.service._api_config.enabled)
        self.assertTrue(self.service._api_config.use_vpn_plugin_api)
        self.assertEqual(self.service._api_config.timeout, 120)
    
    def test_add_template(self):
        """Test adding a template"""
        template = ConfigTemplate(
            id="test_template",
            name="Test Template",
            description="Test description",
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited"
        )
        
        result = self.service.add_template(template)
        
        self.assertTrue(result)
        self.assertEqual(len(self.service._templates), 1)
        self.assertEqual(self.service._templates[0].id, "test_template")
    
    def test_get_template(self):
        """Test getting a template"""
        template = ConfigTemplate(
            id="test_template",
            name="Test Template",
            description="Test description",
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited"
        )
        
        self.service.add_template(template)
        retrieved_template = self.service.get_template("test_template")
        
        self.assertIsNotNone(retrieved_template)
        self.assertEqual(retrieved_template.id, "test_template")
        self.assertEqual(retrieved_template.name, "Test Template")
    
    def test_delete_template(self):
        """Test deleting a template"""
        template = ConfigTemplate(
            id="test_template",
            name="Test Template",
            description="Test description",
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited"
        )
        
        self.service.add_template(template)
        self.assertEqual(len(self.service._templates), 1)
        
        result = self.service.delete_template("test_template")
        
        self.assertTrue(result)
        self.assertEqual(len(self.service._templates), 0)
    
    @patch('requests.post')
    def test_generate_config_fallback_api_success(self, mock_post):
        """Test successful config generation using fallback API"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'test_token'
        }
        mock_post.return_value = mock_response
        
        # Mock client creation response
        mock_client_response = Mock()
        mock_client_response.status_code = 201
        mock_client_response.json.return_value = {
            'config_url': 'vmess://test-config-url'
        }
        
        with patch('requests.Session') as mock_session_class:
            mock_session = Mock()
            mock_session_class.return_value = mock_session
            mock_session.post.side_effect = [mock_response, mock_client_response]
            
            request = VPNConfigRequest(
                server="server11",
                days="30",
                telco="digi",
                plan="unlimited",
                username="testuser"
            )
            
            result = self.service.generate_config(request)
            
            self.assertTrue(result.success)
            self.assertEqual(result.config, 'vmess://test-config-url')
            self.assertIsNotNone(result.created_date)
            self.assertIsNotNone(result.expired_date)
    
    @patch('requests.post')
    def test_generate_config_fallback_api_auth_failure(self, mock_post):
        """Test config generation with authentication failure"""
        # Mock failed authentication
        mock_response = Mock()
        mock_response.status_code = 401
        mock_post.return_value = mock_response
        
        request = VPNConfigRequest(
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited",
            username="testuser"
        )
        
        result = self.service.generate_config(request)
        
        self.assertFalse(result.success)
        self.assertIn("Authentication failed", result.error)
    
    def test_parse_server_id(self):
        """Test server ID parsing"""
        # Test valid server formats
        self.assertEqual(self.service._parse_server_id("server11"), 11)
        self.assertEqual(self.service._parse_server_id("server1"), 1)
        self.assertEqual(self.service._parse_server_id("server123"), 123)
        
        # Test invalid server formats
        self.assertIsNone(self.service._parse_server_id("invalid"))
        self.assertIsNone(self.service._parse_server_id("server"))
        self.assertIsNone(self.service._parse_server_id("11"))


    def test_test_connection_vpn_plugin_not_available(self):
        """Test connection test when VPN plugin is not available"""
        # Ensure VPN plugin API is not available
        self.service._vpn_api_service = None
        self.service._api_config.use_vpn_plugin_api = True

        result = self.service.test_connection()

        self.assertFalse(result['success'])
        self.assertIn('not available', result['message'])

    @patch('requests.post')
    def test_test_connection_fallback_api_success(self, mock_post):
        """Test successful connection test using fallback API"""
        # Configure to use fallback API
        self.service._api_config.use_vpn_plugin_api = False
        self.service._api_config.fallback_api_endpoint = 'https://test.api.com'

        # Mock successful authentication
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'test_token'
        }
        mock_post.return_value = mock_response

        result = self.service.test_connection()

        self.assertTrue(result['success'])
        self.assertIn('successful', result['message'])
        self.assertEqual(result['api_url'], 'https://test.api.com')

    @patch('requests.post')
    def test_test_connection_fallback_api_failure(self, mock_post):
        """Test failed connection test using fallback API"""
        # Configure to use fallback API
        self.service._api_config.use_vpn_plugin_api = False
        self.service._api_config.fallback_api_endpoint = 'https://test.api.com'

        # Mock failed authentication
        mock_response = Mock()
        mock_response.status_code = 401
        mock_post.return_value = mock_response

        result = self.service.test_connection()

        self.assertFalse(result['success'])
        self.assertIn('Authentication failed', result['message'])


if __name__ == '__main__':
    unittest.main()
