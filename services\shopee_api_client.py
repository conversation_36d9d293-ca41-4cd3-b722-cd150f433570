"""
Shopee API Client Service
Handles communication with the centralized Shopee API service.
"""
import requests
import json
from typing import Dict, Any, Optional, Tuple
import config
import logging
from datetime import datetime, timedelta
from urllib3.exceptions import MaxRetryError, NewConnectionError
from requests.exceptions import ConnectionError, ConnectTimeout

logger = logging.getLogger(__name__)

# Rate limiting for connection error logs
_connection_error_cache = {}
_CONNECTION_ERROR_COOLDOWN = 600  # 10 minutes

class ShopeeAPIClient:
    """Client for communicating with the centralized Shopee API service."""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the Shopee API client.

        Args:
            base_url: Base URL of the centralized Shopee API service
        """
        if base_url is None:
            # Try to get base URL from multiple sources in order of priority:
            # 1. Chat Commands Plugin configuration
            # 2. Global config.py setting
            # 3. Default fallback
            base_url = self._get_base_url_from_config()
        self.base_url = base_url.rstrip('/')
        self.timeout = getattr(config, 'REQUEST_TIMEOUT', 60)
        self._service_available = None  # Cache service availability
        self._last_availability_check = None

    def _should_log_connection_error(self, url: str, error_message: str) -> bool:
        """Rate limit connection error logs to prevent spam"""
        cache_key = f"{url}:{hash(error_message)}"
        now = datetime.now()
        
        if cache_key in _connection_error_cache:
            last_logged = _connection_error_cache[cache_key]
            if now - last_logged < timedelta(seconds=_CONNECTION_ERROR_COOLDOWN):
                return False
        
        _connection_error_cache[cache_key] = now
        return True

    def _is_service_available(self) -> bool:
        """Check if the service is available with caching"""
        now = datetime.now()
        
        # Check cache validity (5 minutes)
        if (self._last_availability_check and 
            now - self._last_availability_check < timedelta(minutes=5) and
            self._service_available is not None):
            return self._service_available
        
        # Quick health check
        try:
            import urllib.parse
            parsed_url = urllib.parse.urlparse(self.base_url)
            host = parsed_url.hostname
            port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
            
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)  # 3 second timeout
            result = sock.connect_ex((host, port))
            sock.close()
            
            self._service_available = result == 0
            self._last_availability_check = now
            return self._service_available
            
        except Exception:
            self._service_available = False
            self._last_availability_check = now
            return False

    def _get_base_url_from_config(self) -> str:
        """
        Get base URL from configuration sources in order of priority.

        Returns:
            Base URL for the Shopee API service
        """
        import os
        import json

        # 1. Try to get from Chat Commands Plugin configuration
        try:
            plugin_config_path = os.path.join('plugins', 'chat_commands', 'config.json')
            if os.path.exists(plugin_config_path):
                with open(plugin_config_path, 'r', encoding='utf-8') as f:
                    plugin_config = json.load(f)
                    webhook_config = plugin_config.get('webhook_config', {})
                    shopee_api_url = webhook_config.get('shopee_api_base_url', '').strip()
                    if shopee_api_url:
                        logger.info(f"Using Shopee API URL from Chat Commands Plugin: {shopee_api_url}")
                        return shopee_api_url
        except Exception as e:
            logger.debug(f"Could not load Chat Commands Plugin config: {e}")

        # 2. Try to get from global config.py
        base_url = getattr(config, 'SHOPEE_API_BASE_URL', '').strip()
        if base_url:
            logger.info(f"Using Shopee API URL from global config: {base_url}")
            return base_url

        # 3. Default fallback
        default_url = "https://shop.api.limjianhui.com"
        logger.info(f"Using default Shopee API URL: {default_url}")
        return default_url

    def update_base_url(self, new_base_url: str) -> None:
        """
        Update the base URL for the Shopee API client.

        Args:
            new_base_url: New base URL to use
        """
        self.base_url = new_base_url.rstrip('/')
        logger.info(f"Updated Shopee API base URL to: {self.base_url}")

    def get_current_base_url(self) -> str:
        """
        Get the current base URL being used.

        Returns:
            Current base URL
        """
        return self.base_url

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Tuple[Dict[str, Any], int]:
        """
        Make a request to the Shopee API service.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments for requests
            
        Returns:
            Tuple of (response_data, status_code)
        """
        url = f"{self.base_url}{endpoint}"
        
        # Check service availability first
        if not self._is_service_available():
            error_msg = f"Service unavailable at {self.base_url} - skipping request to {endpoint}"
            if self._should_log_connection_error(url, "service_unavailable"):
                logger.warning(error_msg)
            return {"error": "Service unavailable", "detail": f"Cannot connect to {self.base_url}"}, 503
        
        try:
            response = requests.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # Try to parse JSON response
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = {"error": "Invalid JSON response", "raw_response": response.text}
            
            return data, response.status_code
            
        except (ConnectionError, ConnectTimeout, MaxRetryError, NewConnectionError) as e:
            # Mark service as unavailable
            self._service_available = False
            self._last_availability_check = datetime.now()
            
            error_msg = f"Connection failed to {url}: {str(e)}"
            if self._should_log_connection_error(url, str(e)):
                logger.error(error_msg)
            else:
                logger.debug(f"[Rate Limited] {error_msg}")
            
            return {"error": f"Connection failed: {str(e)}"}, 500
            
        except requests.RequestException as e:
            error_msg = f"Request failed to {url}: {str(e)}"
            if self._should_log_connection_error(url, str(e)):
                logger.error(error_msg)
            else:
                logger.debug(f"[Rate Limited] {error_msg}")
            
            return {"error": f"Request failed: {str(e)}"}, 500
    
    def get_order_status(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get the status of an order.
        
        Args:
            order_sn: Order number
            
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', f'/orders/{order_sn}/status')
    
    def get_order_details(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get detailed information for an order.
        
        Args:
            order_sn: Order number
            
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', f'/orders/{order_sn}/details')
    
    def get_order_details_direct(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get detailed information for an order using direct API call.
        
        Args:
            order_sn: Order number
            
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', f'/orders/{order_sn}/details/direct')
    
    def search_order(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Search for an order by order number.
        
        Args:
            order_sn: Order number to search for
            
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/orders/search', params={'order_sn': order_sn})
    
    def get_to_ship_orders(self) -> Tuple[Dict[str, Any], int]:
        """
        Get orders with 'To Ship' status.
        
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/orders/to_ship')
    
    def get_shipped_orders(self) -> Tuple[Dict[str, Any], int]:
        """
        Get orders with 'Shipped' status.
        
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/orders/shipped')
    
    def get_completed_orders(self) -> Tuple[Dict[str, Any], int]:
        """
        Get orders with 'Completed' status.
        
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/orders/completed')
    
    def ship_order(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Ship an order.
        
        Args:
            order_sn: Order number to ship
            
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('POST', f'/orders/{order_sn}/ship')
    
    def get_buyer_orders(self, buyer_id: int, order_type: str = "all", 
                        page: int = 1, per_page: int = 20) -> Tuple[Dict[str, Any], int]:
        """
        Get orders from a specific buyer.
        
        Args:
            buyer_id: Buyer ID
            order_type: Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)
            page: Page number
            per_page: Number of orders per page
            
        Returns:
            Tuple of (response_data, status_code)
        """
        params = {
            'order_type': order_type,
            'page': page,
            'per_page': per_page
        }
        return self._make_request('GET', f'/buyers/{buyer_id}/orders', params=params)
    
    def get_buyer_orders_by_username(self, username: str, order_type: str = "all",
                                   page: int = 1, per_page: int = 20) -> Tuple[Dict[str, Any], int]:
        """
        Get orders from a specific buyer by username.
        
        Args:
            username: Buyer username
            order_type: Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)
            page: Page number
            per_page: Number of orders per page
            
        Returns:
            Tuple of (response_data, status_code)
        """
        params = {
            'order_type': order_type,
            'page': page,
            'per_page': per_page
        }
        return self._make_request('GET', f'/buyers/username/{username}/orders', params=params)
    
    def check_api_status(self) -> Tuple[Dict[str, Any], int]:
        """
        Check the status of the Shopee API service.
        
        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/status')
    
    def check_auth_status(self) -> Tuple[Dict[str, Any], int]:
        """
        Check authentication status and credentials.

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/auth/check')

    # Chat/Messaging endpoints
    def send_chat_message(self, payload: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
        """
        Send a text message to a user.

        Args:
            payload: Dictionary containing message details
                - text: Message text
                - username: Username to send to
                - force_send_cancel_order_warning (optional): Force send despite cancel warning
                - comply_cancel_order_warning (optional): Comply with cancel warning

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('POST', '/chat/send_message', json=payload)

    def send_image_message(self, payload: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
        """
        Send an image message to a user.

        Args:
            payload: Dictionary containing message details
                - username: Username to send to
                - image_url: URL of the image to send

        Returns:
            Tuple of (response_data, status_code)
        """
        # Create a copy of the payload to avoid modifying the original
        request_payload = payload.copy()

        # Compatibility layer: ensure both 'image_url' and 'url' fields are present
        # This handles different API versions and implementations
        if 'image_url' in request_payload and 'url' not in request_payload:
            request_payload['url'] = request_payload['image_url']
        elif 'url' in request_payload and 'image_url' not in request_payload:
            request_payload['image_url'] = request_payload['url']

        # Debug logging
        logger.debug(f"Sending image message payload: {request_payload}")

        return self._make_request('POST', '/chat/send_image', json=request_payload)

    def send_order_message(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Send an order message.

        Args:
            order_sn: Order serial number

        Returns:
            Tuple of (response_data, status_code)
        """
        payload = {
            "order_sn": order_sn
        }
        return self._make_request('POST', '/chat/send_order_message', json=payload)

    def send_order_card(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Send an order card.

        Args:
            order_sn: Order serial number

        Returns:
            Tuple of (response_data, status_code)
        """
        payload = {
            "order_sn": order_sn
        }
        return self._make_request('POST', '/chat/send_order_card', json=payload)

    def get_recent_conversations(self, unread_only: bool = False) -> Tuple[Dict[str, Any], int]:
        """
        Get recent conversations.

        Args:
            unread_only: Whether to only retrieve unread conversations

        Returns:
            Tuple of (response_data, status_code)
        """
        params = {"unread_only": unread_only}
        return self._make_request('GET', '/chat/conversations/recent', params=params)

    def get_recent_latest_messages(self) -> Tuple[Dict[str, Any], int]:
        """
        Get the latest message from each recent conversation.

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/chat/conversations/recent_messages')

    def get_conversation_messages(self, conversation_id: str, offset: int = 0,
                                limit: int = 20, direction: str = "older") -> Tuple[Dict[str, Any], int]:
        """
        Get messages from a specific conversation.

        Args:
            conversation_id: ID of the conversation
            offset: Number of messages to skip
            limit: Maximum number of messages to return
            direction: Direction to retrieve messages ('older' or 'newer')

        Returns:
            Tuple of (response_data, status_code)
        """
        params = {
            "offset": offset,
            "limit": limit,
            "direction": direction
        }
        return self._make_request('GET', f'/chat/conversations/{conversation_id}/messages', params=params)

    def search_conversation_by_username(self, username: str, raw: bool = False) -> Tuple[Dict[str, Any], int]:
        """
        Search for a conversation by username.

        Args:
            username: Username to search for
            raw: If true, returns the complete raw search response

        Returns:
            Tuple of (response_data, status_code)
        """
        params = {
            "username": username,
            "raw": raw
        }
        return self._make_request('GET', '/chat/search_conversation', params=params)

    def get_conversation_messages_by_username(self, username: str, offset: int = 0,
                                            limit: int = 20, direction: str = "older",
                                            force_refresh: bool = False) -> Tuple[Dict[str, Any], int]:
        """
        Get messages from a conversation with a specific user by username.

        Args:
            username: Username of the user
            offset: Number of messages to skip
            limit: Maximum number of messages to return
            direction: Direction to retrieve messages
            force_refresh: Force refresh from Shopee API instead of using cache

        Returns:
            Tuple of (response_data, status_code)
        """
        params = {
            "offset": offset,
            "limit": limit,
            "direction": direction,
            "force_refresh": force_refresh
        }
        return self._make_request('GET', f'/chat/conversations/username/{username}/messages', params=params)

    def set_conversation_unread(self, conversation_id: str) -> Tuple[Dict[str, Any], int]:
        """
        Mark a conversation as unread.

        Args:
            conversation_id: ID of the conversation to mark as unread

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('PUT', f'/chat/conversations/{conversation_id}/unread')

    def set_conversation_unread_by_username(self, username: str) -> Tuple[Dict[str, Any], int]:
        """
        Mark a conversation as unread by username.

        Args:
            username: Username of the conversation to mark as unread

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('PUT', f'/chat/conversations/username/{username}/unread')

    def clear_cache(self) -> Tuple[Dict[str, Any], int]:
        """
        Clear all caches.

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('POST', '/cache/clear')

    def get_chat_login_info(self) -> Tuple[Dict[str, Any], int]:
        """
        Get chat login information from Shopee API.

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/chat/login_info')

    def get_websocket_status(self) -> Tuple[Dict[str, Any], int]:
        """
        Check the status of the WebSocket connection to Shopee.

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('GET', '/websocket/status')

    def websocket_reconnect(self) -> Tuple[Dict[str, Any], int]:
        """
        Force reconnect the WebSocket connection to Shopee.

        Returns:
            Tuple of (response_data, status_code)
        """
        return self._make_request('POST', '/websocket/reconnect')


# Global instance for use throughout the application
shopee_client = ShopeeAPIClient()
