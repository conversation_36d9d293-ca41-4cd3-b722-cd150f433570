// No auto-update when opening the extension
function checkAllDataCaptured() {
    chrome.storage.local.get(['bearerToken', 'fullCookieText', 'cookieJsonArray'], (result) => {
        const token = result.bearerToken;
        const cookies = result.fullCookieText;
        if (token && cookies) {
            document.getElementById('sendData').textContent = 'Update Credentials';
            // No auto-send, user must click the button
        }
    });
}

chrome.storage.local.get(['bearerToken'], (result) => {
    if (chrome.runtime.lastError) {
        console.error('Error fetching bearerToken:', chrome.runtime.lastError);
        document.getElementById('token').textContent = 'Failed to retrieve';
    } else {
        document.getElementById('token').textContent = result.bearerToken || 'Bearer Token not found';
    }
    enableSendButtonIfPossible();
});

chrome.storage.local.get(['fullCookieText'], (result) => {
    if (chrome.runtime.lastError) {
        console.error('Error fetching fullCookieText:', chrome.runtime.lastError);
        document.getElementById('cookies').textContent = 'Failed to retrieve';
    } else {
        document.getElementById('cookies').textContent = result.fullCookieText || 'Cookies not found';
    }
    enableSendButtonIfPossible();
});

chrome.storage.local.get(['cookieJsonArray'], (result) => {
    if (chrome.runtime.lastError) {
        console.error('Error fetching cookieJsonArray:', chrome.runtime.lastError);
        document.getElementById('cookiesJson').textContent = 'Failed to retrieve';
    } else {
        try {
            if (result.cookieJsonArray) {
                const jsonArray = JSON.parse(result.cookieJsonArray);
                updateJsonDisplay(jsonArray);
            } else {
                document.getElementById('cookiesJson').textContent = 'JSON Cookies not found';
            }
        } catch (error) {
            console.error('Error parsing JSON cookies:', error);
            document.getElementById('cookiesJson').textContent = 'Error parsing JSON cookies';
        }
    }
    enableSendButtonIfPossible();
});

function updateJsonDisplay(jsonData) {
    const formatType = document.querySelector('input[name="jsonFormat"]:checked').value;
    let formattedJson;

    if (formatType === 'array') {
        // Array format (default)
        formattedJson = JSON.stringify(jsonData, null, 2);
    } else {
        // Object format (name-value pairs)
        const objFormat = {};
        jsonData.forEach(cookie => {
            objFormat[cookie.name] = {
                value: cookie.value,
                domain: cookie.domain,
                path: cookie.path,
                expirationDate: cookie.expirationDate,
                secure: cookie.secure,
                httpOnly: cookie.httpOnly
            };
        });
        formattedJson = JSON.stringify(objFormat, null, 2);
    }

    document.getElementById('cookiesJson').textContent = formattedJson;
}

function saveConnectionSettings() {
    const protocol = document.getElementById('protocolSelect').value;
    const domain = document.getElementById('domainInput').value.trim();
    const port = document.getElementById('portInput').value.trim();
    const path = document.getElementById('pathInput').value.trim();

    if (!domain) {
        alert('Please enter a valid domain.');
        return;
    }

    const settings = {
        protocol: protocol,
        domain: domain,
        port: port,
        path: path
    };

    chrome.storage.local.set({ connectionSettings: settings }, () => {
        alert('Connection settings saved successfully!');
    });
}

function loadSavedConnectionSettings() {
    chrome.storage.local.get(['connectionSettings'], (result) => {
        if (result.connectionSettings) {
            const settings = result.connectionSettings;

            // Set protocol
            if (settings.protocol) {
                document.getElementById('protocolSelect').value = settings.protocol;
            }

            // Set domain
            if (settings.domain) {
                document.getElementById('domainInput').value = settings.domain;
            }

            // Set port
            if (settings.port) {
                document.getElementById('portInput').value = settings.port;
            }

            // Set path
            if (settings.path) {
                document.getElementById('pathInput').value = settings.path;
            }
        } else {
            // Default values if no settings are saved
            document.getElementById('protocolSelect').value = 'https';
            document.getElementById('domainInput').value = '127.0.0.1';
            document.getElementById('portInput').value = '8000';
            document.getElementById('pathInput').value = 'admin/update_shopee_credentials';
        }
    });
}

function enableSendButtonIfPossible() {
    const token = document.getElementById('token').textContent;
    const cookies = document.getElementById('cookies').textContent;
    const cookiesJson = document.getElementById('cookiesJson').textContent;
    const sendButton = document.getElementById('sendData');

    if (token && token !== 'Loading...' &&
        ((cookies && cookies !== 'Loading...') ||
         (cookiesJson && cookiesJson !== 'Loading...'))) {
        sendButton.disabled = false;
    }
}

function sendDataToServer() {
    const token = document.getElementById('token').textContent;
    const cookieFormat = document.querySelector('input[name="cookieFormat"]:checked').value;
    let cookieData;

    if (cookieFormat === 'string') {
        // Use string format
        cookieData = document.getElementById('cookies').textContent;
    } else {
        // Use JSON format
        try {
            cookieData = JSON.parse(document.getElementById('cookiesJson').textContent);
        } catch (error) {
            alert('Error parsing JSON cookies. Please try again.');
            console.error('Error parsing JSON cookies:', error);
            return;
        }
    }

    chrome.storage.local.get(['connectionSettings'], (result) => {
        if (!result.connectionSettings) {
            alert('Please save connection settings first.');
            return;
        }

        const settings = result.connectionSettings;
        const protocol = settings.protocol || 'https';
        const domain = settings.domain || '127.0.0.1';
        const port = settings.port ? `:${settings.port}` : '';
        const path = settings.path || 'admin/update_shopee_credentials';

        // Construct URL from settings
        const url = `${protocol}://${domain}${port}/${path.replace(/^\//, '')}`;

        console.log(`Sending data to: ${url}`);

        // Prepare payload based on selected structure
        let data;
        const payloadStructure = document.getElementById('payloadStructure').value;

        if (payloadStructure === 'default') {
            // Default structure (original)
            data = {
                bearerToken: token,
                cookies: cookieData
            };
        } else if (payloadStructure === 'shopeeapi') {
            // ShopeeAPI structure
            data = {
                authorization_code: token,
                cookie: cookieData
            };
        } else if (payloadStructure === 'custom') {
            // Custom structure from textarea
            try {
                const customTemplate = document.getElementById('customPayload').value;
                // Replace placeholders with actual values
                const jsonStr = customTemplate
                    .replace(/{{token}}/g, JSON.stringify(token))
                    .replace(/{{cookies}}/g, JSON.stringify(cookieData));
                data = JSON.parse(jsonStr);
            } catch (error) {
                alert('Error in custom payload template. Please check your JSON syntax.');
                console.error('Error parsing custom payload:', error);
                return;
            }
        }

        // Show sending status
        const sendButton = document.getElementById('sendData');
        const originalText = sendButton.textContent;
        sendButton.textContent = 'Sending...';
        sendButton.disabled = true;

        // Log the payload for debugging
        console.log('Sending payload:', data);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Server responded with status: ' + response.status);
            }
        })
        .then(data => {
            alert(data.message);
            sendButton.textContent = 'Sent Successfully';
        })
        .catch(error => {
            console.error('Error sending data:', error);
            alert(`Error: ${error.message}\nCheck console for details.`);
            sendButton.textContent = originalText;
            sendButton.disabled = false;
        });
    });
}

// Function to handle payload structure changes
function handlePayloadStructureChange() {
    const payloadStructure = document.getElementById('payloadStructure').value;
    const customPayloadContainer = document.getElementById('customPayloadContainer');

    if (payloadStructure === 'custom') {
        customPayloadContainer.style.display = 'block';

        // If custom payload is empty, initialize with a template
        const customPayload = document.getElementById('customPayload');
        if (!customPayload.value.trim()) {
            customPayload.value = `{
  "AUTHORIZATION_CODE": {{token}},
  "COOKIE_JSON": {{cookies}}
}`;
        }
    } else {
        customPayloadContainer.style.display = 'none';
    }
}

// Function to save payload settings
function savePayloadSettings() {
    const payloadStructure = document.getElementById('payloadStructure').value;
    const customPayload = document.getElementById('customPayload').value;

    chrome.storage.local.set({
        payloadSettings: {
            structure: payloadStructure,
            customTemplate: customPayload
        }
    });
}

// Function to load payload settings
function loadPayloadSettings() {
    chrome.storage.local.get(['payloadSettings'], (result) => {
        if (result.payloadSettings) {
            const settings = result.payloadSettings;

            // Set payload structure
            if (settings.structure) {
                document.getElementById('payloadStructure').value = settings.structure;
            }

            // Set custom payload template
            if (settings.customTemplate) {
                document.getElementById('customPayload').value = settings.customTemplate;
            }

            // Update UI based on loaded settings
            handlePayloadStructureChange();
        }
    });
}

function refreshCookies() {
    console.log("Manually refreshing cookies...");

    // Show loading state
    document.getElementById('cookies').textContent = 'Refreshing...';
    document.getElementById('cookiesJson').textContent = 'Refreshing...';

    // Send message to background script to refresh cookies
    chrome.runtime.sendMessage({ action: 'refreshCookies' }, (response) => {
        if (response && response.success) {
            console.log("Cookies refreshed successfully");
            // Reload the cookie data
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            console.error("Failed to refresh cookies");
            alert("Failed to refresh cookies. Please try again.");
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    checkAllDataCaptured();
    loadSavedConnectionSettings();
    loadPayloadSettings();

    // Copy button event listeners
    document.querySelectorAll('.copy-icon').forEach(icon => {
        icon.addEventListener('click', function() {
            const elementId = this.getAttribute('data-target');
            copyToClipboard(elementId);
        });
    });

    // Connection settings save button
    document.getElementById('saveSettings').addEventListener('click', function() {
        saveConnectionSettings();
        savePayloadSettings();
    });

    // Payload structure change handler
    document.getElementById('payloadStructure').addEventListener('change', function() {
        handlePayloadStructureChange();
        savePayloadSettings();
    });

    // Custom payload change handler
    document.getElementById('customPayload').addEventListener('input', function() {
        savePayloadSettings();
    });

    // JSON format radio buttons
    document.querySelectorAll('input[name="jsonFormat"]').forEach(radio => {
        radio.addEventListener('change', function() {
            chrome.storage.local.get(['cookieJsonArray'], (result) => {
                if (result.cookieJsonArray) {
                    try {
                        const jsonArray = JSON.parse(result.cookieJsonArray);
                        updateJsonDisplay(jsonArray);
                    } catch (error) {
                        console.error('Error parsing JSON cookies:', error);
                    }
                }
            });
        });
    });

    // Send button
    document.getElementById('sendData').addEventListener('click', function() {
        sendDataToServer();
    });

    // Refresh cookies button
    document.getElementById('refreshCookies').addEventListener('click', function() {
        refreshCookies();
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;

    const tempTextArea = document.createElement('textarea');
    tempTextArea.value = text;
    document.body.appendChild(tempTextArea);

    tempTextArea.select();
    document.execCommand('copy');

    document.body.removeChild(tempTextArea);

    element.style.backgroundColor = '#4CAF50';
    setTimeout(() => {
        element.style.backgroundColor = '';
    }, 200);

    const message = document.createElement('div');
    message.textContent = 'Copied!';
    message.style.position = 'fixed';
    message.style.top = '10px';
    message.style.left = '50%';
    message.style.transform = 'translateX(-50%)';
    message.style.backgroundColor = '#333';
    message.style.color = '#fff';
    message.style.padding = '10px';
    message.style.borderRadius = '5px';
    message.style.zIndex = '1000';
    document.body.appendChild(message);

    setTimeout(() => {
        document.body.removeChild(message);
    }, 2000);
}