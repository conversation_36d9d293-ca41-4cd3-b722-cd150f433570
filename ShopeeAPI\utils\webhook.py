"""
Webhook utilities for the Shopee API.

This module provides functions for sending webhook notifications.
"""
import asyncio
import json
import logging
import time
from typing import Dict, Any
from datetime import datetime
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# Set this module's logger to ERROR level to reduce spam (change to DEBUG for more detailed logs)
logger.setLevel(logging.ERROR)


class WebhookManager:
    """
    Manager for sending webhook notifications.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the webhook manager.

        Args:
            config: Webhook configuration dictionary
        """
        self.config = config
        self.session = None

        # Error rate limiting to prevent spam
        self.error_log_cache = {}  # url -> {last_error: str, last_log_time: datetime, count: int}

        # Get rate limiting configuration
        rate_limit_config = config.get("ERROR_RATE_LIMITING", {})
        self.rate_limiting_enabled = rate_limit_config.get("ENABLED", True)
        self.error_log_interval = rate_limit_config.get("LOG_INTERVAL_SECONDS", 600)  # 10 minutes default (increased)
        self.max_error_count_per_interval = rate_limit_config.get("MAX_ERRORS_PER_INTERVAL", 1)  # Reduced to 1 error per interval

        # Log webhook configuration on initialization
        logger.info(f"Initializing WebhookManager with config: {json.dumps(config, indent=2)}")

        # Check if webhooks are enabled - ONLY check uppercase keys to match config.json
        if not config.get("ENABLED", False):
            logger.info("Webhooks are disabled in configuration")
        else:
            logger.info("Webhooks are enabled in configuration")

            # Check message_received webhook configuration - ONLY check uppercase keys
            message_received_config = config.get("MESSAGE_RECEIVED", {})
            if message_received_config.get("ENABLED", False):
                urls = message_received_config.get('URLS', [])
                if urls:
                    enabled_urls = [url for url in urls if url.get('ENABLED', True)]
                    logger.info(f"Message received webhooks enabled, {len(enabled_urls)} URLs configured")
                    for url_config in enabled_urls:
                        logger.info(f"  - {url_config.get('NAME', 'Unnamed')}: {url_config.get('URL', 'Not set')}")
                else:
                    # Backward compatibility: check for single URL
                    url = message_received_config.get('URL', 'Not set')
                    logger.info(f"Message received webhooks enabled, URL: {url}")
            else:
                logger.info("Message received webhooks are disabled")

            # Check message_sent webhook configuration - ONLY check uppercase keys
            message_sent_config = config.get("MESSAGE_SENT", {})
            if message_sent_config.get("ENABLED", False):
                urls = message_sent_config.get('URLS', [])
                if urls:
                    enabled_urls = [url for url in urls if url.get('ENABLED', True)]
                    logger.info(f"Message sent webhooks enabled, {len(enabled_urls)} URLs configured")
                    for url_config in enabled_urls:
                        logger.info(f"  - {url_config.get('NAME', 'Unnamed')}: {url_config.get('URL', 'Not set')}")
                else:
                    # Backward compatibility: check for single URL
                    url = message_sent_config.get('URL', 'Not set')
                    logger.info(f"Message sent webhooks enabled, URL: {url}")
            else:
                logger.info("Message sent webhooks are disabled")

    async def initialize(self):
        """Initialize the aiohttp session."""
        try:
            if self.session is None or self.session.closed:
                logger.debug("Creating new aiohttp ClientSession for webhook requests")
                # Create connector with SSL disabled for localhost connections
                connector = aiohttp.TCPConnector(ssl=False)
                self.session = aiohttp.ClientSession(connector=connector)
                logger.debug("Successfully created aiohttp ClientSession")
            else:
                logger.debug("Using existing aiohttp ClientSession")
        except Exception as e:
            logger.error(f"Error initializing aiohttp ClientSession: {e}")
            import traceback
            logger.debug(f"Session initialization error details: {traceback.format_exc()}")

    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    def _should_log_error(self, url: str, error_message: str) -> bool:
        """
        Check if an error should be logged based on rate limiting rules.

        Args:
            url: The webhook URL that failed
            error_message: The error message

        Returns:
            bool: True if the error should be logged, False if it should be suppressed
        """
        # If rate limiting is disabled, always log
        if not self.rate_limiting_enabled:
            return True

        current_time = datetime.now()

        # Get or create error cache entry for this URL
        if url not in self.error_log_cache:
            self.error_log_cache[url] = {
                'last_error': '',
                'last_log_time': datetime.min,
                'count': 0
            }

        cache_entry = self.error_log_cache[url]

        # Check if this is the same error as before
        is_same_error = cache_entry['last_error'] == error_message
        time_since_last_log = (current_time - cache_entry['last_log_time']).total_seconds()

        # Reset count if enough time has passed
        if time_since_last_log > self.error_log_interval:
            cache_entry['count'] = 0

        # Log if it's a different error, or if enough time has passed, or if we haven't hit the limit
        should_log = (
            not is_same_error or
            time_since_last_log > self.error_log_interval or
            cache_entry['count'] < self.max_error_count_per_interval
        )

        if should_log:
            cache_entry['last_error'] = error_message
            cache_entry['last_log_time'] = current_time
            cache_entry['count'] += 1

        return should_log

    async def send_webhook(self, webhook_type: str, data: Dict[str, Any]) -> bool:
        """
        Send a webhook notification to all configured URLs in parallel.

        Args:
            webhook_type: Type of webhook (message_received or message_sent)
            data: Data to send in the webhook

        Returns:
            bool: True if at least one webhook was sent successfully, False otherwise
        """
        # Check if webhooks are enabled - ONLY check uppercase keys to match config.json
        if not self.config.get("ENABLED", False):
            logger.debug(f"Webhooks are disabled, not sending {webhook_type} webhook")
            return False

        # Get the webhook type config - ONLY use uppercase keys to match config.json
        webhook_config = self.config.get(webhook_type, {})

        # Check if this specific webhook type is enabled - ONLY check uppercase keys
        if not webhook_config.get("ENABLED", False):
            logger.debug(f"{webhook_type} webhooks are disabled")
            return False

        # Get webhook URLs - support both new URLS array and legacy URL field
        urls_config = webhook_config.get("URLS", [])
        if not urls_config:
            # Backward compatibility: check for single URL
            single_url = webhook_config.get("URL", "")
            if single_url:
                urls_config = [{"URL": single_url, "NAME": "Legacy URL", "ENABLED": True}]

        if not urls_config:
            logger.warning(f"No URLs configured for {webhook_type} webhook")
            return False

        # Filter enabled URLs
        enabled_urls = [url_config for url_config in urls_config if url_config.get("ENABLED", True)]
        if not enabled_urls:
            logger.debug(f"No enabled URLs for {webhook_type} webhook")
            return False

        # Get retry settings - ONLY use uppercase keys
        retry_count = webhook_config.get("RETRY_COUNT", 3)
        retry_delay = webhook_config.get("RETRY_DELAY", 5)

        # Initialize session if needed
        await self.initialize()

        # Add timestamp to data
        data["timestamp"] = time.time()

        # Create tasks for parallel webhook sending
        webhook_tasks = []
        total_urls = len(enabled_urls)

        for url_config in enabled_urls:
            url = url_config.get("URL", "")
            name = url_config.get("NAME", "Unnamed")

            if not url:
                logger.warning(f"Empty URL for webhook {name}")
                continue

            # Create a task for each webhook URL
            task = asyncio.create_task(
                self._send_webhook_to_url(webhook_type, data, url, name, retry_count, retry_delay)
            )
            webhook_tasks.append(task)

        if not webhook_tasks:
            logger.warning(f"No valid URLs to send {webhook_type} webhook")
            return False

        # Wait for all webhook tasks to complete in parallel
        try:
            results = await asyncio.gather(*webhook_tasks, return_exceptions=True)

            # Count successful sends
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Webhook task {i+1} failed with exception: {result}")
                elif result is True:
                    success_count += 1

            logger.debug(f"Successfully sent {webhook_type} webhook to {success_count}/{total_urls} URLs (parallel)")
            return success_count > 0

        except Exception as e:
            logger.error(f"Error in parallel webhook sending: {e}")
            return False

    async def _send_webhook_to_url(self, webhook_type: str, data: Dict[str, Any], url: str, name: str, retry_count: int, retry_delay: int) -> bool:
        """
        Send a webhook to a specific URL with retry logic.

        Args:
            webhook_type: Type of webhook (message_received or message_sent)
            data: Data to send in the webhook
            url: Target URL
            name: Name of the webhook endpoint
            retry_count: Number of retries
            retry_delay: Delay between retries in seconds

        Returns:
            bool: True if the webhook was sent successfully, False otherwise
        """
        for attempt in range(retry_count + 1):
            try:
                async with self.session.post(
                    url,
                    json=data,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                ) as response:
                    response_text = await response.text()

                    if response.status < 400:
                        logger.debug(f"Successfully sent {webhook_type} webhook to {name} ({url})")
                        return True
                    else:
                        logger.warning(
                            f"Failed to send {webhook_type} webhook to {name} ({url}): "
                            f"HTTP {response.status} - {response_text}"
                        )
            except aiohttp.ClientConnectorError as e:
                error_msg = f"Connection failed to {name} ({url}): {e}. Service may not be running."
                if self._should_log_error(url, str(e)):
                    logger.error(error_msg)
                else:
                    logger.debug(f"[Rate Limited] {error_msg}")
            except aiohttp.ClientResponseError as e:
                error_msg = f"HTTP error sending {webhook_type} webhook to {name} ({url}): {e}"
                if self._should_log_error(url, str(e)):
                    logger.error(error_msg)
                else:
                    logger.debug(f"[Rate Limited] {error_msg}")
            except Exception as e:
                error_msg = f"Unexpected error sending {webhook_type} webhook to {name} ({url}): {e}"
                if self._should_log_error(url, str(e)):
                    logger.error(error_msg)
                else:
                    logger.debug(f"[Rate Limited] {error_msg}")

            # If we've reached the maximum number of retries for this URL, give up
            if attempt >= retry_count:
                final_error_msg = f"Failed to send {webhook_type} webhook to {name} after {retry_count} retries"
                if self._should_log_error(url, f"max_retries_reached_{retry_count}"):
                    logger.error(final_error_msg)
                else:
                    logger.debug(f"[Rate Limited] {final_error_msg}")
                break

            # Wait before retrying
            await asyncio.sleep(retry_delay)

        return False

    async def send_message_received_webhook(self, message_data: Dict[str, Any]) -> bool:
        """
        Send a webhook notification for a received message.

        Args:
            message_data: Message data

        Returns:
            bool: True if the webhook was sent successfully, False otherwise
        """
        # Use uppercase webhook type to match config.json
        return await self.send_webhook("MESSAGE_RECEIVED", message_data)

    async def send_message_sent_webhook(self, message_data: Dict[str, Any]) -> bool:
        """
        Send a webhook notification for a sent message.

        Args:
            message_data: Message data

        Returns:
            bool: True if the webhook was sent successfully, False otherwise
        """
        # Use uppercase webhook type to match config.json
        return await self.send_webhook("MESSAGE_SENT", message_data)
