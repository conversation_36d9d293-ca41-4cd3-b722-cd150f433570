#!/usr/bin/env python3
"""
Simple Docker startup script for ShopeeAPI.
This script ensures proper Python path setup and starts the API server.
"""
import sys
import os
import pathlib

def setup_python_path():
    """Setup Python path for proper imports."""
    # Get the directory containing this script (should be /app)
    app_dir = str(pathlib.Path(__file__).parent.absolute())
    
    # Ensure app directory is first in path
    if app_dir in sys.path:
        sys.path.remove(app_dir)
    sys.path.insert(0, app_dir)
    
    # Also set PYTHONPATH environment variable
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    if app_dir not in current_pythonpath:
        os.environ['PYTHONPATH'] = f"{app_dir}:{current_pythonpath}" if current_pythonpath else app_dir
    
    print(f"✓ Python path setup complete:")
    print(f"  App dir: {app_dir}")
    print(f"  PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
    print(f"  sys.path[0]: {sys.path[0]}")

def test_imports():
    """Test that required modules can be imported."""
    try:
        import api
        print("✓ Successfully imported api module")
        return True
    except ImportError as e:
        print(f"✗ Failed to import api module: {e}")
        return False

def main():
    """Main entry point."""
    print("Starting ShopeeAPI Docker container...")
    
    # Setup Python path
    setup_python_path()
    
    # Test imports
    if not test_imports():
        print("Import test failed. Exiting...")
        sys.exit(1)
    
    # Start the server
    import uvicorn
    
    port = int(os.environ.get("PORT", 8000))
    print(f"Starting server on port {port}...")
    
    # Run the server
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        access_log=True
    )

if __name__ == "__main__":
    main()