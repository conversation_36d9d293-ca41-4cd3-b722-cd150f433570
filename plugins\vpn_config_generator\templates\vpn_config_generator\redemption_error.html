<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Configuration - Error</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        body {
            background: #f8fafc;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(239, 68, 68, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
            min-height: 100vh;
        }
        .crypto-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .crypto-button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .crypto-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
        .error-icon {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8 max-w-2xl">
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 error-icon rounded-full mb-4">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Redemption Link Error</h1>
            <p class="text-gray-600">There was an issue with your redemption link</p>
        </div>

        <div class="crypto-card p-8 text-center">
            <div class="mb-6">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-4">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h2 class="text-xl font-semibold text-red-600 mb-2">Invalid Redemption Link</h2>
                <p class="text-gray-700 mb-4">{{ error }}</p>
                
                {% if link_id %}
                <div class="bg-gray-100 rounded-lg p-3 mb-4">
                    <p class="text-sm text-gray-600">Link ID: <code class="font-mono">{{ link_id }}</code></p>
                </div>
                {% endif %}
            </div>

            <div class="space-y-4">
                <div class="text-left bg-blue-50 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-900 mb-2">Possible reasons:</h3>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• The redemption link has expired</li>
                        <li>• The link has already been used</li>
                        <li>• The link has been deactivated</li>
                        <li>• The link ID is invalid or corrupted</li>
                    </ul>
                </div>

                <div class="text-left bg-yellow-50 rounded-lg p-4">
                    <h3 class="font-semibold text-yellow-900 mb-2">What to do next:</h3>
                    <ul class="text-sm text-yellow-800 space-y-1">
                        <li>• Double-check the redemption link URL</li>
                        <li>• Contact the person who sent you this link</li>
                        <li>• Request a new redemption link if needed</li>
                        <li>• Contact support if you believe this is an error</li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/vpn-config-generator/order-config" class="crypto-button">
                    Try Order-Based Redemption
                </a>
                <button onclick="window.history.back()" class="crypto-button" style="background: linear-gradient(135deg, #6b7280, #4b5563);">
                    Go Back
                </button>
            </div>
        </div>

        <div class="mt-8 text-center">
            <p class="text-sm text-gray-500">
                Need help? Contact support with the link ID above for assistance.
            </p>
        </div>
    </div>
</body>
</html>
