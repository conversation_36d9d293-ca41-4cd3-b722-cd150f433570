"""
VPN Redemption Routes
API routes for VPN product redemption functionality
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

import logging
from flask import Blueprint, request, jsonify
from typing import Dict, Any
from ..services.vpn_redemption_service import VPNRedemptionService

logger = logging.getLogger(__name__)

def create_vpn_redemption_blueprint(redemption_service: VPNRedemptionService) -> Blueprint:
    """Create VPN redemption blueprint with routes"""
    
    bp = Blueprint('vpn_redemption', __name__)
    
    @bp.route('/redeem', methods=['POST'])
    def redeem_vpn_product():
        """
        Redeem a VPN product
        
        Expected JSON payload:
        {
            "order_details": {
                "id": "order_id",
                "customer_email": "<EMAIL>",
                "shopee_username": "username"
            },
            "product_details": {
                "var_sku": "my_highspeed_30",
                "name": "Malaysia High Speed VPN 30 Days",
                "vpn_days": 30
            }
        }
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    "success": False,
                    "message": "JSON data is required"
                }), 400
            
            order_details = data.get('order_details', {})
            product_details = data.get('product_details', {})
            
            if not order_details or not product_details:
                return jsonify({
                    "success": False,
                    "message": "Both order_details and product_details are required"
                }), 400
            
            # Perform redemption
            result = redemption_service.redeem_vpn_product(order_details, product_details)
            
            # Return appropriate status code
            status_code = 200 if result.get('success') else 400
            return jsonify(result), status_code
            
        except Exception as e:
            logger.error(f"Error in VPN redemption endpoint: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Internal server error: {str(e)}"
            }), 500
    
    @bp.route('/validate', methods=['POST'])
    def validate_order_data():
        """
        Validate order data for VPN redemption
        
        Expected JSON payload:
        {
            "order_details": {
                "id": "order_id",
                "customer_email": "<EMAIL>",
                "shopee_username": "username"
            },
            "product_details": {
                "var_sku": "my_highspeed_30",
                "name": "Malaysia High Speed VPN 30 Days"
            }
        }
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    "success": False,
                    "message": "JSON data is required"
                }), 400
            
            order_details = data.get('order_details', {})
            product_details = data.get('product_details', {})
            
            if not order_details or not product_details:
                return jsonify({
                    "success": False,
                    "message": "Both order_details and product_details are required"
                }), 400
            
            # Validate data
            result = redemption_service.validate_order_data(order_details, product_details)
            
            return jsonify({
                "success": result.get('valid', False),
                "validation_result": result
            })
            
        except Exception as e:
            logger.error(f"Error in validation endpoint: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Internal server error: {str(e)}"
            }), 500
    
    @bp.route('/strategy-info/<product_sku>', methods=['GET'])
    def get_strategy_info(product_sku: str):
        """
        Get information about the strategy for a product SKU
        """
        try:
            result = redemption_service.get_strategy_info(product_sku)
            return jsonify({
                "success": True,
                "product_sku": product_sku,
                "strategy_info": result
            })
            
        except Exception as e:
            logger.error(f"Error getting strategy info: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Error getting strategy info: {str(e)}"
            }), 500
    
    @bp.route('/supported-products', methods=['GET'])
    def get_supported_products():
        """
        Get information about supported VPN products
        """
        try:
            result = redemption_service.get_supported_products()
            return jsonify({
                "success": True,
                "supported_products": result
            })
            
        except Exception as e:
            logger.error(f"Error getting supported products: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Error getting supported products: {str(e)}"
            }), 500
    
    @bp.route('/servers', methods=['GET'])
    def get_vpn_servers():
        """
        Get available VPN servers, optionally filtered by product SKU
        
        Query parameters:
        - product_sku (optional): Filter servers by product SKU
        """
        try:
            product_sku = request.args.get('product_sku')
            result = redemption_service.get_vpn_servers(product_sku)
            
            status_code = 200 if result.get('success') else 400
            return jsonify(result), status_code
            
        except Exception as e:
            logger.error(f"Error getting VPN servers: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Error getting servers: {str(e)}"
            }), 500
    
    @bp.route('/check-email', methods=['POST'])
    def check_email_availability():
        """
        Check if an email is available for VPN creation
        
        Expected JSON payload:
        {
            "email": "<EMAIL>",
            "product_sku": "my_highspeed_30"
        }
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    "success": False,
                    "message": "JSON data is required"
                }), 400
            
            email = data.get('email')
            product_sku = data.get('product_sku')
            
            if not email or not product_sku:
                return jsonify({
                    "success": False,
                    "message": "Both email and product_sku are required"
                }), 400
            
            result = redemption_service.check_email_availability(email, product_sku)
            
            status_code = 200 if result.get('success') else 400
            return jsonify(result), status_code
            
        except Exception as e:
            logger.error(f"Error checking email availability: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Error checking email: {str(e)}"
            }), 500
    
    @bp.route('/config', methods=['GET'])
    def get_vpn_config():
        """
        Get VPN configuration for a specific order
        
        Query parameters:
        - orderid: Order ID
        - varSku: Product SKU
        """
        try:
            order_id = request.args.get('orderid')
            var_sku = request.args.get('varSku')
            
            if not order_id or not var_sku:
                return jsonify({
                    "success": False,
                    "message": "Both orderid and varSku parameters are required"
                }), 400
            
            # For now, return basic config info
            # This can be expanded to generate actual VPN configuration files
            return jsonify({
                "success": True,
                "order_id": order_id,
                "product_sku": var_sku,
                "message": "VPN configuration endpoint",
                "config_available": True,
                "download_instructions": "Configuration files will be available here"
            })
            
        except Exception as e:
            logger.error(f"Error getting VPN config: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Error getting config: {str(e)}"
            }), 500
    
    @bp.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint for VPN redemption service"""
        try:
            # Basic health check
            servers_result = redemption_service.get_vpn_servers()
            
            return jsonify({
                "success": True,
                "service": "VPN Redemption Service",
                "status": "healthy",
                "api_connection": servers_result.get('success', False),
                "available_servers": servers_result.get('count', 0)
            })
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return jsonify({
                "success": False,
                "service": "VPN Redemption Service",
                "status": "unhealthy",
                "error": str(e)
            }), 500
    
    return bp
