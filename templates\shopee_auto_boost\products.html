{% extends "base.html" %}

{% block title %}Shopee Auto Boost - Products{% endblock %}

{% block header %}Boostable Products{% endblock %}

{% block content %}
<div class="container-fluid" x-data="shopeeAutoBoostProducts()">
    <!-- Filter and Search -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Product Filters</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="searchInput" class="form-label">Search Products</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="searchInput"
                                       x-model="searchTerm" 
                                       @input="filterProducts()"
                                       placeholder="Search by product name or ID">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sortBy" class="form-label">Sort By</label>
                                <select class="form-select" 
                                        id="sortBy"
                                        x-model="sortBy" 
                                        @change="filterProducts()">
                                    <option value="name">Product Name</option>
                                    <option value="sold_count">Sales Count</option>
                                    <option value="stock">Stock Level</option>
                                    <option value="price_min">Price (Low to High)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Actions</label>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" 
                                            @click="loadProducts(false)" 
                                            :disabled="loading">
                                        <i class="fas fa-database me-2"></i>
                                        <span x-show="!loading">Load Cached</span>
                                        <span x-show="loading">Loading...</span>
                                    </button>
                                    <button class="btn btn-primary" 
                                            @click="loadProducts(true)" 
                                            :disabled="loading">
                                        <i class="fas fa-sync me-2"></i>
                                        <span x-show="!loading">Refresh from API</span>
                                        <span x-show="loading">Refreshing...</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Status -->
    <div class="row mb-3" x-show="cacheInfo.cached">
        <div class="col-md-12">
            <div class="alert alert-info d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <div class="flex-grow-1">
                    <strong>Cache Status:</strong>
                    <span x-show="fromCache" class="text-success">Data loaded from cache</span>
                    <span x-show="!fromCache" class="text-primary">Fresh data from API</span>
                    <span x-show="cacheInfo.cached"> | Cache age: <span x-text="cacheInfo.cache_age_hours"></span> hours</span>
                    <span x-show="!cacheInfo.cache_valid" class="text-warning"> | Cache expired</span>
                </div>
                <button class="btn btn-sm btn-outline-danger" @click="clearCache()" :disabled="loading">
                    <i class="fas fa-trash me-1"></i>Clear Cache
                </button>
            </div>
        </div>
    </div>

    <!-- Bumped Products Status -->
    <div class="row mb-4" x-show="bumpedProducts.config">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Boost Slots Status</h5>
                    <button class="btn btn-sm btn-outline-primary" @click="loadBumpedProducts()" :disabled="loading">
                        <i class="fas fa-sync me-1"></i>Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 x-text="bumpedProducts.config?.total_slots || 5"></h4>
                                    <p class="mb-0">Total Slots</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 x-text="bumpedProducts.available_slots || 0"></h4>
                                    <p class="mb-0">Available Slots</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 x-text="bumpedProducts.products?.length || 0"></h4>
                                    <p class="mb-0">Products in Cooldown</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 x-text="pinnedProducts.length"></h4>
                                    <p class="mb-0">Pinned Products</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Bumped Products List -->
                    <div x-show="bumpedProducts.products && bumpedProducts.products.length > 0" class="mt-3">
                        <h6>Products Currently in Cooldown:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product ID</th>
                                        <th>Cooldown Remaining</th>
                                        <th>Can Boost At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="product in bumpedProducts.products" :key="product.id">
                                        <tr>
                                            <td x-text="product.id"></td>
                                            <td>
                                                <span :class="product.cool_down_seconds > 0 ? 'badge bg-warning' : 'badge bg-success'" 
                                                      x-text="product.cooldown_display"></span>
                                            </td>
                                            <td x-text="formatDateTime(product.can_boost_at)"></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" 
                                                        @click="togglePin(product.id)"
                                                        :class="isPinned(product.id) ? 'btn-warning' : 'btn-outline-primary'">
                                                    <i :class="isPinned(product.id) ? 'fas fa-star' : 'far fa-star'"></i>
                                                    <span x-text="isPinned(product.id) ? 'Unpin' : 'Pin'"></span>
                                                </button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4 x-text="products.length"></h4>
                    <p class="mb-0">Total Boostable Products</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4 x-text="filteredProducts.length"></h4>
                    <p class="mb-0">Filtered Results</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4 x-text="selectedProducts.length"></h4>
                    <p class="mb-0">Selected for Boost</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <button class="btn btn-light btn-sm w-100" 
                            @click="boostSelected()" 
                            :disabled="selectedProducts.length === 0 || loading">
                        <i class="fas fa-rocket me-2"></i>
                        Boost Selected
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Products List</h5>
                </div>
                <div class="card-body">
                    <div x-show="loading" class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading products...</p>
                    </div>

                    <div x-show="!loading && filteredProducts.length === 0" class="text-center text-muted">
                        <i class="fas fa-box-open fa-3x mb-3"></i>
                        <p>No boostable products found</p>
                    </div>

                    <div x-show="!loading && filteredProducts.length > 0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" 
                                                   @change="toggleSelectAll($event.target.checked)"
                                                   :checked="selectedProducts.length === filteredProducts.length && filteredProducts.length > 0">
                                        </th>
                                        <th>Product ID</th>
                                        <th>Product Name</th>
                                        <th>Stock</th>
                                        <th>Sold</th>
                                        <th>Price Range</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="product in paginatedProducts" :key="product.id">
                                        <tr>
                                            <td>
                                                <input type="checkbox" 
                                                       :value="product.id"
                                                       @change="toggleProductSelection(product.id, $event.target.checked)">
                                            </td>
                                            <td x-text="product.id"></td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" 
                                                     :title="product.name" 
                                                     x-text="product.name"></div>
                                            </td>
                                            <td>
                                                <span :class="product.stock < 10 ? 'badge bg-warning' : 'badge bg-success'" 
                                                      x-text="product.stock"></span>
                                            </td>
                                            <td x-text="product.sold_count"></td>
                                            <td x-text="product.price_min + ' - ' + product.price_max"></td>
                                            <td>
                                                <span :class="getStatusBadgeClass(product.boost_status)" 
                                                      x-text="getStatusText(product.boost_status)"></span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-primary" 
                                                            @click="boostSingleProduct(product.id)"
                                                            :disabled="loading"
                                                            title="Boost Product">
                                                        <i class="fas fa-rocket"></i>
                                                    </button>
                                                    <button class="btn btn-sm" 
                                                            @click="togglePin(product.id)"
                                                            :class="isPinned(product.id) ? 'btn-warning' : 'btn-outline-secondary'"
                                                            :title="isPinned(product.id) ? 'Unpin Product' : 'Pin Product'">
                                                        <i :class="isPinned(product.id) ? 'fas fa-star' : 'far fa-star'"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <nav x-show="totalPages > 1">
                            <ul class="pagination justify-content-center">
                                <li class="page-item" :class="{ 'disabled': currentPage === 1 }">
                                    <button class="page-link" @click="currentPage = Math.max(1, currentPage - 1)">Previous</button>
                                </li>
                                
                                <template x-for="page in visiblePages" :key="page">
                                    <li class="page-item" :class="{ 'active': page === currentPage }">
                                        <button class="page-link" @click="currentPage = page" x-text="page"></button>
                                    </li>
                                </template>
                                
                                <li class="page-item" :class="{ 'disabled': currentPage === totalPages }">
                                    <button class="page-link" @click="currentPage = Math.min(totalPages, currentPage + 1)">Next</button>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div x-show="alertMessage" 
         :class="'alert alert-' + alertType + ' alert-dismissible fade show'" 
         role="alert">
        <span x-text="alertMessage"></span>
        <button type="button" class="btn-close" @click="alertMessage = ''" aria-label="Close"></button>
    </div>
</div>

<script>
function shopeeAutoBoostProducts() {
    return {
        products: [],
        filteredProducts: [],
        selectedProducts: [],
        searchTerm: '',
        sortBy: 'name',
        loading: false,
        alertMessage: '',
        alertType: 'info',
        currentPage: 1,
        itemsPerPage: 20,
        cacheInfo: {},
        fromCache: false,
        bumpedProducts: {},
        pinnedProducts: [],
        
        init() {
            this.loadProducts(false); // Load cached data by default
            this.loadBumpedProducts();
            this.loadPinnedProducts();
        },
        
        get paginatedProducts() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredProducts.slice(start, end);
        },
        
        get totalPages() {
            return Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        },
        
        get visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },
        
        async loadProducts(forceRefresh = false) {
            this.loading = true;
            try {
                const url = forceRefresh ? 
                    '/api/shopee_auto_boost/products?refresh=true' : 
                    '/api/shopee_auto_boost/products';
                
                const response = await fetch(url);
                if (response.ok) {
                    const data = await response.json();
                    this.products = data.products || [];
                    this.cacheInfo = data.cache_info || {};
                    this.fromCache = data.from_cache || false;
                    
                    this.filterProducts();
                    
                    const source = this.fromCache ? 'cache' : 'API';
                    const message = forceRefresh ? 
                        `Refreshed ${this.products.length} products from API` :
                        `Loaded ${this.products.length} products from ${source}`;
                    
                    this.showAlert(message, 'success');
                } else {
                    this.showAlert('Failed to load products', 'danger');
                }
            } catch (error) {
                this.showAlert('Error loading products: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },

        async clearCache() {
            this.loading = true;
            try {
                const response = await fetch('/api/shopee_auto_boost/cache/clear', {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        this.cacheInfo = {};
                        this.fromCache = false;
                        this.showAlert('Cache cleared successfully', 'success');
                    } else {
                        this.showAlert('Failed to clear cache: ' + result.message, 'danger');
                    }
                } else {
                    this.showAlert('Failed to clear cache', 'danger');
                }
            } catch (error) {
                this.showAlert('Error clearing cache: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },
        
        filterProducts() {
            let filtered = [...this.products];
            
            // Apply search filter
            if (this.searchTerm) {
                const term = this.searchTerm.toLowerCase();
                filtered = filtered.filter(product => 
                    product.name.toLowerCase().includes(term) || 
                    product.id.toString().includes(term)
                );
            }
            
            // Apply sorting
            filtered.sort((a, b) => {
                switch (this.sortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'sold_count':
                        return b.sold_count - a.sold_count;
                    case 'stock':
                        return b.stock - a.stock;
                    case 'price_min':
                        return parseFloat(a.price_min) - parseFloat(b.price_min);
                    default:
                        return 0;
                }
            });
            
            this.filteredProducts = filtered;
            this.currentPage = 1;
        },
        
        toggleSelectAll(checked) {
            if (checked) {
                this.selectedProducts = this.filteredProducts.map(p => p.id);
            } else {
                this.selectedProducts = [];
            }
        },
        
        toggleProductSelection(productId, checked) {
            if (checked) {
                if (!this.selectedProducts.includes(productId)) {
                    this.selectedProducts.push(productId);
                }
            } else {
                this.selectedProducts = this.selectedProducts.filter(id => id !== productId);
            }
        },
        
        async boostSingleProduct(productId) {
            this.loading = true;
            try {
                const response = await fetch('/api/shopee_auto_boost/boost/single', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ product_id: productId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.showAlert(`Product ${productId} boosted successfully!`, 'success');
                } else {
                    this.showAlert(`Failed to boost product ${productId}: ${result.message}`, 'danger');
                }
            } catch (error) {
                this.showAlert('Error boosting product: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },
        
        async boostSelected() {
            if (this.selectedProducts.length === 0) return;
            
            this.loading = true;
            let successCount = 0;
            let failCount = 0;
            
            for (const productId of this.selectedProducts) {
                try {
                    const response = await fetch('/api/shopee_auto_boost/boost/single', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ product_id: productId })
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                }
            }
            
            this.showAlert(`Boost completed: ${successCount} successful, ${failCount} failed`, 
                          failCount === 0 ? 'success' : 'warning');
            this.selectedProducts = [];
            this.loading = false;
        },
        
        getStatusBadgeClass(status) {
            switch (status) {
                case 1: return 'badge bg-success';
                case 2: return 'badge bg-warning';
                case 3: return 'badge bg-danger';
                default: return 'badge bg-secondary';
            }
        },
        
        getStatusText(status) {
            switch (status) {
                case 1: return 'Can Boost';
                case 2: return 'Boosting';
                case 3: return 'Cannot Boost';
                default: return 'Unknown';
            }
        },
        
        async loadBumpedProducts() {
            try {
                const response = await fetch('/api/shopee_auto_boost/bumped');
                if (response.ok) {
                    const data = await response.json();
                    this.bumpedProducts = data;
                } else {
                    console.error('Failed to load bumped products');
                }
            } catch (error) {
                console.error('Error loading bumped products:', error);
            }
        },

        async loadPinnedProducts() {
            try {
                const response = await fetch('/api/shopee_auto_boost/pinned');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        this.pinnedProducts = data.data.pinned_products.map(p => p.id);
                    }
                } else {
                    console.error('Failed to load pinned products');
                }
            } catch (error) {
                console.error('Error loading pinned products:', error);
            }
        },

        isPinned(productId) {
            return this.pinnedProducts.includes(productId);
        },

        async togglePin(productId) {
            const isPinned = this.isPinned(productId);
            const method = isPinned ? 'DELETE' : 'POST';
            const url = `/api/shopee_auto_boost/pin/${productId}`;

            try {
                const response = await fetch(url, { method });
                const result = await response.json();

                if (result.success) {
                    if (isPinned) {
                        this.pinnedProducts = this.pinnedProducts.filter(id => id !== productId);
                        this.showAlert(`Product ${productId} unpinned`, 'success');
                    } else {
                        this.pinnedProducts.push(productId);
                        this.showAlert(`Product ${productId} pinned`, 'success');
                    }
                } else {
                    this.showAlert(`Failed to ${isPinned ? 'unpin' : 'pin'} product: ${result.message}`, 'danger');
                }
            } catch (error) {
                this.showAlert(`Error ${isPinned ? 'unpinning' : 'pinning'} product: ${error.message}`, 'danger');
            }
        },

        formatDateTime(dateTimeString) {
            if (!dateTimeString) return 'N/A';
            try {
                const date = new Date(dateTimeString);
                return date.toLocaleString();
            } catch (error) {
                return 'Invalid Date';
            }
        },

        showAlert(message, type) {
            this.alertMessage = message;
            this.alertType = type;
            setTimeout(() => this.alertMessage = '', 5000);
        }
    }
}
</script>
{% endblock %}
