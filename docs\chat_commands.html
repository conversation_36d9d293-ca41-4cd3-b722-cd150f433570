{% extends "base.html" %}

{% block title %}Chat Commands Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Chat Commands Management</h3>
                </div>
                <div class="card-body">
                    <!-- Status Summary -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-info-circle"></i> Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-lg-6 mb-2">
                                            <strong>Webhook Status:</strong>
                                            <span id="status-webhook" class="badge rounded-pill bg-secondary">Checking...</span>
                                        </div>
                                        <div class="col-lg-6 mb-2">
                                            <strong>Debug Mode:</strong>
                                            <span id="status-debug" class="badge rounded-pill bg-{{ 'success' if debug_config.enabled else 'secondary' }}">
                                                {{ 'Enabled' if debug_config.enabled else 'Disabled' }}
                                            </span>
                                        </div>
                                        <div class="col-lg-6 mb-2">
                                            <strong>Command Prefix:</strong>
                                            <span id="status-prefix" class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">{{ command_config.command_prefix }}</span>
                                        </div>
                                        <div class="col-lg-6 mb-2">
                                            <strong>Total Commands:</strong>
                                            <span id="status-total-commands" class="badge rounded-pill bg-primary">{{ commands | length }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button type="button" class="btn btn-primary" onclick="testWebhook()">
                                        <i class="fas fa-vial"></i> Test Webhook
                                    </button>
                                    <button type="button" class="btn btn-success ms-2" data-bs-toggle="modal" data-bs-target="#addCommandModal">
                                        <i class="fas fa-plus"></i> Add Command
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Configuration Sections -->
                    <div class="row mb-4">
                        <!-- VPN Configuration Notice -->
                        <div class="col-md-6">
                            <h5>VPN Configuration</h5>
                            <div class="card border-info">
                                <div class="card-body">
                                    <div class="alert alert-info mb-3">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>VPN Configuration Moved</strong>
                                    </div>
                                    <p>VPN configuration is now managed through the dedicated VPN Config Generator plugin.</p>
                                    <p>The <code>#config</code> command will continue to work as before, but all VPN settings should be configured in the proper location.</p>
                                    <div class="text-center">
                                        <a href="/vpn-config-generator/" class="btn btn-primary">
                                            <i class="fas fa-cog"></i> Open VPN Config Generator
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Webhook Configuration -->
                        <div class="col-md-6">
                            <h5>Webhook Configuration</h5>
                            <div class="card">
                                <div class="card-body">
                                    <form id="webhookConfigForm">
                                        <div class="mb-3">
                                            <label for="shopeeApiUrl" class="form-label">ShopeeAPI Base URL</label>
                                            <input type="url" class="form-control" id="shopeeApiUrl"
                                                   value="{{ webhook_config.shopee_api_base_url }}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="steamcodeToolUrl" class="form-label">SteamCodeTool Base URL</label>
                                            <input type="url" class="form-control" id="steamcodeToolUrl"
                                                   value="{{ webhook_config.steamcodetool_base_url }}" required>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="retryCount" class="form-label">Retry Count</label>
                                                    <input type="number" class="form-control" id="retryCount"
                                                           value="{{ webhook_config.retry_count }}" min="0" max="10">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="timeout" class="form-label">Timeout (seconds)</label>
                                                    <input type="number" class="form-control" id="timeout"
                                                           value="{{ webhook_config.timeout }}" min="5" max="120">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="webhookEnabled"
                                                           {{ 'checked' if webhook_config.enabled else '' }}>
                                                    <label class="form-check-label" for="webhookEnabled">
                                                        Enable Webhook
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="autoRegister"
                                                           {{ 'checked' if webhook_config.auto_register else '' }}>
                                                    <label class="form-check-label" for="autoRegister">
                                                        Auto Register
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <button type="submit" class="btn btn-success mb-2">
                                                    <i class="fas fa-save"></i> Save Config
                                                </button>
                                                <br>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="testWebhook()">
                                                    <i class="fas fa-vial"></i> Test
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="registerWebhook()">
                                                    <i class="fas fa-link"></i> Register
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Webhook Status Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Webhook Status</h5>
                            <div class="card">
                                <div class="card-body">
                                    <div id="webhookStatus">
                                        <div class="text-center">
                                            <button type="button" class="btn btn-outline-secondary" onclick="refreshWebhookStatus()">
                                                <i class="fas fa-sync-alt"></i> Check Status
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Debug Configuration Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Debug Configuration</h5>
                            <div class="card">
                                <div class="card-body">
                                    <form id="debugConfigForm">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="debugEnabled"
                                                           {{ 'checked' if debug_config.enabled else '' }}>
                                                    <label class="form-check-label" for="debugEnabled">
                                                        <strong>Enable Debug Logging</strong>
                                                    </label>
                                                    <div class="form-text">Master switch for all debug logging</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-save"></i> Save Debug Config
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input debug-option" type="checkbox" id="logWebhookData"
                                                           {{ 'checked' if debug_config.log_webhook_data else '' }}>
                                                    <label class="form-check-label" for="logWebhookData">
                                                        Log Webhook Data
                                                    </label>
                                                    <div class="form-text">Log incoming webhook requests and responses</div>
                                                </div>
                                                
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input debug-option" type="checkbox" id="logMessageParsing"
                                                           {{ 'checked' if debug_config.log_message_parsing else '' }}>
                                                    <label class="form-check-label" for="logMessageParsing">
                                                        Log Message Parsing
                                                    </label>
                                                    <div class="form-text">Log message parsing and content extraction</div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input debug-option" type="checkbox" id="logCommandProcessing"
                                                           {{ 'checked' if debug_config.log_command_processing else '' }}>
                                                    <label class="form-check-label" for="logCommandProcessing">
                                                        Log Command Processing
                                                    </label>
                                                    <div class="form-text">Log command detection and processing steps</div>
                                                </div>
                                                
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input debug-option" type="checkbox" id="logResponseGeneration"
                                                           {{ 'checked' if debug_config.log_response_generation else '' }}>
                                                    <label class="form-check-label" for="logResponseGeneration">
                                                        Log Response Generation
                                                    </label>
                                                    <div class="form-text">Log response generation and sending</div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info mt-3">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Note:</strong> Debug logging helps troubleshoot issues but may generate a lot of console output. 
                                            Enable only the specific types you need for debugging.
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Command Configuration Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Command Configuration</h5>
                            <div class="card">
                                <div class="card-body">
                                    <form id="commandConfigForm">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="commandPrefix" class="form-label">Command Prefix</label>
                                                    <input type="text" class="form-control" id="commandPrefix"
                                                           value="{{ command_config.command_prefix }}" maxlength="5" required>
                                                    <div class="form-text">Character(s) that trigger commands (e.g., #, @, !)</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="maxResponseLength" class="form-label">Max Response Length</label>
                                                    <input type="number" class="form-control" id="maxResponseLength"
                                                           value="{{ command_config.max_response_length }}" min="100" max="10000">
                                                    <div class="form-text">Maximum characters per response</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="maxImagesPerResponse" class="form-label">Max Images per Response</label>
                                                    <input type="number" class="form-control" id="maxImagesPerResponse"
                                                           value="{{ command_config.max_images_per_response }}" min="1" max="20">
                                                    <div class="form-text">Maximum images per response</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="caseSensitive"
                                                           {{ 'checked' if command_config.case_sensitive else '' }}>
                                                    <label class="form-check-label" for="caseSensitive">
                                                        Case Sensitive Commands
                                                    </label>
                                                    <div class="form-text">Whether command names are case sensitive</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-save"></i> Save Command Config
                                                </button>
                                            </div>
                                        </div>

                                        <div class="alert alert-info mt-3">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Note:</strong> Changing the command prefix will affect how users trigger commands.
                                            Make sure to inform users about any changes.
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Commands Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="commandsTable">
                            <thead>
                                <tr>
                                    <th>Command</th>
                                    <th>Description</th>
                                    <th>Response Preview</th>
                                    <th>Images</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for command_name, command in commands.items() %}
                                <tr data-command="{{ command_name }}">
                                    <td><code>{{ command_config.command_prefix }}{{ command.command }}</code></td>
                                    <td>{{ command.description }}</td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" 
                                             title="{{ command.response_text }}">
                                            {{ command.response_text[:50] }}{% if command.response_text|length > 50 %}...{% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ command.image_urls|length }} images</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if command.enabled else 'secondary' }}">
                                            {{ 'Enabled' if command.enabled else 'Disabled' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="editCommand('{{ command_name }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="testCommand('{{ command_name }}')">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteCommand('{{ command_name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Command Modal -->
<div class="modal fade" id="addCommandModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commandModalTitle">Add New Command</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="commandForm">
                    <input type="hidden" id="originalCommandName">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="commandName" class="form-label">Command Name</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="commandPrefixDisplay">{{ command_config.command_prefix }}</span>
                                    <input type="text" class="form-control" id="commandName" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="commandDescription" class="form-label">Description</label>
                                <input type="text" class="form-control" id="commandDescription" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="responseText" class="form-label">Response Text</label>
                        <textarea class="form-control" id="responseText" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="imageUrls" class="form-label">Image URLs (one per line)</label>
                        <textarea class="form-control" id="imageUrls" rows="3" 
                                  placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="requiredParams" class="form-label">Required Parameters (comma-separated)</label>
                        <input type="text" class="form-control" id="requiredParams" 
                               placeholder="param1, param2, param3">
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="commandEnabled" checked>
                        <label class="form-check-label" for="commandEnabled">
                            Enable Command
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveCommand()">Save Command</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Result Modal -->
<div class="modal fade" id="testResultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="testResultContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Global variables
let commands = {};
let commandPrefix = '{{ command_config.command_prefix }}';

// Load commands on page load
$(document).ready(function() {
    loadCommands();


    // Webhook Config form submission
    $('#webhookConfigForm').on('submit', function(e) {
        e.preventDefault();
        saveWebhookConfig();
    });

    // Debug Config form submission
    $('#debugConfigForm').on('submit', function(e) {
        e.preventDefault();
        saveDebugConfig();
    });

    // Command Config form submission
    $('#commandConfigForm').on('submit', function(e) {
        e.preventDefault();
        saveCommandConfig();
    });

    // Debug master switch handler
    $('#debugEnabled').on('change', function() {
        const isEnabled = $(this).prop('checked');
        $('.debug-option').prop('disabled', !isEnabled);
        if (!isEnabled) {
            $('.debug-option').prop('checked', false);
        }
    });

    // Initialize debug options state
    const debugEnabled = $('#debugEnabled').prop('checked');
    $('.debug-option').prop('disabled', !debugEnabled);

    // Load initial webhook status
    refreshWebhookStatus();
});

// Load all commands
function loadCommands() {
    $.get('/chat-commands/api/commands')
        .done(function(response) {
            if (response.success) {
                commands = response.commands;
                $('#status-total-commands').text(Object.keys(commands).length);
                updateCommandsTable();
            } else {
                showAlert('Error loading commands: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('Failed to load commands', 'danger');
        });
}

// Update commands table
function updateCommandsTable() {
    const tbody = $('#commandsTable tbody');
    tbody.empty();
    
    for (const [commandName, command] of Object.entries(commands)) {
        const row = `
            <tr data-command="${commandName}">
                <td><code>${commandPrefix}${command.command}</code></td>
                <td>${command.description}</td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${command.response_text}">
                        ${command.response_text.substring(0, 50)}${command.response_text.length > 50 ? '...' : ''}
                    </div>
                </td>
                <td><span class="badge bg-info">${command.image_urls.length} images</span></td>
                <td>
                    <span class="badge bg-${command.enabled ? 'success' : 'secondary'}">
                        ${command.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editCommand('${commandName}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="testCommand('${commandName}')">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCommand('${commandName}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    }
}

// Edit command
function editCommand(commandName) {
    const command = commands[commandName];
    if (!command) return;
    
    $('#commandModalTitle').text('Edit Command');
    $('#originalCommandName').val(commandName);
    $('#commandName').val(command.command);
    $('#commandDescription').val(command.description);
    $('#responseText').val(command.response_text);
    $('#imageUrls').val(command.image_urls.join('\n'));
    $('#requiredParams').val(command.required_params.join(', '));
    $('#commandEnabled').prop('checked', command.enabled);
    
    $('#addCommandModal').modal('show');
}

// Save command
function saveCommand() {
    const originalName = $('#originalCommandName').val();
    const isEdit = originalName !== '';
    
    const commandData = {
        command: $('#commandName').val(),
        description: $('#commandDescription').val(),
        response_text: $('#responseText').val(),
        image_urls: $('#imageUrls').val().split('\n').filter(url => url.trim() !== ''),
        required_params: $('#requiredParams').val().split(',').map(p => p.trim()).filter(p => p !== ''),
        enabled: $('#commandEnabled').prop('checked')
    };
    
    const url = isEdit ? `/chat-commands/api/commands/${originalName}` : '/chat-commands/api/commands';
    const method = isEdit ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(commandData)
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            $('#addCommandModal').modal('hide');
            $('#commandForm')[0].reset();
            $('#originalCommandName').val('');
            loadCommands();
        } else {
            showAlert('Error: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('Failed to save command', 'danger');
    });
}

// Delete command
function deleteCommand(commandName) {
    if (!confirm(`Are you sure you want to delete the command "#${commandName}"?`)) {
        return;
    }
    
    $.ajax({
        url: `/chat-commands/api/commands/${commandName}`,
        method: 'DELETE'
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadCommands();
        } else {
            showAlert('Error: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('Failed to delete command', 'danger');
    });
}

// Test command
function testCommand(commandName) {
    $.ajax({
        url: '/chat-commands/api/test-command',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({command: commandName})
    })
    .done(function(response) {
        if (response.success) {
            let content = '<h6>Test Results:</h6>';
            if (response.responses && response.responses.length > 0) {
                response.responses.forEach((resp, index) => {
                    content += `<div class="mb-3"><strong>Response ${index + 1}:</strong>`;
                    if (resp.text) {
                        content += `<div class="mt-2"><pre class="bg-light p-2">${resp.text}</pre></div>`;
                    }
                    if (resp.image_urls && resp.image_urls.length > 0) {
                        content += '<div class="mt-2"><strong>Images:</strong><ul>';
                        resp.image_urls.forEach(url => {
                            content += `<li><a href="${url}" target="_blank">${url}</a></li>`;
                        });
                        content += '</ul></div>';
                    }
                    content += '</div>';
                });
            } else {
                content += '<p>No responses generated.</p>';
            }
            $('#testResultContent').html(content);
        } else {
            $('#testResultContent').html(`<div class="alert alert-danger">Error: ${response.error}</div>`);
        }
        $('#testResultModal').modal('show');
    })
    .fail(function() {
        $('#testResultContent').html('<div class="alert alert-danger">Failed to test command</div>');
        $('#testResultModal').modal('show');
    });
}


// Save webhook config
function saveWebhookConfig() {
    const configData = {
        enabled: $('#webhookEnabled').prop('checked'),
        shopee_api_base_url: $('#shopeeApiUrl').val(),
        steamcodetool_base_url: $('#steamcodeToolUrl').val(),
        auto_register: $('#autoRegister').prop('checked'),
        retry_count: parseInt($('#retryCount').val()),
        retry_delay: parseInt($('#retryDelay').val()) || 5,
        timeout: parseInt($('#timeout').val())
    };

    $.ajax({
        url: '/chat-commands/api/webhook-config',
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(configData)
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            refreshWebhookStatus();
        } else {
            showAlert('Error: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('Failed to save webhook configuration', 'danger');
    });
}

// Save debug config
function saveDebugConfig() {
    const configData = {
        enabled: $('#debugEnabled').prop('checked'),
        log_webhook_data: $('#logWebhookData').prop('checked'),
        log_message_parsing: $('#logMessageParsing').prop('checked'),
        log_command_processing: $('#logCommandProcessing').prop('checked'),
        log_response_generation: $('#logResponseGeneration').prop('checked')
    };

    $.ajax({
        url: '/chat-commands/api/debug-config',
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(configData)
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            const enabled = $('#debugEnabled').prop('checked');
            const debugStatusBadge = $('#status-debug');
            debugStatusBadge.text(enabled ? 'Enabled' : 'Disabled');
            debugStatusBadge.removeClass('bg-success bg-secondary').addClass(enabled ? 'bg-success' : 'bg-secondary');
        } else {
            showAlert('Error: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('Failed to save debug configuration', 'danger');
    });
}

// Refresh webhook status
function refreshWebhookStatus() {
    $('#webhookStatus').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Checking status...</div>');

    $.get('/chat-commands/api/webhook/status')
        .done(function(response) {
            if (response.success) {
                displayWebhookStatus(response.status);
            } else {
                $('#webhookStatus').html('<div class="alert alert-danger">Error: ' + response.error + '</div>');
            }
        })
        .fail(function() {
            $('#webhookStatus').html('<div class="alert alert-danger">Failed to get webhook status</div>');
        });
}

// Display webhook status
function displayWebhookStatus(status) {
    const webhookStatusBadge = $('#status-webhook');
    if (status.webhook_config.enabled) {
        const overallClass = status.overall_status === 'healthy' ? 'success' : 'warning';
        webhookStatusBadge.text('Enabled');
        webhookStatusBadge.removeClass('bg-secondary bg-success bg-warning').addClass(overallClass);
    } else {
        webhookStatusBadge.text('Disabled');
        webhookStatusBadge.removeClass('bg-secondary bg-success bg-warning').addClass('bg-secondary');
    }

    let html = '<div class="row">';

    // Overall status
    const overallClass = status.overall_status === 'healthy' ? 'success' : 'warning';
    html += `<div class="col-md-12 mb-3">
        <div class="alert alert-${overallClass}">
            <strong>Overall Status:</strong> ${status.overall_status === 'healthy' ? 'Healthy' : 'Issues Detected'}
        </div>
    </div>`;

    // ShopeeAPI connectivity
    const shopeeStatus = status.shopee_api_connectivity;
    const shopeeClass = shopeeStatus.status === 'success' ? 'success' : 'danger';
    html += `<div class="col-md-6">
        <div class="card border-${shopeeClass}">
            <div class="card-header bg-${shopeeClass} text-white">
                <h6 class="mb-0">ShopeeAPI Connectivity</h6>
            </div>
            <div class="card-body">
                <p><strong>Status:</strong> ${shopeeStatus.status}</p>
                <p><strong>URL:</strong> ${shopeeStatus.url}</p>
                <p><strong>Message:</strong> ${shopeeStatus.message}</p>
                ${shopeeStatus.response_time ? `<p><strong>Response Time:</strong> ${shopeeStatus.response_time}s</p>` : ''}
            </div>
        </div>
    </div>`;

    // Webhook endpoint test
    const endpointStatus = status.webhook_endpoint_test;
    const endpointClass = endpointStatus.status === 'success' ? 'success' : 'danger';
    html += `<div class="col-md-6">
        <div class="card border-${endpointClass}">
            <div class="card-header bg-${endpointClass} text-white">
                <h6 class="mb-0">Webhook Endpoint</h6>
            </div>
            <div class="card-body">
                <p><strong>Status:</strong> ${endpointStatus.status}</p>
                <p><strong>URL:</strong> ${endpointStatus.url}</p>
                <p><strong>Message:</strong> ${endpointStatus.message}</p>
            </div>
        </div>
    </div>`;

    html += '</div>';

    // Configuration details
    html += '<div class="mt-3"><h6>Configuration:</h6>';
    html += '<div class="row">';
    html += `<div class="col-md-6">
        <small><strong>Webhook URL:</strong> ${status.webhook_config.steamcodetool_base_url}${status.webhook_config.webhook_endpoint}</small><br>
        <small><strong>Enabled:</strong> ${status.webhook_config.enabled ? 'Yes' : 'No'}</small><br>
        <small><strong>Auto Register:</strong> ${status.webhook_config.auto_register ? 'Yes' : 'No'}</small>
    </div>`;
    html += `<div class="col-md-6">
        <small><strong>Retry Count:</strong> ${status.webhook_config.retry_count}</small><br>
        <small><strong>Timeout:</strong> ${status.webhook_config.timeout}s</small><br>
        <small><strong>Retry Delay:</strong> ${status.webhook_config.retry_delay}s</small>
    </div>`;
    html += '</div></div>';

    $('#webhookStatus').html(html);
}

// Test webhook
function testWebhook() {
    showAlert('Testing webhook connectivity...', 'info');

    $.post('/chat-commands/api/webhook/test')
        .done(function(response) {
            if (response.success) {
                let message = 'Webhook test completed:\n';
                message += `ShopeeAPI: ${response.tests.shopee_api.status}\n`;
                message += `Webhook Endpoint: ${response.tests.webhook_endpoint.status}`;
                showAlert(message, 'success');
                refreshWebhookStatus();
            } else {
                showAlert('Webhook test failed: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('Failed to test webhook', 'danger');
        });
}

// Register webhook
function registerWebhook() {
    if (!confirm('Register webhook with ShopeeAPI? This will configure ShopeeAPI to send message events to this plugin.')) {
        return;
    }

    showAlert('Registering webhook...', 'info');

    $.post('/chat-commands/api/webhook/register')
        .done(function(response) {
            if (response.success) {
                showAlert('Webhook registered successfully: ' + response.webhook_url, 'success');
                refreshWebhookStatus();
            } else {
                showAlert('Failed to register webhook: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('Failed to register webhook', 'danger');
        });
}

// Unregister webhook
function unregisterWebhook() {
    if (!confirm('Unregister webhook from ShopeeAPI? This will stop automatic command processing.')) {
        return;
    }

    showAlert('Unregistering webhook...', 'info');

    $.post('/chat-commands/api/webhook/unregister')
        .done(function(response) {
            if (response.success) {
                showAlert('Webhook unregistered successfully', 'success');
                refreshWebhookStatus();
            } else {
                showAlert('Failed to unregister webhook: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('Failed to unregister webhook', 'danger');
        });
}

// Save command configuration
function saveCommandConfig() {
    const configData = {
        command_prefix: $('#commandPrefix').val(),
        case_sensitive: $('#caseSensitive').prop('checked'),
        max_response_length: parseInt($('#maxResponseLength').val()),
        max_images_per_response: parseInt($('#maxImagesPerResponse').val())
    };

    $.ajax({
        url: '/chat-commands/api/command-config',
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(configData)
    })
    .done(function(response) {
        if (response.success) {
            showAlert('Command configuration saved successfully', 'success');
            // Update global command prefix
            commandPrefix = configData.command_prefix;
            $('#status-prefix').text(commandPrefix);
            // Update modal prefix display
            $('#commandPrefixDisplay').text(commandPrefix);
            // Refresh commands table to show new prefix
            updateCommandsTable();
        } else {
            showAlert('Error saving command configuration: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('Failed to save command configuration', 'danger');
    });
}

// Show alert
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
