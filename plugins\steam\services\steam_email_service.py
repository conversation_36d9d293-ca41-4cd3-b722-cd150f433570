"""
Steam Email Service
Handles Steam authentication code retrieval from email
"""

import imaplib
import email
import re
import logging
import time
from typing import Dict, Any, Optional, List
from threading import Lock
import ssl

logger = logging.getLogger(__name__)

class SteamEmailService:
    """Service for retrieving Steam authentication codes from email"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.email_config = config.get('email_config', {})
        self.credentials = self.email_config.get('credentials', [])
        self.lock = Lock()
        
        # IMAP settings
        self.imap_server = self.email_config.get('imap_server', 'imap.gmail.com')
        self.imap_port = self.email_config.get('imap_port', 993)
        self.use_ssl = self.email_config.get('use_ssl', True)
        
        # Auth code pattern
        self.auth_code_pattern = re.compile(r'[A-Z0-9]{5}')
        
    def update_config(self, config: Dict[str, Any]):
        """Update service configuration"""
        self.config = config
        self.email_config = config.get('email_config', {})
        self.credentials = self.email_config.get('credentials', [])
        
    def get_auth_code_for_user(self, username: str) -> Optional[str]:
        """Get Steam authentication code for a specific username"""
        with self.lock:
            try:
                # Find credentials for the username
                user_creds = None
                for cred in self.credentials:
                    if cred.get('username') == username:
                        user_creds = cred
                        break
                        
                if not user_creds:
                    logger.error(f"No email credentials found for username: {username}")
                    return None
                    
                email_address = user_creds['email']
                password = user_creds['password']
                
                logger.info(f"Attempting to retrieve auth code for {username} from {email_address}")
                
                # Connect to IMAP server
                if self.use_ssl:
                    mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
                else:
                    mail = imaplib.IMAP4(self.imap_server, self.imap_port)
                    
                # Login
                mail.login(email_address, password)
                mail.select('inbox')
                
                # Search for Steam emails from the last 10 minutes
                search_criteria = '(FROM "<EMAIL>" SUBJECT "Steam Guard")'
                status, messages = mail.search(None, search_criteria)
                
                if status != 'OK' or not messages[0]:
                    logger.warning(f"No Steam Guard emails found for {username}")
                    mail.logout()
                    return None
                    
                # Get the most recent email
                email_ids = messages[0].split()
                latest_email_id = email_ids[-1]
                
                # Fetch the email
                status, msg_data = mail.fetch(latest_email_id, '(RFC822)')
                if status != 'OK':
                    logger.error(f"Failed to fetch email for {username}")
                    mail.logout()
                    return None
                    
                # Parse the email
                email_body = msg_data[0][1]
                email_message = email.message_from_bytes(email_body)
                
                # Extract auth code from email content
                auth_code = self._extract_auth_code_from_email(email_message)
                
                mail.logout()
                
                if auth_code:
                    logger.info(f"Successfully retrieved auth code for {username}: {auth_code}")
                    return auth_code
                else:
                    logger.warning(f"Could not extract auth code from email for {username}")
                    return None
                    
            except Exception as e:
                logger.error(f"Error retrieving auth code for {username}: {e}")
                return None
                
    def _extract_auth_code_from_email(self, email_message) -> Optional[str]:
        """Extract authentication code from email message"""
        try:
            # Get email content
            content = ""
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                content = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
                
            # Search for auth code pattern
            matches = self.auth_code_pattern.findall(content)
            if matches:
                # Return the first match (should be the auth code)
                return matches[0]
                
            return None
            
        except Exception as e:
            logger.error(f"Error extracting auth code from email: {e}")
            return None
            
    def test_connection(self, email_address: str, password: str) -> bool:
        """Test email connection"""
        try:
            if self.use_ssl:
                mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            else:
                mail = imaplib.IMAP4(self.imap_server, self.imap_port)
                
            mail.login(email_address, password)
            mail.logout()
            return True
            
        except Exception as e:
            logger.error(f"Email connection test failed for {email_address}: {e}")
            return False
            
    def get_available_usernames(self) -> List[str]:
        """Get list of available usernames"""
        return [cred.get('username') for cred in self.credentials if cred.get('username')]
        
    def cleanup(self):
        """Cleanup service resources"""
        logger.info("Steam email service cleanup completed")
