# Plugin Architecture Standards

This document defines the standard architecture patterns and structural organization for all plugins in the SteamCodeTool system.

## 📁 Directory Structure

### Standard Plugin Directory Layout
```
plugins/
└── your_plugin_name/
    ├── __init__.py                 # Package initialization
    ├── plugin.py                   # Main plugin class (REQUIRED)
    ├── config.json                 # Plugin configuration (OPTIONAL)
    ├── README.md                   # Plugin documentation (REQUIRED)
    ├── requirements.txt            # Plugin dependencies (OPTIONAL)
    ├── services/                   # Business logic services (RECOMMENDED)
    │   ├── __init__.py
    │   ├── base_service.py         # Base service class
    │   └── specific_service.py     # Specific business services
    ├── routes/                     # Flask route blueprints (RECOMMENDED)
    │   ├── __init__.py
    │   └── plugin_routes.py        # Route definitions
    ├── models/                     # Data models (OPTIONAL)
    │   ├── __init__.py
    │   └── data_models.py
    ├── templates/                  # HTML templates (OPTIONAL)
    │   └── plugin_template.html
    ├── static/                     # Static assets (OPTIONAL)
    │   ├── css/
    │   ├── js/
    │   └── images/
    ├── tests/                      # Unit tests (RECOMMENDED)
    │   ├── __init__.py
    │   ├── test_plugin.py
    │   ├── test_services.py
    │   └── test_routes.py
    └── docs/                       # Additional documentation (OPTIONAL)
        ├── API.md
        ├── CONFIGURATION.md
        └── TROUBLESHOOTING.md
```

## 🏗️ Core Architecture Patterns

### 1. Plugin Class Pattern (MANDATORY)

```python
# plugins/your_plugin/plugin.py
from core.plugin_manager import PluginInterface
from typing import Dict, Any, Optional
from flask import Blueprint

class Plugin(PluginInterface):
    """Your Plugin Description"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "your_plugin"  # snake_case
        self.version = "1.0.0"     # Semantic versioning
        self.description = "Brief description of plugin functionality"
        self.dependencies = []      # List of required plugins
        self.url_prefix = "/api/your-plugin"  # kebab-case for URLs
        
        # Initialize services and components
        self._initialize_components()
    
    def initialize(self) -> bool:
        """Initialize plugin - REQUIRED"""
        # Implementation here
        return True
    
    def shutdown(self) -> bool:
        """Shutdown plugin - REQUIRED"""
        # Cleanup here
        return True
    
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return Flask blueprint - REQUIRED"""
        # Return blueprint or None
        return self.blueprint
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return configuration schema - REQUIRED"""
        # Return JSON schema
        return {}
```

### 2. Service Layer Pattern (RECOMMENDED)

```python
# plugins/your_plugin/services/base_service.py
from abc import ABC, abstractmethod
from typing import Dict, Any
import logging

class BaseService(ABC):
    """Base class for all plugin services"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._initialized = False
    
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the service"""
        pass
    
    @abstractmethod
    def shutdown(self) -> bool:
        """Shutdown the service"""
        pass
    
    def update_config(self, config: Dict[str, Any]):
        """Update service configuration"""
        self.config.update(config)
```

### 3. Route Blueprint Pattern (RECOMMENDED)

```python
# plugins/your_plugin/routes/plugin_routes.py
from flask import Blueprint, jsonify, request
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..plugin import Plugin

def create_plugin_blueprint(plugin: 'Plugin') -> Blueprint:
    """Create and configure the plugin blueprint"""
    
    bp = Blueprint('your_plugin', __name__)
    
    @bp.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint"""
        return jsonify({
            'status': 'healthy',
            'plugin': plugin.name,
            'version': plugin.version
        })
    
    @bp.route('/api/endpoint', methods=['GET', 'POST'])
    def api_endpoint():
        """Main API endpoint"""
        # Implementation here
        return jsonify({'result': 'success'})
    
    return bp
```

## 🔧 Component Integration Patterns

### 1. Service Integration
```python
class Plugin(PluginInterface):
    def _initialize_components(self):
        """Initialize all plugin components"""
        self.services = {}
        self.blueprint = None
        
    def initialize(self) -> bool:
        try:
            # Initialize services
            self._initialize_services()
            
            # Create routes
            self._create_blueprint()
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize: {e}")
            return False
    
    def _initialize_services(self):
        """Initialize all services"""
        from .services.main_service import MainService
        
        self.services['main'] = MainService(self.config)
        self.services['main'].initialize()
    
    def _create_blueprint(self):
        """Create Flask blueprint"""
        from .routes.plugin_routes import create_plugin_blueprint
        
        self.blueprint = create_plugin_blueprint(self)
```

### 2. Configuration Management
```python
def get_config_schema(self) -> Dict[str, Any]:
    """Standard configuration schema structure"""
    return {
        "type": "object",
        "properties": {
            "enabled": {
                "type": "boolean",
                "default": True,
                "description": "Enable/disable plugin"
            },
            "service_config": {
                "type": "object",
                "properties": {
                    "timeout": {
                        "type": "integer",
                        "default": 30,
                        "description": "Service timeout in seconds"
                    }
                }
            }
        },
        "required": ["enabled"]
    }
```

## 📋 Architecture Guidelines

### Mandatory Requirements
1. **Plugin Class**: Must inherit from `PluginInterface`
2. **Abstract Methods**: Must implement all required abstract methods
3. **Error Handling**: Must include proper exception handling
4. **Logging**: Must use structured logging
5. **Configuration**: Must provide configuration schema

### Recommended Patterns
1. **Service Layer**: Separate business logic into service classes
2. **Route Separation**: Use separate route modules for complex plugins
3. **Dependency Injection**: Pass dependencies through constructors
4. **Interface Segregation**: Create focused, single-purpose services
5. **Configuration Validation**: Validate configuration on load

### Optional Enhancements
1. **Models**: Use data models for complex data structures
2. **Templates**: Provide web UI templates if needed
3. **Static Assets**: Include CSS/JS for web interfaces
4. **Documentation**: Provide comprehensive API documentation

## 🚫 Anti-Patterns to Avoid

### ❌ Don't Do This
```python
# Mixing business logic in routes
@bp.route('/process')
def process_data():
    # Complex business logic here - BAD!
    result = complex_calculation()
    database.save(result)
    return jsonify(result)

# Global state in plugin
global_variable = None  # BAD!

# Direct database access in routes
@bp.route('/data')
def get_data():
    return db.query("SELECT * FROM table")  # BAD!
```

### ✅ Do This Instead
```python
# Use service layer
@bp.route('/process')
def process_data():
    result = plugin.services['processor'].process()
    return jsonify(result)

# Use instance variables
class Plugin(PluginInterface):
    def __init__(self, plugin_manager):
        self.state = {}  # GOOD!

# Use service abstraction
@bp.route('/data')
def get_data():
    return plugin.services['data'].get_all()  # GOOD!
```

## 🔄 Migration Guidelines

### Updating Existing Plugins
1. **Assessment**: Review current plugin against standards
2. **Planning**: Create migration plan with priorities
3. **Incremental**: Implement changes incrementally
4. **Testing**: Test each change thoroughly
5. **Documentation**: Update documentation as you go

### Backward Compatibility
- Maintain existing API endpoints during migration
- Use deprecation warnings for old patterns
- Provide migration guides for breaking changes
- Support both old and new patterns during transition period
