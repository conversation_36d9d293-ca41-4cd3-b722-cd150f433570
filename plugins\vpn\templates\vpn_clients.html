{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>VPN Clients</h2>
                <div>
                    <a href="{{ url_for('vpn.create_client') }}" class="btn btn-primary mr-2">
                        <i class="fas fa-plus"></i> Add Client
                    </a>
                    <a href="{{ url_for('vpn.bulk_create_clients') }}" class="btn btn-success mr-2">
                        <i class="fas fa-upload"></i> Bulk Create
                    </a>
                    <a href="{{ url_for('vpn.client_sync') }}" class="btn btn-warning mr-2">
                        <i class="fas fa-sync-alt"></i> Sync Management
                    </a>
                    <button id="syncAllBtn" class="btn btn-info" onclick="syncAllClients()">
                        <i class="fas fa-sync"></i> Quick Sync
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('vpn.clients') }}">
                        <div class="row align-items-end">
                            <div class="col-md-auto mb-3">
                                <label for="server_id" class="form-label">Server:</label>
                                <select name="server_id" id="server_id" class="form-control">
                                    <option value="">All Servers</option>
                                    {% for server in servers %}
                                    <option value="{{ server.id }}" {{ 'selected' if server_id == server.id else '' }}>
                                        {{ server.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-auto mb-3">
                                <label for="is_active" class="form-label">Status:</label>
                                <select name="is_active" id="is_active" class="form-control">
                                    <option value="">All</option>
                                    <option value="true" {{ 'selected' if is_active == True else '' }}>Active</option>
                                    <option value="false" {{ 'selected' if is_active == False else '' }}>Inactive</option>
                                </select>
                            </div>
                            
                            <div class="col-md-auto mb-3">
                                <label for="is_expired" class="form-label">Expiry:</label>
                                <select name="is_expired" id="is_expired" class="form-control">
                                    <option value="">All</option>
                                    <option value="true" {{ 'selected' if is_expired == True else '' }}>Expired</option>
                                    <option value="false" {{ 'selected' if is_expired == False else '' }}>Not Expired</option>
                                </select>
                            </div>
                            
                            <div class="col-md-auto mb-3">
                                <label for="search" class="form-label">Search:</label>
                                <input type="text" name="search" id="search" class="form-control"
                                       value="{{ search }}" placeholder="Email or username...">
                            </div>
                            
                            <div class="col-md-auto mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <a href="{{ url_for('vpn.clients') }}" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Clients Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Client List</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>Status</th>
                                    <th>Email/ID</th>
                                    <th>Server</th>
                                    <th>Shopee Username</th>
                                    <th>Expiry Date</th>
                                    <th>Days Left</th>
                                    <th>Data Usage</th>
                                    <th>Last Connected</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in clients %}
                                <tr class="{{ 'table-danger' if client.is_expired else '' }}">
                                    <td>
                                        {% if client.is_expired %}
                                        <span class="badge badge-danger">Expired</span>
                                        {% elif client.is_active %}
                                        <span class="badge badge-success">Active</span>
                                        {% else %}
                                        <span class="badge badge-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ client.email }}</strong>
                                        {% if client.description %}
                                        <br><small class="text-muted">{{ client.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% for server in servers %}
                                            {% if server.id == client.server_id %}
                                                {{ server.name }}
                                            {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>{{ client.shopee_username or '-' }}</td>
                                    <td>
                                        {{ client.expired_date }}
                                        {% if client.expired_date == 'lifetime' %}
                                        <span class="badge badge-info">Lifetime</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if client.expired_date == 'lifetime' %}
                                        <span class="badge badge-info">∞</span>
                                        {% elif client.is_expired %}
                                        <span class="badge badge-danger">Expired</span>
                                        {% elif client.days_until_expiry <= 7 %}
                                        <span class="badge badge-warning">{{ client.days_until_expiry }} days</span>
                                        {% else %}
                                        <span class="badge badge-success">{{ client.days_until_expiry }} days</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ (client.data_usage / 1024 / 1024 / 1024) | round(2) }} GB</td>
                                    <td>{{ client.last_connected or 'Never' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" 
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                Actions
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="{{ url_for('vpn.edit_client', client_id=client.id) }}">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <a class="dropdown-item" href="#" onclick="showClientConfig({{ client.id }})">
                                                    <i class="fas fa-qrcode"></i> Show Config
                                                </a>
                                                {% if not client.is_expired %}
                                                <a class="dropdown-item" href="#" onclick="extendExpiry({{ client.id }})">
                                                    <i class="fas fa-calendar-plus"></i> Extend Expiry
                                                </a>
                                                {% endif %}
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item text-danger" href="#" 
                                                   onclick="deleteClient({{ client.id }}, '{{ client.email }}')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if not clients %}
                    <div class="text-center py-5 my-4">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h4>No Clients Found</h4>
                        <p class="text-muted">There are currently no VPN clients matching your criteria.</p>
                        <a href="{{ url_for('vpn.create_client') }}" class="btn btn-primary mt-2">
                            <i class="fas fa-plus"></i> Add Your First Client
                        </a>
                    </div>
                    {% endif %}
                    
                    <!-- Pagination -->
                    {% if total_pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item {{ 'disabled' if page == 1 else '' }}">
                                <a class="page-link" href="{{ url_for('vpn.clients', page=page-1, per_page=per_page, server_id=server_id, is_active=is_active, is_expired=is_expired, search=search) }}">
                                    Previous
                                </a>
                            </li>
                            
                            {% for p in range(1, total_pages + 1) %}
                                {% if p == page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ p }}</span>
                                </li>
                                {% elif p == 1 or p == total_pages or (p > page - 3 and p < page + 3) %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('vpn.clients', page=p, per_page=per_page, server_id=server_id, is_active=is_active, is_expired=is_expired, search=search) }}">
                                        {{ p }}
                                    </a>
                                </li>
                                {% elif p == page - 3 or p == page + 3 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            <li class="page-item {{ 'disabled' if page == total_pages else '' }}">
                                <a class="page-link" href="{{ url_for('vpn.clients', page=page+1, per_page=per_page, server_id=server_id, is_active=is_active, is_expired=is_expired, search=search) }}">
                                    Next
                                </a>
                            </li>
                        </ul>
                    </nav>
                    {% endif %}
                    
                    <div class="text-center text-muted">
                        Showing {{ (page - 1) * per_page + 1 }} - {{ min(page * per_page, total) }} of {{ total }} clients
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete client "<span id="deleteClientEmail"></span>"?</p>
                <p class="text-danger">This will remove the client from the server configuration!</p>
            </div>
            <div class="modal-footer">
                <form id="deleteForm" method="POST" action="">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Client</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Extend Expiry Modal -->
<div class="modal fade" id="extendModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Extend Client Expiry</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="extendForm" method="POST" action="">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="days">Extend by (days):</label>
                        <input type="number" class="form-control" id="days" name="days" 
                               value="30" min="1" max="365" required>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="extend30">
                        <label class="form-check-label" for="extend30">30 days</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="extend90">
                        <label class="form-check-label" for="extend90">90 days</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="extend365">
                        <label class="form-check-label" for="extend365">365 days</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Extend Expiry</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function deleteClient(clientId, clientEmail) {
    $('#deleteClientEmail').text(clientEmail);
    $('#deleteForm').attr('action', `/admin/vpn/clients/${clientId}/delete`);
    $('#deleteModal').modal('show');
}

function extendExpiry(clientId) {
    $('#extendForm').attr('action', `/admin/vpn/clients/${clientId}/extend`);
    $('#extendModal').modal('show');
}

// Quick extend options
$('#extend30').change(function() {
    if ($(this).prop('checked')) {
        $('#days').val(30);
        $('#extend90, #extend365').prop('checked', false);
    }
});

$('#extend90').change(function() {
    if ($(this).prop('checked')) {
        $('#days').val(90);
        $('#extend30, #extend365').prop('checked', false);
    }
});

$('#extend365').change(function() {
    if ($(this).prop('checked')) {
        $('#days').val(365);
        $('#extend30, #extend90').prop('checked', false);
    }
});

function showClientConfig(clientId) {
    // This would show the client configuration
    // You can implement this to show QR code or config details
    toastr.info('Client configuration feature coming soon!');
}

function syncAllClients() {
    if (confirm('Sync all clients from all servers? This may take a while.')) {
        const syncButton = $('#syncAllBtn');
        const originalButtonHtml = syncButton.html();

        // Disable button and show spinner
        syncButton.prop('disabled', true).html(
            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Syncing...'
        );

        toastr.info('Syncing all clients... This may take some time.');

        $.post('/admin/vpn/api/sync/all')
            .done(function(data) {
                toastr.success((data && data.message) || 'All clients synced successfully. Refreshing...');
                setTimeout(() => location.reload(), 1500);
            })
            .fail(function(jqXHR) {
                let errorMessage = 'Failed to sync clients.';
                if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
                    errorMessage = jqXHR.responseJSON.error;
                }
                toastr.error(errorMessage);
                // Restore button on failure
                syncButton.prop('disabled', false).html(originalButtonHtml);
            });
    }
}
</script>
{% endblock %}
