<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Inline Script</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Inline Script</h1>
        <p>This tests if inline JavaScript works properly.</p>
        
        <button type="button" class="btn btn-primary" id="testBtn" onclick="window.testFunction()">
            Test Button
        </button>
        
        <div id="result" class="mt-3"></div>
    </div>

    <!-- Inline JavaScript -->
    <script>
        console.log('=== INLINE SCRIPT LOADED ===');
        
        window.testFunction = function() {
            console.log('Test function called!');
            document.getElementById('result').innerHTML = '<div class="alert alert-success">Function called successfully!</div>';
        };
        
        console.log('Function defined:', typeof window.testFunction !== 'undefined');
    </script>
</body>
</html>
