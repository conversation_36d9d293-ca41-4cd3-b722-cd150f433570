version: '3.8'

services:
  shopee-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      # Log level configuration (reduces log spam)
      - LOG_LEVEL=WARNING          # General log level (DEBUG, INFO, WARNING, ERROR)
      - WEBSOCKET_LOG_LEVEL=ERROR  # WebSocket logs (very frequent)
      - WEBHOOK_LOG_LEVEL=ERROR    # Webhook logs (frequent)
      - DEBUG_MODE=false           # Set to true for troubleshooting
      
      # Server configuration
      - PORT=8000
    volumes:
      # Configuration file
      - ./config.json:/app/ShopeeAPI/config.json
      
      # Log files (with rotation)
      - ./logs:/app/logs
      
      # Optional: Environment file
      # - ./.env:/app/.env
    restart: unless-stopped
    
    # Optional: Resource limits to prevent excessive resource usage
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Optional: Log monitoring service
  log-monitor:
    image: alpine:latest
    volumes:
      - ./logs:/logs:ro
    command: >
      sh -c "
        echo 'Log Monitor Started';
        while true; do
          echo '=== Log File Sizes ===';
          ls -lh /logs/ 2>/dev/null || echo 'No log files yet';
          echo '';
          echo '=== Recent Errors ===';
          tail -n 5 /logs/shopee_api_errors.log 2>/dev/null || echo 'No error log yet';
          echo '';
          sleep 300;
        done
      "
    restart: unless-stopped
    profiles:
      - monitoring

# Usage Examples:
#
# 1. Normal operation (minimal logs):
#    docker-compose up -d
#
# 2. With log monitoring:
#    docker-compose --profile monitoring up -d
#
# 3. Debug mode (verbose logs):
#    DEBUG_MODE=true docker-compose up -d
#
# 4. Custom log levels:
#    LOG_LEVEL=INFO WEBSOCKET_LOG_LEVEL=WARNING docker-compose up -d
