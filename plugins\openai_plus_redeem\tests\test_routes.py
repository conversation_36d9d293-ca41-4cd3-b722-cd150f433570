"""
Unit Tests for OpenAI Plus Redeem Plugin Routes

Comprehensive tests for all endpoints including authentication, validation,
and error scenarios.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
from datetime import datetime, timedelta
from flask import Flask

# Import routes
from ..routes.customer_routes import create_customer_blueprint
from ..routes.admin_routes import create_admin_blueprint

# Import models for test data
from ..models.chatgpt_account import ChatGPTAccount, AccountStatus
from ..models.order_redemption import OrderRedemption, RedemptionStatus


class TestCustomerRoutes(unittest.TestCase):
    """Test customer-facing routes"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        # Create mock plugin
        self.mock_plugin = Mock()
        self.mock_plugin.name = "openai_plus_redeem"
        self.mock_plugin.version = "1.0.0"
        
        # Create mock service manager
        self.mock_service_manager = <PERSON><PERSON>()
        self.mock_plugin.service_manager = self.mock_service_manager
        
        # Create mock services
        self.mock_order_redeem_service = Mock()
        self.mock_email_service = Mock()
        self.mock_cooldown_service = Mock()
        
        # Configure service manager to return mock services
        def get_service_side_effect(service_name):
            services = {
                'order_redeem': self.mock_order_redeem_service,
                'email': self.mock_email_service,
                'cooldown': self.mock_cooldown_service
            }
            return services.get(service_name)
        
        self.mock_service_manager.get_service.side_effect = get_service_side_effect
        
        # Create blueprint and register with app
        self.customer_bp = create_customer_blueprint(self.mock_plugin)
        self.app.register_blueprint(self.customer_bp)
        
        # Create test client
        self.client = self.app.test_client()
    
    def test_redeem_page_get(self):
        """Test GET request to redeem page"""
        with patch('plugins.openai_plus_redeem.routes.customer_routes.render_template') as mock_render:
            mock_render.return_value = "Mocked template"
            
            response = self.client.get('/openai-plus-redeem/')
            
            self.assertEqual(response.status_code, 200)
            mock_render.assert_called_once()
    
    def test_redeem_page_with_shopee_params(self):
        """Test redeem page with Shopee URL parameters"""
        with patch('plugins.openai_plus_redeem.routes.customer_routes.render_template') as mock_render:
            mock_render.return_value = "Mocked template"
            
            response = self.client.get('/openai-plus-redeem/?order_id=ORDER123&source=shopee&username=testuser')
            
            self.assertEqual(response.status_code, 200)
            
            # Check that template was called with correct parameters
            call_args = mock_render.call_args
            self.assertIn('order_id', call_args[1])
            self.assertIn('source', call_args[1])
            self.assertIn('username', call_args[1])
            self.assertEqual(call_args[1]['order_id'], 'ORDER123')
            self.assertEqual(call_args[1]['source'], 'shopee')
            self.assertEqual(call_args[1]['username'], 'testuser')
    
    def test_process_redemption_success(self):
        """Test successful redemption processing"""
        # Mock successful redemption
        self.mock_order_redeem_service.process_order_redemption.return_value = {
            'success': True,
            'redemption_id': 'REDEEM123',
            'account_assigned': True,
            'requires_verification': False,
            'account_info': {
                'email': '<EMAIL>',
                'password': 'password123'
            }
        }
        
        response = self.client.post('/openai-plus-redeem/api/redeem',
                                  json={
                                      'order_id': 'ORDER123',
                                      'buyer_username': 'testuser',
                                      'sku': 'chatgpt_plus',
                                      'var_sku': 'chatgpt_5_30'
                                  })
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['redemption_id'], 'REDEEM123')
        self.assertTrue(data['account_assigned'])
    
    def test_process_redemption_missing_fields(self):
        """Test redemption with missing required fields"""
        response = self.client.post('/openai-plus-redeem/api/redeem',
                                  json={
                                      'order_id': 'ORDER123'
                                      # Missing other required fields
                                  })
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertIn('Missing required fields', data['error'])
    
    def test_process_redemption_failure(self):
        """Test failed redemption processing"""
        # Mock failed redemption
        self.mock_order_redeem_service.process_order_redemption.return_value = {
            'success': False,
            'error': 'User is in cooldown period',
            'step': 'cooldown_check'
        }
        
        response = self.client.post('/openai-plus-redeem/api/redeem',
                                  json={
                                      'order_id': 'ORDER123',
                                      'buyer_username': 'testuser',
                                      'sku': 'chatgpt_plus',
                                      'var_sku': 'chatgpt_5_30'
                                  })
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertEqual(data['error'], 'User is in cooldown period')
        self.assertEqual(data['step'], 'cooldown_check')
    
    def test_get_redemption_status_found(self):
        """Test getting redemption status - found"""
        # Mock redemption status
        self.mock_order_redeem_service.get_redemption_status.return_value = {
            'found': True,
            'redemption_id': 'REDEEM123',
            'status': 'active',
            'can_redeem': True
        }
        
        response = self.client.get('/openai-plus-redeem/api/status/REDEEM123')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertTrue(data['data']['found'])
        self.assertEqual(data['data']['redemption_id'], 'REDEEM123')
    
    def test_get_redemption_status_not_found(self):
        """Test getting redemption status - not found"""
        # Mock redemption not found
        self.mock_order_redeem_service.get_redemption_status.return_value = {
            'found': False,
            'error': 'Redemption not found'
        }
        
        response = self.client.get('/openai-plus-redeem/api/status/INVALID123')
        
        self.assertEqual(response.status_code, 404)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertEqual(data['code'], 'NOT_FOUND')
    
    def test_search_verification_code_success(self):
        """Test successful verification code search"""
        # Mock successful search
        self.mock_email_service.search_verification_code.return_value = {
            'success': True,
            'verification_id': 'VERIFY123',
            'verification_code': '123456',
            'search_attempts': 1
        }
        
        response = self.client.post('/openai-plus-redeem/api/verification/search',
                                  json={
                                      'account_email': '<EMAIL>',
                                      'redemption_id': 'REDEEM123'
                                  })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['verification_code'], '123456')
    
    def test_shopee_integration_endpoint(self):
        """Test Shopee integration endpoint"""
        # Mock successful redemption
        self.mock_order_redeem_service.process_order_redemption.return_value = {
            'success': True,
            'redemption_id': 'REDEEM123',
            'account_assigned': True,
            'account_info': {
                'email': '<EMAIL>',
                'password': 'password123'
            }
        }
        
        # Mock services for messaging
        self.mock_redemption_service = Mock()
        self.mock_account_service = Mock()
        self.mock_messaging_service = Mock()
        
        def get_service_extended(service_name):
            services = {
                'order_redeem': self.mock_order_redeem_service,
                'order_redemption': self.mock_redemption_service,
                'chatgpt_account': self.mock_account_service,
                'shopee_messaging': self.mock_messaging_service
            }
            return services.get(service_name)
        
        self.mock_service_manager.get_service.side_effect = get_service_extended
        
        response = self.client.post('/openai-plus-redeem/api/shopee/redeem',
                                  json={
                                      'order_data': {
                                          'order_sn': 'ORDER123',
                                          'buyer_username': 'testuser',
                                          'sku': 'chatgpt_plus',
                                          'var_sku': 'chatgpt_5_30'
                                      }
                                  })
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['source'], 'shopee_integration')


class TestAdminRoutes(unittest.TestCase):
    """Test admin routes"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        # Create mock plugin
        self.mock_plugin = Mock()
        self.mock_plugin.name = "openai_plus_redeem"
        self.mock_plugin.version = "1.0.0"
        
        # Create mock service manager
        self.mock_service_manager = Mock()
        self.mock_plugin.service_manager = self.mock_service_manager
        
        # Create mock services
        self.mock_account_service = Mock()
        self.mock_cooldown_service = Mock()
        self.mock_redemption_service = Mock()
        
        # Configure service manager
        def get_service_side_effect(service_name):
            services = {
                'chatgpt_account': self.mock_account_service,
                'cooldown': self.mock_cooldown_service,
                'order_redemption': self.mock_redemption_service
            }
            return services.get(service_name)
        
        self.mock_service_manager.get_service.side_effect = get_service_side_effect
        
        # Create blueprint and register with app
        self.admin_bp = create_admin_blueprint(self.mock_plugin)
        self.app.register_blueprint(self.admin_bp)
        
        # Create test client
        self.client = self.app.test_client()
        
        # Mock admin headers
        self.admin_headers = {'X-Admin-Key': 'test_admin_key'}
    
    def test_admin_dashboard_without_auth(self):
        """Test admin dashboard without authentication"""
        response = self.client.get('/admin/openai-plus-redeem/')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertEqual(data['code'], 'UNAUTHORIZED')
    
    def test_admin_dashboard_with_auth(self):
        """Test admin dashboard with authentication"""
        with patch('plugins.openai_plus_redeem.routes.admin_routes.render_template') as mock_render:
            mock_render.return_value = "Admin dashboard"
            
            response = self.client.get('/admin/openai-plus-redeem/', headers=self.admin_headers)
            
            self.assertEqual(response.status_code, 200)
            mock_render.assert_called_once()
    
    def test_get_dashboard_stats(self):
        """Test getting dashboard statistics"""
        # Mock service statistics
        self.mock_account_service.get_account_statistics.return_value = {'total': 5}
        self.mock_redemption_service.get_redemption_statistics.return_value = {'total': 10}
        self.mock_cooldown_service.get_cooldown_statistics.return_value = {'active': 2}
        self.mock_service_manager.health_check_all_services.return_value = {'status': 'healthy'}
        
        response = self.client.get('/admin/openai-plus-redeem/api/dashboard/stats',
                                 headers=self.admin_headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertIn('accounts', data['data'])
        self.assertIn('redemptions', data['data'])
        self.assertIn('cooldowns', data['data'])
    
    def test_create_account_success(self):
        """Test successful account creation"""
        # Mock account creation
        mock_account = ChatGPTAccount.create_account(
            email="<EMAIL>",
            password="password123",
            expiration_date=datetime.now() + timedelta(days=30),
            max_concurrent_users=5
        )
        self.mock_account_service.create_account.return_value = mock_account
        
        response = self.client.post('/admin/openai-plus-redeem/api/accounts',
                                  headers=self.admin_headers,
                                  json={
                                      'email': '<EMAIL>',
                                      'password': 'password123',
                                      'expiration_date': (datetime.now() + timedelta(days=30)).isoformat(),
                                      'max_concurrent_users': 5
                                  })
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['email'], '<EMAIL>')
    
    def test_create_account_missing_fields(self):
        """Test account creation with missing fields"""
        response = self.client.post('/admin/openai-plus-redeem/api/accounts',
                                  headers=self.admin_headers,
                                  json={
                                      'email': '<EMAIL>'
                                      # Missing other required fields
                                  })
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertIn('Missing required fields', data['error'])
    
    def test_set_user_cooldown_success(self):
        """Test successful cooldown setting"""
        # Mock cooldown setting
        self.mock_cooldown_service.set_user_cooldown.return_value = {
            'success': True,
            'cooldown_id': 'COOLDOWN123',
            'username': 'testuser',
            'hours': 24
        }
        
        response = self.client.post('/admin/openai-plus-redeem/api/cooldowns',
                                  headers=self.admin_headers,
                                  json={
                                      'username': 'testuser',
                                      'hours': 24,
                                      'reason': 'Test cooldown'
                                  })
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['username'], 'testuser')
    
    def test_remove_user_cooldown(self):
        """Test removing user cooldown"""
        # Mock cooldown removal
        self.mock_cooldown_service.remove_user_cooldown.return_value = {
            'success': True,
            'username': 'testuser',
            'removed_count': 1
        }
        
        response = self.client.delete('/admin/openai-plus-redeem/api/cooldowns/testuser?admin_override=true',
                                    headers=self.admin_headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['username'], 'testuser')
    
    def test_get_services_health(self):
        """Test getting services health"""
        # Mock health check
        self.mock_service_manager.health_check_all_services.return_value = {
            'status': 'healthy',
            'services': {
                'chatgpt_account': {'status': 'healthy'},
                'order_redemption': {'status': 'healthy'}
            }
        }
        
        response = self.client.get('/admin/openai-plus-redeem/api/services/health',
                                 headers=self.admin_headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['status'], 'healthy')


class TestRouteAuthentication(unittest.TestCase):
    """Test route authentication and authorization"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        # Create mock plugin
        self.mock_plugin = Mock()
        self.mock_plugin.service_manager = Mock()
        
        # Create blueprints
        self.customer_bp = create_customer_blueprint(self.mock_plugin)
        self.admin_bp = create_admin_blueprint(self.mock_plugin)
        
        self.app.register_blueprint(self.customer_bp)
        self.app.register_blueprint(self.admin_bp)
        
        self.client = self.app.test_client()
    
    def test_customer_endpoints_no_auth_required(self):
        """Test that customer endpoints don't require authentication"""
        # Customer endpoints should be accessible without authentication
        response = self.client.get('/openai-plus-redeem/')
        # Should not return 401 (may return other errors due to missing services)
        self.assertNotEqual(response.status_code, 401)
    
    def test_admin_endpoints_require_auth(self):
        """Test that admin endpoints require authentication"""
        # Admin endpoints should require authentication
        response = self.client.get('/admin/openai-plus-redeem/api/dashboard/stats')
        self.assertEqual(response.status_code, 401)
        
        response = self.client.post('/admin/openai-plus-redeem/api/accounts')
        self.assertEqual(response.status_code, 401)
        
        response = self.client.get('/admin/openai-plus-redeem/api/cooldowns')
        self.assertEqual(response.status_code, 401)
    
    @patch('plugins.openai_plus_redeem.routes.admin_routes.session')
    def test_session_based_auth(self, mock_session):
        """Test session-based authentication"""
        # Mock session with admin login
        mock_session.__contains__ = lambda self, key: key == 'admin_logged_in'
        mock_session.get.return_value = 'admin_user'
        
        # Mock service manager
        self.mock_plugin.service_manager.get_service.return_value = Mock()
        
        response = self.client.get('/admin/openai-plus-redeem/api/cooldowns')
        
        # Should not return 401 with valid session
        self.assertNotEqual(response.status_code, 401)


class TestErrorHandling(unittest.TestCase):
    """Test error handling in routes"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        
        # Create mock plugin with failing services
        self.mock_plugin = Mock()
        self.mock_plugin.service_manager = Mock()
        self.mock_plugin.service_manager.get_service.side_effect = RuntimeError("Service not available")
        
        # Create blueprints
        self.customer_bp = create_customer_blueprint(self.mock_plugin)
        self.admin_bp = create_admin_blueprint(self.mock_plugin)
        
        self.app.register_blueprint(self.customer_bp)
        self.app.register_blueprint(self.admin_bp)
        
        self.client = self.app.test_client()
    
    def test_service_unavailable_error(self):
        """Test handling of service unavailable errors"""
        response = self.client.post('/openai-plus-redeem/api/redeem',
                                  json={
                                      'order_id': 'ORDER123',
                                      'buyer_username': 'testuser',
                                      'sku': 'chatgpt_plus',
                                      'var_sku': 'chatgpt_5_30'
                                  })
        
        self.assertEqual(response.status_code, 500)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'error')
        self.assertEqual(data['code'], 'INTERNAL_ERROR')
    
    def test_invalid_json_handling(self):
        """Test handling of invalid JSON"""
        response = self.client.post('/openai-plus-redeem/api/redeem',
                                  data='invalid json',
                                  content_type='application/json')
        
        # Should handle gracefully
        self.assertIn(response.status_code, [400, 500])


if __name__ == '__main__':
    unittest.main()
