# ShopeeAPI Environment Configuration

# General log level (DEBUG, INFO, WAR<PERSON>NG, ERROR, CRITICAL)
# Default: WARNING (to reduce log spam)
LOG_LEVEL=WARNING

# WebSocket service log level (DEBUG, INFO, WARNING, ERROR, <PERSON>IT<PERSON>AL)
# Default: ERROR (WebSocket messages are very frequent)
WEBSOCKET_LOG_LEVEL=ERROR

# Webhook service log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
# Default: ERROR (Webhook calls are frequent)
WEBHOOK_LOG_LEVEL=ERROR

# Server port
PORT=8000

# Development mode (set to true for more verbose logging)
# When set to true, all log levels will be set to DEBUG
DEBUG_MODE=false
