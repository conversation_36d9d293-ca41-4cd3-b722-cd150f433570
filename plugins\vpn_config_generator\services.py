"""
Services for VPN Config Generator Plugin
"""


import json
import os
import logging
import random
import uuid
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import traceback

from .models import (VPNConfigRequest, VPNConfigResponse, VPNAPIConfig, ConfigGeneratorSettings,
                     ConfigTemplate, ChatCommand, WebhookMessage, CommandResponse, WebhookConfig, VPNCommandConfig,
                     VPNTelco, VPNPlan, TelcoConfigManager, VPNUser, VPNOrderRequest, VPNOrderResponse,
                     VPNConfigurationRequest, SKURestriction, RedemptionLink, RedemptionLinkRequest, RedemptionLinkResponse,
                     ChatTemplateConfig, BulkCreationRequest, BulkCreationResult, RedemptionAnalytics)

# Import VPN strategy factory for SKU-based server selection
try:
    from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
    VPN_STRATEGY_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("VPN strategy factory imported successfully")
except ImportError as e:
    VPN_STRATEGY_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"VPN strategy factory not available: {e}")
    # Create a dummy class to prevent errors
    class VPNStrategyFactory:
        @classmethod
        def get_server_tags_for_sku(cls, sku: str) -> List[str]:
            return []

        @classmethod
        def is_vpn_product(cls, sku: str) -> bool:
            return True

logger = logging.getLogger(__name__)


class VPNConfigGeneratorService:
    """Service for generating VPN configurations"""
    
    def __init__(self, plugin_dir: str, app_config: Dict[str, Any], plugin_manager=None):
        self.plugin_dir = plugin_dir
        self.app_config = app_config
        self.plugin_manager = plugin_manager

        # Use configs directory for persistent storage (Docker mount compatible)
        self.configs_dir = os.path.join(os.path.dirname(os.path.dirname(plugin_dir)), 'configs', 'plugins', 'vpn_config_generator')
        os.makedirs(self.configs_dir, exist_ok=True)

        # Config files in persistent directory
        self.config_file = os.path.join(self.configs_dir, 'config.json')
        self.templates_file = os.path.join(self.configs_dir, 'templates.json')
        self.telco_configs_file = os.path.join(self.configs_dir, 'telco_configs.json')
        self.generated_configs_file = os.path.join(self.configs_dir, 'generated_configs.json')

        # Legacy files in plugin directory (for migration)
        self.legacy_config_file = os.path.join(plugin_dir, 'config.json')
        self.legacy_templates_file = os.path.join(plugin_dir, 'templates.json')
        self.legacy_telco_configs_file = os.path.join(plugin_dir, 'telco_configs.json')

        self._api_config = VPNAPIConfig()
        self._generator_settings = ConfigGeneratorSettings()
        self._templates: List[ConfigTemplate] = []  # Legacy support
        self._telco_manager = TelcoConfigManager()

        # Migrate legacy files if they exist
        self._migrate_legacy_files()

        self.load_config()
        self.load_templates()  # Legacy support
        self.load_telco_configs()
        self.load_generated_configs()  # Load generated configs

        # Try to get VPN plugin service
        self._vpn_api_service = None
        self._init_vpn_service()

    def _migrate_legacy_files(self):
        """Migrate legacy config files from plugin directory to configs directory"""
        try:
            import shutil

            # Migrate config.json
            if os.path.exists(self.legacy_config_file) and not os.path.exists(self.config_file):
                logger.info(f"Migrating VPN config from {self.legacy_config_file} to {self.config_file}")
                shutil.copy2(self.legacy_config_file, self.config_file)
                logger.info("VPN config migration completed")

            # Migrate templates.json
            if os.path.exists(self.legacy_templates_file) and not os.path.exists(self.templates_file):
                logger.info(f"Migrating VPN templates from {self.legacy_templates_file} to {self.templates_file}")
                shutil.copy2(self.legacy_templates_file, self.templates_file)
                logger.info("VPN templates migration completed")

            # Migrate telco_configs.json
            if os.path.exists(self.legacy_telco_configs_file) and not os.path.exists(self.telco_configs_file):
                logger.info(f"Migrating VPN telco configs from {self.legacy_telco_configs_file} to {self.telco_configs_file}")
                shutil.copy2(self.legacy_telco_configs_file, self.telco_configs_file)
                logger.info("VPN telco configs migration completed")

        except Exception as e:
            logger.error(f"Error during VPN legacy file migration: {e}")
    
    def _init_vpn_service(self):
        """Initialize VPN API service from VPN plugin"""
        try:
            # First try to get the VPN plugin instance directly from plugin manager
            if self.plugin_manager and hasattr(self.plugin_manager, 'get_plugin'):
                vpn_plugin = self.plugin_manager.get_plugin('vpn')
                if vpn_plugin and hasattr(vpn_plugin, 'api_service'):
                    self._vpn_api_service = vpn_plugin.api_service
                    logger.info("Successfully got VPN API service from VPN plugin instance")
                    # Test the connection
                    if self._vpn_api_service:
                        test_result = self._vpn_api_service.test_connection()
                        if test_result and test_result.get('success'):
                            logger.info("VPN API service connection test successful")
                            return
                        else:
                            logger.warning(f"VPN API service connection test failed: {test_result}")
                            self._vpn_api_service = None
                else:
                    logger.warning("VPN plugin not found or doesn't have api_service")

            # Fallback: Try to create new VPN API service instance
            from plugins.vpn.services.vpn_api_service import VPNAPIService

            # Get VPN plugin config from plugin manager
            vpn_config = None
            if self.plugin_manager and hasattr(self.plugin_manager, 'plugin_configs'):
                vpn_config = self.plugin_manager.plugin_configs.get('vpn', {})
                logger.info(f"Retrieved VPN config from plugin manager: {vpn_config}")

            if vpn_config:
                self._vpn_api_service = VPNAPIService(vpn_config)
                # Test the connection
                test_result = self._vpn_api_service.test_connection()
                if test_result and test_result.get('success'):
                    logger.info("Successfully initialized VPN API service from VPN plugin config")
                else:
                    logger.warning(f"VPN API service connection test failed: {test_result}")
                    self._vpn_api_service = None
            else:
                logger.warning("VPN plugin config not found in plugin manager")

        except ImportError as e:
            logger.warning(f"Could not import VPN plugin service: {e}")
            self._vpn_api_service = None
        except Exception as e:
            logger.error(f"Error initializing VPN API service: {e}")
            self._vpn_api_service = None
    
    def load_config(self):
        """Load plugin configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    api_data = data.get('api_config', {})
                    self._api_config = VPNAPIConfig.from_dict(api_data)
                    logger.info(f"VPNConfigGeneratorService: Loaded api_config: {self._api_config.to_dict()}")
                    
                    settings_data = data.get('generator_settings', {})
                    self._generator_settings = ConfigGeneratorSettings.from_dict(settings_data)
                    logger.info(f"VPNConfigGeneratorService: Loaded generator_settings: {self._generator_settings.to_dict()}")
                    
                logger.info("Loaded VPN config generator configuration")
            else:
                # Create default config
                logger.warning(f"VPNConfigGeneratorService: Config file {self.config_file} not found. Creating default.")
                self.save_config() # This will also log the default saved config
        except Exception as e:
            logger.error(f"Error loading config for VPNConfigGeneratorService: {e}")
            logger.error(f"VPNConfigGeneratorService: _api_config state on error: {getattr(self, '_api_config', 'Not initialized')}")
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    def save_config(self):
        """Save plugin configuration"""
        try:
            data = {
                'api_config': self._api_config.to_dict(),
                'generator_settings': self._generator_settings.to_dict()
            }
            
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved VPN config generator configuration")
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def load_telco_configs(self):
        """Load telco configurations from file"""
        try:
            if os.path.exists(self.telco_configs_file):
                with open(self.telco_configs_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._telco_manager = TelcoConfigManager.from_dict(data)
                logger.info(f"Loaded {len(self._telco_manager.telcos)} telco configurations")
            else:
                # Create default telco configurations
                self._create_default_telco_configs()
                self.save_telco_configs()
        except Exception as e:
            logger.error(f"Error loading telco configs: {e}")
            self._create_default_telco_configs()

    def save_telco_configs(self):
        """Save telco configurations to file"""
        try:
            data = self._telco_manager.to_dict()
            with open(self.telco_configs_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved telco configurations")
        except Exception as e:
            logger.error(f"Error saving telco configs: {e}")

    def _create_default_telco_configs(self):
        """Create default telco configurations"""
        # This will be empty initially - configs will be loaded from telco_configs.json
        self._telco_manager = TelcoConfigManager()

    def load_generated_configs(self):
        """Load generated VPN configurations from file"""
        try:
            if os.path.exists(self.generated_configs_file):
                with open(self.generated_configs_file, 'r', encoding='utf-8') as f:
                    self._generated_configs = json.load(f)
                logger.info(f"Loaded {len(self._generated_configs)} generated VPN configs")
            else:
                self._generated_configs = {}
                logger.info("No existing generated configs file, starting fresh")
        except Exception as e:
            logger.error(f"Error loading generated configs: {e}")
            self._generated_configs = {}

    def save_generated_config(self, username: str, config_info: Dict[str, Any]) -> bool:
        """Save a generated VPN configuration"""
        try:
            # Add timestamp if not present
            if 'timestamp' not in config_info:
                config_info['timestamp'] = datetime.now().isoformat()
            
            # Save by username
            self._generated_configs[username] = config_info
            
            # Write to file
            with open(self.generated_configs_file, 'w', encoding='utf-8') as f:
                json.dump(self._generated_configs, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved VPN config for user: {username}")
            return True
        except Exception as e:
            logger.error(f"Error saving generated config: {e}")
            return False

    def get_generated_configs(self) -> Dict[str, Any]:
        """Get all generated VPN configurations"""
        return self._generated_configs.copy()

    def update_config_status(self, client_id: str, active: bool) -> bool:
        """Update the active status of a VPN configuration by client_id or numeric_id"""
        try:
            # Convert to int if possible (for numeric IDs)
            try:
                numeric_id = int(client_id)
                # Find config by numeric_id
                for username, config in self._generated_configs.items():
                    if config.get('numeric_id') == numeric_id:
                        config['active'] = active
                        config['updated_timestamp'] = datetime.now().isoformat()
                        
                        # Save to file
                        with open(self.generated_configs_file, 'w', encoding='utf-8') as f:
                            json.dump(self._generated_configs, f, indent=2, ensure_ascii=False)
                        
                        logger.info(f"Updated VPN config status for numeric_id {numeric_id}: active={active}")
                        return True
            except ValueError:
                # Not a numeric ID, try as string client_id
                for username, config in self._generated_configs.items():
                    if config.get('client_id') == client_id:
                        config['active'] = active
                        config['updated_timestamp'] = datetime.now().isoformat()
                        
                        # Save to file
                        with open(self.generated_configs_file, 'w', encoding='utf-8') as f:
                            json.dump(self._generated_configs, f, indent=2, ensure_ascii=False)
                        
                        logger.info(f"Updated VPN config status for client_id {client_id}: active={active}")
                        return True
            
            logger.warning(f"No config found with client_id or numeric_id: {client_id}")
            return False
        except Exception as e:
            logger.error(f"Error updating config status: {e}")
            return False

    def update_config_expiry(self, client_id: str, new_expiry_date: str) -> bool:
        """Update the expiry date of a VPN configuration by client_id or numeric_id"""
        try:
            # Convert to int if possible (for numeric IDs)
            try:
                numeric_id = int(client_id)
                # Find config by numeric_id
                for username, config in self._generated_configs.items():
                    if config.get('numeric_id') == numeric_id:
                        config['expired_date'] = new_expiry_date
                        config['updated_timestamp'] = datetime.now().isoformat()
                        # Mark as active if expiry date is in the future
                        try:
                            exp_date = datetime.strptime(new_expiry_date, '%d-%m-%Y')
                            config['active'] = exp_date > datetime.now()
                        except:
                            pass
                        
                        # Save to file
                        with open(self.generated_configs_file, 'w', encoding='utf-8') as f:
                            json.dump(self._generated_configs, f, indent=2, ensure_ascii=False)
                        
                        logger.info(f"Updated VPN config expiry for numeric_id {numeric_id}: {new_expiry_date}")
                        return True
            except ValueError:
                # Not a numeric ID, try as string client_id
                for username, config in self._generated_configs.items():
                    if config.get('client_id') == client_id:
                        config['expired_date'] = new_expiry_date
                        config['updated_timestamp'] = datetime.now().isoformat()
                        # Mark as active if expiry date is in the future
                        try:
                            exp_date = datetime.strptime(new_expiry_date, '%d-%m-%Y')
                            config['active'] = exp_date > datetime.now()
                        except:
                            pass
                        
                        # Save to file
                        with open(self.generated_configs_file, 'w', encoding='utf-8') as f:
                            json.dump(self._generated_configs, f, indent=2, ensure_ascii=False)
                        
                        logger.info(f"Updated VPN config expiry for client_id {client_id}: {new_expiry_date}")
                        return True
            
            logger.warning(f"No config found with client_id or numeric_id: {client_id}")
            return False
        except Exception as e:
            logger.error(f"Error updating config expiry: {e}")
            return False

    def calculate_remaining_days(self, config_info: Dict[str, Any]) -> int:
        """Calculate remaining days for a configuration"""
        try:
            if not config_info.get('server_bound', False):
                # If not bound to server, return original days
                return config_info.get('days_remaining', config_info.get('days_total', 0))

            # Parse expiry date
            expired_date_str = config_info.get('expired_date')
            if not expired_date_str:
                return 0

            try:
                expired_date = datetime.strptime(expired_date_str, '%d-%m-%Y')
                remaining_days = (expired_date - datetime.now()).days
                return max(0, remaining_days)  # Don't return negative days
            except ValueError:
                logger.warning(f"Invalid date format in config: {expired_date_str}")
                return 0

        except Exception as e:
            logger.error(f"Error calculating remaining days: {e}")
            return 0

    def unbind_config_from_server(self, client_id: str, preserve_days: bool = True) -> Dict[str, Any]:
        """Unbind a configuration from its server while optionally preserving remaining days"""
        try:
            config_found = None
            config_username = None

            # Find the configuration
            for username, config in self._generated_configs.items():
                if (config.get('client_id') == client_id or
                    str(config.get('numeric_id')) == str(client_id)):
                    config_found = config
                    config_username = username
                    break

            if not config_found:
                return {'success': False, 'error': f'Configuration not found: {client_id}'}

            if not config_found.get('server_bound', False):
                return {'success': False, 'error': 'Configuration is not bound to a server'}

            # Calculate remaining days if preserving
            remaining_days = 0
            if preserve_days:
                remaining_days = self.calculate_remaining_days(config_found)

            # Update configuration
            config_found.update({
                'server_bound': False,
                'server_id': None,
                'server_name': 'Unbound',
                'days_remaining': remaining_days if preserve_days else 0,
                'unbound_at': datetime.now().isoformat(),
                'unbind_preserved_days': preserve_days,
                'can_rebind': True,
                'updated_timestamp': datetime.now().isoformat()
            })

            # Save to file
            with open(self.generated_configs_file, 'w', encoding='utf-8') as f:
                json.dump(self._generated_configs, f, indent=2, ensure_ascii=False)

            logger.info(f"Unbound config {client_id} from server. Preserved days: {remaining_days if preserve_days else 'No'}")

            return {
                'success': True,
                'client_id': client_id,
                'remaining_days': remaining_days,
                'preserved_days': preserve_days,
                'message': f'Configuration unbound successfully. {"Remaining days preserved: " + str(remaining_days) if preserve_days else "Days not preserved"}'
            }

        except Exception as e:
            logger.error(f"Error unbinding config: {e}")
            return {'success': False, 'error': f'Error unbinding configuration: {str(e)}'}

    def rebind_config_to_server(self, client_id: str, new_server_id: int, extend_days: int = 0) -> Dict[str, Any]:
        """Rebind a configuration to a new server, optionally extending the validity"""
        try:
            config_found = None
            config_username = None

            # Find the configuration
            for username, config in self._generated_configs.items():
                if (config.get('client_id') == client_id or
                    str(config.get('numeric_id')) == str(client_id)):
                    config_found = config
                    config_username = username
                    break

            if not config_found:
                return {'success': False, 'error': f'Configuration not found: {client_id}'}

            # Get server information
            try:
                if self._vpn_api_service:
                    servers = self._vpn_api_service.get_servers()
                    server_info = next((s for s in servers if s.get('id') == new_server_id), None)
                    server_name = server_info.get('name', f'Server {new_server_id}') if server_info else f'Server {new_server_id}'
                else:
                    server_name = f'Server {new_server_id}'
            except:
                server_name = f'Server {new_server_id}'

            # Calculate new expiry date
            current_remaining = config_found.get('days_remaining', 0)
            total_days = current_remaining + extend_days
            new_expiry_date = (datetime.now() + timedelta(days=total_days)).strftime('%d-%m-%Y')

            # Update configuration
            config_found.update({
                'server_bound': True,
                'server_id': new_server_id,
                'server_name': server_name,
                'days_remaining': total_days,
                'expired_date': new_expiry_date,
                'rebound_at': datetime.now().isoformat(),
                'days_extended': extend_days,
                'active': True,
                'updated_timestamp': datetime.now().isoformat()
            })

            # Save to file
            with open(self.generated_configs_file, 'w', encoding='utf-8') as f:
                json.dump(self._generated_configs, f, indent=2, ensure_ascii=False)

            logger.info(f"Rebound config {client_id} to server {new_server_id}. Total days: {total_days}")

            return {
                'success': True,
                'client_id': client_id,
                'server_id': new_server_id,
                'server_name': server_name,
                'total_days': total_days,
                'extended_days': extend_days,
                'new_expiry_date': new_expiry_date,
                'message': f'Configuration rebound to {server_name}. Total validity: {total_days} days'
            }

        except Exception as e:
            logger.error(f"Error rebinding config: {e}")
            return {'success': False, 'error': f'Error rebinding configuration: {str(e)}'}

    def get_telco_plan_template(self, telco_id: str, plan_id: str) -> Optional[str]:
        """Get the template for a specific telco and plan"""
        plan = self._telco_manager.get_plan(telco_id, plan_id)
        if plan:
            return plan.template
        return None

    def generate_config_from_telco_plan(self, telco_id: str, plan_id: str, variables: Dict[str, str]) -> str:
        """Generate configuration using telco plan template and variables"""
        try:
            template = self.get_telco_plan_template(telco_id, plan_id)
            if not template:
                logger.warning(f"No template found for telco: {telco_id}, plan: {plan_id}")
                return ""

            config_string = template

            # Replace variables in template
            for key, value in variables.items():
                placeholder = f"{{{key}}}"
                config_string = config_string.replace(placeholder, str(value))

            return config_string

        except Exception as e:
            logger.error(f"Error generating config from telco plan: {e}")
            return ""

    def generate_config(self, request: VPNConfigRequest) -> VPNConfigResponse:
        """Generate VPN configuration"""
        try:
            if not self._api_config.enabled:
                return VPNConfigResponse(
                    success=False,
                    error="VPN config generation is disabled"
                )
            
            # Try using VPN plugin API first
            if self._api_config.use_vpn_plugin_api:
                try:
                    # Ensure VPN service is initialized
                    if not self._vpn_api_service:
                        logger.info("VPN API service not initialized, trying to initialize...")
                        self._init_vpn_service()
                    
                    if self._vpn_api_service:
                        logger.info("Attempting VPN plugin API generation")
                        result = self._generate_via_vpn_plugin(request)
                        if result.success:
                            return result
                        else:
                            logger.warning(f"VPN plugin API failed: {result.error}, trying fallback")
                    else:
                        logger.warning("VPN API service not available, trying fallback")
                except Exception as e:
                    logger.error(f"VPN plugin API error: {e}, trying fallback")

            # Fallback to direct API call
            if self._api_config.fallback_api_endpoint:
                try:
                    logger.info("Attempting fallback API generation")
                    result = self._generate_via_fallback_api(request)
                    if result.success:
                        return result
                    else:
                        logger.warning(f"Fallback API failed: {result.error}, trying template generation")
                except Exception as e:
                    logger.error(f"Fallback API error: {e}, trying template generation")

            # Final fallback to template-based generation
            logger.info("Using template-based generation as final fallback")
            return self._generate_via_template(request)
                
        except Exception as e:
            logger.error(f"Error generating VPN config: {e}")
            return VPNConfigResponse(
                success=False,
                error=f"Configuration generation failed: {str(e)}"
            )
    
    def _generate_via_vpn_plugin(self, request: VPNConfigRequest) -> VPNConfigResponse:
        """Generate config using VPN plugin API"""
        try:
            # Check if VPN API service is available
            if not self._vpn_api_service:
                logger.warning("VPN API service not available, falling back to template generation")
                return self._generate_via_template(request)

            # Handle server selection - prioritize user selection over auto-selection
            server_id = None
            selected_server = None

            # Check if user provided a specific server ID
            if request.server and request.server != 'auto':
                try:
                    server_id = int(request.server)
                    # Get server details for the selected server
                    servers = self._vpn_api_service.get_servers()
                    if servers:
                        # Handle different response formats safely
                        servers_list = []
                        if isinstance(servers, list):
                            servers_list = servers
                        elif isinstance(servers, dict) and 'data' in servers and servers['data']:
                            servers_list = servers['data'] if isinstance(servers['data'], list) else []

                        selected_server = next((s for s in servers_list if s.get('id') == server_id), None)
                        if selected_server:
                            logger.info(f"User selected server ID {server_id} ({selected_server.get('name', 'Unknown')})")
                        else:
                            logger.warning(f"User selected server ID {server_id} not found, falling back to SKU-based selection")
                            server_id = None
                    else:
                        logger.warning("No servers available from VPN API, falling back to template generation")
                        return self._generate_via_template(request)
                except ValueError:
                    logger.warning(f"Invalid server ID provided: {request.server}, falling back to SKU-based selection")
                except Exception as e:
                    logger.error(f"Error getting servers from VPN API: {e}, falling back to template generation")
                    return self._generate_via_template(request)

            # If no valid user selection, try SKU-based server selection
            if not server_id and (request.var_sku or request.sku):
                sku_to_use = request.var_sku or request.sku
                logger.info(f"Attempting SKU-based server selection for SKU: {sku_to_use}")
                selected_server = self._select_server_by_sku(sku_to_use)
                if selected_server:
                    server_id = selected_server.get('id')
                    logger.info(f"SKU-based selection chose server ID {server_id} ({selected_server.get('name', 'Unknown')})")
                else:
                    logger.warning(f"SKU-based server selection failed for SKU {sku_to_use}, falling back to manual server selection")

            # Fallback to manual server selection if SKU-based selection failed or no SKU provided
            if not server_id:
                # Handle "auto" server selection for chat commands
                if request.server.lower() == 'auto':
                    # For chat commands with "auto", select any available server
                    if self._vpn_api_service:
                        try:
                            servers = self._vpn_api_service.get_servers()
                            # Find first online server
                            online_servers = [s for s in servers if s.get('status') == 'online']
                            if online_servers:
                                server_id = online_servers[0].get('id')
                                logger.info(f"Auto-selected server ID {server_id} ({online_servers[0].get('name', 'Unknown')}) for chat command")
                            elif servers:
                                # If no online servers, use first available server
                                server_id = servers[0].get('id')
                                logger.info(f"Auto-selected server ID {server_id} ({servers[0].get('name', 'Unknown')}) for chat command (no online servers)")
                        except Exception as e:
                            logger.warning(f"Failed to auto-select server for chat command: {e}")

                # If still no server_id, try manual parsing
                if not server_id:
                    server_id = self._parse_server_id(request.server)

                # Final check - if still no server_id, return error
                if not server_id:
                    return VPNConfigResponse(
                        success=False,
                        error=f"Invalid server format: {request.server} and no valid server selection available"
                    )
            
            # Calculate expiration date (VPN API expects DD-MM-YYYY format)
            days = int(request.days)
            expired_date = (datetime.now() + timedelta(days=days)).strftime('%d-%m-%Y')

            # Check if user already has a client on this server
            user_email = f"user-{request.username}@shopee.local"
            existing_client = None

            try:
                existing_client = self._vpn_api_service.get_client_by_email(user_email, server_id)
                if existing_client:
                    logger.info(f"Found existing client for {user_email} on server {server_id}: {existing_client.get('id')}")
            except Exception as e:
                logger.warning(f"Error checking for existing client: {e}")

            if existing_client:
                # Update existing client with new plan/expiry instead of creating new one
                client_id = existing_client.get('id')
                numeric_id = existing_client.get('id')  # Use the same ID

                # Update the existing client's expiry and description
                update_data = {
                    "expired_date": expired_date,
                    "description": f"Updated via VPN Config Generator - {request.telco} {request.plan}",
                    "notes": f"Plan changed to {request.telco} {request.plan} plan"
                }

                try:
                    update_result = self._vpn_api_service.update_client(client_id, update_data)
                    if update_result:
                        logger.info(f"Successfully updated existing client {client_id}")
                    else:
                        logger.warning(f"Failed to update existing client {client_id}, but will continue with existing config")
                except Exception as e:
                    logger.warning(f"Error updating existing client: {e}, but will continue with existing config")

            else:
                # Create new client via VPN API
                # Note: VPN API expects specific field format based on the API schema
                import uuid
                client_data = {
                    "email": user_email,
                    "shopee_username": request.username,
                    "expired_date": expired_date,
                    "description": f"Generated via VPN Config Generator - {request.telco} {request.plan}",
                    "notes": f"Auto-generated for {request.telco} {request.plan} plan",
                    "server_id": server_id,
                    "client_id": str(uuid.uuid4())  # Generate valid UUID
                }

                # Log the client data for debugging
                logger.info(f"Creating VPN client with data: {client_data}")

                result = self._vpn_api_service.create_client(client_data)

                # Add debug logging for VPN plugin API
                logger.info(f"VPN plugin API create_client result: {result}")

                # Handle None result (API error)
                if result is None:
                    return VPNConfigResponse(
                        success=False,
                        error="VPN API request failed. Please check server connectivity and credentials."
                    )

                # Handle error response
                if isinstance(result, dict) and 'error' in result:
                    error_msg = result['error']

                    # Check for specific server-side errors and fall back to template generation
                    if any(error_pattern in error_msg.lower() for error_pattern in [
                        'coroutine', 'invalid json', 'expecting value', 'config file'
                    ]):
                        logger.error(f"VPN API server error detected: {error_msg} - falling back to template generation")
                        return self._generate_via_template(request)

                    # Check for validation errors that might indicate API format issues
                    if 'validation_error' in error_msg.lower() or 'client_id must be a valid uuid' in error_msg.lower():
                        logger.error(f"VPN API validation error: {error_msg} - falling back to template generation")
                        return self._generate_via_template(request)

                    return VPNConfigResponse(
                        success=False,
                        error=error_msg
                    )

                # Handle successful response for new client
                if isinstance(result, dict):
                    client_id = result.get('client_id', client_data.get('client_id'))
                    numeric_id = result.get('id')
                else:
                    # Fallback if result format is unexpected
                    client_id = client_data.get('client_id')
                    numeric_id = None

            # Now handle both existing and new client cases
            # For both existing and new clients, always use template generation
            if existing_client or (not existing_client and isinstance(result, dict)):
                if existing_client:
                    logger.info("Using existing client, generating config using template")
                else:
                    logger.info("Created new client, generating config using template")

                # Always generate using template with the actual client_id
                template_result = self._generate_via_template(request, existing_client_id=client_id)
                if template_result.success:
                    # The template already uses the correct client_id, no need to replace
                    config_with_real_uuid = template_result.config

                    # Calculate remaining days and server binding info
                    days_total = int(request.days)
                    created_date_obj = datetime.now()

                    # Save generated config info with remaining days tracking
                    config_info = {
                        'client_id': client_id,
                        'numeric_id': numeric_id,  # Use the numeric_id we determined above
                        'server_id': server_id,
                        'server_name': selected_server.get('name', 'Unknown') if selected_server else 'Auto-selected',
                        'telco': request.telco,
                        'plan': request.plan,
                        'created_date': created_date_obj.strftime('%d-%m-%Y'),
                        'expired_date': expired_date,
                        'username': request.username,
                        'sku': request.sku,
                        'var_sku': request.var_sku,
                        'active': True,
                        # New fields for remaining days tracking
                        'days_total': days_total,
                        'days_remaining': days_total,
                        'server_bound': True,
                        'bound_at': created_date_obj.isoformat(),
                        'can_unbind': True,
                        'unbind_preserves_days': True,
                        'config_type': 'template'
                    }
                    self.save_generated_config(request.username, config_info)

                    action_message = "Configuration updated successfully (reusing existing client)" if existing_client else "Configuration generated successfully (using template with VPN plugin API client ID)"
                    return VPNConfigResponse(
                        success=True,
                        config=config_with_real_uuid,
                        client_id=client_id,
                        numeric_id=numeric_id,
                        created_date=datetime.now().strftime('%d-%m-%Y'),
                        expired_date=expired_date,
                        message=action_message
                    )

                # If template generation fails, return the template error
                template_error = template_result.error if hasattr(template_result, 'error') else "Template generation failed"
                return VPNConfigResponse(
                    success=False,
                    error=template_error
                )
            else:
                return VPNConfigResponse(
                    success=False,
                    error=f"Unexpected response format from VPN API: {type(result)}"
                )
            
        except Exception as e:
            logger.error(f"Error generating config via VPN plugin: {e}")
            return VPNConfigResponse(
                success=False,
                error=f"VPN plugin error: {str(e)}"
            )
    
    def _generate_via_fallback_api(self, request: VPNConfigRequest) -> VPNConfigResponse:
        """Generate config using fallback API (direct VPN API)"""
        try:
            import requests
            from datetime import datetime, timedelta

            # Handle server selection - prioritize user selection over auto-selection
            server_id = None
            selected_server = None

            # Check if user provided a specific server ID
            if request.server and request.server != 'auto':
                try:
                    server_id = int(request.server)
                    # Get server details for the selected server
                    if self._vpn_api_service:
                        servers = self._vpn_api_service.get_servers()
                        selected_server = next((s for s in servers if s.get('id') == server_id), None)
                        if selected_server:
                            logger.info(f"User selected server ID {server_id} ({selected_server.get('name', 'Unknown')})")
                        else:
                            logger.warning(f"User selected server ID {server_id} not found, falling back to SKU-based selection")
                            server_id = None
                except ValueError:
                    logger.warning(f"Invalid server ID provided: {request.server}, falling back to SKU-based selection")

            # If no valid user selection, try SKU-based server selection
            if not server_id and (request.var_sku or request.sku):
                sku_to_use = request.var_sku or request.sku
                logger.info(f"Attempting SKU-based server selection for SKU: {sku_to_use}")
                selected_server = self._select_server_by_sku(sku_to_use)
                if selected_server:
                    server_id = selected_server.get('id')
                    logger.info(f"SKU-based selection chose server ID {server_id} ({selected_server.get('name', 'Unknown')})")
                else:
                    logger.warning(f"SKU-based server selection failed for SKU {sku_to_use}, falling back to manual server selection")

            # Fallback to manual server selection if SKU-based selection failed or no SKU provided
            if not server_id:
                # Handle "auto" server selection for chat commands
                if request.server.lower() == 'auto':
                    # For chat commands with "auto", select any available server
                    if self._vpn_api_service:
                        try:
                            servers = self._vpn_api_service.get_servers()
                            # Find first online server
                            online_servers = [s for s in servers if s.get('status') == 'online']
                            if online_servers:
                                server_id = online_servers[0].get('id')
                                selected_server = online_servers[0]
                                logger.info(f"Auto-selected server ID {server_id} ({online_servers[0].get('name', 'Unknown')}) for chat command")
                            elif servers:
                                # If no online servers, use first available server
                                server_id = servers[0].get('id')
                                selected_server = servers[0]
                                logger.info(f"Auto-selected server ID {server_id} ({servers[0].get('name', 'Unknown')}) for chat command (no online servers)")
                        except Exception as e:
                            logger.warning(f"Failed to auto-select server for chat command: {e}")

                # If still no server_id, try manual parsing
                if not server_id:
                    server_id = self._parse_server_id(request.server)

                # Final check - if still no server_id, return error
                if not server_id:
                    return VPNConfigResponse(
                        success=False,
                        error=f"Invalid server format: {request.server} and no valid server selection available"
                    )

            # Calculate expiration date (VPN API expects DD-MM-YYYY format)
            days = int(request.days)
            expired_date = (datetime.now() + timedelta(days=days)).strftime('%d-%m-%Y')

            # First, authenticate with the API
            session = requests.Session()
            session.headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            })

            # Login to get access token
            login_response = session.post(
                f"{self._api_config.fallback_api_endpoint}/api/v1/auth/login",
                json={
                    "username": self._api_config.fallback_username,
                    "password": self._api_config.fallback_password
                },
                timeout=self._api_config.timeout
            )

            if login_response.status_code != 200:
                return VPNConfigResponse(
                    success=False,
                    error=f"Authentication failed: {login_response.status_code}"
                )

            auth_data = login_response.json()
            access_token = auth_data.get('access_token')
            if not access_token:
                return VPNConfigResponse(
                    success=False,
                    error="No access token received"
                )

            # Update session with token
            session.headers.update({
                'Authorization': f'Bearer {access_token}'
            })

            # Check if user already has a client on this server
            user_email = f"user-{request.username}@shopee.local"
            existing_client = None

            try:
                # Check for existing client using the fallback API
                check_response = session.get(
                    f"{self._api_config.fallback_api_endpoint}/api/v1/clients/by-email/{user_email}",
                    params={'server_id': server_id},
                    timeout=self._api_config.timeout
                )
                if check_response.status_code == 200:
                    existing_client = check_response.json()
                    logger.info(f"Found existing client for {user_email} on server {server_id}: {existing_client.get('id')}")
            except Exception as e:
                logger.warning(f"Error checking for existing client: {e}")

            if existing_client:
                # Update existing client with new plan/expiry instead of creating new one
                client_id = existing_client.get('client_id', existing_client.get('id'))
                numeric_id = existing_client.get('id')

                # Update the existing client's expiry and description
                update_payload = {
                    "expired_date": expired_date,
                    "description": f"Updated via VPN Config Generator - {request.telco} {request.plan}",
                    "notes": f"Plan changed to {request.telco} {request.plan} plan"
                }

                try:
                    update_response = session.put(
                        f"{self._api_config.fallback_api_endpoint}/api/v1/clients/{numeric_id}",
                        json=update_payload,
                        timeout=self._api_config.timeout
                    )
                    if update_response.status_code == 200:
                        logger.info(f"Successfully updated existing client {numeric_id}")
                    else:
                        logger.warning(f"Failed to update existing client {numeric_id}, but will continue with existing config")
                except Exception as e:
                    logger.warning(f"Error updating existing client: {e}, but will continue with existing config")

                # Skip client creation since we're reusing existing client
                client_response = None

            else:
                # Create new client via VPN API
                import uuid
                client_payload = {
                    "email": user_email,
                    "shopee_username": request.username,
                    "expired_date": expired_date,
                    "description": f"Generated via VPN Config Generator - {request.telco} {request.plan}",
                    "notes": f"Auto-generated for {request.telco} {request.plan} plan",
                    "server_id": server_id,
                    "client_id": str(uuid.uuid4())  # Generate valid UUID
                }

                client_response = session.post(
                    f"{self._api_config.fallback_api_endpoint}/api/v1/clients",
                    json=client_payload,
                    timeout=self._api_config.timeout
                )

            # Handle response based on whether we created a new client or reused existing one
            if existing_client:
                # For existing clients, we don't have a client_response
                logger.info("Using existing client, no new client creation needed")
                client_data = existing_client
            elif client_response and client_response.status_code in [200, 201]:
                # Add debug logging for new client creation
                logger.info(f"VPN API client creation response: status_code={client_response.status_code}")
                if client_response.content:
                    try:
                        response_data = client_response.json()
                        logger.info(f"VPN API response data: {response_data}")
                    except:
                        logger.info(f"VPN API response text: {client_response.text[:500]}")

                client_data = client_response.json()
                client_id = client_data.get('client_id', client_payload.get('client_id'))
                numeric_id = client_data.get('id')
            else:
                # Handle error case for new client creation
                if client_response:
                    logger.error(f"Failed to create client: {client_response.status_code}")
                    return VPNConfigResponse(
                        success=False,
                        error=f"Failed to create client: {client_response.status_code}"
                    )
                else:
                    return VPNConfigResponse(
                        success=False,
                        error="Unexpected error in client creation"
                    )

            # Always generate config using template (centralized config management)
            logger.info("Generating VPN config using template system")
            
            # Try to generate using template with the actual client_id from API response
            actual_client_id = client_data.get('client_id', '')
            template_result = self._generate_via_template(request, existing_client_id=actual_client_id)
            if template_result.success:
                return VPNConfigResponse(
                    success=True,
                    config=template_result.config,
                    created_date=datetime.now().strftime('%d-%m-%Y'),
                    expired_date=expired_date,
                    message="VPN configuration created successfully using centralized template system",
                    client_id=actual_client_id,
                    numeric_id=client_data.get('id')
                )

            # If template generation fails, return the template error
            template_error = template_result.error if hasattr(template_result, 'error') else "Template generation failed"
            return VPNConfigResponse(
                success=False,
                error=template_error
            )
        
        except Exception as e:
            logger.error(f"Error in _generate_via_fallback_api: {e}")
            return VPNConfigResponse(
                success=False,
                error=f"Fallback API error: {str(e)}"
            )
    
    def _parse_server_id(self, server_input: str) -> Optional[int]:
        """Parse server ID from server input (supports server names, 'server11' format, direct ID '11', and 'auto')"""
        try:
            # Handle "auto" server selection
            if server_input.lower() == 'auto':
                if self._vpn_api_service:
                    try:
                        servers = self._vpn_api_service.get_servers()
                        if servers:
                            # Find first online server
                            online_servers = [s for s in servers if s.get('status') == 'online']
                            if online_servers:
                                server_id = online_servers[0].get('id')
                                logger.info(f"Auto-selected server ID {server_id} ({online_servers[0].get('name', 'Unknown')}) for 'auto' request")
                                return server_id
                            elif servers:
                                # If no online servers, use first available server
                                server_id = servers[0].get('id')
                                logger.info(f"Auto-selected server ID {server_id} ({servers[0].get('name', 'Unknown')}) for 'auto' request (no online servers)")
                                return server_id
                    except Exception as e:
                        logger.warning(f"Failed to auto-select server: {e}")
                return None

            # First try to parse as direct integer
            if server_input.isdigit():
                return int(server_input)

            # Try to find server by name using VPN API
            if self._vpn_api_service:
                try:
                    servers = self._vpn_api_service.get_servers()
                    if servers:
                        for server in servers:
                            if server.get('name', '').lower() == server_input.lower():
                                return server.get('id')
                except Exception as e:
                    logger.warning(f"Failed to search servers by name: {e}")

            # If not a direct number, try to extract from server name format (e.g., server11)
            import re
            match = re.search(r'(\d+)', server_input)
            if match:
                return int(match.group(1))
            return None
        except Exception:
            return None

    def _get_server_domain(self, server_input: str) -> str:
        """Get server domain from server input (ID or name)"""
        try:
            # Parse server ID
            server_id = self._parse_server_id(server_input)
            
            # First try to get from VPN API (this should be the primary source)
            if self._vpn_api_service and server_id:
                try:
                    # Try to get specific server info
                    server_info = self._vpn_api_service.get_server(server_id)
                    if server_info and 'host' in server_info:
                        host = server_info['host']
                        logger.info(f"Got server {server_id} host from VPN API: {host}")
                        return host
                    
                    # If specific server lookup fails, try to get from all servers list
                    servers_response = self._vpn_api_service.get_servers()
                    if servers_response:
                        servers_data = []
                        if isinstance(servers_response, list):
                            servers_data = servers_response
                        elif isinstance(servers_response, dict):
                            if 'data' in servers_response:
                                servers_data = servers_response['data']
                            elif 'servers' in servers_response:
                                servers_data = servers_response['servers']
                        
                        # Find the server by ID
                        for server in servers_data:
                            if server.get('id') == server_id and 'host' in server:
                                host = server['host']
                                logger.info(f"Got server {server_id} host from servers list: {host}")
                                return host
                                
                except Exception as e:
                    logger.warning(f"Failed to get server info from VPN API: {e}")

            # Fallback: use default domain mapping (only as last resort)
            server_domain_map = {
                1: "server1.steam.autos",
                2: "server2.steam.autos",
                3: "server3.steam.autos",
                4: "server4.steam.autos",
                5: "server5.steam.autos",
                6: "server6.steam.autos",
                7: "server7.steam.autos",
                8: "server8.steam.autos",
                9: "server9.steam.autos",
                10: "server10.steam.autos",
                11: "server11.steam.autos",
                12: "server12.steam.autos",
                13: "server13.steam.autos",
                14: "server14.steam.autos",
                15: "server15.steam.autos",
                16: "server16.steam.autos",
                17: "server17.steam.autos",
                18: "server18.steam.autos",
                19: "server19.steam.autos",
                20: "server20.steam.autos"
            }

            if server_id and server_id in server_domain_map:
                fallback_host = server_domain_map[server_id]
                logger.warning(f"Using fallback host for server {server_id}: {fallback_host}")
                return fallback_host

            # Final fallback: return default domain with server ID
            final_fallback = f"server{server_id}.steam.autos" if server_id else "default.steam.autos"
            logger.warning(f"Using final fallback host: {final_fallback}")
            return final_fallback

        except Exception as e:
            logger.error(f"Error getting server domain: {e}")
            return "default.steam.autos"

    def _get_servers_by_tags(self, required_tags: List[str]) -> List[Dict[str, Any]]:
        """Get servers that match the required tags"""
        try:
            if not self._vpn_api_service:
                logger.warning("VPN API service not available for server filtering")
                return []

            # Get all servers from VPN API
            servers_response = self._vpn_api_service.get_servers()
            if not servers_response:
                logger.warning("No servers available from VPN API")
                return []

            # Handle different response formats - with proper None checking
            servers_data = []
            if isinstance(servers_response, list):
                servers_data = servers_response
            elif isinstance(servers_response, dict):
                if 'data' in servers_response and servers_response['data'] is not None:
                    servers_data = servers_response['data'] if isinstance(servers_response['data'], list) else []
                elif 'servers' in servers_response and servers_response['servers'] is not None:
                    servers_data = servers_response['servers'] if isinstance(servers_response['servers'], list) else []
                elif servers_response.get('success'):
                    data = servers_response.get('data', [])
                    servers_data = data if isinstance(data, list) else []

            if not servers_data:
                logger.warning("No server data available")
                return []

            # Filter servers by tags
            matching_servers = []
            for server in servers_data:
                server_tags = server.get('tags', [])
                if isinstance(server_tags, str):
                    server_tags = [tag.strip() for tag in server_tags.split(',')]

                # Check if server has any of the required tags (OR logic)
                if any(tag.lower() in [st.lower() for st in server_tags] for tag in required_tags):
                    # Only include active servers
                    if server.get('is_active', False):
                        matching_servers.append(server)
                        logger.debug(f"Server {server.get('name', 'Unknown')} matches tags {required_tags}")

            logger.info(f"Found {len(matching_servers)} servers matching tags {required_tags}")
            return matching_servers

        except Exception as e:
            logger.error(f"Error filtering servers by tags: {e}")
            return []

    def _select_server_by_sku(self, sku: str) -> Optional[Dict[str, Any]]:
        """Select an appropriate server based on SKU using VPN strategy factory"""
        try:
            if not VPN_STRATEGY_AVAILABLE:
                logger.warning("VPN strategy factory not available, cannot use SKU-based server selection")
                return None

            if not sku:
                logger.warning("No SKU provided for server selection")
                return None

            # Get required server tags for this SKU
            required_tags = VPNStrategyFactory.get_server_tags_for_sku(sku)
            if not required_tags:
                logger.warning(f"No server tags found for SKU: {sku}")
                return None

            logger.info(f"Using server tags {required_tags} for SKU {sku}")

            # Get servers that match the required tags
            matching_servers = self._get_servers_by_tags(required_tags)
            if not matching_servers:
                logger.warning(f"No servers found matching tags {required_tags} for SKU {sku}")
                return None

            # Select the first available server (could be enhanced with load balancing)
            selected_server = matching_servers[0]
            logger.info(f"Selected server {selected_server.get('name', 'Unknown')} (ID: {selected_server.get('id')}) for SKU {sku}")

            return selected_server

        except Exception as e:
            logger.error(f"Error selecting server by SKU {sku}: {e}")
            return None

    def generate_username(self, base_name: str) -> str:
        """Generate username with optional random suffix"""
        if self._generator_settings.add_random_suffix:
            suffix = random.randint(10000, 99999)
            return f"{base_name}-{suffix}"
        return base_name

    def _generate_info_message(self, telco_id: str, plan_id: str, variables: Dict[str, str]) -> str:
        """Generate info message using custom template if available"""
        try:
            # Get the plan to check for custom info message template
            plan = self._telco_manager.get_plan(telco_id, plan_id)

            if plan and plan.info_message_template:
                # Use custom template
                info_msg = plan.info_message_template

                # Replace variables in template
                for key, value in variables.items():
                    placeholder = f"{{{key}}}"
                    info_msg = info_msg.replace(placeholder, str(value))

                return info_msg
            else:
                # Use enhanced default template with more variables
                return f"""🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️

📧 Email: {variables.get('email', 'Unknown')}
👤 Shopee User: {variables.get('shopee_username', 'Unknown')}
🖥️ Server: {variables.get('server_number', 'Unknown')}
📅 Created: {variables.get('created_datetime', 'Unknown')}
⏰ Expires: {variables.get('expired_date_formatted', 'Unknown')}
📡 Telco: {variables.get('telco', 'Unknown')}
📋 Plan: {variables.get('plan', 'Unknown')}
⏳ Validity: {variables.get('validity', 'Unknown')}

🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️"""

        except Exception as e:
            logger.error(f"Error generating info message: {e}")
            # Fallback to basic message
            return f"""🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️
ᴄʀᴇᴀᴛᴇᴅ ᴅᴀᴛᴇ : {variables.get('created_date', 'Unknown')}
ᴇxᴘɪʀᴇᴅ ᴅᴀᴛᴇ : {variables.get('expired_date', 'Unknown')}
ᴛᴇʟᴄᴏ : {variables.get('telco', 'Unknown')}
ᴠᴀʟɪᴅɪᴛʏ : {variables.get('days', 'Unknown')} Day(s)
🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️"""
    
    def get_api_config(self) -> VPNAPIConfig:
        """Get API configuration"""
        logger.info(f"VPNConfigGeneratorService: get_api_config called. Returning: {self._api_config.to_dict()}")
        return self._api_config
    
    def update_api_config(self, config: VPNAPIConfig) -> bool:
        """Update API configuration"""
        try:
            self._api_config = config
            self.save_config()
            
            # Reinitialize VPN service if needed
            if config.use_vpn_plugin_api:
                self._init_vpn_service()
            
            return True
        except Exception as e:
            logger.error(f"Error updating API config: {e}")
            return False
    
    def get_generator_settings(self) -> ConfigGeneratorSettings:
        """Get generator settings"""
        return self._generator_settings
    
    def update_generator_settings(self, settings: ConfigGeneratorSettings) -> bool:
        """Update generator settings"""
        try:
            self._generator_settings = settings
            self.save_config()
            return True
        except Exception as e:
            logger.error(f"Error updating generator settings: {e}")
            return False
    
    def test_connection(self) -> Dict[str, Any]:
        """Test VPN API connection"""
        try:
            logger.info(f"Testing VPN connection - use_vpn_plugin_api: {self._api_config.use_vpn_plugin_api}")
            logger.info(f"VPN API service available: {self._vpn_api_service is not None}")

            # Try to reinitialize VPN service if not available
            if self._api_config.use_vpn_plugin_api and not self._vpn_api_service:
                logger.info("VPN API service not available, trying to reinitialize...")
                self._init_vpn_service()
                logger.info(f"After reinit - VPN API service available: {self._vpn_api_service is not None}")

            if self._api_config.use_vpn_plugin_api and self._vpn_api_service:
                result = self._vpn_api_service.test_connection()
                logger.info(f"VPN API test result: {result}")
                return result
            elif self._api_config.fallback_api_endpoint:
                # Test fallback API connection
                logger.info("Testing fallback API connection...")
                return self._test_fallback_api_connection()
            else:
                # Debug information
                debug_info = {
                    "use_vpn_plugin_api": self._api_config.use_vpn_plugin_api,
                    "vpn_api_service_available": self._vpn_api_service is not None,
                    "fallback_api_endpoint": self._api_config.fallback_api_endpoint,
                    "plugin_manager_available": self.plugin_manager is not None,
                    "loaded_plugins": list(self.plugin_manager.plugins.keys()) if self.plugin_manager else []
                }
                logger.warning(f"No VPN API configured. Debug info: {debug_info}")

                error_msg = "No VPN API configured"
                return {
                    "success": False,
                    "message": error_msg,
                    "debug": debug_info
                }
        except Exception as e:
            logger.error(f"Error testing VPN connection: {e}")
            return {
                "success": False,
                "message": f"Connection test failed: {str(e)}"
            }

    def _test_fallback_api_connection(self) -> Dict[str, Any]:
        """Test fallback API connection"""
        try:
            import requests

            # Clean and validate the endpoint URL
            endpoint = self._api_config.fallback_api_endpoint.strip()
            logger.info(f"Testing fallback API connection to: '{endpoint}'")
            
            # Ensure URL doesn't have leading/trailing whitespace that could cause issues
            if not endpoint:
                return {
                    "success": False,
                    "message": "No fallback API endpoint configured"
                }

            # Test authentication
            login_url = f"{endpoint}/api/v1/auth/login"
            logger.info(f"Attempting to connect to: {login_url}")
            
            response = requests.post(
                login_url,
                json={
                    "username": self._api_config.fallback_username,
                    "password": self._api_config.fallback_password
                },
                timeout=self._api_config.timeout
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('access_token'):
                    return {
                        "success": True,
                        "message": "Fallback API connection successful",
                        "api_url": self._api_config.fallback_api_endpoint
                    }
                else:
                    return {
                        "success": False,
                        "message": "Authentication successful but no access token received"
                    }
            else:
                return {
                    "success": False,
                    "message": f"Authentication failed: {response.status_code}"
                }

        except Exception as e:
            error_msg = str(e)
            # Add more specific error information
            if "Failed to resolve" in error_msg:
                error_msg = f"DNS resolution failed for URL: {self._api_config.fallback_api_endpoint}"
            elif "ConnectionError" in error_msg:
                error_msg = f"Connection failed to: {self._api_config.fallback_api_endpoint}"
            
            logger.error(f"Fallback API connection test failed: {e}")
            logger.error(f"URL being tested: '{self._api_config.fallback_api_endpoint}'")
            return {
                "success": False,
                "message": f"Fallback API connection test failed: {error_msg}",
                "endpoint_tested": self._api_config.fallback_api_endpoint
            }

    def _generate_via_template(self, request: VPNConfigRequest, existing_client_id: str = None) -> VPNConfigResponse:
        """Generate config using telco plan templates"""
        try:
            # Check if telco exists first
            telco = self._telco_manager.get_telco(request.telco)
            if not telco:
                # Telco not found - provide list of available telcos
                available_telcos = list(self._telco_manager.get_all_enabled_telcos().keys())
                telco_list = ", ".join(available_telcos)
                return VPNConfigResponse(
                    success=False,
                    error=f"❌ Telco not found: '{request.telco}'\n\n📋 Available telcos:\n{telco_list}"
                )

            # Check if plan exists for the telco
            plan = self._telco_manager.get_plan(request.telco, request.plan)
            if not plan:
                # Plan not found - provide list of available plans for this telco
                telco_plans = self._telco_manager.get_telco_plans(request.telco)
                available_plans = [plan_id for plan_id, plan_obj in telco_plans.items() if getattr(plan_obj, 'enabled', True)]
                plans_list = ", ".join(available_plans)
                return VPNConfigResponse(
                    success=False,
                    error=f"❌ Plan not found: '{request.plan}' for telco '{request.telco}'\n\n📋 Available plans for {request.telco}:\n{plans_list}"
                )

            # Use existing client_id if provided, otherwise generate new UUID
            config_uuid = existing_client_id if existing_client_id else str(uuid.uuid4())
            if existing_client_id:
                logger.info(f"Using existing client_id for template generation: {existing_client_id}")
            else:
                logger.info(f"Generated new UUID for template generation: {config_uuid}")

            # Calculate dates
            days = int(request.days)
            created_date = datetime.now().strftime('%d-%m-%Y')
            expired_date = (datetime.now() + timedelta(days=days)).strftime('%d-%m-%Y')

            # Determine server to use - try SKU-based selection first
            server_to_use = request.server
            selected_server = None

            if request.var_sku or request.sku:
                sku_to_use = request.var_sku or request.sku
                logger.info(f"Attempting SKU-based server selection for template generation with SKU: {sku_to_use}")
                selected_server = self._select_server_by_sku(sku_to_use)
                if selected_server:
                    server_to_use = str(selected_server.get('id'))
                    logger.info(f"SKU-based selection chose server ID {server_to_use} ({selected_server.get('name', 'Unknown')}) for template generation")
                else:
                    logger.warning(f"SKU-based server selection failed for SKU {sku_to_use}, using provided server: {request.server}")

            # Prepare variables for template substitution
            server_domain = self._get_server_domain(server_to_use)

            # Calculate formatted dates for different display formats
            created_datetime = datetime.now()
            expired_datetime = created_datetime + timedelta(days=days)

            # Generate email in the format used by VPN API
            email = f"user-{request.username}@shopee.local"

            variables = {
                # Core configuration variables
                'uuid': config_uuid,
                'server': server_domain,  # Use server domain instead of server ID
                'server_domain': server_domain,  # Server domain for templates that use {server_domain}
                'server_id': server_to_use,  # Use selected server ID
                'server_name': selected_server.get('name', server_domain) if selected_server else server_domain,  # Server name or domain
                'identity': request.username,
                'username': request.username,
                'days': request.days,
                'telco': request.telco,
                'plan': request.plan,

                # Date variables in different formats
                'created_date': created_date,  # YYYY-MM-DD format
                'expired_date': expired_date,  # YYYY-MM-DD format
                'created_date_formatted': created_datetime.strftime('%d-%m-%Y'),  # DD-MM-YYYY format
                'expired_date_formatted': expired_datetime.strftime('%d-%m-%Y'),  # DD-MM-YYYY format
                'created_datetime': created_datetime.strftime('%Y-%m-%d %H:%M'),  # YYYY-MM-DD HH:MM format
                'expired_datetime': expired_datetime.strftime('%Y-%m-%d %H:%M'),  # YYYY-MM-DD HH:MM format
                'created_time': created_datetime.strftime('%H:%M'),  # HH:MM format
                'validity': f"{request.days} Day(s)",  # Formatted validity

                # User and system variables
                'email': email,
                'shopee_user': request.username,  # Alias for username
                'shopee_username': request.username,  # Explicit shopee username

                # Server information
                'server_number': server_to_use,  # Selected server ID or original input
                'original_server_input': request.server,  # Original server input for reference

                # SKU information (if available)
                'sku': request.sku or '',
                'var_sku': request.var_sku or '',
                'selected_by_sku': 'Yes' if selected_server else 'No',

                # Additional formatting variables
                'config_id': config_uuid[:8],  # Short config ID (first 8 chars of UUID)
                'short_uuid': config_uuid[:8],  # Short UUID for display
                
                # Template variables that might be needed (placeholders if not set elsewhere)
                'port': '80',  # Default port
                'path': '/ws',  # Default WebSocket path
            }

            # Generate configuration using the template
            config = self.generate_config_from_telco_plan(request.telco, request.plan, variables)

            if not config:
                return VPNConfigResponse(
                    success=False,
                    error="Failed to generate configuration from template"
                )

            # Generate client ID for template-based configs
            client_id = str(uuid.uuid4())

            # Calculate remaining days and server binding info for template configs
            days_total = int(request.days)
            created_date_obj = datetime.now()

            # Save generated config info with remaining days tracking
            config_info = {
                'client_id': client_id,
                'numeric_id': None,  # Template configs don't have numeric IDs
                'server_id': server_to_use if selected_server else None,
                'server_name': selected_server.get('name', 'Template Server') if selected_server else 'Template Server',
                'telco': request.telco,
                'plan': request.plan,
                'created_date': created_date,
                'expired_date': expired_date,
                'username': request.username,
                'sku': request.sku,
                'var_sku': request.var_sku,
                'active': True,
                # New fields for remaining days tracking
                'days_total': days_total,
                'days_remaining': days_total,
                'server_bound': selected_server is not None,
                'bound_at': created_date_obj.isoformat(),
                'can_unbind': True,
                'unbind_preserves_days': True,
                'config_type': 'template'
            }
            self.save_generated_config(request.username, config_info)

            return VPNConfigResponse(
                success=True,
                config=config,
                created_date=created_date,
                expired_date=expired_date,
                message=f"Configuration generated successfully for {request.telco} {request.plan}",
                client_id=client_id,
                numeric_id=None  # Template configs don't have numeric IDs
            )

        except Exception as e:
            logger.error(f"Error generating config via template: {e}")
            return VPNConfigResponse(
                success=False,
                error=f"Template generation failed: {str(e)}"
            )

    # Template Management Methods

    def load_templates(self):
        """Load configuration templates from file"""
        try:
            if os.path.exists(self.templates_file):
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._templates = [ConfigTemplate.from_dict(template) for template in data]
                logger.info(f"Loaded {len(self._templates)} configuration templates")
            else:
                # Create default templates
                self._create_default_templates()
                self.save_templates()
        except Exception as e:
            logger.error(f"Error loading templates: {e}")
            self._create_default_templates()

    def save_templates(self):
        """Save configuration templates to file"""
        try:
            data = [template.to_dict() for template in self._templates]
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved configuration templates")
        except Exception as e:
            logger.error(f"Error saving templates: {e}")

    def get_all_templates(self) -> List[ConfigTemplate]:
        """Get all configuration templates"""
        return self._templates.copy()

    def get_template_by_id(self, template_id: str) -> Optional[ConfigTemplate]:
        """Get a specific template by ID"""
        for template in self._templates:
            if template.id == template_id:
                return template
        return None

    def create_template(self, template_data: Dict[str, Any]) -> bool:
        """Create a new configuration template"""
        try:
            # Generate ID if not provided
            if 'id' not in template_data:
                template_data['id'] = str(uuid.uuid4())

            # Set timestamps
            now = datetime.now().isoformat()
            template_data['created_at'] = now
            template_data['updated_at'] = now

            # Create template object
            template = ConfigTemplate.from_dict(template_data)

            # Add to list
            self._templates.append(template)

            # Save to file
            self.save_templates()

            logger.info(f"Created new template: {template.name}")
            return True

        except Exception as e:
            logger.error(f"Error creating template: {e}")
            return False

    def update_template(self, template_id: str, template_data: Dict[str, Any]) -> bool:
        """Update an existing configuration template"""
        try:
            for i, template in enumerate(self._templates):
                if template.id == template_id:
                    # Preserve ID and created_at
                    template_data['id'] = template_id
                    template_data['created_at'] = template.created_at
                    template_data['updated_at'] = datetime.now().isoformat()

                    # Update template
                    self._templates[i] = ConfigTemplate.from_dict(template_data)

                    # Save to file
                    self.save_templates()

                    logger.info(f"Updated template: {template_data.get('name', template_id)}")
                    return True

            logger.warning(f"Template not found: {template_id}")
            return False

        except Exception as e:
            logger.error(f"Error updating template: {e}")
            return False

    def delete_template(self, template_id: str) -> bool:
        """Delete a configuration template"""
        try:
            for i, template in enumerate(self._templates):
                if template.id == template_id:
                    deleted_name = template.name
                    del self._templates[i]

                    # Save to file
                    self.save_templates()

                    logger.info(f"Deleted template: {deleted_name}")
                    return True

            logger.warning(f"Template not found: {template_id}")
            return False

        except Exception as e:
            logger.error(f"Error deleting template: {e}")
            return False

    def generate_config_from_template(self, template_id: str, variables: Dict[str, str]) -> str:
        """Generate configuration using a template and variables"""
        try:
            template = self.get_template_by_id(template_id)
            if not template:
                return ""

            config_string = template.template

            # Replace variables in template
            for key, value in variables.items():
                placeholder = f"{{{key}}}"
                config_string = config_string.replace(placeholder, str(value))

            return config_string

        except Exception as e:
            logger.error(f"Error generating config from template: {e}")
            return ""

    def _create_default_templates(self):
        """Create default configuration templates"""
        default_templates = [
            {
                "id": str(uuid.uuid4()),
                "name": "VLESS WebSocket",
                "description": "VLESS protocol with WebSocket transport",
                "template": "vless://{uuid}@{server}:{port}?encryption=none&type=ws&path={path}&host={host}#{name}",
                "variables": {
                    "uuid": "Client UUID",
                    "server": "Server address",
                    "port": "Server port",
                    "path": "WebSocket path",
                    "host": "Host header",
                    "name": "Configuration name"
                },
                "category": "vless",
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            {
                "id": str(uuid.uuid4()),
                "name": "VLESS TCP",
                "description": "VLESS protocol with TCP transport",
                "template": "vless://{uuid}@{server}:{port}?encryption=none&type=tcp#{name}",
                "variables": {
                    "uuid": "Client UUID",
                    "server": "Server address",
                    "port": "Server port",
                    "name": "Configuration name"
                },
                "category": "vless",
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            {
                "id": str(uuid.uuid4()),
                "name": "VMess WebSocket",
                "description": "VMess protocol with WebSocket transport",
                "template": "vmess://{base64_config}",
                "variables": {
                    "base64_config": "Base64 encoded VMess configuration"
                },
                "category": "vmess",
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Trojan",
                "description": "Trojan protocol configuration",
                "template": "trojan://{password}@{server}:{port}?security=tls&type=tcp#{name}",
                "variables": {
                    "password": "Trojan password",
                    "server": "Server address",
                    "port": "Server port",
                    "name": "Configuration name"
                },
                "category": "trojan",
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        ]

        self._templates = [ConfigTemplate.from_dict(template) for template in default_templates]

    # New Telco Management Methods

    def get_all_telcos(self) -> Dict[str, VPNTelco]:
        """Get all telco configurations"""
        return self._telco_manager.get_all_enabled_telcos()

    def get_telco(self, telco_id: str) -> Optional[VPNTelco]:
        """Get a specific telco by ID"""
        return self._telco_manager.get_telco(telco_id)

    def get_telco_by_id(self, telco_id: str) -> Optional[VPNTelco]:
        """Get a specific telco by ID (alias for get_telco)"""
        return self.get_telco(telco_id)

    def get_plan_by_id(self, telco_id: str, plan_id: str) -> Optional[VPNPlan]:
        """Get a specific plan by ID (alias for get_plan)"""
        return self.get_plan(telco_id, plan_id)

    def get_telco_plans(self, telco_id: str) -> Dict[str, VPNPlan]:
        """Get all plans for a specific telco"""
        return self._telco_manager.get_telco_plans(telco_id)

    def get_plan(self, telco_id: str, plan_id: str) -> Optional[VPNPlan]:
        """Get a specific plan from a telco"""
        return self._telco_manager.get_plan(telco_id, plan_id)

    def create_telco(self, telco_data: Dict[str, Any]) -> bool:
        """Create a new telco"""
        try:
            # Generate ID if not provided
            if 'id' not in telco_data:
                telco_data['id'] = telco_data.get('name', '').lower().replace(' ', '_')

            # Set timestamps
            now = datetime.now().isoformat()
            telco_data['created_at'] = now
            telco_data['updated_at'] = now
            telco_data['plans'] = telco_data.get('plans', {})

            # Create telco object
            telco = VPNTelco.from_dict(telco_data)

            # Add to manager
            self._telco_manager.add_telco(telco)

            # Save to file
            self.save_telco_configs()

            logger.info(f"Created new telco: {telco.name}")
            return True

        except Exception as e:
            logger.error(f"Error creating telco: {e}")
            return False

    def update_telco(self, telco_id: str, telco_data: Dict[str, Any]) -> bool:
        """Update an existing telco"""
        try:
            existing_telco = self._telco_manager.get_telco(telco_id)
            if not existing_telco:
                return False

            # Preserve ID, created_at, and existing plans
            telco_data['id'] = telco_id
            telco_data['created_at'] = existing_telco.created_at
            telco_data['updated_at'] = datetime.now().isoformat()
            telco_data['plans'] = telco_data.get('plans', existing_telco.plans)

            # Update telco
            updated_telco = VPNTelco.from_dict(telco_data)
            self._telco_manager.add_telco(updated_telco)

            # Save to file
            self.save_telco_configs()

            logger.info(f"Updated telco: {telco_data.get('name', telco_id)}")
            return True

        except Exception as e:
            logger.error(f"Error updating telco: {e}")
            return False

    def delete_telco(self, telco_id: str) -> bool:
        """Delete a telco"""
        try:
            if self._telco_manager.remove_telco(telco_id):
                self.save_telco_configs()
                logger.info(f"Deleted telco: {telco_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting telco: {e}")
            return False

    def create_plan(self, telco_id: str, plan_data: Dict[str, Any]) -> bool:
        """Create a new plan for a telco"""
        try:
            telco = self._telco_manager.get_telco(telco_id)
            if not telco:
                return False

            # Generate ID if not provided
            if 'id' not in plan_data:
                plan_data['id'] = plan_data.get('name', '').lower().replace(' ', '_')

            # Set timestamps
            now = datetime.now().isoformat()
            plan_data['created_at'] = now
            plan_data['updated_at'] = now
            plan_data['variables'] = plan_data.get('variables', {})
            plan_data['enabled'] = plan_data.get('enabled', True)

            # Create plan object
            plan = VPNPlan.from_dict(plan_data)

            # Add to telco
            telco.add_plan(plan)

            # Save to file
            self.save_telco_configs()

            logger.info(f"Created new plan: {plan.name} for telco: {telco_id}")
            return True

        except Exception as e:
            logger.error(f"Error creating plan: {e}")
            return False

    def update_plan(self, telco_id: str, plan_id: str, plan_data: Dict[str, Any]) -> bool:
        """Update an existing plan"""
        try:
            telco = self._telco_manager.get_telco(telco_id)
            if not telco:
                return False

            existing_plan = telco.get_plan(plan_id)
            if not existing_plan:
                return False

            # Preserve ID, created_at
            plan_data['id'] = plan_id
            plan_data['created_at'] = existing_plan.created_at
            plan_data['updated_at'] = datetime.now().isoformat()

            # Update plan
            updated_plan = VPNPlan.from_dict(plan_data)
            telco.add_plan(updated_plan)

            # Save to file
            self.save_telco_configs()

            logger.info(f"Updated plan: {plan_data.get('name', plan_id)} for telco: {telco_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating plan: {e}")
            return False

    def delete_plan(self, telco_id: str, plan_id: str) -> bool:
        """Delete a plan from a telco"""
        try:
            telco = self._telco_manager.get_telco(telco_id)
            if not telco:
                return False

            if telco.remove_plan(plan_id):
                self.save_telco_configs()
                logger.info(f"Deleted plan: {plan_id} from telco: {telco_id}")
                return True
            return False

        except Exception as e:
            logger.error(f"Error deleting plan: {e}")
            return False

    def delete_telco(self, telco_id: str) -> bool:
        """Delete a telco"""
        try:
            if self._telco_manager.remove_telco(telco_id):
                self.save_telco_configs()
                logger.info(f"Deleted telco: {telco_id}")
                return True
            return False

        except Exception as e:
            logger.error(f"Error deleting telco: {e}")
            return False

    def create_plan(self, telco_id: str, plan_data: Dict[str, Any]) -> bool:
        """Create a new plan for a telco"""
        try:
            telco = self._telco_manager.get_telco(telco_id)
            if not telco:
                return False

            # Generate ID if not provided
            if 'id' not in plan_data:
                plan_data['id'] = plan_data.get('name', '').lower().replace(' ', '_')

            # Set timestamps
            now = datetime.now().isoformat()
            plan_data['created_at'] = now
            plan_data['updated_at'] = now

            # Create plan object
            plan = VPNPlan.from_dict(plan_data)

            # Add to telco
            telco.add_plan(plan)

            # Save to file
            self.save_telco_configs()

            logger.info(f"Created new plan: {plan.name} for telco: {telco_id}")
            return True

        except Exception as e:
            logger.error(f"Error creating plan: {e}")
            return False

    def update_plan(self, telco_id: str, plan_id: str, plan_data: Dict[str, Any]) -> bool:
        """Update an existing plan"""
        try:
            telco = self._telco_manager.get_telco(telco_id)
            if not telco:
                return False

            existing_plan = telco.get_plan(plan_id)
            if not existing_plan:
                return False

            # Preserve ID and created_at
            plan_data['id'] = plan_id
            plan_data['created_at'] = existing_plan.created_at
            plan_data['updated_at'] = datetime.now().isoformat()

            # Update plan
            updated_plan = VPNPlan.from_dict(plan_data)
            telco.add_plan(updated_plan)

            # Save to file
            self.save_telco_configs()

            logger.info(f"Updated plan: {plan_data.get('name', plan_id)} for telco: {telco_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating plan: {e}")
            return False

    def delete_plan(self, telco_id: str, plan_id: str) -> bool:
        """Delete a plan from a telco"""
        try:
            telco = self._telco_manager.get_telco(telco_id)
            if not telco:
                return False

            if telco.remove_plan(plan_id):
                self.save_telco_configs()
                logger.info(f"Deleted plan: {plan_id} from telco: {telco_id}")
                return True
            return False

        except Exception as e:
            logger.error(f"Error deleting plan: {e}")
            return False

    def update_config_template_only(self, request: VPNConfigRequest, preserve_uuid: str = None, preserve_expiry_date: str = None) -> VPNConfigResponse:
        """
        Update VPN configuration template while preserving existing UUID and expiration date.
        This method ensures that only the template configuration changes, not the UUID or expiry.
        
        Args:
            request: VPN configuration request with new template parameters
            preserve_uuid: Existing UUID to preserve (if None, will look up existing)
            preserve_expiry_date: Existing expiry date to preserve (if None, will look up existing)
            
        Returns:
            VPNConfigResponse with updated template but preserved UUID and expiry
        """
        try:
            logger.info(f"Updating config template only for user: {request.username}, preserving UUID: {preserve_uuid}")
            
            # Try to find existing configuration for this user
            existing_config = None
            if not preserve_uuid:
                existing_configs = self.get_generated_configs()
                user_config = existing_configs.get(request.username)
                if user_config:
                    preserve_uuid = user_config.get('client_id')
                    preserve_expiry_date = user_config.get('expired_date')
                    existing_config = user_config
                    logger.info(f"Found existing config for user {request.username}, UUID: {preserve_uuid}, Expiry: {preserve_expiry_date}")
            
            # If we still don't have a UUID to preserve, generate new config normally
            if not preserve_uuid:
                logger.warning(f"No existing UUID found for user {request.username}, generating new configuration")
                return self.generate_config(request)
            
            # Generate template with preserved values
            template_result = self._generate_via_template(request)
            if not template_result.success:
                return template_result
            
            # Replace the generated UUID in template with the preserved UUID
            config_with_preserved_uuid = template_result.config
            if preserve_uuid and template_result.config:
                import re
                # Find UUID pattern and replace with preserved UUID
                uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                config_with_preserved_uuid = re.sub(uuid_pattern, preserve_uuid, template_result.config, flags=re.IGNORECASE)
                logger.info(f"Replaced template UUID with preserved UUID: {preserve_uuid}")
            
            # Use preserved expiry date or keep the existing one
            final_expiry_date = preserve_expiry_date or template_result.expired_date
            
            # Calculate preserved dates
            preserved_created_date = existing_config.get('created_date') if existing_config else template_result.created_date
            
            # Update the stored configuration with template changes but preserved UUID/expiry
            if existing_config:
                config_info = existing_config.copy()
                config_info.update({
                    'telco': request.telco,
                    'plan': request.plan,
                    'expired_date': final_expiry_date,  # Preserve original expiry
                    'client_id': preserve_uuid,  # Preserve original UUID
                    'created_date': preserved_created_date,  # Preserve original creation date
                    'active': True,
                    'template_updated_at': datetime.now().isoformat(),
                    'template_update_reason': f"Template changed to {request.telco} {request.plan}"
                })
                self.save_generated_config(request.username, config_info)
            
            return VPNConfigResponse(
                success=True,
                config=config_with_preserved_uuid,
                client_id=preserve_uuid,
                numeric_id=existing_config.get('numeric_id') if existing_config else None,
                created_date=preserved_created_date,
                expired_date=final_expiry_date,
                message=f"Template updated successfully - UUID and expiry date preserved"
            )
            
        except Exception as e:
            logger.error(f"Error updating config template: {e}")
            return VPNConfigResponse(
                success=False,
                error=f"Failed to update template while preserving UUID: {str(e)}"
            )

class VPNChatCommandService:
    """Service for handling VPN-related chat commands"""
    
    def __init__(self, plugin_dir: str, config_service: VPNConfigGeneratorService, plugin_manager=None):
        self.plugin_dir = plugin_dir
        self.config_service = config_service
        self.plugin_manager = plugin_manager

        # Use configs directory for persistent storage
        self.configs_dir = os.path.join(os.path.dirname(os.path.dirname(plugin_dir)), 'configs', 'plugins', 'vpn_config_generator')
        os.makedirs(self.configs_dir, exist_ok=True)

        self.webhook_config_file = os.path.join(self.configs_dir, 'webhook_config.json')
        self.command_config_file = os.path.join(self.configs_dir, 'command_config.json')
        self.commands_file = os.path.join(self.configs_dir, 'commands.json')

        # Initialize with default values
        self._webhook_config = WebhookConfig(
            enabled=True,
            shopee_api_base_url='https://shop.api.limjianhui.com',
            steamcodetool_base_url='http://localhost:5000',
            webhook_endpoint='/vpn-config-generator/api/webhook',
            auto_register=True,
            retry_count=3,
            retry_delay=5,
            timeout=30
        )
        self._command_config = VPNCommandConfig()
        self._commands: Dict[str, ChatCommand] = {}

        self.load_webhook_config()
        self.load_command_config()
        self.load_commands()

    def load_webhook_config(self):
        """Load webhook configuration"""
        try:
            if os.path.exists(self.webhook_config_file):
                with open(self.webhook_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._webhook_config = WebhookConfig.from_dict(data)
                logger.info("Loaded VPN webhook configuration")
            else:
                self.save_webhook_config()
        except Exception as e:
            logger.error(f"Error loading VPN webhook config: {e}")

    def save_webhook_config(self):
        """Save webhook configuration"""
        try:
            with open(self.webhook_config_file, 'w', encoding='utf-8') as f:
                json.dump(self._webhook_config.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info("Saved VPN webhook configuration")
        except Exception as e:
            logger.error(f"Error saving VPN webhook config: {e}")

    def get_webhook_config(self) -> WebhookConfig:
        """Get webhook configuration"""
        return self._webhook_config

    def update_webhook_config(self, config: WebhookConfig) -> bool:
        """Update webhook configuration"""
        try:
            self._webhook_config = config
            self.save_webhook_config()
            return True
        except Exception as e:
            logger.error(f"Error updating VPN webhook config: {e}")
            return False

    def load_command_config(self):
        """Load command configuration"""
        try:
            if os.path.exists(self.command_config_file):
                with open(self.command_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._command_config = VPNCommandConfig.from_dict(data)
                logger.info("Loaded VPN command configuration")
            else:
                self.save_command_config()
        except Exception as e:
            logger.error(f"Error loading VPN command config: {e}")

    def save_command_config(self):
        """Save command configuration"""
        try:
            with open(self.command_config_file, 'w', encoding='utf-8') as f:
                json.dump(self._command_config.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info("Saved VPN command configuration")
        except Exception as e:
            logger.error(f"Error saving VPN command config: {e}")

    def get_command_config(self) -> VPNCommandConfig:
        """Get command configuration"""
        return self._command_config

    def update_command_config(self, config: VPNCommandConfig) -> bool:
        """Update command configuration and auto-update all related command names"""
        try:
            old_command_name = self._command_config.command_name
            new_command_name = config.command_name
            
            # Update the config
            self._command_config = config
            
            # If command name changed, update all sub-commands automatically
            if old_command_name != new_command_name:
                logger.info(f"Command name changed from '{old_command_name}' to '{new_command_name}', updating all sub-commands")
                self._update_all_command_names(old_command_name, new_command_name)
            
            self.save_command_config()
            return True
        except Exception as e:
            logger.error(f"Error updating VPN command config: {e}")
            return False

    def synchronize_command_names_with_prefix(self) -> None:
        """Ensure in-memory commands use the current prefix from command_config.

        This guards against situations where command_config.json was updated
        but commands.json still contains old names (e.g., 'v', 'vlist', ...).
        """
        try:
            current_prefix = self._command_config.command_name
            if not current_prefix:
                return

            # Expected suffix mapping for known command keys
            suffix_by_key = {
                'v': '',
                'vlist': 'list',
                'vuser': 'users',
                'vdel': 'del',
                'vrenew': 'renew',
                'vtest': 'test',
                'vservers': 'servers',
                'vhelp': 'help',
            }

            updated = False
            for key, cmd in self._commands.items():
                if key in suffix_by_key:
                    expected_name = f"{current_prefix}{suffix_by_key[key]}"
                    if cmd.command != expected_name:
                        logger.info(
                            f"Synchronizing command name for key '{key}': '{cmd.command}' -> '{expected_name}'"
                        )
                        cmd.command = expected_name
                        cmd.updated_at = datetime.now().isoformat()
                        updated = True

            if updated:
                self.save_commands()
        except Exception as e:
            logger.error(f"Error synchronizing command names with prefix: {e}")
    
    def _update_all_command_names(self, old_prefix: str, new_prefix: str):
        """Auto-update all command names when the main prefix changes"""
        try:
            # Define the sub-command mappings
            sub_commands = {
                'vlist': 'list',
                'vuser': 'users', 
                'vdel': 'del',
                'vrenew': 'renew',
                'vtest': 'test',
                'vservers': 'servers',
                'vhelp': 'help'
            }
            
            # Update the main command (key 'v' becomes the new prefix)
            if 'v' in self._commands:
                self._commands['v'].command = new_prefix
                logger.info(f"Updated main command from '{old_prefix}' to '{new_prefix}'")
            
            # Update all sub-commands
            for command_key, suffix in sub_commands.items():
                if command_key in self._commands:
                    new_command_name = f"{new_prefix}{suffix}"
                    old_command_name = self._commands[command_key].command
                    self._commands[command_key].command = new_command_name
                    self._commands[command_key].updated_at = datetime.now().isoformat()
                    logger.info(f"Updated sub-command '{old_command_name}' to '{new_command_name}'")
            
            # Save the updated commands
            self.save_commands()
            logger.info(f"Successfully updated all command names to use prefix '{new_prefix}'")
            
        except Exception as e:
            logger.error(f"Error updating command names: {e}")
            raise

    def load_commands(self):
        """Load chat commands from file"""
        try:
            if os.path.exists(self.commands_file):
                with open(self.commands_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._commands = {key: ChatCommand.from_dict(value) for key, value in data.items()}
                logger.info(f"Loaded {len(self._commands)} VPN chat commands")
            else:
                self._create_default_commands()
                self.save_commands()
        except Exception as e:
            logger.error(f"Error loading VPN commands: {e}")
            self._create_default_commands()

    def save_commands(self):
        """Save chat commands to file"""
        try:
            data = {key: cmd.to_dict() for key, cmd in self._commands.items()}
            with open(self.commands_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved VPN chat commands")
        except Exception as e:
            logger.error(f"Error saving VPN chat commands: {e}")

    def get_all_commands(self) -> Dict[str, ChatCommand]:
        """Get all chat commands"""
        return self._commands.copy()

    def get_command(self, command_key: str) -> Optional[ChatCommand]:
        """Get a specific command by key"""
        return self._commands.get(command_key)

    def create_command(self, command_key: str, command_data: Dict[str, Any]) -> bool:
        """Create a new chat command"""
        try:
            if command_key in self._commands:
                return False  # Command already exists

            # Set timestamps
            now = datetime.now().isoformat()
            command_data['created_at'] = now
            command_data['updated_at'] = now

            # Create command object
            command = ChatCommand.from_dict(command_data)

            # Add to dict
            self._commands[command_key] = command

            # Save to file
            self.save_commands()

            logger.info(f"Created new VPN command: {command_key}")
            return True

        except Exception as e:
            logger.error(f"Error creating VPN command: {e}")
            return False

    def update_command(self, command_key: str, command_data: Dict[str, Any]) -> bool:
        """Update an existing chat command"""
        try:
            if command_key not in self._commands:
                return False  # Command not found

            existing_command = self._commands[command_key]

            # Preserve created_at
            command_data['created_at'] = existing_command.created_at
            command_data['updated_at'] = datetime.now().isoformat()

            # Update command
            self._commands[command_key] = ChatCommand.from_dict(command_data)

            # Save to file
            self.save_commands()

            logger.info(f"Updated VPN command: {command_key}")
            return True

        except Exception as e:
            logger.error(f"Error updating VPN command: {e}")
            return False

    def delete_command(self, command_key: str) -> bool:
        """Delete a chat command"""
        try:
            if command_key in self._commands:
                del self._commands[command_key]
                self.save_commands()
                logger.info(f"Deleted VPN command: {command_key}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting VPN command: {e}")
            return False

    def process_webhook_message(self, message: WebhookMessage) -> List[CommandResponse]:
        """Process incoming webhook message and generate responses"""
        responses = []
        try:
            # Check if webhook processing is enabled
            if not self._webhook_config.enabled:
                return responses

            # Get command prefix from chat commands plugin
            command_prefix = self._get_command_prefix()

            # Check if message contains any of the defined commands
            for command_key, command in self._commands.items():
                if command.enabled and command.keyword.lower() in message.text.lower():
                    # Found a command, generate response
                    response = self._handle_command(command, message, command_prefix)
                    if response:
                        responses.append(response)
            
            return responses

        except Exception as e:
            logger.error(f"Error processing VPN webhook message: {e}")
            return responses

    def _get_command_prefix(self) -> str:
        """Get command prefix from chat commands plugin"""
        try:
            if self.plugin_manager:
                chat_commands_plugin = self.plugin_manager.get_plugin('chat_commands')
                if chat_commands_plugin and hasattr(chat_commands_plugin, 'command_service'):
                    return chat_commands_plugin.command_service.get_command_prefix()
            return "!"
        except Exception:
            return "!"

    def _handle_command(self, command: ChatCommand, message: WebhookMessage, command_prefix: str) -> Optional[CommandResponse]:
        """Handle a specific command and generate a response"""
        try:
            # Extract parameters from message text
            params = self._extract_params(message.text, command.keyword)

            # Validate required parameters
            if len(params) < command.required_params:
                # Send usage instructions
                usage = f"Usage: {command_prefix}{self._command_config.command_name} {command.keyword} {command.usage}"
                return CommandResponse(
                    conversation_id=message.conversation_id,
                    text=usage
                )

            # Create VPN config request
            config_request = VPNConfigRequest(
                server=params[0] if len(params) > 0 else 'auto',
                days=params[1] if len(params) > 1 else '30',
                telco=params[2] if len(params) > 2 else 'default',
                plan=params[3] if len(params) > 3 else 'default',
                username=message.sender_username
            )

            # Generate configuration
            result = self.config_service.generate_config(config_request)

            if result.success:
                # Send config and info message
                return CommandResponse(
                    conversation_id=message.conversation_id,
                    text=f"{result.config}\n\n{result.message}"
                )
            else:
                # Send error message
                return CommandResponse(
                    conversation_id=message.conversation_id,
                    text=f"Error: {result.error}"
                )

        except Exception as e:
            logger.error(f"Error handling VPN command: {e}")
            return CommandResponse(
                conversation_id=message.conversation_id,
                text="An unexpected error occurred while processing your request."
            )

    def _extract_params(self, text: str, keyword: str) -> List[str]:
        """Extract parameters from message text after the keyword"""
        try:
            # Find keyword and get text after it
            keyword_pos = text.lower().find(keyword.lower())
            if keyword_pos == -1:
                return []

            params_str = text[keyword_pos + len(keyword):].strip()
            return [param.strip() for param in params_str.split()] if params_str else []

        except Exception as e:
            logger.error(f"Error extracting params: {e}")
            return []

    def _create_default_commands(self):
        """Create default VPN chat commands"""
        default_commands = {
            "v": {
                "command": "v",
                "description": "Generate a VPN configuration",
                "response_text": "🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️",
                "image_urls": [],
                "required_params": ["server", "days", "telco", "plan"],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            "vlist": {
                "command": "vlist",
                "description": "List available VPN servers and configurations",
                "response_text": "📋 Available VPN Servers & Configurations",
                "image_urls": [],
                "required_params": [],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            "vuser": {
                "command": "vuser",
                "description": "View VPN configurations for a user",
                "response_text": "👤 User VPN Configurations",
                "image_urls": [],
                "required_params": [],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            "vdel": {
                "command": "vdel",
                "description": "Delete a VPN configuration",
                "response_text": "🗑️ VPN Configuration Deleted",
                "image_urls": [],
                "required_params": ["client_id"],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            "vrenew": {
                "command": "vrenew",
                "description": "Renew/extend a VPN configuration",
                "response_text": "🔄 VPN Configuration Renewed",
                "image_urls": [],
                "required_params": ["client_id", "days"],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            "vtest": {
                "command": "vtest",
                "description": "Test VPN API connectivity",
                "response_text": "🔧 VPN API Test",
                "image_urls": [],
                "required_params": [],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            "vservers": {
                "command": "vservers",
                "description": "List all VPN servers with their IDs",
                "response_text": "🖥️ Available VPN Servers",
                "image_urls": [],
                "required_params": [],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            "vhelp": {
                "command": "vhelp",
                "description": "Show help for all VPN commands",
                "response_text": "📚 VPN Commands Help",
                "image_urls": [],
                "required_params": [],
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        }

        self._commands = {key: ChatCommand.from_dict(value) for key, value in default_commands.items()}
    
    async def get_servers_and_configs(self) -> Dict[str, Any]:
        """Get servers and telco configurations for the vlist command"""
        try:
            logger.info("VPNChatCommandService: get_servers_and_configs called")
            
            # Initialize result structure
            result = {
                'servers': [],
                'telcos': {},
                'total_plans': 0,
                'error': None
            }
            
            # Get servers from VPN API via config service
            servers_data = []
            servers_api_error = None
            try:
                # Debug logging
                logger.info(f"VPNChatCommandService: Checking config_service: {type(self.config_service)}")
                logger.info(f"VPNChatCommandService: config_service has _vpn_api_service: {hasattr(self.config_service, '_vpn_api_service')}")
                if hasattr(self.config_service, '_vpn_api_service'):
                    logger.info(f"VPNChatCommandService: _vpn_api_service value: {self.config_service._vpn_api_service}")
                
                if hasattr(self.config_service, '_vpn_api_service') and self.config_service._vpn_api_service:
                    logger.info("Getting servers from VPN API via config service...")
                    vpn_api_service = self.config_service._vpn_api_service
                    logger.info(f"VPN API service type: {type(vpn_api_service)}")
                    
                    # Check if VPN API service has the expected methods
                    if hasattr(vpn_api_service, 'get_servers'):
                        logger.info("VPN API service has get_servers method")
                        servers_response = vpn_api_service.get_servers()
                        logger.info(f"VPN API servers_response type: {type(servers_response)}, value: {servers_response}")
                        
                        if servers_response is not None:
                            # Handle different response formats
                            if isinstance(servers_response, list):
                                # Direct list response
                                servers_data = servers_response
                                logger.info(f"Got {len(servers_data)} servers from VPN API (direct list)")
                            elif isinstance(servers_response, dict):
                                if servers_response.get('success'):
                                    servers_data = servers_response.get('data', [])
                                    logger.info(f"Got {len(servers_data)} servers from VPN API (success wrapper)")
                                elif 'data' in servers_response:
                                    servers_data = servers_response['data']
                                    logger.info(f"Got {len(servers_data)} servers from VPN API (data key)")
                                elif 'error' in servers_response:
                                    # API returned an error
                                    servers_api_error = servers_response['error']
                                    logger.error(f"VPN API returned error: {servers_api_error}")
                                else:
                                    # Assume the dict itself contains server data
                                    servers_data = [servers_response] if servers_response else []
                                    logger.info(f"Got {len(servers_data)} servers from VPN API (single server)")
                        else:
                            servers_api_error = "VPN API returned None - likely authentication failure or service unavailable"
                            logger.error(servers_api_error)
                    else:
                        servers_api_error = "VPN API service does not have get_servers method"
                        logger.error(servers_api_error)
                else:
                    servers_api_error = "VPN API service not available in config_service"
                    logger.warning(servers_api_error)
            except Exception as e:
                servers_api_error = f"Exception while getting servers from VPN API: {str(e)}"
                logger.error(servers_api_error)
            
            # Map server data to our format
            for server in servers_data:
                try:
                    mapped_server = {
                        'id': server.get('id', 0),
                        'name': server.get('name', f"Server {server.get('id', 'Unknown')}"),
                        'location': server.get('description', 'Unknown'),  # Use description as location
                        'status': 'online' if server.get('is_active', False) else 'offline',
                        'host': server.get('host', ''),
                        'health_status': server.get('health_status', 'unknown'),
                        'last_health_check': server.get('last_health_check', '')
                    }
                    result['servers'].append(mapped_server)
                except Exception as e:
                    logger.error(f"Error mapping server data: {e}")
                    continue
            
            logger.info(f"Mapped {len(result['servers'])} servers successfully")
            
            # Get telco configurations from config service
            try:
                telcos_data = self.config_service.get_all_telcos()
                total_plans = 0
                
                for telco_id, telco_obj in telcos_data.items():
                    try:
                        # Convert telco object to dict format
                        telco_dict = {
                            'name': telco_obj.name,
                            'enabled': telco_obj.enabled,
                            'plans': {}
                        }
                        
                        # Add plans
                        for plan_id, plan_obj in telco_obj.plans.items():
                            telco_dict['plans'][plan_id] = {
                                'name': plan_obj.name,
                                'description': plan_obj.description,
                                'enabled': plan_obj.enabled,
                                'template': plan_obj.template
                            }
                            if plan_obj.enabled:
                                total_plans += 1
                        
                        result['telcos'][telco_id] = telco_dict
                    except Exception as e:
                        logger.error(f"Error processing telco {telco_id}: {e}")
                        continue
                
                result['total_plans'] = total_plans
                logger.info(f"Got {len(result['telcos'])} telcos with {total_plans} total enabled plans")
                
            except Exception as e:
                logger.error(f"Error getting telco configurations: {e}")
                result['error'] = f"Failed to load telco configurations: {str(e)}"
            
            # If no servers were found, provide informative error message
            if not result['servers']:
                if servers_api_error:
                    result['error'] = servers_api_error
                    logger.warning(f"No servers found due to API error: {servers_api_error}")
                else:
                    result['error'] = "No servers available. Please check VPN API configuration."
                    logger.warning("No servers found, providing fallback message")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in get_servers_and_configs: {e}")
            return {
                'servers': [],
                'telcos': {},
                'total_plans': 0,
                'error': f"Failed to retrieve server and configuration data: {str(e)}"
            }

class VPNOrderService:
    """Service for handling VPN order processing and user management"""
    
    def __init__(self, plugin_dir: str, plugin_manager=None):
        self.plugin_dir = plugin_dir
        self.plugin_manager = plugin_manager

        # Use configs directory for persistent storage
        self.configs_dir = os.path.join(os.path.dirname(os.path.dirname(plugin_dir)), 'configs', 'data')
        os.makedirs(self.configs_dir, exist_ok=True)

        self.users_file = os.path.join(self.configs_dir, 'vpn_users.json')
        self.orders_file = os.path.join(self.configs_dir, 'manual_orders.json')

        self._users: Dict[str, VPNUser] = {}
        self._orders: List[Dict[str, Any]] = []

        self.load_users()
        self.load_orders()

    def load_users(self):
        """Load VPN users from file"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._users = {key: VPNUser.from_dict(value) for key, value in data.items()}
                logger.info(f"Loaded {len(self._users)} VPN users")
            else:
                self.save_users()
        except Exception as e:
            logger.error(f"Error loading VPN users: {e}")

    def save_users(self):
        """Save VPN users to file"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                data = {key: user.to_dict() for key, user in self._users.items()}
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved VPN users")
        except Exception as e:
            logger.error(f"Error saving VPN users: {e}")

    def load_orders(self):
        """Load manual/fake orders from file"""
        try:
            if os.path.exists(self.orders_file):
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    self._orders = json.load(f)
                logger.info(f"Loaded {len(self._orders)} manual/fake orders")
            else:
                self.save_orders()
        except Exception as e:
            logger.error(f"Error loading manual/fake orders: {e}")

    def save_orders(self):
        """Save manual/fake orders to file"""
        try:
            with open(self.orders_file, 'w', encoding='utf-8') as f:
                json.dump(self._orders, f, indent=2, ensure_ascii=False)
            logger.info("Saved manual/fake orders")
        except Exception as e:
            logger.error(f"Error saving manual/fake orders: {e}")

    def get_user(self, user_uuid: str) -> Optional[VPNUser]:
        """Get a user by UUID"""
        return self._users.get(user_uuid)

    def get_user_by_username(self, username: str) -> Optional[VPNUser]:
        """Get a user by Shopee username"""
        for user in self._users.values():
            if user.buyer_username == username:
                return user
        return None

    def create_user(self, order_details: Dict[str, Any]) -> VPNUser:
        """Create a new VPN user"""
        user_uuid = str(uuid.uuid4())
        user = VPNUser(
            uuid=user_uuid,
            order_sn=order_details['order_sn'],
            buyer_username=order_details['buyer_username'],
            sku=order_details.get('sku', ''),
            var_sku=order_details.get('var_sku', ''),
            created_at=datetime.now().isoformat()
        )
        self._users[user_uuid] = user
        self.save_users()
        logger.info(f"Created new VPN user: {user.buyer_username} ({user.uuid})")
        return user

    def process_order(self, order_request: VPNOrderRequest) -> VPNOrderResponse:
        """Process an order, either by retrieving from Shopee API or using a fake order"""
        try:
            # First, try to get order from Shopee API
            try:
                from services.order_service import get_order_details
                order_details, status_code = get_order_details(order_request.order_sn)

                if status_code == 200 and order_details:
                    logger.info(f"Successfully retrieved order {order_request.order_sn} from Shopee API")
                    return self._process_valid_order(order_details)
                else:
                    logger.warning(f"Failed to get order {order_request.order_sn} from Shopee API, trying fake orders...")
            except ImportError:
                logger.warning("Shopee order service not available, using fake orders only")
            except Exception as e:
                logger.error(f"Error getting order from Shopee API: {e}, trying fake orders...")

            # If Shopee API fails, try to find a fake order
            fake_order = self._find_fake_order(order_request.order_sn)
            if fake_order:
                logger.info(f"Found fake order for {order_request.order_sn}")
                return self._process_valid_order(fake_order)

            return VPNOrderResponse(
                success=False,
                order_sn=order_request.order_sn,
                error="Order not found",
                status="Not Found"
            )

        except Exception as e:
            logger.error(f"Error processing order: {e}")
            return VPNOrderResponse(
                success=False,
                order_sn=order_request.order_sn,
                error=str(e),
                status="Error"
            )

    def _find_fake_order(self, order_sn: str) -> Optional[Dict[str, Any]]:
        """Find a fake order by order_sn"""
        for order in self._orders:
            if order.get('order_sn') == order_sn:
                return order
        return None

    def _process_valid_order(self, order_details: Dict[str, Any]) -> VPNOrderResponse:
        """Process a valid order (from Shopee API or fake order)"""
        try:
            # Extract data from nested structure (Shopee API format) or flat structure (fake orders)
            extracted_data = self._extract_order_data(order_details)
            
            # Block refunded orders from generating VPN configs
            order_status = extracted_data.get('status', '').lower()
            if order_status in ['refunded', 'refund', 'cancelled']:
                logger.warning(f"Blocking refunded/cancelled order {extracted_data['order_sn']} from VPN config generation")
                return VPNOrderResponse(
                    success=False,
                    order_sn=extracted_data['order_sn'],
                    error=f"Cannot generate VPN config for {order_status} order. Please contact support if you believe this is an error.",
                    status=extracted_data.get('status', 'Unknown')
                )

            # Check if user already exists
            user = self.get_user_by_username(extracted_data['buyer_username'])
            is_repeat_customer = user is not None

            if not user:
                user = self.create_user(extracted_data)

            # Update user info with latest order details
            user.order_sn = extracted_data['order_sn']
            user.sku = extracted_data.get('sku', user.sku)
            user.var_sku = extracted_data.get('var_sku', user.var_sku)
            user.last_access = datetime.now().isoformat()

            # Apply SKU-based restrictions
            restriction = SKURestrictionService.get_restriction_for_sku(user.var_sku)
            if restriction:
                user.is_restricted = True
                user.allowed_telcos = restriction.allowed_telcos
                if restriction.single_telco_only and not user.assigned_telco:
                    # This user will be locked to the first telco they choose
                    logger.info(f"User {user.buyer_username} has single_telco_only restriction")
                elif restriction.single_telco_only and user.assigned_telco:
                    logger.info(f"User {user.buyer_username} is locked to telco: {user.assigned_telco}")
            else:
                user.is_restricted = False

            self.save_users()

            return VPNOrderResponse(
                success=True,
                user_uuid=user.uuid,
                order_sn=user.order_sn or order_details.get('order_sn'),
                buyer_username=user.buyer_username,
                sku=user.sku,
                var_sku=user.var_sku,
                is_repeat_customer=is_repeat_customer,
                assigned_telco=user.assigned_telco,
                allowed_telcos=user.allowed_telcos,
                is_restricted=user.is_restricted,
                message="Order verified successfully",
                status=extracted_data.get('status', 'Unknown')
            )

        except Exception as e:
            logger.error(f"Error processing valid order: {e}")
            return VPNOrderResponse(
                success=False,
                order_sn=order_details.get('order_sn', 'UNKNOWN') if 'extracted_data' in locals() else 'UNKNOWN',
                error=str(e),
                status="Error"
            )

    def _extract_order_data(self, order_details: Dict[str, Any]) -> Dict[str, Any]:
        """Extract order data from nested Shopee API structure or flat fake order structure"""
        try:
            # Check if this is already a flat structure (direct fake order)
            if 'buyer_username' in order_details:
                return order_details

            # Handle nested Shopee API structure
            if 'data' in order_details:
                data = order_details['data']

                # Extract buyer username
                buyer_username = 'unknown_user'
                if 'buyer_user' in data and 'user_name' in data['buyer_user']:
                    buyer_username = data['buyer_user']['user_name']

                # Extract order_sn
                order_sn = data.get('order_sn', 'UNKNOWN')

                # Extract var_sku from order items
                var_sku = None
                sku = None
                if 'order_items' in data and data['order_items']:
                    first_item = data['order_items'][0]
                    var_sku = first_item.get('var_sku')
                    if 'item_model' in first_item:
                        sku = first_item['item_model'].get('sku')

                # Extract status - prefer string status from status_info over numeric status
                status = 'Unknown'
                if 'status_info' in data and 'status' in data['status_info']:
                    status = data['status_info']['status']
                elif 'status' in data:
                    # Fallback to numeric status if string status not available
                    numeric_status = data['status']
                    # Map common numeric statuses to strings
                    status_map = {
                        1: 'To Ship',
                        2: 'Shipped',
                        3: 'Completed',
                        4: 'Cancelled',
                        5: 'Refunded'  # Add refund status mapping
                    }
                    status = status_map.get(numeric_status, f'Status_{numeric_status}')

                return {
                    'buyer_username': buyer_username,
                    'order_sn': order_sn,
                    'var_sku': var_sku,
                    'sku': sku,
                    'status': status
                }

            # Fallback - return original data with defaults
            return {
                'buyer_username': order_details.get('buyer_username', 'unknown_user'),
                'order_sn': order_details.get('order_sn', 'UNKNOWN'),
                'var_sku': order_details.get('var_sku'),
                'sku': order_details.get('sku'),
                'status': order_details.get('status', 'Unknown')
            }

        except Exception as e:
            logger.error(f"Error extracting order data: {e}")
            # Return minimal data structure to prevent further errors
            return {
                'buyer_username': 'extraction_error_user',
                'order_sn': order_details.get('order_sn', 'UNKNOWN'),
                'var_sku': None,
                'sku': None,
                'status': 'Error'
            }

    def can_user_access_telco(self, user_uuid: str, telco_id: str) -> Tuple[bool, str]:
        """Check if a user can access a specific telco"""
        user = self.get_user(user_uuid)
        if not user:
            return False, "User not found"

        if not user.is_restricted:
            return True, ""

        if user.assigned_telco:
            if user.assigned_telco == telco_id:
                return True, ""
            else:
                return False, f"You are restricted to the {user.assigned_telco} telco."
        
        if user.allowed_telcos and telco_id not in user.allowed_telcos:
            return False, f"Your SKU does not permit access to the {telco_id} telco."

        return True, ""

    def update_user_telco_selection(self, user_uuid: str, telco_id: str):
        """Update user's assigned telco if they have a single_telco_only restriction"""
        user = self.get_user(user_uuid)
        if not user:
            return

        restriction = SKURestrictionService.get_restriction_for_sku(user.var_sku)
        if restriction and restriction.single_telco_only and not user.assigned_telco:
            user.assigned_telco = telco_id
            self.save_users()
            logger.info(f"User {user.buyer_username} is now locked to telco: {telco_id}")

    def add_generated_config_to_user(self, user_uuid: str, config_info: Dict[str, Any]):
        """Add a generated configuration to a user's record"""
        user = self.get_user(user_uuid)
        if not user:
            return

        if not hasattr(user, 'generated_configs') or not user.generated_configs:
            user.generated_configs = []

        user.generated_configs.append(config_info)
        self.save_users()

    def get_user_generated_configs(self, user_uuid: str) -> List[Dict[str, Any]]:
        """Get all generated configurations for a user by user UUID"""
        user = self.get_user(user_uuid)
        if user and hasattr(user, 'generated_configs'):
            return user.generated_configs
        return []

    def get_renewal_days_from_sku(self, sku: str) -> int:
        """Get renewal days based on SKU pattern"""
        if not sku:
            return 30
        
        sku_lower = sku.lower()
        if 'weekly' in sku_lower or '7d' in sku_lower:
            return 7
        elif 'monthly' in sku_lower or '30d' in sku_lower:
            return 30
        elif 'yearly' in sku_lower or '365d' in sku_lower:
            return 365
        else:
            return 30

    def renew_vpn_config(self, renewal_request) -> 'VPNRenewalResponse':
        """Renew a VPN configuration"""
        try:
            from .models import VPNRenewalResponse
            
            # Get VPN plugin to access API
            if not self.plugin_manager:
                return VPNRenewalResponse(
                    success=False,
                    error="Plugin manager not available"
                )

            vpn_plugin = self.plugin_manager.get_plugin('vpn')
            if not vpn_plugin or not hasattr(vpn_plugin, 'api_service'):
                return VPNRenewalResponse(
                    success=False,
                    error="VPN plugin not available"
                )

            vpn_api = vpn_plugin.api_service

            # Extend the client expiry
            success = vpn_api.extend_client_expiry(renewal_request.client_id, renewal_request.days)
            
            if success:
                # Get updated client info
                updated_client = vpn_api.get_client(renewal_request.client_id)
                new_expiry = updated_client.get('expired_date', 'N/A') if updated_client else 'N/A'

                # Format new expiry date
                if new_expiry != 'N/A':
                    try:
                        from datetime import datetime
                        exp_date = datetime.fromisoformat(new_expiry.replace('Z', '+00:00'))
                        new_expiry_formatted = exp_date.strftime('%d-%m-%Y')
                    except:
                        new_expiry_formatted = new_expiry
                else:
                    new_expiry_formatted = new_expiry

                # Update user's configuration record
                user = self.get_user(renewal_request.user_uuid)
                if user and hasattr(user, 'generated_configs'):
                    for config in user.generated_configs:
                        if (config.get('numeric_id') == renewal_request.client_id or
                            str(config.get('client_id')) == str(renewal_request.client_id)):
                            config['expired_date'] = new_expiry_formatted
                            config['last_renewed'] = datetime.now().isoformat()
                            config['days_extended'] = renewal_request.days
                            break
                    self.save_users()

                return VPNRenewalResponse(
                    success=True,
                    client_id=renewal_request.client_id,
                    new_expiry_date=new_expiry_formatted,
                    days_extended=renewal_request.days,
                    message=f"Configuration renewed successfully for {renewal_request.days} days"
                )
            else:
                return VPNRenewalResponse(
                    success=False,
                    error="Failed to extend client expiry via VPN API"
                )

        except Exception as e:
            logger.error(f"Error renewing VPN config: {e}")
            return VPNRenewalResponse(
                success=False,
                error=str(e)
            )

    def get_user_configs_by_buyer_username(self, buyer_username: str) -> List[Dict[str, Any]]:
        """Get all generated configurations for a user by buyer username"""
        user = self.get_user_by_username(buyer_username)
        if user and hasattr(user, 'generated_configs'):
            return user.generated_configs
        return []

    def get_renewal_days_from_sku(self, var_sku: str) -> int:
        """Get renewal days from SKU using VPN strategy factory"""
        try:
            if VPN_STRATEGY_AVAILABLE:
                return VPNStrategyFactory.get_validity_days_for_sku(var_sku)
            return 30  # Default
        except Exception as e:
            logger.warning(f"Could not get renewal days from SKU: {e}")
            return 30

    def renew_vpn_config(self, renewal_request) -> Dict[str, Any]:
        """Renew VPN configuration by extending expiry date"""
        try:
            # Get VPN plugin to call its API
            if not self.plugin_manager:
                return {'success': False, 'error': 'Plugin manager not available'}

            vpn_plugin = self.plugin_manager.get_plugin('vpn')
            if not vpn_plugin or not hasattr(vpn_plugin, 'api_service'):
                return {'success': False, 'error': 'VPN service not available'}

            # Call renew_client method in VPNAPIService
            result = vpn_plugin.api_service.renew_client(
                client_id=renewal_request.client_id,
                days=renewal_request.days
            )

            if result and result.get('success'):
                # Update user's record
                user = self.get_user(renewal_request.user_uuid)
                if user:
                    # Mark order as claimed
                    if not user.order_claimed:
                        user.order_claimed = True
                        logger.info(f"Marked order as claimed for user {user.uuid} after renewing configuration")
                    
                    # Add renewal record
                    if not hasattr(user, 'renewals'):
                        user.renewals = []
                    user.renewals.append({
                        'order_sn': renewal_request.order_sn,
                        'client_id': renewal_request.client_id,
                        'days_extended': renewal_request.days,
                        'new_expiry_date': result.get('new_expiry_date'),
                        'renewed_at': datetime.now().isoformat()
                    })
                    self.save_users()

                return {
                    'success': True,
                    'client_id': renewal_request.client_id,
                    'new_expiry_date': result.get('new_expiry_date'),
                    'days_extended': renewal_request.days,
                    'message': 'Configuration renewed successfully'
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Failed to renew configuration')
                }

        except Exception as e:
            logger.error(f"Error renewing VPN config: {e}")
            return {'success': False, 'error': str(e)}

    def check_and_remove_refunded_configs(self) -> Dict[str, Any]:
        """Check all existing generated configs for refunded orders and remove VPN clients"""
        try:
            from services.order_service import get_order_details
            
            removed_clients = []
            checked_orders = []
            errors = []
            
            logger.info("Starting refund detection for existing VPN configurations...")
            
            for user_uuid, user in self._users.items():
                if not hasattr(user, 'generated_configs') or not user.generated_configs:
                    continue
                    
                for config in user.generated_configs:
                    order_sn = config.get('order_sn') or user.order_sn
                    if not order_sn or order_sn in checked_orders:
                        continue
                        
                    checked_orders.append(order_sn)
                    
                    try:
                        # Check order status from ShopeeAPI
                        order_details, status_code = get_order_details(order_sn)
                        
                        if status_code == 200 and order_details:
                            extracted_data = self._extract_order_data(order_details)
                            order_status = extracted_data.get('status', '').lower()
                            
                            if order_status in ['refunded', 'refund', 'cancelled']:
                                logger.warning(f"Found refunded order {order_sn} with existing VPN config")
                                
                                # Try to delete VPN client
                                client_id = config.get('numeric_id') or config.get('client_id')
                                if client_id:
                                    delete_result = self._delete_vpn_client(client_id)
                                    if delete_result['success']:
                                        removed_clients.append({
                                            'order_sn': order_sn,
                                            'client_id': client_id,
                                            'buyer_username': user.buyer_username,
                                            'status': order_status
                                        })
                                        logger.info(f"Successfully deleted VPN client {client_id} for refunded order {order_sn}")
                                    else:
                                        errors.append(f"Failed to delete client {client_id} for order {order_sn}: {delete_result['error']}")
                                        
                    except ImportError:
                        logger.warning("ShopeeAPI order service not available for refund detection")
                        break
                    except Exception as e:
                        errors.append(f"Error checking order {order_sn}: {str(e)}")
                        logger.error(f"Error checking order {order_sn}: {e}")
            
            result = {
                'success': True,
                'removed_clients': removed_clients,
                'checked_orders_count': len(checked_orders),
                'removed_count': len(removed_clients),
                'errors': errors
            }
            
            logger.info(f"Refund detection completed: {len(removed_clients)} clients removed from {len(checked_orders)} orders checked")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in refund detection: {e}")
            return {
                'success': False,
                'error': str(e),
                'removed_clients': [],
                'checked_orders_count': 0,
                'removed_count': 0,
                'errors': [str(e)]
            }
    
    def _delete_vpn_client(self, client_id: str) -> Dict[str, Any]:
        """Delete VPN client using blueblue VPS helper API"""
        try:
            import requests
            
            # Get API config
            api_config = self.config_service.get_api_config() if hasattr(self, 'config_service') else None
            if not api_config:
                return {'success': False, 'error': 'VPN API configuration not available'}
            
            # Try primary API first
            if api_config.api_endpoint and api_config.username and api_config.password:
                try:
                    # Login to get token
                    login_response = requests.post(
                        f"{api_config.api_endpoint}/api/v1/login",
                        json={
                            "username": api_config.username,
                            "password": api_config.password
                        },
                        timeout=api_config.timeout
                    )
                    
                    if login_response.status_code == 200:
                        auth_data = login_response.json()
                        token = auth_data.get('access_token')
                        
                        if token:
                            # Delete client
                            delete_response = requests.delete(
                                f"{api_config.api_endpoint}/api/v1/clients/{client_id}",
                                headers={'Authorization': f'Bearer {token}'},
                                timeout=api_config.timeout
                            )
                            
                            if delete_response.status_code == 200:
                                logger.info(f"Successfully deleted VPN client {client_id} via primary API")
                                return {'success': True, 'method': 'primary_api'}
                            else:
                                logger.warning(f"Primary API delete failed for client {client_id}: {delete_response.status_code}")
                                
                except Exception as e:
                    logger.warning(f"Primary API delete failed for client {client_id}: {e}")
            
            # Try fallback API
            if api_config.fallback_api_endpoint and api_config.fallback_username and api_config.fallback_password:
                try:
                    session = requests.Session()
                    
                    # Login to fallback API
                    login_response = session.post(
                        f"{api_config.fallback_api_endpoint}/api/v1/login",
                        json={
                            "username": api_config.fallback_username,
                            "password": api_config.fallback_password
                        },
                        timeout=api_config.timeout
                    )
                    
                    if login_response.status_code == 200:
                        auth_data = login_response.json()
                        token = auth_data.get('access_token')
                        
                        if token:
                            session.headers.update({'Authorization': f'Bearer {token}'})
                            
                            # Delete client
                            delete_response = session.delete(
                                f"{api_config.fallback_api_endpoint}/api/v1/clients/{client_id}",
                                timeout=api_config.timeout
                            )
                            
                            if delete_response.status_code == 200:
                                logger.info(f"Successfully deleted VPN client {client_id} via fallback API")
                                return {'success': True, 'method': 'fallback_api'}
                            else:
                                logger.warning(f"Fallback API delete failed for client {client_id}: {delete_response.status_code}")
                                
                except Exception as e:
                    logger.warning(f"Fallback API delete failed for client {client_id}: {e}")
            
            return {'success': False, 'error': 'All API endpoints failed to delete client'}
            
        except Exception as e:
            logger.error(f"Error deleting VPN client {client_id}: {e}")
            return {'success': False, 'error': str(e)}

class SKURestrictionService:
    """Service for managing SKU-based restrictions"""
    _restrictions: List[SKURestriction] = []
    _config_file: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'configs', 'plugins', 'vpn_config_generator', 'sku_restrictions.json')

    @classmethod
    def load_restrictions(cls):
        """Load SKU restrictions from file"""
        try:
            if os.path.exists(cls._config_file):
                with open(cls._config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    cls._restrictions = [SKURestriction.from_dict(r) for r in data]
                logger.info(f"Loaded {len(cls._restrictions)} SKU restrictions")
            else:
                cls.save_restrictions([])
        except Exception as e:
            logger.error(f"Error loading SKU restrictions: {e}")

    @classmethod
    def save_restrictions(cls, restrictions: List[SKURestriction]) -> bool:
        """Save SKU restrictions to file"""
        try:
            with open(cls._config_file, 'w', encoding='utf-8') as f:
                json.dump([r.to_dict() for r in restrictions], f, indent=2, ensure_ascii=False)
            logger.info("Saved SKU restrictions")
            cls._restrictions = restrictions
            return True
        except Exception as e:
            logger.error(f"Error saving SKU restrictions: {e}")
            return False

    @classmethod
    def get_all_restrictions(cls) -> List[SKURestriction]:
        """Get all SKU restrictions"""
        if not cls._restrictions:
            cls.load_restrictions()
        return cls._restrictions

    @classmethod
    def add_restriction(cls, restriction: SKURestriction) -> bool:
        """Add a new SKU restriction"""
        restrictions = cls.get_all_restrictions()
        restrictions.append(restriction)
        return cls.save_restrictions(restrictions)

    @classmethod
    def remove_restriction(cls, index: int) -> bool:
        """Remove an SKU restriction by index"""
        restrictions = cls.get_all_restrictions()
        if 0 <= index < len(restrictions):
            del restrictions[index]
            return cls.save_restrictions(restrictions)
        return False

    @classmethod
    def get_restriction_for_sku(cls, sku: str) -> Optional[SKURestriction]:
        """Get the first matching and enabled SKU restriction for a given SKU"""
        if not sku:
            return None

        restrictions = cls.get_all_restrictions()
        for restriction in restrictions:
            if restriction.enabled and restriction.matches_sku(sku):
                return restriction
        return None

    @classmethod
    def get_restrictions_stats(cls) -> Dict[str, Any]:
        """Get statistics about SKU restrictions"""
        restrictions = cls.get_all_restrictions()
        return {
            'total_restrictions': len(restrictions),
            'enabled_restrictions': sum(1 for r in restrictions if r.enabled),
            'disabled_restrictions': sum(1 for r in restrictions if not r.enabled),
            'restriction_types': {
                'single_telco_only': sum(1 for r in restrictions if r.single_telco_only),
                'allowed_telcos': sum(1 for r in restrictions if r.allowed_telcos),
                'max_configurations': sum(1 for r in restrictions if r.max_configurations > 0)
            }
        }

class VPNSKUTagsService:
    """Service for managing SKU to Server Tags mapping"""
    
    def __init__(self):
        self.config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'configs', 'core')
        self.config_file = os.path.join(self.config_dir, 'vpn_sku_server_tags.json')
        os.makedirs(self.config_dir, exist_ok=True)

    def load_config(self) -> Dict[str, Any]:
        """Load SKU server tags mapping from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Create a default config if it doesn't exist
                default_config = {
                    "version": "1.0",
                    "last_updated": datetime.now().isoformat(),
                    "sku_server_tags_mapping": {},
                    "fallback_mapping": {},
                    "tag_definitions": {}
                }
                self.save_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"Error loading SKU server tags mapping: {e}")
            return {}

    def save_config(self, config: Dict[str, Any]) -> bool:
        """Save SKU server tags mapping to file"""
        try:
            config['last_updated'] = datetime.now().isoformat()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info("Saved SKU server tags mapping")
            return True
        except Exception as e:
            logger.error(f"Error saving SKU server tags mapping: {e}")
            return False

    def add_sku_mapping(self, category: str, sku: str, tags: List[str], validity_days: Optional[int] = None) -> bool:
        """Add or update a SKU to server tags mapping"""
        config = self.load_config()
        if category not in config['sku_server_tags_mapping']:
            config['sku_server_tags_mapping'][category] = {}
        
        config['sku_server_tags_mapping'][category][sku] = {
            'tags': tags,
            'validity_days': validity_days
        }
        return self.save_config(config)

    def update_sku_mapping(self, category: str, sku: str, tags: List[str], validity_days: Optional[int] = None) -> bool:
        """Update an existing SKU mapping"""
        return self.add_sku_mapping(category, sku, tags, validity_days)

    def delete_sku_mapping(self, category: str, sku: str) -> bool:
        """Delete a SKU mapping"""
        config = self.load_config()
        if category in config['sku_server_tags_mapping'] and sku in config['sku_server_tags_mapping'][category]:
            del config['sku_server_tags_mapping'][category][sku]
            if not config['sku_server_tags_mapping'][category]:
                del config['sku_server_tags_mapping'][category]
            return self.save_config(config)
        return False

    def add_tag_definition(self, tag: str, description: str) -> bool:
        """Add or update a tag definition"""
        config = self.load_config()
        if 'tag_definitions' not in config:
            config['tag_definitions'] = {}
        config['tag_definitions'][tag] = description
        return self.save_config(config)

    def delete_tag_definition(self, tag: str) -> bool:
        """Delete a tag definition"""
        config = self.load_config()
        if 'tag_definitions' in config and tag in config['tag_definitions']:
            del config['tag_definitions'][tag]
            return self.save_config(config)
        return False


class RedemptionLinkService:
    """Service for managing VPN redemption links"""

    def __init__(self, plugin_dir: str, plugin_manager=None):
        self.plugin_dir = plugin_dir
        self.plugin_manager = plugin_manager

        # Use configs directory for persistent storage
        self.configs_dir = os.path.join(os.path.dirname(os.path.dirname(plugin_dir)), 'configs', 'data')
        os.makedirs(self.configs_dir, exist_ok=True)

        self.redemption_links_file = os.path.join(self.configs_dir, 'redemption_links.json')
        self.chat_template_file = os.path.join(self.configs_dir, 'chat_template_config.json')

        self._redemption_links: Dict[str, 'RedemptionLink'] = {}
        self._chat_template_config: ChatTemplateConfig = ChatTemplateConfig()

        self.load_redemption_links()
        self.load_chat_template_config()

    def load_redemption_links(self):
        """Load redemption links from file"""
        try:
            if os.path.exists(self.redemption_links_file):
                with open(self.redemption_links_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    from .models import RedemptionLink
                    self._redemption_links = {key: RedemptionLink.from_dict(value) for key, value in data.items()}
                logger.info(f"Loaded {len(self._redemption_links)} redemption links")
            else:
                self.save_redemption_links()
        except Exception as e:
            logger.error(f"Error loading redemption links: {e}")

    def save_redemption_links(self):
        """Save redemption links to file"""
        try:
            with open(self.redemption_links_file, 'w', encoding='utf-8') as f:
                data = {key: link.to_dict() for key, link in self._redemption_links.items()}
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info("Saved redemption links")
        except Exception as e:
            logger.error(f"Error saving redemption links: {e}")

    def load_chat_template_config(self):
        """Load chat template configuration from file"""
        try:
            if os.path.exists(self.chat_template_file):
                with open(self.chat_template_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._chat_template_config = ChatTemplateConfig.from_dict(data)
                logger.info("Loaded chat template configuration")
            else:
                self.save_chat_template_config()
        except Exception as e:
            logger.error(f"Error loading chat template configuration: {e}")

    def save_chat_template_config(self):
        """Save chat template configuration to file"""
        try:
            with open(self.chat_template_file, 'w', encoding='utf-8') as f:
                json.dump(self._chat_template_config.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info("Saved chat template configuration")
        except Exception as e:
            logger.error(f"Error saving chat template configuration: {e}")

    def get_chat_template_config(self) -> ChatTemplateConfig:
        """Get current chat template configuration"""
        return self._chat_template_config

    def update_chat_template_config(self, config: ChatTemplateConfig) -> bool:
        """Update chat template configuration"""
        try:
            self._chat_template_config = config
            self.save_chat_template_config()
            return True
        except Exception as e:
            logger.error(f"Error updating chat template configuration: {e}")
            return False

    def create_redemption_link(self, request: 'RedemptionLinkRequest') -> 'RedemptionLinkResponse':
        """Create a new redemption link"""
        try:
            from .models import RedemptionLink, RedemptionLinkResponse

            # Create the redemption link
            link = RedemptionLink(
                id=str(uuid.uuid4()),
                customer_username=request.customer_username,
                validity_days=request.validity_days,
                server_id=request.server_id,
                telco=request.telco,
                plan=request.plan,
                var_sku=request.var_sku,
                expires_at=request.expires_at,
                notes=request.notes,
                max_uses=request.max_uses,
                created_by=request.created_by
            )

            # Store the link
            self._redemption_links[link.id] = link
            self.save_redemption_links()

            # Generate the redemption URL
            redemption_url = f"/vpn-config-generator/redeem/{link.id}"

            logger.info(f"Created redemption link {link.id} for customer {request.customer_username}")

            return RedemptionLinkResponse(
                success=True,
                redemption_link=link,
                redemption_url=redemption_url,
                message=f"Redemption link created successfully for {request.customer_username}"
            )

        except Exception as e:
            logger.error(f"Error creating redemption link: {e}")
            return RedemptionLinkResponse(
                success=False,
                error=str(e)
            )

    def get_redemption_link(self, link_id: str) -> Optional['RedemptionLink']:
        """Get a redemption link by ID"""
        return self._redemption_links.get(link_id)

    def get_all_redemption_links(self) -> Dict[str, 'RedemptionLink']:
        """Get all redemption links"""
        return self._redemption_links.copy()

    def validate_redemption_link(self, link_id: str) -> Tuple[bool, str, Optional['RedemptionLink']]:
        """Validate a redemption link"""
        link = self.get_redemption_link(link_id)

        if not link:
            return False, "Redemption link not found", None

        can_use, reason = link.can_be_used()
        return can_use, reason, link

    def use_redemption_link(self, link_id: str, used_by: str = None) -> bool:
        """Mark a redemption link as used"""
        link = self.get_redemption_link(link_id)
        if link:
            link.mark_used(used_by)
            self.save_redemption_links()
            logger.info(f"Redemption link {link_id} used by {used_by}")
            return True
        return False

    def deactivate_redemption_link(self, link_id: str) -> bool:
        """Deactivate a redemption link"""
        link = self.get_redemption_link(link_id)
        if link:
            link.is_active = False
            self.save_redemption_links()
            logger.info(f"Redemption link {link_id} deactivated")
            return True
        return False

    def delete_redemption_link(self, link_id: str) -> bool:
        """Delete a redemption link"""
        if link_id in self._redemption_links:
            del self._redemption_links[link_id]
            self.save_redemption_links()
            logger.info(f"Redemption link {link_id} deleted")
            return True
        return False

    def cleanup_expired_links(self) -> int:
        """Remove expired redemption links"""
        expired_count = 0
        expired_links = []

        for link_id, link in self._redemption_links.items():
            if not link.is_valid():
                expired_links.append(link_id)

        for link_id in expired_links:
            del self._redemption_links[link_id]
            expired_count += 1

        if expired_count > 0:
            self.save_redemption_links()
            logger.info(f"Cleaned up {expired_count} expired redemption links")

        return expired_count

    def send_redemption_link_via_chat(self, link_id: str, base_url: str = "") -> Tuple[bool, str]:
        """Send a redemption link via chat to the customer"""
        try:
            link = self.get_redemption_link(link_id)
            if not link:
                return False, "Redemption link not found"

            if not self._chat_template_config.enabled:
                return False, "Chat integration is disabled"

            # Get chat commands plugin
            if not self.plugin_manager:
                return False, "Plugin manager not available"

            chat_plugin = self.plugin_manager.get_plugin('chat_commands')
            if not chat_plugin:
                return False, "Chat commands plugin not found"

            # Get ShopeeAPI client from chat plugin
            if not hasattr(chat_plugin, 'message_processor') or not chat_plugin.message_processor:
                return False, "Chat message processor not available"

            shopee_client = chat_plugin.message_processor.shopee_api_client
            if not shopee_client:
                return False, "ShopeeAPI client not available"

            # Format the redemption URL
            redemption_url = f"/vpn-config-generator/redeem/{link_id}"

            # Format the chat message
            message = self._chat_template_config.format_message(link, redemption_url, base_url)

            # Send the message
            payload = {
                'username': link.customer_username,
                'text': message
            }

            result, status_code = shopee_client.send_chat_message(payload)

            if status_code == 200:
                logger.info(f"Successfully sent redemption link {link_id} to {link.customer_username} via chat")
                return True, "Message sent successfully"
            else:
                error_msg = f"Failed to send chat message: HTTP {status_code}"
                logger.error(f"Failed to send redemption link via chat: {error_msg}")
                return False, error_msg

        except Exception as e:
            error_msg = f"Error sending redemption link via chat: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def create_bulk_redemption_links(self, request: BulkCreationRequest, base_url: str = "") -> BulkCreationResult:
        """Create multiple redemption links in bulk"""
        try:
            successful_links = []
            failed_links = []
            chat_results = {} if request.send_via_chat else None

            for username in request.customer_usernames:
                try:
                    # Create individual redemption link request
                    individual_request = RedemptionLinkRequest(
                        customer_username=username,
                        validity_days=request.validity_days,
                        server_id=request.server_id,
                        telco=request.telco,
                        plan=request.plan,
                        var_sku=request.var_sku,
                        expires_at=request.expires_at,
                        notes=request.notes,
                        max_uses=request.max_uses,
                        created_by=request.created_by
                    )

                    # Create the redemption link
                    result = self.create_redemption_link(individual_request)

                    if result.success:
                        successful_links.append(result.redemption_link)

                        # Send via chat if requested
                        if request.send_via_chat:
                            chat_success, chat_message = self.send_redemption_link_via_chat(
                                result.redemption_link.id, base_url
                            )
                            chat_results[result.redemption_link.id] = chat_success

                            if not chat_success:
                                logger.warning(f"Failed to send chat for {username}: {chat_message}")
                    else:
                        failed_links.append({
                            'username': username,
                            'error': result.error
                        })

                except Exception as e:
                    failed_links.append({
                        'username': username,
                        'error': str(e)
                    })

            # Prepare result
            total_requested = len(request.customer_usernames)
            success_count = len(successful_links)

            result = BulkCreationResult(
                success=success_count > 0,
                total_requested=total_requested,
                successful_links=successful_links,
                failed_links=failed_links,
                chat_results=chat_results,
                message=f"Created {success_count}/{total_requested} redemption links successfully"
            )

            if success_count == 0:
                result.error = "Failed to create any redemption links"
            elif len(failed_links) > 0:
                result.message += f" ({len(failed_links)} failed)"

            logger.info(f"Bulk creation completed: {success_count}/{total_requested} successful")
            return result

        except Exception as e:
            logger.error(f"Error in bulk redemption link creation: {e}")
            return BulkCreationResult(
                success=False,
                total_requested=len(request.customer_usernames) if request.customer_usernames else 0,
                successful_links=[],
                failed_links=[],
                error=str(e)
            )

    def get_analytics(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> RedemptionAnalytics:
        """Generate analytics for redemption links"""
        try:
            from datetime import datetime, timedelta
            from collections import defaultdict

            # Filter links by date range if specified
            links_to_analyze = []
            for link in self._redemption_links.values():
                if start_date or end_date:
                    try:
                        link_date = datetime.fromisoformat(link.created_at)
                        if start_date:
                            start = datetime.fromisoformat(start_date)
                            if link_date < start:
                                continue
                        if end_date:
                            end = datetime.fromisoformat(end_date)
                            if link_date > end:
                                continue
                    except:
                        continue
                links_to_analyze.append(link)

            # Basic statistics
            total_links = len(links_to_analyze)
            active_links = sum(1 for link in links_to_analyze if link.is_active)
            used_links = sum(1 for link in links_to_analyze if link.current_uses > 0)
            expired_links = sum(1 for link in links_to_analyze if not link.is_valid())
            usage_rate = (used_links / total_links * 100) if total_links > 0 else 0.0

            # Trends analysis
            creation_trends = defaultdict(int)
            usage_trends = defaultdict(int)

            for link in links_to_analyze:
                # Creation trends
                try:
                    created_date = datetime.fromisoformat(link.created_at).strftime('%Y-%m-%d')
                    creation_trends[created_date] += 1
                except:
                    pass

                # Usage trends
                if link.used_at:
                    try:
                        used_date = datetime.fromisoformat(link.used_at).strftime('%Y-%m-%d')
                        usage_trends[used_date] += 1
                    except:
                        pass

            # Popular telcos and plans
            popular_telcos = defaultdict(int)
            popular_plans = defaultdict(int)
            creator_stats = defaultdict(int)

            time_to_use_samples = []

            for link in links_to_analyze:
                if link.telco:
                    popular_telcos[link.telco] += 1
                if link.plan:
                    popular_plans[link.plan] += 1
                if link.created_by:
                    creator_stats[link.created_by] += 1

                # Calculate time to use
                if link.used_at and link.created_at:
                    try:
                        created = datetime.fromisoformat(link.created_at)
                        used = datetime.fromisoformat(link.used_at)
                        hours_diff = (used - created).total_seconds() / 3600
                        time_to_use_samples.append(hours_diff)
                    except:
                        pass

            # Average time to use
            average_time_to_use = None
            if time_to_use_samples:
                average_time_to_use = sum(time_to_use_samples) / len(time_to_use_samples)

            return RedemptionAnalytics(
                total_links=total_links,
                active_links=active_links,
                used_links=used_links,
                expired_links=expired_links,
                usage_rate=round(usage_rate, 2),
                creation_trends=dict(creation_trends),
                usage_trends=dict(usage_trends),
                popular_telcos=dict(popular_telcos),
                popular_plans=dict(popular_plans),
                average_time_to_use=round(average_time_to_use, 2) if average_time_to_use else None,
                creator_stats=dict(creator_stats)
            )

        except Exception as e:
            logger.error(f"Error generating analytics: {e}")
            return RedemptionAnalytics()

    def export_analytics_data(self, format: str = 'json', start_date: Optional[str] = None, end_date: Optional[str] = None) -> Tuple[bool, str, Any]:
        """Export analytics data in specified format"""
        try:
            analytics = self.get_analytics(start_date, end_date)

            if format.lower() == 'json':
                return True, "Analytics data exported successfully", analytics.to_dict()
            elif format.lower() == 'csv':
                # For CSV, we'll export the links data
                import csv
                import io

                output = io.StringIO()
                writer = csv.writer(output)

                # Write header
                writer.writerow([
                    'Link ID', 'Customer Username', 'Validity Days', 'Server ID',
                    'Telco', 'Plan', 'Created At', 'Used At', 'Is Active',
                    'Current Uses', 'Max Uses', 'Created By', 'Notes'
                ])

                # Filter links by date range
                links_to_export = []
                for link in self._redemption_links.values():
                    if start_date or end_date:
                        try:
                            link_date = datetime.fromisoformat(link.created_at)
                            if start_date:
                                start = datetime.fromisoformat(start_date)
                                if link_date < start:
                                    continue
                            if end_date:
                                end = datetime.fromisoformat(end_date)
                                if link_date > end:
                                    continue
                        except:
                            continue
                    links_to_export.append(link)

                # Write data rows
                for link in links_to_export:
                    writer.writerow([
                        link.id, link.customer_username, link.validity_days,
                        link.server_id or '', link.telco or '', link.plan or '',
                        link.created_at, link.used_at or '', link.is_active,
                        link.current_uses, link.max_uses, link.created_by or '',
                        link.notes or ''
                    ])

                csv_data = output.getvalue()
                output.close()

                return True, "CSV data exported successfully", csv_data
            else:
                return False, f"Unsupported export format: {format}", None

        except Exception as e:
            logger.error(f"Error exporting analytics data: {e}")
            return False, str(e), None