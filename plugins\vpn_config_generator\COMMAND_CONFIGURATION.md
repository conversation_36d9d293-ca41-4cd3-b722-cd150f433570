# VPN Config Generator - Command Configuration System

This document explains the configurable command system for the VPN Config Generator plugin.

## Overview

The VPN Config Generator now supports fully configurable chat commands that can be managed through a web interface. All commands are automatically registered with the Chat Commands plugin for centralized management.

## Command Configuration Features

### 1. **Web-Based Management**
- **URL:** `/vpn-config-generator/command-management`
- **Features:**
  - Create new commands
  - Edit existing commands
  - Delete commands
  - Enable/disable commands
  - Real-time registration with Chat Commands plugin

### 2. **Configurable Command Properties**
- **Command Key:** Internal identifier (e.g., `v`, `vlist`)
- **Command Name:** What users type (e.g., `v`, `vlist`)
- **Description:** Command description for help systems
- **Response Text:** Default response header/text
- **Required Parameters:** List of required parameters
- **Enabled Status:** Enable/disable the command

### 3. **Automatic Registration**
- Commands are automatically registered with the Chat Commands plugin
- Changes trigger re-registration
- Supports multiple commands per plugin
- Maintains backward compatibility

## Default Commands

### 1. **VPN Generation Command (`v`)**
- **Command:** `#v`
- **Parameters:** `<server> <days> <telco> <plan>`
- **Function:** Generate VPN configurations
- **Configurable:** Yes (through legacy command config and new system)

### 2. **VPN List Command (`vlist`)**
- **Command:** `#vlist`
- **Parameters:** None
- **Function:** List available servers and configurations
- **Configurable:** Yes (through new command management system)

### 3. **VPN User Command (`vuser`)**
- **Command:** `#vuser`
- **Parameters:** `[username]` (optional)
- **Function:** View VPN configurations for a user
- **Configurable:** Yes (through new command management system)

### 4. **VPN Delete Command (`vdel`)**
- **Command:** `#vdel`
- **Parameters:** `<client_id>` (required)
- **Function:** Delete a VPN configuration
- **Configurable:** Yes (through new command management system)

### 5. **VPN Renew Command (`vrenew`)**
- **Command:** `#vrenew`
- **Parameters:** `<client_id> <days>` (both required)
- **Function:** Renew/extend a VPN configuration
- **Configurable:** Yes (through new command management system)

### 6. **VPN Test Command (`vtest`)**
- **Command:** `#vtest`
- **Parameters:** None
- **Function:** Test VPN API connectivity and configuration
- **Configurable:** Yes (through new command management system)

### 7. **VPN Servers Command (`vservers`)**
- **Command:** `#vservers`
- **Parameters:** None
- **Function:** List all VPN servers with IDs and status
- **Configurable:** Yes (through new command management system)

### 8. **VPN Help Command (`vhelp`)**
- **Command:** `#vhelp`
- **Parameters:** None
- **Function:** Display comprehensive help for all VPN commands
- **Configurable:** Yes (through new command management system)
- **Special Features:**
  - Dynamically adapts to configured command prefix
  - Shows all available commands with usage examples
  - Provides tips and best practices
  - Context-aware help content

## Configuration Files

### 1. **commands.json**
```json
{
  "v": {
    "command": "v",
    "description": "Generate a VPN configuration",
    "response_text": "🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️",
    "required_params": ["server", "days", "telco", "plan"],
    "enabled": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "vlist": {
    "command": "vlist",
    "description": "List available VPN servers and configurations",
    "response_text": "📋 Available VPN Servers & Configurations",
    "required_params": [],
    "enabled": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "vuser": {
    "command": "vuser",
    "description": "View VPN configurations for a user",
    "response_text": "👤 User VPN Configurations",
    "required_params": [],
    "enabled": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "vdel": {
    "command": "vdel",
    "description": "Delete a VPN configuration",
    "response_text": "🗑️ VPN Configuration Deleted",
    "required_params": ["client_id"],
    "enabled": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "vrenew": {
    "command": "vrenew",
    "description": "Renew/extend a VPN configuration",
    "response_text": "🔄 VPN Configuration Renewed",
    "required_params": ["client_id", "days"],
    "enabled": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. **command_config.json** (Legacy - for backward compatibility)
- Still used for the main `v` command configuration
- Synchronized with `commands.json`
- Maintains compatibility with existing interfaces

## API Endpoints

### Command Management APIs
- `GET /vpn-config-generator/api/commands` - Get all commands
- `POST /vpn-config-generator/api/commands` - Create new command
- `PUT /vpn-config-generator/api/commands/<key>` - Update command
- `DELETE /vpn-config-generator/api/commands/<key>` - Delete command

### Legacy APIs (Still supported)
- `GET /vpn-config-generator/api/command-config` - Get legacy config
- `PUT /vpn-config-generator/api/command-config` - Update legacy config

## Usage Examples

### Creating a New Command
1. Go to `/vpn-config-generator/command-management`
2. Click "Add Command"
3. Fill in the details:
   - **Command Key:** `vhelp`
   - **Command Name:** `vhelp`
   - **Description:** `Show VPN help information`
   - **Response Text:** `📚 VPN Help & Documentation`
   - **Required Parameters:** (leave empty)
   - **Enabled:** ✅

### Editing Existing Commands
1. Go to command management page
2. Click edit button on any command card
3. Modify the properties
4. Save changes
5. Commands are automatically re-registered

### Server Parameter Enhancement
The `v` command now supports both formats:
- **New:** `#v 11 30 digi unlimited` (direct server ID)
- **Legacy:** `#v server11 30 digi unlimited` (server name format)

## Integration with Chat Commands Plugin

### Automatic Registration
- Commands are registered when the plugin starts
- Re-registration happens when commands are modified
- Supports external command registration API
- Maintains command lifecycle management

### Command Handlers
- Each command has its own handler function
- `v` command → `_handle_config_command()`
- `vlist` command → `_handle_list_command()`
- New commands can be added with custom handlers

## Backward Compatibility

### Legacy Support
- Existing `command_config.json` still works
- Changes to legacy config sync to new system
- No breaking changes to existing functionality
- Gradual migration path available

### Migration Path
1. Existing installations continue to work
2. New commands can be added through new interface
3. Legacy commands can be migrated gradually
4. Full backward compatibility maintained

## Best Practices

### Command Naming
- Use short, memorable command names
- Follow consistent naming patterns
- Avoid conflicts with existing commands
- Consider user experience

### Parameter Design
- Keep required parameters minimal
- Use clear parameter names
- Provide good error messages
- Support optional parameters where appropriate

### Response Text
- Use clear, informative response headers
- Include emojis for visual appeal
- Keep responses concise but helpful
- Maintain consistent formatting

## Troubleshooting

### Common Issues
1. **Commands not registering:** Check Chat Commands plugin status
2. **Changes not taking effect:** Verify auto re-registration
3. **Parameter validation errors:** Check required_params configuration
4. **Handler not found:** Ensure handler function exists for new commands

### Debug Steps
1. Check plugin logs for registration errors
2. Verify commands.json syntax
3. Test command through web interface
4. Check Chat Commands plugin integration

## Future Enhancements

### Planned Features
- Command templates for quick setup
- Bulk command import/export
- Command usage analytics
- Advanced parameter validation
- Custom command handlers through configuration
