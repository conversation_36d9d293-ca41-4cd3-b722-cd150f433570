<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Redeem</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='spinkit.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='shepherd.css') }}">
    <script src="{{ url_for('static', filename='shepherd.js') }}"></script>
    <style>
        .modal-enter {
            opacity: 0;
            transform: scale(0.9);
        }

        .modal-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: opacity 300ms, transform 300ms;
        }

        .modal-exit {
            opacity: 1;
        }

        .modal-exit-active {
            opacity: 0;
            transform: scale(0.9);
            transition: opacity 300ms, transform 300ms;
        }

        .floating-label-input {
            position: relative;
        }

        .floating-label-input input {
            height: 3rem;
            padding-top: 1rem;
        }

        .floating-label-input label {
            position: absolute;
            top: 0.5rem;
            left: 0.75rem;
            transition: all 0.2s ease-out;
            pointer-events: none;
        }

        .floating-label-input input:focus+label,
        .floating-label-input input:not(:placeholder-shown)+label {
            font-size: 0.75rem;
            top: 0;
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            transform: rotate(180deg);
        }

        .wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 150px;
        }

        .wave .shape-fill {
            fill: #FFFFFF;
        }
    </style>
</head>

<body
    class="bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 min-h-screen flex items-center justify-center relative">
    <div class="wave">
        <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path
                d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
                class="shape-fill"></path>
        </svg>
    </div>
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
        <h1 class="text-2xl font-bold text-purple-800 mb-6">Order Redeem</h1>
        <form id="authForm" class="space-y-4">
            <div class="floating-label-input">
                <input type="text" id="orderId" name="orderId" required
                    class="px-3 mt-1 block w-full rounded-md border-purple-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 placeholder-transparent">
                <label for="orderId" class="text-sm font-medium text-purple-700">Order ID</label>
            </div>
            <button type="submit"
                class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 disabled:bg-purple-400 disabled:text-gray-200 disabled:cursor-not-allowed">
                Redeem Order
            </button>
        </form>
        <button id="helpButton"
            class="mt-4 w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50">
            Help
        </button>
    </div>

    <footer class="fixed bottom-0 w-full text-center py-2 bg-white bg-opacity-80 text-purple-800 z-20">
        &copy; 2024 Copyright. Powered by MTYB Official
    </footer>

    <!-- Modal -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden"
        onclick="handleModalClick(event)">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div id="modalContent" class="mt-3 text-center">
                <div id="loadingIndicator" class="mx-auto flex items-center justify-center h-12 w-12">
                    <div class="sk-wave sk-primary">
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                    </div>
                </div>
                <div id="successIndicator"
                    class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 hidden">
                    <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div id="errorIndicator"
                    class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 hidden">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2" id="modalTitle">Loading</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500" id="modalMessage">
                        Please Login Steam Now, System Waiting The Code.
                    </p>
                    <div id="credentialsContainer" class="mt-4 text-left hidden">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-small">Username:</span>
                            <div class="flex items-center">
                                <span id="steamUsername" class="mr-2"></span>
                                <button onclick="copyToClipboard('steamUsername')"
                                    class="text-purple-600 hover:text-purple-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                        <path
                                            d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="font-small">Password:</span>
                            <div class="flex items-center">
                                <span id="steamPassword" class="mr-2"></span>
                                <button onclick="copyToClipboard('steamPassword')"
                                    class="text-purple-600 hover:text-purple-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                        <path
                                            d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('authForm');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const successIndicator = document.getElementById('successIndicator');
        const errorIndicator = document.getElementById('errorIndicator');
        const submitButton = form.querySelector('button[type="submit"]');
        const checkStatusButton = document.getElementById('statusButton');
        // Add this at the beginning of your script
        const ongoingRequests = new Set();
        let isRequestOngoing = false;

        // Helper function to generate a unique key for each request
        function getRequestKey(username, orderId) {
            return `${username}:${orderId}`;
        }


        function showModal() {
            modal.classList.remove('hidden');
            modal.classList.add('modal-enter');
            setTimeout(() => {
                modal.classList.remove('modal-enter');
                modal.classList.add('modal-enter-active');
            }, 10);
        }

        // 在 hideModal() 中重置
        function hideModal() {
            modal.classList.add('modal-exit');
            setTimeout(() => {
                modal.classList.add('modal-exit-active');
                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.classList.remove('modal-exit', 'modal-exit-active');
                }, 300);
            }, 10);
        }

        function handleModalClick(event) {
            // 检查点击是否发生在 modal 内容之外
            if (event.target === modal) {
                hideModal();
            }
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const orderId = document.getElementById('orderId').value.trim();

            // 生成请求的唯一键
            const requestKey = getRequestKey(username, orderId);

            // 添加到进行中请求的 Set
            ongoingRequests.add(requestKey);
            submitButton.disabled = true;
            isRequestOngoing = true;
            checkStatusButton.disabled = false;

            showModal();
            loadingIndicator.classList.remove('hidden');
            successIndicator.classList.add('hidden');
            errorIndicator.classList.add('hidden');
            modalTitle.textContent = 'Loading';
            modalMessage.textContent = 'Verifying order and fetching credentials...';

            try {
                // 首先获取 Steam 凭据
                const credentialsResponse = await axios.post('/api/get_steam_credentials', {
                    order_id: orderId,
                    username: username
                });

                const steamUsername = credentialsResponse.data.username;
                const steamPassword = credentialsResponse.data.password;

                document.getElementById('steamUsername').textContent = steamUsername;
                document.getElementById('steamPassword').textContent = steamPassword;
                document.getElementById('credentialsContainer').classList.remove('hidden');

                modalMessage.innerHTML = 'Please login now. System is getting the code.';

                // 然后获取 Steam 验证码
                const authCodeResponse = await axios.post('/api/get_steam_auth_code', {
                    order_id: orderId,
                    username: username
                });

                loadingIndicator.classList.add('hidden');
                successIndicator.classList.remove('hidden');
                modalTitle.textContent = 'Success';
                modalMessage.innerHTML += `<br><br>Your auth code is: ${authCodeResponse.data.auth_code}`;
            } catch (error) {
                loadingIndicator.classList.add('hidden');
                errorIndicator.classList.remove('hidden');
                modalTitle.textContent = 'Error';
                modalMessage.innerHTML = error.response?.data?.error || 'An error occurred';
            } finally {
                // 从进行中请求的 Set 中移除
                ongoingRequests.delete(requestKey);
                isRequestOngoing = false;
                submitButton.disabled = false;
                checkStatusButton.disabled = true;
            }
        });

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                // 可以添加一个临时的提示，表示复制成功
                element.classList.add('text-green-600');
                setTimeout(() => {
                    element.classList.remove('text-green-600');
                }, 1000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        window.addEventListener('load', function () {
            // Check for order_sn from URL parameters (priority)
            var orderSn = getUrlParameter('order_sn');
            // Fallback to orderId for backward compatibility
            var orderId = getUrlParameter('orderId');

            // Check for server-side passed order_sn
            var serverOrderSn = '{{ order_sn }}';

            // Use order_sn first, then orderId, then server-side value
            var finalOrderId = orderSn || orderId || serverOrderSn;

            if (finalOrderId) {
                document.getElementById('orderId').value = finalOrderId;
            }

            // 如果两个参数都存在，可以选择自动提交表单
            if (finalOrderId) {
                // 取消下面的注释如果你想自动提交表单
                // document.getElementById('authForm').submit();
            }
        });

        const tour = new Shepherd.Tour({
            defaultStepOptions: {
                useModalOverlay: true,
                cancelIcon: {
                    enabled: true
                },
                classes: 'shadow-md bg-purple-50',
                scrollTo: { behavior: 'smooth', block: 'center' },
                highlightClass: 'highlight-element',
                modalOverlayOpeningPadding: 10,
                modalOverlayOpeningRadius: 4
            }
        });

        tour.addStep({
            id: 'order-id',
            text: `
            <div class="mb-4">
                <img src="https://cf.shopee.com.my/file/sg-11134209-7rdx0-m0qynirez5ve7a" alt="Shopee Order ID" class="w-full rounded-lg shadow-md">
            </div>
            <p>Enter your Shopee order ID here. You can find this in your Shopee order details.</p>
        `,
            attachTo: {
                element: '#orderId',
                on: 'bottom'
            },
            buttons: [
                {
                    text: 'Done',
                    action: tour.complete
                }
            ]
        });

        document.getElementById('helpButton').addEventListener('click', () => {
            tour.start();
        });

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const orderSn = document.getElementById('orderId').value.trim();

            showModal();
            loadingIndicator.classList.remove('hidden');
            successIndicator.classList.add('hidden');
            errorIndicator.classList.add('hidden');
            modalTitle.textContent = 'Processing';
            modalMessage.textContent = 'Processing your order...';

            try {
                const response = await axios.post('/api/process_order', {
                    order_sn: orderSn
                });

                loadingIndicator.classList.add('hidden');

                let hasError = false;
                let resultsHtml = '';

                if (response.data.status === "Already Processed") {
                    successIndicator.classList.remove('hidden');
                    modalTitle.textContent = 'Order Already Processed';
                    resultsHtml = `
                <div class="mt-4 p-4 bg-yellow-100 rounded-lg text-left">
                    <p class="font-semibold mb-2">${response.data.message}</p>
                    ${response.data.results ? response.data.results.map(result => `
                        <div class="mt-3 p-3 bg-white rounded-md shadow-sm">
                            ${result.result}
                            ${result.note ? `<p class="text-yellow-600">${result.note}</p>` : ''}
                        </div>
                    `).join('') : ''}
                </div>
            `;
                } else {
                    if (response.data.results && response.data.results.length > 0 && response.data.results[0].is_manual_order) {
                        resultsHtml = `
                    <div class="mt-4 p-4 bg-yellow-100 rounded-lg text-left">
                        <p class="font-semibold mb-2">Manual Order Processed</p>
                        ${response.data.results[0].result.replace(/\n/g, '<br>')}
                    </div>
                `;
                    } else {
                        resultsHtml = `
                    <div class="mt-4 p-4 bg-gray-100 rounded-lg text-left">
                        <p class="font-semibold mb-2">Order Status: ${response.data.status}</p>
                        ${response.data.results.map(result => {
                            if (result.error) {
                                hasError = true;
                                return `
                                    <div class="mt-3 p-3 bg-red-50 rounded-md shadow-sm">
                                        <p class="text-red-600">${result.error}</p>
                                    </div>
                                `;
                            } else if (result.result) {
                                return `
                                    <div class="mt-3 p-3 bg-white rounded-md shadow-sm">
                                        ${result.result}
                                    </div>
                                `;
                            } else if (result.message) {
                                return `
                                    <div class="mt-3 p-3 bg-green-50 rounded-md shadow-sm">
                                        <p class="text-green-600">${result.message}</p>
                                    </div>
                                `;
                            }
                            return '';
                        }).join('')}
                    </div>
                `;
                    }
                }

                if (hasError) {
                    errorIndicator.classList.remove('hidden');
                    modalTitle.textContent = 'Error';
                } else {
                    successIndicator.classList.remove('hidden');
                    modalTitle.textContent = 'Success';
                }

                modalMessage.innerHTML = resultsHtml;
            } catch (error) {
                loadingIndicator.classList.add('hidden');
                errorIndicator.classList.remove('hidden');
                modalTitle.textContent = 'Error';
                modalMessage.innerHTML = error.response?.data?.error || 'An error occurred';
            }
        });
    </script>
</body>

</html>