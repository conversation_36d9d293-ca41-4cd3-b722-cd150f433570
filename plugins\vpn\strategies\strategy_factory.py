"""
VPN Strategy Factory
Factory class for creating appropriate VPN redemption strategies based on product SKU
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

import logging
import json
import os
from typing import Dict, Any, Optional, List, TYPE_CHECKING
import importlib

if TYPE_CHECKING:
    from .vpn_base_strategy import VPNBaseStrategy

logger = logging.getLogger(__name__)

class VPNStrategyFactory:
    """
    Factory class for creating VPN redemption strategies based on product SKU.
    """

    # Strategy mapping based on product SKU patterns (using string names to avoid circular imports)
    STRATEGY_MAPPING = {
        # Singapore strategies
        'sg_': ('vpn_sg_strategy', 'VPNSingaporeStrategy'),
        'sg_highspeed': ('vpn_sg_strategy', 'VPNSingaporeHighSpeedStrategy'),
        'sg_premium': ('vpn_sg_strategy', 'VPNSingaporePremiumStrategy'),
        'sg_business': ('vpn_sg_strategy', 'VPNSingaporeBusinessStrategy'),

        # Malaysia basic strategies
        'my_': ('vpn_my_basic_strategy', 'VPNMalaysiaBasicStrategy'),
        'my_standard': ('vpn_my_basic_strategy', 'VPNMalaysiaStandardStrategy'),
        'my_premium': ('vpn_my_basic_strategy', 'VPNMalaysiaPremiumStrategy'),

        # Malaysia high-speed strategies
        'my_highspeed': ('vpn_my_highspeed_strategy', 'VPNMalaysiaHighSpeedStrategy'),
        'my_highspeed_premium': ('vpn_my_highspeed_strategy', 'VPNMalaysiaHighSpeedPremiumStrategy'),
    }

    # SKU to Server Tags mapping - loaded from configuration file
    _sku_tags_config = None
    _config_file_path = None

    @classmethod
    def _get_config_file_path(cls) -> str:
        """Get the path to the SKU tags configuration file"""
        if cls._config_file_path is None:
            # Get the project root directory (3 levels up from this file)
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
            cls._config_file_path = os.path.join(project_root, 'configs', 'services', 'vpn_sku_tags.json')
        return cls._config_file_path

    @classmethod
    def _load_sku_tags_config(cls) -> Dict[str, Any]:
        """Load SKU tags configuration from file"""
        if cls._sku_tags_config is None:
            try:
                config_path = cls._get_config_file_path()
                logger.info(f"Loading VPN SKU tags configuration from: {config_path}")

                if not os.path.exists(config_path):
                    logger.error(f"VPN SKU tags configuration file not found: {config_path}")
                    return cls._get_default_config()

                with open(config_path, 'r', encoding='utf-8') as f:
                    cls._sku_tags_config = json.load(f)
                    logger.info("Successfully loaded VPN SKU tags configuration")

            except Exception as e:
                logger.error(f"Error loading VPN SKU tags configuration: {str(e)}")
                cls._sku_tags_config = cls._get_default_config()

        return cls._sku_tags_config

    @classmethod
    def _get_default_config(cls) -> Dict[str, Any]:
        """Get default configuration if file loading fails"""
        return {
            "sku_server_tags_mapping": {
                "malaysia_basic": {
                    "my_": ["malaysia", "shinjiru", "basic"]
                },
                "singapore_basic": {
                    "sg_": ["singapore", "digitalocean", "basic"]
                }
            },
            "fallback_mapping": {
                "sg_": ["singapore", "digitalocean"],
                "my_": ["malaysia", "shinjiru"],
                "default": ["malaysia", "shinjiru"]
            }
        }

    @classmethod
    def _get_flattened_sku_mapping(cls) -> Dict[str, List[str]]:
        """Get flattened SKU to tags mapping from configuration"""
        config = cls._load_sku_tags_config()
        flattened = {}

        # Flatten the nested structure
        sku_mapping = config.get('sku_server_tags_mapping', {})
        for category, skus in sku_mapping.items():
            for sku, sku_config in skus.items():
                # Support both old format (list) and new format (dict with tags and validity)
                if isinstance(sku_config, list):
                    # Old format: just tags
                    flattened[sku] = sku_config
                elif isinstance(sku_config, dict):
                    # New format: dict with tags and validity_days
                    flattened[sku] = sku_config.get('tags', [])
                else:
                    logger.warning(f"Invalid SKU config format for {sku}: {sku_config}")

        return flattened

    @classmethod
    def _get_flattened_sku_validity_mapping(cls) -> Dict[str, int]:
        """Get flattened SKU to validity days mapping from configuration"""
        config = cls._load_sku_tags_config()
        flattened = {}

        # Flatten the nested structure
        sku_mapping = config.get('sku_server_tags_mapping', {})
        for category, skus in sku_mapping.items():
            for sku, sku_config in skus.items():
                if isinstance(sku_config, dict):
                    # New format: dict with tags and validity_days
                    validity_days = sku_config.get('validity_days')
                    if validity_days is not None:
                        flattened[sku] = validity_days

        return flattened

    @classmethod
    def _import_strategy_class(cls, module_name: str, class_name: str):
        """Dynamically import a strategy class"""
        try:
            module = __import__(f'plugins.vpn.strategies.{module_name}', fromlist=[class_name])
            return getattr(module, class_name)
        except ImportError as e:
            logger.error(f"Failed to import {class_name} from {module_name}: {e}")
            return None
    
    @classmethod
    def create_strategy(cls, product_sku: str) -> Optional['VPNBaseStrategy']:
        """
        Create appropriate VPN strategy based on product SKU.

        Args:
            product_sku (str): Product SKU to determine strategy

        Returns:
            VPNBaseStrategy: Appropriate strategy instance or None if no match
        """
        try:
            if not product_sku:
                logger.error("Product SKU is required to create strategy")
                return None

            product_sku_lower = product_sku.lower()
            logger.info(f"Creating VPN strategy for SKU: {product_sku}")

            # Check for exact matches first (more specific patterns)
            for pattern, (module_name, class_name) in sorted(cls.STRATEGY_MAPPING.items(), key=lambda x: len(x[0]), reverse=True):
                if pattern in product_sku_lower:
                    logger.info(f"Selected strategy: {class_name} for pattern: {pattern}")
                    strategy_class = cls._import_strategy_class(module_name, class_name)
                    if strategy_class:
                        try:
                            return strategy_class()
                        except RuntimeError as e:
                            if "VPN API service not initialized" in str(e):
                                logger.warning(f"VPN API service not initialized, but strategy class identified: {class_name}")
                                return None
                            else:
                                raise
                    else:
                        logger.error(f"Failed to import strategy class: {class_name}")
                        return None

            # Default fallback strategies based on region prefix
            if product_sku_lower.startswith('sg_'):
                logger.info(f"Using default Singapore strategy for SKU: {product_sku}")
                strategy_class = cls._import_strategy_class('vpn_sg_strategy', 'VPNSingaporeStrategy')
                if strategy_class:
                    try:
                        return strategy_class()
                    except RuntimeError as e:
                        if "VPN API service not initialized" in str(e):
                            logger.warning("VPN API service not initialized")
                            return None
                        else:
                            raise
            elif product_sku_lower.startswith('my_'):
                logger.info(f"Using default Malaysia basic strategy for SKU: {product_sku}")
                strategy_class = cls._import_strategy_class('vpn_my_basic_strategy', 'VPNMalaysiaBasicStrategy')
                if strategy_class:
                    try:
                        return strategy_class()
                    except RuntimeError as e:
                        if "VPN API service not initialized" in str(e):
                            logger.warning("VPN API service not initialized")
                            return None
                        else:
                            raise

            logger.warning(f"No strategy found for SKU: {product_sku}. Using default Malaysia basic strategy.")
            strategy_class = cls._import_strategy_class('vpn_my_basic_strategy', 'VPNMalaysiaBasicStrategy')
            if strategy_class:
                try:
                    return strategy_class()
                except RuntimeError as e:
                    if "VPN API service not initialized" in str(e):
                        logger.warning("VPN API service not initialized")
                        return None
                    else:
                        raise

            return None

        except Exception as e:
            logger.error(f"Error creating strategy for SKU {product_sku}: {str(e)}")
            return None
    
    @classmethod
    def get_supported_skus(cls) -> Dict[str, str]:
        """
        Get list of supported SKU patterns and their corresponding strategies.

        Returns:
            Dict[str, str]: Mapping of SKU patterns to strategy names
        """
        return {
            pattern: class_name
            for pattern, (_, class_name) in cls.STRATEGY_MAPPING.items()
        }

    @classmethod
    def get_server_tags_for_sku(cls, product_sku: str) -> List[str]:
        """
        Get server tags that should be used for a specific product SKU.

        Args:
            product_sku (str): Product SKU to get tags for

        Returns:
            List[str]: List of server tags to filter by
        """
        if not product_sku:
            return []

        product_sku_lower = product_sku.lower()

        # Get flattened SKU mapping from configuration
        sku_mapping = cls._get_flattened_sku_mapping()

        # Check for exact matches first (more specific patterns)
        for pattern, tags in sorted(sku_mapping.items(), key=lambda x: len(x[0]), reverse=True):
            if pattern in product_sku_lower:
                logger.info(f"Found server tags for SKU {product_sku}: {tags} (pattern: {pattern})")
                return tags

        # Use fallback mapping from configuration
        config = cls._load_sku_tags_config()
        fallback_mapping = config.get('fallback_mapping', {})

        # Check fallback patterns
        if product_sku_lower.startswith('sg_'):
            fallback_tags = fallback_mapping.get('sg_', ['singapore', 'digitalocean'])
            logger.info(f"Using fallback tags for SG SKU {product_sku}: {fallback_tags}")
            return fallback_tags
        elif product_sku_lower.startswith('my_'):
            fallback_tags = fallback_mapping.get('my_', ['malaysia', 'shinjiru'])
            logger.info(f"Using fallback tags for MY SKU {product_sku}: {fallback_tags}")
            return fallback_tags

        # Ultimate fallback
        default_tags = fallback_mapping.get('default', ['malaysia', 'shinjiru'])
        logger.warning(f"No specific server tags found for SKU {product_sku}, using default: {default_tags}")
        return default_tags

    @classmethod
    def get_validity_days_for_sku(cls, product_sku: str) -> int:
        """
        Get validity days for a specific product SKU.

        Args:
            product_sku (str): Product SKU to get validity for

        Returns:
            int: Number of validity days
        """
        if not product_sku:
            return 30  # Default

        product_sku_lower = product_sku.lower()

        # Get validity mapping from configuration
        validity_mapping = cls._get_flattened_sku_validity_mapping()

        # Check for exact matches first (more specific patterns)
        for pattern, validity_days in sorted(validity_mapping.items(), key=lambda x: len(x[0]), reverse=True):
            if pattern in product_sku_lower:
                logger.info(f"Found validity for SKU {product_sku}: {validity_days} days (pattern: {pattern})")
                return validity_days

        # Fallback: extract from SKU name using regex
        import re
        match = re.search(r'_(\d+)$', product_sku_lower)
        if match:
            extracted_days = int(match.group(1))
            logger.info(f"Extracted validity from SKU name {product_sku}: {extracted_days} days")
            return extracted_days

        # Ultimate fallback
        logger.warning(f"No validity found for SKU {product_sku}, using default: 30 days")
        return 30

    @classmethod
    def reload_sku_tags_config(cls) -> bool:
        """
        Reload SKU tags configuration from file.

        Returns:
            bool: True if reload was successful, False otherwise
        """
        try:
            cls._sku_tags_config = None  # Clear cached config
            cls._load_sku_tags_config()  # Reload from file
            logger.info("Successfully reloaded VPN SKU tags configuration")
            return True
        except Exception as e:
            logger.error(f"Failed to reload VPN SKU tags configuration: {str(e)}")
            return False

    @classmethod
    def get_config_info(cls) -> Dict[str, Any]:
        """
        Get information about the current configuration.

        Returns:
            Dict[str, Any]: Configuration information
        """
        config = cls._load_sku_tags_config()
        flattened = cls._get_flattened_sku_mapping()

        return {
            "config_file_path": cls._get_config_file_path(),
            "config_loaded": cls._sku_tags_config is not None,
            "total_sku_mappings": len(flattened),
            "categories": list(config.get('sku_server_tags_mapping', {}).keys()),
            "fallback_patterns": list(config.get('fallback_mapping', {}).keys()),
            "version": config.get('version', 'unknown'),
            "last_updated": config.get('last_updated', 'unknown')
        }
    
    @classmethod
    def is_vpn_product(cls, product_sku: str) -> bool:
        """
        Check if a product SKU is a VPN product.
        
        Args:
            product_sku (str): Product SKU to check
            
        Returns:
            bool: True if it's a VPN product, False otherwise
        """
        if not product_sku:
            return False
            
        product_sku_lower = product_sku.lower()
        
        # Check for VPN-specific patterns
        vpn_patterns = ['sg_', 'my_', 'vpn_', 'vpn-']
        
        return any(product_sku_lower.startswith(pattern) for pattern in vpn_patterns)
    
    @classmethod
    def get_strategy_info(cls, product_sku: str) -> Dict[str, Any]:
        """
        Get information about the strategy that would be used for a given SKU.

        Args:
            product_sku (str): Product SKU

        Returns:
            Dict[str, Any]: Strategy information including name, type, and features
        """
        try:
            # First, determine strategy class without instantiating
            if not product_sku:
                return {
                    "strategy_name": "Unknown",
                    "strategy_type": "unknown",
                    "supported": False,
                    "error": "Product SKU is required"
                }

            product_sku_lower = product_sku.lower()

            # Check for exact matches first (more specific patterns)
            strategy_name = None
            for pattern, (_, class_name) in sorted(cls.STRATEGY_MAPPING.items(), key=lambda x: len(x[0]), reverse=True):
                if pattern in product_sku_lower:
                    strategy_name = class_name
                    break

            # Default fallback strategies based on region prefix
            if not strategy_name:
                if product_sku_lower.startswith('sg_'):
                    strategy_name = 'VPNSingaporeStrategy'
                elif product_sku_lower.startswith('my_'):
                    strategy_name = 'VPNMalaysiaBasicStrategy'
                else:
                    strategy_name = 'VPNMalaysiaBasicStrategy'

            if not strategy_name:
                return {
                    "strategy_name": "Unknown",
                    "strategy_type": "unknown",
                    "supported": False,
                    "error": "No strategy found for this SKU"
                }
            
            # Determine strategy type and features
            strategy_info = {
                "strategy_name": strategy_name,
                "supported": True,
                "is_vpn": cls.is_vpn_product(product_sku)
            }
            
            # Add type-specific information
            if "Singapore" in strategy_name:
                strategy_info.update({
                    "strategy_type": "singapore",
                    "region": "Singapore",
                    "server_type": "digital_ocean",
                    "creation_type": "single_server"
                })
            elif "HighSpeed" in strategy_name:
                strategy_info.update({
                    "strategy_type": "malaysia_highspeed",
                    "region": "Malaysia",
                    "server_type": "all_servers",
                    "creation_type": "multi_server",
                    "features": ["shared_uuid", "server_selection", "high_redundancy"]
                })
            elif "Malaysia" in strategy_name:
                strategy_info.update({
                    "strategy_type": "malaysia_basic",
                    "region": "Malaysia", 
                    "server_type": "shinjiru",
                    "creation_type": "single_server"
                })
            
            # Add tier information
            if "Premium" in strategy_name:
                strategy_info["tier"] = "premium"
            elif "Business" in strategy_name:
                strategy_info["tier"] = "business"
            elif "Standard" in strategy_name:
                strategy_info["tier"] = "standard"
            else:
                strategy_info["tier"] = "basic"
            
            return strategy_info
            
        except Exception as e:
            logger.error(f"Error getting strategy info for SKU {product_sku}: {str(e)}")
            return {
                "strategy_name": "Error",
                "strategy_type": "error",
                "supported": False,
                "error": str(e)
            }


# Convenience function for external use
def create_vpn_strategy(product_sku: str) -> Optional['VPNBaseStrategy']:
    """
    Convenience function to create a VPN strategy.

    Args:
        product_sku (str): Product SKU

    Returns:
        VPNBaseStrategy: Strategy instance or None
    """
    return VPNStrategyFactory.create_strategy(product_sku)


def get_vpn_strategy_info(product_sku: str) -> Dict[str, Any]:
    """
    Convenience function to get VPN strategy information.
    
    Args:
        product_sku (str): Product SKU
        
    Returns:
        Dict[str, Any]: Strategy information
    """
    return VPNStrategyFactory.get_strategy_info(product_sku)
