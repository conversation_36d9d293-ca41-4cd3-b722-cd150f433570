"""
Tests for VPN Config Generator models.
"""

import unittest
import sys
import os
from datetime import datetime

# Add plugin directory to path
plugin_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, plugin_dir)

from models import (
    VPNConfigRequest, VPNConfigResponse, VPNAPIConfig, 
    ConfigGeneratorSettings, ConfigTemplate
)


class TestVPNConfigRequest(unittest.TestCase):
    """Test VPNConfigRequest model"""
    
    def test_valid_request_creation(self):
        """Test creating a valid VPN config request"""
        request = VPNConfigRequest(
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited",
            username="testuser"
        )
        
        self.assertEqual(request.server, "server11")
        self.assertEqual(request.days, "30")
        self.assertEqual(request.telco, "digi")
        self.assertEqual(request.plan, "unlimited")
        self.assertEqual(request.username, "testuser")
    
    def test_request_to_dict(self):
        """Test converting request to dictionary"""
        request = VPNConfigRequest(
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited",
            username="testuser"
        )
        
        data = request.to_dict()
        expected_keys = ['server', 'days', 'telco', 'plan', 'username']
        
        for key in expected_keys:
            self.assertIn(key, data)
        
        self.assertEqual(data['server'], "server11")
        self.assertEqual(data['username'], "testuser")
    
    def test_request_from_dict(self):
        """Test creating request from dictionary"""
        data = {
            'server': 'server12',
            'days': '7',
            'telco': 'maxis',
            'plan': 'basic',
            'username': 'testuser2'
        }
        
        request = VPNConfigRequest.from_dict(data)
        
        self.assertEqual(request.server, "server12")
        self.assertEqual(request.days, "7")
        self.assertEqual(request.telco, "maxis")
        self.assertEqual(request.plan, "basic")
        self.assertEqual(request.username, "testuser2")


class TestVPNConfigResponse(unittest.TestCase):
    """Test VPNConfigResponse model"""
    
    def test_successful_response(self):
        """Test creating a successful response"""
        response = VPNConfigResponse(
            success=True,
            config="vmess://config-data",
            created_date="2024-01-01",
            expired_date="2024-01-31",
            message="Config generated successfully"
        )
        
        self.assertTrue(response.success)
        self.assertEqual(response.config, "vmess://config-data")
        self.assertEqual(response.created_date, "2024-01-01")
        self.assertEqual(response.expired_date, "2024-01-31")
        self.assertEqual(response.message, "Config generated successfully")
        self.assertIsNone(response.error)
    
    def test_error_response(self):
        """Test creating an error response"""
        response = VPNConfigResponse(
            success=False,
            error="API connection failed"
        )
        
        self.assertFalse(response.success)
        self.assertEqual(response.error, "API connection failed")
        self.assertIsNone(response.config)
        self.assertIsNone(response.created_date)
        self.assertIsNone(response.expired_date)
    
    def test_response_to_dict(self):
        """Test converting response to dictionary"""
        response = VPNConfigResponse(
            success=True,
            config="vmess://config-data",
            created_date="2024-01-01",
            expired_date="2024-01-31"
        )
        
        data = response.to_dict()
        
        self.assertIn('success', data)
        self.assertIn('config', data)
        self.assertIn('created_date', data)
        self.assertIn('expired_date', data)
        self.assertTrue(data['success'])


class TestVPNAPIConfig(unittest.TestCase):
    """Test VPNAPIConfig model"""
    
    def test_default_config(self):
        """Test default API configuration"""
        config = VPNAPIConfig()
        
        self.assertTrue(config.enabled)
        self.assertFalse(config.use_vpn_plugin_api)
        self.assertEqual(config.timeout, 30)
    
    def test_config_from_dict(self):
        """Test creating config from dictionary"""
        data = {
            'enabled': True,
            'use_vpn_plugin_api': True,
            'fallback_api_endpoint': 'https://api.example.com',
            'fallback_username': 'admin',
            'fallback_password': 'password123',
            'timeout': 60
        }
        
        config = VPNAPIConfig.from_dict(data)
        
        self.assertTrue(config.enabled)
        self.assertTrue(config.use_vpn_plugin_api)
        self.assertEqual(config.fallback_api_endpoint, 'https://api.example.com')
        self.assertEqual(config.fallback_username, 'admin')
        self.assertEqual(config.fallback_password, 'password123')
        self.assertEqual(config.timeout, 60)


class TestConfigTemplate(unittest.TestCase):
    """Test ConfigTemplate model"""
    
    def test_template_creation(self):
        """Test creating a config template"""
        template = ConfigTemplate(
            id="template1",
            name="Basic Template",
            description="Basic VPN configuration",
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited"
        )
        
        self.assertEqual(template.id, "template1")
        self.assertEqual(template.name, "Basic Template")
        self.assertEqual(template.description, "Basic VPN configuration")
        self.assertEqual(template.server, "server11")
        self.assertEqual(template.days, "30")
        self.assertEqual(template.telco, "digi")
        self.assertEqual(template.plan, "unlimited")
        self.assertTrue(template.enabled)
    
    def test_template_to_dict(self):
        """Test converting template to dictionary"""
        template = ConfigTemplate(
            id="template1",
            name="Basic Template",
            description="Basic VPN configuration",
            server="server11",
            days="30",
            telco="digi",
            plan="unlimited"
        )
        
        data = template.to_dict()
        expected_keys = ['id', 'name', 'description', 'server', 'days', 'telco', 'plan', 'enabled', 'created_at', 'updated_at']
        
        for key in expected_keys:
            self.assertIn(key, data)
    
    def test_template_from_dict(self):
        """Test creating template from dictionary"""
        data = {
            'id': 'template2',
            'name': 'Advanced Template',
            'description': 'Advanced VPN configuration',
            'server': 'server12',
            'days': '7',
            'telco': 'maxis',
            'plan': 'basic',
            'enabled': False
        }
        
        template = ConfigTemplate.from_dict(data)
        
        self.assertEqual(template.id, "template2")
        self.assertEqual(template.name, "Advanced Template")
        self.assertEqual(template.server, "server12")
        self.assertFalse(template.enabled)


if __name__ == '__main__':
    unittest.main()
