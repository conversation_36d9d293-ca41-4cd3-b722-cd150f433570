# Credential Expiration Email Notification

This feature automatically sends email notifications when your Shopee API credentials (specifically the SPC_STK cookie) expire.

## Configuration

Add the following email configuration to your `config.json` file:

```json
{
  "EMAIL_SENDER": {
    "FROM_EMAIL": "<EMAIL>",
    "FROM_PASSWORD": "your_app_password_here",
    "DEFAULT_TO_EMAIL": "<EMAIL>"
  }
}
```

### Setting up Gmail App Password

1. Enable 2-factor authentication on your Gmail account
2. Go to Google Account settings > Security > App passwords
3. Generate a new app password for "Mail"
4. Use this app password (not your regular password) in the `FROM_PASSWORD` field

## How It Works

1. **Automatic Detection**: The system checks cookie expiration every time credentials are validated
2. **Email Notification**: When SPC_STK cookie is expired, an email is automatically sent to the configured address
3. **Rate Limiting**: Emails are limited to once every 60 minutes to prevent spam
4. **Detailed Information**: The email includes:
   - Exact expiration time
   - Current time
   - Instructions to update credentials

## Email Content Example

```
Subject: ⚠️ Shopee API Cookie Expiration Warning

Shopee API Cookie Expiration Warning

📅 Timestamp: 2025-07-13 21:54:18

Reason: SPC_STK cookie has expired

Details:
Cookie expired at: 2025-07-13 20:54:18
Current time: 2025-07-13 21:54:18
Please update your credentials in the config.json file.

Please update your Shopee authorization code and cookie in the config.json file as soon as possible.

---
This is an automated notification from the Shopee API system.
```

## Testing

Run the test script to verify your email configuration:

```bash
cd ShopeeAPI
python3 test_credential_expiration_email.py
```

## Troubleshooting

### Email Not Sending
- Verify all email configuration fields are correct
- Check that you're using an app password (not regular password) for Gmail
- Ensure the sender email has SMTP access enabled

### Too Many Emails
- The system has a built-in 60-minute cooldown between emails
- Check logs for credential validation frequency

### No Email Received
- Check spam/junk folder
- Verify the recipient email address is correct
- Ensure credentials are actually expired (check expiration timestamps)

## Integration with Existing Code

The email notification is automatically integrated when you:

1. Initialize `ShopeeAPI` client (it will load email config from `config.json`)
2. Call `validate_credentials()` or `check_credentials()` methods
3. Use any API functionality that validates credentials

No additional code changes are required in your application.