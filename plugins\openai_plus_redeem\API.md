# OpenAI Plus Redeem Plugin API Documentation

This document describes the API endpoints provided by the OpenAI Plus Redeem Plugin.

## Base URL

All API endpoints are prefixed with the following base URLs:
- **Customer Endpoints**: `/openai-plus-redeem`
- **Admin Endpoints**: `/admin/openai-plus-redeem`

## Authentication

### Customer Endpoints
Customer endpoints use IP-based rate limiting and do not require authentication tokens.

### Admin Endpoints
Admin endpoints require authentication through the main application's authentication system. Include the authentication token in the request headers:

```http
Authorization: Bearer <admin_token>
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:
- **Customer redemption endpoints**: 10-15 requests per hour per IP
- **Customer verification endpoints**: 20 requests per hour per IP
- **Admin endpoints**: 50-100 requests per hour per IP

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 9
X-RateLimit-Reset: 1640995200
```

## Response Format

All API responses follow a consistent JSON format:

### Success Response
```json
{
  "status": "success",
  "data": {
    // Response data
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response
```json
{
  "status": "error",
  "error": "Error description",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Customer Endpoints

### 1. Main Redemption Page

**GET** `/openai-plus-redeem/`

Renders the main redemption page with optional Shopee integration parameters.

**Query Parameters:**
- `order_id` (optional): Order ID for auto-population
- `source` (optional): Source system (e.g., "shopee")
- `username` (optional): Buyer username for auto-population
- `order_sn` (optional): Alternative order identifier
- `buyer_id` (optional): Buyer ID

**Response:** HTML page

### 2. Process Order Redemption

**POST** `/openai-plus-redeem/api/redeem`

Process a new order redemption request.

**Rate Limit:** 10 requests per hour per IP

**Request Body:**
```json
{
  "order_id": "ORDER_123456",
  "buyer_username": "customer_username",
  "sku": "chatgpt_plus",
  "var_sku": "chatgpt_5_30"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "redemption_id": "REDEEM_789",
    "status": "processing",
    "estimated_completion": "2024-01-15T11:00:00Z",
    "message": "Redemption request submitted successfully"
  }
}
```

**Error Responses:**
- `400`: Missing required parameters
- `409`: Order already redeemed
- `429`: Rate limit exceeded
- `503`: No available accounts

### 3. Get Redemption Status

**GET** `/openai-plus-redeem/api/status/<redemption_id>`

Check the status of a redemption request.

**Path Parameters:**
- `redemption_id`: Unique redemption identifier

**Response:**
```json
{
  "status": "success",
  "data": {
    "redemption_id": "REDEEM_789",
    "status": "completed",
    "order_id": "ORDER_123456",
    "buyer_username": "customer_username",
    "account_details": {
      "email": "<EMAIL>",
      "password": "masked_password",
      "expiration_date": "2024-12-31T23:59:59Z"
    },
    "created_date": "2024-01-15T10:30:00Z",
    "completed_date": "2024-01-15T10:45:00Z"
  }
}
```

**Status Values:**
- `pending`: Waiting to be processed
- `processing`: Currently being processed
- `completed`: Successfully completed
- `failed`: Processing failed
- `expired`: Redemption expired

### 4. Get User Summary

**GET** `/openai-plus-redeem/api/user/<username>/summary`

Get redemption summary for a specific user.

**Path Parameters:**
- `username`: Buyer username

**Response:**
```json
{
  "status": "success",
  "data": {
    "username": "customer_username",
    "total_redemptions": 3,
    "successful_redemptions": 2,
    "failed_redemptions": 1,
    "last_redemption_date": "2024-01-15T10:30:00Z",
    "cooldown_status": {
      "active": false,
      "expires_at": null
    }
  }
}
```

### 5. Get Verification Status

**GET** `/openai-plus-redeem/api/verification/<verification_id>/status`

Check email verification status.

**Path Parameters:**
- `verification_id`: Verification identifier

**Response:**
```json
{
  "status": "success",
  "data": {
    "verification_id": "VERIFY_123",
    "status": "completed",
    "verification_code": "123456",
    "account_email": "<EMAIL>",
    "created_date": "2024-01-15T10:30:00Z",
    "completed_date": "2024-01-15T10:32:00Z"
  }
}
```

### 6. Search Verification Code

**POST** `/openai-plus-redeem/api/verification/search`

Search for verification code in email.

**Rate Limit:** 20 requests per hour per IP

**Request Body:**
```json
{
  "account_email": "<EMAIL>",
  "redemption_id": "REDEEM_789",
  "search_keywords": ["openai", "verification"]
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "verification_id": "VERIFY_123",
    "code_found": true,
    "verification_code": "123456",
    "email_subject": "OpenAI Verification Code",
    "found_at": "2024-01-15T10:32:00Z"
  }
}
```

### 7. Get Cooldown Status

**GET** `/openai-plus-redeem/api/cooldown/<username>/status`

Check user cooldown status.

**Path Parameters:**
- `username`: Buyer username

**Response:**
```json
{
  "status": "success",
  "data": {
    "username": "customer_username",
    "cooldown_active": true,
    "cooldown_expires_at": "2024-01-16T10:30:00Z",
    "remaining_hours": 18.5,
    "can_redeem": false
  }
}
```

### 8. Test Account Access

**POST** `/openai-plus-redeem/api/account/test`

Test ChatGPT account access (mock implementation).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "account_password"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "email": "<EMAIL>",
    "access_valid": true,
    "test_timestamp": "2024-01-15T10:30:00Z",
    "message": "Account access test completed"
  }
}
```

### 9. Shopee Integration Endpoint

**POST** `/openai-plus-redeem/api/shopee/redeem`

Direct redemption endpoint for Shopee integration.

**Rate Limit:** 15 requests per hour per IP

**Request Body (Direct Format):**
```json
{
  "order_id": "ORDER_123456",
  "buyer_username": "customer_username",
  "sku": "chatgpt_plus",
  "var_sku": "chatgpt_5_30"
}
```

**Request Body (Shopee Webhook Format):**
```json
{
  "order_data": {
    "order_sn": "ORDER_123456",
    "buyer_username": "customer_username",
    "items": [
      {
        "item_sku": "chatgpt_plus",
        "variation_sku": "chatgpt_5_30"
      }
    ]
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "redemption_id": "REDEEM_789",
    "integration_source": "shopee",
    "processing_status": "initiated"
  }
}
```

## Admin Endpoints

All admin endpoints require authentication and are prefixed with `/admin/openai-plus-redeem`.

### 1. Admin Dashboard

**GET** `/admin/openai-plus-redeem/`

Renders the admin dashboard page.

**Authentication:** Required

**Response:** HTML page

### 2. Get Dashboard Statistics

**GET** `/admin/openai-plus-redeem/api/dashboard/stats`

Get comprehensive dashboard statistics.

**Authentication:** Required

**Response:**
```json
{
  "status": "success",
  "data": {
    "accounts": {
      "total": 25,
      "active": 20,
      "expired": 3,
      "suspended": 2
    },
    "redemptions": {
      "total": 150,
      "completed": 140,
      "failed": 8,
      "pending": 2
    },
    "cooldowns": {
      "active": 5,
      "total_users_affected": 12
    },
    "services": {
      "chatgpt_account": "healthy",
      "order_redemption": "healthy",
      "email": "healthy",
      "cooldown": "healthy"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### 3. Account Management

#### Get All Accounts

**GET** `/admin/openai-plus-redeem/api/accounts`

Retrieve all ChatGPT accounts with optional filtering.

**Authentication:** Required

**Query Parameters:**
- `status` (optional): Filter by account status (active, expired, suspended)
- `include_expired` (optional): Include expired accounts (true/false, default: false)

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "account_id": "CHATGPT_001",
      "email": "<EMAIL>",
      "password": "masked_password",
      "max_concurrent_users": 5,
      "current_users": 2,
      "status": "active",
      "expiration_date": "2024-12-31T23:59:59Z",
      "created_date": "2024-01-01T00:00:00Z",
      "last_used": "2024-01-15T09:30:00Z"
    }
  ],
  "total": 25
}
```

#### Create New Account

**POST** `/admin/openai-plus-redeem/api/accounts`

Create a new ChatGPT account.

**Authentication:** Required
**Rate Limit:** 50 requests per hour per IP

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "secure_password",
  "expiration_date": "2024-12-31T23:59:59Z",
  "max_concurrent_users": 5
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "account_id": "CHATGPT_026",
    "email": "<EMAIL>",
    "status": "active",
    "created_date": "2024-01-15T10:30:00Z"
  }
}
```

#### Update Account

**PUT** `/admin/openai-plus-redeem/api/accounts/<account_id>`

Update an existing ChatGPT account.

**Authentication:** Required

**Path Parameters:**
- `account_id`: Account identifier

**Request Body:**
```json
{
  "password": "new_password",
  "expiration_date": "2025-12-31T23:59:59Z",
  "max_concurrent_users": 10,
  "status": "active"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "account_id": "CHATGPT_001",
    "updated_fields": ["password", "expiration_date", "max_concurrent_users"],
    "updated_date": "2024-01-15T10:30:00Z"
  }
}
```

#### Delete Account

**DELETE** `/admin/openai-plus-redeem/api/accounts/<account_id>`

Delete a ChatGPT account.

**Authentication:** Required

**Path Parameters:**
- `account_id`: Account identifier

**Response:**
```json
{
  "status": "success",
  "data": {
    "account_id": "CHATGPT_001",
    "deleted": true,
    "deleted_date": "2024-01-15T10:30:00Z"
  }
}
```

### 4. Cooldown Management

#### Get Active Cooldowns

**GET** `/admin/openai-plus-redeem/api/cooldowns`

Get all active user cooldowns.

**Authentication:** Required

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "username": "customer1",
      "cooldown_expires_at": "2024-01-16T10:30:00Z",
      "remaining_hours": 18.5,
      "reason": "automatic",
      "created_date": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Set User Cooldown

**POST** `/admin/openai-plus-redeem/api/cooldowns`

Set cooldown for a specific user.

**Authentication:** Required
**Rate Limit:** 100 requests per hour per IP

**Request Body:**
```json
{
  "username": "customer1",
  "hours": 24,
  "reason": "manual_admin_action"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "username": "customer1",
    "cooldown_set": true,
    "expires_at": "2024-01-16T10:30:00Z",
    "hours": 24
  }
}
```

#### Remove User Cooldown

**DELETE** `/admin/openai-plus-redeem/api/cooldowns/<username>`

Remove cooldown for a specific user.

**Authentication:** Required

**Path Parameters:**
- `username`: User to remove cooldown for

**Query Parameters:**
- `admin_override` (optional): Force removal even if not expired (true/false)

**Response:**
```json
{
  "status": "success",
  "data": {
    "username": "customer1",
    "cooldown_removed": true,
    "admin_override": true,
    "removed_date": "2024-01-15T10:30:00Z"
  }
}
```

### 5. Redemption Management

#### Assign Account to Redemption

**POST** `/admin/openai-plus-redeem/api/redemptions/<redemption_id>/assign`

Manually assign a ChatGPT account to a redemption.

**Authentication:** Required

**Path Parameters:**
- `redemption_id`: Redemption identifier

**Request Body:**
```json
{
  "account_id": "CHATGPT_001",
  "force_assignment": false
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "redemption_id": "REDEEM_789",
    "account_id": "CHATGPT_001",
    "assigned": true,
    "assigned_date": "2024-01-15T10:30:00Z"
  }
}
```

#### Release Redemption

**POST** `/admin/openai-plus-redeem/api/redemptions/<redemption_id>/release`

Release a redemption and free the associated account.

**Authentication:** Required

**Path Parameters:**
- `redemption_id`: Redemption identifier

**Request Body:**
```json
{
  "reason": "manual_release",
  "notify_user": true
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "redemption_id": "REDEEM_789",
    "released": true,
    "account_freed": true,
    "release_date": "2024-01-15T10:30:00Z"
  }
}
```

### 6. Service Management

#### Get Services Health

**GET** `/admin/openai-plus-redeem/api/services/health`

Get health status of all plugin services.

**Authentication:** Required

**Response:**
```json
{
  "status": "success",
  "data": {
    "chatgpt_account": {
      "status": "healthy",
      "last_check": "2024-01-15T10:30:00Z",
      "response_time_ms": 45
    },
    "order_redemption": {
      "status": "healthy",
      "last_check": "2024-01-15T10:30:00Z",
      "response_time_ms": 32
    },
    "email": {
      "status": "degraded",
      "last_check": "2024-01-15T10:30:00Z",
      "response_time_ms": 1200,
      "issues": ["IMAP connection slow"]
    },
    "cooldown": {
      "status": "healthy",
      "last_check": "2024-01-15T10:30:00Z",
      "response_time_ms": 15
    }
  }
}
```

#### Restart Service

**POST** `/admin/openai-plus-redeem/api/services/<service_name>/restart`

Restart a specific service.

**Authentication:** Required

**Path Parameters:**
- `service_name`: Name of service to restart

**Response:**
```json
{
  "status": "success",
  "message": "Service chatgpt_account restarted successfully",
  "restart_time": "2024-01-15T10:30:00Z"
}
```

#### Cleanup Expired Data

**POST** `/admin/openai-plus-redeem/api/cleanup/expired`

Cleanup expired data across all services.

**Authentication:** Required

**Response:**
```json
{
  "status": "success",
  "data": {
    "accounts_cleaned": 3,
    "redemptions_cleaned": 15,
    "verifications_cleaned": 8,
    "cooldowns_cleaned": 2,
    "cleanup_date": "2024-01-15T10:30:00Z"
  }
}
```

## Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `INVALID_PARAM` | Invalid parameter value | Check parameter format and requirements |
| `MISSING_PARAM` | Required parameter missing | Include all required parameters |
| `ORDER_ALREADY_REDEEMED` | Order has already been redeemed | Check order status before redemption |
| `NO_AVAILABLE_ACCOUNTS` | No ChatGPT accounts available | Add more accounts or wait for availability |
| `USER_ON_COOLDOWN` | User is currently on cooldown | Wait for cooldown to expire or contact admin |
| `VERIFICATION_FAILED` | Email verification failed | Check email credentials and IMAP settings |
| `SERVICE_UNAVAILABLE` | External service unavailable | Retry later or check service status |
| `CONFIGURATION_ERROR` | Plugin misconfigured | Check plugin configuration |
| `AUTHENTICATION_REQUIRED` | Admin authentication required | Provide valid authentication token |
| `RATE_LIMIT_EXCEEDED` | Rate limit exceeded | Wait before making more requests |
| `ACCOUNT_NOT_FOUND` | ChatGPT account not found | Verify account ID exists |
| `REDEMPTION_NOT_FOUND` | Redemption not found | Verify redemption ID exists |

## Status Codes

- `200`: Success
- `400`: Bad request (validation error)
- `401`: Unauthorized (authentication required)
- `403`: Forbidden (insufficient permissions)
- `404`: Not found (resource doesn't exist)
- `409`: Conflict (resource already exists or in conflicting state)
- `429`: Too many requests (rate limit exceeded)
- `500`: Internal server error
- `503`: Service unavailable

## Examples

### cURL Examples

#### Customer Redemption
```bash
# Process order redemption
curl -X POST http://localhost:5000/openai-plus-redeem/api/redeem \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORDER_123456",
    "buyer_username": "customer1",
    "sku": "chatgpt_plus",
    "var_sku": "chatgpt_5_30"
  }'

# Check redemption status
curl -X GET http://localhost:5000/openai-plus-redeem/api/status/REDEEM_789
```

#### Admin Operations
```bash
# Get all accounts (admin)
curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/accounts \
  -H "Authorization: Bearer admin_token"

# Create new account (admin)
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/accounts \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "secure_password",
    "expiration_date": "2024-12-31T23:59:59Z",
    "max_concurrent_users": 5
  }'

# Reset user cooldown (admin)
curl -X DELETE http://localhost:5000/admin/openai-plus-redeem/api/cooldowns/customer1 \
  -H "Authorization: Bearer admin_token" \
  -G -d "admin_override=true"
```

### Python Examples

#### Customer Operations
```python
import requests

# Process redemption
redemption_data = {
    "order_id": "ORDER_123456",
    "buyer_username": "customer1",
    "sku": "chatgpt_plus",
    "var_sku": "chatgpt_5_30"
}

response = requests.post(
    "http://localhost:5000/openai-plus-redeem/api/redeem",
    json=redemption_data
)
print(response.json())

# Check status
response = requests.get(
    "http://localhost:5000/openai-plus-redeem/api/status/REDEEM_789"
)
status = response.json()
print(f"Redemption status: {status['data']['status']}")
```

#### Admin Operations
```python
import requests

# Admin headers
headers = {"Authorization": "Bearer admin_token"}

# Get dashboard stats
response = requests.get(
    "http://localhost:5000/admin/openai-plus-redeem/api/dashboard/stats",
    headers=headers
)
stats = response.json()
print(f"Total accounts: {stats['data']['accounts']['total']}")

# Create new account
account_data = {
    "email": "<EMAIL>",
    "password": "secure_password",
    "expiration_date": "2024-12-31T23:59:59Z",
    "max_concurrent_users": 5
}

response = requests.post(
    "http://localhost:5000/admin/openai-plus-redeem/api/accounts",
    json=account_data,
    headers=headers
)
print(response.json())
```

### JavaScript Examples

#### Customer Operations
```javascript
// Process redemption
const redemptionData = {
  order_id: "ORDER_123456",
  buyer_username: "customer1",
  sku: "chatgpt_plus",
  var_sku: "chatgpt_5_30"
};

fetch('/openai-plus-redeem/api/redeem', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(redemptionData)
})
.then(response => response.json())
.then(data => console.log(data));

// Check status
fetch('/openai-plus-redeem/api/status/REDEEM_789')
.then(response => response.json())
.then(data => console.log('Status:', data.data.status));
```

#### Admin Operations
```javascript
// Admin operations with authentication
const adminHeaders = {
  'Authorization': 'Bearer admin_token',
  'Content-Type': 'application/json'
};

// Get accounts
fetch('/admin/openai-plus-redeem/api/accounts', {
  headers: adminHeaders
})
.then(response => response.json())
.then(data => console.log('Accounts:', data.data));

// Create account
const accountData = {
  email: "<EMAIL>",
  password: "secure_password",
  expiration_date: "2024-12-31T23:59:59Z",
  max_concurrent_users: 5
};

fetch('/admin/openai-plus-redeem/api/accounts', {
  method: 'POST',
  headers: adminHeaders,
  body: JSON.stringify(accountData)
})
.then(response => response.json())
.then(data => console.log('Created:', data));
```

## Webhooks

The plugin supports webhook integration for external systems like Shopee.

### Shopee Webhook

**POST** `/openai-plus-redeem/api/shopee/redeem`

Accepts Shopee order webhooks for automatic redemption processing.

**Webhook Payload:**
```json
{
  "order_data": {
    "order_sn": "ORDER_123456",
    "buyer_username": "customer1",
    "items": [
      {
        "item_sku": "chatgpt_plus",
        "variation_sku": "chatgpt_5_30"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "signature": "webhook_signature"
}
```

## SDK Support

For easier integration, consider using the plugin's Python SDK:

```python
from plugins.openai_plus_redeem.sdk import OpenAIPlusRedeemClient

# Initialize client
client = OpenAIPlusRedeemClient(
    base_url="http://localhost:5000",
    admin_token="admin_token"  # Optional, for admin operations
)

# Customer operations
redemption = client.process_redemption(
    order_id="ORDER_123456",
    buyer_username="customer1",
    sku="chatgpt_plus",
    var_sku="chatgpt_5_30"
)

# Admin operations
accounts = client.get_accounts()
new_account = client.create_account(
    email="<EMAIL>",
    password="secure_password",
    expiration_date="2024-12-31T23:59:59Z",
    max_concurrent_users=5
)
```
