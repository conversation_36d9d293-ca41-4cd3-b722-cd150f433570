# VPN Configuration Management - Technical Documentation

## System Architecture

### Overview
The VPN Configuration Management System follows the MVC (Model-View-Controller) pattern and integrates seamlessly with the existing SteamCodeTool architecture.

### Component Structure
```
plugins/vpn_config_generator/
├── models.py                 # Data models and business logic
├── services.py              # Business services and data access
├── routes.py                # API endpoints and web routes
├── plugin.py                # Plugin initialization and registration
├── templates/               # HTML templates
│   └── vpn_config_generator/
│       └── order_config.html
├── configs/                 # Configuration files
│   ├── vpn_users.json      # User data storage
│   └── sku_restrictions.json # Access control rules
└── tests/                   # Test files
    └── test_order_integration.py
```

## Data Models

### Core Models

#### VPNUser
**Purpose**: Represents a customer with VPN access
**Key Features**:
- UUID-based identification
- SKU-based restriction detection
- Telco assignment and access control
- Usage tracking and statistics

**Methods**:
- `can_access_telco(telco: str) -> bool`: Check telco access permissions
- `assign_telco(telco: str)`: Assign telco to restricted user

#### VPNOrderRequest/Response
**Purpose**: Handle order verification workflow
**Features**:
- Order status validation
- User creation/retrieval
- Restriction application

#### SKURestriction
**Purpose**: Define access control rules based on SKU patterns
**Features**:
- Wildcard pattern matching
- Configurable restriction types
- Enable/disable functionality

### Data Flow

```
Order Verification → User Creation/Retrieval → Access Control → Configuration Generation
       ↓                      ↓                     ↓                    ↓
   Order Service      VPNOrderService      SKU Restrictions    VPN Config Service
```

## Services Architecture

### VPNOrderService

**Responsibilities**:
- Order processing and validation
- User lifecycle management
- Access control enforcement
- Data persistence

**Key Methods**:
```python
def process_order(request: VPNOrderRequest) -> VPNOrderResponse
def get_user(user_uuid: str) -> Optional[VPNUser]
def can_user_access_telco(user_uuid: str, telco: str) -> Tuple[bool, str]
def update_user_telco_selection(user_uuid: str, telco: str) -> bool
```

**Data Storage**:
- JSON-based persistence for Docker compatibility
- Atomic file operations for data integrity
- Automatic backup and recovery

### Integration Points

#### Order Service Integration
```python
from services.order_service import get_order_status, get_order_details, ship_order

# Order verification
order_status_data, status_code = get_order_status(order_sn)

# Order shipping after verification
ship_result, ship_status = ship_order(order_sn)
```

#### VPN Config Service Integration
```python
# Generate configuration using existing service
result = config_service.generate_config(config_request)
```

## API Design

### RESTful Endpoints

#### Order Verification
```http
POST /vpn-config-generator/api/order/verify
Content-Type: application/json

{
  "order_sn": "ORDER123456",
  "buyer_username": "customer1"
}
```

**Response**:
```json
{
  "success": true,
  "user_uuid": "uuid-here",
  "order_sn": "ORDER123456",
  "buyer_username": "customer1",
  "sku": "vpn_service",
  "var_sku": "my_highspeed_15",
  "is_repeat_customer": false,
  "assigned_telco": null,
  "allowed_telcos": [],
  "is_restricted": true,
  "message": "Order verified successfully!"
}
```

#### Configuration Generation
```http
POST /vpn-config-generator/api/order/generate-config
Content-Type: application/json

{
  "user_uuid": "uuid-here",
  "order_sn": "ORDER123456",
  "server": "SG-01",
  "days": "30",
  "telco": "digi",
  "plan": "unlimited"
}
```

**Response**:
```json
{
  "success": true,
  "config": "vless://uuid@server:80?...",
  "created_date": "2024-01-01T12:00:00",
  "expired_date": "2024-01-31T12:00:00",
  "message": "Configuration generated successfully"
}
```

### Error Handling

**Standard Error Response**:
```json
{
  "success": false,
  "error": "Error message",
  "status": "order_status"
}
```

**HTTP Status Codes**:
- `200`: Success
- `400`: Bad Request (validation errors)
- `403`: Forbidden (access denied)
- `500`: Internal Server Error

## Frontend Architecture

### Technology Stack
- **HTML5**: Semantic markup
- **Tailwind CSS**: Utility-first styling
- **Vanilla JavaScript**: No framework dependencies
- **Axios**: HTTP client for API calls

### Component Structure

#### Modal System
```javascript
// Verification Modal
showVerificationModal() → verifyOrder() → showVerificationSuccess()

// Service Selection Modal  
showServiceSelection() → populateTelcoSelection() → selectTelco() → selectPlan()

// Result Modal
generateConfiguration() → showConfigurationResult()
```

#### State Management
```javascript
// Global state variables
let currentUserData = null;    // User information from verification
let selectedTelco = null;      // Currently selected telco
let selectedPlan = null;       // Currently selected plan
let availableTelcos = {};      // Available telco configurations
```

### User Experience Flow

1. **Order Entry**: Simple form with floating label input
2. **Verification**: Loading indicator with progress feedback
3. **Service Selection**: Interactive cards with visual selection
4. **Restriction Display**: Clear indication of access limitations
5. **Configuration Display**: Formatted output with copy functionality

## Security Implementation

### Access Control Matrix

| User Type | Telco Access | Plan Access | Restrictions |
|-----------|--------------|-------------|--------------|
| Regular | All telcos | All plans | None |
| Restricted (new) | All telcos | All plans | Locked after first selection |
| Restricted (assigned) | Assigned only | All plans in telco | Cannot change telco |

### Data Protection

**UUID Generation**:
```python
user_uuid = str(uuid.uuid4())  # Cryptographically secure
```

**Input Validation**:
```python
# Required field validation
required_fields = ['user_uuid', 'order_sn', 'server', 'days', 'telco', 'plan']
for field in required_fields:
    if field not in data:
        return error_response(f'Missing field: {field}')
```

**Access Control Enforcement**:
```python
can_access, access_message = order_service.can_user_access_telco(user_uuid, telco)
if not can_access:
    return jsonify({'success': False, 'error': access_message}), 403
```

## Testing Strategy

### Unit Tests
- Model validation and serialization
- SKU pattern matching
- Access control logic
- Data persistence

### Integration Tests
- Order verification workflow
- Configuration generation
- User state management
- API endpoint functionality

### Test Coverage
```bash
# Run tests
python plugins/vpn_config_generator/test_order_integration.py

# Expected output
🧪 Starting VPN Order Integration Tests...
✅ SKU restriction pattern matching works correctly
✅ VPN User model works correctly
✅ Data model serialization works correctly
🎉 All tests passed successfully!
```

## Performance Considerations

### Data Storage
- **JSON Files**: Suitable for moderate user volumes (<10,000 users)
- **Atomic Operations**: Prevent data corruption during concurrent access
- **Memory Caching**: User data cached in memory for fast access

### Optimization Opportunities
- **Database Migration**: For high-volume deployments
- **Caching Layer**: Redis for session management
- **API Rate Limiting**: Prevent abuse
- **Background Processing**: Async order processing

## Deployment

### Docker Compatibility
- Configuration stored in mounted `configs/` directory
- No database dependencies
- Stateless application design

### Environment Variables
```bash
# Optional configuration
VPN_ORDER_DEBUG=true          # Enable debug logging
VPN_ORDER_MAX_USERS=10000     # Maximum user limit
VPN_ORDER_CACHE_TTL=3600      # Cache timeout in seconds
```

### Health Checks
```python
# Service health endpoint
@bp.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'users': len(order_service._users),
        'restrictions': len(order_service._restrictions)
    })
```

## Monitoring & Logging

### Log Levels
- **INFO**: User actions, order processing
- **WARNING**: Access denied, configuration failures
- **ERROR**: System errors, data corruption
- **DEBUG**: Detailed flow information

### Metrics
- User registration rate
- Configuration generation success rate
- Telco assignment distribution
- Error frequency by type

### Log Format
```
[2024-01-01 12:00:00] INFO [VPNOrderService] Order ORDER123 verified for user uuid-here
[2024-01-01 12:00:01] WARNING [VPNOrderService] User uuid-here denied access to celcom: locked to digi
```

## Maintenance

### Regular Tasks
- **User Data Cleanup**: Archive old inactive users
- **Log Rotation**: Manage log file sizes
- **Configuration Backup**: Regular backup of user and restriction data
- **Performance Monitoring**: Track response times and error rates

### Troubleshooting Commands
```bash
# Check user data
cat configs/plugins/vpn_config_generator/vpn_users.json | jq '.[] | select(.is_restricted == true)'

# Check restrictions
cat configs/plugins/vpn_config_generator/sku_restrictions.json | jq '.[] | select(.enabled == true)'

# Test configuration
curl -X POST http://localhost:5000/vpn-config-generator/api/order/verify \
  -H "Content-Type: application/json" \
  -d '{"order_sn": "TEST123"}'
```
