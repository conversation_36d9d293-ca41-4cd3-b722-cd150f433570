{% extends "base.html" %}

{% block title %}Self Redeem Text SKUs{% endblock %}
{% block header %}Self Redeem Text SKUs Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="selfRedeemData()" x-init="init()">
    <div x-show="isLoaded">
        <!-- Add New SKU -->
        <div class="mb-6 bg-white shadow rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Add New SKU</h3>
            <div class="config-item">
                <label for="new_sku" class="block text-sm font-medium text-gray-700">SKU</label>
                <input id="new_sku" name="new_sku" type="text" x-model="newTextSku.sku"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
            <div class="config-item mt-4">
                <label for="new_message" class="block text-sm font-medium text-gray-700">Redeem Message Template</label>
                <textarea id="new_message" name="new_message" x-model="newTextSku.message"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    rows="3" placeholder="Use {result} to include the stock item"></textarea>
            </div>
            <div class="config-item mt-4 flex items-center">
                <input type="checkbox" id="new_is_unlimited" x-model="newTextSku.is_unlimited_stock" class="mr-2">
                <label for="new_is_unlimited" class="text-sm font-medium text-gray-700">Is Unlimited Stock?</label>
            </div>
            <button @click="addTextSku"
                class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                Add SKU
            </button>
        </div>

        <!-- Existing SKUs -->
        <div class="mb-6 bg-white shadow rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Existing SKUs</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                SKU</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                                Redeem Message Template</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                ♾️ Stock</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="(sku, index) in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY" :key="index">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="text" x-model="sku.sku"
                                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <textarea x-model="sku.message"
                                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        rows="2" placeholder="Use {result} to include the stock item"></textarea>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" x-model="sku.is_unlimited_stock" class="mr-2">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <button @click.prevent="deleteTextSku(index)"
                                        class="text-red-600 hover:text-red-900 mr-2">Delete</button>
                                    <button x-show="!sku.is_unlimited_stock" @click="selectTextSku(sku)"
                                        class="text-blue-600 hover:text-blue-900">Manage Stock</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Enhanced Stock Management for Selected SKU -->
        <div class="mb-6 bg-white shadow rounded-lg p-6" x-show="selectedTextSku !== null && !selectedTextSku.is_unlimited_stock">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold">Manage Stock for SKU: <span x-text="selectedTextSku.sku" class="text-blue-600"></span></h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">Total Items: <span x-text="filteredStockItems.length" class="font-semibold"></span></span>
                    <button @click="selectedTextSku = null"
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        Close
                    </button>
                </div>
            </div>

            <!-- Stock Management Controls -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Add New Stock Items -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-lg font-medium mb-3">Add New Stock Items</h4>
                    <textarea id="new_stock_item" name="new_stock_item" x-model="newStockItem"
                        class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        rows="4" placeholder="Enter one item per line&#10;Item 1&#10;Item 2&#10;Item 3"></textarea>
                    <div class="flex space-x-2 mt-3">
                        <button @click="addStockItems"
                            class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Items
                        </button>
                        <button @click="importFromFile"
                            class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Import File
                        </button>
                    </div>
                    <input type="file" x-ref="fileInput" @change="handleFileImport" class="hidden" accept=".txt,.csv">
                </div>

                <!-- Bulk Operations -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-lg font-medium mb-3">Bulk Operations</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">Selected: <span x-text="selectedItems.length" class="font-semibold"></span> items</span>
                            <div class="flex space-x-2">
                                <button @click="selectAllVisible" 
                                    class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                    Select All Visible
                                </button>
                                <button @click="clearSelection" 
                                    class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                    Clear
                                </button>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="deleteSelectedItems" x-show="selectedItems.length > 0"
                                class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete Selected
                            </button>
                            <button @click="exportStockItems"
                                class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="mb-4 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" x-model="stockSearchTerm" 
                            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Search stock items...">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <select x-model="itemsPerPage" @change="currentPage = 1"
                        class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="10">10 per page</option>
                        <option value="25">25 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                        <option value="all">Show All</option>
                    </select>
                </div>
            </div>

            <!-- Stock Items Table -->
            <div class="overflow-x-auto border border-gray-200 rounded-lg">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left">
                                <input type="checkbox" @change="toggleSelectAll($event)" 
                                    :checked="selectedItems.length === paginatedStockItems.length && paginatedStockItems.length > 0"
                                    class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                #
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stock Item
                            </th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="(item, index) in paginatedStockItems" :key="getGlobalIndex(index)">
                            <tr :class="selectedItems.includes(getGlobalIndex(index)) ? 'bg-blue-50' : 'hover:bg-gray-50'">
                                <td class="px-4 py-3">
                                    <input type="checkbox" :value="getGlobalIndex(index)" x-model="selectedItems"
                                        class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                </td>
                                <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                                    <span x-text="getGlobalIndex(index) + 1"></span>
                                </td>
                                <td class="px-6 py-3">
                                    <textarea x-model="item.value" @input="updateStockItem(getGlobalIndex(index), $event.target.value)"
                                        class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm resize-none"
                                        rows="1" 
                                        @focus="$event.target.style.height = 'auto'; $event.target.style.height = $event.target.scrollHeight + 'px'"
                                        @blur="$event.target.style.height = '2.25rem'"></textarea>
                                </td>
                                <td class="px-6 py-3 whitespace-nowrap text-center">
                                    <button @click.prevent="deleteStockItem(selectedTextSku.sku, getGlobalIndex(index))"
                                        class="text-red-600 hover:text-red-900 text-sm">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </td>
                            </tr>
                        </template>
                        <tr x-show="filteredStockItems.length === 0">
                            <td colspan="4" class="px-6 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-300 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-2.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7"></path>
                                    </svg>
                                    <span x-show="stockSearchTerm">No items found matching your search.</span>
                                    <span x-show="!stockSearchTerm">No stock items available. Add some items above.</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 flex items-center justify-between" x-show="totalPages > 1">
                <div class="text-sm text-gray-700">
                    Showing <span x-text="startIndex + 1"></span> to <span x-text="Math.min(endIndex, filteredStockItems.length)"></span> of <span x-text="filteredStockItems.length"></span> results
                </div>
                <div class="flex space-x-1">
                    <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1"
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Previous
                    </button>
                    <template x-for="page in visiblePages" :key="page">
                        <button @click="currentPage = page" 
                            :class="page === currentPage ? 'bg-indigo-600 text-white' : 'bg-white text-gray-500 hover:bg-gray-50'"
                            class="px-3 py-2 text-sm font-medium border border-gray-300 rounded-md">
                            <span x-text="page"></span>
                        </button>
                    </template>
                    <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages"
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Next
                    </button>
                </div>
            </div>
        </div>

        <!-- Ship Success Message Template -->
        <div class="mb-6 bg-white shadow rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Ship Success Message Template</h3>
            <div class="config-item">
                <label for="ship_success_message" class="block text-sm font-medium text-gray-700">Ship Success Message Template</label>
                <textarea id="ship_success_message" name="ship_success_message"
                    x-model="config.SHIP_SUCCESS_MESSAGE_TEMPLATE"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    rows="3"></textarea>
                <p class="mt-2 text-sm text-gray-600">
                    Available variables: {buyer_username}, {order_sn}
                </p>
            </div>
        </div>

        <!-- Save Button -->
        <div class="mb-6">
            <button @click="saveConfig"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                Save Configuration
            </button>
        </div>
    </div>
    
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function selfRedeemData() {
        return {
            config: {},
            isLoaded: false,
            newTextSku: {
                sku: '',
                message: '',
                is_unlimited_stock: false
            },
            selectedTextSku: null,
            newStockItem: '',
            // Enhanced stock management properties
            stockSearchTerm: '',
            selectedItems: [],
            currentPage: 1,
            itemsPerPage: 25,
            
            init() {
                this.loadConfig();
            },
            
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            
            saveConfig() {
                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        this.animateSaveButton();
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                        alert('An error occurred while saving the configuration.');
                    });
            },
            
            addTextSku() {
                if (this.newTextSku.sku.trim() === '') {
                    alert('SKU cannot be empty.');
                    return;
                }
                if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY) {
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY = [];
                }
                this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.push({
                    sku: this.newTextSku.sku.trim(),
                    message: this.newTextSku.message.trim(),
                    is_unlimited_stock: this.newTextSku.is_unlimited_stock
                });
                if (!this.newTextSku.is_unlimited_stock) {
                    if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                    }
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push({
                        sku: this.newTextSku.sku.trim(),
                        stock: []
                    });
                }
                this.newTextSku = { sku: '', message: '', is_unlimited_stock: false };
            },
            
            deleteTextSku(index) {
                if (confirm('Are you sure you want to delete this SKU?')) {
                    const sku = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY[index];
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.splice(index, 1);
                    if (!sku.is_unlimited_stock && this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        const stockIndex = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.findIndex(s => s.sku === sku.sku);
                        if (stockIndex !== -1) {
                            this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.splice(stockIndex, 1);
                        }
                    }
                }
            },
            
            selectTextSku(sku) {
                this.selectedTextSku = sku;
                this.stockSearchTerm = '';
                this.selectedItems = [];
                this.currentPage = 1;
            },
            
            getStockItems(sku) {
                if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                    return [];
                }
                const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === sku);
                return stockEntry ? stockEntry.stock : [];
            },
            
            // Enhanced computed properties for stock management
            get filteredStockItems() {
                if (!this.selectedTextSku) return [];
                
                const items = this.getStockItems(this.selectedTextSku.sku);
                const mapped = items.map((item, index) => ({ value: item, originalIndex: index }));
                
                if (!this.stockSearchTerm.trim()) {
                    return mapped;
                }
                
                const searchTerm = this.stockSearchTerm.toLowerCase();
                return mapped.filter(item => 
                    item.value.toLowerCase().includes(searchTerm)
                );
            },
            
            get totalPages() {
                if (this.itemsPerPage === 'all') return 1;
                return Math.ceil(this.filteredStockItems.length / parseInt(this.itemsPerPage));
            },
            
            get startIndex() {
                if (this.itemsPerPage === 'all') return 0;
                return (this.currentPage - 1) * parseInt(this.itemsPerPage);
            },
            
            get endIndex() {
                if (this.itemsPerPage === 'all') return this.filteredStockItems.length;
                return this.startIndex + parseInt(this.itemsPerPage);
            },
            
            get paginatedStockItems() {
                if (this.itemsPerPage === 'all') {
                    return this.filteredStockItems;
                }
                return this.filteredStockItems.slice(this.startIndex, this.endIndex);
            },
            
            get visiblePages() {
                const pages = [];
                const total = this.totalPages;
                const current = this.currentPage;
                
                // Show up to 5 pages around current page
                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);
                
                // Adjust if we're near the beginning or end
                if (end - start < 4) {
                    if (start === 1) {
                        end = Math.min(total, start + 4);
                    } else if (end === total) {
                        start = Math.max(1, end - 4);
                    }
                }
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                return pages;
            },
            
            getGlobalIndex(localIndex) {
                return this.filteredStockItems[this.startIndex + localIndex]?.originalIndex ?? localIndex;
            },
            
            addStockItems() {
                if (this.newStockItem.trim() === '') {
                    alert('Stock items cannot be empty.');
                    return;
                }
                if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                }
                let stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === this.selectedTextSku.sku);
                if (!stockEntry) {
                    stockEntry = { sku: this.selectedTextSku.sku, stock: [] };
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push(stockEntry);
                }

                // Split the input by newlines and add each non-empty item
                const newItems = this.newStockItem.split('\n').map(item => item.trim()).filter(item => item !== '');
                stockEntry.stock.push(...newItems);

                this.newStockItem = '';
                // Force Alpine to recognize the change
                this.config = { ...this.config };
                
                // Show success message
                this.showNotification(`Added ${newItems.length} items successfully!`, 'success');
            },
            
            deleteStockItem(sku, index) {
                if (confirm('Are you sure you want to delete this item?')) {
                    const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === sku);
                    if (stockEntry) {
                        stockEntry.stock.splice(index, 1);
                        this.selectedItems = this.selectedItems.filter(i => i !== index);
                        this.showNotification('Item deleted successfully!', 'success');
                    }
                }
            },
            
            updateStockItem(index, value) {
                const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === this.selectedTextSku.sku);
                if (stockEntry && stockEntry.stock[index] !== undefined) {
                    stockEntry.stock[index] = value;
                }
            },
            
            // Bulk operations
            selectAllVisible() {
                this.selectedItems = this.paginatedStockItems.map((_, index) => this.getGlobalIndex(index));
            },
            
            clearSelection() {
                this.selectedItems = [];
            },
            
            toggleSelectAll(event) {
                if (event.target.checked) {
                    this.selectAllVisible();
                } else {
                    this.clearSelection();
                }
            },
            
            deleteSelectedItems() {
                if (this.selectedItems.length === 0) return;
                
                if (confirm(`Are you sure you want to delete ${this.selectedItems.length} selected items?`)) {
                    const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === this.selectedTextSku.sku);
                    if (stockEntry) {
                        // Sort indices in descending order to avoid index shifting issues
                        const sortedIndices = [...this.selectedItems].sort((a, b) => b - a);
                        sortedIndices.forEach(index => {
                            stockEntry.stock.splice(index, 1);
                        });
                        this.selectedItems = [];
                        this.showNotification(`Deleted ${sortedIndices.length} items successfully!`, 'success');
                    }
                }
            },
            
            // File operations
            importFromFile() {
                this.$refs.fileInput.click();
            },
            
            handleFileImport(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    const content = e.target.result;
                    const lines = content.split('\n').map(line => line.trim()).filter(line => line !== '');
                    
                    if (lines.length === 0) {
                        alert('No valid items found in the file.');
                        return;
                    }
                    
                    if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                    }
                    let stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === this.selectedTextSku.sku);
                    if (!stockEntry) {
                        stockEntry = { sku: this.selectedTextSku.sku, stock: [] };
                        this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push(stockEntry);
                    }
                    
                    stockEntry.stock.push(...lines);
                    this.config = { ...this.config };
                    this.showNotification(`Imported ${lines.length} items from file!`, 'success');
                };
                reader.readAsText(file);
                
                // Reset file input
                event.target.value = '';
            },
            
            exportStockItems() {
                const items = this.getStockItems(this.selectedTextSku.sku);
                if (items.length === 0) {
                    alert('No items to export.');
                    return;
                }
                
                const content = items.join('\n');
                const blob = new Blob([content], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${this.selectedTextSku.sku}_stock_items.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                this.showNotification('Stock items exported successfully!', 'success');
            },
            
            // Utility functions
            showNotification(message, type = 'info') {
                // Simple notification - you can enhance this with a proper notification system
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white ${type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'}`;
                notification.textContent = message;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 3000);
            },
            
            animateInitialLoad() {
                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}
