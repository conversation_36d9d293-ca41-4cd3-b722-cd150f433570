"""
OpenAI Plus Redeem Plugin

Main plugin class implementing the PluginInterface for ChatGPT Plus account redemption functionality.
"""

import logging
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from flask import Blueprint

from core.plugin_manager import PluginInterface, DashboardWidget

logger = logging.getLogger(__name__)


class Plugin(PluginInterface):
    """OpenAI Plus Account Redeem Plugin"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "openai_plus_redeem"
        self.version = "1.0.0"
        self.description = "ChatGPT Plus Account Redeem Plugin - Provides order redemption, email verification, and account management"
        self.dependencies = []
        self.url_prefix = "/api/openai-plus-redeem"
        
        # Initialize components
        self.services = {}
        self.blueprint = None
        self.logger = logging.getLogger(f"plugins.{self.name}")
        
        # Load configuration
        self._load_plugin_config()
        
    def _load_plugin_config(self):
        """Load plugin configuration from config.json"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self.config = json.load(f)
                self.logger.info("Plugin configuration loaded successfully")
            else:
                self.logger.warning("No config.json found, using default configuration")
                self.config = self._get_default_config()
        except Exception as e:
            self.logger.error(f"Failed to load plugin configuration: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "enabled": True,
            "email_config": {
                "imap_server": "imap.gmail.com",
                "imap_port": 993,
                "global_credentials": {"email": "", "password": ""}
            },
            "cooldown_config": {
                "default_cooldown_hours": 24,
                "max_reset_attempts": 3
            },
            "shopee_config": {
                "message_template": "Your ChatGPT Plus account details: Email: {email}, Password: {password}",
                "auto_send_enabled": True
            }
        }
    
    def initialize(self) -> bool:
        """Initialize the OpenAI Plus Redeem plugin"""
        try:
            self.logger.info(f"Initializing {self.name} plugin...")
            
            if not self.config.get('enabled', True):
                self.logger.info(f"{self.name} plugin is disabled in configuration")
                return True
            
            # Initialize services
            self._initialize_services()
            
            # Create routes
            self._create_blueprint()
            
            self.logger.info(f"{self.name} plugin initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize {self.name} plugin: {e}")
            return False
    
    def _initialize_services(self):
        """Initialize all plugin services"""
        try:
            # Import service manager
            from .services.service_manager import ServiceManager, ServiceInitializationError

            # Create and initialize service manager
            self.service_manager = ServiceManager(self.config, self.logger)

            if not self.service_manager.initialize_all_services():
                raise ServiceInitializationError("Failed to initialize services")

            self.logger.info("All plugin services initialized successfully")

        except ImportError as e:
            self.logger.error(f"Failed to import service manager: {e}")
            raise
        except ServiceInitializationError as e:
            self.logger.error(f"Service initialization failed: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during service initialization: {e}")
            raise
    
    def _create_blueprint(self):
        """Create Flask blueprint for plugin routes"""
        try:
            # Import route creators
            from .routes import create_customer_blueprint, create_admin_blueprint

            # Create main plugin blueprint
            self.blueprint = Blueprint(
                self.name,
                __name__,
                url_prefix=self.url_prefix
            )

            # Add standard plugin endpoints
            @self.blueprint.route('/health', methods=['GET'])
            def health_check():
                from flask import jsonify

                # Get service health if service manager is available
                if hasattr(self, 'service_manager') and self.service_manager:
                    service_health = self.service_manager.health_check_all_services()
                    return jsonify({
                        'status': service_health.get('status', 'unknown'),
                        'plugin': self.name,
                        'version': self.version,
                        'enabled': self.config.get('enabled', True),
                        'services': service_health,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        'status': 'healthy',
                        'plugin': self.name,
                        'version': self.version,
                        'enabled': self.config.get('enabled', True),
                        'services': 'not_initialized',
                        'timestamp': datetime.now().isoformat()
                    })

            @self.blueprint.route('/info', methods=['GET'])
            def plugin_info():
                from flask import jsonify
                return jsonify({
                    'name': self.name,
                    'version': self.version,
                    'description': self.description,
                    'enabled': self.config.get('enabled', True),
                    'url_prefix': self.url_prefix,
                    'endpoints': {
                        'health': f'{self.url_prefix}/health',
                        'info': f'{self.url_prefix}/info',
                        'customer': f'/openai-plus-redeem/',
                        'admin': f'/admin/openai-plus-redeem/'
                    },
                    'timestamp': datetime.now().isoformat()
                })

            # Create and register sub-blueprints
            customer_bp = create_customer_blueprint(self)
            admin_bp = create_admin_blueprint(self)

            # Register sub-blueprints with the main app (they have their own URL prefixes)
            from flask import current_app
            if current_app:
                current_app.register_blueprint(customer_bp)
                current_app.register_blueprint(admin_bp)

            self.logger.info("Blueprint created successfully with customer and admin routes")

        except Exception as e:
            self.logger.error(f"Failed to create blueprint: {e}")
            raise
    
    def shutdown(self) -> bool:
        """Shutdown the plugin"""
        try:
            self.logger.info(f"Shutting down {self.name} plugin...")

            # Shutdown services via service manager
            if hasattr(self, 'service_manager') and self.service_manager:
                if not self.service_manager.shutdown_all_services():
                    self.logger.warning("Some services failed to shutdown properly")
                self.service_manager = None

            # Legacy service shutdown (for backward compatibility)
            if hasattr(self, 'services'):
                for service_name, service in self.services.items():
                    try:
                        if hasattr(service, 'shutdown'):
                            service.shutdown()
                        self.logger.info(f"Service {service_name} shutdown successfully")
                    except Exception as e:
                        self.logger.error(f"Error shutting down service {service_name}: {e}")

            self.logger.info(f"{self.name} plugin shutdown successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error shutting down {self.name} plugin: {e}")
            return False
    
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return Flask blueprint for this plugin's routes"""
        return self.blueprint
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return configuration schema for this plugin"""
        return {
            "type": "object",
            "properties": {
                "enabled": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable/disable plugin"
                },
                "email_config": {
                    "type": "object",
                    "properties": {
                        "imap_server": {
                            "type": "string",
                            "default": "imap.gmail.com",
                            "description": "IMAP server for email verification"
                        },
                        "imap_port": {
                            "type": "integer",
                            "default": 993,
                            "description": "IMAP server port"
                        },
                        "global_credentials": {
                            "type": "object",
                            "properties": {
                                "email": {"type": "string", "description": "Email address"},
                                "password": {"type": "string", "description": "Email password"}
                            },
                            "required": ["email", "password"]
                        }
                    },
                    "required": ["imap_server", "imap_port", "global_credentials"]
                },
                "cooldown_config": {
                    "type": "object",
                    "properties": {
                        "default_cooldown_hours": {
                            "type": "integer",
                            "default": 24,
                            "description": "Default cooldown period in hours"
                        },
                        "max_reset_attempts": {
                            "type": "integer",
                            "default": 3,
                            "description": "Maximum cooldown reset attempts"
                        }
                    }
                },
                "shopee_config": {
                    "type": "object",
                    "properties": {
                        "message_template": {
                            "type": "string",
                            "default": "Your ChatGPT Plus account details: Email: {email}, Password: {password}",
                            "description": "Message template for Shopee notifications"
                        },
                        "auto_send_enabled": {
                            "type": "boolean",
                            "default": True,
                            "description": "Enable automatic message sending"
                        }
                    }
                }
            },
            "required": ["enabled"]
        }
    
    def get_dashboard_widgets(self) -> List[DashboardWidget]:
        """Return list of dashboard widgets provided by this plugin"""
        widgets = []
        
        # OpenAI Plus redemption statistics widget
        redemption_widget = DashboardWidget(
            widget_id="openai-plus-redemptions",
            title="OpenAI Plus Redemptions",
            position="main",
            order=50,
            size="medium"
        )
        redemption_widget.data_endpoint = f"{self.url_prefix}/widget/redemptions"
        redemption_widget.refresh_interval = 60  # Refresh every minute
        widgets.append(redemption_widget)
        
        # Active cooldowns widget
        cooldown_widget = DashboardWidget(
            widget_id="openai-plus-cooldowns",
            title="Active OpenAI Plus Cooldowns",
            position="sidebar",
            order=30,
            size="small"
        )
        cooldown_widget.data_endpoint = f"{self.url_prefix}/widget/cooldowns"
        cooldown_widget.refresh_interval = 30  # Refresh every 30 seconds
        widgets.append(cooldown_widget)
        
        return widgets
    
    def get_widget_data(self, widget_id: str) -> Dict[str, Any]:
        """Get data for a specific widget"""
        if widget_id == "openai-plus-redemptions":
            # This would normally fetch real redemption data
            return {
                "total_redemptions": 0,
                "active_redemptions": 0,
                "pending_redemptions": 0,
                "expired_redemptions": 0
            }
        elif widget_id == "openai-plus-cooldowns":
            # This would normally fetch real cooldown data
            return {
                "active_cooldowns": 0,
                "cooldowns": []
            }
        return {"error": f"Unknown widget: {widget_id}"}
