# VPN API Analysis Report
Generated from: VPSScriptHelper-BlueBlue
Version: 1.0.0
Total Endpoints: 55

## Endpoint Summary by Category
- **Authentication**: 5 endpoints
- **Servers**: 12 endpoints
- **Clients**: 14 endpoints
- **Configuration**: 9 endpoints
- **Task Manager**: 4 endpoints
- **Background Tasks**: 1 endpoints
- **Health**: 7 endpoints
- **WebSocket Health**: 1 endpoints

## Authentication Endpoints

### POST /api/v1/auth/register
**Operation ID**: register_api_v1_auth_register_post
**Summary**: Register
**Description**: Register a new user.
**Security**: Public endpoint
**Request Body**: Required

### POST /api/v1/auth/login
**Operation ID**: login_api_v1_auth_login_post
**Summary**: Login
**Description**: Authenticate user and return access token.
**Security**: Public endpoint
**Request Body**: Required

### GET /api/v1/auth/me
**Operation ID**: get_current_user_info_api_v1_auth_me_get
**Summary**: Get Current User Info
**Description**: Get current user information.
**Security**: Requires authentication

### POST /api/v1/auth/logout
**Operation ID**: logout_api_v1_auth_logout_post
**Summary**: Logout
**Description**: Logout user (client should discard the token).
**Security**: Requires authentication

### POST /api/v1/auth/refresh
**Operation ID**: refresh_token_api_v1_auth_refresh_post
**Summary**: Refresh Token
**Description**: Refresh access token.
**Security**: Requires authentication

## Servers Endpoints

### GET /api/v1/servers/
**Operation ID**: get_servers_api_v1_servers__get
**Summary**: Get Servers
**Description**: Get list of servers.
**Security**: Requires authentication
**Parameters**:
  - skip (query) (optional)
  - limit (query) (optional)

### POST /api/v1/servers/
**Operation ID**: create_server_api_v1_servers__post
**Summary**: Create Server
**Description**: Create a new server.
**Security**: Requires authentication
**Request Body**: Required

### POST /api/v1/servers/test-credentials
**Operation ID**: test_server_credentials_api_v1_servers_test_credentials_post
**Summary**: Test Server Credentials
**Description**: Test server credentials before creating a server.
**Security**: Requires authentication
**Request Body**: Required

### GET /api/v1/servers/{server_id}
**Operation ID**: get_server_api_v1_servers__server_id__get
**Summary**: Get Server
**Description**: Get server by ID.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### PUT /api/v1/servers/{server_id}
**Operation ID**: update_server_api_v1_servers__server_id__put
**Summary**: Update Server
**Description**: Update server information.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)
**Request Body**: Required

### DELETE /api/v1/servers/{server_id}
**Operation ID**: delete_server_api_v1_servers__server_id__delete
**Summary**: Delete Server
**Description**: Delete server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/servers/{server_id}/test-connection
**Operation ID**: test_server_connection_api_v1_servers__server_id__test_connection_post
**Summary**: Test Server Connection
**Description**: Test SSH connection to server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/servers/{server_id}/restart-xray
**Operation ID**: restart_xray_service_api_v1_servers__server_id__restart_xray_post
**Summary**: Restart Xray Service
**Description**: Restart Xray service on server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### GET /api/v1/servers/{server_id}/service-status
**Operation ID**: get_service_status_api_v1_servers__server_id__service_status_get
**Summary**: Get Service Status
**Description**: Get Xray service status.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### GET /api/v1/servers/{server_id}/config
**Operation ID**: get_server_config_api_v1_servers__server_id__config_get
**Summary**: Get Server Config
**Description**: Get Xray configuration from server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### GET /api/v1/servers/{server_id}/clients
**Operation ID**: get_server_clients_api_v1_servers__server_id__clients_get
**Summary**: Get Server Clients
**Description**: Get clients from server configuration.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/servers/{server_id}/remove-expired
**Operation ID**: remove_expired_clients_api_v1_servers__server_id__remove_expired_post
**Summary**: Remove Expired Clients
**Description**: Remove expired clients from server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

## Clients Endpoints

### GET /api/v1/clients/
**Operation ID**: get_clients_api_v1_clients__get
**Summary**: Get Clients
**Description**: Get list of clients with filtering and pagination.
**Security**: Requires authentication
**Parameters**:
  - skip (query) (optional)
  - limit (query) (optional)
  - server_id (query) (optional)
  - is_active (query) (optional)
  - is_expired (query) (optional)
  - search (query) (optional)

### POST /api/v1/clients/
**Operation ID**: create_client_api_v1_clients__post
**Summary**: Create Client
**Description**: Create a new client and add to server configuration.
**Security**: Requires authentication
**Request Body**: Required

### GET /api/v1/clients/by-email/{email}
**Operation ID**: get_client_by_email_api_v1_clients_by_email__email__get
**Summary**: Get Client By Email
**Description**: Get client by email address.
**Security**: Requires authentication
**Parameters**:
  - email (path) (required)
  - server_id (query) (optional)

### GET /api/v1/clients/{client_id}
**Operation ID**: get_client_api_v1_clients__client_id__get
**Summary**: Get Client
**Description**: Get client by ID.
**Security**: Requires authentication
**Parameters**:
  - client_id (path) (required)

### PUT /api/v1/clients/{client_id}
**Operation ID**: update_client_api_v1_clients__client_id__put
**Summary**: Update Client
**Description**: Update client information.
**Security**: Requires authentication
**Parameters**:
  - client_id (path) (required)
**Request Body**: Required

### DELETE /api/v1/clients/{client_id}
**Operation ID**: delete_client_api_v1_clients__client_id__delete
**Summary**: Delete Client
**Description**: Delete client from database and server configuration.
**Security**: Requires authentication
**Parameters**:
  - client_id (path) (required)

### POST /api/v1/clients/bulk
**Operation ID**: create_clients_bulk_api_v1_clients_bulk_post
**Summary**: Create Clients Bulk
**Description**: Create multiple clients in bulk.
**Security**: Requires authentication
**Request Body**: Required

### GET /api/v1/clients/server/{server_id}/expiry
**Operation ID**: get_server_client_expiry_api_v1_clients_server__server_id__expiry_get
**Summary**: Get Server Client Expiry
**Description**: Get expiry information for all clients on a server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/clients/{client_id}/extend
**Operation ID**: extend_client_expiry_api_v1_clients__client_id__extend_post
**Summary**: Extend Client Expiry
**Description**: Extend client expiry by specified number of days.
**Security**: Requires authentication
**Parameters**:
  - client_id (path) (required)
  - days (query) (required)

### POST /api/v1/clients/sync/server/{server_id}
**Operation ID**: sync_server_clients_api_v1_clients_sync_server__server_id__post
**Summary**: Sync Server Clients
**Description**: Sync clients from server Xray configuration to local database.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/clients/sync/all
**Operation ID**: sync_all_servers_clients_api_v1_clients_sync_all_post
**Summary**: Sync All Servers Clients
**Description**: Sync clients from all active servers to local database.
**Security**: Requires authentication

### GET /api/v1/clients/sync/compare/{server_id}
**Operation ID**: compare_server_clients_api_v1_clients_sync_compare__server_id__get
**Summary**: Compare Server Clients
**Description**: Compare clients between server configuration and local database.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### GET /api/v1/clients/sync/analyze/{server_id}
**Operation ID**: analyze_invalid_clients_api_v1_clients_sync_analyze__server_id__get
**Summary**: Analyze Invalid Clients
**Description**: Analyze invalid clients in server configuration.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/clients/sync/cleanup/{server_id}
**Operation ID**: cleanup_orphaned_clients_api_v1_clients_sync_cleanup__server_id__post
**Summary**: Cleanup Orphaned Clients
**Description**: Remove clients from database that don't exist in server configuration.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

## Configuration Endpoints

### GET /api/v1/config/
**Operation ID**: get_configurations_api_v1_config__get
**Summary**: Get Configurations
**Description**: Get configuration overview for all servers.
**Security**: Requires authentication

### GET /api/v1/config/server/{server_id}
**Operation ID**: get_server_config_api_v1_config_server__server_id__get
**Summary**: Get Server Config
**Description**: Get configuration for a specific server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### PUT /api/v1/config/server/{server_id}
**Operation ID**: update_server_config_api_v1_config_server__server_id__put
**Summary**: Update Server Config
**Description**: Update configuration for a specific server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)
**Request Body**: Required

### POST /api/v1/config/backup
**Operation ID**: backup_configurations_api_v1_config_backup_post
**Summary**: Backup Configurations
**Description**: Backup server configurations.
**Security**: Requires authentication
**Request Body**: Required

### POST /api/v1/config/restore
**Operation ID**: restore_configuration_api_v1_config_restore_post
**Summary**: Restore Configuration
**Description**: Restore server configuration from a backup.
**Security**: Requires authentication
**Request Body**: Required

### GET /api/v1/config/validate/server/{server_id}
**Operation ID**: validate_server_config_api_v1_config_validate_server__server_id__get
**Summary**: Validate Server Config
**Description**: Validate configuration for a specific server.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### GET /api/v1/config/backups
**Operation ID**: list_config_backups_api_v1_config_backups_get
**Summary**: List Config Backups
**Description**: List available configuration backups.
**Security**: Requires authentication
**Parameters**:
  - server_id (query) (optional)

### GET /api/v1/config/compare/server/{server_id}
**Operation ID**: compare_server_config_api_v1_config_compare_server__server_id__get
**Summary**: Compare Server Config
**Description**: Compare server configuration with database state.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/config/sync
**Operation ID**: sync_configurations_api_v1_config_sync_post
**Summary**: Sync Configurations
**Description**: Synchronize configurations between database and servers.
**Security**: Requires authentication
**Request Body**: Required

## Task Manager Endpoints

### GET /api/v1/tasks/
**Operation ID**: get_tasks_api_v1_tasks__get
**Summary**: Get Tasks
**Description**: Get list of tasks.
**Security**: Requires authentication
**Parameters**:
  - skip (query) (optional)
  - limit (query) (optional)

### GET /api/v1/tasks/{task_id}
**Operation ID**: get_task_api_v1_tasks__task_id__get
**Summary**: Get Task
**Description**: Get task by ID.
**Security**: Requires authentication
**Parameters**:
  - task_id (path) (required)

### POST /api/v1/tasks/expiry-check
**Operation ID**: trigger_expiry_check_api_v1_tasks_expiry_check_post
**Summary**: Trigger Expiry Check
**Description**: Trigger manual expiry check.
**Security**: Requires authentication
**Parameters**:
  - server_id (query) (optional)

### GET /api/v1/tasks/expiry/summary
**Operation ID**: get_expiry_summary_api_v1_tasks_expiry_summary_get
**Summary**: Get Expiry Summary
**Description**: Get expiry summary for all servers.
**Security**: Requires authentication

## Background Tasks Endpoints

### POST /api/v1/background-tasks/enqueue-task
**Operation ID**: enqueue_task_api_v1_background_tasks_enqueue_task_post
**Summary**: Enqueue Task
**Description**: Enqueues a sample background task.
**Security**: Public endpoint

## Health Endpoints

### GET /api/v1/health/
**Operation ID**: comprehensive_health_dashboard_api_v1_health__get
**Summary**: Comprehensive Health Dashboard
**Description**: Comprehensive health dashboard endpoint matching frontend HealthDashboard interface.
**Security**: Public endpoint

### GET /api/v1/health/detailed
**Operation ID**: detailed_health_check_api_v1_health_detailed_get
**Summary**: Detailed Health Check
**Description**: Detailed health check including database and services.
**Security**: Public endpoint

### GET /api/v1/health/expiry-summary
**Operation ID**: expiry_health_check_api_v1_health_expiry_summary_get
**Summary**: Expiry Health Check
**Description**: Health check focused on client expiry status.
**Security**: Public endpoint

### GET /api/v1/health/servers/{server_id}
**Operation ID**: server_health_check_api_v1_health_servers__server_id__get
**Summary**: Server Health Check
**Description**: Health check for a specific server matching frontend ServerHealth interface.
**Security**: Public endpoint
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/health/servers/{server_id}/refresh
**Operation ID**: refresh_server_health_api_v1_health_servers__server_id__refresh_post
**Summary**: Refresh Server Health
**Description**: Manually refresh health status for a specific server matching frontend HealthCheckResult interface.
**Security**: Requires authentication
**Parameters**:
  - server_id (path) (required)

### POST /api/v1/health/refresh-all-servers
**Operation ID**: refresh_all_servers_health_api_v1_health_refresh_all_servers_post
**Summary**: Refresh All Servers Health
**Description**: Manually refresh health status for all servers in parallel matching frontend AllServersHealthCheckResult interface.
**Security**: Requires authentication

### GET /api/v1/health/ssh-pool
**Operation ID**: ssh_connection_pool_status_api_v1_health_ssh_pool_get
**Summary**: Ssh Connection Pool Status
**Description**: Get SSH connection pool status and statistics.
**Security**: Public endpoint

## WebSocket Health Endpoints

### GET /api/v1/ws/stats
**Operation ID**: get_websocket_stats_api_v1_ws_stats_get
**Summary**: Get Websocket Stats
**Description**: Get WebSocket connection statistics.
**Security**: Public endpoint
