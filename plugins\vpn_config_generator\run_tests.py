#!/usr/bin/env python3
"""
Comprehensive test runner for VPN Config Generator Plugin.
"""

import sys
import os
import unittest
import logging
import argparse
from io import StringIO

# Add plugin directory to path
plugin_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, plugin_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestResult:
    """Test result container"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.failures = []
        self.errors = []
        self.execution_time = 0


def run_test_suite(test_module_name, verbose=False):
    """Run a specific test suite"""
    logger.info(f"Running test suite: {test_module_name}")
    
    try:
        # Import test module
        test_module = __import__(f'tests.{test_module_name}', fromlist=[''])
        
        # Create test loader and suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(test_module)
        
        # Create test runner
        stream = StringIO()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=2 if verbose else 1
        )
        
        # Run tests
        import time
        start_time = time.time()
        result = runner.run(suite)
        end_time = time.time()
        
        # Create result object
        test_result = TestResult()
        test_result.total_tests = result.testsRun
        test_result.passed_tests = result.testsRun - len(result.failures) - len(result.errors) - len(result.skipped)
        test_result.failed_tests = len(result.failures)
        test_result.error_tests = len(result.errors)
        test_result.skipped_tests = len(result.skipped)
        test_result.failures = result.failures
        test_result.errors = result.errors
        test_result.execution_time = end_time - start_time
        
        # Print results
        output = stream.getvalue()
        if verbose:
            print(output)
        
        return test_result, result.wasSuccessful()
        
    except ImportError as e:
        logger.error(f"Failed to import test module {test_module_name}: {e}")
        return None, False
    except Exception as e:
        logger.error(f"Error running test suite {test_module_name}: {e}")
        return None, False


def print_test_summary(test_results):
    """Print comprehensive test summary"""
    print("\n" + "="*80)
    print("VPN CONFIG GENERATOR PLUGIN - TEST SUMMARY")
    print("="*80)
    
    total_tests = sum(result.total_tests for result in test_results.values())
    total_passed = sum(result.passed_tests for result in test_results.values())
    total_failed = sum(result.failed_tests for result in test_results.values())
    total_errors = sum(result.error_tests for result in test_results.values())
    total_skipped = sum(result.skipped_tests for result in test_results.values())
    total_time = sum(result.execution_time for result in test_results.values())
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {total_passed}")
    print(f"Failed: {total_failed}")
    print(f"Errors: {total_errors}")
    print(f"Skipped: {total_skipped}")
    print(f"Total Execution Time: {total_time:.2f} seconds")
    print()
    
    # Print results by test suite
    for suite_name, result in test_results.items():
        status = "✅ PASS" if result.failed_tests == 0 and result.error_tests == 0 else "❌ FAIL"
        print(f"{status} {suite_name}: {result.passed_tests}/{result.total_tests} passed ({result.execution_time:.2f}s)")
    
    print()
    
    # Print failure details
    if total_failed > 0 or total_errors > 0:
        print("FAILURE DETAILS:")
        print("-" * 40)
        
        for suite_name, result in test_results.items():
            if result.failures or result.errors:
                print(f"\n{suite_name}:")
                
                for test, traceback in result.failures:
                    print(f"  FAIL: {test}")
                    if args.verbose:
                        print(f"    {traceback}")
                
                for test, traceback in result.errors:
                    print(f"  ERROR: {test}")
                    if args.verbose:
                        print(f"    {traceback}")
    
    # Overall result
    overall_success = total_failed == 0 and total_errors == 0
    print("\n" + "="*80)
    if overall_success:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("💥 SOME TESTS FAILED!")
    print("="*80)
    
    return overall_success


def run_all_tests(verbose=False, include_performance=False):
    """Run all test suites"""
    test_suites = [
        'test_models',
        'test_services',
        'test_routes',
        'test_plugin',
        'test_integration'
    ]

    if include_performance:
        test_suites.append('test_performance')
    
    test_results = {}
    all_success = True
    
    print("🚀 Starting VPN Config Generator Plugin Test Suite")
    print("="*80)
    
    for suite in test_suites:
        result, success = run_test_suite(suite, verbose)
        if result:
            test_results[suite] = result
        all_success = all_success and success
    
    # Print summary
    overall_success = print_test_summary(test_results)
    
    return overall_success


def run_specific_test(test_name, verbose=False):
    """Run a specific test"""
    if test_name in ['models', 'services', 'routes', 'plugin', 'integration', 'performance']:
        test_module = f'test_{test_name}'
        result, success = run_test_suite(test_module, verbose)
        
        if result:
            print(f"\nTest Results for {test_name}:")
            print(f"Tests: {result.total_tests}")
            print(f"Passed: {result.passed_tests}")
            print(f"Failed: {result.failed_tests}")
            print(f"Errors: {result.error_tests}")
            print(f"Time: {result.execution_time:.2f}s")
            
            if success:
                print("✅ All tests passed!")
            else:
                print("❌ Some tests failed!")
        
        return success
    else:
        print(f"Unknown test: {test_name}")
        print("Available tests: models, services, routes, plugin, integration, performance")
        return False


def check_dependencies():
    """Check if all required dependencies are available"""
    logger.info("Checking dependencies...")
    
    required_modules = [
        'unittest',
        'json',
        'tempfile',
        'unittest.mock'
    ]
    
    optional_modules = [
        'flask',
        'requests'
    ]
    
    missing_required = []
    missing_optional = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_required.append(module)
    
    for module in optional_modules:
        try:
            __import__(module)
        except ImportError:
            missing_optional.append(module)
    
    if missing_required:
        logger.error(f"Missing required modules: {missing_required}")
        return False
    
    if missing_optional:
        logger.warning(f"Missing optional modules (some tests may be skipped): {missing_optional}")
    
    logger.info("✅ All required dependencies are available")
    return True


def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description='VPN Config Generator Plugin Test Runner')
    parser.add_argument('--test', '-t', help='Run specific test (models, services, routes, plugin, integration, performance)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--performance', '-p', action='store_true', help='Include performance tests')
    parser.add_argument('--check-deps', action='store_true', help='Check dependencies only')
    
    global args
    args = parser.parse_args()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    if args.check_deps:
        print("✅ All dependencies are available")
        sys.exit(0)
    
    # Run tests
    if args.test:
        success = run_specific_test(args.test, args.verbose)
    else:
        success = run_all_tests(args.verbose, args.performance)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
