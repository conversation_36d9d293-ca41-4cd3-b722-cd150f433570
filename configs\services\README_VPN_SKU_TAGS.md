# VPN SKU Tags Configuration Guide

## Overview

This guide explains how to configure and manage VPN SKU to server tags mapping using the configuration file located at `configs/services/vpn_sku_tags.json`.

## Configuration File Location

```
configs/services/vpn_sku_tags.json
```

## Configuration Structure

### Main Sections

1. **sku_server_tags_mapping**: Maps SKUs to server tags by category
2. **fallback_mapping**: Fallback tags for unmatched SKU patterns
3. **tag_definitions**: Documentation for available tags
4. **configuration_notes**: Usage guidelines

### Example Configuration

```json
{
  "sku_server_tags_mapping": {
    "malaysia_basic": {
      "my_": ["malaysia", "shinjiru", "basic"],
      "my_30": ["malaysia", "shinjiru", "basic"]
    },
    "singapore_premium": {
      "sg_premium": ["singapore", "digitalocean", "premium"]
    }
  },
  "fallback_mapping": {
    "sg_": ["singapore", "digitalocean"],
    "my_": ["malaysia", "shinjiru"],
    "default": ["malaysia", "shinjiru"]
  }
}
```

## How to Configure SKU Mappings

### Method 1: Using the Configuration Manager Tool

```bash
# Show current configuration
python plugins/vpn/tools/sku_tags_config_manager.py show

# Add a new SKU mapping
python plugins/vpn/tools/sku_tags_config_manager.py add \
  --category "thailand_basic" \
  --sku "th_30" \
  --tags "thailand,basic,new_provider"

# Remove a SKU mapping
python plugins/vpn/tools/sku_tags_config_manager.py remove --sku "th_30"

# Test SKU resolution
python plugins/vpn/tools/sku_tags_config_manager.py test

# Validate configuration
python plugins/vpn/tools/sku_tags_config_manager.py validate
```

### Method 2: Direct File Editing

1. Open `configs/services/vpn_sku_tags.json`
2. Add your SKU mapping to the appropriate category
3. Save the file
4. Restart the application or reload configuration

## SKU Mapping Examples

### Malaysia Products
```json
"malaysia_basic": {
  "my_": ["malaysia", "shinjiru", "basic"],
  "my_15": ["malaysia", "shinjiru", "basic"],
  "my_30": ["malaysia", "shinjiru", "basic"]
},
"malaysia_highspeed": {
  "my_highspeed": ["malaysia", "singapore", "highspeed", "premium"],
  "my_highspeed_30": ["malaysia", "singapore", "highspeed", "premium"]
}
```

### Singapore Products
```json
"singapore_basic": {
  "sg_": ["singapore", "digitalocean", "basic"],
  "sg_15": ["singapore", "digitalocean", "basic"]
},
"singapore_business": {
  "sg_business": ["singapore", "digitalocean", "business", "premium"]
}
```

## Available Server Tags

### Location Tags
- `malaysia` - Servers in Malaysia
- `singapore` - Servers in Singapore
- `thailand` - Servers in Thailand
- `indonesia` - Servers in Indonesia

### Provider Tags
- `shinjiru` - Shinjiru hosting
- `digitalocean` - Digital Ocean hosting
- `gbnetwork` - GBNetwork hosting
- `vultr` - Vultr hosting

### Performance Tags
- `basic` - Basic performance
- `standard` - Standard performance
- `highspeed` - High-speed performance
- `premium` - Premium performance
- `business` - Business-grade performance

### Feature Tags
- `unlimited` - Unlimited bandwidth
- `dedicated` - Dedicated IP
- `shared` - Shared IP
- `multiregion` - Multi-region access

## SKU Matching Logic

1. **Exact Match**: System looks for exact SKU matches first
2. **Pattern Match**: Uses longest matching pattern (e.g., `my_highspeed` before `my_`)
3. **Fallback**: Uses fallback mapping for unmatched patterns
4. **Default**: Falls back to default tags if no pattern matches

## Server Tag Matching

- **OR Logic**: Servers need ANY of the required tags
- **Case Insensitive**: Tag matching ignores case
- **Active Only**: Only considers active servers
- **Fallback**: If no servers match, uses all active servers

## Testing Your Configuration

### Test Specific SKUs
```bash
python plugins/vpn/tools/sku_tags_config_manager.py test \
  --test-skus "my_30,sg_premium_60,my_highspeed_30"
```

### Run Full Test Suite
```bash
python plugins/vpn/test_tag_based_strategy.py
```

## Common Configuration Patterns

### Basic Product Tier
```json
"region_basic": {
  "prefix_": ["location", "provider", "basic"],
  "prefix_15": ["location", "provider", "basic"],
  "prefix_30": ["location", "provider", "basic"]
}
```

### High-Speed Multi-Region
```json
"region_highspeed": {
  "prefix_highspeed": ["location1", "location2", "highspeed", "premium"],
  "prefix_highspeed_premium": ["location1", "location2", "highspeed", "premium", "business"]
}
```

## Troubleshooting

### No Servers Found
1. Check server tags are correctly configured
2. Verify servers are marked as active
3. Review SKU mapping in configuration file
4. Check logs for tag matching details

### Wrong Servers Selected
1. Verify SKU pattern matching order
2. Check for conflicting tag assignments
3. Review fallback mapping configuration
4. Test with configuration manager tool

### Configuration Errors
```bash
# Validate configuration file
python plugins/vpn/tools/sku_tags_config_manager.py validate
```

## Best Practices

1. **Use Descriptive Tags**: Choose clear, meaningful tag names
2. **Group by Category**: Organize SKUs into logical categories
3. **Test Changes**: Always test after configuration changes
4. **Document Custom Tags**: Add new tags to tag_definitions section
5. **Backup Configuration**: Keep backups before major changes
6. **Version Control**: Track configuration changes

## Reloading Configuration

The system automatically loads configuration on startup. To reload without restart:

```python
from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory
VPNStrategyFactory.reload_sku_tags_config()
```

## Configuration File Backup

Before making changes, backup your configuration:

```bash
cp configs/services/vpn_sku_tags.json configs/services/vpn_sku_tags.json.backup
```

## Support

For issues with SKU tag configuration:

1. Check the logs for detailed error messages
2. Validate configuration file syntax
3. Test with the configuration manager tool
4. Review this documentation for common patterns
