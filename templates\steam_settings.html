{% extends "base.html" %}

{% block title %}Steam Settings{% endblock %}
{% block header %}Steam Settings{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="steamConfigData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Sidebar -->
        <div class="w-64 mr-8">
            <nav class="space-y-1">
                <template
                    x-for="(section, index) in ['Advanced Settings', 'Auto Chat Settings']"
                    :key="index">
                    <a href="#" @click.prevent="currentSection = section; animateSection()"
                        :class="{'bg-gray-100 text-gray-900': currentSection === section, 'text-gray-600 hover:bg-gray-50 hover:text-gray-900': currentSection !== section}"
                        class="group flex items-center px-3 py-2 text-sm font-medium rounded-md sidebar-item">
                        <span x-text="section"></span>
                    </a>
                </template>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1">
            <!-- Advanced Settings Section -->
            <div x-show="currentSection === 'Advanced Settings'" class="section-content">
                <div class="bg-white p-6 rounded-lg shadow-md config-item">
                    <h2 class="text-xl font-bold mb-4">Steam Advanced Settings</h2>
                    
                    <div class="mb-6 flex items-center">
                        <input type="checkbox" 
                               id="send_chat_on_auth_success" 
                               x-model="config.SEND_CHAT_ON_AUTH_SUCCESS"
                               class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                        <label for="send_chat_on_auth_success" 
                               class="ml-2 block text-sm font-medium text-gray-700">
                            Send Chat on Auth Code Success
                        </label>
                    </div>

                    <div class="mb-6 flex items-center">
                        <input type="checkbox" 
                               id="auto_ship_order" 
                               x-model="config.AUTO_SHIP_ORDER"
                               class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                        <label for="auto_ship_order" 
                               class="ml-2 block text-sm font-medium text-gray-700">
                            Automatically Ship Order Before Showing Auth Code
                        </label>
                    </div>
                </div>
            </div>

            <!-- Auto Chat Settings Section -->
            <div x-show="currentSection === 'Auto Chat Settings'" class="section-content">
                <div class="bg-white p-6 rounded-lg shadow-md config-item">
                    <h2 class="text-xl font-bold mb-4">Steam Auto Chat Settings</h2>
                    
                    <!-- Steam-specific VAR SKUs -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">Steam Auto Chat VAR_SKUs</h3>
                        <div class="mt-2">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            VAR_SKU</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Custom Message</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <template x-for="(sku, index) in steamAutoRedeemSKUs" :key="index">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="text" x-model="sku.sku"
                                                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <textarea x-model="sku.message"
                                                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                    rows="3" placeholder="Leave blank to use global message"></textarea>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <button @click.prevent="removeSteamVarSKU(index)"
                                                    class="text-red-600 hover:text-red-900">Delete</button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                            <button @click.prevent="addSteamVarSKU"
                                class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Add Steam VAR_SKU
                            </button>
                        </div>
                    </div>

                    <!-- Steam Auto Chat Message -->
                    <div class="mb-6">
                        <label for="steam_auto_redeem_message" class="block text-sm font-medium text-gray-700">Steam Auto Chat Message</label>
                        <textarea id="steam_auto_redeem_message" name="steam_auto_redeem_message" x-model="config.STEAM_AUTO_REDEEM_MESSAGE"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            rows="10" style="white-space: pre-wrap;" placeholder="Steam-specific auto chat message template"></textarea>
                        <div class="mt-2 text-sm text-gray-600">
                            <p>Available variables for Steam message customization:</p>
                            <ul class="list-disc pl-5 mt-2">
                                <li>{order_sn} - Order serial number</li>
                                <li>{buyer_username} - Buyer's username</li>
                                <li>{item_name} - Name of the item</li>
                                <li>{item_price} - Price of the item</li>
                                <li>{buyer_name} - Buyer's name</li>
                                <li>{buyer_phone} - Buyer's phone number</li>
                                <li>{create_time} - Order creation time</li>
                                <li>{shipping_address} - Shipping address</li>
                                <li>{item_sku} - Item SKU</li>
                                <li>{item_quantity} - Quantity of items</li>
                                <li>{payment_method} - Payment method</li>
                                <li>{shop_name} - Shop name</li>
                                <li>{escrow_release_time} - Escrow release time</li>
                                <li>{buyer_rating} - Buyer's rating</li>
                                <li>{order_status} - Order status</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Ship Success Message Template -->
                    <div class="mb-6">
                        <label for="steam_ship_success_message" class="block text-sm font-medium text-gray-700">Steam Ship Success Message Template</label>
                        <textarea id="steam_ship_success_message" name="steam_ship_success_message"
                            x-model="config.STEAM_SHIP_SUCCESS_MESSAGE_TEMPLATE"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            rows="3" placeholder="Steam-specific ship success message template"></textarea>
                        <p class="mt-2 text-sm text-gray-600">
                            Available variables: {buyer_username}, {order_sn}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="mt-6">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function steamConfigData() {
        return {
            config: {},
            steamAutoRedeemSKUs: [],
            currentSection: 'Advanced Settings',
            isLoaded: false,
            init() {
                this.loadConfig();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        
                        // Initialize Steam-specific AUTO_REDEEM_VAR_SKUS if not exists
                        if (!this.config.STEAM_AUTO_REDEEM_VAR_SKUS) {
                            this.config.STEAM_AUTO_REDEEM_VAR_SKUS = [];
                        }
                        this.steamAutoRedeemSKUs = this.config.STEAM_AUTO_REDEEM_VAR_SKUS;
                        
                        // Initialize Steam-specific message if not exists
                        if (!this.config.STEAM_AUTO_REDEEM_MESSAGE) {
                            this.config.STEAM_AUTO_REDEEM_MESSAGE = '';
                        }
                        
                        // Initialize Steam ship success message if not exists
                        if (!this.config.STEAM_SHIP_SUCCESS_MESSAGE_TEMPLATE) {
                            this.config.STEAM_SHIP_SUCCESS_MESSAGE_TEMPLATE = '';
                        }
                        
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            addSteamVarSKU() {
                this.steamAutoRedeemSKUs.push({ sku: '', message: '' });
                this.config.STEAM_AUTO_REDEEM_VAR_SKUS = this.steamAutoRedeemSKUs;
            },
            removeSteamVarSKU(index) {
                this.steamAutoRedeemSKUs.splice(index, 1);
                this.config.STEAM_AUTO_REDEEM_VAR_SKUS = this.steamAutoRedeemSKUs;
            },
            saveConfig() {
                // Ensure Steam-specific configurations are saved
                this.config.STEAM_AUTO_REDEEM_VAR_SKUS = this.steamAutoRedeemSKUs;
                
                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    this.animateSaveButton();
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the configuration.');
                });
            },
            animateInitialLoad() {
                anime({
                    targets: '.sidebar-item',
                    translateX: [-50, 0],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutQuad'
                });

                this.animateSection();
            },
            animateSection() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}