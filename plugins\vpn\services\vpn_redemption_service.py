"""
VPN Redemption Service
Service for handling VPN product redemptions using strategy pattern
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

import logging
from typing import Dict, Any, Optional
from ..strategies.strategy_factory import VPNStrategyFactory, create_vpn_strategy
from .vpn_api_service import initialize_vpn_api_service

logger = logging.getLogger(__name__)

class VPNRedemptionService:
    """
    Service for handling VPN product redemptions using the strategy pattern.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize the VPN API service
        self.vpn_api_service = initialize_vpn_api_service(config)
        
        logger.info("VPN Redemption Service initialized")
    
    def redeem_vpn_product(self, order_details: Dict[str, Any], product_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Redeem a VPN product using the appropriate strategy.
        
        Args:
            order_details (Dict[str, Any]): Order information including customer details
            product_details (Dict[str, Any]): Product information including SKU
            
        Returns:
            Dict[str, Any]: Redemption result
        """
        try:
            # Extract product SKU
            product_sku = product_details.get('var_sku', product_details.get('sku', ''))
            if not product_sku:
                return {
                    "success": False,
                    "message": "Product SKU is required for VPN redemption"
                }
            
            logger.info(f"Starting VPN redemption for SKU: {product_sku}")
            
            # Check if this is a VPN product
            if not VPNStrategyFactory.is_vpn_product(product_sku):
                return {
                    "success": False,
                    "message": f"Product SKU {product_sku} is not a VPN product"
                }
            
            # Create appropriate strategy
            strategy = create_vpn_strategy(product_sku)
            if not strategy:
                return {
                    "success": False,
                    "message": f"No strategy found for product SKU: {product_sku}"
                }
            
            logger.info(f"Using strategy: {strategy.__class__.__name__}")
            
            # Execute redemption using the strategy
            result = strategy.redeem(order_details, product_details)
            
            # Log the result
            if result.get('success'):
                logger.info(f"VPN redemption successful for SKU: {product_sku}")
            else:
                logger.error(f"VPN redemption failed for SKU: {product_sku} - {result.get('message', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in VPN redemption: {str(e)}")
            return {
                "success": False,
                "message": f"Internal error during VPN redemption: {str(e)}"
            }
    
    def get_strategy_info(self, product_sku: str) -> Dict[str, Any]:
        """
        Get information about the strategy that would be used for a product SKU.
        
        Args:
            product_sku (str): Product SKU
            
        Returns:
            Dict[str, Any]: Strategy information
        """
        return VPNStrategyFactory.get_strategy_info(product_sku)
    
    def get_supported_products(self) -> Dict[str, Any]:
        """
        Get information about supported VPN products.
        
        Returns:
            Dict[str, Any]: Supported products information
        """
        return {
            "supported_skus": VPNStrategyFactory.get_supported_skus(),
            "regions": {
                "singapore": {
                    "prefix": "sg_",
                    "server_type": "digital_ocean",
                    "variants": ["basic", "highspeed", "premium", "business"]
                },
                "malaysia": {
                    "prefix": "my_",
                    "server_type": "shinjiru",
                    "variants": ["basic", "standard", "premium"]
                },
                "malaysia_highspeed": {
                    "prefix": "my_highspeed_",
                    "server_type": "all_servers",
                    "variants": ["basic", "premium"],
                    "features": ["multi_server", "shared_uuid", "server_selection"]
                }
            }
        }
    
    def validate_order_data(self, order_details: Dict[str, Any], product_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate order data for VPN redemption.
        
        Args:
            order_details (Dict[str, Any]): Order information
            product_details (Dict[str, Any]): Product information
            
        Returns:
            Dict[str, Any]: Validation result
        """
        try:
            # Extract product SKU
            product_sku = product_details.get('var_sku', product_details.get('sku', ''))
            
            # Create strategy to validate
            strategy = create_vpn_strategy(product_sku)
            if not strategy:
                return {
                    "valid": False,
                    "error": f"No strategy found for product SKU: {product_sku}"
                }
            
            # Use strategy's validation
            return strategy.validate_user_data(order_details, product_details)
            
        except Exception as e:
            logger.error(f"Error validating order data: {str(e)}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}"
            }
    
    def get_vpn_servers(self, product_sku: Optional[str] = None) -> Dict[str, Any]:
        """
        Get available VPN servers, optionally filtered by product SKU.
        
        Args:
            product_sku (str, optional): Product SKU to filter servers
            
        Returns:
            Dict[str, Any]: Server information
        """
        try:
            if product_sku:
                # Get servers for specific product
                strategy = create_vpn_strategy(product_sku)
                if strategy:
                    servers = strategy.get_target_servers(product_sku)
                    return {
                        "success": True,
                        "product_sku": product_sku,
                        "strategy": strategy.__class__.__name__,
                        "servers": servers,
                        "count": len(servers)
                    }
                else:
                    return {
                        "success": False,
                        "message": f"No strategy found for SKU: {product_sku}"
                    }
            else:
                # Get all servers
                servers = self.vpn_api_service.get_servers()
                return {
                    "success": True,
                    "servers": servers,
                    "count": len(servers)
                }
                
        except Exception as e:
            logger.error(f"Error getting VPN servers: {str(e)}")
            return {
                "success": False,
                "message": f"Error getting servers: {str(e)}"
            }
    
    def check_email_availability(self, email: str, product_sku: str) -> Dict[str, Any]:
        """
        Check if an email is available for VPN creation.
        
        Args:
            email (str): Email to check
            product_sku (str): Product SKU to determine target servers
            
        Returns:
            Dict[str, Any]: Availability check result
        """
        try:
            strategy = create_vpn_strategy(product_sku)
            if not strategy:
                return {
                    "success": False,
                    "message": f"No strategy found for SKU: {product_sku}"
                }
            
            # Get target servers for this product
            target_servers = strategy.get_target_servers(product_sku)
            if not target_servers:
                return {
                    "success": False,
                    "message": f"No servers available for SKU: {product_sku}"
                }
            
            # Check email availability
            result = strategy.check_email_availability(email, target_servers)
            
            return {
                "success": True,
                "email": email,
                "product_sku": product_sku,
                "available": result.get('available', False),
                "checked_servers": result.get('total_checked', 0),
                "details": result
            }
            
        except Exception as e:
            logger.error(f"Error checking email availability: {str(e)}")
            return {
                "success": False,
                "message": f"Error checking email: {str(e)}"
            }
