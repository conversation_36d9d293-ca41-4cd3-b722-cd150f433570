"""
Admin Routes for OpenAI Plus Redeem Plugin

Admin endpoints for dashboard, account CRUD operations, cooldown management,
and order reassignment functionality.
"""

import logging
from typing import TYPE_CHECKING
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, request, render_template, abort, url_for, redirect, session, g
from functools import wraps

if TYPE_CHECKING:
    from ..plugin import Plugin

logger = logging.getLogger(__name__)

# Import authentication utilities
try:
    from utils.auth import require_api_key
    from utils.rate_limiter import rate_limit, auth_rate_limit, get_client_ip
    AUTH_UTILS_AVAILABLE = True
except ImportError:
    logger.warning("Authentication utilities not available")
    AUTH_UTILS_AVAILABLE = False

    # Fallback decorators
    def require_api_key(func):
        return func

    def rate_limit(limit=100, window=3600, per='ip'):
        def decorator(func):
            return func
        return decorator

    def auth_rate_limit(limit=10, window=900):
        def decorator(func):
            return func
        return decorator

    def get_client_ip():
        return request.remote_addr or 'unknown'


def admin_required(func):
    """Decorator to require admin authentication"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Check session-based authentication first
        if 'admin_logged_in' in session:
            g.user_role = 'admin'
            g.user_id = session.get('admin_user_id', 'admin')
            return func(*args, **kwargs)

        # Check API key authentication
        if AUTH_UTILS_AVAILABLE:
            # Use the standard API key authentication
            api_key = request.headers.get('X-API-Key') or request.headers.get('Authorization', '').replace('Bearer ', '')
            if api_key:
                try:
                    from config import API_KEY
                    if api_key == API_KEY:
                        g.user_role = 'admin'
                        g.user_id = 'api_user'
                        return func(*args, **kwargs)
                except ImportError:
                    pass

        # Check plugin-specific admin key
        admin_key = request.headers.get('X-Admin-Key')
        if admin_key:
            # TODO: Validate against plugin configuration
            g.user_role = 'admin'
            g.user_id = 'plugin_admin'
            return func(*args, **kwargs)

        return jsonify({
            'status': 'error',
            'error': 'Admin authentication required',
            'code': 'UNAUTHORIZED'
        }), 401
    return wrapper


def handle_admin_error(func):
    """Decorator to handle admin API errors"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"Admin validation error in {func.__name__}: {e}")
            return jsonify({
                'status': 'error',
                'error': str(e),
                'code': 'VALIDATION_ERROR'
            }), 400
        except Exception as e:
            logger.error(f"Admin error in {func.__name__}: {e}")
            return jsonify({
                'status': 'error',
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }), 500
    return wrapper


def create_admin_blueprint(plugin: 'Plugin') -> Blueprint:
    """Create admin routes blueprint"""
    
    # Specify template folder for plugin templates
    import os
    template_folder = os.path.join(os.path.dirname(__file__), '..', 'templates')
    
    bp = Blueprint('openai_plus_redeem_admin', __name__, 
                   template_folder=template_folder,
                   url_prefix='/admin/openai-plus-redeem')
    
    # ========== Helper Functions ==========
    
    def get_service(service_name: str):
        """Get service from plugin service manager"""
        if not hasattr(plugin, 'service_manager') or not plugin.service_manager:
            raise RuntimeError("Service manager not available")
        
        service = plugin.service_manager.get_service(service_name)
        if not service:
            raise RuntimeError(f"Service {service_name} not available")
        
        return service
    
    def validate_required_params(data: dict, required_fields: list) -> None:
        """Validate required parameters"""
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
    
    # ========== Admin Web Pages ==========
    
    @bp.route('/', methods=['GET'])
    @admin_required
    def admin_dashboard():
        """Admin dashboard page"""
        try:
            return render_template('openai_plus_redeem/admin_dashboard.html')
        except Exception as e:
            logger.error(f"Error rendering admin dashboard: {e}")
            abort(500)
    
    @bp.route('/accounts', methods=['GET'])
    @admin_required
    def account_management():
        """Account management page"""
        try:
            return render_template('openai_plus_redeem/account_management.html')
        except Exception as e:
            logger.error(f"Error rendering account management: {e}")
            abort(500)
    
    @bp.route('/cooldowns', methods=['GET'])
    @admin_required
    def cooldown_management():
        """Cooldown management page"""
        try:
            return render_template('openai_plus_redeem/cooldown_management.html')
        except Exception as e:
            logger.error(f"Error rendering cooldown management: {e}")
            abort(500)
    
    # ========== Admin API Endpoints ==========
    
    @bp.route('/api/dashboard/stats', methods=['GET'])
    @admin_required
    @handle_admin_error
    def get_dashboard_stats():
        """Get dashboard statistics"""
        # Get services
        account_service = get_service('chatgpt_account')
        redemption_service = get_service('order_redemption')
        cooldown_service = get_service('cooldown')
        
        # Get statistics
        account_stats = account_service.get_account_statistics()
        redemption_stats = redemption_service.get_redemption_statistics()
        cooldown_stats = cooldown_service.get_cooldown_statistics()
        
        # Get service health
        service_health = plugin.service_manager.health_check_all_services()
        
        return jsonify({
            'status': 'success',
            'data': {
                'accounts': account_stats,
                'redemptions': redemption_stats,
                'cooldowns': cooldown_stats,
                'services': service_health,
                'timestamp': datetime.now().isoformat()
            }
        })
    
    @bp.route('/api/accounts', methods=['GET'])
    @admin_required
    @handle_admin_error
    def get_accounts():
        """Get all ChatGPT accounts"""
        account_service = get_service('chatgpt_account')
        
        # Get query parameters
        status_filter = request.args.get('status')
        include_expired = request.args.get('include_expired', 'false').lower() == 'true'
        
        accounts = account_service.get_all_accounts(
            status_filter=status_filter,
            include_expired=include_expired
        )
        
        return jsonify({
            'status': 'success',
            'data': accounts,
            'total': len(accounts)
        })
    
    @bp.route('/api/accounts', methods=['POST'])
    @rate_limit(limit=50, window=3600, per='ip')  # 50 account operations per hour
    @admin_required
    @handle_admin_error
    def create_account():
        """Create new ChatGPT account"""
        data = request.get_json() or {}
        
        # Validate required fields
        validate_required_params(data, ['email', 'password', 'expiration_date', 'max_concurrent_users'])
        
        account_service = get_service('chatgpt_account')
        
        # Parse expiration date
        try:
            expiration_date = datetime.fromisoformat(data['expiration_date'].replace('Z', '+00:00'))
        except ValueError:
            raise ValueError("Invalid expiration_date format. Use ISO format.")
        
        # Create account
        account = account_service.create_account(
            email=data['email'],
            password=data['password'],
            expiration_date=expiration_date,
            max_concurrent_users=data['max_concurrent_users']
        )
        
        if account:
            return jsonify({
                'status': 'success',
                'data': account.to_dict(),
                'message': 'Account created successfully'
            }), 201
        else:
            return jsonify({
                'status': 'error',
                'error': 'Failed to create account',
                'code': 'CREATION_FAILED'
            }), 500
    
    @bp.route('/api/accounts/<account_id>', methods=['PUT'])
    @admin_required
    @handle_admin_error
    def update_account(account_id: str):
        """Update ChatGPT account"""
        data = request.get_json() or {}
        
        account_service = get_service('chatgpt_account')
        
        # Parse expiration date if provided
        if 'expiration_date' in data:
            try:
                data['expiration_date'] = datetime.fromisoformat(data['expiration_date'].replace('Z', '+00:00'))
            except ValueError:
                raise ValueError("Invalid expiration_date format. Use ISO format.")
        
        # Update account
        result = account_service.update_account(account_id, data)
        
        if result['success']:
            return jsonify({
                'status': 'success',
                'data': result['account'].to_dict(),
                'message': 'Account updated successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'code': 'UPDATE_FAILED'
            }), 400
    
    @bp.route('/api/accounts/<account_id>', methods=['DELETE'])
    @admin_required
    @handle_admin_error
    def delete_account(account_id: str):
        """Delete ChatGPT account"""
        account_service = get_service('chatgpt_account')
        
        result = account_service.delete_account(account_id)
        
        if result['success']:
            return jsonify({
                'status': 'success',
                'message': 'Account deleted successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'code': 'DELETE_FAILED'
            }), 400
    
    @bp.route('/api/cooldowns', methods=['GET'])
    @admin_required
    @handle_admin_error
    def get_active_cooldowns():
        """Get all active cooldowns"""
        cooldown_service = get_service('cooldown')
        
        cooldowns = cooldown_service.get_all_active_cooldowns()
        
        return jsonify({
            'status': 'success',
            'data': cooldowns,
            'total': len(cooldowns)
        })
    
    @bp.route('/api/cooldowns', methods=['POST'])
    @rate_limit(limit=100, window=3600, per='ip')  # 100 cooldown operations per hour
    @admin_required
    @handle_admin_error
    def set_user_cooldown():
        """Set cooldown for user"""
        data = request.get_json() or {}
        
        # Validate required fields
        validate_required_params(data, ['username', 'hours'])
        
        cooldown_service = get_service('cooldown')
        
        # Import cooldown type
        from ..models.account_cooldown import CooldownType
        
        # Parse cooldown type
        cooldown_type = CooldownType.REDEMPTION
        if 'cooldown_type' in data:
            try:
                cooldown_type = CooldownType(data['cooldown_type'])
            except ValueError:
                raise ValueError(f"Invalid cooldown_type: {data['cooldown_type']}")
        
        # Set cooldown
        result = cooldown_service.set_user_cooldown(
            username=data['username'],
            hours=data['hours'],
            cooldown_type=cooldown_type,
            reason=data.get('reason', 'Admin action')
        )
        
        if result['success']:
            return jsonify({
                'status': 'success',
                'data': result,
                'message': f'Cooldown set for {data["hours"]} hours'
            }), 201
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'code': 'COOLDOWN_FAILED'
            }), 400
    
    @bp.route('/api/cooldowns/<username>', methods=['DELETE'])
    @admin_required
    @handle_admin_error
    def remove_user_cooldown(username: str):
        """Remove user cooldown"""
        cooldown_service = get_service('cooldown')
        
        # Get query parameters
        admin_override = request.args.get('admin_override', 'false').lower() == 'true'
        
        result = cooldown_service.remove_user_cooldown(
            username=username,
            admin_override=admin_override
        )
        
        if result['success']:
            return jsonify({
                'status': 'success',
                'data': result,
                'message': f'Cooldown removed for user {username}'
            })
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'code': 'COOLDOWN_REMOVAL_FAILED'
            }), 400
    
    @bp.route('/api/redemptions/<redemption_id>/assign', methods=['POST'])
    @admin_required
    @handle_admin_error
    def assign_account_to_redemption(redemption_id: str):
        """Assign account to redemption"""
        data = request.get_json() or {}
        
        order_redeem_service = get_service('order_redeem')
        
        # Assign account (with optional specific account ID)
        result = order_redeem_service.assign_account_to_redemption(
            redemption_id=redemption_id,
            account_id=data.get('account_id')
        )
        
        if result['success']:
            return jsonify({
                'status': 'success',
                'data': result,
                'message': 'Account assigned successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'code': 'ASSIGNMENT_FAILED'
            }), 400
    
    @bp.route('/api/redemptions/<redemption_id>/release', methods=['POST'])
    @admin_required
    @handle_admin_error
    def release_redemption(redemption_id: str):
        """Release redemption and free account"""
        data = request.get_json() or {}
        
        order_redeem_service = get_service('order_redeem')
        
        result = order_redeem_service.release_redemption(
            redemption_id=redemption_id,
            reason=data.get('reason', 'Admin action')
        )
        
        if result['success']:
            return jsonify({
                'status': 'success',
                'data': result,
                'message': 'Redemption released successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'code': 'RELEASE_FAILED'
            }), 400
    
    @bp.route('/api/services/health', methods=['GET'])
    @admin_required
    @handle_admin_error
    def get_services_health():
        """Get health status of all services"""
        health = plugin.service_manager.health_check_all_services()
        
        return jsonify({
            'status': 'success',
            'data': health
        })
    
    @bp.route('/api/services/<service_name>/restart', methods=['POST'])
    @admin_required
    @handle_admin_error
    def restart_service(service_name: str):
        """Restart a specific service"""
        result = plugin.service_manager.restart_service(service_name)
        
        if result:
            return jsonify({
                'status': 'success',
                'message': f'Service {service_name} restarted successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'error': f'Failed to restart service {service_name}',
                'code': 'RESTART_FAILED'
            }), 500
    
    @bp.route('/api/cleanup/expired', methods=['POST'])
    @admin_required
    @handle_admin_error
    def cleanup_expired_data():
        """Cleanup expired data across all services"""
        results = {}
        
        # Cleanup expired accounts
        try:
            account_service = get_service('chatgpt_account')
            account_result = account_service.cleanup_expired_accounts()
            results['accounts'] = account_result
        except Exception as e:
            results['accounts'] = {'error': str(e)}
        
        # Cleanup expired cooldowns
        try:
            cooldown_service = get_service('cooldown')
            cooldown_result = cooldown_service.cleanup_expired_cooldowns()
            results['cooldowns'] = cooldown_result
        except Exception as e:
            results['cooldowns'] = {'error': str(e)}
        
        return jsonify({
            'status': 'success',
            'data': results,
            'message': 'Cleanup completed'
        })
    
    # ========== Error Handlers ==========
    
    @bp.errorhandler(401)
    def unauthorized(error):
        """Handle unauthorized errors"""
        return jsonify({
            'status': 'error',
            'error': 'Unauthorized access',
            'code': 'UNAUTHORIZED'
        }), 401
    
    @bp.errorhandler(403)
    def forbidden(error):
        """Handle forbidden errors"""
        return jsonify({
            'status': 'error',
            'error': 'Access forbidden',
            'code': 'FORBIDDEN'
        }), 403
    
    @bp.errorhandler(400)
    def bad_request(error):
        """Handle bad request errors"""
        return jsonify({
            'status': 'error',
            'error': 'Bad request',
            'code': 'BAD_REQUEST',
            'details': str(error.description) if hasattr(error, 'description') else None
        }), 400
    
    @bp.errorhandler(404)
    def not_found(error):
        """Handle not found errors"""
        return jsonify({
            'status': 'error',
            'error': 'Resource not found',
            'code': 'NOT_FOUND'
        }), 404
    
    @bp.errorhandler(500)
    def internal_error(error):
        """Handle internal server errors"""
        return jsonify({
            'status': 'error',
            'error': 'Internal server error',
            'code': 'INTERNAL_ERROR'
        }), 500
    
    return bp
