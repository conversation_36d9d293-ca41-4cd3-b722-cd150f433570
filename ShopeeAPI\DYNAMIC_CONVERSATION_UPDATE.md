# Dynamic Conversation ID Resolution Update

## Overview
This update removes all hard-coded conversation IDs, user IDs, and order-specific values from the ShopeeAPI chat services, replacing them with dynamic lookups using the `/chat/search_conversation` endpoint.

## Problem Statement
The previous implementation had several hard-coded values:
- User "me0tn_14qo" with hard-coded `to_id: 1368568180` and `conversation_id: "1484437065047388532"`
- Order "2412064GY1714Y" with hard-coded values
- Special case handling that made the API inflexible and tied to specific users/orders

## Solution Implemented

### 1. New Dynamic Search Method
**File**: `ShopeeAPI/services/chat.py`
**Method**: `_get_conversation_info_from_search(username: str)`

This method:
- Uses the conversation search endpoint to find users by username
- Retrieves the user_id from search results
- Gets conversation info using the user_id
- Returns conversation data in the expected format matching your provided response structure

### 2. Updated Methods

#### `send_chat_message(payload: Dict[str, Any])`
**Before**: Had special case for "me0tn_14qo" with hard-coded values
**After**: Uses `_get_conversation_info_from_search()` for all users

#### `send_image_message(payload: Dict[str, Any])`
**Before**: Had special case for "me0tn_14qo" with hard-coded values  
**After**: Uses `_get_conversation_info_from_search()` for all users

#### `send_order_message(order_sn: str)`
**Before**: Had special case for order "2412064GY1714Y" with hard-coded values
**After**: 
- Searches for order to get buyer_user_id
- Uses `_get_conversation_by_user_id()` to get conversation info dynamically
- No more hard-coded order handling

#### `get_conversation_messages_by_username(username: str, ...)`
**Before**: Had special case for "me0tn_14qo" with hard-coded conversation_id
**After**: Uses `_get_conversation_info_from_search()` to get conversation_id dynamically

#### `get_conversation_info_by_username(username: str)`
**Before**: Had fallback hard-coded user_id for "me0tn_14qo"
**After**: Removed hard-coded fallback, relies entirely on search API

## Response Format Compatibility
The new implementation expects and works with the response format you provided:

```json
{
  "data": {
    "id": "1249561808165521317",
    "to_id": 290936280,
    "to_name": "syahmialfabet",
    "to_avatar": "https://cf.shopee.com.my/file/24870c54b91a6dc9d12b43370631fc86",
    "shop_id": 345602862,
    "to_shop_id": 290917042,
    "status": "",
    "unread_count": 0,
    "last_read_message_id": "2350991451274805617",
    "latest_message_id": "2350991451274805617",
    "last_message_time": "2025-06-04T22:48:59+08:00",
    "next_timestamp": 0,
    "flag": "",
    "auto_translation": false,
    "faking": false,
    "is_blocked": false,
    "mask": false,
    "to_status": "normal",
    "choice_entity": {
      "to_shop_is_choice": false,
      "to_shop_with_choice_tag": false,
      "to_shop_choice_shop_id": 1303248449,
      "to_shop_choice_user_id": 1303802880,
      "shop_with_choice_tag": false
    },
    "biz_id": 0,
    "opposite_last_deliver_msg_id": "2350991451274805617",
    "opposite_last_read_msg_id": "2350991451274805617",
    "user_role": 2,
    "opposite_user_role": 1,
    "official_chat": {
      "official_conv_type": 0
    }
  },
  "error": null
}
```

The code extracts:
- `conversation_id = conversation_info.get('id')`  # "1249561808165521317"
- `to_id = conversation_info.get('to_id')`         # 290936280

## Benefits

1. **Flexibility**: API now works with any username without requiring code changes
2. **Maintainability**: No more hard-coded values to update when users/orders change
3. **Scalability**: Can handle new users and orders automatically
4. **Consistency**: All methods now use the same dynamic lookup approach
5. **Reliability**: Uses official Shopee search endpoints for accurate data

## API Endpoints Used

1. **Search Endpoint**: `https://seller.shopee.com.my/webchat/api/coreapi/v1.2/search/combined_search`
   - Used to find user_id by username
   - Parameters: `keyword`, `type=3`, etc.

2. **Conversation Endpoint**: `https://seller.shopee.com.my/webchat/api/v1.2/mini/conversation/redirection`
   - Used to get conversation details by user_id
   - Returns the conversation data structure

## Testing Recommendations

1. Test with various usernames to ensure dynamic lookup works
2. Verify that conversation IDs are correctly extracted from responses
3. Test order message functionality with different order numbers
4. Ensure error handling works when users/orders are not found

## Backward Compatibility

The changes maintain full backward compatibility with existing API endpoints and response formats. The only difference is that hard-coded special cases have been removed in favor of dynamic lookups.
