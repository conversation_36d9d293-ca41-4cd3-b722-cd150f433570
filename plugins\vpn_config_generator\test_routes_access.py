#!/usr/bin/env python3
"""
Test script to verify all VPN redemption links routes are accessible
"""

import requests
import sys
import time

def test_route_access():
    """Test if all redemption link routes are accessible"""
    base_url = "http://localhost:5000"
    
    # Routes to test
    routes_to_test = [
        # Main redemption links admin
        "/vpn-config-generator/admin/redemption-links",
        
        # Bulk creation
        "/vpn-config-generator/admin/redemption-links/bulk",
        
        # Analytics
        "/vpn-config-generator/admin/redemption-links/analytics",
        
        # API endpoints (these might return 401 without auth, but should not 404)
        "/vpn-config-generator/api/redemption-links",
        "/vpn-config-generator/api/chat-template-config",
        "/vpn-config-generator/api/redemption-links/analytics",
    ]
    
    print("🚀 Testing VPN Redemption Links Route Access\n")
    
    results = []
    
    for route in routes_to_test:
        url = f"{base_url}{route}"
        print(f"Testing: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            status_code = response.status_code
            
            if status_code == 200:
                print(f"✅ {route} - OK (200)")
                results.append((route, "OK", status_code))
            elif status_code == 401:
                print(f"🔐 {route} - Requires Auth (401)")
                results.append((route, "AUTH_REQUIRED", status_code))
            elif status_code == 404:
                print(f"❌ {route} - Not Found (404)")
                results.append((route, "NOT_FOUND", status_code))
            elif status_code == 500:
                print(f"⚠️ {route} - Server Error (500)")
                results.append((route, "SERVER_ERROR", status_code))
            else:
                print(f"⚠️ {route} - Unexpected Status ({status_code})")
                results.append((route, "UNEXPECTED", status_code))
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {route} - Connection Failed (Server not running?)")
            results.append((route, "CONNECTION_ERROR", None))
        except requests.exceptions.Timeout:
            print(f"❌ {route} - Timeout")
            results.append((route, "TIMEOUT", None))
        except Exception as e:
            print(f"❌ {route} - Error: {e}")
            results.append((route, "ERROR", None))
        
        time.sleep(0.5)  # Small delay between requests
    
    print("\n" + "="*60)
    print("📊 SUMMARY")
    print("="*60)
    
    ok_count = sum(1 for _, status, _ in results if status == "OK")
    auth_count = sum(1 for _, status, _ in results if status == "AUTH_REQUIRED")
    error_count = sum(1 for _, status, _ in results if status in ["NOT_FOUND", "CONNECTION_ERROR", "TIMEOUT", "ERROR"])
    
    print(f"✅ Working routes: {ok_count}")
    print(f"🔐 Auth required: {auth_count}")
    print(f"❌ Error routes: {error_count}")
    
    if error_count == 0:
        print("\n🎉 All routes are accessible!")
        if auth_count > 0:
            print("💡 Some routes require authentication, which is expected for admin interfaces.")
    else:
        print(f"\n⚠️ {error_count} routes have issues. Check server logs for details.")
    
    print("\n📋 Detailed Results:")
    for route, status, code in results:
        status_emoji = {
            "OK": "✅",
            "AUTH_REQUIRED": "🔐",
            "NOT_FOUND": "❌",
            "CONNECTION_ERROR": "❌",
            "TIMEOUT": "❌",
            "ERROR": "❌",
            "SERVER_ERROR": "⚠️",
            "UNEXPECTED": "⚠️"
        }.get(status, "❓")
        
        code_str = f"({code})" if code else ""
        print(f"  {status_emoji} {route} - {status} {code_str}")

def test_menu_access():
    """Test if the menu items are accessible"""
    print("\n" + "="*60)
    print("🔗 MENU ACCESS TEST")
    print("="*60)
    
    menu_items = [
        ("Redemption Links", "/vpn-config-generator/admin/redemption-links"),
        ("Bulk Creation", "/vpn-config-generator/admin/redemption-links/bulk"),
        ("Analytics", "/vpn-config-generator/admin/redemption-links/analytics")
    ]
    
    print("The following menu items should now be visible in the VPN Config Generator section:")
    for name, url in menu_items:
        print(f"  📋 {name}: {url}")
    
    print("\n💡 To access these features:")
    print("1. Start your application server")
    print("2. Navigate to the admin interface")
    print("3. Look for 'VPN Config Generator' in the sidebar")
    print("4. Expand the section to see the new menu items")

def main():
    """Run all tests"""
    try:
        test_route_access()
        test_menu_access()
        
        print("\n" + "="*60)
        print("🎯 NEXT STEPS")
        print("="*60)
        print("1. Ensure your application server is running")
        print("2. Check that the VPN Config Generator plugin is enabled")
        print("3. Navigate to the admin interface and look for the new menu items")
        print("4. Test the functionality by creating redemption links")
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
