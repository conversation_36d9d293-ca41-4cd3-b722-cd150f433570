"""
Order Redemption Service

Service for managing order redemptions including processing logic,
status management, and integration with Shopee API.
"""

import os
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from .base_service import BaseService
from ..models.order_redemption import OrderRedemption, RedemptionStatus
from ..models.utils import (
    load_json_data, save_json_data, validate_username, validate_sku,
    DataPersistenceError, ValidationError, get_data_file_path
)


class OrderRedemptionService(BaseService):
    """
    Service for managing order redemptions
    
    Provides functionality for:
    - Order redemption processing
    - Status management and transitions
    - Cooldown handling
    - Integration with Shopee API
    - Data persistence
    """
    
    def __init__(self, config: Dict[str, Any], logger=None):
        super().__init__(config, logger)
        self.service_name = "OrderRedemptionService"
        
        # Data file path
        self.data_file = get_data_file_path('openai_plus_redeem', 'order_redemptions.json')
        
        # In-memory cache
        self._redemptions_cache: Dict[str, OrderRedemption] = {}
        self._cache_loaded = False
        
        # Configuration
        self.default_cooldown_hours = self._get_config_value('cooldown_config.default_hours', 24)
        self.max_retry_attempts = self._get_config_value('redemption_config.max_retry_attempts', 3)
        self.auto_expire_enabled = self._get_config_value('auto_expire_enabled', True)
        self.shopee_integration_enabled = self._get_config_value('shopee_config.enabled', True)
    
    def initialize(self) -> bool:
        """Initialize the Order Redemption Service"""
        try:
            self.logger.info(f"Initializing {self.service_name}...")
            
            # Validate configuration
            if not self._validate_service_config():
                return False
            
            # Load redemptions from storage
            if not self._load_redemptions():
                self.logger.warning("Failed to load redemptions, starting with empty cache")
                self._redemptions_cache = {}
            
            self._cache_loaded = True
            
            # Perform initial cleanup if enabled
            if self.auto_expire_enabled:
                self._cleanup_expired_redemptions()
            
            self._mark_initialized()
            return True
            
        except Exception as e:
            self._handle_service_error("initialize", e)
            return False
    
    def shutdown(self) -> bool:
        """Shutdown the Order Redemption Service"""
        try:
            self.logger.info(f"Shutting down {self.service_name}...")
            
            # Save any pending changes
            if self._cache_loaded:
                self._save_redemptions()
            
            # Clear cache
            self._redemptions_cache.clear()
            self._cache_loaded = False
            
            self._mark_shutdown()
            return True
            
        except Exception as e:
            self._handle_service_error("shutdown", e)
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        self._update_health_check_time()
        
        try:
            # Basic health indicators
            health_data = {
                'status': 'healthy',
                'service': self.service_name,
                'cache_loaded': self._cache_loaded,
                'total_redemptions': len(self._redemptions_cache),
                'data_file_exists': os.path.exists(self.data_file),
                'timestamp': datetime.now().isoformat()
            }
            
            # Redemption statistics
            if self._cache_loaded:
                status_counts = {}
                for status in RedemptionStatus:
                    count = sum(1 for r in self._redemptions_cache.values() if r.status == status)
                    status_counts[status.value] = count
                
                health_data.update({
                    'status_counts': status_counts,
                    'active_redemptions': status_counts.get('ACTIVE', 0),
                    'pending_redemptions': status_counts.get('PENDING', 0),
                    'cooldown_redemptions': status_counts.get('COOLDOWN', 0)
                })
            
            # Check for issues
            issues = []
            if not self._cache_loaded:
                issues.append("Cache not loaded")
            if not os.path.exists(self.data_file):
                issues.append("Data file missing")
            
            if issues:
                health_data['status'] = 'degraded'
                health_data['issues'] = issues
            
            return health_data
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': self.service_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def create_redemption_from_order(self, order_id: str, buyer_username: str, 
                                   sku: str, var_sku: str) -> Optional[OrderRedemption]:
        """
        Create a new redemption from order data
        
        Args:
            order_id: Shopee order ID
            buyer_username: Buyer's username
            sku: Product SKU
            var_sku: Product variant SKU
            
        Returns:
            Created OrderRedemption or None if creation failed
        """
        try:
            # Validate inputs
            if not validate_username(buyer_username):
                raise ValidationError(f"Invalid username: {buyer_username}")
            
            if not validate_sku(sku):
                raise ValidationError(f"Invalid SKU: {sku}")
            
            # Check for duplicate order
            if self.get_redemption_by_order_id(order_id):
                raise ValidationError(f"Redemption for order {order_id} already exists")
            
            # Create redemption
            redemption = OrderRedemption.create_from_order(
                order_id=order_id,
                buyer_username=buyer_username,
                sku=sku,
                var_sku=var_sku
            )
            
            # Add to cache
            self._redemptions_cache[redemption.redemption_id] = redemption
            
            # Save to storage
            self._save_redemptions()
            
            self._log_operation("create_redemption", {
                'redemption_id': redemption.redemption_id,
                'order_id': order_id,
                'buyer_username': buyer_username,
                'sku': sku,
                'var_sku': var_sku
            })
            
            return redemption
            
        except Exception as e:
            self._handle_service_error("create_redemption_from_order", e)
            return None
    
    def get_redemption(self, redemption_id: str) -> Optional[OrderRedemption]:
        """
        Get redemption by ID
        
        Args:
            redemption_id: Redemption ID
            
        Returns:
            OrderRedemption or None if not found
        """
        return self._redemptions_cache.get(redemption_id)
    
    def get_redemption_by_order_id(self, order_id: str) -> Optional[OrderRedemption]:
        """
        Get redemption by order ID
        
        Args:
            order_id: Order ID
            
        Returns:
            OrderRedemption or None if not found
        """
        for redemption in self._redemptions_cache.values():
            if redemption.order_id == order_id:
                return redemption
        return None
    
    def get_redemptions_by_user(self, buyer_username: str) -> List[OrderRedemption]:
        """
        Get all redemptions for a user
        
        Args:
            buyer_username: Buyer's username
            
        Returns:
            List of OrderRedemption objects
        """
        return [
            redemption for redemption in self._redemptions_cache.values()
            if redemption.buyer_username.lower() == buyer_username.lower()
        ]
    
    def get_redemptions_by_status(self, status: RedemptionStatus) -> List[OrderRedemption]:
        """
        Get redemptions by status
        
        Args:
            status: Redemption status
            
        Returns:
            List of OrderRedemption objects
        """
        return [
            redemption for redemption in self._redemptions_cache.values()
            if redemption.status == status
        ]
    
    def get_all_redemptions(self) -> List[OrderRedemption]:
        """
        Get all redemptions
        
        Returns:
            List of all OrderRedemption objects
        """
        return list(self._redemptions_cache.values())
    
    def activate_redemption(self, redemption_id: str, account_id: str) -> bool:
        """
        Activate a redemption by assigning an account
        
        Args:
            redemption_id: Redemption ID
            account_id: ChatGPT account ID to assign
            
        Returns:
            True if activation was successful
        """
        try:
            redemption = self.get_redemption(redemption_id)
            if not redemption:
                self.logger.warning(f"Redemption not found: {redemption_id}")
                return False
            
            if not redemption.can_redeem():
                self.logger.warning(f"Redemption cannot be activated: {redemption_id}")
                return False
            
            # Activate redemption
            if redemption.activate(account_id):
                self._save_redemptions()
                
                self._log_operation("activate_redemption", {
                    'redemption_id': redemption_id,
                    'account_id': account_id,
                    'buyer_username': redemption.buyer_username
                })
                
                return True
            
            return False
            
        except Exception as e:
            self._handle_service_error("activate_redemption", e)
            return False
    
    def set_redemption_cooldown(self, redemption_id: str, hours: int = None) -> bool:
        """
        Set redemption to cooldown status
        
        Args:
            redemption_id: Redemption ID
            hours: Cooldown duration in hours (uses default if not provided)
            
        Returns:
            True if cooldown was set successfully
        """
        try:
            redemption = self.get_redemption(redemption_id)
            if not redemption:
                return False
            
            cooldown_hours = hours or self.default_cooldown_hours
            redemption.set_cooldown(cooldown_hours)
            
            self._save_redemptions()
            
            self._log_operation("set_cooldown", {
                'redemption_id': redemption_id,
                'cooldown_hours': cooldown_hours,
                'buyer_username': redemption.buyer_username
            })
            
            return True
            
        except Exception as e:
            self._handle_service_error("set_redemption_cooldown", e)
            return False
    
    def clear_redemption_cooldown(self, redemption_id: str) -> bool:
        """
        Clear redemption cooldown
        
        Args:
            redemption_id: Redemption ID
            
        Returns:
            True if cooldown was cleared successfully
        """
        try:
            redemption = self.get_redemption(redemption_id)
            if not redemption:
                return False
            
            redemption.clear_cooldown()
            self._save_redemptions()
            
            self._log_operation("clear_cooldown", {
                'redemption_id': redemption_id,
                'buyer_username': redemption.buyer_username
            })
            
            return True
            
        except Exception as e:
            self._handle_service_error("clear_redemption_cooldown", e)
            return False
    
    def expire_redemption(self, redemption_id: str) -> bool:
        """
        Expire a redemption
        
        Args:
            redemption_id: Redemption ID
            
        Returns:
            True if expiration was successful
        """
        try:
            redemption = self.get_redemption(redemption_id)
            if not redemption:
                return False
            
            redemption.expire()
            self._save_redemptions()
            
            self._log_operation("expire_redemption", {
                'redemption_id': redemption_id,
                'buyer_username': redemption.buyer_username
            })
            
            return True
            
        except Exception as e:
            self._handle_service_error("expire_redemption", e)
            return False
    
    def cancel_redemption(self, redemption_id: str, reason: str = "") -> bool:
        """
        Cancel a redemption
        
        Args:
            redemption_id: Redemption ID
            reason: Cancellation reason
            
        Returns:
            True if cancellation was successful
        """
        try:
            redemption = self.get_redemption(redemption_id)
            if not redemption:
                return False
            
            redemption.cancel(reason)
            self._save_redemptions()
            
            self._log_operation("cancel_redemption", {
                'redemption_id': redemption_id,
                'reason': reason,
                'buyer_username': redemption.buyer_username
            })
            
            return True
            
        except Exception as e:
            self._handle_service_error("cancel_redemption", e)
            return False
    
    def set_redemption_error(self, redemption_id: str, error_message: str) -> bool:
        """
        Set redemption to error status
        
        Args:
            redemption_id: Redemption ID
            error_message: Error message
            
        Returns:
            True if error was set successfully
        """
        try:
            redemption = self.get_redemption(redemption_id)
            if not redemption:
                return False
            
            redemption.set_error(error_message)
            self._save_redemptions()
            
            self._log_operation("set_error", {
                'redemption_id': redemption_id,
                'error_message': error_message,
                'buyer_username': redemption.buyer_username
            })
            
            return True
            
        except Exception as e:
            self._handle_service_error("set_redemption_error", e)
            return False
    
    def get_user_cooldown_status(self, buyer_username: str) -> Dict[str, Any]:
        """
        Get cooldown status for a user
        
        Args:
            buyer_username: Buyer's username
            
        Returns:
            Dictionary with cooldown status information
        """
        user_redemptions = self.get_redemptions_by_user(buyer_username)
        
        # Find active cooldowns
        cooldown_redemptions = [r for r in user_redemptions if r.is_in_cooldown()]
        
        if not cooldown_redemptions:
            return {
                'has_cooldown': False,
                'can_redeem': True
            }
        
        # Get the cooldown with the longest remaining time
        longest_cooldown = max(cooldown_redemptions, 
                             key=lambda r: r.get_cooldown_remaining_time().total_seconds())
        
        remaining_time = longest_cooldown.get_cooldown_remaining_time()
        
        return {
            'has_cooldown': True,
            'can_redeem': False,
            'remaining_seconds': int(remaining_time.total_seconds()),
            'remaining_hours': remaining_time.total_seconds() / 3600,
            'cooldown_until': longest_cooldown.cooldown_until,
            'redemption_id': longest_cooldown.redemption_id
        }
    
    def _validate_service_config(self) -> bool:
        """Validate service configuration"""
        # Check required configuration
        required_keys = []  # No required keys for basic functionality
        return self._validate_config(required_keys)
    
    def _load_redemptions(self) -> bool:
        """Load redemptions from storage"""
        try:
            data = load_json_data(self.data_file)
            redemptions_data = data.get('redemptions', [])
            
            self._redemptions_cache.clear()
            
            for redemption_data in redemptions_data:
                try:
                    redemption = OrderRedemption.from_dict(redemption_data)
                    self._redemptions_cache[redemption.redemption_id] = redemption
                except Exception as e:
                    self.logger.warning(f"Failed to load redemption: {e}")
            
            self.logger.info(f"Loaded {len(self._redemptions_cache)} redemptions from storage")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load redemptions: {e}")
            return False
    
    def _save_redemptions(self) -> bool:
        """Save redemptions to storage"""
        try:
            redemptions_data = [redemption.to_dict() for redemption in self._redemptions_cache.values()]
            
            data = {
                'redemptions': redemptions_data,
                'metadata': {
                    'version': '1.0.0',
                    'total_redemptions': len(redemptions_data),
                    'last_updated': datetime.now().isoformat()
                }
            }
            
            save_json_data(self.data_file, data)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save redemptions: {e}")
            return False
    
    def _cleanup_expired_redemptions(self) -> int:
        """
        Clean up expired redemptions
        
        Returns:
            Number of redemptions cleaned up
        """
        try:
            expired_count = 0
            
            for redemption in self._redemptions_cache.values():
                if redemption.is_expired() and redemption.status != RedemptionStatus.EXPIRED:
                    redemption.expire()
                    expired_count += 1
            
            if expired_count > 0:
                self._save_redemptions()
                self.logger.info(f"Expired {expired_count} redemptions")
            
            return expired_count
            
        except Exception as e:
            self.logger.error(f"Error during redemption cleanup: {e}")
            return 0
