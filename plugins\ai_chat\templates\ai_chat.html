{% extends "base.html" %}

{% block title %}AI Chat Settings{% endblock %}
{% block header %}AI Chat Settings{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="aiChatData()" x-init="init()">
    <div class="bg-white shadow rounded-lg p-6" x-show="isLoaded">
        <!-- Enable/Disable Toggle -->
        <div class="mb-6">
            <div class="flex items-center">
                <button 
                    @click="toggleStatus"
                    :class="{'bg-green-500': config.AI_REPLY_ENABLED, 'bg-gray-300': !config.AI_REPLY_ENABLED}"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out">
                    <span 
                        :class="{'translate-x-5': config.AI_REPLY_ENABLED, 'translate-x-0': !config.AI_REPLY_ENABLED}"
                        class="inline-block h-5 w-5 transform rounded-full bg-white shadow transition duration-200 ease-in-out">
                    </span>
                </button>
                <span class="ml-3 text-sm font-medium text-gray-900">
                    <span x-text="config.AI_REPLY_ENABLED ? 'AI Chat Enabled' : 'AI Chat Disabled'"></span>
                </span>
            </div>
        </div>

        <!-- API Key Setting -->
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700">DeepSeek API Key</label>
            <div class="mt-1 relative rounded-md shadow-sm">
                <input 
                    type="password" 
                    x-model="config.DEEPSEEK_API_KEY"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxx">
                <button 
                    @click="toggleApiKeyVisibility"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5">
                    <svg x-show="!showApiKey" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <svg x-show="showApiKey" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                    </svg>
                </button>
            </div>
            <p class="mt-1 text-sm text-gray-500">Your DeepSeek API key for authentication</p>
        </div>

        <!-- AI Chat Settings -->
        <div x-show="config.AI_REPLY_ENABLED">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">System Prompt</label>
                <textarea 
                    x-model="config.AI_SYSTEM_PROMPT"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    rows="8">
                </textarea>
                <p class="mt-1 text-sm text-gray-500">Define the AI's behavior and response format</p>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Temperature</label>
                <input 
                    type="number" 
                    step="0.1"
                    min="0"
                    max="2"
                    x-model.number="config.AI_TEMPERATURE"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-sm text-gray-500">
                    Controls randomness in responses:<br>
                    0.0 - Most focused and deterministic<br>
                    1.0 - Balanced creativity<br>
                    2.0 - Most creative and random
                </p>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Cooldown Period (minutes)</label>
                <input 
                    type="number" 
                    x-model.number="config.AI_REPLY_COOLDOWN_MINUTES"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-sm text-gray-500">Minimum time between AI replies to the same customer</p>
            </div>
        </div>

        <!-- Save Button -->
        <div class="mt-4">
            <button 
                @click="saveConfig"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Settings
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function aiChatData() {
    return {
        config: {},
        isLoaded: false,
        showApiKey: false,
        init() {
            this.loadConfig();
        },
        loadConfig() {
            fetch('/api/ai_chat/admin/get_ai_config')
                .then(response => response.json())
                .then(data => {
                    this.config = data;
                    this.isLoaded = true;
                })
                .catch(error => {
                    console.error('Error loading config:', error);
                    alert('Failed to load configuration');
                });
        },
        toggleStatus() {
            this.config.AI_REPLY_ENABLED = !this.config.AI_REPLY_ENABLED;
        },
        toggleApiKeyVisibility() {
            this.showApiKey = !this.showApiKey;
            const input = document.querySelector('input[type="password"]');
            input.type = this.showApiKey ? 'text' : 'password';
        },
        saveConfig() {
            fetch('/api/ai_chat/admin/update_ai_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.config),
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to save configuration');
            });
        }
    }
}
</script>
{% endblock %}
