#!/usr/bin/env python3
"""
Test script to identify what service is running on the specified port
"""

import socket
import sys

def test_service_banner(host: str, port: int) -> dict:
    """Test what service is running on the port by reading the banner"""
    try:
        print(f"Connecting to {host}:{port} to read service banner...")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((host, port))
        
        # Try to read the banner/greeting
        try:
            banner = sock.recv(1024).decode('utf-8', errors='ignore')
            print(f"Service banner: {repr(banner)}")
            
            # Check if it looks like SSH
            if 'SSH' in banner:
                return {
                    'success': True,
                    'service': 'SSH',
                    'banner': banner.strip(),
                    'message': 'SSH service detected'
                }
            elif 'HTTP' in banner:
                return {
                    'success': True,
                    'service': 'HTTP',
                    'banner': banner.strip(),
                    'message': 'HTTP service detected'
                }
            else:
                return {
                    'success': True,
                    'service': 'Unknown',
                    'banner': banner.strip(),
                    'message': f'Unknown service, banner: {banner.strip()}'
                }
        except socket.timeout:
            # No banner received, try sending SSH greeting
            print("No banner received, trying to send SSH greeting...")
            sock.send(b'SSH-2.0-TestClient\r\n')
            response = sock.recv(1024).decode('utf-8', errors='ignore')
            print(f"Response to SSH greeting: {repr(response)}")
            
            return {
                'success': True,
                'service': 'No banner',
                'banner': response.strip() if response else 'No response',
                'message': 'Service does not send banner'
            }
        
    except ConnectionResetError:
        return {
            'success': False,
            'service': 'Connection reset',
            'message': 'Connection was reset by remote host'
        }
    except socket.timeout:
        return {
            'success': False,
            'service': 'Timeout',
            'message': 'Connection timed out'
        }
    except Exception as e:
        return {
            'success': False,
            'service': 'Error',
            'message': f'Error: {str(e)}'
        }
    finally:
        try:
            sock.close()
        except:
            pass

def test_common_ssh_ports(host: str) -> dict:
    """Test common SSH ports"""
    common_ports = [22, 2222, 20203, 443, 80]
    results = {}
    
    for port in common_ports:
        print(f"\nTesting port {port}...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"Port {port} is open")
                service_info = test_service_banner(host, port)
                results[port] = service_info
            else:
                print(f"Port {port} is closed or filtered")
                results[port] = {'success': False, 'message': 'Port closed'}
        except Exception as e:
            print(f"Error testing port {port}: {e}")
            results[port] = {'success': False, 'message': f'Error: {str(e)}'}
    
    return results

def main():
    """Main test function"""
    host = "server2.online-mtyb.com"
    port = 20203
    
    print("=== Port Service Detection Test ===")
    print(f"Target: {host}:{port}")
    print()
    
    # Test the specific port
    print(f"1. Testing service on port {port}...")
    service_result = test_service_banner(host, port)
    print(f"Result: {service_result}")
    print()
    
    # Test common SSH ports
    print("2. Testing common SSH ports...")
    port_results = test_common_ssh_ports(host)
    
    print("\n=== Summary ===")
    for port, result in port_results.items():
        if result.get('success'):
            service = result.get('service', 'Unknown')
            print(f"Port {port}: {service} - {result.get('message', '')}")
        else:
            print(f"Port {port}: {result.get('message', 'Failed')}")

if __name__ == "__main__":
    main()
