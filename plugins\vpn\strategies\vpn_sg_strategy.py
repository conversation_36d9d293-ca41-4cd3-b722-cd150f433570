"""
VPN Singapore Strategy
Strategy for Singapore VPN products (sg_*) that require:
- Creation only on Digital Ocean servers
- Single server selection
- Singapore-specific configuration
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

import logging
from typing import Dict, List, Any
from .vpn_base_strategy import VPNBaseStrategy
from .strategy_factory import VPNStrategyFactory

logger = logging.getLogger(__name__)

class VPNSingaporeStrategy(VPNBaseStrategy):
    """
    Strategy for Singapore VPN products (sg_*).
    Creates users only on Digital Ocean servers.
    """
    
    def get_target_servers(self, product_sku: str) -> List[Dict[str, Any]]:
        """
        Get servers for Singapore products based on server tags.
        Uses the strategy factory to determine which server tags to use.
        """
        try:
            # Get required server tags for this SKU
            required_tags = VPNStrategyFactory.get_server_tags_for_sku(product_sku)
            logger.info(f"Singapore strategy: Using server tags {required_tags} for SKU {product_sku}")

            # Get servers filtered by tags
            target_servers = self._get_servers_by_tags(required_tags)

            logger.info(f"Singapore strategy: Found {len(target_servers)} servers for {product_sku}")
            for server in target_servers:
                logger.info(f"  - {server.get('name', 'Unknown')} (tags: {server.get('tags', [])})")

            if not target_servers:
                logger.warning(f"No servers found with tags {required_tags} for {product_sku}. This may cause issues.")

            return target_servers

        except Exception as e:
            logger.error(f"Error getting servers for {product_sku}: {str(e)}")
            return []
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create VPN user on the first available Digital Ocean server.
        """
        try:
            if not servers:
                return {
                    "status": "error",
                    "message": "No Digital Ocean servers available for Singapore region"
                }
            
            # Use the first available server
            selected_server = servers[0]
            
            logger.info(f"Creating Singapore VPN user on server: {selected_server['name']}")
            
            result = self.vpn_service.create_client(
                server_id=selected_server['id'],
                email=user_data['customer_email'],
                shopee_username=user_data['shopee_username'],
                expired_date=user_data['expiry_date'],
                description=f"{user_data['description']} - Singapore Region"
            )
            
            if result.get('id'):
                logger.info(f"✅ Successfully created Singapore user on {selected_server['name']}")
                return {
                    "status": "success",
                    "message": "Singapore VPN user created successfully",
                    "server": selected_server,
                    "client": result,
                    "created_clients": [{
                        "server": selected_server,
                        "client": result
                    }]
                }
            else:
                error_msg = result.get('message', 'Unknown error')
                logger.error(f"❌ Failed to create Singapore user on {selected_server['name']}: {error_msg}")
                
                return {
                    "status": "error",
                    "message": f"Failed to create Singapore VPN user: {error_msg}"
                }
                
        except Exception as e:
            logger.error(f"Error in Singapore VPN user creation: {str(e)}")
            return {
                "status": "error",
                "message": f"Internal error during Singapore user creation: {str(e)}"
            }
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for Singapore products.
        """
        base_response = super().format_success_response(user_data, vpn_result)
        
        # Add Singapore specific information
        server_info = vpn_result.get('server', {})
        client_info = vpn_result.get('client', {})
        
        base_response["data"]["strategy"] = "singapore"
        base_response["data"]["creation_type"] = "single_server"
        base_response["data"]["server_type"] = "digital_ocean"
        base_response["data"]["region"] = "Singapore"
        
        # Add server details
        base_response["data"]["server_details"] = [{
            "name": server_info.get('name'),
            "host": server_info.get('host'),
            "location": "Singapore",
            "provider": "Digital Ocean",
            "client_id": client_info.get('id'),
            "status": "active"
        }]
        
        # Add configuration information
        base_response["data"]["configuration"] = {
            "access_method": "single_server",
            "redundancy": "standard",
            "location": "Singapore",
            "bandwidth": "standard",
            "latency": "low"
        }
        
        return base_response


class VPNSingaporeHighSpeedStrategy(VPNSingaporeStrategy):
    """
    High-speed variant of the Singapore strategy with enhanced performance.
    """
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create high-speed Singapore VPN users with enhanced configuration.
        """
        # Add high-speed specific description
        user_data['description'] = f"{user_data['description']} - HIGH SPEED"
        
        # Call parent method
        result = super().create_vpn_users(servers, user_data)
        
        # Add high-speed specific metadata
        if result.get('status') == 'success':
            result['highspeed_features'] = {
                "bandwidth_limit": "1 Gbps",
                "concurrent_connections": "10",
                "priority_routing": True,
                "optimized_protocols": ["WireGuard", "IKEv2"]
            }
        
        return result
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for high-speed Singapore products.
        """
        response = super().format_success_response(user_data, vpn_result)
        
        # Add high-speed specific information
        response["data"]["strategy"] = "singapore_highspeed"
        response["data"]["tier"] = "high_speed"
        
        # Update configuration for high-speed
        response["data"]["configuration"]["bandwidth"] = "high_speed"
        response["data"]["configuration"]["performance"] = "optimized"
        
        if vpn_result.get('highspeed_features'):
            response["data"]["highspeed_features"] = vpn_result['highspeed_features']
        
        return response


class VPNSingaporePremiumStrategy(VPNSingaporeStrategy):
    """
    Premium variant of the Singapore strategy with premium features.
    """
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create premium Singapore VPN users with enhanced configuration.
        """
        # Add premium-specific description
        user_data['description'] = f"{user_data['description']} - PREMIUM"
        
        # Call parent method
        result = super().create_vpn_users(servers, user_data)
        
        # Add premium-specific metadata
        if result.get('status') == 'success':
            result['premium_features'] = {
                "bandwidth_limit": "unlimited",
                "concurrent_connections": "unlimited",
                "support_level": "premium",
                "priority_routing": True,
                "dedicated_ip": True,
                "advanced_protocols": ["WireGuard", "IKEv2", "OpenVPN"]
            }
        
        return result
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for premium Singapore products.
        """
        response = super().format_success_response(user_data, vpn_result)
        
        # Add premium-specific information
        response["data"]["strategy"] = "singapore_premium"
        response["data"]["tier"] = "premium"
        
        # Update configuration for premium
        response["data"]["configuration"]["bandwidth"] = "unlimited"
        response["data"]["configuration"]["redundancy"] = "premium"
        response["data"]["configuration"]["support"] = "24/7_premium"
        
        if vpn_result.get('premium_features'):
            response["data"]["premium_features"] = vpn_result['premium_features']
        
        return response


class VPNSingaporeBusinessStrategy(VPNSingaporeStrategy):
    """
    Business variant of the Singapore strategy for enterprise customers.
    """
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create business Singapore VPN users with enterprise configuration.
        """
        # Add business-specific description
        user_data['description'] = f"{user_data['description']} - BUSINESS"
        
        # Call parent method
        result = super().create_vpn_users(servers, user_data)
        
        # Add business-specific metadata
        if result.get('status') == 'success':
            result['business_features'] = {
                "bandwidth_limit": "unlimited",
                "concurrent_connections": "50",
                "support_level": "business",
                "priority_routing": True,
                "dedicated_ip": True,
                "static_ip": True,
                "enterprise_protocols": True,
                "compliance": ["SOC2", "GDPR"]
            }
        
        return result
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for business Singapore products.
        """
        response = super().format_success_response(user_data, vpn_result)
        
        # Add business-specific information
        response["data"]["strategy"] = "singapore_business"
        response["data"]["tier"] = "business"
        
        # Update configuration for business
        response["data"]["configuration"]["bandwidth"] = "unlimited"
        response["data"]["configuration"]["redundancy"] = "enterprise"
        response["data"]["configuration"]["support"] = "business_hours"
        response["data"]["configuration"]["compliance"] = "enterprise"
        
        if vpn_result.get('business_features'):
            response["data"]["business_features"] = vpn_result['business_features']
        
        return response
