# Troubleshooting Guide

This document provides solutions for common issues encountered with the OpenAI Plus Redeem Plugin.

## Quick Diagnostics

### 1. Plugin Health Check

Check if the plugin is running properly:

```bash
# Check plugin status via API
curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/services/health \
  -H "Authorization: Bearer admin_token"

# Check application logs
tail -f logs/application.log | grep "openai_plus_redeem"
```

### 2. Service Status Check

```bash
# Run built-in diagnostics
python plugins/openai_plus_redeem/validate_error_handling.py
python plugins/openai_plus_redeem/validate_data_persistence.py
python plugins/openai_plus_redeem/run_performance_tests.py
```

## Common Issues and Solutions

### 1. Plugin Initialization Issues

#### Issue: Plugin fails to initialize

**Symptoms:**
- Plugin not appearing in admin interface
- Error messages in application logs
- API endpoints returning 404 errors

**Possible Causes:**
- Missing or invalid configuration
- Missing dependencies
- File permission issues
- Service initialization failures

**Solutions:**

1. **Check Configuration**:
   ```bash
   # Validate configuration syntax
   python -c "import json; json.load(open('configs/core/plugin_config.json'))"
   ```

2. **Verify Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Check File Permissions**:
   ```bash
   # Ensure data directory is writable
   chmod 755 configs/data/
   mkdir -p configs/data/openai_plus_redeem
   chmod 755 configs/data/openai_plus_redeem
   ```

4. **Review Logs**:
   ```bash
   grep -i "openai_plus_redeem" logs/application.log | tail -20
   ```

#### Issue: Service manager initialization fails

**Symptoms:**
- Plugin loads but services are unavailable
- "Service not available" errors in API responses

**Solutions:**

1. **Check Service Configuration**:
   ```json
   {
     "openai_plus_redeem": {
       "enabled": true,
       "email_config": {
         "global_credentials": {
           "email": "<EMAIL>",
           "password": "your-app-password"
         }
       }
     }
   }
   ```

2. **Restart Services**:
   ```bash
   curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/services/chatgpt_account/restart \
     -H "Authorization: Bearer admin_token"
   ```

### 2. Email Connectivity Issues

#### Issue: Email verification fails

**Symptoms:**
- "IMAP connection failed" errors
- Verification codes not found
- Email service showing as unhealthy

**Possible Causes:**
- Incorrect Gmail credentials
- App password not configured
- IMAP settings incorrect
- Network connectivity issues
- Gmail security restrictions

**Solutions:**

1. **Verify Gmail App Password**:
   - Go to Google Account → Security → 2-Step Verification → App passwords
   - Generate new app password for "Mail"
   - Update configuration with new password

2. **Test IMAP Connection**:
   ```python
   import imaplib
   import ssl
   
   # Test IMAP connection
   try:
       context = ssl.create_default_context()
       server = imaplib.IMAP4_SSL("imap.gmail.com", 993, ssl_context=context)
       server.login("<EMAIL>", "your-app-password")
       print("IMAP connection successful")
       server.logout()
   except Exception as e:
       print(f"IMAP connection failed: {e}")
   ```

3. **Check Gmail Settings**:
   - Ensure 2-Factor Authentication is enabled
   - Verify IMAP is enabled in Gmail settings
   - Check for any security alerts from Google

4. **Network Troubleshooting**:
   ```bash
   # Test connectivity to Gmail IMAP server
   telnet imap.gmail.com 993
   
   # Check DNS resolution
   nslookup imap.gmail.com
   ```

#### Issue: Verification codes not found in emails

**Symptoms:**
- Email connection successful but codes not retrieved
- "No verification code found" messages

**Solutions:**

1. **Check Search Keywords**:
   ```json
   {
     "email_config": {
       "search_config": {
         "verification_keywords": ["openai", "chatgpt", "verification", "code", "login"]
       }
     }
   }
   ```

2. **Increase Search Results**:
   ```json
   {
     "email_config": {
       "search_config": {
         "max_search_results": 20,
         "search_timeout_seconds": 60
       }
     }
   }
   ```

3. **Manual Email Check**:
   - Log into Gmail manually
   - Search for recent OpenAI emails
   - Verify emails are arriving in the configured account

### 3. Data Persistence Issues

#### Issue: Data not saving or loading

**Symptoms:**
- Account data not persisting between restarts
- "Failed to save data" errors
- Empty account lists after adding accounts

**Possible Causes:**
- File permission issues
- Disk space full
- Corrupted data files
- Concurrent access conflicts

**Solutions:**

1. **Check File Permissions**:
   ```bash
   ls -la configs/data/openai_plus_redeem/
   chmod 644 configs/data/openai_plus_redeem/*.json
   ```

2. **Check Disk Space**:
   ```bash
   df -h
   du -sh configs/data/
   ```

3. **Validate Data Files**:
   ```bash
   # Check JSON syntax
   python -m json.tool configs/data/openai_plus_redeem/chatgpt_accounts.json
   ```

4. **Restore from Backup**:
   ```bash
   # List available backups
   ls -la configs/data/openai_plus_redeem/backups/
   
   # Restore from backup
   cp configs/data/openai_plus_redeem/backups/chatgpt_accounts_backup_YYYYMMDD_HHMMSS.json \
      configs/data/openai_plus_redeem/chatgpt_accounts.json
   ```

#### Issue: Backup creation fails

**Symptoms:**
- "Backup failed" messages in logs
- No backup files being created

**Solutions:**

1. **Check Backup Directory**:
   ```bash
   mkdir -p configs/data/openai_plus_redeem/backups
   chmod 755 configs/data/openai_plus_redeem/backups
   ```

2. **Manual Backup Test**:
   ```bash
   python -c "
   from plugins.openai_plus_redeem.models.utils import backup_data_file
   result = backup_data_file('configs/data/openai_plus_redeem/chatgpt_accounts.json')
   print(result)
   "
   ```

### 4. Shopee Integration Issues

#### Issue: Shopee webhooks not working

**Symptoms:**
- Orders not automatically processed
- Webhook endpoint returning errors
- No redemption records for Shopee orders

**Solutions:**

1. **Verify Webhook URL**:
   - Ensure URL is publicly accessible
   - Test webhook endpoint manually:
   ```bash
   curl -X POST https://your-domain.com/openai-plus-redeem/api/shopee/redeem \
     -H "Content-Type: application/json" \
     -d '{"order_data": {"order_sn": "TEST123", "buyer_username": "testuser"}}'
   ```

2. **Check Webhook Secret**:
   ```json
   {
     "shopee_integration": {
       "webhook_secret": "your-webhook-secret-from-shopee"
     }
   }
   ```

3. **Review Shopee Configuration**:
   - Verify webhook URL in Shopee seller center
   - Check webhook secret matches configuration
   - Ensure webhook is enabled for order events

#### Issue: SKU parsing errors

**Symptoms:**
- "Unsupported SKU" errors
- Orders processed but wrong account allocation

**Solutions:**

1. **Check SKU Format**:
   ```json
   {
     "shopee_integration": {
       "supported_skus": ["chatgpt_plus", "chatgpt_premium"]
     }
   }
   ```

2. **Debug SKU Parsing**:
   ```python
   from plugins.openai_plus_redeem.services.order_redemption_service import OrderRedemptionService
   
   # Test SKU parsing
   service = OrderRedemptionService({}, None)
   result = service._parse_sku_details("chatgpt_5_30")
   print(result)
   ```

### 5. Account Management Issues

#### Issue: No available accounts for redemption

**Symptoms:**
- "No available accounts" errors
- Redemptions stuck in pending status

**Solutions:**

1. **Check Account Status**:
   ```bash
   curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/accounts \
     -H "Authorization: Bearer admin_token"
   ```

2. **Add More Accounts**:
   ```bash
   curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/accounts \
     -H "Authorization: Bearer admin_token" \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "secure_password",
       "expiration_date": "2024-12-31T23:59:59Z",
       "max_concurrent_users": 5
     }'
   ```

3. **Check Account Capacity**:
   - Review current_users vs max_concurrent_users
   - Release unused account allocations
   - Increase max_concurrent_users if needed

#### Issue: Account allocation conflicts

**Symptoms:**
- Multiple users assigned to same account
- Account capacity exceeded
- Inconsistent account states

**Solutions:**

1. **Run Data Consistency Check**:
   ```bash
   python plugins/openai_plus_redeem/validate_data_persistence.py
   ```

2. **Manual Account Reset**:
   ```bash
   curl -X PUT http://localhost:5000/admin/openai-plus-redeem/api/accounts/ACCOUNT_ID \
     -H "Authorization: Bearer admin_token" \
     -H "Content-Type: application/json" \
     -d '{"current_users": 0}'
   ```

### 6. Performance Issues

#### Issue: Slow response times

**Symptoms:**
- API endpoints taking too long to respond
- Timeouts during redemption process
- High memory usage

**Solutions:**

1. **Run Performance Tests**:
   ```bash
   python plugins/openai_plus_redeem/run_performance_tests.py
   ```

2. **Check System Resources**:
   ```bash
   # Check memory usage
   free -h
   
   # Check CPU usage
   top -p $(pgrep -f "python.*main.py")
   
   # Check disk I/O
   iostat -x 1 5
   ```

3. **Optimize Configuration**:
   ```json
   {
     "email_config": {
       "search_config": {
         "search_timeout_seconds": 30,
         "max_search_results": 10
       }
     },
     "account_management": {
       "cleanup_interval_hours": 24
     }
   }
   ```

#### Issue: Memory leaks

**Symptoms:**
- Gradually increasing memory usage
- Application becomes unresponsive over time

**Solutions:**

1. **Monitor Memory Usage**:
   ```bash
   # Monitor plugin memory usage
   python -c "
   import psutil
   import os
   process = psutil.Process(os.getpid())
   print(f'Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB')
   "
   ```

2. **Restart Services Periodically**:
   ```bash
   # Add to cron job for periodic restart
   0 2 * * * curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/services/restart-all
   ```

### 7. Rate Limiting Issues

#### Issue: Rate limit exceeded errors

**Symptoms:**
- "Rate limit exceeded" responses
- Users unable to make requests
- 429 HTTP status codes

**Solutions:**

1. **Check Current Rate Limits**:
   ```json
   {
     "security_config": {
       "rate_limit_requests_per_minute": 10,
       "admin_rate_limit_requests_per_minute": 100
     }
   }
   ```

2. **Adjust Rate Limits**:
   ```bash
   curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/config/security \
     -H "Authorization: Bearer admin_token" \
     -H "Content-Type: application/json" \
     -d '{"rate_limit_requests_per_minute": 20}'
   ```

3. **Monitor Rate Limit Usage**:
   ```bash
   # Check rate limit headers in responses
   curl -I http://localhost:5000/openai-plus-redeem/api/status/REDEEM_123
   ```

### 8. Authentication and Authorization Issues

#### Issue: Admin authentication fails

**Symptoms:**
- "Authentication required" errors
- Unable to access admin endpoints
- 401 HTTP status codes

**Solutions:**

1. **Verify Admin Token**:
   ```bash
   # Test admin authentication
   curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/dashboard/stats \
     -H "Authorization: Bearer your_admin_token" -v
   ```

2. **Check Token Expiration**:
   - Verify token hasn't expired
   - Generate new admin token if needed
   - Update client applications with new token

3. **Review Authentication Configuration**:
   - Check main application authentication settings
   - Verify plugin authentication integration

## Debugging Tools and Techniques

### 1. Enable Debug Logging

```json
{
  "openai_plus_redeem": {
    "debug": true
  }
}
```

### 2. Use Built-in Validation Scripts

```bash
# Run all validation scripts
python plugins/openai_plus_redeem/validate_error_handling.py
python plugins/openai_plus_redeem/validate_data_persistence.py
python plugins/openai_plus_redeem/validate_email_service.py
python plugins/openai_plus_redeem/run_performance_tests.py
```

### 3. Manual API Testing

```bash
# Test customer endpoints
curl -X POST http://localhost:5000/openai-plus-redeem/api/redeem \
  -H "Content-Type: application/json" \
  -d '{"order_id": "TEST123", "buyer_username": "testuser", "sku": "chatgpt_plus", "var_sku": "chatgpt_5_30"}'

# Test admin endpoints
curl -X GET http://localhost:5000/admin/openai-plus-redeem/api/accounts \
  -H "Authorization: Bearer admin_token"
```

### 4. Database/File Inspection

```bash
# Check data files
ls -la configs/data/openai_plus_redeem/
cat configs/data/openai_plus_redeem/chatgpt_accounts.json | python -m json.tool

# Check backups
ls -la configs/data/openai_plus_redeem/backups/
```

### 5. Service Health Monitoring

```bash
# Continuous health monitoring
watch -n 30 'curl -s http://localhost:5000/admin/openai-plus-redeem/api/services/health -H "Authorization: Bearer admin_token" | python -m json.tool'
```

## Log Analysis

### 1. Important Log Patterns

```bash
# Plugin initialization
grep "OpenAI Plus Redeem Plugin" logs/application.log

# Service errors
grep -i "error.*openai_plus_redeem" logs/application.log

# Email connectivity
grep -i "imap\|email.*openai_plus_redeem" logs/application.log

# Rate limiting
grep -i "rate.*limit.*openai_plus_redeem" logs/application.log
```

### 2. Log Levels

- **DEBUG**: Detailed execution information
- **INFO**: General operational messages
- **WARNING**: Potential issues that don't stop operation
- **ERROR**: Errors that affect functionality
- **CRITICAL**: Severe errors that may stop the plugin

### 3. Custom Log Analysis

```bash
# Create custom log analysis script
cat > analyze_logs.sh << 'EOF'
#!/bin/bash
echo "=== OpenAI Plus Redeem Plugin Log Analysis ==="
echo "Errors in last 24 hours:"
grep -i "error.*openai_plus_redeem" logs/application.log | tail -20

echo -e "\nWarnings in last 24 hours:"
grep -i "warning.*openai_plus_redeem" logs/application.log | tail -10

echo -e "\nRate limit hits:"
grep -i "rate.*limit.*exceeded.*openai_plus_redeem" logs/application.log | wc -l

echo -e "\nEmail connectivity issues:"
grep -i "imap.*failed\|email.*error.*openai_plus_redeem" logs/application.log | tail -5
EOF

chmod +x analyze_logs.sh
./analyze_logs.sh
```

## Recovery Procedures

### 1. Complete Plugin Reset

```bash
# Stop application
systemctl stop steamcodetool  # or your service name

# Backup current data
cp -r configs/data/openai_plus_redeem configs/data/openai_plus_redeem_backup_$(date +%Y%m%d_%H%M%S)

# Reset data files
rm -f configs/data/openai_plus_redeem/*.json

# Restart application
systemctl start steamcodetool
```

### 2. Service-Specific Recovery

```bash
# Restart specific service
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/services/chatgpt_account/restart \
  -H "Authorization: Bearer admin_token"

# Reload configuration
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/reload \
  -H "Authorization: Bearer admin_token"
```

### 3. Data Recovery from Backup

```bash
# List available backups
ls -la configs/data/openai_plus_redeem/backups/

# Restore specific backup
cp configs/data/openai_plus_redeem/backups/chatgpt_accounts_backup_20240115_103000.json \
   configs/data/openai_plus_redeem/chatgpt_accounts.json

# Restart plugin
curl -X POST http://localhost:5000/admin/openai-plus-redeem/api/services/restart-all \
  -H "Authorization: Bearer admin_token"
```

## Preventive Measures

### 1. Regular Health Checks

Set up automated health checks:

```bash
# Add to cron job
*/15 * * * * curl -s http://localhost:5000/admin/openai-plus-redeem/api/services/health -H "Authorization: Bearer admin_token" | grep -q "healthy" || echo "Plugin health check failed" | mail -s "Plugin Alert" <EMAIL>
```

### 2. Backup Monitoring

```bash
# Check backup creation
*/30 * * * * [ -f configs/data/openai_plus_redeem/backups/chatgpt_accounts_backup_$(date +%Y%m%d).json ] || echo "Backup missing" | mail -s "Backup Alert" <EMAIL>
```

### 3. Resource Monitoring

```bash
# Monitor disk space
0 */6 * * * df -h | grep -E "9[0-9]%|100%" && echo "Disk space critical" | mail -s "Disk Alert" <EMAIL>
```

### 4. Configuration Validation

```bash
# Validate configuration daily
0 2 * * * python -c "import json; json.load(open('configs/core/plugin_config.json'))" || echo "Configuration invalid" | mail -s "Config Alert" <EMAIL>
```

## Getting Help

### 1. Support Channels

- **Documentation**: Check README.md and API.md
- **Issue Tracker**: Report bugs and feature requests
- **Community Forum**: Ask questions and share solutions

### 2. Information to Include in Support Requests

- Plugin version and configuration
- Error messages and stack traces
- Steps to reproduce the issue
- System information (OS, Python version, etc.)
- Relevant log excerpts

### 3. Emergency Contacts

For critical production issues:
- Primary: <EMAIL>
- Secondary: <EMAIL>
- Emergency: +1-XXX-XXX-XXXX

## Appendix: Common Error Messages

| Error Message | Cause | Solution |
|---------------|-------|----------|
| `IMAP connection failed` | Gmail credentials or network issue | Check app password and network |
| `No available accounts` | All accounts in use or expired | Add more accounts or check capacity |
| `Rate limit exceeded` | Too many requests | Wait or increase rate limits |
| `Configuration validation failed` | Invalid config syntax | Check JSON syntax and required fields |
| `Service initialization failed` | Missing dependencies or permissions | Check requirements and file permissions |
| `Webhook signature invalid` | Wrong webhook secret | Update webhook secret configuration |
| `Data persistence error` | File system issue | Check permissions and disk space |
| `Account allocation conflict` | Concurrent access issue | Run data consistency check |
