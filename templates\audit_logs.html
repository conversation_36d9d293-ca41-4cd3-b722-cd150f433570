<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Audit Logs</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-900">🔒 Security Audit Logs</h1>
                <div class="flex space-x-2">
                    <select id="filterSelect" onchange="changeFilter()" class="px-3 py-2 border border-gray-300 rounded-md">
                        <option value="all" {% if filter_type == 'all' %}selected{% endif %}>All Events</option>
                        <option value="fake_order" {% if filter_type == 'fake_order' %}selected{% endif %}>🧪 Fake Order Events</option>
                    </select>
                    <button onclick="refreshLogs()" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                        🔄 Refresh
                    </button>
                    <a href="/admin/dashboard" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>

            {% if error %}
            <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-red-700">{{ error }}</p>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="mb-4 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    Showing {{ total_logs }} log entries (filtered: {{ filter_type }})
                </div>
                <div class="text-sm text-gray-500">
                    Last updated: <span id="lastUpdated">{{ moment().format('YYYY-MM-DD HH:mm:ss') }}</span>
                </div>
            </div>

            {% if logs %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Timestamp
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Level
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Event Type
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Details
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                User/IP
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for log in logs %}
                        <tr class="hover:bg-gray-50 {% if 'fake_order' in log.event_type %}bg-yellow-50{% endif %}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ log.timestamp }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if log.level == 'ERROR' %}bg-red-100 text-red-800
                                    {% elif log.level == 'WARNING' %}bg-yellow-100 text-yellow-800
                                    {% elif log.level == 'INFO' %}bg-green-100 text-green-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ log.level }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if 'fake_order' in log.event_type %}
                                    🧪 {{ log.event_type }}
                                {% else %}
                                    {{ log.event_type }}
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {% if log.details %}
                                    {% if log.details.order_sn %}
                                        <div><strong>Order:</strong> {{ log.details.order_sn }}</div>
                                    {% endif %}
                                    {% if log.details.operation %}
                                        <div><strong>Operation:</strong> {{ log.details.operation }}</div>
                                    {% endif %}
                                    {% if log.details.message %}
                                        <div>{{ log.details.message }}</div>
                                    {% endif %}
                                    {% if log.details.error %}
                                        <div class="text-red-600"><strong>Error:</strong> {{ log.details.error }}</div>
                                    {% endif %}
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if log.client_info %}
                                    <div>{{ log.client_info.user_id or 'anonymous' }}</div>
                                    <div class="text-xs">{{ log.client_info.ip_address or 'unknown' }}</div>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No audit logs found</h3>
                <p class="mt-1 text-sm text-gray-500">No security events match the current filter.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        function changeFilter() {
            const filter = document.getElementById('filterSelect').value;
            window.location.href = `/admin/audit-logs?filter=${filter}`;
        }

        function refreshLogs() {
            window.location.reload();
        }

        // Update last updated timestamp
        document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
    </script>
</body>
</html>