"""
VPN Malaysia High-Speed Strategy
Strategy for Malaysia high-speed VPN products (my_highspeed_*) that require:
- Creation on ALL available servers
- Same UUID across all servers
- Email validation across all servers
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

import logging
import uuid
import concurrent.futures
import threading
from typing import Dict, List, Any
from .vpn_base_strategy import VPNBaseStrategy
from .strategy_factory import VPNStrategyFactory

logger = logging.getLogger(__name__)

class VPNMalaysiaHighSpeedStrategy(VPNBaseStrategy):
    """
    Strategy for Malaysia high-speed VPN products (my_highspeed_*).
    Creates users with the same UUID on all available servers.
    """
    
    def get_target_servers(self, product_sku: str) -> List[Dict[str, Any]]:
        """
        Get servers for Malaysia high-speed products based on server tags.
        Uses the strategy factory to determine which server tags to use.
        """
        try:
            # Get required server tags for this SKU
            required_tags = VPNStrategyFactory.get_server_tags_for_sku(product_sku)
            logger.info(f"Malaysia high-speed strategy: Using server tags {required_tags} for SKU {product_sku}")

            # Get servers filtered by tags
            target_servers = self._get_servers_by_tags(required_tags)

            logger.info(f"Malaysia high-speed strategy: Found {len(target_servers)} servers for {product_sku}")
            logger.info(f"my_highspeed_ users can select from servers with tags: {required_tags}")
            for server in target_servers:
                logger.info(f"  - {server.get('name', 'Unknown')} (tags: {server.get('tags', [])})")

            return target_servers

        except Exception as e:
            logger.error(f"Error getting servers for high-speed product {product_sku}: {str(e)}")
            return []
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create VPN users with the same UUID on all servers using parallel processing.
        """
        try:
            # First, check for existing users across all servers in parallel
            existing_check = self._check_existing_users_parallel(user_data['customer_email'], servers)

            if existing_check.get("has_existing"):
                # If user already exists, return existing configuration links
                return self._handle_existing_redemption(existing_check, user_data)

            # For my_highspeed products, we need to check if there's already a shared UUID for this email
            # This prevents the "Client ID already exists" error on first redemption
            shared_client_id = self._get_or_generate_shared_client_id(user_data['customer_email'], servers)

            logger.info(f"Creating VPN users on {len(servers)} servers with shared ID: {shared_client_id}")

            # Create users on all servers in parallel
            creation_results = self._create_users_parallel(servers, user_data, shared_client_id)

            created_clients = creation_results.get("created_clients", [])
            failed_servers = creation_results.get("failed_servers", [])

            # Determine success based on results
            if created_clients:
                success_count = len(created_clients)
                total_count = len(servers)

                return {
                    "status": "success",
                    "message": f"Created users on {success_count}/{total_count} servers",
                    "created_clients": created_clients,
                    "failed_servers": failed_servers,
                    "shared_client_id": shared_client_id,
                    "success_rate": f"{success_count}/{total_count}"
                }
            else:
                return {
                    "status": "error",
                    "message": "Failed to create users on any server",
                    "failed_servers": failed_servers
                }

        except Exception as e:
            logger.error(f"Error in high-speed VPN user creation: {str(e)}")
            return {
                "status": "error",
                "message": f"Internal error during user creation: {str(e)}"
            }

    def _get_or_generate_shared_client_id(self, email: str, servers: List[Dict[str, Any]]) -> str:
        """
        Get existing shared client ID for this exact email or generate a new one.
        Each order should have a unique client ID since email format is {username}-{order_id}.
        """
        try:
            # Check if client exists by exact email using the VPN API service
            existing_client = self.vpn_service.get_client_by_email(email)
            
            if existing_client and existing_client.get('client_id'):
                existing_client_id = existing_client.get('client_id')
                logger.info(f"Found existing client ID for exact email {email}: {existing_client_id}")
                return str(existing_client_id)
            else:
                logger.info(f"No existing client found for email {email}, generating new client ID")

        except Exception as e:
            logger.error(f"Error checking for existing client by email: {str(e)}")

        # Generate new UUID for this unique email (username-orderid combination)
        new_client_id = str(uuid.uuid4())
        logger.info(f"Generated new shared client ID for email {email}: {new_client_id}")
        return new_client_id

    def _check_existing_users_parallel(self, email: str, servers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Check for existing users across all servers in parallel.
        """
        existing_clients = []

        def check_server(server):
            try:
                clients = self.vpn_service.get_clients(server_id=server['id'], search=email)
                if clients and clients.get('clients'):
                    for client in clients['clients']:
                        if client.get('email', '').lower() == email.lower():
                            return {
                                "server": server,
                                "client": client
                            }
                return None
            except Exception as e:
                logger.error(f"Error checking existing user on {server['name']}: {str(e)}")
                return None

        # Check all servers in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            future_to_server = {executor.submit(check_server, server): server for server in servers}

            for future in concurrent.futures.as_completed(future_to_server):
                result = future.result()
                if result:
                    existing_clients.append(result)

        return {
            "has_existing": len(existing_clients) > 0,
            "existing_clients": existing_clients
        }

    def _handle_existing_redemption(self, existing_check: Dict[str, Any], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle case where user already exists - return existing configuration links.
        """
        existing_clients = existing_check.get("existing_clients", [])

        # For VPN products, we always want to show the config link even if already redeemed
        logger.info(f"User already exists on {len(existing_clients)} servers. Providing config access.")

        # For my_highspeed products, we need to provide server selection capability
        # Store all available servers for user selection in vpnConfig.html
        all_servers = self.get_target_servers(user_data.get('product_sku', ''))

        result = {
            "status": "success",
            "message": f"User already exists on {len(existing_clients)} servers. Providing config access.",
            "existing_redemption": True,
            "created_clients": existing_clients,  # Reuse existing structure for consistency
            "success_rate": f"{len(existing_clients)}/{len(existing_clients)}",
            "available_servers": all_servers,  # Add server list for selection
            "server_selection_enabled": True  # Flag to enable server selection in UI
        }

        return result

    def _create_users_parallel(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any], shared_client_id: str) -> Dict[str, Any]:
        """
        Create users on all servers in parallel with progress tracking.
        """
        created_clients = []
        failed_servers = []
        lock = threading.Lock()

        def create_on_server(server):
            try:
                logger.info(f"Creating user on server: {server['name']}")

                result = self.vpn_service.create_client(
                    server_id=server['id'],
                    email=user_data['customer_email'],
                    shopee_username=user_data['shopee_username'],
                    expired_date=user_data['expiry_date'],
                    description=f"{user_data['description']} - Shared ID: {shared_client_id}",
                    client_id=shared_client_id
                )

                with lock:
                    if result.get('id'):
                        created_clients.append({
                            "server": server,
                            "client": result
                        })
                        logger.info(f"✅ Successfully created user on {server['name']}")
                    elif result.get('status') == 'client_exists':
                        # Client ID already exists - this is expected for my_highspeed products
                        created_clients.append({
                            "server": server,
                            "client": {"id": shared_client_id, "status": "already_exists"},
                            "status": "already_exists"
                        })
                        logger.info(f"⚠️ Client ID already exists on {server['name']} - skipping")
                    else:
                        error_msg = result.get('message', 'Unknown error')
                        failed_servers.append({
                            "server": server,
                            "error": error_msg
                        })
                        logger.error(f"❌ Failed to create user on {server['name']}: {error_msg}")

            except Exception as e:
                error_msg = str(e)
                with lock:
                    failed_servers.append({
                        "server": server,
                        "error": error_msg
                    })
                logger.error(f"❌ Exception creating user on {server['name']}: {error_msg}")

        # Create users on all servers in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(create_on_server, server) for server in servers]
            concurrent.futures.wait(futures)

        return {
            "created_clients": created_clients,
            "failed_servers": failed_servers
        }
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for high-speed products with multiple servers.
        """
        base_response = super().format_success_response(user_data, vpn_result)

        # Add high-speed specific information
        created_clients = vpn_result.get('created_clients', [])
        failed_servers = vpn_result.get('failed_servers', [])

        base_response["data"]["strategy"] = "malaysia_highspeed"
        base_response["data"]["creation_type"] = "multi_server"
        base_response["data"]["success_rate"] = vpn_result.get('success_rate', '0/0')

        # Add server selection capability for my_highspeed products
        all_servers = self.get_target_servers(user_data.get('product_sku', ''))
        base_response["data"]["available_servers"] = all_servers
        base_response["data"]["server_selection_enabled"] = True

        if failed_servers:
            base_response["data"]["warnings"] = [
                f"Failed to create user on {fs['server']['name']}: {fs['error']}"
                for fs in failed_servers
            ]
        
        # Add server details
        base_response["data"]["server_details"] = [
            {
                "name": client_info['server']['name'],
                "host": client_info['server']['host'],
                "client_id": client_info['client']['id'],
                "status": "created"
            }
            for client_info in created_clients
        ]
        
        # Add configuration information
        base_response["data"]["configuration"] = {
            "shared_uuid": vpn_result.get('shared_client_id'),
            "access_method": "multi_server",
            "redundancy": "high",
            "total_servers": len(created_clients)
        }
        
        return base_response


class VPNMalaysiaHighSpeedPremiumStrategy(VPNMalaysiaHighSpeedStrategy):
    """
    Premium variant of the high-speed strategy with additional features.
    """
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create premium VPN users with enhanced configuration.
        """
        # Add premium-specific description
        user_data['description'] = f"{user_data['description']} - PREMIUM"
        
        # Call parent method
        result = super().create_vpn_users(servers, user_data)
        
        # Add premium-specific metadata
        if result.get('status') == 'success':
            result['premium_features'] = {
                "priority_support": True,
                "bandwidth_limit": "unlimited",
                "concurrent_connections": "unlimited",
                "premium_locations": True
            }
        
        return result
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for premium high-speed products.
        """
        response = super().format_success_response(user_data, vpn_result)
        
        # Add premium-specific information
        response["data"]["strategy"] = "malaysia_highspeed_premium"
        response["data"]["tier"] = "premium"
        
        if vpn_result.get('premium_features'):
            response["data"]["premium_features"] = vpn_result['premium_features']
        
        return response
