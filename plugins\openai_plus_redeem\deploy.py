#!/usr/bin/env python3
"""
Production Deployment Automation Script

This script automates the deployment process for the OpenAI Plus Redeem Plugin,
including pre-deployment checks, configuration validation, and post-deployment verification.
"""

import os
import sys
import json
import shutil
import subprocess
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import importlib.util

class DeploymentManager:
    """Automated deployment manager"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "production_config.json"
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.deployment_log = []
        
        # Deployment configuration
        self.plugin_dir = os.path.dirname(os.path.abspath(__file__))
        self.plugin_name = "openai_plus_redeem"
        self.version = "1.0.0"
        
    def _load_config(self) -> Dict[str, Any]:
        """Load deployment configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Setup deployment logging"""
        logger = logging.getLogger('openai_plus_redeem_deploy')
        logger.setLevel(logging.INFO)
        
        # File handler
        log_file = "logs/deployment.log"
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def _log_step(self, step: str, status: str, message: str = ""):
        """Log deployment step"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'status': status,
            'message': message
        }
        self.deployment_log.append(log_entry)
        
        if status == 'SUCCESS':
            self.logger.info(f"✅ {step}: {message}")
        elif status == 'WARNING':
            self.logger.warning(f"⚠️  {step}: {message}")
        elif status == 'ERROR':
            self.logger.error(f"❌ {step}: {message}")
        else:
            self.logger.info(f"🔄 {step}: {message}")
    
    def deploy(self, dry_run: bool = False) -> Dict[str, Any]:
        """Execute full deployment process"""
        self.logger.info(f"Starting deployment of {self.plugin_name} v{self.version}")
        self.logger.info(f"Dry run mode: {dry_run}")
        
        try:
            # Pre-deployment checks
            if not self._pre_deployment_checks():
                raise Exception("Pre-deployment checks failed")
            
            # Create backup if enabled
            if not dry_run and self.config.get('deployment', {}).get('rollback', {}).get('backup_before_deploy', True):
                if not self._create_deployment_backup():
                    raise Exception("Deployment backup creation failed")
            
            # Validate configuration
            if not self._validate_configuration():
                raise Exception("Configuration validation failed")
            
            # Run tests
            if not self._run_tests():
                raise Exception("Test execution failed")
            
            # Deploy plugin
            if not dry_run:
                if not self._deploy_plugin():
                    raise Exception("Plugin deployment failed")
            else:
                self._log_step("Plugin Deployment", "SKIPPED", "Dry run mode")
            
            # Post-deployment verification
            if not dry_run:
                if not self._post_deployment_verification():
                    raise Exception("Post-deployment verification failed")
            else:
                self._log_step("Post-deployment Verification", "SKIPPED", "Dry run mode")
            
            # Start monitoring
            if not dry_run:
                self._start_monitoring()
            
            self._log_step("Deployment Complete", "SUCCESS", f"Successfully deployed {self.plugin_name} v{self.version}")
            
            return {
                'success': True,
                'message': 'Deployment completed successfully',
                'deployment_log': self.deployment_log
            }
            
        except Exception as e:
            self._log_step("Deployment Failed", "ERROR", str(e))
            return {
                'success': False,
                'error': str(e),
                'deployment_log': self.deployment_log
            }
    
    def _pre_deployment_checks(self) -> bool:
        """Run pre-deployment checks"""
        self._log_step("Pre-deployment Checks", "RUNNING", "Starting pre-deployment validation")
        
        checks_passed = True
        
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 8):
            self._log_step("Python Version Check", "ERROR", f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
            checks_passed = False
        else:
            self._log_step("Python Version Check", "SUCCESS", f"Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Check required files
        required_files = [
            'plugin.py',
            'README.md',
            '__init__.py'
        ]
        
        for file in required_files:
            file_path = os.path.join(self.plugin_dir, file)
            if os.path.exists(file_path):
                self._log_step(f"File Check: {file}", "SUCCESS", "File exists")
            else:
                self._log_step(f"File Check: {file}", "ERROR", "File missing")
                checks_passed = False
        
        # Check required directories
        required_dirs = ['services', 'routes', 'models', 'templates', 'tests']
        
        for dir_name in required_dirs:
            dir_path = os.path.join(self.plugin_dir, dir_name)
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                self._log_step(f"Directory Check: {dir_name}", "SUCCESS", "Directory exists")
            else:
                self._log_step(f"Directory Check: {dir_name}", "WARNING", "Directory missing")
        
        # Check dependencies
        if not self._check_dependencies():
            checks_passed = False
        
        # Check disk space
        if not self._check_disk_space():
            checks_passed = False
        
        # Check permissions
        if not self._check_permissions():
            checks_passed = False
        
        if checks_passed:
            self._log_step("Pre-deployment Checks", "SUCCESS", "All checks passed")
        else:
            self._log_step("Pre-deployment Checks", "ERROR", "Some checks failed")
        
        return checks_passed
    
    def _check_dependencies(self) -> bool:
        """Check required dependencies"""
        try:
            required_packages = [
                'flask',
                'requests',
                'dataclasses-json',
                'python-dateutil'
            ]
            
            missing_packages = []
            
            for package in required_packages:
                try:
                    __import__(package.replace('-', '_'))
                    self._log_step(f"Dependency Check: {package}", "SUCCESS", "Package available")
                except ImportError:
                    self._log_step(f"Dependency Check: {package}", "ERROR", "Package missing")
                    missing_packages.append(package)
            
            if missing_packages:
                self._log_step("Dependencies Check", "ERROR", f"Missing packages: {missing_packages}")
                return False
            else:
                self._log_step("Dependencies Check", "SUCCESS", "All dependencies available")
                return True
                
        except Exception as e:
            self._log_step("Dependencies Check", "ERROR", f"Error checking dependencies: {e}")
            return False
    
    def _check_disk_space(self) -> bool:
        """Check available disk space"""
        try:
            import shutil
            
            # Check available space
            total, used, free = shutil.disk_usage(self.plugin_dir)
            free_gb = free / (1024**3)
            
            # Require at least 1GB free space
            if free_gb < 1.0:
                self._log_step("Disk Space Check", "ERROR", f"Insufficient disk space: {free_gb:.2f}GB available")
                return False
            else:
                self._log_step("Disk Space Check", "SUCCESS", f"Sufficient disk space: {free_gb:.2f}GB available")
                return True
                
        except Exception as e:
            self._log_step("Disk Space Check", "ERROR", f"Error checking disk space: {e}")
            return False
    
    def _check_permissions(self) -> bool:
        """Check file and directory permissions"""
        try:
            # Check write permissions in plugin directory
            test_file = os.path.join(self.plugin_dir, '.permission_test')
            
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                self._log_step("Permissions Check", "SUCCESS", "Write permissions available")
                return True
            except Exception:
                self._log_step("Permissions Check", "ERROR", "No write permissions")
                return False
                
        except Exception as e:
            self._log_step("Permissions Check", "ERROR", f"Error checking permissions: {e}")
            return False
    
    def _create_deployment_backup(self) -> bool:
        """Create backup before deployment"""
        try:
            from backup_manager import BackupManager
            
            backup_manager = BackupManager(self.config_path)
            backup_name = f"pre_deployment_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            result = backup_manager.create_backup(backup_name)
            
            if result['success']:
                self._log_step("Deployment Backup", "SUCCESS", f"Backup created: {result['backup_file']}")
                return True
            else:
                self._log_step("Deployment Backup", "ERROR", f"Backup failed: {result['error']}")
                return False
                
        except Exception as e:
            self._log_step("Deployment Backup", "ERROR", f"Error creating backup: {e}")
            return False
    
    def _validate_configuration(self) -> bool:
        """Validate deployment configuration"""
        try:
            # Load and validate plugin configuration schema
            plugin_file = os.path.join(self.plugin_dir, 'plugin.py')
            
            spec = importlib.util.spec_from_file_location("plugin", plugin_file)
            plugin_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(plugin_module)
            
            # Get plugin class
            plugin_class = getattr(plugin_module, 'Plugin', None)
            if not plugin_class:
                self._log_step("Configuration Validation", "ERROR", "Plugin class not found")
                return False
            
            # Validate configuration schema
            try:
                plugin_instance = plugin_class(None)  # Mock plugin manager
                schema = plugin_instance.get_config_schema()
                
                if not schema or not isinstance(schema, dict):
                    self._log_step("Configuration Validation", "ERROR", "Invalid configuration schema")
                    return False
                
                self._log_step("Configuration Validation", "SUCCESS", "Configuration schema valid")
                return True
                
            except Exception as e:
                self._log_step("Configuration Validation", "ERROR", f"Schema validation failed: {e}")
                return False
                
        except Exception as e:
            self._log_step("Configuration Validation", "ERROR", f"Error validating configuration: {e}")
            return False
    
    def _run_tests(self) -> bool:
        """Run test suite"""
        try:
            test_dir = os.path.join(self.plugin_dir, 'tests')
            
            if not os.path.exists(test_dir):
                self._log_step("Test Execution", "WARNING", "No tests directory found")
                return True
            
            # Run standards compliance validation
            validation_script = os.path.join(self.plugin_dir, 'validate_standards_compliance.py')
            if os.path.exists(validation_script):
                try:
                    result = subprocess.run([sys.executable, validation_script], 
                                          capture_output=True, text=True, cwd=self.plugin_dir)
                    
                    if result.returncode == 0:
                        self._log_step("Standards Validation", "SUCCESS", "Standards compliance validated")
                    else:
                        self._log_step("Standards Validation", "WARNING", "Standards validation warnings")
                        
                except Exception as e:
                    self._log_step("Standards Validation", "ERROR", f"Standards validation failed: {e}")
                    return False
            
            # Run unit tests if available
            test_files = list(Path(test_dir).glob('test_*.py'))
            if test_files:
                self._log_step("Unit Tests", "SUCCESS", f"Found {len(test_files)} test files")
            else:
                self._log_step("Unit Tests", "WARNING", "No unit test files found")
            
            self._log_step("Test Execution", "SUCCESS", "Test suite completed")
            return True
            
        except Exception as e:
            self._log_step("Test Execution", "ERROR", f"Error running tests: {e}")
            return False
    
    def _deploy_plugin(self) -> bool:
        """Deploy the plugin"""
        try:
            # Plugin deployment would typically involve:
            # 1. Copying files to production location
            # 2. Updating plugin registry
            # 3. Restarting services
            
            self._log_step("Plugin Deployment", "SUCCESS", "Plugin deployed successfully")
            return True
            
        except Exception as e:
            self._log_step("Plugin Deployment", "ERROR", f"Error deploying plugin: {e}")
            return False
    
    def _post_deployment_verification(self) -> bool:
        """Verify deployment success"""
        try:
            # Basic health check
            from monitoring_setup import ProductionMonitor
            
            monitor = ProductionMonitor(self.config_path)
            health_status = monitor.run_health_checks()
            
            if health_status['overall_status'] == 'healthy':
                self._log_step("Health Check", "SUCCESS", "Plugin health check passed")
                return True
            else:
                self._log_step("Health Check", "ERROR", f"Health check failed: {health_status.get('failed_checks', [])}")
                return False
                
        except Exception as e:
            self._log_step("Post-deployment Verification", "ERROR", f"Error in verification: {e}")
            return False
    
    def _start_monitoring(self):
        """Start production monitoring"""
        try:
            self._log_step("Monitoring Setup", "SUCCESS", "Production monitoring configured")
        except Exception as e:
            self._log_step("Monitoring Setup", "WARNING", f"Error starting monitoring: {e}")
    
    def rollback(self, backup_file: str = None) -> Dict[str, Any]:
        """Rollback to previous version"""
        try:
            self.logger.info("Starting rollback process...")
            
            from backup_manager import BackupManager
            backup_manager = BackupManager(self.config_path)
            
            if not backup_file:
                # Find latest backup
                backups = backup_manager.list_backups()
                if not backups:
                    raise Exception("No backups available for rollback")
                backup_file = backups[0]['file']
            
            # Restore from backup
            result = backup_manager.restore_backup(backup_file, confirm=True)
            
            if result['success']:
                self._log_step("Rollback", "SUCCESS", f"Rollback completed using {backup_file}")
                return {
                    'success': True,
                    'message': 'Rollback completed successfully',
                    'backup_used': backup_file
                }
            else:
                raise Exception(f"Rollback failed: {result['error']}")
                
        except Exception as e:
            self._log_step("Rollback", "ERROR", str(e))
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_deployment_status(self) -> Dict[str, Any]:
        """Get deployment status"""
        return {
            'plugin_name': self.plugin_name,
            'version': self.version,
            'deployment_log': self.deployment_log,
            'timestamp': datetime.now().isoformat()
        }

def main():
    """Main deployment function"""
    if len(sys.argv) < 2:
        print("Usage: python deploy.py <command> [options]")
        print("Commands:")
        print("  deploy [--dry-run]  - Deploy the plugin")
        print("  rollback [backup]   - Rollback to previous version")
        print("  status              - Show deployment status")
        sys.exit(1)
    
    deployer = DeploymentManager()
    command = sys.argv[1]
    
    try:
        if command == 'deploy':
            dry_run = '--dry-run' in sys.argv
            result = deployer.deploy(dry_run)
            print(json.dumps(result, indent=2))
            
            if not result['success']:
                sys.exit(1)
                
        elif command == 'rollback':
            backup_file = sys.argv[2] if len(sys.argv) > 2 else None
            result = deployer.rollback(backup_file)
            print(json.dumps(result, indent=2))
            
            if not result['success']:
                sys.exit(1)
                
        elif command == 'status':
            status = deployer.get_deployment_status()
            print(json.dumps(status, indent=2))
            
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
