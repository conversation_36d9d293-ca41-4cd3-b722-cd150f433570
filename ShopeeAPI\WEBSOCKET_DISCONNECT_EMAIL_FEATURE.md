# WebSocket Disconnect Email Notification Feature

## Overview

This feature automatically sends email notifications when the WebSocket connection to <PERSON><PERSON> is disconnected. This helps you stay informed about connection issues and take appropriate action when needed.

## How It Works

The system monitors the WebSocket connection and automatically sends email notifications in the following scenarios:

1. **Socket.IO Disconnection**: When the Socket.IO connection is unexpectedly closed
2. **Connection Timeout**: When no messages are received for 10 minutes
3. **Connection Loss Detection**: When the maintenance loop detects a lost connection
4. **Authentication Failures**: When the connection is dropped immediately after login due to auth issues

## Configuration

To enable email notifications, you need to configure the email settings in your `config.json` file:

```json
{
  "email_sender": {
    "from_email": "<EMAIL>",
    "from_password": "your-app-password",
    "default_to_email": "<EMAIL>"
  }
}
```

### Email Configuration Fields

- **from_email**: The sender email address (Gmail recommended)
- **from_password**: App password for the sender email (not your regular password)
- **default_to_email**: The recipient email address for notifications

### Setting Up Gmail App Password

1. Enable 2-factor authentication on your Gmail account
2. Go to Google Account settings > Security > App passwords
3. Generate a new app password for "Mail"
4. Use this app password in the `from_password` field

## Email Content

The disconnect notification email includes:

- **Disconnect reason**: Why the connection was lost
- **Connection duration**: How long the connection was active
- **Last message time**: When the last message was received
- **Reconnection status**: Whether automatic reconnection was successful
- **Health metrics**: Connection statistics and diagnostic information
- **Error details**: Additional technical information if available

## API Endpoints

### Test Disconnect Email

You can test the email notification system using the API endpoint:

```bash
POST /websocket/test_disconnect_email?reason=Test%20notification
```

This sends a test email without actually disconnecting the WebSocket.

### WebSocket Status

Check the current WebSocket status:

```bash
GET /websocket/status
```

## Testing

### Using the Test Script

Run the included test script to verify email functionality:

```bash
cd ShopeeAPI
python test_disconnect_email.py
```

### Using the API

1. Start the ShopeeAPI service
2. Make a POST request to `/websocket/test_disconnect_email`
3. Check your email inbox for the test notification

### Manual Testing

1. Configure email settings in `config.json`
2. Start the service with WebSocket enabled
3. Temporarily disable your internet connection
4. Check your email for disconnect notifications

## Features

### Spam Prevention

- Only one email is sent per disconnect event
- Email flag is reset when a new connection is established
- Prevents flooding your inbox with duplicate notifications

### Automatic Reconnection

- The system attempts to reconnect automatically
- Email includes whether reconnection was successful
- Connection status is continuously monitored

### Rich Diagnostics

- Connection duration tracking
- Health metrics collection
- Detailed error information
- Reconnection attempt counting

## Troubleshooting

### Email Not Sent

1. **Check Configuration**: Ensure all email fields are properly configured
2. **Verify Credentials**: Test your email credentials manually
3. **Check Logs**: Look for error messages in the application logs
4. **Gmail Security**: Make sure you're using an app password, not your regular password

### Common Issues

1. **"Authentication failed"**: Use app password instead of regular password
2. **"Email configuration incomplete"**: Check all required fields are present
3. **"Email service not available"**: EmailService import failed, check dependencies

### Log Messages

The system logs various messages to help with debugging:

- `Email service initialized for WebSocket disconnect notifications`
- `Sending WebSocket disconnect email notification...`
- `WebSocket disconnect email notification sent successfully`
- `Email configuration incomplete, skipping disconnect notification`

## Code Integration

### WebSocket Service

The feature is integrated into the `WebSocketService` class in `services/websocket.py`:

- Email service is initialized in the constructor
- Disconnect handlers call `_send_disconnect_email_notification()`
- Connection tracking maintains timestamps and metrics

### Email Service

The `EmailService` class in `services/email_service.py` provides:

- `send_websocket_disconnect_email()` method
- Rich email formatting with emojis and status indicators
- Error handling and logging

## Rate Limiting and Spam Prevention

To prevent email spam from frequent WebSocket disconnections, the system implements intelligent rate limiting:

### Rate Limiting Rules
- **Cooldown Period**: Minimum 15 minutes between disconnect emails
- **Hourly Limit**: Maximum 4 disconnect emails per hour
- **Automatic Reset**: Hourly counter resets after 60 minutes

### Rate Limiting Behavior
- First disconnect email is sent immediately
- Subsequent emails within 15 minutes are blocked
- After cooldown expires, emails are allowed again (up to hourly limit)
- Rate limiting status can be monitored via API endpoint

### Monitoring Rate Limits
Check current rate limiting status:
```bash
GET /websocket/email_rate_limit_status
```

Response includes:
- Current cooldown status
- Emails sent this hour
- Next allowed email time
- Hourly limit reset time

## Security Considerations

- Email passwords are stored in configuration files
- Use app passwords instead of regular passwords
- Consider using environment variables for sensitive data
- Email content includes diagnostic information but no sensitive credentials
- Rate limiting prevents email server abuse and spam

## Future Enhancements

Potential improvements for this feature:

1. **Email Templates**: Customizable email templates
2. **Multiple Recipients**: Support for multiple notification recipients
3. **Severity Levels**: Different notification levels based on disconnect reason
4. **Rate Limiting**: Advanced spam prevention with time-based limits
5. **Integration**: Webhook notifications or Slack integration
6. **Dashboard**: Web interface for managing email notifications

## Example Email

Here's what a typical disconnect notification email looks like:

```
Subject: ⚠️ WebSocket Disconnection Alert - Shopee API Service

WebSocket Connection Status Report ❌

📅 Timestamp: 2024-01-15 14:30:25
📡 Disconnect Reason: Socket.IO disconnected
⏰ Last Message Received: 2024-01-15 14:25:10
⏳ Connection Duration: 2:15:30
🔄 Reconnection Attempts: 1
🔴 Reconnection Status: Failed

📊 Health Metrics:
  • reconnect_attempts: 1
  • auth_failure_count: 0
  • connected_clients: 2
  • websocket_enabled: True
  • last_ping_time: 2024-01-15T14:29:45
  • connection_duration_seconds: 8130

⚠️ Please check the service status and configuration. The system will continue attempting to reconnect automatically.

---
This is an automated notification from the Shopee API WebSocket monitoring system.
```

This feature ensures you're always informed about WebSocket connection issues, helping you maintain reliable service operation.