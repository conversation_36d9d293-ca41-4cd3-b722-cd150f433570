# Standard Procedure: Adding New Dashboard Widgets

## Document Information
- **Version**: 1.0
- **Date**: 2025-01-20
- **Author**: SteamCodeTool Development Team
- **Status**: Active

## Table of Contents
1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Step-by-Step Procedure](#step-by-step-procedure)
4. [Code Templates](#code-templates)
5. [File Structure Requirements](#file-structure-requirements)
6. [Testing Procedures](#testing-procedures)
7. [Validation Checklist](#validation-checklist)
8. [Best Practices](#best-practices)
9. [Common Issues and Solutions](#common-issues-and-solutions)
10. [Approval Process](#approval-process)

---

## Overview

This document provides standardized procedures for adding new dashboard widgets to the SteamCodeTool pluggable dashboard system. Following these procedures ensures consistency, maintainability, and proper integration with the existing system.

### Scope
- Creating new dashboard widgets
- Modifying existing plugins to support widgets
- Creating new plugins with widget capabilities
- Widget testing and validation

### References
- `docs/PLUGGABLE_DASHBOARD_SYSTEM.md` - System Architecture
- `core/plugin_manager.py` - Plugin Interface Reference
- `static/js/widget-loader.js` - Widget Loader System

---

## Prerequisites

### Technical Requirements
1. **Development Environment**
   - Python 3.8+
   - Node.js (for JavaScript development)
   - Active SteamCodeTool instance
   - Admin access to the system

2. **Knowledge Requirements**
   - Python Flask framework
   - JavaScript ES6+ features
   - HTML/CSS (Tailwind CSS)
   - REST API design
   - Plugin architecture concepts

3. **Access Requirements**
   - Write access to plugin directories
   - Admin credentials for testing
   - Development environment setup

---

## Step-by-Step Procedure

### Phase 1: Planning and Design

#### Step 1.1: Define Widget Requirements
1. **Widget Purpose Statement**
   ```
   Purpose: [Clear description of what the widget does]
   Data Source: [Where data comes from]
   Update Frequency: [How often data refreshes]
   User Interaction: [What users can do with the widget]
   ```

2. **Widget Specifications**
   - Widget ID: `[plugin-name]-[widget-name]`
   - Title: User-friendly display name
   - Position: `header`, `main`, `sidebar`, or `footer`
   - Size: `small`, `medium`, `large`, or `full`
   - Refresh Interval: Time in seconds (or `null` for no auto-refresh)

3. **Data Requirements**
   ```json
   {
     "data_structure": {
       "field1": "description",
       "field2": "description"
     },
     "permissions": ["required", "permissions"],
     "dependencies": ["other", "widgets"]
   }
   ```

#### Step 1.2: Choose Implementation Approach
- **New Plugin**: Create entirely new plugin with widget
- **Existing Plugin**: Add widget to existing plugin
- **Core Widget**: Add to core plugin (requires approval)

### Phase 2: Backend Implementation

#### Step 2.1: Update Plugin Class
1. **Navigate to Plugin Directory**
   ```bash
   cd plugins/[plugin-name]/
   ```

2. **Modify `plugin.py`**
   - Import `DashboardWidget` from `core.plugin_manager`
   - Implement `get_dashboard_widgets()` method
   - Implement `get_widget_data()` method
   - Add widget route handlers

3. **Implementation Template**
   ```python
   from core.plugin_manager import DashboardWidget
   
   def get_dashboard_widgets(self) -> list:
       widgets = []
       
       widget = DashboardWidget(
           widget_id="[widget-id]",
           title="[Widget Title]",
           position="main",
           order=50,
           size="medium"
       )
       widget.data_endpoint = f"/api/{self.name}/widget/[widget-id]"
       widget.refresh_interval = 300  # 5 minutes
       widgets.append(widget)
       
       return widgets
   
   def get_widget_data(self, widget_id: str) -> dict:
       if widget_id == "[widget-id]":
           return self._get_[widget_id]_data()
       return {"error": f"Unknown widget: {widget_id}"}
   ```

#### Step 2.2: Create Widget Data Methods
1. **Add Data Retrieval Method**
   ```python
   def _get_[widget_id]_data(self) -> dict:
       try:
           # Implement data collection logic
           data = {
               "timestamp": datetime.now().isoformat(),
               "status": "success",
               # Add your data fields here
           }
           return data
       except Exception as e:
           logger.error(f"Error getting widget data: {e}")
           return {
               "timestamp": datetime.now().isoformat(),
               "status": "error",
               "error": str(e)
           }
   ```

#### Step 2.3: Add API Routes
1. **Create Widget Route Handler**
   ```python
   @self.blueprint.route('/widget/[widget-id]')
   def widget_[widget_id]():
       try:
           data = self._get_[widget_id]_data()
           return jsonify({"success": True, "data": data})
       except Exception as e:
           logger.error(f"Widget API error: {e}")
           return jsonify({"success": False, "error": str(e)}), 500
   ```

### Phase 3: Frontend Implementation

#### Step 3.1: Create Widget JavaScript File
1. **Create Widget File**
   ```bash
   touch static/js/widgets/[widget-id].js
   ```

2. **Implement Widget Class**
   ```javascript
   /**
    * [Widget Name] Widget
    * [Description of what this widget does]
    */
   
   class [WidgetName]Widget extends window.DashboardWidget {
       constructor(config) {
           super(config);
           this.widgetData = null;
       }
   
       async loadData() {
           try {
               const response = await fetch(this.config.data_endpoint);
               const result = await response.json();
               
               if (result.success) {
                   this.widgetData = result.data;
                   this.lastRefresh = new Date();
               } else {
                   throw new Error(result.error || 'Failed to load data');
               }
           } catch (error) {
               console.error('Error loading widget data:', error);
               this.showError(error.message);
           }
       }
   
       render() {
           if (!this.container || !this.widgetData) return;
   
           this.container.innerHTML = `
               <div class="bg-white overflow-hidden shadow rounded-lg">
                   <div class="p-5">
                       <h3 class="text-lg font-medium text-gray-900">${this.config.title}</h3>
                       <div class="mt-3">
                           <!-- Widget content here -->
                       </div>
                   </div>
               </div>
           `;
           
           // Add event listeners if needed
           this.attachEventListeners();
       }
   
       attachEventListeners() {
           // Implement event handlers
       }
   
       shouldRefresh() {
           // Custom refresh logic if needed
           return super.shouldRefresh();
       }
   
       destroy() {
           // Cleanup code
       }
   }
   
   // Register widget
   window.DashboardWidgets['[widget-id]'] = [WidgetName]Widget;
   ```

#### Step 3.2: Implement Widget Styling
1. **Use Tailwind CSS Classes**
   - Follow existing design patterns
   - Ensure responsive design
   - Use consistent spacing and colors

2. **Common Widget Patterns**
   ```html
   <!-- Statistics Card -->
   <div class="bg-white overflow-hidden shadow rounded-lg">
       <div class="p-5">
           <div class="flex items-center">
               <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                   <i class="fas fa-chart-bar text-white text-2xl"></i>
               </div>
               <div class="ml-5 w-0 flex-1">
                   <dl>
                       <dt class="text-sm font-medium text-gray-500 truncate">
                           Metric Name
                       </dt>
                       <dd class="text-3xl font-semibold text-gray-900">
                           ${value}
                       </dd>
                   </dl>
               </div>
           </div>
       </div>
   </div>
   
   <!-- Data Table -->
   <div class="bg-white shadow overflow-hidden sm:rounded-lg">
       <table class="min-w-full divide-y divide-gray-200">
           <thead class="bg-gray-50">
               <!-- Table headers -->
           </thead>
           <tbody class="bg-white divide-y divide-gray-200">
               <!-- Table rows -->
           </tbody>
       </table>
   </div>
   ```

### Phase 4: Testing

#### Step 4.1: Unit Testing
1. **Test Widget Data Methods**
   ```python
   def test_widget_data_method(self):
       # Test data retrieval
       data = self._get_[widget_id]_data()
       self.assertIsInstance(data, dict)
       self.assertIn('timestamp', data)
       self.assertIn('status', data)
   ```

2. **Test API Endpoints**
   ```python
   def test_widget_api_endpoint(self):
       response = self.client.get('/api/[plugin]/widget/[widget-id]')
       self.assertEqual(response.status_code, 200)
       data = response.get_json()
       self.assertTrue(data['success'])
   ```

#### Step 4.2: Integration Testing
1. **Test Widget Loading**
   - Access dashboard as admin
   - Verify widget appears in correct position
   - Check widget loads data successfully
   - Test auto-refresh functionality

2. **Test Plugin Enable/Disable**
   - Disable plugin in admin panel
   - Verify widget disappears from dashboard
   - Enable plugin again
   - Verify widget reappears

#### Step 4.3: Browser Testing
1. **Cross-Browser Compatibility**
   - Chrome
   - Firefox
   - Safari
   - Edge

2. **Responsive Design**
   - Desktop (1920x1080)
   - Tablet (768x1024)
   - Mobile (375x667)

---

## Code Templates

### Complete Plugin Widget Implementation

```python
# plugins/[plugin-name]/plugin.py

from flask import Blueprint, jsonify
from core.plugin_manager import PluginInterface, DashboardWidget
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class Plugin(PluginInterface):
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "[plugin-name]"
        self.version = "1.0.0"
        self.description = "[Plugin description]"
        self.blueprint = Blueprint(self.name, __name__)
        self._setup_routes()
    
    def get_dashboard_widgets(self) -> list:
        widgets = []
        
        # Example widget
        example_widget = DashboardWidget(
            widget_id="example-widget",
            title="Example Widget",
            position="main",
            order=30,
            size="medium"
        )
        example_widget.data_endpoint = f"/api/{self.name}/widget/example"
        example_widget.refresh_interval = 300
        widgets.append(example_widget)
        
        return widgets
    
    def get_widget_data(self, widget_id: str) -> dict:
        if widget_id == "example-widget":
            return self._get_example_data()
        return {"error": f"Unknown widget: {widget_id}"}
    
    def _setup_routes(self):
        @self.blueprint.route('/widget/example')
        def widget_example():
            try:
                data = self._get_example_data()
                return jsonify({"success": True, "data": data})
            except Exception as e:
                logger.error(f"Widget error: {e}")
                return jsonify({"success": False, "error": str(e)}), 500
    
    def _get_example_data(self) -> dict:
        return {
            "timestamp": datetime.now().isoformat(),
            "value": 42,
            "status": "active"
        }
    
    # Required abstract methods
    def initialize(self) -> bool:
        logger.info(f"{self.name} plugin initialized")
        return True
    
    def shutdown(self) -> bool:
        return True
    
    def get_blueprint(self):
        return self.blueprint
    
    def get_config_schema(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "enabled": {"type": "boolean", "default": True}
            }
        }
```

### Complete JavaScript Widget Implementation

```javascript
// static/js/widgets/example-widget.js

/**
 * Example Widget
 * Demonstrates widget implementation patterns
 */

class ExampleWidget extends window.DashboardWidget {
    constructor(config) {
        super(config);
        this.data = null;
        this.refreshTimer = null;
    }

    async loadData() {
        this.showLoading();
        
        try {
            const response = await fetch(this.config.data_endpoint);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.data = result.data;
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load data');
            }
        } catch (error) {
            console.error(`Error loading ${this.config.widget_id} data:`, error);
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container) return;

        if (!this.data) {
            this.showLoading();
            return;
        }

        this.container.innerHTML = `
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex justify-between items-start">
                        <h3 class="text-lg font-medium text-gray-900">${this.config.title}</h3>
                        <button class="text-gray-400 hover:text-gray-600 refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    
                    <div class="mt-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">Value</p>
                                <p class="text-2xl font-semibold text-gray-900">${this.data.value}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Status</p>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusColor()}">
                                    ${this.data.status}
                                </span>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-xs text-gray-500">
                            Last updated: ${new Date(this.data.timestamp).toLocaleString()}
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.attachEventListeners();
    }

    attachEventListeners() {
        const refreshBtn = this.container.querySelector('.refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refresh());
        }
    }

    getStatusColor() {
        switch (this.data.status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'warning': return 'bg-yellow-100 text-yellow-800';
            case 'error': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    shouldRefresh() {
        if (!this.lastRefresh) return true;
        const now = new Date();
        const diff = now - this.lastRefresh;
        return diff > (this.config.refresh_interval * 1000);
    }

    destroy() {
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }
    }
}

// Register widget
window.DashboardWidgets['example-widget'] = ExampleWidget;
```

---

## File Structure Requirements

```
plugins/[plugin-name]/
├── __init__.py                 # Plugin package init
├── plugin.py                   # Main plugin class
├── routes/
│   ├── __init__.py
│   └── [plugin]_routes.py     # Plugin routes
├── services/
│   ├── __init__.py
│   └── [plugin]_service.py    # Business logic
└── templates/                  # Plugin templates (if needed)

static/js/widgets/
└── [widget-id].js             # Widget JavaScript implementation

docs/
└── [PLUGIN_NAME]_WIDGETS.md   # Widget documentation
```

---

## Testing Procedures

### Pre-Deployment Testing Checklist

#### Backend Testing
- [ ] Plugin loads without errors
- [ ] Widget data method returns valid JSON
- [ ] API endpoint responds with 200 status
- [ ] Error handling works correctly
- [ ] Logging is implemented
- [ ] Configuration schema is valid

#### Frontend Testing
- [ ] Widget JavaScript loads without errors
- [ ] Widget renders correctly in all positions
- [ ] Widget handles loading states
- [ ] Widget handles error states
- [ ] Auto-refresh works (if enabled)
- [ ] Manual refresh works
- [ ] Event handlers work correctly
- [ ] Widget cleanup on destruction

#### Integration Testing
- [ ] Widget appears when plugin enabled
- [ ] Widget disappears when plugin disabled
- [ ] Widget data loads from API
- [ ] Widget respects configuration settings
- [ ] Multiple widgets don't conflict
- [ ] Performance is acceptable

#### Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers

#### Accessibility Testing
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast meets standards
- [ ] Focus indicators visible

---

## Validation Checklist

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Error handling is comprehensive
- [ ] Logging is appropriate and informative
- [ ] Security considerations addressed
- [ ] Performance optimizations applied
- [ ] Documentation is complete

### Functionality
- [ ] Widget displays correct data
- [ ] All user interactions work
- [ ] Auto-refresh timing is correct
- [ ] Error states are user-friendly
- [ ] Loading states are informative

### Design
- [ ] Follows existing design patterns
- [ ] Responsive design implemented
- [ ] Accessibility standards met
- [ ] Cross-browser compatibility
- [ ] Performance is acceptable

---

## Best Practices

### Code Organization
1. **Separation of Concerns**
   - Keep data logic in services
   - Keep UI logic in widgets
   - Keep routing in route handlers

2. **Error Handling**
   - Always wrap API calls in try-catch
   - Provide user-friendly error messages
   - Log detailed errors for debugging

3. **Performance**
   - Use appropriate refresh intervals
   - Implement loading states
   - Clean up resources on destroy

### Security
1. **Input Validation**
   - Validate all user inputs
   - Sanitize data before display
   - Use parameterized queries

2. **Authentication**
   - All widget endpoints require authentication
   - Check permissions appropriately
   - Use CSRF protection

### User Experience
1. **Feedback**
   - Show loading states
   - Provide error messages
   - Confirm destructive actions

2. **Performance**
   - Keep widgets lightweight
   - Use efficient data structures
   - Implement proper caching

---

## Common Issues and Solutions

### Issue 1: Widget Not Appearing
**Symptoms**: Widget doesn't show on dashboard
**Causes**:
- Plugin not enabled
- JavaScript file not found
- Widget not registered
- API endpoint returns error

**Solutions**:
1. Check plugin is enabled in admin panel
2. Verify JavaScript file exists and loads
3. Check browser console for errors
4. Test API endpoint directly

### Issue 2: Data Not Loading
**Symptoms**: Widget shows but no data
**Causes**:
- API endpoint error
- Authentication issues
- Network problems
- Data format mismatch

**Solutions**:
1. Check API endpoint response
2. Verify authentication
3. Check network tab in dev tools
4. Validate data format

### Issue 3: Auto-refresh Not Working
**Symptoms**: Widget data doesn't update automatically
**Causes**:
- Refresh interval not set
- shouldRefresh() returns false
- JavaScript errors
- API errors

**Solutions**:
1. Set refresh_interval in widget config
2. Implement shouldRefresh() method
3. Check browser console for errors
4. Test API endpoint reliability

### Issue 4: Widget Layout Issues
**Symptoms**: Widget doesn't display correctly
**Causes**:
- CSS conflicts
- Incorrect size setting
- Responsive design issues
- Browser-specific problems

**Solutions**:
1. Use standard Tailwind classes
2. Test on different screen sizes
3. Check CSS specificity
4. Test on multiple browsers

---

## Approval Process

### Development Review
1. **Code Review**
   - Peer review by senior developer
   - Architecture review for new patterns
   - Security review for data handling
   - Performance review for optimization

2. **Testing Review**
   - QA testing on staging environment
   - Cross-browser testing
   - Accessibility testing
   - Performance testing

### Approval Workflow
1. **Developer**: Implements widget following procedures
2. **Peer Review**: Code review and testing
3. **Tech Lead**: Architecture and security review
4. **QA**: Comprehensive testing
5. **Product Owner**: Feature acceptance
6. **Release Manager**: Production deployment

### Documentation Requirements
- [ ] Widget functionality documented
- [ ] API endpoints documented
- [ ] Configuration options documented
- [ ] User guide updated (if needed)
- [ ] Change log updated

---

## Revision History

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0 | 2025-01-20 | Development Team | Initial version |

---

## Appendices

### Appendix A: Widget Configuration Reference
```typescript
interface DashboardWidget {
    widget_id: string;           // Unique identifier
    title: string;               // Display title
    position: 'header' | 'main' | 'sidebar' | 'footer';
    order: number;               // Display order (lower = higher priority)
    size: 'small' | 'medium' | 'large' | 'full';
    script_path: string;         // Path to JavaScript file
    template_id: string;         // DOM element ID
    data_endpoint: string;       // API endpoint for data
    refresh_interval?: number;   // Refresh interval in seconds
    dependencies: string[];      // Widget dependencies
    permissions: string[];       // Required permissions
}
```

### Appendix B: JavaScript Widget Interface
```typescript
interface WidgetInterface {
    config: DashboardWidget;
    container: HTMLElement;
    data: any;
    lastRefresh: Date;
    
    init(): Promise<void>;
    loadData(): Promise<void>;
    render(): void;
    refresh(): Promise<void>;
    shouldRefresh(): boolean;
    destroy(): void;
    showError(message: string): void;
    showLoading(): void;
}
```

### Appendix C: API Response Format
```typescript
interface WidgetAPIResponse {
    success: boolean;
    data?: any;
    error?: string;
    timestamp?: string;
}
``` 