import os
import logging
from logging.handlers import RotatingFileHandler

LOG_LEVEL = os.environ.get('LOG_LEVEL', 'WARNING').upper()  # Changed default to WARNING
WEBSOCKET_LOG_LEVEL = os.environ.get('WEBSOCKET_LOG_LEVEL', 'ERROR').upper()  # Separate level for WebSocket
WEBHOOK_LOG_LEVEL = os.environ.get('WEBHOOK_LOG_LEVEL', 'ERROR').upper()  # Separate level for Webhook

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            '()': 'uvicorn.logging.DefaultFormatter',
            'fmt': '%(levelprefix)s %(asctime)s - %(name)s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'access': {
            '()': 'uvicorn.logging.AccessFormatter',
            'fmt': '%(levelprefix)s %(asctime)s - %(client_addr)s - "%(request_line)s" %(status_code)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'file': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
    'handlers': {
        'default': {
            'formatter': 'default',
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stderr',
        },
        'access': {
            'formatter': 'access',
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/shopee_api.log',
            'maxBytes': 5242880,  # 5MB per file
            'backupCount': 3,     # Keep 3 backup files (total ~20MB max)
            'formatter': 'file',
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/shopee_api_errors.log',
            'maxBytes': 2097152,  # 2MB per file
            'backupCount': 2,     # Keep 2 backup files
            'formatter': 'file',
            'level': 'ERROR',
        },
    },
    'loggers': {
        '': { # Root logger
            'handlers': ['default', 'file', 'error_file'],
            'level': LOG_LEVEL,
        },
        'uvicorn.error': {
            'level': 'WARNING',  # Reduced from INFO
        },
        'uvicorn.access': {
            'handlers': ['access'],
            'level': 'WARNING',  # Reduced from INFO
            'propagate': False
        },
        # Specific loggers for noisy modules
        'services.websocket': {
            'level': WEBSOCKET_LOG_LEVEL,  # Default ERROR - only log errors
            'propagate': True
        },
        'utils.webhook': {
            'level': WEBHOOK_LOG_LEVEL,  # Default ERROR - only log errors
            'propagate': True
        },
        # Suppress aiohttp client logs
        'aiohttp.client': {
            'level': 'ERROR',
            'propagate': True
        },
        'aiohttp.access': {
            'level': 'ERROR',
            'propagate': True
        },
    },
}