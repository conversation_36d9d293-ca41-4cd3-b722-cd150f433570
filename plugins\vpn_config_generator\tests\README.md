# VPN Config Generator Plugin - Test Suite

This directory contains a comprehensive test suite for the VPN Config Generator Plugin.

## Test Structure

The test suite is organized into the following modules:

### Core Tests
- **`test_models.py`** - Tests for data models (VPNConfigRequest, VPNConfigResponse, etc.)
- **`test_services.py`** - Tests for service layer (VPNConfigGeneratorService)
- **`test_routes.py`** - Tests for Flask routes and API endpoints
- **`test_plugin.py`** - Tests for the main plugin class and lifecycle

### Integration Tests
- **`test_integration.py`** - End-to-end integration tests

## Running Tests

### Run All Tests
```bash
# From the plugin directory
python run_tests.py

# With verbose output
python run_tests.py --verbose
```

### Run Specific Test Suites
```bash
# Test models only
python run_tests.py --test models

# Test services only
python run_tests.py --test services

# Test routes only
python run_tests.py --test routes

# Test plugin only
python run_tests.py --test plugin

# Test integration only
python run_tests.py --test integration
```

### Check Dependencies
```bash
python run_tests.py --check-deps
```

## Test Coverage

### Models (`test_models.py`)
- ✅ VPNConfigRequest creation and validation
- ✅ VPNConfigResponse success and error scenarios
- ✅ VPNAPIConfig configuration management
- ✅ ConfigTemplate CRUD operations
- ✅ Data serialization (to_dict/from_dict)

### Services (`test_services.py`)
- ✅ Service initialization and configuration
- ✅ Configuration loading and saving
- ✅ Template management (add, get, update, delete)
- ✅ VPN config generation (success and failure scenarios)
- ✅ API connection testing
- ✅ Fallback API integration
- ✅ Error handling and edge cases

### Routes (`test_routes.py`)
- ✅ Dashboard route rendering
- ✅ API endpoint responses
- ✅ Config generation endpoints
- ✅ Template management endpoints
- ✅ Connection testing endpoints
- ✅ Error handling and validation
- ✅ JSON request/response handling

### Plugin (`test_plugin.py`)
- ✅ Plugin initialization and shutdown
- ✅ Configuration schema validation
- ✅ Chat command registration
- ✅ Command handling logic
- ✅ Blueprint creation
- ✅ Admin route registration
- ✅ Error handling in plugin lifecycle

### Integration (`test_integration.py`)
- ✅ Complete plugin lifecycle
- ✅ End-to-end config generation workflow
- ✅ Chat command integration
- ✅ Template management workflow
- ✅ Configuration persistence
- ✅ Error handling in real scenarios

## Test Environment

### Requirements
- Python 3.7+
- unittest (built-in)
- unittest.mock (built-in)
- tempfile (built-in)
- json (built-in)

### Optional Dependencies
- Flask (for route testing)
- requests (for API testing)

### Mock Objects
The tests use extensive mocking to isolate components and avoid external dependencies:
- Mock plugin manager
- Mock Flask application
- Mock HTTP requests
- Mock file system operations
- Mock external API responses

## Test Data

### Sample Configurations
```python
# API Configuration
api_config = {
    'enabled': True,
    'use_vpn_plugin_api': False,
    'fallback_api_endpoint': 'https://test.api.com',
    'fallback_username': 'admin',
    'fallback_password': 'admin123',
    'timeout': 30
}

# Generator Settings
generator_settings = {
    'default_server': 'server11',
    'default_days': '30',
    'default_telco': 'digi',
    'default_plan': 'unlimited',
    'auto_generate_username': True,
    'username_prefix': 'user'
}
```

### Sample Requests
```python
# VPN Config Request
request = VPNConfigRequest(
    server="server11",
    days="30",
    telco="digi",
    plan="unlimited",
    username="testuser"
)

# Config Template
template = ConfigTemplate(
    id="test_template",
    name="Test Template",
    description="Test description",
    server="server11",
    days="30",
    telco="digi",
    plan="unlimited"
)
```

## Continuous Integration

### GitHub Actions
The test suite is designed to work with GitHub Actions:

```yaml
name: VPN Config Generator Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
    - name: Run tests
      run: |
        cd plugins/vpn_config_generator
        python run_tests.py
```

### Local Development
For local development, you can run tests automatically on file changes:

```bash
# Install watchdog
pip install watchdog

# Run tests on file changes (example script)
python -c "
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import subprocess

class TestHandler(FileSystemEventHandler):
    def on_modified(self, event):
        if event.src_path.endswith('.py'):
            subprocess.run(['python', 'run_tests.py'])

observer = Observer()
observer.schedule(TestHandler(), '.', recursive=True)
observer.start()
try:
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    observer.stop()
observer.join()
"
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure the plugin directory is in the Python path
   - Check that all required modules are installed

2. **Mock Failures**
   - Verify mock objects are properly configured
   - Check that mock return values match expected types

3. **File System Errors**
   - Ensure temporary directories are properly cleaned up
   - Check file permissions in test environment

4. **Flask Test Client Issues**
   - Verify Flask app is properly configured for testing
   - Check that blueprints are registered correctly

### Debug Mode
For debugging test failures, use verbose mode:

```bash
python run_tests.py --verbose --test services
```

This will show detailed test output and failure tracebacks.

## Contributing

When adding new tests:

1. Follow the existing naming conventions
2. Use descriptive test method names
3. Include docstrings for test methods
4. Mock external dependencies
5. Test both success and failure scenarios
6. Update this README if adding new test categories

## Test Metrics

The test suite aims for:
- **Code Coverage**: >90%
- **Test Execution Time**: <30 seconds
- **Test Reliability**: 100% pass rate in clean environment
- **Maintainability**: Clear, readable test code
