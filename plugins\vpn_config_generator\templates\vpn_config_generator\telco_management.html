{% extends "base.html" %}

{% block title %}VPN Configuration Templates{% endblock %}

{% block extra_head %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<style>
    .telco-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        overflow: hidden;
    }

    .telco-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .telco-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
    }

    .plan-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.75rem;
        transition: all 0.2s ease;
    }

    .plan-card:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 600;
    }

    .status-enabled {
        background-color: #dcfce7;
        color: #166534;
    }

    .status-disabled {
        background-color: #f3f4f6;
        color: #6b7280;
    }

    .action-btn {
        padding: 0.5rem;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        margin: 0 0.25rem;
    }

    .action-btn:hover {
        transform: scale(1.05);
    }

    .btn-edit {
        background-color: #fbbf24;
        color: white;
    }

    .btn-edit:hover {
        background-color: #f59e0b;
    }

    .btn-delete {
        background-color: #ef4444;
        color: white;
    }

    .btn-delete:hover {
        background-color: #dc2626;
    }

    .btn-add {
        background-color: #10b981;
        color: white;
    }

    .btn-add:hover {
        background-color: #059669;
    }

    .usage-info {
        background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
        border: none;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #d1d5db;
    }
</style>
{% endblock %}

{% block header %}VPN Configuration Templates{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Telco Management</h2>
                <p class="text-gray-600 mt-1">Manage VPN configuration templates by telco and plan</p>
            </div>
            <button type="button" class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" onclick="showCreateTelcoModal()">
                <i class="fas fa-plus mr-2"></i>Add Telco
            </button>
        </div>

        <!-- Usage Information -->
        <div class="usage-info">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">Command Usage</h4>
                    <p class="text-gray-700 mb-2">
                        <code class="bg-white px-3 py-1 rounded-md text-sm font-mono border">#v &lt;server&gt; &lt;days&gt; &lt;telco&gt; &lt;plan&gt;</code>
                    </p>
                    <p class="text-sm text-gray-600">
                        <strong>Example:</strong> <code class="bg-white px-2 py-1 rounded text-xs font-mono border">#v server11 30 digi unlimited</code>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Telco List -->
    <div id="telco-list" class="space-y-6">
        <!-- Telco cards will be loaded here -->
    </div>
</div>

<!-- Create/Edit Telco Modal -->
<div class="modal fade" id="telcoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
            <div class="modal-header bg-gradient-to-r from-blue-500 to-purple-600 text-white" style="border-radius: 16px 16px 0 0;">
                <h4 class="modal-title font-semibold" id="telcoModalTitle">Add Telco</h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-6">
                <form id="telcoForm">
                    <div class="mb-4">
                        <label for="telcoName" class="form-label font-semibold text-gray-700">Name</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all" id="telcoName" required placeholder="Enter telco name">
                    </div>
                    <div class="mb-4">
                        <label for="telcoDescription" class="form-label font-semibold text-gray-700">Description</label>
                        <textarea class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all" id="telcoDescription" rows="3" placeholder="Enter description (optional)"></textarea>
                    </div>
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="telcoEnabled" checked>
                            <label class="form-check-label font-semibold text-gray-700" for="telcoEnabled">
                                Enabled
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-6 pt-0">
                <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all" onclick="saveTelco()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Plan Modal -->
<div class="modal fade" id="planModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
            <div class="modal-header bg-gradient-to-r from-green-500 to-blue-600 text-white" style="border-radius: 16px 16px 0 0;">
                <h4 class="modal-title font-semibold" id="planModalTitle">Add Plan</h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-6">
                <form id="planForm">
                    <div class="mb-4">
                        <label for="planName" class="form-label font-semibold text-gray-700">Name</label>
                        <input type="text" class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all" id="planName" required placeholder="Enter plan name">
                    </div>
                    <div class="mb-4">
                        <label for="planDescription" class="form-label font-semibold text-gray-700">Description</label>
                        <textarea class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all" id="planDescription" rows="2" placeholder="Enter description (optional)"></textarea>
                    </div>
                    <div class="mb-4">
                        <label for="planTemplate" class="form-label font-semibold text-gray-700">Configuration Template</label>
                        <textarea class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all font-mono text-sm" id="planTemplate" rows="4" required placeholder="vless://{uuid}@{server}:80?..."></textarea>
                        <div class="mt-2 p-3 bg-blue-50 rounded-lg">
                            <small class="text-blue-700 font-medium">
                                <i class="fas fa-info-circle mr-1"></i>Available variables:
                            </small>
                            <div class="mt-1 text-xs text-blue-600">
                                <code class="bg-white px-1 rounded mr-1">{uuid}</code>
                                <code class="bg-white px-1 rounded mr-1">{server}</code>
                                <code class="bg-white px-1 rounded mr-1">{server_id}</code>
                                <code class="bg-white px-1 rounded mr-1">{identity}</code>
                                <code class="bg-white px-1 rounded mr-1">{username}</code>
                                <code class="bg-white px-1 rounded mr-1">{days}</code>
                                <code class="bg-white px-1 rounded mr-1">{telco}</code>
                                <code class="bg-white px-1 rounded mr-1">{plan}</code>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="planInfoMessage" class="form-label font-semibold text-gray-700">Custom Info Message Template</label>
                        <textarea class="form-control border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all font-mono text-sm" id="planInfoMessage" rows="8" placeholder="🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️&#10;&#10;📧 Email: {email}&#10;👤 Shopee User: {shopee_username}&#10;🖥️ Server: {server_number}&#10;📅 Created: {created_datetime}&#10;⏰ Expires: {expired_date_formatted}&#10;📡 Telco: {telco}&#10;📋 Plan: {plan}&#10;⏳ Validity: {validity}&#10;&#10;🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️"></textarea>
                        <div class="mt-2 p-3 bg-purple-50 rounded-lg">
                            <small class="text-purple-700 font-medium">
                                <i class="fas fa-magic mr-1"></i>Available info message variables:
                            </small>
                            <div class="mt-1 text-xs text-purple-600 space-y-2">
                                <div>
                                    <strong>Core Variables:</strong><br>
                                    <code class="bg-white px-1 rounded mr-1">{uuid}</code>
                                    <code class="bg-white px-1 rounded mr-1">{username}</code>
                                    <code class="bg-white px-1 rounded mr-1">{shopee_username}</code>
                                    <code class="bg-white px-1 rounded mr-1">{email}</code>
                                    <code class="bg-white px-1 rounded mr-1">{days}</code>
                                    <code class="bg-white px-1 rounded mr-1">{validity}</code>
                                </div>
                                <div>
                                    <strong>Server Variables:</strong><br>
                                    <code class="bg-white px-1 rounded mr-1">{server}</code>
                                    <code class="bg-white px-1 rounded mr-1">{server_id}</code>
                                    <code class="bg-white px-1 rounded mr-1">{server_name}</code>
                                    <code class="bg-white px-1 rounded mr-1">{server_number}</code>
                                </div>
                                <div>
                                    <strong>Date Variables:</strong><br>
                                    <code class="bg-white px-1 rounded mr-1">{created_date}</code>
                                    <code class="bg-white px-1 rounded mr-1">{expired_date}</code>
                                    <code class="bg-white px-1 rounded mr-1">{created_date_formatted}</code>
                                    <code class="bg-white px-1 rounded mr-1">{expired_date_formatted}</code>
                                </div>
                                <div>
                                    <strong>Time Variables:</strong><br>
                                    <code class="bg-white px-1 rounded mr-1">{created_datetime}</code>
                                    <code class="bg-white px-1 rounded mr-1">{expired_datetime}</code>
                                    <code class="bg-white px-1 rounded mr-1">{created_time}</code>
                                </div>
                                <div>
                                    <strong>Config Variables:</strong><br>
                                    <code class="bg-white px-1 rounded mr-1">{telco}</code>
                                    <code class="bg-white px-1 rounded mr-1">{plan}</code>
                                    <code class="bg-white px-1 rounded mr-1">{config_id}</code>
                                    <code class="bg-white px-1 rounded mr-1">{short_uuid}</code>
                                </div>
                            </div>
                            <div class="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-700">
                                <strong>💡 Example Usage:</strong><br>
                                📧 Email: {email}<br>
                                👤 User: {shopee_username}<br>
                                🖥️ Server: {server_number}<br>
                                📅 Created: {created_datetime}<br>
                                ⏰ Expires: {expired_date_formatted}
                            </div>
                            <div class="mt-2 text-xs text-purple-600">
                                <strong>Note:</strong> Leave empty to use enhanced default message template
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="planEnabled" checked>
                            <label class="form-check-label font-semibold text-gray-700" for="planEnabled">
                                Enabled
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-6 pt-0">
                <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn bg-gradient-to-r from-green-500 to-blue-600 text-white px-6 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all" onclick="savePlan()">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentTelcoId = null;
let currentPlanId = null;
let editMode = false;

$(document).ready(function() {
    loadTelcos();
});

function loadTelcos() {
    $.get('/vpn-config-generator/api/telcos')
        .done(function(response) {
            if (response.success) {
                displayTelcos(response.telcos);
            } else {
                toastr.error('Failed to load telcos: ' + response.error);
            }
        })
        .fail(function() {
            toastr.error('Failed to load telcos');
        });
}

function displayTelcos(telcos) {
    const container = $('#telco-list');
    container.empty();

    if (Object.keys(telcos).length === 0) {
        container.html(`
            <div class="empty-state">
                <i class="fas fa-network-wired"></i>
                <h3 class="text-xl font-semibold mb-2">No Telcos Configured</h3>
                <p class="text-gray-500 mb-4">Get started by adding your first telco configuration</p>
                <button class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all" onclick="showCreateTelcoModal()">
                    <i class="fas fa-plus mr-2"></i>Add Your First Telco
                </button>
            </div>
        `);
        return;
    }

    for (const [telcoId, telco] of Object.entries(telcos)) {
        const telcoCard = createTelcoCard(telcoId, telco);
        container.append(telcoCard);
    }
}

function createTelcoCard(telcoId, telco) {
    const planCount = Object.keys(telco.plans || {}).length;
    const statusBadge = telco.enabled ?
        '<span class="status-badge status-enabled">Enabled</span>' :
        '<span class="status-badge status-disabled">Disabled</span>';

    return `
        <div class="telco-card bg-white shadow-lg">
            <div class="telco-header">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold">${telco.name}</h3>
                            ${statusBadge}
                        </div>
                        <p class="text-blue-100 text-sm">${telco.description || 'No description provided'}</p>
                        <div class="flex items-center mt-3 text-blue-100">
                            <i class="fas fa-layer-group mr-2"></i>
                            <span class="text-sm">${planCount} plan${planCount !== 1 ? 's' : ''} configured</span>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button class="action-btn btn-add" onclick="showCreatePlanModal('${telcoId}')" title="Add Plan">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="action-btn btn-edit" onclick="editTelco('${telcoId}')" title="Edit Telco">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn btn-delete" onclick="deleteTelco('${telcoId}')" title="Delete Telco">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-semibold text-gray-800">Configuration Plans</h4>
                    ${planCount > 0 ? `<span class="text-sm text-gray-500">${planCount} plan${planCount !== 1 ? 's' : ''}</span>` : ''}
                </div>
                <div id="plans-${telcoId}">
                    ${createPlansGrid(telcoId, telco.plans || {})}
                </div>
            </div>
        </div>
    `;
}

function createPlansGrid(telcoId, plans) {
    if (Object.keys(plans).length === 0) {
        return `
            <div class="text-center py-8">
                <i class="fas fa-layer-group text-4xl text-gray-300 mb-3"></i>
                <p class="text-gray-500 mb-4">No plans configured for this telco</p>
                <button class="bg-gradient-to-r from-green-500 to-blue-600 text-white px-4 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all text-sm" onclick="showCreatePlanModal('${telcoId}')">
                    <i class="fas fa-plus mr-2"></i>Add First Plan
                </button>
            </div>
        `;
    }

    let grid = '<div class="grid gap-4">';

    for (const [planId, plan] of Object.entries(plans)) {
        const statusBadge = plan.enabled ?
            '<span class="status-badge status-enabled">Enabled</span>' :
            '<span class="status-badge status-disabled">Disabled</span>';

        grid += `
            <div class="plan-card">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                            <h5 class="font-semibold text-gray-800">${plan.name}</h5>
                            ${statusBadge}
                        </div>
                        <p class="text-gray-600 text-sm mb-3">${plan.description || 'No description provided'}</p>
                        <div class="bg-gray-100 rounded-md p-2">
                            <code class="text-xs text-gray-700 break-all">${plan.template ? plan.template.substring(0, 80) + (plan.template.length > 80 ? '...' : '') : 'No template'}</code>
                        </div>
                    </div>
                    <div class="flex gap-1 ml-4">
                        <button class="action-btn btn-edit" onclick="editPlan('${telcoId}', '${planId}')" title="Edit Plan">
                            <i class="fas fa-edit text-sm"></i>
                        </button>
                        <button class="action-btn btn-delete" onclick="deletePlan('${telcoId}', '${planId}')" title="Delete Plan">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    grid += '</div>';
    return grid;
}

function showCreateTelcoModal() {
    editMode = false;
    currentTelcoId = null;
    $('#telcoModalTitle').text('Add Telco');
    $('#telcoForm')[0].reset();
    $('#telcoEnabled').prop('checked', true);
    $('#telcoModal').modal('show');
}

function showCreatePlanModal(telcoId) {
    editMode = false;
    currentTelcoId = telcoId;
    currentPlanId = null;
    $('#planModalTitle').text('Add Plan');
    $('#planForm')[0].reset();
    $('#planEnabled').prop('checked', true);
    $('#planModal').modal('show');
}

function editTelco(telcoId) {
    $.get(`/vpn-config-generator/api/telcos/${telcoId}`)
        .done(function(response) {
            if (response.success) {
                editMode = true;
                currentTelcoId = telcoId;
                $('#telcoModalTitle').text('Edit Telco');
                $('#telcoName').val(response.telco.name);
                $('#telcoDescription').val(response.telco.description || '');
                $('#telcoEnabled').prop('checked', response.telco.enabled);
                $('#telcoModal').modal('show');
            } else {
                toastr.error('Failed to load telco: ' + response.error);
            }
        })
        .fail(function() {
            toastr.error('Failed to load telco');
        });
}

function editPlan(telcoId, planId) {
    $.get(`/vpn-config-generator/api/telcos/${telcoId}/plans/${planId}`)
        .done(function(response) {
            if (response.success) {
                editMode = true;
                currentTelcoId = telcoId;
                currentPlanId = planId;
                $('#planModalTitle').text('Edit Plan');
                $('#planName').val(response.plan.name);
                $('#planDescription').val(response.plan.description || '');
                $('#planTemplate').val(response.plan.template);
                $('#planInfoMessage').val(response.plan.info_message_template || '');
                $('#planEnabled').prop('checked', response.plan.enabled);
                $('#planModal').modal('show');
            } else {
                toastr.error('Failed to load plan: ' + response.error);
            }
        })
        .fail(function() {
            toastr.error('Failed to load plan');
        });
}

function deleteTelco(telcoId) {
    // Create a custom confirmation modal
    const confirmHtml = `
        <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
                    <div class="modal-header bg-gradient-to-r from-red-500 to-pink-600 text-white" style="border-radius: 16px 16px 0 0;">
                        <h5 class="modal-title font-semibold">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-6 text-center">
                        <i class="fas fa-trash-alt text-red-500 text-4xl mb-4"></i>
                        <h4 class="font-semibold text-gray-800 mb-2">Delete Telco</h4>
                        <p class="text-gray-600">Are you sure you want to delete this telco and all its plans? This action cannot be undone.</p>
                    </div>
                    <div class="modal-footer border-0 p-6 pt-0">
                        <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn bg-gradient-to-r from-red-500 to-pink-600 text-white px-6 py-2 rounded-lg font-semibold" onclick="confirmDeleteTelco('${telcoId}')">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('body').append(confirmHtml);
    $('#confirmDeleteModal').modal('show');
    $('#confirmDeleteModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

function confirmDeleteTelco(telcoId) {
    $('#confirmDeleteModal').modal('hide');
    $.ajax({
        url: `/vpn-config-generator/api/telcos/${telcoId}`,
        method: 'DELETE'
    })
    .done(function(response) {
        if (response.success) {
            toastr.success('Telco deleted successfully', '', {
                progressBar: true,
                timeOut: 3000
            });
            loadTelcos();
        } else {
            toastr.error('Failed to delete telco: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to delete telco');
    });
}

function deletePlan(telcoId, planId) {
    const confirmHtml = `
        <div class="modal fade" id="confirmDeletePlanModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow-2xl" style="border-radius: 16px;">
                    <div class="modal-header bg-gradient-to-r from-red-500 to-pink-600 text-white" style="border-radius: 16px 16px 0 0;">
                        <h5 class="modal-title font-semibold">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-6 text-center">
                        <i class="fas fa-layer-group text-red-500 text-4xl mb-4"></i>
                        <h4 class="font-semibold text-gray-800 mb-2">Delete Plan</h4>
                        <p class="text-gray-600">Are you sure you want to delete this plan? This action cannot be undone.</p>
                    </div>
                    <div class="modal-footer border-0 p-6 pt-0">
                        <button type="button" class="btn btn-light px-6 py-2 rounded-lg font-semibold" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn bg-gradient-to-r from-red-500 to-pink-600 text-white px-6 py-2 rounded-lg font-semibold" onclick="confirmDeletePlan('${telcoId}', '${planId}')">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('body').append(confirmHtml);
    $('#confirmDeletePlanModal').modal('show');
    $('#confirmDeletePlanModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

function confirmDeletePlan(telcoId, planId) {
    $('#confirmDeletePlanModal').modal('hide');
    $.ajax({
        url: `/vpn-config-generator/api/telcos/${telcoId}/plans/${planId}`,
        method: 'DELETE'
    })
    .done(function(response) {
        if (response.success) {
            toastr.success('Plan deleted successfully', '', {
                progressBar: true,
                timeOut: 3000
            });
            loadTelcos();
        } else {
            toastr.error('Failed to delete plan: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to delete plan');
    });
}

function saveTelco() {
    const data = {
        name: $('#telcoName').val(),
        description: $('#telcoDescription').val(),
        enabled: $('#telcoEnabled').is(':checked')
    };

    const url = editMode ?
        `/vpn-config-generator/api/telcos/${currentTelcoId}` :
        '/vpn-config-generator/api/telcos';
    const method = editMode ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data)
    })
    .done(function(response) {
        if (response.success) {
            toastr.success(editMode ? 'Telco updated successfully' : 'Telco created successfully', '', {
                progressBar: true,
                timeOut: 3000,
                positionClass: 'toast-top-right'
            });
            $('#telcoModal').modal('hide');
            loadTelcos();
        } else {
            toastr.error('Failed to save telco: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to save telco');
    });
}

function savePlan() {
    const infoMessageTemplate = $('#planInfoMessage').val().trim();
    const data = {
        name: $('#planName').val(),
        description: $('#planDescription').val(),
        template: $('#planTemplate').val(),
        info_message_template: infoMessageTemplate || null,
        enabled: $('#planEnabled').is(':checked'),
        variables: {
            // Core configuration variables
            uuid: "Client UUID",
            server: "Server domain",
            server_id: "Original server input",
            server_name: "Server domain (alias)",
            server_number: "Server number/name",
            identity: "User identity",
            username: "Username",
            days: "Validity days",
            telco: "Telco name",
            plan: "Plan name",

            // Date variables in different formats
            created_date: "Created date (YYYY-MM-DD)",
            expired_date: "Expired date (YYYY-MM-DD)",
            created_date_formatted: "Created date (DD-MM-YYYY)",
            expired_date_formatted: "Expired date (DD-MM-YYYY)",
            created_datetime: "Created datetime (YYYY-MM-DD HH:MM)",
            expired_datetime: "Expired datetime (YYYY-MM-DD HH:MM)",
            created_time: "Created time (HH:MM)",
            validity: "Formatted validity (X Day(s))",

            // User and system variables
            email: "User email address",
            shopee_user: "Shopee username (alias)",
            shopee_username: "Shopee username",

            // Additional formatting variables
            config_id: "Short config ID (8 chars)",
            short_uuid: "Short UUID (8 chars)"
        }
    };

    const url = editMode ?
        `/vpn-config-generator/api/telcos/${currentTelcoId}/plans/${currentPlanId}` :
        `/vpn-config-generator/api/telcos/${currentTelcoId}/plans`;
    const method = editMode ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data)
    })
    .done(function(response) {
        if (response.success) {
            toastr.success(editMode ? 'Plan updated successfully' : 'Plan created successfully', '', {
                progressBar: true,
                timeOut: 3000,
                positionClass: 'toast-top-right'
            });
            $('#planModal').modal('hide');
            loadTelcos();
        } else {
            toastr.error('Failed to save plan: ' + response.error);
        }
    })
    .fail(function() {
        toastr.error('Failed to save plan');
    });
}

// Configure toastr options
toastr.options = {
    "closeButton": true,
    "debug": false,
    "newestOnTop": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "preventDuplicates": false,
    "onclick": null,
    "showDuration": "300",
    "hideDuration": "1000",
    "timeOut": "5000",
    "extendedTimeOut": "1000",
    "showEasing": "swing",
    "hideEasing": "linear",
    "showMethod": "fadeIn",
    "hideMethod": "fadeOut"
};
</script>
{% endblock %}
