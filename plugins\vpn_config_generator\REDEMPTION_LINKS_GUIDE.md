# VPN Redemption Links 功能使用指南

## 🎯 功能概述

VPN Redemption Links 系统为您提供了三个强大的新功能：

1. **聊天集成** - 自动通过聊天发送兑换链接
2. **批量创建** - 一次性创建多个兑换链接
3. **分析报告** - 详细的使用统计和趋势分析

## 📍 功能入口位置

### 在管理界面中访问：

1. 启动您的应用程序服务器
2. 登录管理界面
3. 在左侧菜单中找到 **"VPN Config Generator"** 部分
4. 展开该部分，您将看到以下新菜单项：

```
VPN Config Generator
├── Dashboard
├── Telco Configuration  
├── SKU Restrictions
├── SKU Tags Management
├── 🆕 Redemption Links        ← 主要管理界面
├── 🆕 Bulk Creation          ← 批量创建功能
└── 🆕 Analytics              ← 分析报告功能
```

### 直接URL访问：

- **主管理界面**: `http://localhost:5000/vpn-config-generator/admin/redemption-links`
- **批量创建**: `http://localhost:5000/vpn-config-generator/admin/redemption-links/bulk`
- **分析报告**: `http://localhost:5000/vpn-config-generator/admin/redemption-links/analytics`

## 🚀 功能使用说明

### 1. 兑换链接管理 (Redemption Links)

**功能**: 创建和管理单个VPN兑换链接

**使用步骤**:
1. 点击 "Redemption Links" 菜单项
2. 填写客户信息和VPN配置参数
3. 点击 "Create Redemption Link" 创建链接
4. 使用以下操作：
   - **Copy URL**: 复制兑换链接
   - **Send via Chat**: 自动通过聊天发送给客户
   - **Deactivate**: 停用链接
   - **Delete**: 删除链接

### 2. 批量创建 (Bulk Creation)

**功能**: 一次性为多个客户创建兑换链接

**使用步骤**:
1. 点击 "Bulk Creation" 菜单项
2. 选择输入方式：
   - **手动列表**: 直接输入客户用户名（每行一个）
   - **CSV上传**: 粘贴CSV数据或上传文件
3. 配置VPN参数（适用于所有客户）
4. 可选择自动通过聊天发送
5. 点击 "Create Bulk Redemption Links"
6. 查看详细的创建结果报告

### 3. 分析报告 (Analytics)

**功能**: 查看兑换链接的使用统计和趋势

**功能包括**:
- **统计概览**: 总链接数、使用率、活跃链接等
- **趋势图表**: 创建和使用趋势
- **热门分析**: 最受欢迎的运营商和套餐
- **时间分析**: 平均使用时间
- **数据导出**: CSV和JSON格式导出

## 💬 聊天集成配置

### 配置聊天模板：

1. 访问 `/vpn-config-generator/api/chat-template-config` API
2. 自定义消息模板，支持以下变量：
   - `{customer_username}` - 客户用户名
   - `{redemption_url}` - 兑换链接
   - `{validity_days}` - 有效天数
   - `{server_display}` - 服务器显示名
   - `{telco_display}` - 运营商显示名
   - `{plan_display}` - 套餐显示名

### 默认消息模板：
```
🎁 VPN Configuration Link

Hi {customer_username}!

Your VPN configuration is ready. Click the link below to generate your {validity_days}-day VPN config:

🔗 {redemption_url}

📋 Details:
• Validity: {validity_days} days
• Server: {server_display}
• Telco: {telco_display}
• Plan: {plan_display}

This link will expire if not used. Contact support if you need assistance!
```

## 🔧 API端点

### 主要API端点：

- `GET /vpn-config-generator/api/redemption-links` - 获取所有兑换链接
- `POST /vpn-config-generator/api/redemption-links` - 创建新兑换链接
- `POST /vpn-config-generator/api/redemption-links/{id}/send-chat` - 通过聊天发送
- `POST /vpn-config-generator/api/redemption-links/bulk` - 批量创建
- `GET /vpn-config-generator/api/redemption-links/analytics` - 获取分析数据
- `GET /vpn-config-generator/api/redemption-links/analytics/export` - 导出数据

## 🎯 客户使用流程

### 客户兑换流程：

1. 客户收到兑换链接（通过聊天或其他方式）
2. 点击链接访问兑换界面
3. 查看预配置的参数
4. 确认并生成VPN配置
5. 复制配置文件使用

### 兑换链接格式：
```
http://localhost:5000/vpn-config-generator/redeem/{link_id}
```

## 🛠️ 故障排除

### 常见问题：

1. **菜单项不显示**:
   - 确保VPN Config Generator插件已启用
   - 检查base.html文件是否包含新菜单项
   - 重启应用程序服务器

2. **聊天发送失败**:
   - 检查chat_commands插件是否正常运行
   - 验证ShopeeAPI配置是否正确
   - 查看服务器日志获取详细错误信息

3. **批量创建失败**:
   - 检查CSV格式是否正确
   - 确保客户用户名有效
   - 查看创建结果中的错误详情

4. **分析数据为空**:
   - 确保已创建一些兑换链接
   - 检查日期过滤器设置
   - 验证数据存储路径权限

## 📊 性能建议

- **批量创建**: 建议每次不超过100个链接
- **数据导出**: 大量数据建议使用日期过滤
- **定期清理**: 使用cleanup API清理过期链接

## 🔒 安全注意事项

- 兑换链接使用UUID确保唯一性
- 支持链接过期时间设置
- 可设置使用次数限制
- 提供链接停用功能

---

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 检查服务器日志文件
2. 运行测试脚本验证功能
3. 查看API响应获取详细错误信息
4. 联系技术支持团队

**测试脚本**: `python plugins/vpn_config_generator/test_routes_access.py`
