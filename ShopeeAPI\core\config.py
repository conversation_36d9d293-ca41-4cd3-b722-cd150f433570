"""
Configuration settings for Shopee API.
"""
import os
import json
import time
from typing import Dict, Any, Optional, List

# Try different import approaches to handle both package and direct imports
try:
    from core.exceptions import ConfigurationError
    from utils.validators import validate_config
except ImportError:
    try:
        from core.exceptions import ConfigurationError
        from utils.validators import validate_config
    except ImportError:
        try:
            from exceptions import ConfigurationError
            from validators import validate_config
        except ImportError:
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            sys.path.insert(0, parent_dir)
            from core.exceptions import ConfigurationError
            from utils.validators import validate_config


class ShopeeConfig:
    """
    Configuration settings for Shopee API.
    """

    def __init__(self, config_path: Optional[str] = None):
        # If config_path is not provided, use the default path in the ShopeeAPI directory
        if config_path is None:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.json')
            print(f"ShopeeConfig using default config path: {config_path}")

        # Credentials
        self.authorization_code = ""
        self.cookie = ""
        self.cookie_json = None

        # Default values
        self.shop_id = 0
        self.region_id = "MY"
        self.page_size = 40
        self.timeout = 60

        # Cache settings
        self.cache = {
            "enabled": True,
            "username_to_conversation_id": {
                "enabled": True,
                "expiry_seconds": 86400,  # 24 hours
                "max_size": 1000
            },
            "conversation_messages": {
                "enabled": True,
                "expiry_seconds": 3600,  # 1 hour
                "max_size": 50,
                "websocket_only": True  # Only cache when WebSocket is connected
            }
        }

        # WebSocket settings
        self.websocket = {
            "enabled": True,
            "reconnect_interval": 30,  # seconds
            "max_reconnect_attempts": 10,
            "ping_interval": 25,  # seconds
            "ping_timeout": 5,  # seconds
            "client_max_size": 100  # maximum number of connected clients
        }

        # Webhook settings
        self.webhook = {
            "enabled": False,
            "message_received": {
                "enabled": False,
                "url": "",  # Keep for backward compatibility
                "urls": [],  # New multi-URL support
                "retry_count": 3,
                "retry_delay": 5  # seconds
            },
            "message_sent": {
                "enabled": False,
                "url": "",  # Keep for backward compatibility
                "urls": [],  # New multi-URL support
                "retry_count": 3,
                "retry_delay": 5  # seconds
            }
        }

        # Email Sender settings
        self.email_sender = {
            "from_email": "",
            "from_password": "",
            "default_to_email": ""
        }

        # Automated Checks settings
        self.automated_checks = {
            "cookie_validity": {
                "enabled": False,
                "interval_minutes": 60,
                "notification_email": ""
            }
        }

        # API URLs
        self.urls = {
            "initial_order_list": "https://seller.shopee.com.my/api/v3/order/search_order_list_index",
            "order_details": "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list",
            "order_details_specific": "https://seller.shopee.com.my/api/v3/order/get_one_order",
            "order_details_alternative": "https://seller.shopee.com.my/api/v3/order/get_order_detail",
            "init_order": "https://seller.shopee.com.my/api/v3/shipment/init_order",
            "conversation": "https://seller.shopee.com.my/webchat/api/v1.2/mini/conversation/redirection",
            "conversation_search": "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/search/combined_search",
            "chat_message": "https://seller.shopee.com.my/webchat/api/v1.2/messages",
            "chat_image_upload": "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/images",
            "recent_conversations": "https://seller.shopee.com.my/webchat/api/v1.2/conversations",
            "conversation_messages": "https://seller.shopee.com.my/webchat/api/v1.2/conversations",
            "buyer_orders": "https://seller.shopee.com.my/webchat/api/v1.2/orders/orders_by_buyer",
            "websocket": "wss://seller-push-ws.shopee.com.my/socket.io/?region=my&_v=new&EIO=3&transport=websocket",
            "product_list": "https://seller.shopee.com.my/api/v3/mpsku/list/v2/get_product_list",
            "boost_product": "https://seller.shopee.com.my/api/v3/product/boost_product/"
        }

        # Order list tab values
        self.order_tabs = {
            "all": 100,
            "unpaid": 200,
            "to_ship": 300,
            "shipped": 400,
            "completed": 500
        }

        # Load from file if provided
        if config_path and os.path.exists(config_path):
            self.load_from_file(config_path)

    def load_from_file(self, config_path: str) -> None:
        """
        Load configuration from a JSON file.

        Args:
            config_path: Path to the config file

        Raises:
            ConfigurationError: If the configuration is invalid
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Validate configuration
            errors = validate_config(config_data)
            if errors:
                error_messages = []
                for key, messages in errors.items():
                    for message in messages:
                        error_messages.append(f"{key}: {message}")
                raise ConfigurationError(f"Invalid configuration: {', '.join(error_messages)}")

            # Update credentials
            if 'AUTHORIZATION_CODE' in config_data:
                self.authorization_code = config_data['AUTHORIZATION_CODE']
            if 'COOKIE' in config_data:
                self.cookie = config_data['COOKIE']
            if 'COOKIE_JSON' in config_data:
                self.cookie_json = config_data['COOKIE_JSON']

            # Update shop and region settings
            if 'SHOP_ID' in config_data:
                self.shop_id = config_data['SHOP_ID']
            if 'REGION_ID' in config_data:
                self.region_id = config_data['REGION_ID']
            if 'PAGE_SIZE' in config_data:
                self.page_size = config_data['PAGE_SIZE']
            if 'REQUEST_TIMEOUT' in config_data:
                self.timeout = config_data['REQUEST_TIMEOUT']

            # Update cache settings if they are in the config
            if 'CACHE' in config_data and isinstance(config_data['CACHE'], dict):
                if 'ENABLED' in config_data['CACHE']:
                    self.cache['enabled'] = config_data['CACHE']['ENABLED']

                if 'USERNAME_TO_CONVERSATION_ID' in config_data['CACHE'] and isinstance(config_data['CACHE']['USERNAME_TO_CONVERSATION_ID'], dict):
                    username_cache = config_data['CACHE']['USERNAME_TO_CONVERSATION_ID']
                    if 'ENABLED' in username_cache:
                        self.cache['username_to_conversation_id']['enabled'] = username_cache['ENABLED']
                    if 'EXPIRY_SECONDS' in username_cache:
                        self.cache['username_to_conversation_id']['expiry_seconds'] = username_cache['EXPIRY_SECONDS']
                    if 'MAX_SIZE' in username_cache:
                        self.cache['username_to_conversation_id']['max_size'] = username_cache['MAX_SIZE']

                # Load conversation messages cache settings
                if 'CONVERSATION_MESSAGES' in config_data['CACHE'] and isinstance(config_data['CACHE']['CONVERSATION_MESSAGES'], dict):
                    messages_cache = config_data['CACHE']['CONVERSATION_MESSAGES']
                    if 'ENABLED' in messages_cache:
                        self.cache['conversation_messages']['enabled'] = messages_cache['ENABLED']
                        print(f"Config: Setting conversation_messages.enabled to {messages_cache['ENABLED']}")
                    if 'EXPIRY_SECONDS' in messages_cache:
                        self.cache['conversation_messages']['expiry_seconds'] = messages_cache['EXPIRY_SECONDS']
                    if 'MAX_SIZE' in messages_cache:
                        self.cache['conversation_messages']['max_size'] = messages_cache['MAX_SIZE']
                    if 'WEBSOCKET_ONLY' in messages_cache:
                        self.cache['conversation_messages']['websocket_only'] = messages_cache['WEBSOCKET_ONLY']

            # Update WebSocket settings if they are in the config
            if 'WEBSOCKET' in config_data and isinstance(config_data['WEBSOCKET'], dict):
                if 'ENABLED' in config_data['WEBSOCKET']:
                    self.websocket['enabled'] = config_data['WEBSOCKET']['ENABLED']
                if 'RECONNECT_INTERVAL' in config_data['WEBSOCKET']:
                    self.websocket['reconnect_interval'] = config_data['WEBSOCKET']['RECONNECT_INTERVAL']
                if 'MAX_RECONNECT_ATTEMPTS' in config_data['WEBSOCKET']:
                    self.websocket['max_reconnect_attempts'] = config_data['WEBSOCKET']['MAX_RECONNECT_ATTEMPTS']
                if 'PING_INTERVAL' in config_data['WEBSOCKET']:
                    self.websocket['ping_interval'] = config_data['WEBSOCKET']['PING_INTERVAL']
                if 'PING_TIMEOUT' in config_data['WEBSOCKET']:
                    self.websocket['ping_timeout'] = config_data['WEBSOCKET']['PING_TIMEOUT']
                if 'CLIENT_MAX_SIZE' in config_data['WEBSOCKET']:
                    self.websocket['client_max_size'] = config_data['WEBSOCKET']['CLIENT_MAX_SIZE']

            # Update Webhook settings if they are in the config
            if 'WEBHOOK' in config_data and isinstance(config_data['WEBHOOK'], dict):
                print(f"Found WEBHOOK in config_data: {json.dumps(config_data['WEBHOOK'], indent=2)}")

                if 'ENABLED' in config_data['WEBHOOK']:
                    self.webhook['enabled'] = config_data['WEBHOOK']['ENABLED']
                    # Log webhook enabled status for debugging
                    print(f"Config: Setting webhook enabled to {config_data['WEBHOOK']['ENABLED']}")
                else:
                    print("WEBHOOK.ENABLED not found in config.json")

                # Message received webhook settings
                if 'MESSAGE_RECEIVED' in config_data['WEBHOOK'] and isinstance(config_data['WEBHOOK']['MESSAGE_RECEIVED'], dict):
                    msg_received = config_data['WEBHOOK']['MESSAGE_RECEIVED']
                    print(f"Found MESSAGE_RECEIVED in config_data: {json.dumps(msg_received, indent=2)}")

                    if 'ENABLED' in msg_received:
                        self.webhook['message_received']['enabled'] = msg_received['ENABLED']
                        print(f"Config: Setting message_received.enabled to {msg_received['ENABLED']}")
                    else:
                        print("MESSAGE_RECEIVED.ENABLED not found in config.json")

                    # Support new URLS array format
                    if 'URLS' in msg_received and isinstance(msg_received['URLS'], list):
                        self.webhook['message_received']['urls'] = msg_received['URLS']
                        print(f"Config: Setting MESSAGE_RECEIVED webhook URLs to {len(msg_received['URLS'])} URLs")
                        for i, url_config in enumerate(msg_received['URLS']):
                            print(f"  URL {i+1}: {url_config.get('NAME', 'Unnamed')} - {url_config.get('URL', 'No URL')}")
                    # Backward compatibility: support single URL
                    elif 'URL' in msg_received:
                        self.webhook['message_received']['url'] = msg_received['URL']
                        # Log webhook URL for debugging
                        print(f"Config: Setting MESSAGE_RECEIVED webhook URL to {msg_received['URL']}")
                    else:
                        print("MESSAGE_RECEIVED.URL or URLS not found in config.json")

                    if 'RETRY_COUNT' in msg_received:
                        self.webhook['message_received']['retry_count'] = msg_received['RETRY_COUNT']
                    if 'RETRY_DELAY' in msg_received:
                        self.webhook['message_received']['retry_delay'] = msg_received['RETRY_DELAY']
                else:
                    print("MESSAGE_RECEIVED section not found in config.json WEBHOOK section")

                # Message sent webhook settings
                if 'MESSAGE_SENT' in config_data['WEBHOOK'] and isinstance(config_data['WEBHOOK']['MESSAGE_SENT'], dict):
                    msg_sent = config_data['WEBHOOK']['MESSAGE_SENT']
                    if 'ENABLED' in msg_sent:
                        self.webhook['message_sent']['enabled'] = msg_sent['ENABLED']

                    # Support new URLS array format
                    if 'URLS' in msg_sent and isinstance(msg_sent['URLS'], list):
                        self.webhook['message_sent']['urls'] = msg_sent['URLS']
                        print(f"Config: Setting MESSAGE_SENT webhook URLs to {len(msg_sent['URLS'])} URLs")
                        for i, url_config in enumerate(msg_sent['URLS']):
                            print(f"  URL {i+1}: {url_config.get('NAME', 'Unnamed')} - {url_config.get('URL', 'No URL')}")
                    # Backward compatibility: support single URL
                    elif 'URL' in msg_sent:
                        self.webhook['message_sent']['url'] = msg_sent['URL']
                        # Log webhook URL for debugging
                        print(f"Config: Setting MESSAGE_SENT webhook URL to {msg_sent['URL']}")

                    if 'RETRY_COUNT' in msg_sent:
                        self.webhook['message_sent']['retry_count'] = msg_sent['RETRY_COUNT']
                    if 'RETRY_DELAY' in msg_sent:
                        self.webhook['message_sent']['retry_delay'] = msg_sent['RETRY_DELAY']

            # Update URLs if they are in the config
            if 'URLS' in config_data and isinstance(config_data['URLS'], dict):
                for key, url in config_data['URLS'].items():
                    if key.lower() in self.urls:
                        self.urls[key.lower()] = url

            # Update Email Sender settings
            if 'EMAIL_SENDER' in config_data and isinstance(config_data['EMAIL_SENDER'], dict):
                sender_conf = config_data['EMAIL_SENDER']
                if 'FROM_EMAIL' in sender_conf:
                    self.email_sender['from_email'] = sender_conf['FROM_EMAIL']
                if 'FROM_PASSWORD' in sender_conf:
                    self.email_sender['from_password'] = sender_conf['FROM_PASSWORD']
                if 'DEFAULT_TO_EMAIL' in sender_conf:
                    self.email_sender['default_to_email'] = sender_conf['DEFAULT_TO_EMAIL']

            # Update Automated Checks settings
            if 'AUTOMATED_CHECKS' in config_data and isinstance(config_data['AUTOMATED_CHECKS'], dict):
                auto_checks_conf = config_data['AUTOMATED_CHECKS']
                if 'COOKIE_VALIDITY' in auto_checks_conf and isinstance(auto_checks_conf['COOKIE_VALIDITY'], dict):
                    cookie_check_conf = auto_checks_conf['COOKIE_VALIDITY']
                    if 'ENABLED' in cookie_check_conf:
                        self.automated_checks['cookie_validity']['enabled'] = cookie_check_conf['ENABLED']
                    if 'INTERVAL_MINUTES' in cookie_check_conf:
                        self.automated_checks['cookie_validity']['interval_minutes'] = cookie_check_conf['INTERVAL_MINUTES']
                    if 'NOTIFICATION_EMAIL' in cookie_check_conf:
                        self.automated_checks['cookie_validity']['notification_email'] = cookie_check_conf['NOTIFICATION_EMAIL']

        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in config file: {e}")
        except IOError as e:
            raise ConfigurationError(f"Error reading config file: {e}")

    def save_to_file(self, config_path: str) -> None:
        """
        Save current configuration to a JSON file.

        Args:
            config_path: Path to save the config file

        Raises:
            ConfigurationError: If the configuration cannot be saved
        """
        config_data = {
            "SHOP_ID": self.shop_id,
            "REGION_ID": self.region_id,
            "PAGE_SIZE": self.page_size,
            "REQUEST_TIMEOUT": self.timeout,
            "CACHE": {
                "ENABLED": self.cache["enabled"],
                "USERNAME_TO_CONVERSATION_ID": {
                    "ENABLED": self.cache["username_to_conversation_id"]["enabled"],
                    "EXPIRY_SECONDS": self.cache["username_to_conversation_id"]["expiry_seconds"],
                    "MAX_SIZE": self.cache["username_to_conversation_id"]["max_size"]
                },
                "CONVERSATION_MESSAGES": {
                    "ENABLED": self.cache["conversation_messages"]["enabled"],
                    "EXPIRY_SECONDS": self.cache["conversation_messages"]["expiry_seconds"],
                    "MAX_SIZE": self.cache["conversation_messages"]["max_size"],
                    "WEBSOCKET_ONLY": self.cache["conversation_messages"]["websocket_only"]
                }
            },
            "WEBSOCKET": {
                "ENABLED": self.websocket["enabled"],
                "RECONNECT_INTERVAL": self.websocket["reconnect_interval"],
                "MAX_RECONNECT_ATTEMPTS": self.websocket["max_reconnect_attempts"],
                "PING_INTERVAL": self.websocket["ping_interval"],
                "PING_TIMEOUT": self.websocket["ping_timeout"],
                "CLIENT_MAX_SIZE": self.websocket["client_max_size"]
            },
            "WEBHOOK": {
                "ENABLED": self.webhook["enabled"],
                "MESSAGE_RECEIVED": {
                    "ENABLED": self.webhook["message_received"]["enabled"],
                    "URL": self.webhook["message_received"]["url"],
                    "RETRY_COUNT": self.webhook["message_received"]["retry_count"],
                    "RETRY_DELAY": self.webhook["message_received"]["retry_delay"]
                },
                "MESSAGE_SENT": {
                    "ENABLED": self.webhook["message_sent"]["enabled"],
                    "URL": self.webhook["message_sent"]["url"],
                    "RETRY_COUNT": self.webhook["message_sent"]["retry_count"],
                    "RETRY_DELAY": self.webhook["message_sent"]["retry_delay"]
                }
            },
            "EMAIL_SENDER": self.email_sender,
            "AUTOMATED_CHECKS": self.automated_checks,
            "URLS": self.urls
        }

        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        except IOError as e:
            raise ConfigurationError(f"Error saving config to {config_path}: {e}")

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert config to dictionary.

        Returns:
            Dictionary representation of the config
        """
        return {
            "shop_id": self.shop_id,
            "region_id": self.region_id,
            "page_size": self.page_size,
            "timeout": self.timeout,
            "cache": self.cache,
            "websocket": self.websocket,
            "email_sender": self.email_sender,
            "automated_checks": self.automated_checks,
            "urls": self.urls,
            "order_tabs": self.order_tabs
        }

    def update(self, config_data: Dict[str, Any]) -> None:
        """
        Update configuration with new values.

        Args:
            config_data: Dictionary with new configuration values
        """
        if 'shop_id' in config_data and config_data['shop_id'] is not None:
            self.shop_id = config_data['shop_id']
        if 'region_id' in config_data and config_data['region_id'] is not None:
            self.region_id = config_data['region_id']
        if 'page_size' in config_data and config_data['page_size'] is not None:
            self.page_size = config_data['page_size']
        if 'timeout' in config_data and config_data['timeout'] is not None:
            self.timeout = config_data['timeout']
        if 'urls' in config_data and config_data['urls'] is not None:
            for key, url in config_data['urls'].items():
                if key in self.urls:
                    self.urls[key] = url

        # Update cache settings if provided
        if 'cache' in config_data and config_data['cache'] is not None:
            cache_config = config_data['cache']
            if 'enabled' in cache_config:
                self.cache['enabled'] = cache_config['enabled']

            if 'username_to_conversation_id' in cache_config and isinstance(cache_config['username_to_conversation_id'], dict):
                username_cache = cache_config['username_to_conversation_id']
                if 'enabled' in username_cache:
                    self.cache['username_to_conversation_id']['enabled'] = username_cache['enabled']
                if 'expiry_seconds' in username_cache:
                    self.cache['username_to_conversation_id']['expiry_seconds'] = username_cache['expiry_seconds']
                if 'max_size' in username_cache:
                    self.cache['username_to_conversation_id']['max_size'] = username_cache['max_size']

            if 'conversation_messages' in cache_config and isinstance(cache_config['conversation_messages'], dict):
                messages_cache = cache_config['conversation_messages']
                if 'enabled' in messages_cache:
                    self.cache['conversation_messages']['enabled'] = messages_cache['enabled']
                if 'expiry_seconds' in messages_cache:
                    self.cache['conversation_messages']['expiry_seconds'] = messages_cache['expiry_seconds']
                if 'max_size' in messages_cache:
                    self.cache['conversation_messages']['max_size'] = messages_cache['max_size']
                if 'websocket_only' in messages_cache:
                    self.cache['conversation_messages']['websocket_only'] = messages_cache['websocket_only']

        # Update WebSocket settings if provided
        if 'websocket' in config_data and config_data['websocket'] is not None:
            ws_config = config_data['websocket']
            if 'enabled' in ws_config:
                self.websocket['enabled'] = ws_config['enabled']
            if 'reconnect_interval' in ws_config:
                self.websocket['reconnect_interval'] = ws_config['reconnect_interval']
            if 'max_reconnect_attempts' in ws_config:
                self.websocket['max_reconnect_attempts'] = ws_config['max_reconnect_attempts']
            if 'ping_interval' in ws_config:
                self.websocket['ping_interval'] = ws_config['ping_interval']
            if 'ping_timeout' in ws_config:
                self.websocket['ping_timeout'] = ws_config['ping_timeout']
            if 'client_max_size' in ws_config:
                self.websocket['client_max_size'] = ws_config['client_max_size']

        # Update Webhook settings if provided
        if 'webhook' in config_data and config_data['webhook'] is not None:
            webhook_config = config_data['webhook']
            if 'enabled' in webhook_config:
                self.webhook['enabled'] = webhook_config['enabled']

            # Update message received webhook settings
            if 'message_received' in webhook_config and webhook_config['message_received'] is not None:
                msg_received = webhook_config['message_received']
                if 'enabled' in msg_received:
                    self.webhook['message_received']['enabled'] = msg_received['enabled']
                if 'url' in msg_received:
                    self.webhook['message_received']['url'] = msg_received['url']
                if 'retry_count' in msg_received:
                    self.webhook['message_received']['retry_count'] = msg_received['retry_count']
                if 'retry_delay' in msg_received:
                    self.webhook['message_received']['retry_delay'] = msg_received['retry_delay']

            # Update message sent webhook settings
            if 'message_sent' in webhook_config and webhook_config['message_sent'] is not None:
                msg_sent = webhook_config['message_sent']
                if 'enabled' in msg_sent:
                    self.webhook['message_sent']['enabled'] = msg_sent['enabled']
                if 'url' in msg_sent:
                    self.webhook['message_sent']['url'] = msg_sent['url']
                if 'retry_count' in msg_sent:
                    self.webhook['message_sent']['retry_count'] = msg_sent['retry_count']
                if 'retry_delay' in msg_sent:
                    self.webhook['message_sent']['retry_delay'] = msg_sent['retry_delay']

        # Update Email Sender settings if provided
        if 'email_sender' in config_data and config_data['email_sender'] is not None:
            self.email_sender.update(config_data['email_sender'])

        # Update Automated Checks settings if provided
        if 'automated_checks' in config_data and config_data['automated_checks'] is not None:
            if 'cookie_validity' in config_data['automated_checks']:
                self.automated_checks['cookie_validity'].update(config_data['automated_checks']['cookie_validity'])


# Create a default instance
default_config = ShopeeConfig()
