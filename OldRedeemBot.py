import html
import json
import logging
import time
from webbrowser import get
import requests
import numpy as np
from flask import Flask, render_template, jsonify, request, Blueprint
from OpenSSL import SSL
import asyncio
import re
import os.path
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import random
import sqlite3
import datetime
from flask_cors import CORS, cross_origin
import uuid as uid

version = "2.8"

work_dict = "/www/wwwroot/MTYB_RedeemBot_System/"

################ Copy & Paste ################

#Keyauth License System
sellerkey = "dd910021415c17e7cb50bb69df65e682"
domain = "keyauth.online-mtyb.com"
variable = ""

servers = {
    "MY": {
        "backup": {
            "ip": "http://netflix.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        "server1": {
            "ip": "http://server1.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        "server2": {
            "ip": "http://server2.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        # Tambahkan server lain di sini dengan format yang sama
        "server4": {
            "ip": "http://server4.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        "server6": {
            "ip": "http://server6.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        "server7": {
            "ip": "http://server7.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        "server8": {
            "ip": "http://server8.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        "server11": {
            "ip": "http://server11.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        },
        "server12": {
            "ip": "http://server12.steam.autos:5000",
            "apis": {
                "redeemUser": "/redeemUser",
                "getConfig": "/redeemUserGetConfig",
                "getAvailableUsers": "/getAvailableUsers",
            }
        }
    }
}

# MY Servers
MY_serverIP = 'http://server1.steam.autos:5000'
MY_api = '/redeemUser'
MY_api_getConfig = '/redeemUserGetConfig'
MY_api_getAvailableUsers = '/getAvailableUsers'

MY_serverIP2 = 'http://server2.steam.autos:5000'
MY_api2 = '/redeemUser'
MY_api_getConfig2 = '/redeemUserGetConfig'
MY_api_getAvailableUsers2 = '/getAvailableUsers'

MY_serverIP4 = 'http://server4.steam.autos:5000'
MY_api4 = '/redeemUser'
MY_api_getConfig4 = '/redeemUserGetConfig'
MY_api_getAvailableUsers4 = '/getAvailableUsers'

MY_serverIP5 = 'http://server5.steam.autos:5000'
MY_api5 = '/redeemUser'
MY_api_getConfig5 = '/redeemUserGetConfig'
MY_api_getAvailableUsers5 = '/getAvailableUsers'

MY_serverIP6 = 'http://server6.steam.autos:5000'
MY_api6 = '/redeemUser'
MY_api_getConfig6 = '/redeemUserGetConfig'
MY_api_getAvailableUsers6 = '/getAvailableUsers'

MY_serverIP8 = 'http://server8.steam.autos:5000'
MY_api8 = '/redeemUser'
MY_api_getConfig8 = '/redeemUserGetConfig'
MY_api_getAvailableUsers8 = '/getAvailableUsers'

# SG Servers
SG_serverIP = 'http://server3.steam.autos:5000'
SG_api = '/redeemUser'
SG_api_getConfig = '/redeemUserGetConfig'
SG_api_getAvailableUsers = '/getAvailableUsers'

# MY High Speed Servers
MY_serverIP7 = 'http://server7.steam.autos:5000'
MY_api7 = '/redeemUser'
MY_api_getConfig7 = '/redeemUserGetConfig'
MY_api_getAvailableUsers7 = '/getAvailableUsers'

# MY High Speed Servers
MY_serverIP11 = 'http://server11.steam.autos:5000'
MY_api11 = '/redeemUser'
MY_api_getConfig11 = '/redeemUserGetConfig'
MY_api_getAvailableUsers11 = '/getAvailableUsers'

MY_serverIP12 = 'http://server12.steam.autos:5000'
MY_api12 = '/redeemUser'
MY_api_getConfig12 = '/redeemUserGetConfig'
MY_api_getAvailableUsers12 = '/getAvailableUsers'

Backup_IP = 'http://netflix.steam.autos:5000'
Backup_api = '/redeemUser'
Backup_getConfig = '/redeemUserGetConfig'
Backup_getAvailableUsers = '/getAvailableUsers'

#with requests.Session() as s:
s = requests.Session()
s.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36 Edg/100.0.1185.29'
s.headers['Host'] = 'www.bigseller.com'
s.headers['Connection'] = 'keep-alive'
s.headers['sec-ch-ua'] = '" Not A;Brand";v="99", "Chromium";v="100", "Microsoft Edge";v="100"'
s.headers['Accept'] = 'application/json, text/plain, */*'
s.headers['sec-ch-ua-platform'] = '"Windows"'
s.headers['Origin'] = 'https://www.bigseller.com'
s.headers['Referer'] = 'https://www.bigseller.com/order/index.htm?status=new'
s.headers['Accept-Language'] = 'en-US,en;q=0.9,id;q=0.8'
cookies = {
    'bs_u': 'm1i4JrXBt%2BZyHwR%2Fv1sedTWEu3wJ1dudBJwX4odI8y0cA0x5vaG4D5e9ObOEZh6JELFREmgMakl1egOqD4BabEi0pwxttPGgGIn393BED8O6zzOVDQl5X%2BZfNV3UrK8vva92XQYLhoVUHdx9pa1f12L0zlUn%2Bb63XZ3Um2qkEBjmaZVTPYP3gQBoB%2BpGSGXMYwOlEX3J3HoJJowvMNNUdTSuP5i3nfYKUXKgGtNi0IC7i2AXYjXPTNt%2BnH%2FWfPmbNQlIKCjI4Z8w1xK%2FjtmK7so7jN1X4u4t%2F6%2FOl8dELr%2BdAVGzOyghwk%2FXWBJIyvgeLGYIlXLjDd4ovWk8QMtNeGygRphWif22LbDItsMB57r2f%2BSlHwFvevB%2BXYtktFF%2BfY6uIRc5QJ14cCAqaw%2FCpvK4e2aeVrsJtaVAR9GOxzVpeUcUaARpYQpwIyreqjnsyH22koSyPNkBO%2B9%2FScFR6DPjwHbSfKsonmX8L3g1e1d5v9jnKynb4fe4%2BWdr9MEieIIQuwttzdhwbhwnxNf%2B5wQaH%2BLMaHrBZ8SCavBecVVzT9DeZFeMkUfIaYARAXgEuxydMd6DgQetJV8sw8K0fg%3D%3D',
    'ActivityExpired': '1',
    'JSESSIONID': '30BCDC8192232222016EE59DC4049395'
    }
s.headers['Accept-Encoding'] = 'gzip, deflate'

#Designed For CyberSpace
def getSteamCode(orderPrior, product_detail):
    product_sku = product_detail['data']['orderDetail']['orderItemVoList'][orderPrior]['varSku']

    file = open(work_dict + "data/cyberspace_product/" + str(product_sku) + ".txt", "r")
    L = []
    L = file.readlines()
    file.close()
        
    #Find ID
    for i in range(0, len(L)):
        cyber_ID = re.search(r"[^ID:]\w+", L[i])
        if(cyber_ID != None):
            break;
        
    #Find Username
    for i in range(0, len(L)):
        cyber_username = re.search(r"(?<=Steam Username : ).*?(?=\s)", L[i])
        if(cyber_username != None):
            break;

    if cyber_ID != None and cyber_username != None:
        URL = 'http://cyberspace.cyou/'
        client = requests.Session()
        client.headers['Cookie'] = "csrftoken=L1N9BOVOXzrKHy4qOQjw9VnqdQ9mBOpUiT30Z9iieB1gfMQiq7bdDSaz0dg6yTqB"
        result = client.get(URL)
        url = "http://cyberspace.cyou/spider/"
        payload ={
            "csrfmiddlewaretoken": "V3vN1UIJMLQUojaOx7x14dTZYW2mpUOisVLEpf5d3NqqWxWG9opIyaG8Lj96mZPZ",
            "orderKey": cyber_ID.group(),
            "userName": cyber_username.group(),
            "ip": ""
            }
        result = client.post(url=url, data=payload)
        soup = BeautifulSoup(result.content, 'html.parser')
        content = soup.find('div', class_="mdui-dialog-content", )

        return content.string
    else:
        return False;
    
    
#Designed For Keyauth ( Generate Key )
def generateKey(orderPrior, product_detail):
    product_sku = product_detail['data']['orderDetail']['orderItemVoList'][orderPrior]['varSku']
    product_price = product_detail['data']['orderDetail']['orderItemVoList'][orderPrior]['amount']
    domain = "keyauth.online-mtyb.com"
    seller_key = ""
    expiry = 1
    mask = "XXXXXXXX"
    level = 1
    if(product_sku == "ready_or_not"):
        print("Generating Ready Or Not License Key")
        seller_key = "0ae96b6f3b2beddd5548362d8387a56f"
        expiry = 1280
        mask = "XXXXXXXX"
        level = 1
    elif(product_sku == "baldur3_offline"):
        level = 1
        seller_key = "215c7b9cde59afdd37baf89391670de9"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "XXXX-XXXX-XXXX-XXXX"
    elif(product_sku == "palworld_online"):
        level = 1
        seller_key = "f41b120806eebccf0ea94c0e801bb31b"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "50871EA0-E086-4328-BFC4-XXXXXXXXXXXX"
    elif(product_sku == "remnant2_online"):
        level = 2
        seller_key = "533d3eb934756fd089f7118fb3ab86a1"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "XXXXXX-XXXXXX-XXXXXX-XXXXXX"
    elif(product_sku == "remnant2_offline"):
        seller_key = "533d3eb934756fd089f7118fb3ab86a1"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "XXXX-XXXX-XXXX-XXXX"
    elif(product_sku == "cod_mw2"):
        seller_key = "7384841bac6f8ac0355c9142e15b8e39"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "XXXX-XXXX-XXXX-XXXX"
    elif(product_sku == "ghost_own"):
        seller_key = "fbaef4921db5eece1f38356b4efd5c6c"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "GHOST-XXXX-XXXX-XXXX-XXXX"
    elif(product_sku == "elden_ring2_own"):
        seller_key = "fbaef4921db5eece1f38356b4efd5c6c"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "ELDEN-XXXX-XXXX-XXXX-XXXX"
    elif(product_sku == "wukong_own"):
        seller_key = "fbaef4921db5eece1f38356b4efd5c6c"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "WUKONG-XXXX-XXXX-XXXX-XXXX"
    elif(product_sku == "cod_mw3"):
        seller_key = "6e8bd0b93371d177d043ac92b300380f"
        domain = "keyauth2.online-mtyb.com"
        expiry = 1280
        mask = "XXXX-XXXX-XXXX-XXXX"
    elif(product_sku == "genshin_1"):
            if(int(product_price) >= 10):
                print("Generating Genshin Impact (1 Day) License Key")
                seller_key = "743100e9d0b69585f39f7a3039027f19"
                domain = "keyauth2.online-mtyb.com"
                expiry = 1
                mask = "XXXXXXXX"
    elif(product_sku == "genshin_3"):
            if(int(product_price) >= 20):
                print("Generating Genshin Impact (3 Day) License Key")
                seller_key = "743100e9d0b69585f39f7a3039027f19"
                domain = "keyauth2.online-mtyb.com"
                expiry = 3
                mask = "XXXXXXXX"
    elif(product_sku == "genshin_7"):
            if(int(product_price) >= 30):
                print("Generating Genshin Impact (7 Days) License Key")
                seller_key = "743100e9d0b69585f39f7a3039027f19"
                domain = "keyauth2.online-mtyb.com"
                expiry = 7
                mask = "XXXXXXXX"
    elif(product_sku == "genshin_28"):
            if(int(product_price) >= 50):
                print("Generating Genshin Impact (28 Days) License Key")
                seller_key = "743100e9d0b69585f39f7a3039027f19"
                domain = "keyauth2.online-mtyb.com"
                expiry = 28
                mask = "XXXXXXXX"
    elif(product_sku == "apex_1"):
        print("Generating Apex Cheat (1 Day) License Key")
        seller_key = "2a6e0520b8e6f05efc10d0864512110e"
        expiry = 1
        mask = "XXXXXXXX"
    elif(product_sku == "apex_7"):
        print("Generating Apex Cheat (7 Days) License Key")
        seller_key = "2a6e0520b8e6f05efc10d0864512110e"
        expiry = 7
        mask = "XXXXXXXX"
    elif(product_sku == "apex_28"):
        print("Generating Apex Cheat (28 Days) License Key")
        seller_key = "2a6e0520b8e6f05efc10d0864512110e"
        expiry = 28
        mask = "XXXXXXXX"
    elif(product_sku == "mc_win10"):
        print("Generating Minecraft Win 10 License Key")
        seller_key = "3fc3992309389ba6e17ae79d399f6aee"
        expiry = 99999
        mask = "XXXXXXXX"
    else:
        return False
    s = requests.Session()
    url = "https://" + domain + "/api/seller/?sellerkey="+str(seller_key)+"&type=add&expiry="+str(expiry)+"&mask="+str(mask)+"&level="+str(level)+"&amount=1&format=json"
    result = s.get(url=url)
    if(result.status_code == 200):
        result_json = json.loads(result.content)
        if(result_json['success'] == True):
            return result_json['key']
        else:
            return False
    else:
        return False

# Designed For MTYB VPN SERVER API
def registerVPN(orderPrior, product_detail):
    buyerUsername = '%.20s' % product_detail['data']['orderDetail']['buyer']
    varSku = product_detail['data']['orderDetail']['orderItemVoList'][orderPrior]['varSku']
    itemSku = product_detail['data']['orderDetail']['orderItemVoList'][orderPrior]['itemSku']
    
    vpnObj = sortRequestDetail(varSku, False, itemSku ,buyer)
    
    payload = {
        'username': buyerUsername,
        'password': buyerUsername,
        'days': vpnObj['validity']
    }
    
    url = vpnObj['url']
    
    result = requests.post(url, json=payload)
    data = json.loads(result.content)
    
    if data.get('status') == 0:
        print("")

def generateHistory(platformID): #Platform id is bigseller order ID
    print("Bigseller ID " + str(platformID) + " Generated Order History")
    product_details = getDetails(platformID);
    orderID = product_details['data']['orderDetail']['platfromOrderId']
    order_amount = orderAmount(orderID)
    order_file = open(work_dict + "data/orders/" + orderID + ".txt", "w")
    L = [
            "--------------------------------------------------\n",
            "Bigseller Order ID : " + str(product_details['data']['orderDetail']['id'] ) +"\n", 
            "Shopee Order ID : " + str(product_details['data']['orderDetail']['platfromOrderId'])+"\n",
            "--------------------------------------------------\n",
            "Buyer Info >> \n",
            "Username : " + product_details['data']['orderDetail']['buyer'] + "\n",
            "Message : " + product_details['data']['orderDetail']['buyerMessage'] + "\n",
            "Name : " + product_details['data']['orderDetail']['buyer']+ "\n",
            "Phone : " + str(product_details['data']['orderDetail']['address']['phone'])+ "\n",
            "Address : " + str(product_details['data']['orderDetail']['address']['address'])+ "\n",
            "Order Time : " + str(product_details['data']['orderDetail']['orderedTimeStr'])+ "\n",
            "Paid Time : " + str(product_details['data']['orderDetail']['payTimeStr'])+ "\n",
            "Total Price : " + str(product_details['data']['orderDetail']['orderTotal'])+ "\n",
            ]
    order_file.writelines(L);
    order_file.close();
    order_file = open(work_dict + "data/orders/" + orderID + ".txt", "a")
    for i in range(0, order_amount):
        itemName = product_details['data']['orderDetail']['orderItemVoList'][i]['itemName'].encode('utf-8')
        if(product_details['data']['orderDetail']['orderItemVoList'][i]['varSku'] != None):
            varSku = str(product_details['data']['orderDetail']['orderItemVoList'][i]['varSku']);
            A = [
                "--------------------------------------------------\n",
                "Order Item " + str(i) + "\n",
                "--------------------------------------------------\n",
                "varSku : " + str(varSku) + "\n",
                "Quantity : " + str(product_details['data']['orderDetail']['orderItemVoList'][i]['quantity'])+ "\n",
                "Discounted Price : " + str(product_details['data']['orderDetail']['orderItemVoList'][i]['varDiscountedPrice'])+ "\n",
                "Total Price : " + str(product_details['data']['orderDetail']['orderItemVoList'][i]['amount'])+ "\n",
                "Item Name : " + str(itemName) + "\n"
                ]

            order_file.writelines(A);
        else:
            A = [
                "--------------------------------------------------\n",
                "Order Item " + str(i) + "\n",
                "--------------------------------------------------\n",
                "varSku : " + "!!! Cant Find Product SKU !!!" + "\n",
                "Quantity : " + str(product_details['data']['orderDetail']['orderItemVoList'][i]['quantity'])+ "\n",
                "Discounted Price : " + str(product_details['data']['orderDetail']['orderItemVoList'][i]['varDiscountedPrice'])+ "\n",
                "Total Price : " + str(product_details['data']['orderDetail']['orderItemVoList'][i]['amount'])+ "\n",
                "Item Name : " + str(itemName) + "\n"
                ]

            order_file.writelines(A);
    order_file.close()

def getDetails(id):
    details_url = 'https://www.bigseller.com/api/v1/order/detail.json'
    details_payload = {
        'id': id,
        }
    r = s.get(details_url, params=details_payload, cookies=cookies)
    data = json.loads(r.content)
    if(data['code'] == 0):
        return data
    return

def writeReward(message, productDetail):
    if(os.path.exists(work_dict + "data/reward/" + productDetail['data']['orderDetail']['platfromOrderId'] + ".txt")):
        txtfile = open(work_dict + "data/reward/" + productDetail['data']['orderDetail']['platfromOrderId'] + ".txt", "a");
    else:
        txtfile = open(work_dict + "data/reward/" + productDetail['data']['orderDetail']['platfromOrderId'] + ".txt", "w");

    for i in range(0, len(message)):
        txtfile.writelines(message[i]);
    txtfile.close()
    
def writeCustom(message, productDetail, folderName):
    if(os.path.exists(work_dict + "data/CustomReward/" + folderName + "/" + productDetail['data']['orderDetail']['platfromOrderId'] + ".txt")):
        txtfile = open(work_dict + "data/CustomReward/" + folderName + "/" + productDetail['data']['orderDetail']['platfromOrderId'] + ".txt", "a");
    else:
        txtfile = open(work_dict + "data/CustomReward/" + folderName + "/" + productDetail['data']['orderDetail']['platfromOrderId'] + ".txt", "w");

    for i in range(0, len(message)):
        txtfile.writelines(message[i]);
    txtfile.close()
    
def removeCustom(orderid, folderName):
    if(os.path.exists(work_dict + "data/CustomReward/"+ folderName + "/" + orderid +".txt") == True):
        os.remove(work_dict + "data/CustomReward/"+ folderName + "/" + orderid +".txt")
        return True
    else:
        return False
        
# 设计 - VPN 
def vasmaVPN(orderPrior, productDetail):
    product_sku = productDetail['data']['orderDetail']['orderItemVoList'][orderPrior]['varSku']
    
    return None

def is_refunded(orderID, orderSn):
    sync_refund_data()
    
    url = 'https://www.bigseller.com/api/v1/order/refund/pageList.json'
    payload = {
        "pageNo": 1,
        "pageSize": 50,
        "returnStatus": "SELLER_DISPUTE,JUDGING",
        "processingStatus": 0,
        "orderBy": "ExpireTime",
        "desc": True,
        "platform": "shopee",
        "shopId": 1066453  # Make sure this is the correct shop ID
    }
    
    response = s.post(url, json=payload, cookies=cookies)
    data = json.loads(response.content)
    
    if data['code'] == 0:
        refund_orders = data['data']['page']['rows']
        for order in refund_orders:
            if order['orderSn'] == orderSn:
                return True  # Order is in refund process
    
    return False  # Order is not in refund process

def sync_refund_data():
    url = 'https://www.bigseller.com/api/v1/order/refund/sync.json'
    payload = {
        "platform": "shopee",
        "syncProgressMark": int(time.time() * 1000),  # Current timestamp in milliseconds
        "historyOrder": False,
        "shopId": 1066453
    }

    response = requests.post(url, data=payload, cookies=cookies)
    data = response.json()

    if data['code'] != 0:
        print(response.content)
        raise Exception(f"Failed to sync refund data: {data.get('message', 'Unknown error')}")

async def findResult(orderPrior, productDetail): #platformID is bigseller order ID
    product_sku = str(productDetail['data']['orderDetail']['orderItemVoList'][orderPrior]['varSku'])
    orderID = str(productDetail['data']['orderDetail']['id'])
    orderSN = str(productDetail['data']['orderDetail']['platfromOrderId'])
    
    if is_refunded(orderID, orderSN):
        return [
            "This order has been refunded. Please cancel the refund to proceed with redemption AGAIN."
        ]
        
    #Designed For CyberSpace
    if(os.path.exists(work_dict + "data/cyberspace_product/" + str(product_sku) + ".txt")):
        if(os.path.exists(work_dict + "data/reward/" + str(productDetail['data']['orderDetail']['platfromOrderId']) + ".txt") == True):
            reward = open(work_dict + "data/reward/" + str(productDetail['data']['orderDetail']['platfromOrderId']) + ".txt", "r")
            L = []
            L = reward.readlines()
            reward.close()
            return L
        else:
            file_cyber = open(work_dict + "data/cyberspace_product/" + str(product_sku) + ".txt")
            if(file_cyber != None):
                L = []
                L = file_cyber.readlines()
                file_cyber.close()
                code = getSteamCode(orderPrior, productDetail)
                L.append(code)
                L += '\n'
                return L
            else:
                print(str(product_sku+".txt") + " Not Found")
                return False

    #Designed For Keyauth Product
    if(os.path.exists(work_dict + "data/keyauth_product/" + str(product_sku) + ".txt")):
        if(os.path.exists(work_dict + "data/reward/" + str(productDetail['data']['orderDetail']['platfromOrderId']) + ".txt") == True):
            reward = open(work_dict + "data/reward/" + str(productDetail['data']['orderDetail']['platfromOrderId']) + ".txt", "r")
            L = []
            L = reward.readlines()
            reward.close()
            return L
        else:
            file = open(work_dict + "data/keyauth_product/" + str(product_sku) + ".txt")
            if(file != None):
                L = []
                L = file.readlines()
                file.close()
                L += '\n'
                key = generateKey(orderPrior,productDetail)
                if(key != False):
                    L.append(key + "</p>")
                    generateHistory(productDetail['data']['orderDetail']['id']);
                    writeReward(L, productDetail)
                    return L
                else:
                    return False
            else:
                print(str(product_sku+".txt") + " Not Found")
                return False
                
    #Designed For VPN Reseller
    elif(os.path.exists(work_dict + "data/vpn_reseller/" + str(product_sku) + ".txt")):
        if(os.path.exists(work_dict + "data/orders/"+ productDetail['data']['orderDetail']['platfromOrderId'] +".txt") == False):
            if(str(product_sku) == "vpn_reseller"):
                generateHistory(productDetail['data']['orderDetail']['id']);
                result = [
                    f"Successfully Redeemed The Order\n",
                    f"\n",
                    f"<b>Please Goto This Link To Register Your Account</b>\n",
                    f"<a href='https://reseller.online-mtyb.com/SignUp'>Click ME</a>\n"
                ]
                writeReward(result, productDetail)
                writeCustom(result, productDetail, "vpn_reseller")
                return result
            elif(str(product_sku) == "vpn_reload"):
                generateHistory(productDetail['data']['orderDetail']['id']);
                orderAmount = productDetail['data']['orderDetail']['orderTotal']
                result = [
                    f"${orderAmount}"
                ]
                writeCustom(result, productDetail, "vpn_reload")
                result = [
                    f"Successfully Saved Record In System \n",
                    f"Goto Reseller Panel > Wallet > Enter Order ID \n",
                    f"To Reload To Your Panel Account \n",
                    f"\n",
                    f"<b>Reload Amount : ${orderAmount} </b>"
                ]
                writeReward(result, productDetail)
                
                return result
        else:
            reward = open(work_dict + "data/reward/" + str(productDetail['data']['orderDetail']['platfromOrderId']) + ".txt", "r")
            L = []
            L = reward.readlines()
            reward.close()
            return L
    #Designed For VPN
    elif(os.path.exists(work_dict + "data/vpn_product/" + str(product_sku) + ".txt")):
        if(os.path.exists(work_dict + "data/orders/"+ productDetail['data']['orderDetail']['platfromOrderId'] +".txt") == False):
            # 找不到用户的记录，生成奖励
            # Return False = Product Not Found
            # Return [] = Result Found
            # 记得使用generateHistory和writeReward
            generateHistory(productDetail['data']['orderDetail']['id'])
            orderId = productDetail['data']['orderDetail']['platfromOrderId']
            result = [
                f"Successfully Redeemed The Order\n",
                f"<b>Masuk Link Itu Untuk Config / Tutorial:</b>\n\n",
                "\n",
                f"<a href='https:///generator.online-mtyb.com/index.html?orderid={orderId}'>Click Me To Get Config</a>\n"
            ]
            
            return result
            
        result = []
        if 1 == 1:
            orderId = productDetail['data']['orderDetail']['platfromOrderId']
            
            result = [
                f"Successfully Redeemed The Order\n",
                f"<b>Masuk Link Itu Untuk Config / Tutorial:</b>\n",
                "\n",
                f"<a href='https:///generator.online-mtyb.com/index.html?orderid={orderId}'>Click Me To Get Config</a>"
            ]
            
            return result
        else:
            return False
    
    
    #Shared ID Product
    elif(os.path.exists(work_dict + "data/sharedID_product/"+ str(product_sku) + ".txt")):
        print("Found the product sku");
        if(os.path.exists(work_dict + "data/sharedID_product/"+ productDetail['data']['orderDetail']['platfromOrderId'] +".txt") == False):
            result_file = open(work_dict + "data/sharedID_product/"+ str(product_sku) + ".txt", "r");
            temp = result_file.readlines();
            result_file.close();
            generateHistory(productDetail['data']['orderDetail']['id']);
            writeReward(temp, productDetail)
            return temp;
        else:
            if(os.path.exists(work_dict + "data/reward/" + productDetail['data']['orderDetail']['platfromOrderId'] +".txt")):
                reward = open(work_dict + "data/reward/" + productDetail['data']['orderDetail']['platfromOrderId'] +".txt", "r")
                F = [] 
                F = reward.readlines()
                reward.close()
                return F
            else:
                return False

    else:
        print("Cant found the product sku", str(product_sku))
        return False;
    
def generateVPNConfig(productDetail, product_sku, user_uuid=None):
    username = productDetail['data']['orderDetail']['platfromOrderId']
    
    if not user_uuid:
        user_uuid = str(uid.uuid4())

    # 根据product_sku设置有效期
    if product_sku in ['my_15', 'my_hs_15', 'sg_15', 'sg_hs_15', 'my_highspeed_15', 'sg_highspeed_15']:
        validity = 15
    elif product_sku in ['my_30', 'my_hs_30', 'sg_30', 'sg_hs_30', 'my_highspeed_30', 'sg_highspeed_30']:
        validity = 40
    elif product_sku in ['my_60', 'my_hs_60', 'sg_60', 'sg_hs_60', 'my_highspeed_60', 'sg_highspeed_60']:
        validity = 70
        
    # 根据product_sku选择服务器
    if product_sku.startswith("my_highspeed_"):
        print("Generate HighSpeed MY User")
        result7 = requests.get(MY_serverIP7 + MY_api_getAvailableUsers7)
        data7 = result7.json()
        result11 = requests.get(MY_serverIP11 + MY_api_getAvailableUsers11)
        data11 = result11.json()
        result12 = requests.get(MY_serverIP12 + MY_api_getAvailableUsers12)
        data12 = result12.json()
            
        payload = {
            "username": username,
            "validity": validity,
            "uuid": user_uuid
        }
        serverIP = MY_serverIP11
        api = MY_api11
    
        response = requests.post(url=MY_serverIP7+MY_api7, json=payload)
        requests.post(url=MY_serverIP12+MY_api12, json=payload)
        requests.post(url=MY_serverIP+MY_api, json=payload)
        requests.post(url=MY_serverIP2+MY_api2, json=payload)
        requests.post(url=MY_serverIP6+MY_api6, json=payload)
        requests.post(url=MY_serverIP8+MY_api8, json=payload)
    elif product_sku.startswith('my_'):
        print("Generate MY Normal User")
        result = requests.get(MY_serverIP+MY_api_getAvailableUsers)
        data = result.json()
        if int(data.get('status')) == 0:
            server1Amount = data.get('amount')
        
        result = requests.get(MY_serverIP2+MY_api_getAvailableUsers2)
        data = result.json()
        if int(data.get('status')) == 0:
            server2Amount = data.get('amount')
            
        result = requests.get(MY_serverIP6+MY_api_getAvailableUsers6)
        data = result.json()
        if int(data.get('status')) == 0:
            server6Amount = data.get('amount')
        
        result = requests.get(MY_serverIP8+MY_api_getAvailableUsers8)
        data = result.json()
        if int(data.get('status')) == 0:
            server8Amount = data.get('amount')
            
        if all(amount is not None for amount in [server1Amount, server2Amount, server6Amount, server8Amount]):
            if server1Amount <= min(server2Amount, server6Amount, server8Amount):
                serverIP = MY_serverIP
                api = MY_api
            elif server2Amount <= min(server6Amount, server8Amount):
                serverIP = MY_serverIP2
                api = MY_api
            elif server6Amount <= server8Amount:
                serverIP = MY_serverIP6
                api = MY_api6
            else:
                serverIP = MY_serverIP8
                api = MY_api8
        else:
            serverIP = MY_serverIP
            api = MY_api
    
    elif product_sku.startswith("sg_highspeed_"):
        result = requests.get(MY_serverIP4+MY_api_getAvailableUsers4)
        data = result.json()
        if int(data.get('status')) == 0:
            sg4amount = data.get('amount')
            
        if sg4amount is not None:
            serverIP = MY_serverIP4
            api = MY_api4
        
    # 构建payload
    payload = {
        "username": username,
        "validity": validity,
        "uuid": user_uuid  # 添加uuid到payload
    }
    
    # 发送请求
    result = requests.post(url=serverIP+api, json=payload)
    print(result.content)
    data = result.json()
    if int(data.get('status')) == 0:
        
        # 保存过期日期和其他信息到本地数据库
        saved_validity = validity
        saved_username = username
        saved_sku = product_sku
        saved_server = ""
        if product_sku.startswith("sg_"):
            saved_server = "SG"
        elif product_sku.startswith("my_"):
            saved_server = "MY"
        
        current_date = datetime.datetime.now()
        expiration_date = current_date + datetime.timedelta(days=saved_validity)
        formatted_exp_date = expiration_date.strftime('%d %B %Y')
        
        conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/VPN_Customer.db")
        cursor = conn.cursor()
        cursor.execute('''CREATE TABLE IF NOT EXISTS info (
            Username TEXT,
            Sku TEXT,
            Server TEXT,
            ExpirationDate TEXT,
            FormattedExpDate TEXT
        )''')
        
        data_to_insert = (saved_username, saved_sku, saved_server, expiration_date.strftime('%Y-%m-%d'), formatted_exp_date)
        cursor.execute("INSERT INTO info VALUES (?, ?, ?, ?, ?)", data_to_insert)
        
        conn.commit()
        conn.close()
        
        result = [
            f"Successfully Redeemed The Order\n",
            f"<b>Masuk Link Itu Untuk Config / Tutorial:</b>\n\n",
            "\n",
            f"<a href='./vpnConfig?orderid={username}&varSku={product_sku}'>https://redeem.online-mtyb/vpnConfig/?orderid={username}&varSku={product_sku}</a>\n",
            f"\n",
            f"Expired Date: {formatted_exp_date}\n"
            ]
        if product_sku in ['my_hs_15', 'my_hs_30', 'my_hs_60', 'sg_hs_60', 'sg_hs_30', 'sg_hs_15']:
            addon = [
                "🌟How To Setting Proxy🌟 \n",
                "1. Connect VPN dekat [Phone Sendiri] \n",
                "2. Buka Hotspot",
                "3. Dekat [Device/Phone Lain] Connect Ke [Hotspot Sendiri] \n",
                "4. Setting Proxy Dekat [Device/Phone Lain] \n",
                "\n",
                "Tutorial Video (Setting Proxy): \n",
                "🔗 https://bit.ly/3oRR8So \n",
                "\n",
                "IP: **********\n",
                "Port: 8080\n"
                ]
            result += addon
        writeReward(result, productDetail)
        return result
    else:
        result = [
            f"{data.get('message')}\n",
            ]
        return result

def orderAmount(orderID):
    #Get The Order Total Item Ordered ( Not The Same Item With How many units)
    url = 'https://www.bigseller.com/api/v1/order/new/pageList.json'
    payload = {
      "shopId": None,
      "platformStatus": None,
      "shipProviderId": None,
      "printStatus": None,
      "paymentMethod": None,
      "hasRemark": None,
      "hasDeducted": None,
      "isWarehouseBack": None,
      "hasLable": "",
      "lableIds": None,
      "timeType": 4,
      "days": 120,
      "beginDate": "",
      "endDate": "",
      "searchType": "orderNo",
      "warehouseId": None,
      "searchContent": orderID,
      "inquireType": 2,
      "priorityValue": None,
      "failType": None,
      "printLabelMark": None,
      "printPickListMark": None,
      "printSign": None,
      "currentShopPlatform": None,
      "isPreOrder": None,
      "selectedShipList": None,
      "priorityDelivery": None,
      "packageTypes": None,
      "tiktokPackageType": None,
      "firstShipped": None,
      "allOrder": True,
      "historyOrder": 0,
      "packState": "",
      "desc": 1,
      "orderBy": "paidTime",
      "wareType": None,
      "pageNo": 1,
      "pageSize": 50,
      "warehouseIdList": None
    }
    r = s.post(url, json=payload, cookies=cookies)
    result = json.loads(r.content)
    if(result['data']['page']['totalSize'] == 1):
        return len(result['data']['page']['rows'][0]['orderItemList'])
    else:
        return False

def shippedStatus(orderID):
    url = 'https://www.bigseller.com/api/v1/order/new/pageList.json'
    payload = {
      "shopId": None,
      "status": "shipped",
      "platformStatus": None,
      "shipProviderId": None,
      "printStatus": None,
      "paymentMethod": None,
      "hasRemark": None,
      "hasDeducted": None,
      "isWarehouseBack": None,
      "hasLable": "",
      "lableIds": None,
      "timeType": 4,
      "days": 120,
      "beginDate": "",
      "endDate": "",
      "searchType": "orderNo",
      "warehouseId": None,
      "searchContent": orderID,
      "inquireType": 2,
      "priorityValue": None,
      "failType": None,
      "printLabelMark": None,
      "printPickListMark": None,
      "printSign": None,
      "currentShopPlatform": None,
      "isPreOrder": None,
      "selectedShipList": None,
      "priorityDelivery": None,
      "packageTypes": None,
      "tiktokPackageType": None,
      "firstShipped": None,
      "allOrder": True,
      "historyOrder": 0,
      "packState": "",
      "desc": 1,
      "orderBy": "paidTime",
      "wareType": None,
      "pageNo": 1,
      "pageSize": 50,
      "warehouseIdList": None
    }
    r = s.post(url, json=payload, cookies=cookies)
    result = json.loads(r.content)
    if(result['data']['page']['totalSize'] == 1):
        return result
    else:
        payload['status'] = "completed"
        r = s.post(url, json=payload, cookies=cookies)
        result = json.loads(r.content)
        if(result['data']['page']['totalSize'] == 1):
            return result
        else:
            print("Shipped Function Returning False")
            return False

def packageList(orderID):
    #Get Order Details
    url = 'https://www.bigseller.com/api/v1/order/new/pageList.json'
    payload = {
      "shopId": None,
      "status": None,
      "platformStatus": None,
      "shipProviderId": None,
      "printStatus": None,
      "paymentMethod": None,
      "hasRemark": None,
      "hasDeducted": None,
      "isWarehouseBack": None,
      "hasLable": "",
      "lableIds": None,
      "timeType": 1,
      "days": 120,
      "beginDate": None,
      "endDate": None,
      "searchType": "orderNo",
      "warehouseId": None,
      "searchContent": orderID,
      "inquireType": 2,
      "priorityValue": None,
      "failType": None,
      "printLabelMark": None,
      "printPickListMark": None,
      "printSign": None,
      "currentShopPlatform": None,
      "isPreOrder": None,
      "selectedShipList": None,
      "priorityDelivery": None,
      "packageTypes": None,
      "tiktokPackageType": None,
      "firstShipped": None,
      "allOrder": True,
      "historyOrder": 0,
      "packState": "",
      "desc": 1,
      "orderBy": "paidTime",
      "wareType": None,
      "pageNo": 1,
      "pageSize": 50,
      "warehouseIdList": None
}
    r = s.post(url, json=payload, cookies=cookies)
    result = json.loads(r.content)
    if(result['data']['page']['totalSize'] == 1):
        return result
    else:
        return False

async def shipItem(orderID):
    #Get Package ID
    result_data = packageList(orderID);
    if (result_data == False):
        print("Order Not Found");
        return False;
    else:
        packageID_size = result_data['data']['page']['totalSize']
        packageID = result_data['data']['page']['rows'][0]['id'];

    #Update Item Tracking Code
    shipping_url = 'https://www.bigseller.com/api/v1/order/updateTrackingNo.json'
    shipping_payload = {
   "orderId" : packageID,
   "trackingNo" : orderID
}
    r = s.post(shipping_url, json=shipping_payload, cookies=cookies)

    result = json.loads(r.content)
    if(result['code'] != 0):
        print("(Step 1) Error : " + result['msg']);
        return False;
    #Pack Item
    else: 
        pack_url = 'https://www.bigseller.com/api/v1/order/pack.json'
        pack_payload = {
       "orderId" : packageID,
       "flag" : 0
    }
        r = s.post(pack_url, data=pack_payload, cookies=cookies)
        result = json.loads(r.content)
        if(result['code'] != 0):
            print("(Step 2) Error : " + result['msg']);
            return False;
        #Ship Item
        else: 
            ship_url = 'https://www.bigseller.com/api/v1/order/ship.json'
            ship_payload = {
           "orderIds" : packageID
        }
            r = s.post(ship_url, data=ship_payload, cookies=cookies)
            result = json.loads(r.content)
            if(result['code'] != 0 and result['code'] != -1):
                print("(Step 3) Code : " + str(result['code']));
                print("(Step 3) Error : " + result['msg']);
                return False;
            else:
                ship_url = 'https://www.bigseller.com/api/v1/order/new/pageList.json'
                ship_payload = {
                  "shopId": None,
                  "status": "shipped",
                  "platformStatus": None,
                  "shipProviderId": None,
                  "printStatus": None,
                  "paymentMethod": None,
                  "hasRemark": None,
                  "hasDeducted": None,
                  "isWarehouseBack": None,
                  "hasLable": "",
                  "lableIds": None,
                  "timeType": 4,
                  "days": 120,
                  "beginDate": "",
                  "endDate": "",
                  "searchType": "orderNo",
                  "warehouseId": None,
                  "searchContent": packageID,
                  "inquireType": 2,
                  "priorityValue": None,
                  "failType": None,
                  "printLabelMark": None,
                  "printPickListMark": None,
                  "printSign": None,
                  "currentShopPlatform": None,
                  "isPreOrder": None,
                  "selectedShipList": None,
                  "priorityDelivery": None,
                  "packageTypes": None,
                  "tiktokPackageType": None,
                  "firstShipped": None,
                  "allOrder": False,
                  "historyOrder": 0,
                  "packState": "",
                  "desc": 1,
                  "orderBy": "paidTime",
                  "wareType": None,
                  "pageNo": 1,
                  "pageSize": 50,
                  "warehouseIdList": None
                }
                r = s.post(ship_url, json=ship_payload, cookies=cookies)
                result = json.loads(r.content)
                if(result['code'] != 0):
                    print("(Step 4) Error : " + result['msg']);
                    return False;
                else:
                    moveShipped_payload = {
                       "orderIds" : packageID,
                       "type": 1
                    }
                    print(moveShipped_payload)
                    r = s.post("https://www.bigseller.com/api/v1/order/moveShipped.json", data=moveShipped_payload, cookies=cookies)
                    result = json.loads(r.content)
                    if(result['code'] != 0):
                        print("(Step 5) Error : " + result['msg']);
                        return False;
                    else:
                        #Make sure it is shipped.
                        shipped = shippedStatus(orderID);
                        if(shipped != False):
                            print("Order ID " + orderID + " Status (Shipped)")
                            return result_data;
                        else:
                            print("Order ID " + orderID + " Havent Change To Shipped Status")
                            return "havent_shipped"

def replace_symbols_with_html(text):
    html_chars = {
        '>': '&gt;',
        '<': '&lt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#39;',
        '`': '&#x60;'
    }
    return html.escape(text, html_chars)

app = Flask(__name__)
cors = CORS(app)
app.config['CORS_HEADERS'] = 'Content-Type'
#Protect
#CSRFProtect(app)
#app.config['SECRET_KEY'] = '@MTYB_OFFICIAL_PROTECTED'


@app.route('/')
def index():
    orderid = request.args.get('orderid')
    if orderid != None:
        return render_template('index.html', orderid=orderid)
    
    return render_template('index.html')

@app.route('/process_order', methods=['POST'])
async def process_order():
    if(request.method == 'POST'):
        #Get User Input
        content_type = request.headers.get('Content-Type')
        if(content_type == 'application/json'):
            debug = {
              "code": 0,
              "msg": "",
              "data": {
                "noEditNote": False,
                "remarkTypeIds": "",
                "removeAdditionsMerchant": False,
                "orderDetail": {
                  "id": 975968732,
                  "orderStatus": "shipped",
                  "platform": "shopee",
                  "shopName": "mtyb_official",
                  "platfromOrderId": "22071169FGUQP4",
                  "shopId": 1066453,
                  "platformName": "shopee",
                  "viewPlatfrom": "Shopee",
                  "buyer": "apitester",
                  "buyerMessage": "",
                  "buyerDesignatedLogistics": "Others (West Malaysia)",
                  "shippingMethod": "Shopee-MY-Others (West Malaysia)",
                  "shippingMethodId": 2395163,
                  "shippingConfig": None,
                  "shippingConfigId": None,
                  "rackingNoRequire": 2,
                  "trackingNo": "22071040UP3J4D",
                  "senderName": None,
                  "isSenderName": 0,
                  "additionalInfo": None,
                  "isAdditional": 0,
                  "packageNo": "BS93X2010488",
                  "name": "Wahidah (Athirah)",
                  "phone": "60137370677",
                  "address": "No 1187, Blok 4, Felda Bukit Waha, Kota Tinggi, 81907, Johor",
                  "store": None,
                  "ordered": None,
                  "orderedTime": 1657467969000,
                  "orderedTimeStr": "2022-07-10 15:46",
                  "paid": None,
                  "payTime": 1657468047000,
                  "payTimeStr": "2022-07-10 15:47",
                  "amountUnit": "MYR",
                  "shippingFee": "0",
                  "payment": "Prepaid",
                  "orderTotal": "6.50",
                  "orderItemVoList": [
                    {
                      "id": 971386152,
                      "varSku": "ovpn_my_15",
                      "varAttr": "Malaysia,30 Days",
                      "quantity": 1,
                      "image": "https://cf.shopee.com.my/file/c56324e7a5f518517e0b31c3b32cbb10",
                      "itemPlatformState": None,
                      "varOriginalPrice": "6.50",
                      "varDiscountedPrice": "6.50",
                      "amount": "6.50",
                      "link": None,
                      "preOrder": None,
                      "itemFlag": "",
                      "itemSku": None,
                      "itemName": "ovpn",
                      "vName": "Malaysia,30 Days",
                      "itemNo": None,
                      "isAddition": 0,
                      "inventorySku": None,
                      "inventorySkuImage": None,
                      "available": None,
                      "sourcePlatform": None,
                      "sourceUrl": None,
                      "varSkuGroupVoList": None,
                      "allocated": None
                    }
                  ],
                  "orderRemarksList": [],
                  "orderAdditionSkuIds": [],
                  "orderLableList": None,
                  "currentWarehouseId": 28994,
                  "warehouseNameList": [
                    {
                      "id": 28994,
                      "name": "Default Warehouse",
                      "isDefault": 1
                    }
                  ],
                  "warehouseName": None,
                  "isFinalCarrier": 1,
                  "hasLable": 0
                },
                "shipProviderList": [
                  {
                    "id": 2404353,
                    "providerAgentId": 1,
                    "providerChannelId": 30,
                    "trackingNoRequire": 1,
                    "isAdditional": 1,
                    "isSenderName": 0,
                    "name": "Shopee-MY-J&T Express",
                    "count": None,
                    "platform": None
                  },
                  {
                    "id": 2395163,
                    "providerAgentId": 1,
                    "providerChannelId": 31,
                    "trackingNoRequire": 2,
                    "isAdditional": 0,
                    "isSenderName": 0,
                    "name": "Shopee-MY-Others (West Malaysia)",
                    "count": None,
                    "platform": None
                  },
                  {
                    "id": 2395164,
                    "providerAgentId": 1,
                    "providerChannelId": 37,
                    "trackingNoRequire": 2,
                    "isAdditional": 0,
                    "isSenderName": 0,
                    "name": "Shopee-MY-Others (East Malaysia)",
                    "count": None,
                    "platform": None
                  },
                  {
                    "id": 2395165,
                    "providerAgentId": 1,
                    "providerChannelId": 104,
                    "trackingNoRequire": 1,
                    "isAdditional": 0,
                    "isSenderName": 0,
                    "name": "Shopee-MY-Standard Delivery",
                    "count": None,
                    "platform": None
                  }
                ],
                "isAdditional": 0,
                "addAdditionsMerchant": False
              }
            }           
            #result = await findResult(0, debug);
            print(request.json)
            Getjson = request.json;
            searchID = replace_symbols_with_html(Getjson['order_id'])
            result = await shipItem(searchID); #Its result packageList.json info
            web_result = """<div id="result" class="modal-body">"""
            if(result == "havent_shipped"):
                web_result += """
                <div class="alert alert-primary" role="alert">
                    Wait...<br>
                </div>
                """
                return jsonify(web_result),201

            elif(result == False):
                web_result += f"""<div class="alert alert-danger" role="alert">
                                         Order ID Not Found, Please try again later [ Shopee System Delay ]
                                </div>"""

                web_result += """</div>"""
                return jsonify(web_result),202
            else:
                #Status Is Shipped Already
                amount = orderAmount(searchID);
                product_details = getDetails(result['data']['page']['rows'][0]['id'])
                #Loop For Each Order
                for i in range(0, amount):
                    #Claim Result
                    #Get Product Sku
                    product_image = result['data']['page']['rows'][0]['orderItemList'][i]['image']
                    if(result['data']['page']['rows'][0]['orderItemList'][i]['varSku'] != None and result['data']['page']['rows'][0]['orderItemList'][i]['varSku'] != ""):
                        product_result = await findResult(i, product_details);
                        
                    else:
                        continue

                    if(product_result != False and product_result != None):
                        print(product_result)
                        #Generate Web Result
                        ##Get Order Details
                        
                        ##Add div Open 
                        web_result += f"""
                                <div class="alert alert-success" role="alert">
                                <div class="row">
                                    <div class="col-3">
                                        <img class="img-thumbnail" src="{product_image}"/>
                                    </div>
                                    <div class="col">
                                        {product_details['data']['orderDetail']['orderItemVoList'][i]['itemName']}
                                    </div>
                                </div>
                                <div class="text-center">
                                <br>
                                <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#result{i}" aria-expanded="false" aria-controls="result{i}">
                                Result
                                </button>
                                </div>
                                <div class="collapse show" id="result{i}">
                                </br>
                                <div class="card card-body text-center">
                                """
                        for i in range(0, len(product_result)):
                                ##Add Message into div
                                web_result += f"""{product_result[i]}</br>"""
                            
                        #Add div Close
                        web_result += f"""</div></div></div>"""

                    else:
                        web_result += f"""
                                   <div class="alert alert-danger" role="alert">

                                   <div class="row">
                                    <div class="col-3">
                                        <img class="img-thumbnail" src="{product_image}"/>
                                    </div>
                                    <div class="col">
                                        {product_details['data']['orderDetail']['orderItemVoList'][i]['itemName']}
                                    </div>
                                    </div>
                                    <br>
                                    <div class="row">
                                        <div class="col">
                                            <p class="text-center">Product Result Not Found, Please Contact Seller.</p>
                                        </div>
                                    </div>
                                
                                   </div>
                                """
                web_result += """</div>"""
                return jsonify(web_result),200
        else:
            return jsonify(f"""Please Don't Simply Try Ya :), Auto System Alert To Admin Now...'""");

        return jsonify('', render_template('random.html', result="None"))
        
@app.route('/vpnConfig', methods=['GET'])
def vpnConfig():
    username = request.args.get('orderid', '')
    varSku = request.args.get('varSku', '')
    if(username != None):
        # 确认已经生成 History
        if(os.path.exists(work_dict + "data/orders/"+ username +".txt") == True):
            return render_template('vpnConfig.html')
        else:
            return jsonify({}), 403
    else:
        return jsonify({}), 404

@app.route('/vpnConfig2', methods=['GET'])
def vpnConfig2():
    username = request.args.get('orderid', '')
    varSku = request.args.get('varSku', '')
    if(username != None):
        # 确认已经生成 History
        if(os.path.exists(work_dict + "data/orders/"+ username +".txt") == True):
            return render_template('vpnConfig2.html')
        else:
            return jsonify({}), 403
    else:
        return jsonify({}), 404

        
@app.route('/umobile', methods=['GET'])
def umobile():
    return render_template('convert.html')

@app.route('/removeReward', methods=['GET'])
def removeReward():
    orderid = request.args.get('orderid', None)
    if(orderid != None):
        if(os.path.exists(work_dict + "data/reward/"+ orderid +".txt") == True):
            os.remove(work_dict + "data/reward/"+ orderid +".txt")
            return jsonify({
                'status': 'true'
            }), 200
        else:
            return jsonify({
                'status': 'false'
            }), 200
    else:
        return jsonify({
            'status': 'false'
        }), 200
        
@app.route('/removeReseller', methods=['GET'])
def removeReseller():
    orderid = request.args.get('orderid', None)
    if(orderid != None):
        if(os.path.exists(work_dict + "data/CustomReward/"+ "vpn_reseller/" + orderid +".txt") == True):
            os.remove(work_dict + "data/CustomReward/"+ "vpn_reseller/" + orderid +".txt")
            return jsonify({
                'status': 'true'
            }), 200
        else:
            return jsonify({
                'status': 'false'
            }), 200
    else:
        return jsonify({
            'status': 'false'
        }), 200
        
@app.route('/removeWalletReload', methods=['GET'])
def removeWalletReload():
    orderid = request.args.get('orderid', None)
    if(orderid != None):
        if(os.path.exists(work_dict + "data/CustomReward/"+ "vpn_reload/" + orderid +".txt") == True):
            with open((work_dict + "data/CustomReward/"+ "vpn_reload/" + orderid +".txt"), 'r') as file:
                content = file.read()
                match = re.search(r'\$(\d+)', content)
                if match:
                    amount = float(match.group(1))
                else:
                    amount = 0.0
                    
            os.remove(work_dict + "data/CustomReward/"+ "vpn_reload/" + orderid +".txt")
            return jsonify({
                'status': 'true',
                'amount': amount
            }), 200
        else:
            return jsonify({
                'status': 'false'
            }), 200
    else:
        return jsonify({
            'status': 'false'
        }), 200
        
@app.route('/resellerStatus', methods=['GET'])
def resellerStatus():
    orderid = request.args.get('orderid', None)
    if(orderid != None):
        if(os.path.exists(work_dict + "data/CustomReward/"+ "vpn_reseller/" + orderid +".txt") == True):
            return jsonify({
                'status': 'true'
            }), 200
        else:
            return jsonify({
                'status': 'false'
            }), 200
    else:
        return jsonify({
            'status': 'false'
        }), 200

@app.route('/orderStatus', methods=['GET'])
def orderStatus():
    orderid = request.args.get('orderid', None)
    if(orderid != None):
        if(os.path.exists(work_dict + "data/reward/"+ orderid +".txt") == True):
            return jsonify({
                'status': 'true'
            }), 200
        else:
            return jsonify({
                'status': 'false'
            }), 200
    else:
        return jsonify({
            'status': 'false'
        }), 200

@app.route('/getOrderPrice', methods=['GET'])
def getOrderPrice(orderid_param=None):
    orderid = request.args.get('orderid', None)
    if(orderid_param != None):
        orderid = orderid_param
        
    if(orderid != None):
        json_payload = {
          "shopId": None,
          "platformStatus": None,
          "shipProviderId": None,
          "printStatus": None,
          "paymentMethod": None,
          "hasRemark": None,
          "hasDeducted": None,
          "isWarehouseBack": None,
          "hasLable": "",
          "lableIds": None,
          "timeType": 4,
          "days": 120,
          "beginDate": "",
          "endDate": "",
          "allOrder": True,
          "searchType": "orderNo",
          "warehouseId": None,
          "searchContent": orderid,
          "inquireType": 2,
          "priorityValue": None,
          "failType": None,
          "printLabelMark": None,
          "printPickListMark": None,
          "printSign": None,
          "currentShopPlatform": None,
          "isPreOrder": None,
          "selectedShipList": None,
          "priorityDelivery": None,
          "estimatedProfit": None,
          "showLogisticsArr": False,
          "showStoreArr": False,
          "shopGroup": None,
          "blacklist": "",
          "packageTypes": None,
          "tiktokPackageType": None,
          "firstShipped": None,
          "allOrder": False,
          "historyOrder": 0,
          "packState": "",
          "desc": 1,
          "orderBy": "paidTime",
          "wareType": None,
          "warehouseIdList": None,
          "pageNo": 1,
          "pageSize": 50
        }
        
        json_url = "https://www.bigseller.com/api/v1/order/new/pageList.json"
        
        r = s.post(json_url, json=json_payload, cookies=cookies)
        result = json.loads(r.content)
        if(result['data']['page']['totalSize'] > 0):
            varSku = result['data']['page']['rows'][0]['amount']
        else:
            varSku = ""
        return jsonify({'status': "success", 'result': varSku})
    
    
    return jsonify({'status': "failed"})

@app.route('/getOrderSKU', methods=['GET'])
def getOrderSKU(orderid=None):
    if orderid is None:
        orderid = request.args.get('orderid', None)

    if orderid is not None:
        json_payload = {
            "hasLable": "",
            "timeType": 1,
            "days": 90,
            "searchType": "orderNo",
            "searchContent": orderid,
            "inquireType": 2,
            "showLogisticsArr": 0,
            "showStoreArr": 0,
            "blacklist": "",
            "cancelLabelProcess": "",
            "cancelTimeTimeout": "",
            "payType": "",
            "shippedType": "",
            "allOrder": True,
            "historyOrder": 0,
            "packState": "",
            "desc": 1,
            "orderBy": "paidTime",
            "pageNo": 1,
            "pageSize": 50
        }

        json_url = "https://www.bigseller.com/api/v1/order/new/pageList.json"

        r = s.post(json_url, json=json_payload, cookies=cookies)
        result = json.loads(r.content)
        if result['data']['page']['totalSize'] > 0:
            varSku = result['data']['page']['rows'][0]['orderItemList'][0]['varSku']
        else:
            varSku = ""
        return jsonify({'status': "success", 'result': varSku})

    return jsonify({'status': "failed", 'message': "Missing orderid"})

@app.route('/getOrderInfo', methods=['GET'])
def getOrderInfo(orderid=None):
    if orderid is None:
        orderid = request.args.get('orderid', None)

    if orderid is not None:
        json_payload = {
            "hasLable": "",
            "timeType": 1,
            "days": 30,
            "searchType": "orderNo",
            "searchContent": orderid,
            "inquireType": 2,
            "showLogisticsArr": 0,
            "showStoreArr": 0,
            "blacklist": "",
            "cancelLabelProcess": "",
            "cancelTimeTimeout": "",
            "payType": "",
            "shippedType": "",
            "allOrder": True,
            "historyOrder": 0,
            "packState": "",
            "desc": 1,
            "orderBy": "paidTime",
            "pageNo": 1,
            "pageSize": 50
        }

        json_url = "https://www.bigseller.com/api/v1/order/new/pageList.json"

        r = s.post(json_url, json=json_payload, cookies=cookies)
        result = json.loads(r.content)
        if result['data']['page']['totalSize'] > 0:
            return jsonify({'status': "success", 'result': result})
        else:
            return jsonify({'status': "error", 'result': result}), 400

    return jsonify({'status': "failed", 'message': "Missing orderid"})
 
@app.route('/getUsername', methods=['GET'])
def getUsername(orderid=None):
    if orderid is None:
        orderid = request.args.get('orderid', None)

    if orderid is not None:
        json_payload = {
            "hasLable": "",
            "timeType": 1,
            "days": 999,
            "searchType": "orderNo",
            "searchContent": orderid,
            "inquireType": 2,
            "showLogisticsArr": 0,
            "showStoreArr": 0,
            "blacklist": "",
            "cancelLabelProcess": "",
            "cancelTimeTimeout": "",
            "payType": "",
            "shippedType": "",
            "allOrder": True,
            "historyOrder": 0,
            "packState": "",
            "desc": 1,
            "orderBy": "paidTime",
            "pageNo": 1,
            "pageSize": 50
        }

        json_url = "https://www.bigseller.com/api/v1/order/new/pageList.json"

        r = s.post(json_url, json=json_payload, cookies=cookies)
        result = json.loads(r.content)
        if result['data']['page']['totalSize'] > 0:
            varSku = result['data']['page']['rows'][0]['buyerUsername']
        else:
            varSku = ""
        return jsonify({'status': "success", 'result': varSku})

    return jsonify({'status': "failed", 'message': "Missing orderid"})

@app.route('/chatUI', methods=['GET'])
def chatUI():
    return render_template('chatUI.html')

@app.route("/vpnGenerate", methods=['POST'])
def vpnGenerate():
    print("[DEBUG] Starting vpnGenerate function")
    data = request.get_json()
    key = data.get("key")
    server = data.get("server")
    days = data.get("days", 0)
    telco = data.get("telco")
    plan = data.get("plan")
    username = data.get("username")
    print(f"[DEBUG] Received data: {data}")

    if key is None:
        print("[DEBUG] Key is None, returning 404")
        return jsonify({}), 404

    if key != "020817":
        print("[DEBUG] Invalid key, returning 400")
        return jsonify({}), 400

    print(f"[DEBUG] Processing request for server: {server}")
    
    registerPayload = {
        "username": username,
        "validity": float(days)
    }
    
    redeemPayload = {
        'username': username,
        'varSku': "",
        'plan': plan,
        'telco': telco
    }
    
    print(f"[DEBUG] Register payload: {registerPayload}")
    print(f"[DEBUG] Redeem payload: {redeemPayload}")
    
    server_abbreviation_map = {
        "server1": "M1",
        "server2": "M2",
        "server6": "M6",
        "server8": "M8",
        "server7": "M7",
        "server11": "M11",
        "server12": "M12",
        "server3": "S3",
        "server4": "S4"
    }
    
    for country, servers_in_country in servers.items():
        print(f"[DEBUG] Checking country: {country}")
        for server_name, server_info in servers_in_country.items():
            if server == server_name:
                print(f"[DEBUG] Found matching server: {server_name}")
                print(f"[DEBUG] Server info: {server_info}")
                
                try:
                    register_response = requests.post(
                        url=server_info['ip']+server_info['apis']['redeemUser'], 
                        json=registerPayload
                    )
                    print(f"[DEBUG] Register response: {register_response.text}")
                    
                    result = requests.post(
                        url=server_info['ip']+server_info['apis']['getConfig'], 
                        json=redeemPayload
                    )
                    print(f"[DEBUG] Get config response: {result.text}")
                    
                    result_json = result.json()
                    if result_json is not None:
                        response_str = result_json.get('config', '')
                        uuid = response_str
                        server_name = server_abbreviation_map.get(server_name, server_name)
                        
                        if uuid is not None:
                            print(f"[DEBUG] UUID generated: {uuid}")
                            today = datetime.datetime.today()
                            expiry_date = today + timedelta(days=float(days))
                            expired_date = expiry_date.strftime("%Y-%m-%d")
                            real_varSku = ""
                            
                            vless_config = generate_vless_config(
                                telco, plan, server_name, uuid, 
                                real_varSku, expired_date, "admin"
                            )
                            print(f"[DEBUG] Generated VLESS config: {vless_config}")
                            
                            return {
                                'status': 0, 
                                'created_date': datetime.datetime.today().strftime("%Y-%m-%d"), 
                                'expired_date': expired_date, 
                                'config': vless_config, 
                                'message': "Successfully Generated Config"
                            }, 200
                        
                        if response_str != '':
                            print(f"[DEBUG] Returning result_json: {result_json}")
                            return result_json, 200
                        
                        return result_json, 200
                except Exception as e:
                    print(f"[ERROR] Exception occurred: {str(e)}")
                    return jsonify({'error': str(e)}), 500
    
    print("[DEBUG] No matching server found")
    return jsonify({}), 400

def update_user_expired_date(order_id, validity):
    
    # 创建或连接到SQLite数据库
    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/vpn_users.db")
    c = conn.cursor()
    
    
    # 创建users表
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (order_id TEXT PRIMARY KEY,
                 user_id TEXT,
                 var_sku TEXT,
                 telco TEXT,
                 created_date TEXT,
                 expired_date TEXT)''')
                 
    # 获取当前时间
    current_time = datetime.datetime.now()
    
    # 查询用户当前的expired_date
    c.execute("SELECT expired_date FROM users WHERE order_id = ?", (order_id,))
    result = c.fetchone()
    
    if result:
        expired_date = result[0]
        # 将expired_date转换为datetime对象
        expired_date_obj = datetime.datetime.strptime(expired_date, '%Y-%m-%d')
        
        # 如果expired_date已经过了当前时间,就以当前时间+Validity
        if expired_date_obj < current_time:
            new_expired_date = current_time + datetime.timedelta(days=validity)
        # 如果还没有过了当前时间,就以Expired Date+Validity
        else:
            new_expired_date = expired_date_obj + datetime.timedelta(days=validity)
        
        # 将新的expired_date转换为字符串格式
        new_expired_date_str = new_expired_date.strftime('%Y-%m-%d')
        
        # 更新用户的expired_date
        c.execute("UPDATE users SET expired_date = ? WHERE order_id = ?", (new_expired_date_str, order_id))
        conn.commit()
    else:
        print(f"No user found with order_id: {order_id}")
    
    conn.close()

def is_first_call(order_id):
    
    
    # 创建或连接到SQLite数据库
    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/vpn_users.db")
    c = conn.cursor()
    
    
    # 创建users表
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (order_id TEXT PRIMARY KEY,
                 user_id TEXT,
                 var_sku TEXT,
                 telco TEXT,
                 created_date TEXT,
                 expired_date TEXT)''')
                 
    # 检查order_id是否第一次调用该API
    c.execute("SELECT * FROM users WHERE order_id = ?", (order_id,))
    result = c.fetchone()
    c.execute("SELECT expired_date FROM users WHERE order_id = ?", (order_id,))
    
    conn.close()
    return result is None



@app.route('/vpnAPI2-shipItem')
async def ship_item():
    order_id = request.args.get('orderid')
    if not order_id:
        return jsonify({'error': 'Missing orderid parameter'})

    result = await shipItem(order_id)
    if result == False:
        return jsonify({'error': 'Failed to ship item'}), 400
    elif result == "havent_shipped":
        return jsonify({'status': 'not_shipped'})
    else:
        product_details = getDetails(result['data']['page']['rows'][0]['id'])
        if(result['data']['page']['rows'][0]['orderItemList'][0]['varSku'] != None):
            product_result = await findResult(0, product_details);
        else:
            return jsonify({'error': 'Failed to find order result.'})
            
        return jsonify({'status': 'shipped'})

@app.route('/vpnAPI2-checkOrderUsage', methods=['POST'])
@cross_origin()
def check_order_usage():
    order_id = request.json.get('order_id')
    user_id = getUsername(order_id).json.get('result')
    
    if not user_id or not order_id:
        return jsonify({"error": "Missing user_id or order_id"}), 400
    
    conn = sqlite3.connect('/www/wwwroot/MTYB_RedeemBot_System/vpn_orders.db')
    c = conn.cursor()
    
    # 创建表用于记录续订和创建配置的操作
    c.execute('''CREATE TABLE IF NOT EXISTS vpn_operations
                 (user_id TEXT, from_order_id TEXT, to_order_id TEXT, operation TEXT)''')
    conn.commit()
    
    c.execute("SELECT operation FROM vpn_operations WHERE user_id=? AND from_order_id=?", (user_id, order_id))
    result = c.fetchone()
    conn.close()
    
    if result is None:
        return jsonify({"order_used": False, "operation": None})
    else:
        operation = result[0]
        return jsonify({"order_used": True, "operation": operation})

@app.route('/instock')
def instockHtml():
    return render_template('instock.html')

@app.route('/vpnAPI2-instockUUID', methods=['POST'])
def instockUUID():
    
    servers = [
        'server1.steam.autos',
        'server2.steam.autos',
        'server4.steam.autos',
        'server6.steam.autos',
        'server8.steam.autos',
        'server7.steam.autos',
        'server11.steam.autos',
        'server12.steam.autos',
    ]
    
    data = request.get_json()
    my_uuid = data.get('uuid')
    encrypted_uuid = data.get('encrypted_uuid')

    # 发送请求到每个服务器
    for server in servers:
        url = f'http://{server}:5000/instockUUID'
        payload = {
            'uuid': my_uuid,
            'encrypted_uuid': encrypted_uuid
        }
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                print(f'Successfully sent UUID to {server}')
            else:
                print(f'Failed to send UUID to {server}. Status code: {response.status_code}')
        except requests.exceptions.RequestException as e:
            print(f'Error sending UUID to {server}: {e}')

    return jsonify({'status': 'success', 'message': 'UUID sent to all servers'}), 200
    

@app.route('/vpnAPI2-createConfig', methods=['POST'])
@cross_origin()
def vpnAPI2_createConfig(order_id=None):
    if request.method == 'POST':
        data = request.get_json()
        order_id = data.get('order_id', '')
    else:
        order_id = order_id or ''

    user_id = getUsername(order_id).json.get('result')
    real_varSku = getOrderSKU(order_id).json.get('result')

    if order_id == '' or order_id is None or user_id == '' or user_id is None:
        return jsonify({'error': 'Invalid order_id or user_id'}), 400

    # 检查order_id是否已经用来续订或创建配置
    if is_order_used(user_id, order_id):
        return jsonify({'error': 'Order already used'}), 400

    # 记录创建配置操作
    record_operation(user_id, order_id, '', 'create')
    
    result = getOrderInfo(order_id).json.get('result')
    product_details = getDetails(result['data']['page']['rows'][0]['id'])
    if(result['data']['page']['rows'][0]['orderItemList'][0]['varSku'] != None):
        product_sku = product_details['data']['orderDetail']['orderItemVoList'][0]['varSku']
        
        generateVPNConfig(product_details, product_sku)
    
    # Record new user
    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/vpn_users.db")
    c = conn.cursor()
    
    # 创建users表
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (order_id TEXT PRIMARY KEY,
                 user_id TEXT,
                 var_sku TEXT,
                 telco TEXT,
                 created_date TEXT,
                 expired_date TEXT)''')
                 
    if is_first_call(order_id):
        # 插入新记录到users表
        created_date = datetime.datetime.now().strftime('%Y-%m-%d')
        expired_date = get_formatted_exp_date_by_username(order_id)
        
        c.execute("INSERT INTO users (order_id, user_id, var_sku, telco, created_date, expired_date) VALUES (?, ?, ?, ?, ?, ?)",
                  (order_id, user_id, real_varSku, '', created_date, expired_date))
    
    conn.commit()
    conn.close()

    return jsonify({'message': 'Config created successfully'})
    
    
@app.route('/vpnAPI2-renewOrder', methods=['POST'])
@cross_origin()
def vpnAPI2_renewOrder():
    try:
        data = request.get_json()
        
        from_order_id = data.get('from_order_id', '')
        to_order_id = data.get('to_order_id', '')
        
        # Validate order IDs are provided
        if not from_order_id or not to_order_id:
            return jsonify({'error': 'Both order IDs are required'}), 400
        
        # Get user and SKU information
        user_response = getUsername(from_order_id)
        from_sku_response = getOrderSKU(from_order_id)
        to_sku_response = getOrderSKU(to_order_id)
        
        # Validate API responses
        if not user_response.json or not from_sku_response.json or not to_sku_response.json:
            return jsonify({'error': 'Failed to fetch order details'}), 400
            
        user_id = user_response.json.get('result')
        from_sku = from_sku_response.json.get('result')
        to_sku = to_sku_response.json.get('result')
        
        # Validate user and SKU data
        if not user_id or not from_sku or not to_sku:
            return jsonify({'error': 'Invalid order details'}), 400
            
        # Check if order is already used
        if is_order_used(user_id, from_order_id):
            return jsonify({'error': 'Order already used'}), 400
        
        # Define VPN types
        VPN_TYPES = ['sg_', 'my_', 'my_highspeed_']
        
        # Get VPN type for both orders
        from_type = None
        to_type = None
        
        # Determine the VPN type for from_sku
        for vpn_type in VPN_TYPES:
            if from_sku.startswith(vpn_type):
                from_type = vpn_type
                break
                
        # Determine the VPN type for to_sku
        for vpn_type in VPN_TYPES:
            if to_sku.startswith(vpn_type):
                to_type = vpn_type
                break
        
        # Validate that both SKUs are recognized types
        if not from_type or not to_type:
            return jsonify({
                'error': 'Unrecognized VPN type',
                'details': f'From SKU: {from_sku}, To SKU: {to_sku}'
            }), 400
        
        # Validate SKU types match
        if from_type != to_type:
            return jsonify({
                'error': 'Cannot renew different VPN types',
                'details': f'From: {from_type}, To: {to_type}'
            }), 400
        
        # Process the renewal
        try:
            record_operation(user_id, from_order_id, to_order_id, 'renew')
            update_user_expired_date(to_order_id, get_renew_days(to_sku))
            update_user_data(to_order_id, get_renew_days(to_sku))
        except Exception as e:
            return jsonify({
                'error': 'Failed to process renewal',
                'details': str(e)
            }), 500
        
        return jsonify({
            'message': 'Order renewed successfully',
            'user_id': user_id,
            'from_order': from_order_id,
            'to_order': to_order_id,
            'vpn_type': to_type
        })
        
    except Exception as e:
        return jsonify({
            'error': 'Unexpected error occurred',
            'details': str(e)
        }), 500

def get_renew_days(var_sku):
    if var_sku in ['my_30', 'my_highspeed_30', 'sg_30', 'sg_highspeed_30']:
        return 40
    elif var_sku in ['my_15', 'my_highspeed_15', 'sg_15', 'sg_highspeed_15']:
        return 15
    elif var_sku in ['my_60', 'my_highspeed_60', 'sg_60', 'sg_highspeed_60']:
        return 70
    else:
        return 0

def is_order_used(user_id, order_id):
    conn = sqlite3.connect('/www/wwwroot/MTYB_RedeemBot_System/vpn_orders.db')
    c = conn.cursor()
    
    # 创建表用于记录续订和创建配置的操作
    c.execute('''CREATE TABLE IF NOT EXISTS vpn_operations
                 (user_id TEXT, from_order_id TEXT, to_order_id TEXT, operation TEXT)''')
    conn.commit()

    c.execute("SELECT * FROM vpn_operations WHERE user_id=? AND from_order_id=?", (user_id, order_id))
    result = c.fetchone()
    conn.close()
    return result is not None

def record_operation(user_id, from_order_id, to_order_id, operation):
    conn = sqlite3.connect('/www/wwwroot/MTYB_RedeemBot_System/vpn_orders.db')
    c = conn.cursor()
    
    # 创建表用于记录续订和创建配置的操作
    c.execute('''CREATE TABLE IF NOT EXISTS vpn_operations
                 (user_id TEXT, from_order_id TEXT, to_order_id TEXT, operation TEXT)''')
    conn.commit()
    
    
    c.execute("INSERT INTO vpn_operations (user_id, from_order_id, to_order_id, operation) VALUES (?, ?, ?, ?)",
              (user_id, from_order_id, to_order_id, operation))
    conn.commit()     
    conn.close()

@app.route('/vpnAPI2-getOrderInfo', methods=['POST'])
@cross_origin()
def vpnAPI2_getOrderInfo():
    data = request.get_json()
    orderId = data.get('orderid', '')
    if(orderId == '' or orderId == None):
        return jsonify({}), 400
    
    real_varSku = getOrderSKU(orderId).json.get('result')
    user_id = getUsername(orderId).json.get('result')
    
    if(user_id == "" or user_id == None):
        conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/vpn_users.db")
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS users
                 (order_id TEXT PRIMARY KEY,
                 user_id TEXT,
                 var_sku TEXT,
                 telco TEXT,
                 created_date TEXT,
                 expired_date TEXT)''')
        
        c.execute("SELECT * FROM users WHERE order_id = ? ORDER BY created_date DESC", (orderId,))
        first_row = c.fetchone()
        conn.close()
        
        if(first_row != None):
            user_id = first_row[1]
            real_varSku = first_row[2]
        
    if(real_varSku != "" and real_varSku != None):
        return jsonify({
            'varSku': real_varSku,
            'username': user_id
        }), 200
    else:
        return jsonify({}), 400

@app.route('/vpnAPI2-getOrdersByUsername', methods=['POST'])
@cross_origin()
def vpnAPI2_getOrdersByUsername():
    data = request.get_json()
    orderId = data.get('orderid', '')
    if(orderId == '' or orderId == None):
        return jsonify({}), 400
    
    user_id = getUsername(orderId).json.get('result')
    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/vpn_users.db")
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS users
             (order_id TEXT PRIMARY KEY,
             user_id TEXT,
             var_sku TEXT,
             telco TEXT,
             created_date TEXT,
             expired_date TEXT)''')
    
    c.execute("SELECT * FROM users WHERE (user_id = ? OR order_id = ?) ORDER BY created_date DESC", (user_id, orderId,))
    orders = c.fetchall()
    conn.close()
    
    if(len(orders) == 0):
        return jsonify({}), 400
    
    result = []
    for order in orders:
        order_data = {
            'order_id': order[0],
            'user_id': order[1],
            'var_sku': order[2],
            'telco': order[3],
            'created_date': order[4],
            'expired_date': order[5]
        }
        result.append(order_data)
    
    return jsonify(result)
    

@app.route('/vpnAPI2', methods=['POST'])
def vpnAPI2():
    
    # 创建或连接到SQLite数据库
    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/vpn_users.db")
    c = conn.cursor()
    
    # 创建users表
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (order_id TEXT PRIMARY KEY,
                 user_id TEXT,
                 var_sku TEXT,
                 telco TEXT,
                 created_date TEXT,
                 expired_date TEXT)''')
                 
    data = request.get_json()
    username = data.get('orderid')
    varSku = data.get('varSku')
    plan = data.get('plan')
    telco = data.get('telco')
    isIOS = data.get('isIOS')
    server = data.get('server', None)

    real_varSku = getOrderSKU(username).json.get('result')
    user_id = getUsername(username).json.get('result')
    
    payload = {
        'username': username,
        'varSku': varSku,
        'plan': plan,
        'telco': telco
    }

    server_url_map = {
        "my1": MY_serverIP+MY_api_getConfig,
        "my2": MY_serverIP2+MY_api_getConfig2,
        "my6": MY_serverIP6+MY_api_getConfig6,
        "my8": MY_serverIP8+MY_api_getConfig8,
        "my7": MY_serverIP7+MY_api_getConfig7,
        "my11": MY_serverIP11+MY_api_getConfig11,
        "my12": MY_serverIP12+MY_api_getConfig12,
        "sg4": MY_serverIP4+MY_api_getConfig4,
    }
    
    server_abbreviation_map = {
        "my1": "M1",
        "my2": "M2",
        "my6": "M6",
        "my8": "M8",
        "my7": "M7",
        "my11": "M11",
        "my12": "M12",
        "sg3": "S3",
        "sg4": "S4"
    }

    if server != "":
        # If server is provided, use the specified server
        server_urls = [server_url_map[server]]
    else:
        # If server is not provided, search through all servers
        server_urls = list(server_url_map.values())
    print("00--------------------------------------------00")
    for server_url in server_urls:
        print(server_url)
        result = requests.post(url=server_url, json=payload)
        print(result.content)
        result_json = result.json()
        
        if result_json is not None:
            if 'You are not allowed' in result_json.get('message', ''):
                conn.close()
                return result_json
                
            # 解析服务器响应,提取telco、config、server和uuid
            response_str = result_json.get('config', '')
            response_message = result_json.get('message', '')
            uuid = response_str
            server_name = next((k for k, v in server_url_map.items() if v == server_url), None)
            server_name = server_abbreviation_map.get(server_name, server_name)
            
            if response_message != "Cant find username in database stock" and response_message != "":
                result_order = []
                count = 0
                if(user_id != None and user_id != ""):
                    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/vpn_users.db")
                    c = conn.cursor()
                    
                    c.execute("SELECT * FROM users WHERE user_id = ? AND order_id = ? ORDER BY created_date DESC", (user_id, username,))
                    orders = c.fetchall()
                    
                    for order in orders:
                        order_data = {
                            'order_id': order[0],
                            'user_id': order[1],
                            'var_sku': order[2],
                            'telco': order[3],
                            'created_date': order[4],
                            'expired_date': order[5]
                        }
                        result_order.append(order_data)
                        count = count + 1
                        
                expired_date = get_formatted_exp_date_by_username(username)
                if (count == 1):
                    vless_config = generate_vless_config(telco, plan, server_name, uuid, real_varSku, result_order[0]['expired_date'], username)
                else:
                    vless_config = generate_vless_config(telco, plan, server_name, uuid, real_varSku, expired_date, username)
                
                # 检查是否为第一次调用该API
                c.execute("SELECT telco FROM users WHERE order_id = ?", (username,))
                result = c.fetchone()
                
                if result is not None:
                    current_telco = result[0]
                    if current_telco is None or current_telco == '':
                        c.execute("UPDATE users SET telco = ? WHERE order_id = ?", (telco, username))
                        conn.commit()
                
                conn.close()
                
                if (count == 1):
                    return {'status': 0, 'created_date': get_created_date_by_username(username), 'expired_date': result_order[0]['expired_date'], 'config': vless_config, 'message': "Successfully Generated Config"}
                else: 
                    return {'status': 0, 'created_date': get_created_date_by_username(username), 'expired_date': expired_date, 'config': vless_config, 'message': "Successfully Generated Config"}
            
            if response_str != '':
                conn.close()
                return result_json
    conn.close()
    return {'status': 0, 'created_date': 0, 'expired_date': 0, 'config': 'Error, Please Screenshot to Shopee Seller', 'message': "Failed To Generated Config"}

def generate_vless_config(telco, config, server_name, uuid, varSku, expired_date, orderid=""):

    vless_templates = {
        "digi": {
            "no_plan": "vless://{uuid}@*************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#DN_{server_name}_{expired_date}",
            "no_plan2": "vless://{uuid}@************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#DN_{server_name}_{expired_date}",
            "booster": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#DU_{server_name}_{expired_date}",
            "booster2": "vless://{uuid}@cdn.weglot.com:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#DU_{server_name}_{expired_date}",
            "social": "vless://{uuid}@ssl.google-analytics.com.api.digi.com.my.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=ssl.google-analytics.com#DS_{server_name}_{expired_date}",
            "social2": "vless://{uuid}@m.facebook.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.facebook.com#DS2_{server_name}_{expired_date}",
            "other": "vless://{uuid}@api.useinsider.com:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=api.useinsider.com.{server_domain}#DN_{server_name}_{expired_date}"
        },
        "umobile": {
            "funz": "vless://{uuid}@www.pubgmobile.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.pubgmobile.com#UN_{server_name}_{expired_date}",
            "no_plan3": "vless://{uuid}@cityofpalacios.org:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cityofpalacios.org.{server_domain}#UNP3_{server_name}_{expired_date}",
            "no_plan4": "vless://{uuid}@***********:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cityofpalacios.org.{server_domain}#UNP4_{server_name}_{expired_date}",
            "no_plan5": "vless://{uuid}@somersby.pt:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=somersby.pt.{server_domain}#UNP4_{server_name}_{expired_date}"
        },
        "celcom": {
            "booster": "vless://{uuid}@*************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#CB_{server_name}_{expired_date}",
            "booster2": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#CB2_{server_name}_{expired_date}"
        },
        "maxis": {
            "tv": "vless://{uuid}@help.viu.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=help.viu.com#MT_{server_name}_{expired_date}",
            "tv2": "vless://{uuid}@umc.viu.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=umc.viu.com#MT2_{server_name}_{expired_date}",
            "tv3": "vless://{uuid}@adl-ga-pccw.viu.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=adl-ga-pccw.viu.com#MT3_{server_name}_{expired_date}",
            "no_plan": "vless://{uuid}@api-faceid.maxis.com.my.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.mosti.gov.my#MN_{server_name}_{expired_date}",
            "freeze": "vless://{uuid}@cdn.opensignal.com:80?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cdn.opensignal.com.{server_domain}#MF_{server_name}_{expired_date}",
            "booster": "vless://{uuid}@************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cdn.opensignal.com.{server_domain}#MF_{server_name}_{expired_date}"
        },
        "tunetalk": {
            "booster": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#TB_{server_name}_{expired_date}",
            "booster2": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#TB2_{server_name}_{expired_date}",
            "booster3": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=api.fast.com.{server_domain}#TB3_{server_name}_{expired_date}",
        },
        "yes": {
            "no_plan": "vless://{uuid}@who.int:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cdn.who.int.{server_domain}#YN_{server_name}_{expired_date}",
            "no_plan2": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=tap-database.who.int.{server_domain}#YN2_{server_name}_{expired_date}"
        },
        "yoodo": {
            "booster": "vless://{uuid}@*************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#YoB_{server_name}_{expired_date}",
            "booster2": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#YoB2_{server_name}_{expired_date}",
            "booster3": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=api.fast.com.{server_domain}#YoB3_{server_name}_{expired_date}",
            "pubg": "vless://{uuid}@m.pubgmobile.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.pubgmobile.com#YoP_{server_name}_{expired_date}",
            "mobilelegend": "vless://{uuid}@m.mobilelegends.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.mobilelegends.com#YoM_{server_name}_{expired_date}"
        },
        "unifi": {
            "bebas": "vless://{uuid}@************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#UnN_{server_name}_{expired_date}",
            "wow": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#UnN2_{server_name}_{expired_date}",
            "no_plan3": "vless://{uuid}@opensignal.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}#UnN3_{server_name}_{expired_date}"
        },
        "default": "No Config Found, Please Contact Shopee Seller."
    }
    
    server_domain_map = {
        "M7": {
            "domain": "server7.steam.autos",
            "port": 80,
            "path": "/"
        },
        "M11": {
            "domain": "server11.steam.autos",
            "port": 80,
            "path": "/"
        },
        "M12": {
            "domain": "server12.steam.autos",
            "port": 80,
            "path": "/mtyb_official"
        },
        "M1": {
            "domain": "server1.steam.autos",
            "port": 80,
            "path": "/"
        },
        "M2": {
            "domain": "server2.steam.autos",
            "port": 80,
            "path": "/"
        },
        "S3": {
            "domain": "server3.steam.autos",
            "port": 80,
            "path": "/"
        },
        "S4": {
            "domain": "server4.steam.autos",
            "port": 80,
            "path": "/"
        },
        "M6": {
            "domain": "server6.steam.autos",
            "port": 80,
            "path": "/"
        },
        "M8": {
            "domain": "server8.steam.autos",
            "port": 80,
            "path": "/"
        }
    }
    
    vless_template = vless_templates.get(telco, {}).get(config, vless_templates["default"])
    
    # Custom rules
    if vless_template != vless_templates["default"] and orderid != "admin":
        if telco == "digi" and config == "no_plan":
            price_amount = getOrderPrice(orderid).json.get('result')
            if float(price_amount) < 1:
                vless_template = "You are not buying [Special] Config, Not allowed to redeem this config."
        elif telco == "maxis" and config == "freeze":
            price_amount = getOrderPrice(orderid).json.get('result')
            if float(price_amount) < 1:
                vless_template = "You are not buying [Special] Config, Not allowed to redeem this config."

    server_info = server_domain_map.get(server_name, {"domain": "default.steam.autos", "port": 80, "path": "/"})
    
    # Custom rules
    if(telco == "umobile"):
        if(server_name == "M11" and config == "no_plan"):
            server_info["domain"] = "d3mrxvzu0st89g.cloudfront.net"
        elif (server_name == "M7" and config == "no_plan"):
            server_info["domain"] = "d1gg9kwbsmczq4.cloudfront.net"
        elif (server_name == "M7" and config == "no_plan2"):
            server_info["domain"] = "d1gg9kwbsmczq4.cloudfront.net"
    
    return vless_template.format(
        uuid=uuid,
        server_domain=server_info["domain"],
        port=server_info["port"],
        path=server_info["path"],
        telco=telco,
        config=config,
        server_name=server_name,
        expired_date=expired_date
    )

def update_user_data(target_email, validity):
    server_domain_map = {
        "M7": {
            "domain": "server7.steam.autos",
            "port": 10,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M11": {
            "domain": "server11.steam.autos",
            "port": 10,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M12": {
            "domain": "server12.steam.autos",
            "port": 10,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M1": {
            "domain": "server1.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M2": {
            "domain": "server2.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M6": {
            "domain": "server6.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M8": {
            "domain": "server8.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "S4": {
            "domain": "server4.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        }
    }
    
    for server_id, server_info in server_domain_map.items():
        domain = server_info["domain"]
        port = server_info["port"]
        path = server_info["path"]
        username = server_info["username"]
        password = server_info["password"]

        # 登录到服务器
        login_url = f"http://{domain}:{port}{path}/login"
        login_payload = {
            "username": username,
            "password": password
        }
        session = requests.Session()
        response = session.post(login_url, data=login_payload)
        
        if response.status_code == 200:
            print(f"Successfully logged in to server {server_id}")
            
            # 获取inbound列表
            inbound_list_url = f"http://{domain}:{port}{path}/panel/inbound/list"
            response = session.post(inbound_list_url)
            
            if response.status_code == 200:
                inbound_list = response.json()["obj"]
                
                for inbound in inbound_list:
                    for client in inbound["clientStats"]:
                        if client["email"].startswith(target_email):
                            
                            # 找到目标用户，获取现有设置
                            print("Target Found: " + client["email"])
                            existing_settings = next((c for c in json.loads(inbound["settings"])["clients"] if c["email"].startswith(target_email)), None)
                            
                            if existing_settings:
                                # 根据现有设置和validity值更新用户数据
                                new_settings = existing_settings.copy()
                                
                                if str(existing_settings["expiryTime"]).startswith("-"):
                                    # 如果expiryTime以"-"开头,表示剩余天数
                                    remaining_days = int(str(existing_settings["expiryTime"])[1:])
                                    new_expiry_time = int(time.time() * 1000) + (remaining_days + validity) * 24 * 60 * 60 * 1000
                                else:
                                    # 如果expiryTime是时间戳
                                    existing_expiry_time = existing_settings["expiryTime"]
                                    if existing_expiry_time < int(time.time() * 1000):
                                        # 如果已经过期,从当前时间开始计算新的过期时间
                                        new_expiry_time = int(time.time() * 1000) + validity * 24 * 60 * 60 * 1000
                                    else:
                                        # 如果还没过期,在原有过期时间的基础上增加时间
                                        new_expiry_time = existing_expiry_time + validity * 24 * 60 * 60 * 1000
                                new_settings["expiryTime"] = new_expiry_time
                                new_settings["totalGB"] = existing_settings["totalGB"] + 50 * validity * 1024 * 1024 * 1024  # 将50GB转换为字节
                                
                                update_client_url = f"http://{domain}:{port}{path}/panel/inbound/updateClient/{new_settings['id']}"
                                update_payload = {
                                    "id": inbound["id"],
                                    "settings": json.dumps({"clients": [new_settings]})
                                }
                                
                                
                                response = session.post(update_client_url, data=update_payload)
                                
                                if response.status_code == 200:
                                    print(response.content)
                                    print(f"Successfully updated user data for {target_email} on server {server_id}")
                                else:
                                    print(f"Failed to update user data for {target_email} on server {server_id}")
                            else:
                                print(f"Existing settings not found for {target_email} on server {server_id}")
                            
                            break
                    else:
                        continue
                    break
            else:
                print(f"Failed to retrieve inbound list from server {server_id}")
        else:
            print(f"Failed to log in to server {server_id}")

import requests

def check_online_users_ips():
    server_domain_map = {
        "M7": {
            "domain": "server7.steam.autos",
            "port": 10,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M11": {
            "domain": "server11.steam.autos",
            "port": 10,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M12": {
            "domain": "server12.steam.autos",
            "port": 10,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M1": {
            "domain": "server1.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M2": {
            "domain": "server2.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "S4": {
            "domain": "server4.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M6": {
            "domain": "server6.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        },
        "M8": {
            "domain": "server8.steam.autos",
            "port": 8070,
            "path": "",
            "username": "limjianhui789",
            "password": "whitepaperh0817"
        }
    }
    
    users_with_multiple_ips = []
    email_server_map = {}  # 新增：用于跟踪每个email连接的服务器
    
    for server_id, server_info in server_domain_map.items():
        domain = server_info["domain"]
        port = server_info["port"]
        path = server_info["path"]
        username = server_info["username"]
        password = server_info["password"]
        
        login_url = f"http://{domain}:{port}{path}/login"
        login_payload = {
            "username": username,
            "password": password
        }
        session = requests.Session()
        response = session.post(login_url, data=login_payload)
        
        if response.status_code == 200:
            print(f"Successfully logged in to server {server_id}")
            
            online_users_url = f"http://{domain}:{port}{path}/panel/inbound/onlines"
            response = session.post(online_users_url)
            
            if response.status_code == 200:
                online_users = response.json()["obj"]
                
                for user_email in online_users:
                    client_ips_url = f"http://{domain}:{port}{path}/panel/inbound/clientIps/{user_email}"
                    response = session.post(client_ips_url)
                    
                    if response.status_code == 200:
                        client_ips = response.json()["obj"]
                        
                        # 检查是否有多个IP
                        if len(client_ips) > 3:
                            users_with_multiple_ips.append({
                                "server_id": server_id,
                                "email": user_email,
                                "ips": client_ips
                            })
                        
                        # 检查是否在多个服务器上登录
                        if user_email in email_server_map:
                            if server_id not in email_server_map[user_email]:
                                email_server_map[user_email].append(server_id)
                        else:
                            email_server_map[user_email] = [server_id]
                    else:
                        print(f"Failed to retrieve client IPs for {user_email} on server {server_id}")
            else:
                print(f"Failed to retrieve online users from server {server_id}")
        else:
            print(f"Failed to log in to server {server_id}")
    
    # 检查重复登录
    duplicated_logins = [
        {"email": email, "servers": servers}
        for email, servers in email_server_map.items()
        if len(servers) > 1
    ]
    
    return {
        "users_with_multiple_ips": users_with_multiple_ips,
        "duplicated_logins": duplicated_logins
    }

@app.route('/check_online_users_ips', methods=['GET'])
def check_online_users_ips_route():
    result = check_online_users_ips()
    return jsonify(result)
    
@app.route('/vpnAPI', methods=['POST'])
def vpnAPI():
    data = request.get_json()
    username = data.get('orderid')
    varSku = data.get('varSku')
    plan = data.get('plan')
    telco = data.get('telco')
    isIOS = data.get('isIOS')
    server = data.get('server', None)
    real_varSku = getOrderSKU(username).json.get('result')
    print(real_varSku)
    
    payload = {
        'username': username,
        'varSku': varSku,
        'plan': plan,
        'telco': telco
    }
    
    
    
    if(server != None):
        if(server == "my1"):
            result = requests.post(url=MY_serverIP+MY_api_getConfig, json=payload)
            result_json = result.json()
            if(result_json != None):
                return result_json
        elif(server == "my2"):
            result = requests.post(url=MY_serverIP2+MY_api_getConfig2, json=payload)
            result_json = result.json()
            if(result_json != None):
                return result_json
        elif(server == "my6"):
            result = requests.post(url=MY_serverIP6+MY_api_getConfig6, json=payload)
            result_json = result.json()
            if(result_json != None):
                return result_json
        elif(server == "my8"):
            result = requests.post(url=MY_serverIP8+MY_api_getConfig8, json=payload)
            result_json = result.json()
            if(result_json != None):
                return result_json
        elif(server == "my7"):
            result = requests.post(url=MY_serverIP7+MY_api_getConfig7, json=payload)
            result_json = result.json()
            if(result_json != None):
                return result_json
        elif(server == "my11"):
            result = requests.post(url=MY_serverIP11+MY_api_getConfig11, json=payload)
            result_json = result.json()
            if(result_json != None):
                return result_json
        elif(server == "my12"):
            result = requests.post(url=MY_serverIP12+MY_api_getConfig12, json=payload)
            result_json = result.json()
            if(result_json != None):
                return result_json
                    
    if(varSku == 'my_15' or varSku == 'my_30' or varSku == 'my_60' or varSku == 'my_hs_15' or varSku == 'my_hs_30' or varSku == 'my_hs_60'):
        
        # Search from server 1
        result = requests.post(url=MY_serverIP+MY_api_getConfig, json=payload)
        result_json = result.json()
        
        # Search from server 2
        if(result_json.get('message') == 'Cant find username in database stock'):
            result = requests.post(url=MY_serverIP2+MY_api_getConfig2, json=payload)
            result_json = result.json()
        
        # Search From Server 6
        if(result_json.get('message') == 'Cant find username in database stock'):
            result = requests.post(url=MY_serverIP6+MY_api_getConfig6, json=payload)
            result_json = result.json()
        
        # Search From Server 8
        if(result_json.get('message') == 'Cant find username in database stock'):
            result = requests.post(url=MY_serverIP8+MY_api_getConfig8, json=payload)
            result_json = result.json()
        
        # Request GX Config From Appium
        print("Telco = " + telco)
        print("Plan = " + plan)
        if((telco == 'umobile' and plan == '123') or
        (telco == 'umobile' and plan == '456') 
        ):
            if(result_json.get('status') == 0 and result_json.get('config') != None):
                if(result_json.get('config').startswith("[")):
                    return result_json
                
                if(isIOS == "false"):
                    '''
                    encrypt_payload = {
                        'config': result_json.get('config'),
                        'preventHotspot': "false"
                    }
                    result = requests.post(url="http://online-mtyb.ddns.net:8060/Appium2/ArmodVPN_Main", json=encrypt_payload)
                    content = result.content
                    if(content.startswith(b"Request Exists")):
                        result_json['config'] = "Other People Generating Config, Please Try Again Later. (10s)"
                    else:
                        encrypted_result = result.json()
                        if(encrypted_result.get('encrypted_config') != "null"):
                            write_or_append_to_file(f"{username} - \"{result_json['config']}\"\n\n")
                            result_json['config'] = encrypted_result.get('encrypted_config')
                            write_or_append_to_file(f"{username} - \"{result_json['config']}\"\n\n")
                            write_or_append_to_file(f"-------------------------------\n\n")
                    '''
                else:
                    encrypt_payload = {
                        'config': result_json.get('config'),
                        'preventHotspot': "false",
                        'username': username,
                        'isIOS': isIOS
                    }
                    result = requests.post(url="http://online-mtyb.ddns.net:8060/Appium2/NapsternetV_Main", json=encrypt_payload)
                    content = result.content
                    if(content.startswith(b"Request Exists")):
                        result_json['config'] = "Other People Generating Config, Please Try Again Later. (30s)"
                    else:
                        encrypted_result = result.json()
                        if(encrypted_result.get('encrypted_config') != "null"):
                            write_or_append_to_file(f"{username} - \"{result_json.get('config')}\"\n\n")
                            result_json['config'] = encrypted_result.get('encrypted_config')
                            write_or_append_to_file(f"{username} - \"{result_json['config']}\"\n\n")
                            write_or_append_to_file(f"-------------------------------\n\n")
                            if isIOS == "true":
                                result_json['status'] = 11
                                result_json['real_varSku'] = real_varSku
                            else:
                                result_json['status'] = 12
                                result_json['real_varSku'] = real_varSku
            
        if(0 == 1):
            if(result_json.get('status') == 0 and result_json.get('config') != None):
                if(result_json.get('config').startswith("[")):
                    return result_json
                
                ori_config = result_json.get('config')
                # ori_config = replace_uuid(ori_config, "66c6a904-7b5f-41ad-92a0-16fdfda05e30")
                # ori_config = replace_server(ori_config, "/xrayws", "/vless")
                # ori_config = replace_server(ori_config, "security=tls", "security=none")
                # ori_config = replace_server(ori_config, ":443", ":80")
                # ori_config = replace_server(ori_config, ":8080", ":80")
                # ori_config = replace_server(ori_config, "server1.steam.autos", "proxy.my3.steam.autos")
                # ori_config = replace_server(ori_config, "server2.steam.autos", "proxy.my3.steam.autos")
                # ori_config = replace_server(ori_config, "server4.steam.autos", "proxy.my3.steam.autos")
                # ori_config = replace_server(ori_config, "server5.steam.autos", "proxy.my3.steam.autos")
                
                
                encrypt_payload = {
                    'config': ori_config,
                    'preventHotspot': "false",
                    'username': username,
                    'isIOS': isIOS
                }
                
                result = requests.post(url="http://online-mtyb.ddns.net:8060/Appium2/NapsternetV_Main", json=encrypt_payload)
                content = result.content
                if(content.startswith(b"Request Exists")):
                    result_json['config'] = "Other People Generating Config, Please Try Again Later. (30s)"
                else:
                    encrypted_result = result.json()
                    if(encrypted_result.get('encrypted_config') != "null"):
                        write_or_append_to_file(f"{username} - \"{result_json.get('config')}\"\n\n")
                        write_or_append_to_file(f"{username} - \"{ori_config}\"\n\n")
                        write_or_append_to_file(f"-------------------------------\n\n")
                        result_json['config'] = encrypted_result.get('encrypted_config')
                        if isIOS == "true":
                            result_json['status'] = 11
                            result_json['real_varSku'] = real_varSku
                        else:
                            result_json['status'] = 12
                            result_json['real_varSku'] = real_varSku
        
        return result_json
    
    elif (varSku == 'sg_highspeed_15' or varSku == 'sg_highspeed_30' or varSku == 'sg_highspeed_60'):
        
        # Search from server 4 (SG)
        result = requests.post(url=MY_serverIP4+MY_api_getConfig4, json=payload)
        result_json = result.json()
        
        return result_json
    
    elif (varSku == 'my_highspeed_15' or varSku == 'my_highspeed_30' or varSku == 'my_highspeed_60'):
        # Search from High Speed server
        result = requests.post(url=MY_serverIP7+MY_api_getConfig7, json=payload)
        result_json = result.json()
        
        if(result_json.get('message') == 'Cant find username in database stock'):
            result = requests.post(url=MY_serverIP11+MY_api_getConfig11, json=payload)
            result_json = result.json()
        
        if(result_json.get('message') == 'Cant find username in database stock'):
            result = requests.post(url=MY_serverIP12+MY_api_getConfig12, json=payload)
            result_json = result.json()
            
        
        return result_json
        
    return jsonify(request.get_json())
    
def write_or_append_to_file(content, mode="a"):
    file_path = "/www/wwwroot/MTYB_RedeemBot_System/configRedeemLog.txt"

    try:
        with open(file_path, mode) as file:
            file.write(content)
    except FileNotFoundError:
        # If the file does not exist, create it and then write/append content
        with open(file_path, "w") as file:
            file.write(content)


def replace_server(vless, old_server, new_server):
    return vless.replace(old_server, new_server)

# Function to replace the UUID with a new one
def replace_uuid(vless, new_uuid):
    pattern = r'vless://(.*?)@'
    match = re.search(pattern, vless)
    if match:
        original_uuid = match.group(1)
        new_uuid = new_uuid  # Replace 'your_new_uuid_here' with your desired UUID
        replaced_vless = vless.replace(original_uuid, new_uuid)
        return replaced_vless
    else:
        return vless

@app.route('/getExpDateByUsername', methods=['GET'])
def get_formatted_exp_date_by_username(username=""):
    if username == "":
        username = request.args.get('username', None)
    
    # 连接到SQLite数据库
    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/VPN_Customer.db")
    cursor = conn.cursor()

    # 查询指定用户名的FormattedExpDate
    cursor.execute("SELECT ExpirationDate FROM info WHERE Username=?", (username,))
    result = cursor.fetchone()

    # 关闭连接
    conn.close()

    if result:
        return result[0]
    else:
        return "Invalid Request"
        
def get_created_date_by_username(username):
    # 连接到SQLite数据库
    conn = sqlite3.connect("/www/wwwroot/MTYB_RedeemBot_System/VPN_Customer.db")
    cursor = conn.cursor()

    # 查询指定用户名的FormattedExpDate和Sku
    cursor.execute("SELECT ExpirationDate, Sku FROM info WHERE Username=?", (username,))
    result = cursor.fetchone()

    # 关闭连接
    conn.close()

    if result:
        formatted_exp_date_str = result[0]
        sku = result[1]

        # 将日期字符串转换为datetime对象
        formatted_exp_date = datetime.datetime.strptime(formatted_exp_date_str, "%Y-%m-%d")

        # 根据SKU确定有效期
        validity_period = get_validity_period(sku)

        # 计算创建日期
        created_date = formatted_exp_date - validity_period

        return created_date.strftime("%Y-%m-%d")
    else:
        return "Invalid Request"

def get_validity_period(sku):
    if sku == "my_15" or sku == "sg_15" or sku == "my_hs_15" or sku == "my_highspeed_15" or sku == "sg_highspeed_15":
        return timedelta(days=15)
    elif sku == "my_30" or sku == "sg_30" or sku == "my_hs_30" or sku == "my_highspeed_30" or sku == "sg_highspeed_30":
        return timedelta(days=40)
    elif sku == "my_60" or sku == "sg_60" or sku == "my_hs_60" or sku == "my_highspeed_60" or sku == "sg_highspeed_60":
        return timedelta(days=70)
    else:
        raise ValueError(f"Unknown SKU: {sku}")

@app.route('/chat', methods=['POST'])
def chat():
    data = request.get_json()
    # url = 'http://api.aichatos.cloud/api/generateStream'
    url = "https://api.binjie.fun/api/generateStream"
    customerMSG = data.get('customerMSG')
    sessionId = data.get('sessionId')
    if customerMSG == None:
        customerMSG = ""
    
    randomNumber = random.randint(50000,99999)
    
    data = {
    "prompt":"""
    
Sources >>>

### Telco Maxis ###
Q: Maxis or Hotlink can use the bypass config or not ?
A: Maxis/Hotlink have to subscribe the maxis tv plan rm18 (Unlimited) at https://tv.maxis.com.my

### Telco Tunetalk ###

Q: Tunetalk can bypass ?
A: Yes, You need to subscribe 3mbps unlimited data plan to bypass the 3mbps speed cap.

\n\n###\n\n

Customer: %s

    """ % (customerMSG),
    "userId":"#/chat/"+str(sessionId),
    "network":True,
    "apikey":"",
    "system":"",
    "withoutContext":False
    }
    
    headers = {
        "Origin": "https://chat3.aichatos.top",
        "Referer": "https://chat3.aichatos.top/",
        "sec-ch-ua": 'Chromium";v="112", "Microsoft Edge";v="112", "Not:A-Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "cross-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.48"
    }
    
    response = requests.post(url, json=data, headers=headers)
    logging.basicConfig(filename='chatbot_log.txt', level=logging.INFO)
    logging.info('Customer Message: {}'.format(customerMSG))
    logging.info('Response: {}'.format(response.text))
    
    if "限制" in response.text:
        return jsonify({"message": "Sorry, AI Not Available. Please Try Again Tomorrow"})
    
    return jsonify({"message": response.text})

################ Copy & Paste ################
        
if(__name__ == '__main__'):
    #Listening Server
    app.run(
        host="0.0.0.0",
        port=2096,
        debug=False
    );