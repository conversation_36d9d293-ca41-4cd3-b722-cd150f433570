{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{{ title }}</h3>
                    <div>
                        <a href="{{ url_for('vpn.create_server') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Server
                        </a>
                        <button type="button" class="btn btn-info" onclick="refreshAllHealth()">
                            <i class="fas fa-sync"></i> Refresh All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if servers %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Host</th>
                                    <th>Status</th>
                                    <th>Health</th>
                                    <th>Clients</th>
                                    <th>Last Check</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for server in servers %}
                                <tr id="server-{{ server.id }}">
                                    <td>
                                        <strong>{{ server.name }}</strong>
                                        {% if server.description %}
                                        <br><small class="text-muted">{{ server.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ server.host }}:{{ server.port }}
                                        <br><small class="text-muted">{{ server.username }}</small>
                                    </td>
                                    <td>
                                        {% if server.is_active %}
                                        <span class="badge badge-success">Active</span>
                                        {% else %}
                                        <span class="badge badge-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td id="health-{{ server.id }}">
                                         <span class="badge bg-warning text-dark">
                                             <i class="fas fa-spinner fa-spin"></i> Checking...
                                         </span>
                                    </td>
                                    <td id="clients-{{ server.id }}">
                                         <div class="text-center">
                                             <i class="fas fa-spinner fa-spin text-muted"></i>
                                         </div>
                                    </td>
                                    <td id="lastcheck-{{ server.id }}">
                                        <span class="text-muted">Never</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="testConnection({{ server.id }})" 
                                                    title="Test Connection">
                                                <i class="fas fa-plug"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="refreshHealth({{ server.id }})" 
                                                    title="Refresh Health">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                            <a href="{{ url_for('vpn.edit_server', server_id=server.id) }}"
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('vpn.manage_server_clients', server_id=server.id) }}"
                                               class="btn btn-sm btn-outline-info" title="Manage Clients">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteServer({{ server.id }}, '{{ server.name }}')"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <div class="btn-group mt-1" role="group">
                                            <a href="{{ url_for('vpn.service_status', server_id=server.id) }}" 
                                               class="btn btn-sm btn-outline-secondary" title="Service Status">
                                                <i class="fas fa-cogs"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="restartXray({{ server.id }})" 
                                                    title="Restart Xray">
                                                <i class="fas fa-redo"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-server fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No VPN servers found</h5>
                        <p class="text-muted">Get started by adding your first VPN server.</p>
                        <a href="{{ url_for('vpn.create_server') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add First Server
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Processing...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showLoading() {
    $('#loadingModal').modal('show');
}

function hideLoading() {
    $('#loadingModal').modal('hide');
}

function testConnection(serverId) {
    showLoading();
    
    fetch(`{{ url_for('vpn.test_server_connection', server_id=0) }}`.replace('0', serverId), {
        method: 'POST',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            toastr.success('Connection test successful!');
        } else {
            toastr.error('Connection test failed: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        hideLoading();
        toastr.error('Connection test failed: ' + error.message);
    });
}

function refreshHealth(serverId) {
    // Show spinner while refreshing
    const healthCell = document.getElementById(`health-${serverId}`);
    const clientsCell = document.getElementById(`clients-${serverId}`);
    
    if (healthCell) {
        healthCell.innerHTML = '<span class="badge bg-warning text-dark"><i class="fas fa-spinner fa-spin"></i> Refreshing...</span>';
    }
    if (clientsCell) {
        clientsCell.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin text-muted"></i></div>';
    }
    
    const formData = new FormData();
    formData.append('server_id', serverId);
    
    fetch('{{ url_for("vpn.refresh_health") }}', {
        method: 'POST',
        headers: {
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.is_healthy !== undefined) {
            updateHealthDisplay(serverId, data);
            toastr.success('Health status refreshed!');
        } else {
            // Reload health status
            loadServerHealth(serverId);
            toastr.success('Health refresh triggered!');
        }
    })
    .catch(error => {
        // Reload health status
        loadServerHealth(serverId);
        toastr.error('Failed to refresh health: ' + error.message);
    });
}

function refreshAllHealth() {
    // Show loading indicators for all servers
    {% for server in servers %}
    const healthCell{{ server.id }} = document.getElementById(`health-{{ server.id }}`);
    const clientsCell{{ server.id }} = document.getElementById(`clients-{{ server.id }}`);
    
    if (healthCell{{ server.id }}) {
        healthCell{{ server.id }}.innerHTML = '<span class="badge bg-warning text-dark"><i class="fas fa-spinner fa-spin"></i> Refreshing...</span>';
    }
    if (clientsCell{{ server.id }}) {
        clientsCell{{ server.id }}.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin text-muted"></i></div>';
    }
    {% endfor %}
    
    fetch('{{ url_for("vpn.refresh_health") }}', {
        method: 'POST',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        toastr.success('Health refresh triggered for all servers!');
        // Load health status for each server with delay
        {% for server in servers %}
        setTimeout(() => loadServerHealth({{ server.id }}), {{ loop.index0 * 500 }});
        {% endfor %}
    })
    .catch(error => {
        toastr.error('Failed to refresh health: ' + error.message);
        // Still load health status for each server
        {% for server in servers %}
        setTimeout(() => loadServerHealth({{ server.id }}), {{ loop.index0 * 500 }});
        {% endfor %}
    });
}

function loadServerHealth(serverId) {
    console.log(`Loading health for server ${serverId}`);
    
    // Confirm spinner is showing
    const healthCell = document.getElementById(`health-${serverId}`);
    const clientsCell = document.getElementById(`clients-${serverId}`);
    
    if (healthCell) {
        console.log(`Health cell found for server ${serverId}, current content:`, healthCell.innerHTML);
    } else {
        console.error(`Health cell not found for server ${serverId}`);
        return;
    }
    
    fetch(`{{ url_for('vpn.get_server_health_ajax', server_id=0) }}`.replace('0', serverId))
    .then(response => {
        console.log(`Health response for server ${serverId}:`, response.status);
        return response.json();
    })
    .then(data => {
        console.log(`Health data for server ${serverId}:`, data);
        if (data.success && data.health) {
            updateHealthDisplay(serverId, data.health);
        } else {
            // Show error state
            if (healthCell) {
                healthCell.innerHTML = '<span class="badge bg-danger"><i class="fas fa-times"></i> Error</span>';
            }
            if (clientsCell) {
                clientsCell.innerHTML = '<div class="text-center"><span class="text-muted">-</span></div>';
            }
        }
    })
    .catch(error => {
        console.error('Error loading server health:', error);
        // Show error state
        if (healthCell) {
            healthCell.innerHTML = '<span class="badge bg-danger"><i class="fas fa-times"></i> Error</span>';
        }
        if (clientsCell) {
            clientsCell.innerHTML = '<div class="text-center"><span class="text-muted">-</span></div>';
        }
    });
}

function updateHealthDisplay(serverId, healthData) {
    const healthCell = document.getElementById(`health-${serverId}`);
    const clientsCell = document.getElementById(`clients-${serverId}`);
    
    if (healthCell) {
        let healthHtml = '';
        if (healthData.is_healthy === true) {
            healthHtml = '<span class="badge bg-success"><i class="fas fa-check"></i> Healthy</span>';
        } else if (healthData.is_healthy === false) {
            healthHtml = `<span class="badge bg-danger"><i class="fas fa-exclamation-triangle"></i> ${(healthData.status || 'Unhealthy').charAt(0).toUpperCase() + (healthData.status || 'Unhealthy').slice(1)}</span>`;
        } else {
            healthHtml = '<span class="badge bg-secondary"><i class="fas fa-question"></i> Unknown</span>';
        }
        
        if (healthData.response_time) {
            healthHtml += `<br><small class="text-muted">${healthData.response_time.toFixed(2)}ms</small>`;
        }
        
        healthCell.innerHTML = healthHtml;
    }
    
    if (clientsCell && healthData.client_stats) {
        const stats = healthData.client_stats;
        clientsCell.innerHTML = `
            <div class="text-center">
                <div><strong>${stats.total}</strong> Total</div>
                <div class="small">
                    <span class="text-success">${stats.active} Active</span> |
                    <span class="text-danger">${stats.expired} Expired</span>
                </div>
            </div>
        `;
    }

    const lastCheckCell = document.getElementById(`lastcheck-${serverId}`);
    if (lastCheckCell) {
        if (healthData.last_check) {
            lastCheckCell.innerHTML = `<small>${healthData.last_check}</small>`;
        } else {
            lastCheckCell.innerHTML = `<span class="text-muted">Never</span>`;
        }
    }
}

function restartXray(serverId) {
    if (!confirm('Are you sure you want to restart the Xray service? This will temporarily interrupt connections.')) {
        return;
    }
    
    showLoading();
    
    const formData = new FormData();
    
    fetch(`{{ url_for('vpn.restart_xray', server_id=0) }}`.replace('0', serverId), {
        method: 'POST',
        body: formData
    })
    .then(response => {
        hideLoading();
        if (response.ok) {
            toastr.success('Xray service restarted successfully!');
        } else {
            toastr.error('Failed to restart Xray service');
        }
    })
    .catch(error => {
        hideLoading();
        toastr.error('Failed to restart Xray: ' + error.message);
    });
}

function deleteServer(serverId, serverName) {
    if (!confirm(`Are you sure you want to delete server "${serverName}"? This action cannot be undone.`)) {
        return;
    }
    
    showLoading();
    
    const formData = new FormData();
    
    fetch(`{{ url_for('vpn.delete_server', server_id=0) }}`.replace('0', serverId), {
        method: 'POST',
        body: formData
    })
    .then(response => {
        hideLoading();
        if (response.ok) {
            toastr.success('Server deleted successfully!');
            // Remove the row from table
            document.getElementById(`server-${serverId}`).remove();
        } else {
            toastr.error('Failed to delete server');
        }
    })
    .catch(error => {
        hideLoading();
        toastr.error('Failed to delete server: ' + error.message);
    });
}

// Load health status for all servers when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('VPN Servers page loaded, starting health checks...');
    {% for server in servers %}
    // Add delay between requests to avoid overwhelming the server
    setTimeout(() => {
        console.log('Loading health for server {{ server.id }}');
        loadServerHealth({{ server.id }});
    }, {{ loop.index0 * 500 }});
    {% endfor %}
});

// Auto-refresh health every 5 minutes
setInterval(function() {
    {% for server in servers %}
    setTimeout(() => loadServerHealth({{ server.id }}), {{ loop.index0 * 500 }});
    {% endfor %}
}, 300000);
</script>
{% endblock %}
