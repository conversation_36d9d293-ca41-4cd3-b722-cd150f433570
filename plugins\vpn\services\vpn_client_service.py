"""
VPN Client Service
Handles VPN client management and operations
"""

import logging
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class VPNClientService:
    """Service for managing VPN clients"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.default_traffic_gb = config.get('client_config', {}).get('default_traffic_gb', 100)
        self.default_expiry_days = config.get('client_config', {}).get('default_expiry_days', 30)
        
    def create_client(self, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new VPN client configuration"""
        try:
            # Generate UUID for client
            client_id = str(uuid.uuid4())
            
            # Calculate expiry time
            expiry_time = None
            if client_data.get('expiryTime'):
                expiry_time = client_data['expiryTime']
            else:
                # Use default expiry
                expiry_date = datetime.now() + timedelta(days=self.default_expiry_days)
                expiry_time = expiry_date.isoformat()
            
            # Convert traffic from GB to bytes
            total_gb = client_data.get('totalGB', self.default_traffic_gb)
            total_bytes = int(total_gb) * (1024 ** 3) if total_gb else 0
            
            client = {
                "id": client_id,
                "email": client_data.get('email', ''),
                "limitIp": 0,
                "totalGB": total_bytes,
                "expiryTime": expiry_time,
                "enable": client_data.get('enable', True),
                "tgId": "",
                "subId": client_id,
                "reset": 0
            }
            
            return client
            
        except Exception as e:
            logger.error(f"Error creating VPN client: {e}")
            return {}
    
    def update_client(self, client_id: str, client_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing VPN client configuration"""
        try:
            # Convert traffic from GB to bytes if provided
            if 'totalGB' in client_data:
                total_gb = client_data['totalGB']
                client_data['totalGB'] = int(total_gb) * (1024 ** 3) if total_gb else 0
            
            # Ensure expiry time is in correct format
            if 'expiryTime' in client_data and client_data['expiryTime']:
                # If it's already a timestamp, keep it; otherwise convert
                expiry_time = client_data['expiryTime']
                if isinstance(expiry_time, str) and 'T' in expiry_time:
                    # Convert from datetime-local format to ISO format
                    try:
                        dt = datetime.fromisoformat(expiry_time.replace('Z', '+00:00'))
                        client_data['expiryTime'] = dt.isoformat()
                    except:
                        pass  # Keep original if conversion fails
            
            return client_data
            
        except Exception as e:
            logger.error(f"Error updating VPN client: {e}")
            return client_data
    
    def format_traffic(self, bytes_value: Optional[int]) -> str:
        """Format traffic bytes to human readable format"""
        if not bytes_value:
            return 'Unlimited'
        
        # Convert bytes to GB
        gb = bytes_value / (1024 ** 3)
        return f"{gb:.2f} GB"
    
    def format_expiry_date(self, timestamp: Optional[str]) -> str:
        """Format expiry timestamp to human readable date"""
        if not timestamp:
            return 'Never'
        
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M')
        except:
            return 'Invalid Date'
    
    def is_client_expired(self, client: Dict[str, Any]) -> bool:
        """Check if a client has expired"""
        expiry_time = client.get('expiryTime')
        if not expiry_time:
            return False
        
        try:
            expiry_dt = datetime.fromisoformat(expiry_time.replace('Z', '+00:00'))
            return datetime.now() > expiry_dt
        except:
            return False
    
    def get_client_status(self, client: Dict[str, Any]) -> str:
        """Get the status of a VPN client"""
        if not client.get('enable', True):
            return 'Disabled'
        
        if self.is_client_expired(client):
            return 'Expired'
        
        return 'Active'
    
    def validate_client_data(self, client_data: Dict[str, Any]) -> Dict[str, str]:
        """Validate client data and return any errors"""
        errors = {}
        
        # Validate email
        email = client_data.get('email', '').strip()
        if not email:
            errors['email'] = 'Email is required'
        elif '@' not in email:
            errors['email'] = 'Invalid email format'
        
        # Validate traffic limit
        total_gb = client_data.get('totalGB')
        if total_gb is not None:
            try:
                gb_value = float(total_gb)
                if gb_value < 0:
                    errors['totalGB'] = 'Traffic limit cannot be negative'
            except (ValueError, TypeError):
                errors['totalGB'] = 'Invalid traffic limit value'
        
        # Validate expiry time
        expiry_time = client_data.get('expiryTime')
        if expiry_time:
            try:
                dt = datetime.fromisoformat(expiry_time.replace('Z', '+00:00'))
                if dt < datetime.now():
                    errors['expiryTime'] = 'Expiry time cannot be in the past'
            except:
                errors['expiryTime'] = 'Invalid expiry time format'
        
        return errors
    
    def generate_client_id(self) -> str:
        """Generate a new UUID for a client"""
        return str(uuid.uuid4())
    
    def calculate_expiry_from_days(self, days: int) -> str:
        """Calculate expiry time from number of days"""
        try:
            expiry_date = datetime.now() + timedelta(days=days)
            return expiry_date.isoformat()
        except Exception as e:
            logger.error(f"Error calculating expiry date: {e}")
            return datetime.now().isoformat()
