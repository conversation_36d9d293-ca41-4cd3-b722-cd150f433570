# VPN Strategy Integration Documentation

## Overview

This document describes the integration of VPN redemption strategies from ShopeeRedeemBot into the SteamCodeTool plugin architecture. The integration provides a flexible, strategy-pattern-based system for handling different types of VPN products.

## Architecture

### Plugin Structure

```
plugins/vpn/
├── plugin.py                          # Main VPN plugin
├── services/
│   ├── vpn_api_service.py             # VPN API wrapper service
│   ├── vpn_redemption_service.py      # Main redemption service
│   ├── vpn_server_service.py          # Existing server management
│   ├── vpn_client_service.py          # Existing client management
│   ├── vpn_inbound_service.py         # Existing inbound management
│   └── vpn_config_service.py          # Existing config management
├── strategies/
│   ├── base_strategy.py               # Abstract base strategy
│   ├── vpn_base_strategy.py           # VPN-specific base strategy
│   ├── vpn_sg_strategy.py             # Singapore VPN strategies
│   ├── vpn_my_basic_strategy.py       # Malaysia basic VPN strategies
│   ├── vpn_my_highspeed_strategy.py   # Malaysia high-speed VPN strategies
│   └── strategy_factory.py            # Strategy factory
└── routes/
    ├── vpn_routes.py                  # Existing VPN routes
    └── vpn_redemption_routes.py       # New redemption routes
```

### Strategy Pattern Implementation

The system uses the Strategy pattern to handle different VPN product types:

1. **Base Strategy**: Abstract interface for all redemption strategies
2. **VPN Base Strategy**: Common VPN functionality and workflow
3. **Specific Strategies**: Implementation for different VPN product types

## Supported VPN Products

### Singapore Products (sg_*)
- **Basic Singapore** (`sg_`): Single Digital Ocean server
- **High-Speed Singapore** (`sg_highspeed`): Enhanced performance
- **Premium Singapore** (`sg_premium`): Premium features
- **Business Singapore** (`sg_business`): Enterprise features

### Malaysia Basic Products (my_*)
- **Basic Malaysia** (`my_`): Single Shinjiru server (randomly selected)
- **Standard Malaysia** (`my_standard`): Enhanced basic features
- **Premium Malaysia** (`my_premium`): Premium features with server prioritization

### Malaysia High-Speed Products (my_highspeed_*)
- **High-Speed Malaysia** (`my_highspeed`): Multi-server with shared UUID
- **High-Speed Premium** (`my_highspeed_premium`): Premium high-speed features

## Key Features

### 1. Strategy Factory
- Automatic strategy selection based on product SKU
- Lazy loading to avoid circular imports
- Fallback strategies for unknown SKUs

### 2. VPN API Service
- Wrapper for VPN backend API
- Server management and client creation
- Email availability checking with progress reporting

### 3. Redemption Service
- Main service orchestrating VPN redemptions
- Strategy-based processing
- Validation and error handling

### 4. Multi-Server Support
- Parallel processing for high-speed products
- Shared UUID across servers
- Progress tracking and reporting

## API Endpoints

### Redemption Endpoints (`/api/vpn/redemption/`)

#### POST `/redeem`
Redeem a VPN product using appropriate strategy.

**Request Body:**
```json
{
  "order_details": {
    "id": "order_id",
    "customer_email": "<EMAIL>",
    "shopee_username": "username"
  },
  "product_details": {
    "var_sku": "my_highspeed_30",
    "name": "Malaysia High Speed VPN 30 Days",
    "vpn_days": 30
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "VPN redemption successful",
  "redemption_type": "vpn",
  "status": "new_redemption",
  "data": {
    "order_id": "order_id",
    "product_sku": "my_highspeed_30",
    "expiry_date": "15-01-2025",
    "config_link": "/api/vpn/config?orderid=order_id&varSku=my_highspeed_30",
    "customer_email": "<EMAIL>",
    "servers_created": 5,
    "strategy": "malaysia_highspeed"
  }
}
```

#### POST `/validate`
Validate order data for VPN redemption.

#### GET `/strategy-info/<product_sku>`
Get strategy information for a product SKU.

#### GET `/supported-products`
Get information about supported VPN products.

#### GET `/servers?product_sku=<sku>`
Get available servers for a product SKU.

#### POST `/check-email`
Check email availability across target servers.

#### GET `/config?orderid=<id>&varSku=<sku>`
Get VPN configuration for an order.

#### GET `/health`
Health check endpoint.

## Configuration

### Plugin Configuration Schema

```json
{
  "vpn_api": {
    "base_url": "http://blueblue.api.limjianhui.com:32771",
    "timeout": 30
  },
  "redemption": {
    "enabled": true,
    "default_expiry_days": 30
  }
}
```

## Usage Examples

### 1. Basic VPN Redemption

```python
from plugins.vpn.services.vpn_redemption_service import VPNRedemptionService

# Initialize service
config = {"vpn_api": {"base_url": "...", "timeout": 30}}
redemption_service = VPNRedemptionService(config)

# Redeem VPN product
order_details = {
    "id": "ORDER123",
    "customer_email": "<EMAIL>",
    "shopee_username": "testuser"
}

product_details = {
    "var_sku": "my_highspeed_30",
    "name": "Malaysia High Speed VPN 30 Days",
    "vpn_days": 30
}

result = redemption_service.redeem_vpn_product(order_details, product_details)
```

### 2. Strategy Information

```python
from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory

# Check if product is VPN
is_vpn = VPNStrategyFactory.is_vpn_product("my_highspeed_30")

# Get strategy information
strategy_info = VPNStrategyFactory.get_strategy_info("my_highspeed_30")
print(strategy_info)
# Output: {
#   "strategy_name": "VPNMalaysiaHighSpeedStrategy",
#   "strategy_type": "malaysia_highspeed",
#   "region": "Malaysia",
#   "server_type": "all_servers",
#   "creation_type": "multi_server",
#   "features": ["shared_uuid", "server_selection", "high_redundancy"],
#   "tier": "basic"
# }
```

### 3. Direct Strategy Usage

```python
from plugins.vpn.strategies.strategy_factory import create_vpn_strategy

# Create strategy for specific SKU
strategy = create_vpn_strategy("sg_premium_60")

if strategy:
    # Use strategy for redemption
    result = strategy.redeem(order_details, product_details)
```

## Error Handling

The system provides comprehensive error handling:

1. **Validation Errors**: Missing or invalid order/product data
2. **API Errors**: VPN service unavailable or timeout
3. **Strategy Errors**: No strategy found for SKU
4. **Server Errors**: No suitable servers available
5. **Email Conflicts**: Email already exists (handled gracefully for VPN products)

## Migration from ShopeeRedeemBot

The integration maintains compatibility with the original ShopeeRedeemBot implementation:

1. **Same Strategy Logic**: Identical server selection and user creation logic
2. **Compatible Response Format**: Same redemption message format
3. **Configuration Links**: Same VPN config link generation
4. **Progress Reporting**: Maintained for multi-server operations

## Testing

The integration includes comprehensive testing:

- Strategy factory pattern recognition
- Plugin structure validation
- Configuration schema verification
- Sample redemption data processing

All tests pass successfully, confirming the integration works correctly.

## Future Enhancements

Potential improvements for the VPN integration:

1. **Caching**: Cache server lists and strategy instances
2. **Metrics**: Add redemption metrics and monitoring
3. **Rate Limiting**: Implement rate limiting for API calls
4. **Retry Logic**: Add retry mechanisms for failed operations
5. **Configuration UI**: Admin interface for VPN settings
6. **Batch Operations**: Support for bulk VPN redemptions
