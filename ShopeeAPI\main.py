"""
Run the FastAPI application.
"""
import uvicorn
import os
import sys
import pathlib

def setup_python_path():
    """Setup Python path for proper imports."""
    current_dir = str(pathlib.Path(__file__).parent.absolute())

    # Add current directory to Python path to make imports work
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # Add parent directory to Python path to make ShopeeAPI package importable
    parent_dir = str(pathlib.Path(__file__).parent.parent.absolute())
    if parent_dir not in sys.path:
        sys.path.append(parent_dir)

    print(f"Python path setup complete:")
    print(f"  Current dir: {current_dir}")
    print(f"  Parent dir: {parent_dir}")
    print(f"  Python path: {sys.path[:3]}...")  # Show first 3 entries

if __name__ == "__main__":
    # Setup Python path first
    setup_python_path()

    # Define port
    port = int(os.environ.get("PORT", 8000))

    # Always run directly from api module in Docker
    print(f"Starting ShopeeAPI on port {port}...")
    uvicorn.run("api:app", host="0.0.0.0", port=port, reload=False)