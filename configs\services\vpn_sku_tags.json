{"description": "VPN SKU to Server Tags Mapping Configuration", "version": "1.0", "last_updated": "2025-06-30", "sku_server_tags_mapping": {"malaysia_basic": {"my_": {"tags": ["malaysia", "shin<PERSON><PERSON>", "basic"], "validity_days": 30}, "my_15": {"tags": ["malaysia", "shin<PERSON><PERSON>", "basic"], "validity_days": 15}, "my_30": {"tags": ["malaysia", "shin<PERSON><PERSON>", "basic"], "validity_days": 30}, "my_60": {"tags": ["malaysia", "shin<PERSON><PERSON>", "basic"], "validity_days": 60}, "my_90": {"tags": ["malaysia", "shin<PERSON><PERSON>", "basic"], "validity_days": 90}}, "malaysia_standard": {"my_standard": {"tags": ["malaysia", "shin<PERSON><PERSON>", "standard"], "validity_days": 30}, "my_standard_15": {"tags": ["malaysia", "shin<PERSON><PERSON>", "standard"], "validity_days": 15}, "my_standard_30": {"tags": ["malaysia", "shin<PERSON><PERSON>", "standard"], "validity_days": 30}, "my_standard_60": {"tags": ["malaysia", "shin<PERSON><PERSON>", "standard"], "validity_days": 60}}, "malaysia_premium": {"my_premium": {"tags": ["malaysia", "shin<PERSON><PERSON>", "premium"], "validity_days": 30}, "my_premium_15": {"tags": ["malaysia", "shin<PERSON><PERSON>", "premium"], "validity_days": 15}, "my_premium_30": {"tags": ["malaysia", "shin<PERSON><PERSON>", "premium"], "validity_days": 30}, "my_premium_60": {"tags": ["malaysia", "shin<PERSON><PERSON>", "premium"], "validity_days": 60}, "my_premium_90": {"tags": ["malaysia", "shin<PERSON><PERSON>", "premium"], "validity_days": 90}}, "malaysia_highspeed": {"my_highspeed": {"tags": ["malaysia", "singapore", "highspeed", "premium"], "validity_days": 30}, "my_highspeed_15": {"tags": ["malaysia", "singapore", "highspeed", "premium"], "validity_days": 15}, "my_highspeed_30": {"tags": ["malaysia", "singapore", "highspeed", "premium"], "validity_days": 30}, "my_highspeed_60": {"tags": ["malaysia", "singapore", "highspeed", "premium"], "validity_days": 60}, "my_highspeed_90": {"tags": ["malaysia", "singapore", "highspeed", "premium"], "validity_days": 90}, "my_highspeed_premium": {"tags": ["malaysia", "singapore", "highspeed", "premium", "business"], "validity_days": 30}, "my_highspeed_premium_30": {"tags": ["malaysia", "singapore", "highspeed", "premium", "business"], "validity_days": 30}, "my_highspeed_premium_60": {"tags": ["malaysia", "singapore", "highspeed", "premium", "business"], "validity_days": 60}, "my_highspeed_premium_90": {"tags": ["malaysia", "singapore", "highspeed", "premium", "business"], "validity_days": 90}}, "singapore_basic": {"sg_": ["singapore", "digitalocean", "basic"], "sg_15": ["singapore", "digitalocean", "basic"], "sg_30": ["singapore", "digitalocean", "basic"], "sg_60": ["singapore", "digitalocean", "basic"]}, "singapore_highspeed": {"sg_highspeed": ["singapore", "digitalocean", "highspeed"], "sg_highspeed_15": ["singapore", "digitalocean", "highspeed"], "sg_highspeed_30": ["singapore", "digitalocean", "highspeed"], "sg_highspeed_60": ["singapore", "digitalocean", "highspeed"]}, "singapore_premium": {"sg_premium": ["singapore", "digitalocean", "premium"], "sg_premium_15": ["singapore", "digitalocean", "premium"], "sg_premium_30": ["singapore", "digitalocean", "premium"], "sg_premium_60": ["singapore", "digitalocean", "premium"], "sg_premium_90": ["singapore", "digitalocean", "premium"]}, "singapore_business": {"sg_business": ["singapore", "digitalocean", "business", "premium"], "sg_business_30": ["singapore", "digitalocean", "business", "premium"], "sg_business_60": ["singapore", "digitalocean", "business", "premium"], "sg_business_90": ["singapore", "digitalocean", "business", "premium"]}, "thailand_basic": {"th_30": ["thailand", "basic", "new_provider"]}}, "fallback_mapping": {"description": "Fallback tags for SKU patterns that don't have exact matches", "sg_": ["singapore", "digitalocean"], "my_": ["malaysia", "shin<PERSON><PERSON>"], "default": ["malaysia", "shin<PERSON><PERSON>"]}, "tag_definitions": {"location_tags": {"malaysia": "Servers located in Malaysia", "singapore": "Servers located in Singapore", "thailand": "Servers located in Thailand", "indonesia": "Servers located in Indonesia"}, "provider_tags": {"shinjiru": "Shinjiru hosting provider", "digitalocean": "Digital Ocean hosting provider", "gbnetwork": "GBNetwork hosting provider", "vultr": "Vultr hosting provider"}, "performance_tags": {"basic": "Basic performance tier", "standard": "Standard performance tier", "highspeed": "High-speed performance tier", "premium": "Premium performance tier", "business": "Business-grade performance tier"}, "feature_tags": {"unlimited": "Unlimited bandwidth", "dedicated": "Dedicated IP", "shared": "Shared IP", "multiregion": "Multi-region access", "backup": "Backup server"}}, "configuration_notes": {"sku_matching": "SKU matching is done using 'contains' logic - the system looks for the longest matching pattern", "tag_matching": "Server tag matching uses OR logic - servers need to have ANY of the required tags", "case_sensitivity": "All tag matching is case-insensitive", "fallback_behavior": "If no servers match the required tags, the system falls back to all active servers", "modification_guide": "To modify SKU mappings, edit the 'sku_server_tags_mapping' section and restart the application"}}