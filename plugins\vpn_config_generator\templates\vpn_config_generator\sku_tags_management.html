{% extends "base.html" %}

{% block title %}VPN SKU Tags Management{% endblock %}

{% block header %}
<i class="fas fa-tags mr-2"></i>VPN SKU Tags Management
{% endblock %}

{% block content %}
<style>
    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 600;
    }
    .status-success { background-color: #d1e7dd; color: #0f5132; }
    .status-warning { background-color: #fff3cd; color: #664d03; }
    .status-error { background-color: #f8d7da; color: #842029; }
    .tag-badge {
        background-color: #e5e7eb;
        color: #374151;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        margin: 0.125rem;
        display: inline-block;
    }
    .category-section {
        border-left: 4px solid #9333ea;
        padding-left: 1rem;
        margin-bottom: 1.5rem;
    }
    .loading-spinner {
        display: none;
    }
    .test-result {
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
    }
</style>
<!-- Header Section -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex justify-between items-center">
        <div>
            <p class="text-gray-600 mb-4">Manage SKU to server tags mapping configuration</p>
        </div>
        <div class="flex space-x-3">
            <button class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="loadData()">
                <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
            <button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="showAddMappingModal()">
                <i class="fas fa-plus mr-2"></i>Add Mapping
            </button>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center justify-center" onclick="testConfiguration()">
            <i class="fas fa-vial mr-2"></i>Test Configuration
        </button>
        <button class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center justify-center" onclick="validateConfiguration()">
            <i class="fas fa-check-circle mr-2"></i>Validate Config
        </button>
        <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center justify-center" onclick="reloadConfiguration()">
            <i class="fas fa-redo mr-2"></i>Reload Config
        </button>
        <button class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center justify-center" onclick="exportConfiguration()">
            <i class="fas fa-download mr-2"></i>Export Config
        </button>
    </div>
</div>

<!-- Configuration Info -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-info-circle mr-2 text-purple-500"></i>Configuration Info
            </h3>
        </div>
        <div class="space-y-3">
            <div class="flex justify-between">
                <span class="font-medium text-gray-700">Version:</span>
                <span id="configVersion" class="text-gray-600">Loading...</span>
            </div>
            <div class="flex justify-between">
                <span class="font-medium text-gray-700">Last Updated:</span>
                <span id="configLastUpdated" class="text-gray-600">Loading...</span>
            </div>
            <div class="flex justify-between">
                <span class="font-medium text-gray-700">Total Categories:</span>
                <span id="totalCategories" class="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full">0</span>
            </div>
            <div class="flex justify-between">
                <span class="font-medium text-gray-700">Total SKUs:</span>
                <span id="totalSkus" class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">0</span>
            </div>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-cogs mr-2 text-blue-500"></i>Quick Actions
            </h3>
        </div>
        <div class="space-y-3">
            <div>
                <label for="testSkuInput" class="block text-sm font-medium text-gray-700 mb-2">Test SKU Resolution:</label>
                <div class="flex space-x-2">
                    <input type="text" id="testSkuInput" 
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                           placeholder="Enter SKU (e.g., my_premium_30)">
                    <button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="testSingleSku()">
                        <i class="fas fa-search mr-2"></i>Test
                    </button>
                </div>
            </div>
            <div id="singleTestResult"></div>
        </div>
    </div>
</div>

<!-- SKU Mappings -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-list mr-2 text-green-500"></i>SKU Mappings by Category
        </h3>
    </div>
    <div id="skuMappingsContainer">
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 loading-spinner"></div>
            <p class="text-gray-600 mt-4">Loading SKU mappings...</p>
        </div>
    </div>
</div>

<!-- Results Modal -->
<div id="resultsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 id="resultsModalTitle" class="text-lg font-medium text-gray-900">Results</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeResultsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="resultsContent" class="mb-4"></div>
            <div class="flex justify-end">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200" onclick="closeResultsModal()">Close</button>
            </div>
        </div>
    </div>
</div>
    </div>

<!-- Add Mapping Modal -->
<div id="addMappingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add SKU Mapping</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeAddMappingModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addMappingForm" class="space-y-4">
                <div>
                    <label for="categoryInput" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <input type="text" id="categoryInput" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                           placeholder="e.g., malaysia_premium">
                </div>
                <div>
                    <label for="skuInput" class="block text-sm font-medium text-gray-700 mb-2">SKU</label>
                    <input type="text" id="skuInput" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                           placeholder="e.g., my_premium_30">
                </div>
                <div>
                    <label for="tagsInput" class="block text-sm font-medium text-gray-700 mb-2">Tags (comma-separated)</label>
                    <input type="text" id="tagsInput" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                           placeholder="e.g., malaysia,shinjiru,premium">
                </div>
                <div>
                    <label for="validityInput" class="block text-sm font-medium text-gray-700 mb-2">Validity Days (optional)</label>
                    <input type="number" id="validityInput" min="1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                           placeholder="e.g., 30">
                </div>
            </form>
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200" onclick="closeAddMappingModal()">Cancel</button>
                <button type="button" class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition duration-200" onclick="saveMappingForm()">Save Mapping</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
    let currentData = null;

    // Modal functions
    function showAddMappingModal() {
        document.getElementById('addMappingModal').classList.remove('hidden');
    }

    function closeAddMappingModal() {
        document.getElementById('addMappingModal').classList.add('hidden');
        document.getElementById('addMappingForm').reset();
    }

    function showResultsModal(title, content) {
        document.getElementById('resultsModalTitle').textContent = title;
        document.getElementById('resultsContent').innerHTML = content;
        document.getElementById('resultsModal').classList.remove('hidden');
    }

    function closeResultsModal() {
        document.getElementById('resultsModal').classList.add('hidden');
    }

    function showResults(title, content) {
        showResultsModal(title, content);
    }

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
        });

        function loadData() {
            showLoading(true);
            fetch('/vpn-config-generator/api/sku-tags')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentData = data.data;
                        updateConfigInfo(data.data);
                        renderMappings(data.data.mappings);
                    } else {
                        showError('Failed to load data: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('Error loading data: ' + error.message);
                })
                .finally(() => {
                    showLoading(false);
                });
        }

        function updateConfigInfo(data) {
            document.getElementById('configVersion').textContent = data.version || 'Unknown';
            document.getElementById('configLastUpdated').textContent = data.last_updated || 'Unknown';
            
            const totalCategories = Object.keys(data.mappings || {}).length;
            let totalSkus = 0;
            
            Object.values(data.mappings || {}).forEach(category => {
                totalSkus += Object.keys(category).length;
            });
            
            document.getElementById('totalCategories').textContent = totalCategories;
            document.getElementById('totalSkus').textContent = totalSkus;
        }

        function renderMappings(mappings) {
            const container = document.getElementById('skuMappingsContainer');
            
                if (!mappings || Object.keys(mappings).length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-inbox fa-3x text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">No SKU mappings found</p>
                            <button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="showAddMappingModal()">
                                Add First Mapping
                            </button>
                        </div>
                    `;
                    return;
                }

                let html = '';
                Object.entries(mappings).forEach(([category, skus]) => {
                    html += `
                        <div class="category-section">
                            <h6 class="text-purple-600 font-semibold mb-3 flex items-center">
                                <i class="fas fa-folder mr-2"></i>
                                ${category.replace(/_/g, ' ').toUpperCase()}
                                <span class="bg-gray-200 text-gray-800 text-xs font-medium px-2 py-1 rounded-full ml-2">${Object.keys(skus).length} SKUs</span>
                            </h6>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    `;
                    
                    Object.entries(skus).forEach(([sku, config]) => {
                        const tags = Array.isArray(config) ? config : (config.tags || []);
                        const validity = config.validity_days ? ` (${config.validity_days} days)` : '';
                        
                        html += `
                            <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                <div class="flex justify-between items-start mb-2">
                                    <h6 class="font-medium text-gray-900 mb-0">${sku}</h6>
                                    <div class="relative">
                                        <button class="text-gray-400 hover:text-gray-600 text-sm" onclick="toggleDropdown('${sku}')">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div id="dropdown-${sku}" class="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg hidden z-10">
                                            <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="editMapping('${category}', '${sku}')">
                                                <i class="fas fa-edit mr-2"></i>Edit
                                            </a>
                                            <a href="#" class="block px-3 py-2 text-sm text-red-600 hover:bg-gray-100" onclick="deleteMapping('${sku}')">
                                                <i class="fas fa-trash mr-2"></i>Delete
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    ${tags.map(tag => `<span class="tag-badge">${tag}</span>`).join('')}
                                </div>
                                ${validity ? `<small class="text-gray-500">${validity}</small>` : ''}
                            </div>
                        `;
                    });
                    
                    html += `
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html;
        }

        function showLoading(show) {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = show ? 'inline-block' : 'none';
            }
        }

        function toggleDropdown(sku) {
            const dropdown = document.getElementById(`dropdown-${sku}`);
            // Close all other dropdowns
            document.querySelectorAll('[id^="dropdown-"]').forEach(d => {
                if (d !== dropdown) d.classList.add('hidden');
            });
            // Toggle current dropdown
            dropdown.classList.toggle('hidden');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('[id^="dropdown-"]') && !event.target.closest('button')) {
                document.querySelectorAll('[id^="dropdown-"]').forEach(d => d.classList.add('hidden'));
            }
        });

        function showError(message) {
            const container = document.getElementById('skuMappingsContainer');
            container.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                        <span class="text-red-700">${message}</span>
                    </div>
                </div>
            `;
        }

        function showSuccess(message) {
            // Create and show a toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                    <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(toast);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        function saveMappingForm() {
            const category = document.getElementById('categoryInput').value.trim();
            const sku = document.getElementById('skuInput').value.trim();
            const tagsInput = document.getElementById('tagsInput').value.trim();
            const validity = document.getElementById('validityInput').value;

            if (!category || !sku || !tagsInput) {
                alert('Please fill in all required fields');
                return;
            }

            const tags = tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag);
            if (tags.length === 0) {
                alert('Please provide at least one tag');
                return;
            }

            const data = {
                category: category,
                sku: sku,
                tags: tags
            };

            if (validity) {
                data.validity_days = parseInt(validity);
            }

            fetch('/vpn-config-generator/api/sku-tags', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    closeAddMappingModal();
                    loadData();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error saving mapping: ' + error.message);
            });
        }

        function deleteMapping(sku) {
            if (!confirm(`Are you sure you want to delete the mapping for SKU "${sku}"?`)) {
                return;
            }

            fetch(`/vpn-config-generator/api/sku-tags/${encodeURIComponent(sku)}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadData();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error deleting mapping: ' + error.message);
            });
        }

        function testConfiguration() {
            fetch('/vpn-config-generator/api/sku-tags/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                showResults('Test Results', formatTestResults(data));
            })
            .catch(error => {
                showResults('Test Error', `<div class="alert alert-danger">Error: ${error.message}</div>`);
            });
        }

        function validateConfiguration() {
            fetch('/vpn-config-generator/api/sku-tags/validate', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showResults('Validation Results', formatValidationResults(data));
            })
            .catch(error => {
                showResults('Validation Error', `<div class="alert alert-danger">Error: ${error.message}</div>`);
            });
        }

        function reloadConfiguration() {
            fetch('/vpn-config-generator/api/sku-tags/reload', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error reloading configuration: ' + error.message);
            });
        }

        function testSingleSku() {
            const sku = document.getElementById('testSkuInput').value.trim();
            if (!sku) {
                alert('Please enter a SKU to test');
                return;
            }

            fetch('/vpn-config-generator/api/sku-tags/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ test_skus: [sku] })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('singleTestResult');
                if (data.success && data.results && data.results[sku]) {
                    const result = data.results[sku];
                    const statusClass = result.success ? 'status-success' : 'status-error';
                    const statusText = result.success ? 'Found' : 'Not Found';

                    resultDiv.innerHTML = `
                        <div class="mt-2">
                            <span class="status-badge ${statusClass}">${statusText}</span>
                            ${result.tags.length > 0 ?
                                `<div class="mt-1">${result.tags.map(tag => `<span class="tag-badge">${tag}</span>`).join('')}</div>` :
                                '<div class="text-muted mt-1">No tags found</div>'
                            }
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-warning mt-2">Error testing SKU: ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('singleTestResult').innerHTML =
                    `<div class="alert alert-danger mt-2">Error: ${error.message}</div>`;
            });
        }

        function exportConfiguration() {
            if (currentData) {
                const dataStr = JSON.stringify(currentData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = 'vpn_sku_tags_config.json';
                link.click();
                URL.revokeObjectURL(url);
            }
        }

        function showResults(title, content) {
            document.getElementById('resultsModalTitle').textContent = title;
            document.getElementById('resultsContent').innerHTML = content;
            document.getElementById('resultsModal').classList.remove('hidden');
        }

        function formatTestResults(data) {
            if (!data.success) {
                return `<div class="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">Test failed: ${data.error}</div>`;
            }

            let html = `
                <div class="bg-blue-50 border border-blue-200 text-blue-700 p-4 rounded-lg mb-4">
                    <i class="fas fa-info-circle mr-2"></i>
                    Tested ${data.total_tested} SKUs
                </div>
                <div class="test-result">
            `;

            Object.entries(data.results).forEach(([sku, result]) => {
                const statusClass = result.success ? 'status-success' : 'status-error';
                const statusText = result.success ? 'PASS' : 'FAIL';

                html += `
                    <div class="mb-3 p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between mb-1">
                            <strong class="text-gray-900">${sku}</strong>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </div>
                        <small class="text-gray-600">Tags: ${result.tags.join(', ') || 'None'}</small>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        function formatValidationResults(data) {
            let html = '';

            if (data.success) {
                html += `<div class="bg-green-50 border border-green-200 text-green-700 p-4 rounded-lg mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    Configuration is valid!
                </div>`;
            } else {
                html += `<div class="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Configuration has errors!
                </div>`;
            }

            html += `
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <span class="font-medium text-gray-700">Total Categories:</span>
                        <span class="ml-2 text-gray-900">${data.total_categories}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Total SKUs:</span>
                        <span class="ml-2 text-gray-900">${data.total_skus}</span>
                    </div>
                </div>
            `;

            if (data.errors && data.errors.length > 0) {
                html += '<h6 class="text-red-600 font-semibold mb-2">Errors:</h6><ul class="text-red-600 mb-4 list-disc pl-5">';
                data.errors.forEach(error => {
                    html += `<li>${error}</li>`;
                });
                html += '</ul>';
            }

            if (data.warnings && data.warnings.length > 0) {
                html += '<h6 class="text-yellow-600 font-semibold mb-2">Warnings:</h6><ul class="text-yellow-600 list-disc pl-5">';
                data.warnings.forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += '</ul>';
            }

            return html;
        }
</script>
{% endblock %}
