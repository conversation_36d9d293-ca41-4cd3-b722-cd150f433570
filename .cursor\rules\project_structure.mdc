---
alwaysApply: true
description: "Project Structure Guide"
---
# Project Structure Overview
The main entry point is [main.py](mdc:main.py), which initializes the core application, loads configuration, and bootstraps plugins.

## Key Directories
- **[api/](mdc:api)**: FastAPI route modules for REST endpoints.
- **[plugins/](mdc:plugins)**: Pluggable feature modules. Each plugin contains its own plugin.py, 
outes/, services/, and optional 	emplates/.
- **[services/](mdc:services)**: Shared business-logic utilities used across the application.
- **[configs/](mdc:configs)**: JSON/YAML configuration files and templates.
- **[core/](mdc:core)**: Core framework components such as the plugin manager.
- **[templates/](mdc:templates)** and **[static/](mdc:static)**: Jinja2 templates and static assets for the admin UI.

Use these references to quickly navigate the codebase.
