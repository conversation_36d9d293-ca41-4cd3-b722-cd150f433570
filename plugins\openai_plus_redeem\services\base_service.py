"""
Base Service Class for OpenAI Plus Redeem Plugin

Abstract base class following the service layer pattern from 
PLUGIN_ARCHITECTURE_STANDARDS.md with proper initialization,
shutdown, health check, and logging capabilities.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime


class BaseService(ABC):
    """
    Base class for all OpenAI Plus Redeem plugin services
    
    Provides common functionality including:
    - Configuration management
    - Logging setup
    - Lifecycle management (initialize/shutdown)
    - Health checking
    - Error handling patterns
    """
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize base service
        
        Args:
            config: Service configuration dictionary
            logger: Optional logger instance (creates one if not provided)
        """
        self.config = config or {}
        self.logger = logger or logging.getLogger(f"plugins.openai_plus_redeem.{self.__class__.__name__}")
        self._initialized = False
        self._shutdown = False
        self._initialization_time = None
        self._last_health_check = None
        
        # Service-specific configuration
        self.service_name = self.__class__.__name__
        self.service_version = "1.0.0"
        
        self.logger.debug(f"Created {self.service_name} service instance")
    
    @abstractmethod
    def initialize(self) -> bool:
        """
        Initialize the service
        
        This method should be implemented by each service to perform
        service-specific initialization tasks.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        pass
    
    @abstractmethod
    def shutdown(self) -> bool:
        """
        Shutdown the service
        
        This method should be implemented by each service to perform
        cleanup tasks and graceful shutdown.
        
        Returns:
            True if shutdown was successful, False otherwise
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        Check service health
        
        This method should be implemented by each service to return
        health status information.
        
        Returns:
            Dictionary containing health status information
        """
        pass
    
    def is_initialized(self) -> bool:
        """
        Check if service is initialized and ready
        
        Returns:
            True if service is initialized and not shutdown
        """
        return self._initialized and not self._shutdown
    
    def is_healthy(self) -> bool:
        """
        Check if service is healthy
        
        Returns:
            True if service is healthy, False otherwise
        """
        try:
            health_status = self.health_check()
            return health_status.get('status') == 'healthy'
        except Exception as e:
            self.logger.error(f"Health check failed for {self.service_name}: {e}")
            return False
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        Update service configuration
        
        Args:
            config: New configuration dictionary to merge
        """
        if not isinstance(config, dict):
            self.logger.warning(f"Invalid config type for {self.service_name}: {type(config)}")
            return
        
        old_config = self.config.copy()
        self.config.update(config)
        
        self.logger.info(f"Updated configuration for {self.service_name}")
        self.logger.debug(f"Config changes: {set(config.keys())}")
        
        # Call service-specific config update handler if it exists
        if hasattr(self, '_on_config_update'):
            try:
                self._on_config_update(old_config, self.config)
            except Exception as e:
                self.logger.error(f"Error in config update handler for {self.service_name}: {e}")
    
    def get_service_info(self) -> Dict[str, Any]:
        """
        Get service information
        
        Returns:
            Dictionary containing service metadata
        """
        return {
            'name': self.service_name,
            'version': self.service_version,
            'initialized': self._initialized,
            'shutdown': self._shutdown,
            'initialization_time': self._initialization_time,
            'last_health_check': self._last_health_check,
            'healthy': self.is_healthy() if self._initialized else None
        }
    
    def _mark_initialized(self) -> None:
        """Mark service as initialized (internal use)"""
        self._initialized = True
        self._initialization_time = datetime.now().isoformat()
        self.logger.info(f"{self.service_name} service initialized successfully")
    
    def _mark_shutdown(self) -> None:
        """Mark service as shutdown (internal use)"""
        self._shutdown = True
        self._initialized = False
        self.logger.info(f"{self.service_name} service shutdown completed")
    
    def _update_health_check_time(self) -> None:
        """Update last health check timestamp (internal use)"""
        self._last_health_check = datetime.now().isoformat()
    
    def _get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value with fallback
        
        Args:
            key: Configuration key (supports dot notation for nested keys)
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        try:
            # Support dot notation for nested keys
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            self.logger.warning(f"Error getting config value '{key}' for {self.service_name}: {e}")
            return default
    
    def _validate_config(self, required_keys: list) -> bool:
        """
        Validate that required configuration keys are present
        
        Args:
            required_keys: List of required configuration keys
            
        Returns:
            True if all required keys are present, False otherwise
        """
        missing_keys = []
        
        for key in required_keys:
            if self._get_config_value(key) is None:
                missing_keys.append(key)
        
        if missing_keys:
            self.logger.error(f"Missing required configuration keys for {self.service_name}: {missing_keys}")
            return False
        
        return True
    
    def _handle_service_error(self, operation: str, error: Exception, 
                            reraise: bool = False) -> Optional[Any]:
        """
        Handle service errors with consistent logging
        
        Args:
            operation: Name of the operation that failed
            error: The exception that occurred
            reraise: Whether to re-raise the exception
            
        Returns:
            None, or re-raises the exception if reraise=True
        """
        error_msg = f"Error in {self.service_name}.{operation}: {error}"
        self.logger.error(error_msg, exc_info=True)
        
        if reraise:
            raise error
        
        return None
    
    def _log_operation(self, operation: str, details: Dict[str, Any] = None) -> None:
        """
        Log service operations with structured data
        
        Args:
            operation: Name of the operation
            details: Additional operation details
        """
        log_data = {
            'service': self.service_name,
            'operation': operation,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            log_data.update(details)
        
        self.logger.info(f"{self.service_name} operation: {operation}", extra=log_data)
    
    def __str__(self) -> str:
        """String representation of the service"""
        status = "initialized" if self._initialized else "not initialized"
        if self._shutdown:
            status = "shutdown"
        
        return f"{self.service_name} ({status})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the service"""
        return (f"{self.__class__.__name__}("
                f"initialized={self._initialized}, "
                f"shutdown={self._shutdown}, "
                f"config_keys={list(self.config.keys())}"
                f")")
