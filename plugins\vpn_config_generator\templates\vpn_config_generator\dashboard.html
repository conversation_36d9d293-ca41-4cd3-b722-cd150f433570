{% extends "base.html" %}

{% block title %}VPN Config Generator - Admin Panel{% endblock %}

{% block header %}
<i class="fas fa-cogs mr-2"></i>VPN Config Generator
{% endblock %}

{% block content %}
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <p class="text-gray-600 mb-4">Generate VPN configurations using the VPN plugin API</p>
</div>

<!-- Status Card -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-info-circle mr-2 text-blue-500"></i>Status
        </h3>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-3">
            <div>
                <span class="font-medium text-gray-700">API Mode:</span>
                {% if api_config.use_vpn_plugin_api %}
                    <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">VPN Plugin API</span>
                {% else %}
                    <span class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">Fallback API</span>
                {% endif %}
            </div>
            <div>
                <span class="font-medium text-gray-700">Status:</span>
                {% if api_config.enabled %}
                    <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">Enabled</span>
                {% else %}
                    <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">Disabled</span>
                {% endif %}
            </div>
        </div>
        <div class="flex items-center">
            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="testConnection()">
                <i class="fas fa-plug mr-2"></i>Test Connection
            </button>
        </div>
    </div>
</div>

<!-- Chat Commands Integration -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-comments mr-2 text-purple-500"></i>Chat Commands Integration
        </h3>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-3">
            <div>
                <span class="font-medium text-gray-700">Registration Status:</span>
                <span id="chatCommandsStatus" class="ml-2 px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Checking...</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Available Commands:</span>
                <span id="availableCommandDisplay" class="ml-2 text-sm text-gray-600">#config</span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <button id="registerBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="registerChatCommands()">
                <i class="fas fa-plus mr-2"></i>Register Commands
            </button>
            <button id="unregisterBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="unregisterChatCommands()">
                <i class="fas fa-minus mr-2"></i>Unregister Commands
            </button>
        </div>
    </div>
    <div class="mt-4 p-4 bg-blue-50 rounded-lg">
        <p class="text-sm text-blue-700">
            <i class="fas fa-info-circle mr-2"></i>
            When registered, VPN config generation commands will be handled by the Chat Commands plugin,
            allowing centralized command management and webhook processing.
        </p>
    </div>
</div>

<!-- Command Configuration -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-terminal mr-2 text-orange-500"></i>Command Configuration
        </h3>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="space-y-3">
            <div>
                <span class="font-medium text-gray-700">Current Command Prefix:</span>
                <span id="currentPrefix" class="ml-2 px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Loading...</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Current Command Name:</span>
                <span id="currentCommandName" class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">Loading...</span>
            </div>
        </div>
        <div class="flex items-center">
            <button class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition duration-200" onclick="showCommandConfigModal()">
                <i class="fas fa-edit mr-2"></i>Configure Command
            </button>
        </div>
    </div>
    <div class="mt-4 p-4 bg-orange-50 rounded-lg">
        <p class="text-sm text-orange-700">
            <i class="fas fa-info-circle mr-2"></i>
            The command prefix is controlled by the Chat Commands plugin. You can customize the command name and response text here.
        </p>
    </div>
</div>

<!-- Configuration Forms -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- API Configuration -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-cog mr-2 text-blue-500"></i>API Configuration
            </h3>
        </div>
        <form id="apiConfigForm" class="space-y-4">
            <div class="flex items-center">
                <input type="checkbox" id="enabled" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if api_config.enabled %}checked{% endif %}>
                <label for="enabled" class="ml-2 block text-sm text-gray-700">
                    Enable VPN Config Generation
                </label>
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="useVpnPluginApi" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if api_config.use_vpn_plugin_api %}checked{% endif %}>
                <label for="useVpnPluginApi" class="ml-2 block text-sm text-gray-700">
                    Use VPN Plugin API (Recommended)
                </label>
            </div>

            <div>
                <label for="fallbackEndpoint" class="block text-sm font-medium text-gray-700 mb-1">Fallback API Endpoint</label>
                <input type="url" id="fallbackEndpoint"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="{{ api_config.fallback_api_endpoint }}"
                       placeholder="https://api.example.com/generate">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="fallbackUsername" class="block text-sm font-medium text-gray-700 mb-1">Fallback Username</label>
                    <input type="text" id="fallbackUsername"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ api_config.fallback_username }}">
                </div>
                <div>
                    <label for="fallbackPassword" class="block text-sm font-medium text-gray-700 mb-1">Fallback Password</label>
                    <input type="password" id="fallbackPassword"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           value="{{ api_config.fallback_password }}">
                </div>
            </div>

            <div>
                <label for="timeout" class="block text-sm font-medium text-gray-700 mb-1">Timeout (seconds)</label>
                <input type="number" id="timeout"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="{{ api_config.timeout }}" min="5" max="120">
            </div>

            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-save mr-2"></i>Save API Config
            </button>
        </form>
    </div>

    <!-- Generator Settings -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-sliders-h mr-2 text-green-500"></i>Generator Settings
            </h3>
        </div>
        <form id="generatorSettingsForm" class="space-y-4">
            <div>
                <label for="defaultValidityDays" class="block text-sm font-medium text-gray-700 mb-1">Default Validity (Days)</label>
                <input type="number" id="defaultValidityDays"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="{{ generator_settings.default_validity_days }}" min="1" max="365">
            </div>

            <div>
                <label for="usernamePrefix" class="block text-sm font-medium text-gray-700 mb-1">Username Prefix</label>
                <input type="text" id="usernamePrefix"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="{{ generator_settings.username_prefix }}">
            </div>

            <div class="flex items-center">
                <input type="checkbox" id="addRandomSuffix" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if generator_settings.add_random_suffix %}checked{% endif %}>
                <label for="addRandomSuffix" class="ml-2 block text-sm text-gray-700">
                    Add Random Suffix to Username
                </label>
            </div>

            <div>
                <label for="configFormat" class="block text-sm font-medium text-gray-700 mb-1">Config Format</label>
                <select id="configFormat" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="vless" {% if generator_settings.config_format == 'vless' %}selected{% endif %}>VLESS</option>
                    <option value="vmess" {% if generator_settings.config_format == 'vmess' %}selected{% endif %}>VMess</option>
                    <option value="trojan" {% if generator_settings.config_format == 'trojan' %}selected{% endif %}>Trojan</option>
                </select>
            </div>

            <button type="submit" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-save mr-2"></i>Save Settings
            </button>
        </form>
    </div>
</div>

<!-- Test Generation -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-flask mr-2 text-purple-500"></i>Test Config Generation
        </h3>
    </div>
    <form id="testGenerationForm" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label for="testServer" class="block text-sm font-medium text-gray-700 mb-1">Server</label>
                <input type="text" id="testServer"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="SG-01 or 11" required>
            </div>
            <div>
                <label for="testDays" class="block text-sm font-medium text-gray-700 mb-1">Days</label>
                <input type="number" id="testDays"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       value="30" min="1" max="365" required>
            </div>
            <div>
                <label for="testTelco" class="block text-sm font-medium text-gray-700 mb-1">Telco</label>
                <input type="text" id="testTelco"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="digi" required>
            </div>
            <div>
                <label for="testPlan" class="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                <input type="text" id="testPlan"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="unlimited" required>
            </div>
            <div>
                <label for="testUsername" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <input type="text" id="testUsername"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="testuser" required>
            </div>
        </div>
        <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition duration-200">
            <i class="fas fa-play mr-2"></i>Generate Test Config
        </button>
    </form>

    <div id="testResult" class="mt-4 hidden">
        <div id="testResultAlert" class="p-4 rounded-lg">
            <pre id="testResultContent" class="whitespace-pre-wrap text-sm"></pre>
        </div>
    </div>
</div>

<!-- Management Links -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Telco Configuration Management -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-network-wired mr-2 text-indigo-500"></i>Telco Configuration
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Manage telco providers and their VPN configuration plans</p>
        <a href="/vpn-config-generator/telco-management" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200 inline-block">
            <i class="fas fa-cog mr-2"></i>Manage Telcos & Plans
        </a>
    </div>

    <!-- Command Management -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-terminal mr-2 text-purple-500"></i>Command Management
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Configure chat commands that users can use to generate VPN configs</p>
        <a href="/vpn-config-generator/command-management" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200 inline-block">
            <i class="fas fa-edit mr-2"></i>Manage Commands
        </a>
    </div>
</div>

<!-- Telco Summary -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6">
    <div class="flex items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-chart-bar mr-2 text-indigo-500"></i>Configuration Summary
        </h3>
    </div>

    <div class="mb-4 p-4 bg-blue-50 rounded-lg">
        <p class="text-sm text-blue-700">
            <i class="fas fa-info-circle mr-2"></i>
            <strong>Usage:</strong> <code>#v &lt;server&gt; &lt;days&gt; &lt;telco&gt; &lt;plan&gt;</code>
            <br>
            <strong>Example:</strong> <code>#v SG-01 30 digi unlimited</code>
        </p>
    </div>

    <div id="telcoSummary" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Telco summary will be loaded here -->
    </div>
</div>



<!-- Command Configuration Modal -->
<div id="commandConfigModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Configure VPN Command</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeCommandConfigModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="commandConfigForm" class="space-y-4">
                <div>
                    <label for="commandName" class="block text-sm font-medium text-gray-700 mb-1">Command Name *</label>
                    <input type="text" id="commandName" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="config">
                    <p class="mt-1 text-sm text-gray-500">
                        The command name that users will type (without the prefix)
                    </p>
                </div>

                <div>
                    <label for="commandDescription" class="block text-sm font-medium text-gray-700 mb-1">Command Description</label>
                    <input type="text" id="commandDescription"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Generate a VPN configuration">
                </div>

                <div>
                    <label for="responseText" class="block text-sm font-medium text-gray-700 mb-1">Response Text</label>
                    <textarea id="responseText" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️"></textarea>
                    <p class="mt-1 text-sm text-gray-500">
                        The text that will be displayed when the command is executed
                    </p>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="commandEnabled" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="commandEnabled" class="ml-2 block text-sm text-gray-700">
                        Enable Command
                    </label>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200" onclick="closeCommandConfigModal()">
                        Cancel
                    </button>
                    <button type="button" class="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition duration-200" onclick="saveCommandConfig()">
                        Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
        let commandPrefix = '#';
        let commandName = 'config';

        function updateAvailableCommandDisplay() {
            const display = document.getElementById('availableCommandDisplay');
            if (display) {
                display.textContent = `${commandPrefix}${commandName}`;
            }
        }
        // API Configuration Form
        document.getElementById('apiConfigForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                enabled: document.getElementById('enabled').checked,
                use_vpn_plugin_api: document.getElementById('useVpnPluginApi').checked,
                fallback_api_endpoint: document.getElementById('fallbackEndpoint').value.trim(),
                fallback_username: document.getElementById('fallbackUsername').value,
                fallback_password: document.getElementById('fallbackPassword').value,
                timeout: parseInt(document.getElementById('timeout').value)
            };

            try {
                const response = await fetch('/vpn-config-generator/api/config/api', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                if (result.success) {
                    alert('API configuration saved successfully!');
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error saving configuration: ' + error.message);
            }
        });

        // Generator Settings Form
        document.getElementById('generatorSettingsForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                default_validity_days: parseInt(document.getElementById('defaultValidityDays').value),
                username_prefix: document.getElementById('usernamePrefix').value,
                add_random_suffix: document.getElementById('addRandomSuffix').checked,
                config_format: document.getElementById('configFormat').value
            };

            try {
                const response = await fetch('/vpn-config-generator/api/config/generator', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                if (result.success) {
                    alert('Generator settings saved successfully!');
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error saving settings: ' + error.message);
            }
        });

        // Test Generation Form
        document.getElementById('testGenerationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                server: document.getElementById('testServer').value,
                days: document.getElementById('testDays').value,
                telco: document.getElementById('testTelco').value,
                plan: document.getElementById('testPlan').value,
                username: document.getElementById('testUsername').value
            };

            try {
                const response = await fetch('/vpn-config-generator/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('testResult');
                const alertDiv = document.getElementById('testResultAlert');
                const contentDiv = document.getElementById('testResultContent');

                if (result.success) {
                    alertDiv.className = 'p-4 rounded-lg bg-green-100 border border-green-400 text-green-700';
                    contentDiv.textContent = JSON.stringify(result, null, 2);
                } else {
                    alertDiv.className = 'p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
                    contentDiv.textContent = 'Error: ' + result.error;
                }

                resultDiv.classList.remove('hidden');
            } catch (error) {
                const resultDiv = document.getElementById('testResult');
                const alertDiv = document.getElementById('testResultAlert');
                const contentDiv = document.getElementById('testResultContent');

                alertDiv.className = 'p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
                contentDiv.textContent = 'Error: ' + error.message;
                resultDiv.classList.remove('hidden');
            }
        });

        // Test Connection
        async function testConnection() {
            try {
                const response = await fetch('/vpn-config-generator/api/test-connection', {
                    method: 'POST'
                });

                const result = await response.json();
                if (result.success) {
                    alert('Connection test successful: ' + result.message);
                } else {
                    alert('Connection test failed: ' + result.message);
                }
            } catch (error) {
                alert('Error testing connection: ' + error.message);
            }
        }

        // Telco Management Functions

        // Load telco summary on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadTelcoSummary();
            checkChatCommandsStatus();
            loadCommandConfig();
            loadCommandPrefix();
        });

        async function loadTelcoSummary() {
            try {
                const response = await fetch('/vpn-config-generator/api/telcos');
                const result = await response.json();

                if (result.success) {
                    displayTelcoSummary(result.telcos);
                } else {
                    console.error('Failed to load telco summary:', result.error);
                }
            } catch (error) {
                console.error('Error loading telco summary:', error);
            }
        }

        function displayTelcoSummary(telcos) {
            const container = document.getElementById('telcoSummary');
            container.innerHTML = '';

            Object.values(telcos).forEach(telco => {
                const planCount = Object.keys(telco.plans || {}).length;
                const enabledPlans = Object.values(telco.plans || {}).filter(plan => plan.enabled).length;

                const card = document.createElement('div');
                card.className = 'bg-gray-50 rounded-lg p-4 border border-gray-200';
                card.innerHTML = `
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-800">${telco.name}</h4>
                        <span class="px-2 py-1 text-xs rounded-full ${telco.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${telco.enabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">${telco.description}</p>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">Plans: ${enabledPlans}/${planCount}</span>
                        <span class="text-blue-600 font-medium">${telco.id}</span>
                    </div>
                `;
                container.appendChild(card);
            });

            if (Object.keys(telcos).length === 0) {
                container.innerHTML = '<div class="col-span-full text-center text-gray-500 py-8">No telco configurations found. <a href="/vpn-config-generator/telco-management" class="text-blue-600 hover:underline">Add some telcos</a> to get started.</div>';
            }
        }







        // Chat Commands Integration Functions
        async function checkChatCommandsStatus() {
            try {
                const response = await fetch('/vpn-config-generator/api/chat-commands/status');
                const result = await response.json();

                const statusElement = document.getElementById('chatCommandsStatus');
                const registerBtn = document.getElementById('registerBtn');
                const unregisterBtn = document.getElementById('unregisterBtn');

                if (result.success) {
                    if (result.registered) {
                        statusElement.className = 'ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full';
                        statusElement.textContent = 'Registered';
                        registerBtn.disabled = true;
                        registerBtn.className = 'bg-gray-400 text-white px-4 py-2 rounded-lg cursor-not-allowed';
                        unregisterBtn.disabled = false;
                        unregisterBtn.className = 'bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition duration-200';
                    } else {
                        statusElement.className = 'ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full';
                        statusElement.textContent = 'Not Registered';
                        registerBtn.disabled = false;
                        registerBtn.className = 'bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200';
                        unregisterBtn.disabled = true;
                        unregisterBtn.className = 'bg-gray-400 text-white px-4 py-2 rounded-lg cursor-not-allowed';
                    }
                } else {
                    statusElement.className = 'ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full';
                    statusElement.textContent = 'Error';
                }
            } catch (error) {
                console.error('Error checking chat commands status:', error);
                const statusElement = document.getElementById('chatCommandsStatus');
                statusElement.className = 'ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full';
                statusElement.textContent = 'Error';
            }
        }

        async function registerChatCommands() {
            try {
                const response = await fetch('/vpn-config-generator/api/chat-commands/register', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('VPN commands registered successfully with Chat Commands plugin!');
                    checkChatCommandsStatus();
                } else {
                    alert('Error registering commands: ' + result.error);
                }
            } catch (error) {
                alert('Error registering commands: ' + error.message);
            }
        }

        async function unregisterChatCommands() {
            if (confirm('Are you sure you want to unregister VPN commands from the Chat Commands plugin?')) {
                try {
                    const response = await fetch('/vpn-config-generator/api/chat-commands/unregister', {
                        method: 'POST'
                    });
                    const result = await response.json();

                    if (result.success) {
                        alert('VPN commands unregistered successfully from Chat Commands plugin!');
                        checkChatCommandsStatus();
                    } else {
                        alert('Error unregistering commands: ' + result.error);
                    }
                } catch (error) {
                    alert('Error unregistering commands: ' + error.message);
                }
            }
        }

        // Command Configuration Functions
        async function loadCommandConfig() {
            try {
                const response = await fetch('/vpn-config-generator/api/command-config');
                const result = await response.json();

                if (result.success) {
                    const config = result.config;
                    document.getElementById('currentCommandName').textContent = config.command_name;
                    commandName = config.command_name;
                    updateAvailableCommandDisplay();
                } else {
                    console.error('Failed to load command config:', result.error);
                }
            } catch (error) {
                console.error('Error loading command config:', error);
            }
        }

        async function loadCommandPrefix() {
            try {
                const response = await fetch('/vpn-config-generator/api/command-prefix');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('currentPrefix').textContent = result.prefix;
                    commandPrefix = result.prefix;
                    updateAvailableCommandDisplay();
                } else {
                    console.error('Failed to load command prefix:', result.error);
                }
            } catch (error) {
                console.error('Error loading command prefix:', error);
            }
        }

        function showCommandConfigModal() {
            // Load current configuration
            loadCommandConfigForEdit();
            document.getElementById('commandConfigModal').classList.remove('hidden');
        }

        function closeCommandConfigModal() {
            document.getElementById('commandConfigModal').classList.add('hidden');
        }

        async function loadCommandConfigForEdit() {
            try {
                const response = await fetch('/vpn-config-generator/api/command-config');
                const result = await response.json();

                if (result.success) {
                    const config = result.config;
                    document.getElementById('commandName').value = config.command_name;
                    document.getElementById('commandDescription').value = config.command_description;
                    document.getElementById('responseText').value = config.response_text;
                    document.getElementById('commandEnabled').checked = config.enabled;
                } else {
                    alert('Error loading command configuration: ' + result.error);
                }
            } catch (error) {
                alert('Error loading command configuration: ' + error.message);
            }
        }

        async function saveCommandConfig() {
            try {
                const formData = {
                    command_name: document.getElementById('commandName').value,
                    command_description: document.getElementById('commandDescription').value,
                    response_text: document.getElementById('responseText').value,
                    enabled: document.getElementById('commandEnabled').checked
                };

                const response = await fetch('/vpn-config-generator/api/command-config', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                if (result.success) {
                    alert('Command configuration saved successfully!');
                    closeCommandConfigModal();
                    loadCommandConfig();
                    checkChatCommandsStatus(); // Refresh status to show new command name
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error saving command configuration: ' + error.message);
            }
        }

        // URL Input Validation and Cleaning
        document.addEventListener('DOMContentLoaded', function() {
            const fallbackEndpointInput = document.getElementById('fallbackEndpoint');
            if (fallbackEndpointInput) {
                fallbackEndpointInput.addEventListener('blur', function() {
                    // Clean the URL when user finishes editing
                    let url = this.value.trim();
                    if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
                        url = 'https://' + url;
                        this.value = url;
                    }
                });
                
                fallbackEndpointInput.addEventListener('input', function() {
                    // Remove any leading/trailing whitespace as user types
                    this.value = this.value.replace(/^\s+|\s+$/g, '');
                });
            }
        });
    </script>
{% endblock %}
