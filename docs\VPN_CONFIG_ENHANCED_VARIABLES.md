# VPN Config Generator Enhanced Variables

## Overview

The VPN Config Generator plugin has been enhanced with a comprehensive variable system that allows for highly customizable message templates. This document outlines all available variables and their usage.

## Available Variables

### Core Configuration Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{uuid}` | Client UUID | `a1b2c3d4-e5f6-7890-abcd-ef1234567890` |
| `{username}` | Username | `john_doe` |
| `{shopee_username}` | Shopee username (alias) | `john_doe` |
| `{shopee_user}` | Shopee user (alias) | `john_doe` |
| `{identity}` | User identity | `john_doe` |
| `{email}` | Generated email address | `<EMAIL>` |
| `{days}` | Validity days | `7` |
| `{validity}` | Formatted validity | `7 Day(s)` |
| `{telco}` | Telco name | `digi` |
| `{plan}` | Plan name | `unlimited` |

### Server Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{server}` | Server domain | `server11.steam.autos` |
| `{server_id}` | Original server input | `11` |
| `{server_name}` | Server domain (alias) | `server11.steam.autos` |
| `{server_number}` | Server number/name | `11` |

### Date Variables

| Variable | Description | Format | Example |
|----------|-------------|--------|---------|
| `{created_date}` | Created date | YYYY-MM-DD | `2025-06-21` |
| `{expired_date}` | Expired date | YYYY-MM-DD | `2025-06-28` |
| `{created_date_formatted}` | Created date | DD-MM-YYYY | `21-06-2025` |
| `{expired_date_formatted}` | Expired date | DD-MM-YYYY | `28-06-2025` |
| `{created_datetime}` | Created datetime | YYYY-MM-DD HH:MM | `2025-06-21 14:30` |
| `{expired_datetime}` | Expired datetime | YYYY-MM-DD HH:MM | `2025-06-28 14:30` |
| `{created_time}` | Created time | HH:MM | `14:30` |

### Additional Formatting Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{config_id}` | Short config ID (8 chars) | `a1b2c3d4` |
| `{short_uuid}` | Short UUID (8 chars) | `a1b2c3d4` |

## Enhanced Default Template

The new enhanced default template includes:

```
🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️

📧 Email: {email}
👤 Shopee User: {shopee_username}
🖥️ Server: {server_number}
📅 Created: {created_datetime}
⏰ Expires: {expired_date_formatted}
📡 Telco: {telco}
📋 Plan: {plan}
⏳ Validity: {validity}

🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️
```

## Custom Template Examples

### Example 1: Minimal Template
```
Config for {username}
Server: {server_number}
Expires: {expired_date_formatted}
```

### Example 2: Detailed Template
```
🎯 VPN Configuration Details 🎯

👤 User Information:
   • Username: {shopee_username}
   • Email: {email}
   • Config ID: {config_id}

🖥️ Server Information:
   • Server: {server} (#{server_number})
   • Telco: {telco}
   • Plan: {plan}

📅 Validity Information:
   • Created: {created_datetime}
   • Expires: {expired_date_formatted}
   • Duration: {validity}

🔑 UUID: {uuid}
```

### Example 3: Simple Format
```
📱 {telco} {plan} Config
👤 {username}
🖥️ Server {server_number}
📅 {created_date} - {expired_date_formatted}
⏰ {validity}
```

## Usage in Interface

1. **Telco Management**: Navigate to VPN Config Generator → Telco Management
2. **Create/Edit Plan**: When creating or editing a plan, use the "Custom Info Message Template" field
3. **Variable Reference**: The interface shows all available variables organized by category
4. **Live Preview**: The placeholder text demonstrates the enhanced default template

## Implementation Details

### Variable Generation
- Variables are generated dynamically for each configuration request
- Date formatting supports multiple formats for different display needs
- Email addresses follow the VPN API format: `user-{username}@shopee.local`
- UUIDs are generated fresh for each configuration

### Backward Compatibility
- All existing templates continue to work
- New variables are additive and don't break existing functionality
- Default template is enhanced but maintains the same structure

### Error Handling
- Missing variables are replaced with "Unknown"
- Invalid date formats fall back to basic date strings
- Template parsing errors fall back to the basic template

## Benefits

1. **Flexibility**: Support for various message formats and styles
2. **Internationalization**: Multiple date formats for different regions
3. **User Experience**: Rich information display with emojis and formatting
4. **Customization**: Complete control over message appearance
5. **Consistency**: Standardized variable naming across all templates

## Migration Notes

Existing configurations will automatically use the enhanced default template. To customize:

1. Edit existing plans to add custom info message templates
2. Use the new variables for richer information display
3. Test templates with different variable combinations
4. Consider user preferences for date formats and information density
