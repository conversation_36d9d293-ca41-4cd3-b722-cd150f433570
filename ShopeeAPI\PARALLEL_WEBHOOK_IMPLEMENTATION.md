# Parallel Webhook Implementation

## Overview

The ShopeeAPI webhook system has been enhanced to send MESSAGE_RECEIVE and MESSAGE_SENT events to multiple webhook URLs in parallel, preventing blocking and improving performance when multiple webhook endpoints are configured.

## Problem Solved

Previously, webhooks were sent sequentially (one after another), which could cause significant delays when:
- Multiple webhook URLs are configured
- Some webhook endpoints have slow response times
- Network latency affects webhook delivery

This sequential approach could block the main WebSocket message processing, potentially causing message delivery delays.

## Solution

The webhook system now uses `asyncio.gather()` to send webhooks to all configured URLs simultaneously, ensuring:
- **Non-blocking operation**: Slow webhook endpoints don't affect others
- **Parallel execution**: All webhooks are sent at the same time
- **Improved performance**: Total webhook time equals the slowest endpoint, not the sum of all endpoints
- **Better reliability**: Individual webhook failures don't affect others

## Implementation Details

### Key Changes

1. **Modified `send_webhook()` method** in `utils/webhook.py`:
   - Creates individual tasks for each webhook URL
   - Uses `asyncio.gather()` for parallel execution
   - Maintains retry logic for each URL independently

2. **New `_send_webhook_to_url()` method**:
   - Handles individual webhook sending with retry logic
   - Isolated error handling per URL
   - Independent timeout and retry configuration

### Code Structure

```python
async def send_webhook(self, webhook_type: str, data: Dict[str, Any]) -> bool:
    # ... validation code ...
    
    # Create tasks for parallel webhook sending
    webhook_tasks = []
    for url_config in enabled_urls:
        task = asyncio.create_task(
            self._send_webhook_to_url(webhook_type, data, url, name, retry_count, retry_delay)
        )
        webhook_tasks.append(task)
    
    # Wait for all webhook tasks to complete in parallel
    results = await asyncio.gather(*webhook_tasks, return_exceptions=True)
    
    # Count successful sends
    success_count = sum(1 for result in results if result is True)
    return success_count > 0

async def _send_webhook_to_url(self, webhook_type: str, data: Dict[str, Any], 
                              url: str, name: str, retry_count: int, retry_delay: int) -> bool:
    # Individual webhook sending with retry logic
    for attempt in range(retry_count + 1):
        try:
            # Send webhook request
            # Handle retries and errors
        except Exception as e:
            # Error handling
    return False
```

## Performance Benefits

### Before (Sequential)
- **Total Time**: Sum of all webhook response times
- **Example**: 3 webhooks with 0.1s, 0.5s, 1.0s delays = **1.6s total**
- **Blocking**: Slow webhooks delay subsequent ones

### After (Parallel)
- **Total Time**: Maximum of all webhook response times
- **Example**: 3 webhooks with 0.1s, 0.5s, 1.0s delays = **1.0s total**
- **Non-blocking**: All webhooks sent simultaneously

## Configuration

No configuration changes are required. The parallel webhook feature works with existing webhook configurations:

```json
{
  "WEBHOOK": {
    "ENABLED": true,
    "MESSAGE_RECEIVED": {
      "ENABLED": true,
      "URLS": [
        {
          "URL": "http://endpoint1.com/webhook",
          "NAME": "Endpoint 1",
          "ENABLED": true
        },
        {
          "URL": "http://endpoint2.com/webhook",
          "NAME": "Endpoint 2", 
          "ENABLED": true
        },
        {
          "URL": "http://endpoint3.com/webhook",
          "NAME": "Endpoint 3",
          "ENABLED": true
        }
      ],
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    },
    "MESSAGE_SENT": {
      "ENABLED": true,
      "URLS": [
        // Same structure as MESSAGE_RECEIVED
      ]
    }
  }
}
```

## Error Handling

- **Individual failures**: One webhook failure doesn't affect others
- **Retry logic**: Each webhook URL has independent retry attempts
- **Exception handling**: Exceptions are caught and logged per webhook
- **Success tracking**: Returns true if at least one webhook succeeds

## Logging

Enhanced logging provides visibility into parallel webhook operations:

```
INFO:utils.webhook:Successfully sent MESSAGE_RECEIVED webhook to 3/3 URLs (parallel)
INFO:utils.webhook:Successfully sent MESSAGE_RECEIVED webhook to Fast Server (http://localhost:8001/webhook)
INFO:utils.webhook:Successfully sent MESSAGE_RECEIVED webhook to Medium Server (http://localhost:8002/webhook)
INFO:utils.webhook:Successfully sent MESSAGE_RECEIVED webhook to Slow Server (http://localhost:8003/webhook)
```

## Testing

The implementation has been tested with:
- Multiple webhook endpoints with different response times
- Simulated network delays and failures
- Concurrent webhook sending verification
- Error handling and retry logic validation

## Backward Compatibility

The enhancement maintains full backward compatibility:
- Existing webhook configurations work unchanged
- Single webhook URLs continue to function normally
- Legacy URL field support is preserved
- All existing retry and timeout settings are respected

## Benefits Summary

1. **Performance**: Significantly reduced webhook delivery time
2. **Reliability**: Individual webhook failures don't affect others
3. **Scalability**: Can handle many webhook endpoints efficiently
4. **Non-blocking**: Doesn't delay main message processing
5. **Maintainability**: Clean separation of concerns with dedicated methods
