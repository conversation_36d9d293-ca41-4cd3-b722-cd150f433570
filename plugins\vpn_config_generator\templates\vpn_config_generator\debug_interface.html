<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Debug Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .scenario-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .scenario-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .scenario-card.selected {
            border-color: #3B82F6;
            background-color: #EBF8FF;
        }
    </style>
</head>

<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🧪 VPN Debug Interface</h1>
                <p class="text-gray-600">Create and test VPN orders with different restriction scenarios</p>
                <div class="mt-4 flex space-x-4">
                    <a href="/vpn-config-generator/order-config" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        🔗 Go to VPN Config Page
                    </a>
                    <a href="/vpn-config-generator" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        ⚙️ Admin Dashboard
                    </a>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Test Scenarios -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">📋 Predefined Test Scenarios</h2>
                    <div id="scenariosList" class="space-y-3">
                        <!-- Scenarios will be loaded here -->
                    </div>
                    <button id="loadScenariosBtn" 
                            class="mt-4 w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200"
                            onclick="loadTestScenarios()">
                        Load Test Scenarios
                    </button>
                </div>

                <!-- Custom Order Creation -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">🛠️ Create Custom Test Order</h2>
                    <form id="customOrderForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Order SN (Optional)</label>
                            <input type="text" id="customOrderSn" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Leave empty for auto-generation">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">VAR SKU</label>
                            <input type="text" id="customVarSku" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="e.g., vpn_service_30, my_highspeed_15">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Buyer Username</label>
                            <input type="text" id="customBuyerUsername" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="test_user" value="test_user">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Restriction Type</label>
                            <select id="customRestrictionType" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="none">No Restrictions</option>
                                <option value="my_restricted">My_ Prefix (Telco Lock)</option>
                                <option value="custom">Custom</option>
                            </select>
                        </div>
                        
                        <button type="submit" 
                                class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                            Create Test Order
                        </button>
                    </form>
                </div>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" class="mt-6 bg-white rounded-lg shadow-md p-6 hidden">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">📊 Test Results</h2>
                <div id="resultsContent">
                    <!-- Results will be displayed here -->
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-6 bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">⚡ Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="createQuickTest('regular')" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition duration-200">
                        🔓 Create Regular User Test
                    </button>
                    <button onclick="createQuickTest('restricted')" 
                            class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg transition duration-200">
                        🔒 Create Restricted User Test
                    </button>
                    <button onclick="viewFakeOrders()" 
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-lg transition duration-200">
                        📋 View All Fake Orders
                    </button>
                </div>
            </div>

            <!-- Instructions -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-2">📖 How to Use</h3>
                <ol class="list-decimal list-inside text-blue-700 space-y-1">
                    <li>Select a predefined scenario or create a custom test order</li>
                    <li>Click "Create Test Order" to generate a fake order</li>
                    <li>Use the provided test URL to test the VPN configuration flow</li>
                    <li>Verify the expected behavior matches the actual results</li>
                    <li>Clean up test orders when done (optional)</li>
                </ol>
                
                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <p class="text-yellow-800 text-sm">
                        <strong>Note:</strong> Test orders are stored in <code>configs/data/manual_orders.json</code> 
                        and integrate with the existing fake order system.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedScenario = null;

        // Load test scenarios
        async function loadTestScenarios() {
            try {
                const response = await axios.get('/vpn-config-generator/api/debug/test-scenarios');
                if (response.data.success) {
                    displayScenarios(response.data.scenarios);
                } else {
                    alert('Error loading scenarios: ' + response.data.error);
                }
            } catch (error) {
                alert('Error loading scenarios: ' + error.message);
            }
        }

        function displayScenarios(scenarios) {
            const container = document.getElementById('scenariosList');
            container.innerHTML = '';

            scenarios.forEach((scenario, index) => {
                const card = document.createElement('div');
                card.className = 'scenario-card p-4 border-2 border-gray-200 rounded-lg';
                card.innerHTML = `
                    <h4 class="font-semibold text-gray-800">${scenario.name}</h4>
                    <p class="text-sm text-gray-600 mt-1">${scenario.description}</p>
                    <div class="mt-2 text-xs text-blue-600">
                        <span class="font-medium">SKU:</span> ${scenario.var_sku} | 
                        <span class="font-medium">Type:</span> ${scenario.restriction_type}
                    </div>
                    <p class="text-xs text-gray-500 mt-2">${scenario.expected_behavior}</p>
                `;

                card.addEventListener('click', () => selectScenario(scenario, card, index));
                container.appendChild(card);
            });

            // Hide load button and show create button
            document.getElementById('loadScenariosBtn').style.display = 'none';
            
            const createBtn = document.createElement('button');
            createBtn.className = 'mt-4 w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200';
            createBtn.textContent = 'Create Selected Scenario';
            createBtn.onclick = createSelectedScenario;
            container.parentElement.appendChild(createBtn);
        }

        function selectScenario(scenario, cardElement, index) {
            // Remove previous selection
            document.querySelectorAll('.scenario-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current scenario
            cardElement.classList.add('selected');
            selectedScenario = scenario;

            // Populate custom form with scenario data
            document.getElementById('customVarSku').value = scenario.var_sku;
            document.getElementById('customRestrictionType').value = scenario.restriction_type;
        }

        async function createSelectedScenario() {
            if (!selectedScenario) {
                alert('Please select a scenario first');
                return;
            }

            await createTestOrder(selectedScenario);
        }

        // Custom order form submission
        document.getElementById('customOrderForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const orderData = {
                order_sn: document.getElementById('customOrderSn').value.trim() || undefined,
                var_sku: document.getElementById('customVarSku').value.trim(),
                buyer_username: document.getElementById('customBuyerUsername').value.trim() || 'test_user',
                restriction_type: document.getElementById('customRestrictionType').value
            };

            await createTestOrder(orderData);
        });

        async function createTestOrder(orderData) {
            try {
                const response = await axios.post('/vpn-config-generator/api/debug/create-test-order', orderData);
                
                if (response.data.success) {
                    displayResults(response.data);
                } else {
                    alert('Error creating test order: ' + response.data.error);
                }
            } catch (error) {
                alert('Error creating test order: ' + error.message);
            }
        }

        function displayResults(result) {
            const resultsSection = document.getElementById('resultsSection');
            const resultsContent = document.getElementById('resultsContent');
            
            resultsContent.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <h4 class="font-semibold text-green-800 mb-2">✅ Test Order Created Successfully</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p><span class="font-medium">Order SN:</span> ${result.order_sn}</p>
                            <p><span class="font-medium">VAR SKU:</span> ${result.var_sku}</p>
                            <p><span class="font-medium">Username:</span> ${result.buyer_username}</p>
                        </div>
                        <div>
                            <p><span class="font-medium">Restriction:</span> ${result.restriction_type}</p>
                            <p><span class="font-medium">Status:</span> To Ship</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-2">🔗 Test Links</h4>
                    <div class="space-y-2">
                        <a href="${result.test_url}" target="_blank" 
                           class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition duration-200">
                            Test VPN Configuration Flow
                        </a>
                        <a href="/order?order_sn=${result.order_sn}" target="_blank" 
                           class="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition duration-200 ml-2">
                            Test Order Processing
                        </a>
                    </div>
                    <p class="text-sm text-blue-700 mt-2">${result.message}</p>
                </div>
            `;
            
            resultsSection.classList.remove('hidden');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // Quick test functions
        async function createQuickTest(type) {
            const orderData = {
                var_sku: type === 'restricted' ? 'my_quicktest_30' : 'vpn_quicktest_30',
                buyer_username: `${type}_tester`,
                restriction_type: type === 'restricted' ? 'my_restricted' : 'none'
            };

            await createTestOrder(orderData);
        }

        function viewFakeOrders() {
            window.open('/order', '_blank');
        }

        // Load scenarios on page load
        window.addEventListener('load', function() {
            // Auto-load scenarios
            setTimeout(loadTestScenarios, 500);
        });
    </script>
</body>

</html>
