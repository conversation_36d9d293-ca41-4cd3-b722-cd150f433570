"""
Unit Tests for OpenAI Plus Redeem Models

Comprehensive unit tests for all data models including validation,
serialization, and edge cases.
"""

import unittest
import tempfile
import os
import json
from datetime import datetime, timedelta
from unittest.mock import patch, mock_open

# Import models
from ..models.chatgpt_account import ChatGPTAccount
from ..models.order_redemption import OrderRedemption, RedemptionStatus
from ..models.email_verification import EmailVerification, VerificationStatus
from ..models.account_cooldown import AccountCooldown, CooldownReason, CooldownStatus
from ..models.utils import (
    validate_email, validate_password, validate_sku, validate_username,
    validate_iso_datetime, validate_verification_code, sanitize_string,
    serialize_model, deserialize_model, load_json_data, save_json_data,
    format_duration, parse_duration_string
)


class TestChatGPTAccount(unittest.TestCase):
    """Test cases for ChatGPTAccount model"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.account_data = {
            'email': '<EMAIL>',
            'password': 'test_password',
            'max_concurrent_users': 5,
            'validity_days': 30
        }
    
    def test_account_creation(self):
        """Test basic account creation"""
        account = ChatGPTAccount(**self.account_data)
        
        self.assertEqual(account.email, '<EMAIL>')
        self.assertEqual(account.max_concurrent_users, 5)
        self.assertEqual(account.validity_days, 30)
        self.assertEqual(account.current_users, 0)
        self.assertTrue(account.is_active)
        self.assertIsNotNone(account.account_id)
    
    def test_parse_capacity_from_sku(self):
        """Test SKU parsing functionality"""
        # Test standard chatgpt SKU
        users, days = ChatGPTAccount.parse_capacity_from_sku("chatgpt_5_30")
        self.assertEqual(users, 5)
        self.assertEqual(days, 30)
        
        # Test my_ prefix SKU
        users, days = ChatGPTAccount.parse_capacity_from_sku("my_premium_15")
        self.assertEqual(users, 1)
        self.assertEqual(days, 15)
        
        # Test invalid SKU
        users, days = ChatGPTAccount.parse_capacity_from_sku("invalid_sku")
        self.assertEqual(users, 1)
        self.assertEqual(days, 30)
        
        # Test empty SKU
        users, days = ChatGPTAccount.parse_capacity_from_sku("")
        self.assertEqual(users, 1)
        self.assertEqual(days, 30)
    
    def test_from_sku(self):
        """Test account creation from SKU"""
        account = ChatGPTAccount.from_sku(
            email="<EMAIL>",
            password="password123",
            var_sku="chatgpt_3_15"
        )
        
        self.assertEqual(account.email, "<EMAIL>")
        self.assertEqual(account.max_concurrent_users, 3)
        self.assertEqual(account.validity_days, 15)
    
    def test_user_assignment(self):
        """Test user assignment and release"""
        account = ChatGPTAccount(**self.account_data)
        
        # Test assignment
        self.assertTrue(account.can_assign_user())
        self.assertTrue(account.assign_user())
        self.assertEqual(account.current_users, 1)
        
        # Test capacity limit
        for _ in range(4):  # Assign 4 more users (total 5)
            account.assign_user()
        
        self.assertFalse(account.can_assign_user())
        self.assertFalse(account.assign_user())
        self.assertEqual(account.current_users, 5)
        
        # Test release
        self.assertTrue(account.release_user())
        self.assertEqual(account.current_users, 4)
        self.assertTrue(account.can_assign_user())
    
    def test_expiration(self):
        """Test account expiration"""
        # Create account with past expiration
        past_date = (datetime.now() - timedelta(days=1)).isoformat()
        account = ChatGPTAccount(
            email="<EMAIL>",
            expiration_date=past_date
        )
        
        self.assertTrue(account.is_expired())
        self.assertFalse(account.can_assign_user())
    
    def test_serialization(self):
        """Test account serialization/deserialization"""
        account = ChatGPTAccount(**self.account_data)
        
        # Test to_dict
        data = account.to_dict()
        self.assertIsInstance(data, dict)
        self.assertEqual(data['email'], '<EMAIL>')
        
        # Test from_dict
        new_account = ChatGPTAccount.from_dict(data)
        self.assertEqual(new_account.email, account.email)
        self.assertEqual(new_account.account_id, account.account_id)


class TestOrderRedemption(unittest.TestCase):
    """Test cases for OrderRedemption model"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.redemption_data = {
            'order_id': 'ORDER123',
            'buyer_username': 'testuser',
            'sku': 'chatgpt_plus',
            'var_sku': 'chatgpt_5_30'
        }
    
    def test_redemption_creation(self):
        """Test basic redemption creation"""
        redemption = OrderRedemption(**self.redemption_data)
        
        self.assertEqual(redemption.order_id, 'ORDER123')
        self.assertEqual(redemption.buyer_username, 'testuser')
        self.assertEqual(redemption.status, RedemptionStatus.PENDING)
        self.assertIsNotNone(redemption.redemption_id)
    
    def test_create_from_order(self):
        """Test redemption creation from order"""
        redemption = OrderRedemption.create_from_order(
            order_id="ORDER456",
            buyer_username="user2",
            sku="chatgpt_plus",
            var_sku="chatgpt_1_15"
        )
        
        self.assertEqual(redemption.order_id, "ORDER456")
        self.assertEqual(redemption.buyer_username, "user2")
    
    def test_status_management(self):
        """Test redemption status management"""
        redemption = OrderRedemption(**self.redemption_data)
        
        # Test activation
        self.assertTrue(redemption.can_redeem())
        self.assertTrue(redemption.activate("account123"))
        self.assertEqual(redemption.status, RedemptionStatus.ACTIVE)
        self.assertEqual(redemption.assigned_account_id, "account123")
        
        # Test cooldown
        redemption.set_cooldown(24)
        self.assertEqual(redemption.status, RedemptionStatus.COOLDOWN)
        self.assertTrue(redemption.is_in_cooldown())
        
        # Test clear cooldown
        redemption.clear_cooldown()
        self.assertEqual(redemption.status, RedemptionStatus.PENDING)
        self.assertFalse(redemption.is_in_cooldown())
    
    def test_expiration(self):
        """Test redemption expiration"""
        # Create redemption with past validity
        past_date = (datetime.now() - timedelta(days=1)).isoformat()
        redemption = OrderRedemption(
            validity_until=past_date,
            **self.redemption_data
        )
        
        self.assertTrue(redemption.is_expired())
        self.assertFalse(redemption.can_redeem())
    
    def test_error_handling(self):
        """Test error handling"""
        redemption = OrderRedemption(**self.redemption_data)
        
        redemption.set_error("Test error message")
        self.assertEqual(redemption.status, RedemptionStatus.ERROR)
        self.assertEqual(redemption.error_message, "Test error message")
        self.assertEqual(redemption.retry_count, 1)


class TestEmailVerification(unittest.TestCase):
    """Test cases for EmailVerification model"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.verification_data = {
            'redemption_id': 'REDEMPTION123',
            'account_email': '<EMAIL>'
        }
    
    def test_verification_creation(self):
        """Test basic verification creation"""
        verification = EmailVerification(**self.verification_data)
        
        self.assertEqual(verification.redemption_id, 'REDEMPTION123')
        self.assertEqual(verification.account_email, '<EMAIL>')
        self.assertEqual(verification.status, VerificationStatus.PENDING)
        self.assertEqual(verification.search_attempts, 0)
    
    def test_create_for_redemption(self):
        """Test verification creation for redemption"""
        verification = EmailVerification.create_for_redemption(
            redemption_id="REDEMPTION456",
            account_email="<EMAIL>",
            timeout_minutes=15,
            max_attempts=3
        )
        
        self.assertEqual(verification.redemption_id, "REDEMPTION456")
        self.assertEqual(verification.search_timeout_minutes, 15)
        self.assertEqual(verification.max_search_attempts, 3)
    
    def test_search_management(self):
        """Test search attempt management"""
        verification = EmailVerification(**self.verification_data)
        
        # Test search start
        self.assertTrue(verification.can_search())
        self.assertTrue(verification.start_search())
        self.assertEqual(verification.status, VerificationStatus.SEARCHING)
        self.assertEqual(verification.search_attempts, 1)
        
        # Test search completion
        verification.complete_search(
            verification_code="123456",
            email_subject="Verify your ChatGPT account",
            email_sender="<EMAIL>"
        )
        self.assertEqual(verification.status, VerificationStatus.FOUND)
        self.assertEqual(verification.verification_code, "123456")
        
        # Test search failure
        verification.fail_search("Email not found")
        self.assertEqual(verification.status, VerificationStatus.FAILED)
        self.assertEqual(verification.error_message, "Email not found")
    
    def test_expiration(self):
        """Test verification expiration"""
        # Create verification with past expiration
        past_date = (datetime.now() - timedelta(hours=1)).isoformat()
        verification = EmailVerification(
            expires_at=past_date,
            **self.verification_data
        )
        
        self.assertTrue(verification.is_expired())
        self.assertFalse(verification.can_search())


class TestAccountCooldown(unittest.TestCase):
    """Test cases for AccountCooldown model"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.cooldown_data = {
            'buyer_username': 'testuser',
            'order_id': 'ORDER123',
            'original_duration_hours': 24
        }
    
    def test_cooldown_creation(self):
        """Test basic cooldown creation"""
        cooldown = AccountCooldown(**self.cooldown_data)
        
        self.assertEqual(cooldown.buyer_username, 'testuser')
        self.assertEqual(cooldown.original_duration_hours, 24)
        self.assertEqual(cooldown.status, CooldownStatus.ACTIVE)
        self.assertTrue(cooldown.is_active())
    
    def test_create_for_user(self):
        """Test cooldown creation for user"""
        cooldown = AccountCooldown.create_for_user(
            buyer_username="user2",
            duration_hours=48,
            reason=CooldownReason.ABUSE_PREVENTION,
            order_id="ORDER456"
        )
        
        self.assertEqual(cooldown.buyer_username, "user2")
        self.assertEqual(cooldown.original_duration_hours, 48)
        self.assertEqual(cooldown.reason, CooldownReason.ABUSE_PREVENTION)
    
    def test_cooldown_management(self):
        """Test cooldown management operations"""
        cooldown = AccountCooldown(**self.cooldown_data)
        
        # Test extension
        cooldown.extend_cooldown(12, "Extended for testing")
        self.assertIn("Extended by 12h", cooldown.notes)
        
        # Test reset
        self.assertTrue(cooldown.can_reset())
        self.assertTrue(cooldown.reset_cooldown("admin", "Reset for testing"))
        self.assertEqual(cooldown.status, CooldownStatus.RESET)
        self.assertEqual(cooldown.reset_count, 1)
        
        # Test cancellation
        cooldown.cancel_cooldown("admin", "Cancelled for testing")
        self.assertEqual(cooldown.status, CooldownStatus.CANCELLED)
    
    def test_time_calculations(self):
        """Test time-related calculations"""
        cooldown = AccountCooldown(**self.cooldown_data)
        
        # Test remaining time (should be close to 24 hours)
        remaining = cooldown.get_remaining_time()
        self.assertGreater(remaining.total_seconds(), 23 * 3600)  # At least 23 hours
        
        # Test remaining hours
        remaining_hours = cooldown.get_remaining_hours()
        self.assertGreaterEqual(remaining_hours, 23)
        self.assertLessEqual(remaining_hours, 24)


class TestModelUtils(unittest.TestCase):
    """Test cases for model utilities"""
    
    def test_email_validation(self):
        """Test email validation"""
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertFalse(validate_email("invalid-email"))
        self.assertFalse(validate_email(""))
        self.assertFalse(validate_email(None))
    
    def test_password_validation(self):
        """Test password validation"""
        self.assertTrue(validate_password("password123"))
        self.assertTrue(validate_password("complex_password!"))
        self.assertFalse(validate_password("short"))
        self.assertFalse(validate_password(""))
        self.assertFalse(validate_password(None))
    
    def test_sku_validation(self):
        """Test SKU validation"""
        self.assertTrue(validate_sku("chatgpt_5_30"))
        self.assertTrue(validate_sku("my_premium_15"))
        self.assertTrue(validate_sku("PRODUCT-123"))
        self.assertFalse(validate_sku("invalid sku"))
        self.assertFalse(validate_sku(""))
        self.assertFalse(validate_sku(None))
    
    def test_username_validation(self):
        """Test username validation"""
        self.assertTrue(validate_username("testuser"))
        self.assertTrue(validate_username("user.name_123"))
        self.assertFalse(validate_username("ab"))  # Too short
        self.assertFalse(validate_username("user@domain"))  # Invalid character
        self.assertFalse(validate_username(""))
        self.assertFalse(validate_username(None))
    
    def test_verification_code_validation(self):
        """Test verification code validation"""
        self.assertTrue(validate_verification_code("123456"))
        self.assertTrue(validate_verification_code("000000"))
        self.assertFalse(validate_verification_code("12345"))  # Too short
        self.assertFalse(validate_verification_code("1234567"))  # Too long
        self.assertFalse(validate_verification_code("12345a"))  # Non-numeric
        self.assertFalse(validate_verification_code(""))
        self.assertFalse(validate_verification_code(None))
    
    def test_string_sanitization(self):
        """Test string sanitization"""
        self.assertEqual(sanitize_string("  test  "), "test")
        self.assertEqual(sanitize_string("test<script>"), "testscript")
        self.assertEqual(sanitize_string('test"quote'), "testquote")
        self.assertEqual(sanitize_string("a" * 300, 10), "a" * 10)
    
    def test_duration_formatting(self):
        """Test duration formatting"""
        self.assertEqual(format_duration(30), "30s")
        self.assertEqual(format_duration(90), "1m")
        self.assertEqual(format_duration(3661), "1h 1m")
        self.assertEqual(format_duration(90061), "1d 1h")
    
    def test_duration_parsing(self):
        """Test duration string parsing"""
        self.assertEqual(parse_duration_string("1h 30m"), 5400)
        self.assertEqual(parse_duration_string("2d"), 172800)
        self.assertEqual(parse_duration_string("45m"), 2700)
        self.assertEqual(parse_duration_string(""), 0)
    
    @patch('builtins.open', new_callable=mock_open, read_data='{"test": "data"}')
    def test_json_operations(self, mock_file):
        """Test JSON load/save operations"""
        # Test load
        data = load_json_data("test.json")
        self.assertEqual(data["test"], "data")
        
        # Test save
        save_json_data("test.json", {"new": "data"})
        mock_file.assert_called()


if __name__ == '__main__':
    unittest.main()
