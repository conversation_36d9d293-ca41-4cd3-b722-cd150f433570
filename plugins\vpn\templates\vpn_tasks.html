{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{{ title }}</h3>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="enqueueTask()">
                            <i class="fas fa-plus"></i> Enqueue Task
                        </button>
                        <button type="button" class="btn btn-info" onclick="refreshTasks()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if tasks and tasks.get('tasks') %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Started</th>
                                    <th>Completed</th>
                                    <th>Progress</th>
                                    <th>Result</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks.tasks %}
                                <tr id="task-{{ task.id }}">
                                    <td>{{ task.id }}</td>
                                    <td>
                                        <span class="badge badge-info">{{ task.get('type', 'Unknown') }}</span>
                                    </td>
                                    <td>
                                        {% if task.get('status') == 'pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                        {% elif task.get('status') == 'running' %}
                                        <span class="badge badge-primary">Running</span>
                                        {% elif task.get('status') == 'completed' %}
                                        <span class="badge badge-success">Completed</span>
                                        {% elif task.get('status') == 'failed' %}
                                        <span class="badge badge-danger">Failed</span>
                                        {% else %}
                                        <span class="badge badge-secondary">{{ task.get('status', 'Unknown') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.get('created_at') %}
                                        <small>{{ task.created_at }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.get('started_at') %}
                                        <small>{{ task.started_at }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.get('completed_at') %}
                                        <small>{{ task.completed_at }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.get('progress') is not none %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ task.progress }}%"
                                                 aria-valuenow="{{ task.progress }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                {{ task.progress }}%
                                            </div>
                                        </div>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.get('result') %}
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="showTaskResult({{ task.id }}, '{{ task.result|e }}')"
                                                title="View Result">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="viewTaskDetails({{ task.id }})" 
                                                    title="View Details">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                            {% if task.get('status') in ['pending', 'running'] %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="cancelTask({{ task.id }})" 
                                                    title="Cancel Task">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if tasks.get('total', 0) > tasks.get('per_page', 50) %}
                    <nav aria-label="Tasks pagination">
                        <ul class="pagination justify-content-center">
                            {% if tasks.get('has_prev', False) %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ tasks.page - 1 }}">Previous</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in range(1, (tasks.total // tasks.per_page) + 2) %}
                            {% if page_num == tasks.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_num }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if tasks.get('has_next', False) %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ tasks.page + 1 }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No background tasks found</h5>
                        <p class="text-muted">Background tasks will appear here when they are created.</p>
                        <button type="button" class="btn btn-primary" onclick="enqueueTask()">
                            <i class="fas fa-plus"></i> Enqueue First Task
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Task Result Modal -->
<div class="modal fade" id="taskResultModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Task Result</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <pre id="taskResultContent" class="bg-light p-3"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Task Details Modal -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Task Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="taskDetailsContent">
                <!-- Task details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Processing...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showLoading() {
    $('#loadingModal').modal('show');
}

function hideLoading() {
    $('#loadingModal').modal('hide');
}

function enqueueTask() {
    showLoading();
    
    fetch('{{ url_for("vpn.enqueue_task") }}', {
        method: 'POST'
    })
    .then(response => {
        hideLoading();
        if (response.ok) {
            toastr.success('Task enqueued successfully!');
            // Refresh the page to show the new task
            location.reload();
        } else {
            toastr.error('Failed to enqueue task');
        }
    })
    .catch(error => {
        hideLoading();
        toastr.error('Failed to enqueue task: ' + error.message);
    });
}

function refreshTasks() {
    location.reload();
}

function showTaskResult(taskId, result) {
    document.getElementById('taskResultContent').textContent = result;
    $('#taskResultModal').modal('show');
}

function viewTaskDetails(taskId) {
    showLoading();
    
    fetch(`{{ url_for('vpn.get_task', task_id=0) }}`.replace('0', taskId), {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.error) {
            toastr.error('Failed to get task details: ' + data.error);
        } else {
            displayTaskDetails(data);
        }
    })
    .catch(error => {
        hideLoading();
        toastr.error('Failed to get task details: ' + error.message);
    });
}

function displayTaskDetails(task) {
    const content = `
        <table class="table table-sm">
            <tbody>
                <tr><td><strong>ID:</strong></td><td>${task.id || 'N/A'}</td></tr>
                <tr><td><strong>Type:</strong></td><td>${task.type || 'N/A'}</td></tr>
                <tr><td><strong>Status:</strong></td><td>${task.status || 'N/A'}</td></tr>
                <tr><td><strong>Created:</strong></td><td>${task.created_at || 'N/A'}</td></tr>
                <tr><td><strong>Started:</strong></td><td>${task.started_at || 'N/A'}</td></tr>
                <tr><td><strong>Completed:</strong></td><td>${task.completed_at || 'N/A'}</td></tr>
                <tr><td><strong>Progress:</strong></td><td>${task.progress !== undefined ? task.progress + '%' : 'N/A'}</td></tr>
                <tr><td><strong>Description:</strong></td><td>${task.description || 'N/A'}</td></tr>
                <tr><td><strong>Parameters:</strong></td><td><pre class="bg-light p-2">${JSON.stringify(task.parameters || {}, null, 2)}</pre></td></tr>
                <tr><td><strong>Result:</strong></td><td><pre class="bg-light p-2">${task.result || 'N/A'}</pre></td></tr>
                <tr><td><strong>Error:</strong></td><td><pre class="bg-light p-2">${task.error || 'N/A'}</pre></td></tr>
            </tbody>
        </table>
    `;
    
    document.getElementById('taskDetailsContent').innerHTML = content;
    $('#taskDetailsModal').modal('show');
}

function cancelTask(taskId) {
    if (!confirm('Are you sure you want to cancel this task?')) {
        return;
    }
    
    showLoading();
    
    // Note: This would need to be implemented in the API
    fetch(`/admin/vpn/api/tasks/${taskId}/cancel`, {
        method: 'POST'
    })
    .then(response => {
        hideLoading();
        if (response.ok) {
            toastr.success('Task cancelled successfully!');
            location.reload();
        } else {
            toastr.error('Failed to cancel task');
        }
    })
    .catch(error => {
        hideLoading();
        toastr.error('Failed to cancel task: ' + error.message);
    });
}

// Auto-refresh every 10 seconds for running tasks
setInterval(function() {
    const runningTasks = document.querySelectorAll('tr[id^="task-"] .badge-primary');
    if (runningTasks.length > 0) {
        refreshTasks();
    }
}, 10000);
</script>
{% endblock %}
