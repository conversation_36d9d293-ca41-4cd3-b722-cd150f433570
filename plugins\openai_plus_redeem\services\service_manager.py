"""
Service Manager

Central service integration and management for OpenAI Plus Redeem Plugin.
Implements proper error handling, logging, and service coordination patterns
following PLUGIN_DEVELOPMENT_GUIDELINES.md standards.
"""

import logging
import traceback
from typing import Dict, Any, List, Optional, Type
from datetime import datetime

from .base_service import BaseService
from .chatgpt_account_service import ChatGPTAccountService
from .order_redemption_service import OrderRedemptionService
from .order_redeem_service import OrderRedeemService
from .email_service import EmailService
from .cooldown_service import CooldownService
from .shopee_messaging_service import ShopeeMessagingService


class ServiceInitializationError(Exception):
    """Raised when service initialization fails"""
    pass


class ServiceIntegrationError(Exception):
    """Raised when service integration fails"""
    pass


class ServiceManager:
    """
    Central manager for all plugin services
    
    Provides functionality for:
    - Service lifecycle management
    - Service dependency resolution
    - Error handling and recovery
    - Health monitoring
    - Configuration updates
    - Service coordination
    """
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(f"{__name__}.ServiceManager")
        
        # Service registry
        self.services: Dict[str, BaseService] = {}
        self.service_classes: Dict[str, Type[BaseService]] = {
            'chatgpt_account': ChatGPTAccountService,
            'order_redemption': OrderRedemptionService,
            'order_redeem': OrderRedeemService,
            'email': EmailService,
            'cooldown': CooldownService,
            'shopee_messaging': ShopeeMessagingService
        }
        
        # Service dependencies (services that depend on others)
        self.service_dependencies = {
            'order_redeem': ['chatgpt_account', 'order_redemption', 'cooldown'],
            'shopee_messaging': []  # No dependencies
        }
        
        # State tracking
        self._initialized = False
        self._shutdown = False
        self._initialization_order: List[str] = []
        self._last_health_check = None
    
    def initialize_all_services(self) -> bool:
        """
        Initialize all services in proper dependency order
        
        Returns:
            True if all services initialized successfully
        """
        try:
            self.logger.info("Starting service initialization...")
            
            # Determine initialization order based on dependencies
            init_order = self._calculate_initialization_order()
            
            # Initialize services in order
            for service_name in init_order:
                if not self._initialize_service(service_name):
                    self.logger.error(f"Failed to initialize service: {service_name}")
                    # Attempt to shutdown already initialized services
                    self._shutdown_initialized_services()
                    return False
                
                self._initialization_order.append(service_name)
            
            self._initialized = True
            self.logger.info("All services initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Service initialization failed: {e}")
            self.logger.error(traceback.format_exc())
            self._shutdown_initialized_services()
            return False
    
    def shutdown_all_services(self) -> bool:
        """
        Shutdown all services in reverse initialization order
        
        Returns:
            True if all services shutdown successfully
        """
        try:
            self.logger.info("Starting service shutdown...")
            
            success = True
            
            # Shutdown in reverse order
            for service_name in reversed(self._initialization_order):
                if not self._shutdown_service(service_name):
                    self.logger.error(f"Failed to shutdown service: {service_name}")
                    success = False
            
            # Clear state
            self.services.clear()
            self._initialization_order.clear()
            self._initialized = False
            self._shutdown = True
            
            if success:
                self.logger.info("All services shutdown successfully")
            else:
                self.logger.warning("Some services failed to shutdown properly")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Service shutdown failed: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def get_service(self, service_name: str) -> Optional[BaseService]:
        """
        Get a service by name
        
        Args:
            service_name: Name of the service
            
        Returns:
            Service instance or None
        """
        return self.services.get(service_name)
    
    def health_check_all_services(self) -> Dict[str, Any]:
        """
        Perform health check on all services
        
        Returns:
            Dictionary with overall health status
        """
        try:
            self._last_health_check = datetime.now()
            
            overall_status = 'healthy'
            service_health = {}
            issues = []
            
            # Check each service
            for service_name, service in self.services.items():
                try:
                    health = service.health_check()
                    service_health[service_name] = health
                    
                    if health.get('status') != 'healthy':
                        overall_status = 'degraded'
                        if health.get('status') == 'unhealthy':
                            issues.append(f"Service {service_name} is unhealthy")
                        else:
                            issues.append(f"Service {service_name} is degraded")
                            
                except Exception as e:
                    service_health[service_name] = {
                        'status': 'unhealthy',
                        'error': str(e)
                    }
                    overall_status = 'degraded'
                    issues.append(f"Health check failed for {service_name}")
            
            return {
                'status': overall_status,
                'services': service_health,
                'total_services': len(self.services),
                'initialized_services': len([s for s in self.services.values() if s.is_initialized()]),
                'issues': issues,
                'last_check': self._last_health_check.isoformat(),
                'manager_initialized': self._initialized
            }
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def update_service_configs(self, new_config: Dict[str, Any]) -> bool:
        """
        Update configuration for all services
        
        Args:
            new_config: New configuration dictionary
            
        Returns:
            True if all services updated successfully
        """
        try:
            self.config.update(new_config)
            
            success = True
            for service_name, service in self.services.items():
                try:
                    service.update_config(new_config)
                    self.logger.info(f"Updated configuration for service: {service_name}")
                except Exception as e:
                    self.logger.error(f"Failed to update config for {service_name}: {e}")
                    success = False
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update service configurations: {e}")
            return False
    
    def restart_service(self, service_name: str) -> bool:
        """
        Restart a specific service
        
        Args:
            service_name: Name of the service to restart
            
        Returns:
            True if service restarted successfully
        """
        try:
            if service_name not in self.services:
                self.logger.error(f"Service not found: {service_name}")
                return False
            
            self.logger.info(f"Restarting service: {service_name}")
            
            # Shutdown service
            if not self._shutdown_service(service_name):
                self.logger.warning(f"Service shutdown failed for {service_name}, continuing with restart")
            
            # Remove from services
            if service_name in self.services:
                del self.services[service_name]
            
            # Remove from initialization order
            if service_name in self._initialization_order:
                self._initialization_order.remove(service_name)
            
            # Reinitialize service
            if self._initialize_service(service_name):
                self._initialization_order.append(service_name)
                self.logger.info(f"Service restarted successfully: {service_name}")
                return True
            else:
                self.logger.error(f"Failed to restart service: {service_name}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error restarting service {service_name}: {e}")
            return False
    
    def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about all services
        
        Returns:
            Dictionary with service information
        """
        service_info = {}
        
        for service_name, service in self.services.items():
            try:
                info = service.get_service_info()
                service_info[service_name] = info
            except Exception as e:
                service_info[service_name] = {
                    'error': f'Failed to get info: {str(e)}'
                }
        
        return {
            'services': service_info,
            'total_services': len(self.services),
            'manager_initialized': self._initialized,
            'initialization_order': self._initialization_order,
            'last_health_check': self._last_health_check.isoformat() if self._last_health_check else None
        }
    
    def _calculate_initialization_order(self) -> List[str]:
        """
        Calculate service initialization order based on dependencies
        
        Returns:
            List of service names in initialization order
        """
        # Simple dependency resolution - initialize independent services first
        independent_services = []
        dependent_services = []
        
        for service_name in self.service_classes.keys():
            if service_name in self.service_dependencies:
                dependent_services.append(service_name)
            else:
                independent_services.append(service_name)
        
        # Return independent services first, then dependent services
        return independent_services + dependent_services
    
    def _initialize_service(self, service_name: str) -> bool:
        """
        Initialize a single service
        
        Args:
            service_name: Name of the service to initialize
            
        Returns:
            True if service initialized successfully
        """
        try:
            if service_name not in self.service_classes:
                raise ServiceInitializationError(f"Unknown service: {service_name}")
            
            # Check dependencies
            dependencies = self.service_dependencies.get(service_name, [])
            for dep in dependencies:
                if dep not in self.services or not self.services[dep].is_initialized():
                    raise ServiceInitializationError(f"Dependency {dep} not available for {service_name}")
            
            # Create service instance
            service_class = self.service_classes[service_name]
            service = service_class(self.config, self.logger)
            
            # Initialize service
            if not service.initialize():
                raise ServiceInitializationError(f"Service {service_name} initialization returned False")
            
            # Store service
            self.services[service_name] = service
            
            self.logger.info(f"Service initialized successfully: {service_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize service {service_name}: {e}")
            return False
    
    def _shutdown_service(self, service_name: str) -> bool:
        """
        Shutdown a single service
        
        Args:
            service_name: Name of the service to shutdown
            
        Returns:
            True if service shutdown successfully
        """
        try:
            if service_name not in self.services:
                return True  # Already shutdown
            
            service = self.services[service_name]
            
            if not service.shutdown():
                self.logger.warning(f"Service {service_name} shutdown returned False")
                return False
            
            self.logger.info(f"Service shutdown successfully: {service_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to shutdown service {service_name}: {e}")
            return False
    
    def _shutdown_initialized_services(self) -> None:
        """Shutdown all initialized services in reverse order"""
        for service_name in reversed(self._initialization_order):
            try:
                self._shutdown_service(service_name)
            except Exception as e:
                self.logger.error(f"Error during emergency shutdown of {service_name}: {e}")
        
        self.services.clear()
        self._initialization_order.clear()
    
    def is_initialized(self) -> bool:
        """Check if service manager is initialized"""
        return self._initialized and not self._shutdown
    
    def is_healthy(self) -> bool:
        """Check if all services are healthy"""
        try:
            health = self.health_check_all_services()
            return health.get('status') == 'healthy'
        except Exception:
            return False
