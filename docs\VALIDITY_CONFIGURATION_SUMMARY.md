# VPN SKU Validity Configuration Summary

## 🎯 **How Validity (有效期) is Ensured for Each var_sku**

The system now supports **two methods** to determine the validity days for each VPN SKU:

### 1. **Configuration File Method (Recommended)**
- **Location**: `configs/services/vpn_sku_tags.json`
- **Format**: Each SKU can have explicit `validity_days` specified
- **Priority**: Highest priority - checked first

### 2. **Regex Extraction Method (Fallback)**
- **Logic**: Extracts number from SKU name using regex `_(\d+)$`
- **Examples**: `my_30` → 30 days, `sg_highspeed_60` → 60 days
- **Default**: 30 days if no number found

## 📋 **Current Validity Configuration Status**

### ✅ **Fully Configured (Malaysia Products)**
These SKUs have explicit validity configuration:

```json
{
  "my_": {"tags": [...], "validity_days": 30},
  "my_15": {"tags": [...], "validity_days": 15},
  "my_30": {"tags": [...], "validity_days": 30},
  "my_60": {"tags": [...], "validity_days": 60},
  "my_90": {"tags": [...], "validity_days": 90},
  
  "my_standard": {"tags": [...], "validity_days": 30},
  "my_standard_15": {"tags": [...], "validity_days": 15},
  "my_standard_30": {"tags": [...], "validity_days": 30},
  "my_standard_60": {"tags": [...], "validity_days": 60},
  
  "my_premium": {"tags": [...], "validity_days": 30},
  "my_premium_15": {"tags": [...], "validity_days": 15},
  "my_premium_30": {"tags": [...], "validity_days": 30},
  "my_premium_60": {"tags": [...], "validity_days": 60},
  "my_premium_90": {"tags": [...], "validity_days": 90},
  
  "my_highspeed": {"tags": [...], "validity_days": 30},
  "my_highspeed_15": {"tags": [...], "validity_days": 15},
  "my_highspeed_30": {"tags": [...], "validity_days": 30},
  "my_highspeed_60": {"tags": [...], "validity_days": 60},
  "my_highspeed_90": {"tags": [...], "validity_days": 90},
  
  "my_highspeed_premium": {"tags": [...], "validity_days": 30},
  "my_highspeed_premium_30": {"tags": [...], "validity_days": 30},
  "my_highspeed_premium_60": {"tags": [...], "validity_days": 60},
  "my_highspeed_premium_90": {"tags": [...], "validity_days": 90}
}
```

### ⚠️ **Using Fallback (Singapore Products)**
These SKUs use regex extraction or default values:

- `sg_`, `sg_highspeed`, `sg_premium`, `sg_business` → 30 days (default)
- `sg_15`, `sg_highspeed_15`, `sg_premium_15` → 15 days (regex)
- `sg_30`, `sg_highspeed_30`, `sg_premium_30`, `sg_business_30` → 30 days (regex)
- `sg_60`, `sg_highspeed_60`, `sg_premium_60`, `sg_business_60` → 60 days (regex)
- `sg_90`, `sg_premium_90`, `sg_business_90` → 90 days (regex)

## 🔧 **How to Configure Validity for New SKUs**

### Method 1: Using Configuration Manager Tool
```bash
# Add SKU with explicit validity
python plugins/vpn/tools/sku_tags_config_manager.py add \
  --category "thailand_basic" \
  --sku "th_30" \
  --tags "thailand,basic,new_provider" \
  --validity 30

# Add SKU without explicit validity (will use regex/default)
python plugins/vpn/tools/sku_tags_config_manager.py add \
  --category "thailand_basic" \
  --sku "th_premium" \
  --tags "thailand,premium,new_provider"
```

### Method 2: Direct Configuration File Edit
```json
{
  "sku_server_tags_mapping": {
    "new_category": {
      "new_sku_30": {
        "tags": ["location", "provider", "tier"],
        "validity_days": 30
      },
      "new_sku_without_validity": {
        "tags": ["location", "provider", "tier"]
        // No validity_days - will use regex/default
      }
    }
  }
}
```

## 📊 **Validity Resolution Priority**

1. **Explicit Configuration** (Highest Priority)
   - Looks for `validity_days` in configuration file
   - Exact SKU match first, then pattern matching

2. **Regex Extraction** (Medium Priority)
   - Extracts number from SKU name: `_(\d+)$`
   - Examples: `my_45` → 45 days, `sg_premium_120` → 120 days

3. **Default Fallback** (Lowest Priority)
   - Returns 30 days if no other method works

## 🧪 **Testing Validity Configuration**

### Test All SKUs
```bash
python plugins/vpn/test_validity_configuration.py
```

### Test Specific SKUs
```bash
python plugins/vpn/tools/sku_tags_config_manager.py test \
  --test-skus "my_30,sg_premium_60,new_sku_45"
```

### Validate Configuration
```bash
python plugins/vpn/tools/sku_tags_config_manager.py validate
```

## 📈 **Current Test Results**

From the latest test run:

### ✅ **Working Correctly**
- Malaysia products: All have explicit validity configuration
- Regex extraction: Works for SKUs with numbers (e.g., `sg_15` → 15 days)
- Default fallback: Works for SKUs without numbers (e.g., `sg_premium` → 30 days)
- Unknown SKUs: Properly extract from name (e.g., `test_45` → 45 days)

### ⚠️ **Edge Cases**
- Multiple numbers in SKU: Takes the first matching pattern from config
- Example: `my_15_premium_30` → 15 days (matches `my_15` pattern first)

## 🔄 **Migration Recommendations**

### For Complete Control
Update Singapore SKUs to use explicit validity configuration:

```json
"singapore_basic": {
  "sg_": {"tags": ["singapore", "digitalocean", "basic"], "validity_days": 30},
  "sg_15": {"tags": ["singapore", "digitalocean", "basic"], "validity_days": 15},
  "sg_30": {"tags": ["singapore", "digitalocean", "basic"], "validity_days": 30},
  "sg_60": {"tags": ["singapore", "digitalocean", "basic"], "validity_days": 60}
}
```

### For Flexibility
Keep current hybrid approach:
- Explicit configuration for complex cases
- Regex extraction for simple numbered SKUs
- Default fallback for edge cases

## 🛠 **Code Integration**

### In VPN Strategies
```python
# Get validity days for a SKU
validity_days = self._extract_days_from_sku(product_sku)

# This method now:
# 1. Checks configuration file first
# 2. Falls back to regex extraction
# 3. Uses 30-day default if all else fails
```

### In Order Processing
```python
from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory

# Get validity directly from factory
validity_days = VPNStrategyFactory.get_validity_days_for_sku(product_sku)
```

## 📝 **Summary**

The system now provides **flexible validity management** with:

1. **Explicit Configuration**: For precise control over validity periods
2. **Automatic Extraction**: For standard numbered SKUs
3. **Robust Fallbacks**: Ensures system always works
4. **Easy Management**: Tools for adding/testing configurations
5. **Backward Compatibility**: Existing SKUs continue to work

Each var_sku's validity is determined through this multi-layered approach, ensuring reliable and configurable VPN account expiration dates.
