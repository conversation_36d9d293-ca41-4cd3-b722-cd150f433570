{"telcos": {"digi": {"id": "digi", "name": "<PERSON><PERSON>", "description": "Digi Telecommunications Malaysia", "enabled": true, "plans": {"unlimited": {"id": "unlimited", "name": "Unlimited", "description": "Unlimited data plan with PUBG Mobile host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=m.pubgmobile.com&path=%2F%3Fed%3D2048#DIGI-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "🚀 DIGI UNLIMITED CONFIG 🚀\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Server: {server_id}\n⏰ Validity: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🎮 Optimized for PUBG Mobile Gaming\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "basic": {"id": "basic", "name": "Basic", "description": "Basic data plan with Speedtest host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=speedtest.net&path=%2F%3Fed%3D2048#DIGI-BASIC-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️\n\n📧 Email: {email}\n👤 Shopee User: {shopee_username}\n🖥️ Server: {server_number}\n📅 Created: {created_datetime}\n⏰ Expires: {expired_date_formatted}\n📡 Telco: {telco}\n📋 Plan: {plan}\n⏳ Validity: {validity}\n🔑 Config ID: {config_id}\n\n🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "gaming": {"id": "gaming", "name": "Gaming", "description": "Gaming optimized plan", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=mobile-legends.com&path=%2F%3Fed%3D2048#DIGI-GAMING-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "🎮 DIGI GAMING CONFIG 🎮\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓\n🖥️ Server: {server_id}\n⚡ Duration: {days} Day(s)\n📶 Provider: {telco} {plan}\n🎯 Gamer: {username}\n📅 Start: {created_date}\n⏰ End: {expired_date}\n🏆 Optimized for Mobile Legends\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "social": {"id": "social", "name": "Social", "description": "Social media optimized plan with Facebook host", "template": "vless://{uuid}@m.facebook.com.{server}:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=m.facebook.com#DigiSocial3Mbps_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "📱 DIGI SOCIAL CONFIG 📱\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Server: {server_id}\n⏰ Validity: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n📲 Optimized for Social Media\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster": {"id": "booster", "name": "3Mbps Booster", "description": "High-speed 3Mbps booster plan", "template": "vless://{uuid}@**************:80?security=none&encryption=none&type=ws&headerType=none&path=/&host={server}#DigiUnlimitedPlan_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "⚡ DIGI 3MBPS BOOSTER ⚡\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Server: {server_id}\n⏰ Validity: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🚀 High Speed 3Mbps Connection\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "maxis": {"id": "maxis", "name": "<PERSON><PERSON>", "description": "Maxis Communications Malaysia", "enabled": true, "plans": {"hotlink": {"id": "hotlink", "name": "Hotlink", "description": "Hotlink prepaid plan with Speedtest host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.speedtest.net&path=%2F%3Fed%3D2048#MAXIS-HOTLINK-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "🔥 MAXIS HOTLINK CONFIG 🔥\n═══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🚀 High Speed Prepaid Connection\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "postpaid": {"id": "postpaid", "name": "Postpaid", "description": "Maxis postpaid plan with Fast.com host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=fast.com&path=%2F%3Fed%3D2048#MAXIS-POSTPAID-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "💎 MAXIS POSTPAID PREMIUM 💎\n\n📧 Account: {email}\n👤 User: {shopee_username}\n🖥️ Server: {server_name}\n📅 Activated: {created_date_formatted}\n⏰ Valid Until: {expired_date_formatted}\n📡 Network: {telco} {plan}\n⏳ Duration: {validity}\n🆔 Short ID: {short_uuid}\n\n✨ Premium Postpaid Experience ✨", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "zerolution": {"id": "zerolution", "name": "Zerolution", "description": "Maxis Zerolution plan", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.instagram.com&path=%2F%3Fed%3D2048#MAXIS-ZERO-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "sabah_freeze": {"id": "sabah_freeze", "name": "Sabah Freeze", "description": "Maxis Sabah region specialized plan", "template": "vless://{uuid}@cdn.opensignal.com:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=cdn.opensignal.com.{server}#MaxisSabah_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🌴 MAXIS SABAH FREEZE 🌴\n═══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🏝️ Optimized for Sabah Region\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "celcom": {"id": "celcom", "name": "Celcom", "description": "Celcom Axiata Malaysia", "enabled": true, "plans": {"xpax": {"id": "xpax", "name": "Xpax", "description": "Xpax prepaid plan with Netflix host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.netflix.com&path=%2F%3Fed%3D2048#CELCOM-XPAX-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "🎬 CELCOM XPAX CONFIG 🎬\n┌─────────────────────────────────────────────────┐\n│ 📱 Server: {server_id}                          │\n│ ⏰ Duration: {days} Day(s)                      │\n│ 📡 Provider: {telco} {plan}                     │\n│ 👤 User: {username}                             │\n│ 📅 Created: {created_date}                      │\n│ ⏳ Expires: {expired_date}                      │\n│ 🎥 Optimized for Netflix Streaming              │\n└─────────────────────────────────────────────────┘", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "first": {"id": "first", "name": "First", "description": "Celcom First postpaid with YouTube host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.youtube.com&path=%2F%3Fed%3D2048#CELCOM-FIRST-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "mega": {"id": "mega", "name": "Mega", "description": "Celcom Mega plan", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.tiktok.com&path=%2F%3Fed%3D2048#CELCOM-MEGA-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster1": {"id": "booster1", "name": "Booster 1", "description": "Celcom Booster 1 with Speedtest optimization", "template": "vless://{uuid}@www.speedtest.net:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=www.speedtest.net.{server}#Booster1_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🚀 CELCOM BOOSTER 1 🚀\n┌─────────────────────────────────────────────────┐\n│ 📱 Server: {server_id}                          │\n│ ⏰ Duration: {days} Day(s)                      │\n│ 📡 Provider: {telco} {plan}                     │\n│ 👤 User: {username}                             │\n│ 📅 Created: {created_date}                      │\n│ ⏳ Expires: {expired_date}                      │\n│ ⚡ High Speed Booster Plan                      │\n└─────────────────────────────────────────────────┘", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster2": {"id": "booster2", "name": "Booster 2", "description": "Celcom Booster 2 with enhanced speed", "template": "vless://{uuid}@**************:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=www.speedtest.net.{server}#Booster2_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "⚡ CELCOM BOOSTER 2 ⚡\n┌─────────────────────────────────────────────────┐\n│ 📱 Server: {server_id}                          │\n│ ⏰ Duration: {days} Day(s)                      │\n│ 📡 Provider: {telco} {plan}                     │\n│ 👤 User: {username}                             │\n│ 📅 Created: {created_date}                      │\n│ ⏳ Expires: {expired_date}                      │\n│ 🔥 Enhanced Speed Booster                       │\n└─────────────────────────────────────────────────┘", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "umobile": {"id": "umobile", "name": "U Mobile", "description": "U Mobile Malaysia", "enabled": true, "plans": {"prepaid": {"id": "prepaid", "name": "Prepaid", "description": "U Mobile prepaid plan", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.facebook.com&path=%2F%3Fed%3D2048#UMOBILE-PREPAID-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "postpaid": {"id": "postpaid", "name": "Postpaid", "description": "U Mobile postpaid plan", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.whatsapp.com&path=%2F%3Fed%3D2048#UMOBILE-POSTPAID-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "unlimited": {"id": "unlimited", "name": "Unlimited", "description": "U Mobile unlimited plan", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.twitter.com&path=%2F%3Fed%3D2048#UMOBILE-UNLIMITED-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "5g_ready": {"id": "5g_ready", "name": "5G Ready", "description": "U Mobile 5G Ready plan with enhanced connectivity", "template": "vless://{uuid}@cityofpalacios.org:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=cityofpalacios.org.{server}#UNP3_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "📶 U MOBILE 5G READY 📶\n══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🚀 5G Ready Technology\n══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "funz": {"id": "funz", "name": "Funz", "description": "U Mobile Funz entertainment plan", "template": "vless://{uuid}@***********:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=cityofpalacios.org.{server}#UNP4_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🎉 U MOBILE FUNZ 🎉\n══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🎮 Entertainment Optimized\n══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "yes": {"id": "yes", "name": "Yes", "description": "Yes 4G Malaysia", "enabled": true, "plans": {"no_plan": {"id": "no_plan", "name": "No Plan", "description": "Yes no contract plan", "template": "vless://{uuid}@**************:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=tap-database.who.int.{server}#Yes_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "✅ YES NO PLAN CONFIG ✅\n══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🚫 No Contract Required\n══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "yoodo": {"id": "yoodo", "name": "<PERSON><PERSON><PERSON>", "description": "Yoodo Malaysia Digital Telco", "enabled": true, "plans": {"booster1": {"id": "booster1", "name": "Booster 1", "description": "Yoodo Booster 1 with Speedtest optimization", "template": "vless://{uuid}@*************:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=www.speedtest.net.{server}#Booster2_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🚀 YOODO BOOSTER 1 🚀\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n💫 Digital Telco Experience\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster2": {"id": "booster2", "name": "Booster 2", "description": "Yoodo Booster 2 with enhanced performance", "template": "vless://{uuid}@www.speedtest.net:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=www.speedtest.net.{server}#Booster2_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "⚡ YOODO BOOSTER 2 ⚡\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🔥 Enhanced Performance\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "pubg": {"id": "pubg", "name": "PUBG", "description": "Yoodo PUBG gaming optimized plan", "template": "vless://{uuid}@m.pubgmobile.com.{server}:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=m.pubgmobile.com#YoodoPUBG_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🎮 YOODO PUBG GAMING 🎮\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🏆 PUBG Mobile Optimized\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "mobile_legend": {"id": "mobile_legend", "name": "Mobile Legend", "description": "Yoodo Mobile Legends gaming plan", "template": "vless://{uuid}@m.mobilelegends.com.{server}:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=m.mobilelegends.com#YoodoML_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🏆 YOODO MOBILE LEGENDS 🏆\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🎯 Mobile Legends Optimized\n▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "unifi": {"id": "unifi", "name": "Unifi", "description": "Unifi Malaysia", "enabled": true, "plans": {"bebas": {"id": "bebas", "name": "<PERSON><PERSON>", "description": "Unifi Bebas unlimited plan", "template": "vless://{uuid}@www.speedtest.net:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=www.speedtest.net.{server}#UnifiBebas_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🔓 UNIFI BEBAS 🔓\n══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🆓 Unlimited Freedom\n══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "5g_wow": {"id": "5g_wow", "name": "5G Wow", "description": "Unifi 5G Wow plan with cutting-edge technology", "template": "vless://{uuid}@opensignal.com.{server}:80?security=none&encryption=none&type=ws&headerType=none&path=/#Unifi5G_{identity}", "variables": {"uuid": "Client UUID", "server": "Server domain", "identity": "User identity", "username": "Username", "days": "Validity days"}, "info_message_template": "🌟 UNIFI 5G WOW 🌟\n══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n📶 5G Technology Experience\n══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}}