#!/bin/bash

# Set default port if not provided
export PORT=${PORT:-8000}

# Run in development mode if DEV=1
if [ "$DEV" = "1" ]; then
    echo "Starting in development mode with auto-reload"
    uvicorn api:app --host 0.0.0.0 --port $PORT --reload
else
    echo "Starting in production mode"
    gunicorn --workers 4 --worker-class uvicorn.workers.UvicornWorker \
        --bind 0.0.0.0:$PORT api:app
fi