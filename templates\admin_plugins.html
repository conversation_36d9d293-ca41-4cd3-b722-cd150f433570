{% extends "base.html" %}

{% block title %}Plugin Manager{% endblock %}

{% block header %}Plugin Manager{% endblock %}

{% block extra_head %}
<style>
    .plugin-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .plugin-card.enabled {
        border-left-color: #10b981;
        background-color: #f0fdf4;
    }
    
    .plugin-card.disabled {
        border-left-color: #ef4444;
        background-color: #fef2f2;
    }
    
    .plugin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }
    
    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }
    
    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    
    input:checked + .slider {
        background-color: #10b981;
    }
    
    input:checked + .slider:before {
        transform: translateX(26px);
    }
    
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .status-enabled {
        background-color: #dcfce7;
        color: #166534;
    }
    
    .status-disabled {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .plugin-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 1rem;
    }
    
    .steam-icon { background: linear-gradient(135deg, #1b2838, #2a475e); }
    .netflix-icon { background: linear-gradient(135deg, #e50914, #b20710); }
    .vpn-icon { background: linear-gradient(135deg, #4f46e5, #3730a3); }
    .canva-icon { background: linear-gradient(135deg, #00c4cc, #0099a3); }
    .shopee-icon { background: linear-gradient(135deg, #ee4d2d, #c73e1d); }
    .default-icon { background: linear-gradient(135deg, #6b7280, #4b5563); }
    
    .nav-tabs {
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 2rem;
    }
    
    .nav-tab {
        padding: 0.75rem 1.5rem;
        border-bottom: 2px solid transparent;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-block;
    }
    
    .nav-tab.active {
        color: #3b82f6;
        border-bottom-color: #3b82f6;
    }
    
    .nav-tab:hover {
        color: #3b82f6;
    }
</style>
{% endblock %}

{% block content %}
<div x-data="pluginManager()" x-init="init()">
    <!-- Action Buttons -->
    <div class="flex justify-end items-center space-x-4 mb-6">
        <button @click="refreshPlugins()" 
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
            <i class="fas fa-sync-alt mr-2"></i>Refresh
        </button>
        <button @click="discoverPlugins()" 
                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
            <i class="fas fa-search mr-2"></i>Discover
        </button>
    </div>
    
    <!-- Navigation Tabs -->
    <div class="nav-tabs">
        <div class="flex space-x-0">
            <div class="nav-tab" :class="{ 'active': activeTab === 'overview' }" @click="activeTab = 'overview'">
                <i class="fas fa-chart-bar mr-2"></i>Overview
            </div>
            <div class="nav-tab" :class="{ 'active': activeTab === 'plugins' }" @click="activeTab = 'plugins'">
                <i class="fas fa-puzzle-piece mr-2"></i>Plugins
            </div>
            <div class="nav-tab" :class="{ 'active': activeTab === 'configuration' }" @click="activeTab = 'configuration'">
                <i class="fas fa-cog mr-2"></i>Configuration
            </div>
        </div>
    </div>

    <!-- Overview Tab -->
    <div x-show="activeTab === 'overview'" class="space-y-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-puzzle-piece text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-gray-900" x-text="Object.keys(plugins).length"></h3>
                        <p class="text-sm text-gray-500">Total Plugins</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-check text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-gray-900" x-text="getEnabledCount()"></h3>
                        <p class="text-sm text-gray-500">Enabled</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-times text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-gray-900" x-text="getDisabledCount()"></h3>
                        <p class="text-sm text-gray-500">Disabled</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-heart text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-green-600">Good</h3>
                        <p class="text-sm text-gray-500">System Health</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plugins Tab -->
    <div x-show="activeTab === 'plugins'" class="space-y-6">
        <!-- Loading State -->
        <div x-show="loading" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-600">Loading plugins...</p>
        </div>

        <!-- Error State -->
        <div x-show="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                <div>
                    <h3 class="text-red-800 font-medium">Error Loading Plugins</h3>
                    <p class="text-red-600 text-sm" x-text="error"></p>
                </div>
            </div>
        </div>

        <!-- Plugins Grid -->
        <div x-show="!loading && !error">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Available Plugins</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <template x-for="(plugin, name) in plugins" :key="name">
                    <div class="plugin-card bg-white rounded-lg shadow-md p-6 transition-all duration-300"
                         :class="plugin.enabled ? 'enabled' : 'disabled'">
                        
                        <!-- Plugin Header -->
                        <div class="flex items-center mb-4">
                            <div class="plugin-icon" :class="getPluginIconClass(name)">
                                <i :class="getPluginIcon(name)"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900" x-text="plugin.name || name"></h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="status-badge" 
                                          :class="plugin.enabled ? 'status-enabled' : 'status-disabled'">
                                        <i :class="plugin.enabled ? 'fas fa-check-circle' : 'fas fa-times-circle'" 
                                           class="mr-1"></i>
                                        <span x-text="plugin.enabled ? 'Enabled' : 'Disabled'"></span>
                                    </span>
                                    <span class="text-xs text-gray-500" x-text="'v' + (plugin.version || '1.0.0')"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Plugin Description -->
                        <p class="text-gray-600 text-sm mb-4" x-text="plugin.description || getPluginDescription(name)"></p>

                        <!-- Plugin Controls -->
                        <div class="flex items-center justify-between">
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       :checked="plugin.enabled"
                                       @change="togglePlugin(name, $event.target.checked)">
                                <span class="slider"></span>
                            </label>
                            
                            <div class="flex space-x-2">
                                <button @click="showPluginConfig(name)" 
                                        class="text-blue-500 hover:text-blue-600 text-sm">
                                    <i class="fas fa-cog mr-1"></i>Config
                                </button>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Configuration Tab -->
    <div x-show="activeTab === 'configuration'" class="space-y-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Plugin Configuration</h2>
            
            <div x-show="selectedPlugin">
                <h3 class="text-lg font-medium text-gray-900 mb-4" x-text="selectedPlugin ? selectedPlugin.name + ' Configuration' : ''"></h3>
                
                <template x-if="selectedPlugin && selectedPlugin.config">
                    <div>
                        <pre class="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto" 
                             x-text="JSON.stringify(selectedPlugin.config, null, 2)"></pre>
                        
                        <div class="mt-4 flex space-x-3">
                            <button @click="savePluginConfig()" 
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-save mr-2"></i>Save Configuration
                            </button>
                            <button @click="selectedPlugin = null" 
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </button>
                        </div>
                    </div>
                </template>
                
                <div x-show="!selectedPlugin" class="text-center py-8 text-gray-500">
                    <i class="fas fa-cog text-4xl mb-4"></i>
                    <p>Select a plugin from the Plugins tab to configure it</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div x-show="message" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-2"
         class="fixed bottom-4 right-4 z-50">
        <div class="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
            <i class="fas fa-check-circle mr-2"></i>
            <span x-text="message"></span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function pluginManager() {
        return {
            plugins: {},
            loading: false,
            error: null,
            message: null,
            activeTab: 'overview',
            selectedPlugin: null,

            async init() {
                await this.loadPlugins();
            },

            async loadPlugins() {
                this.loading = true;
                this.error = null;
                
                try {
                    const response = await fetch('/api/plugins');
                    if (!response.ok) {
                        throw new Error('Failed to load plugins');
                    }
                    
                    this.plugins = await response.json();
                } catch (error) {
                    this.error = error.message;
                } finally {
                    this.loading = false;
                }
            },

            async refreshPlugins() {
                await this.loadPlugins();
                this.showMessage('Plugins refreshed successfully');
            },

            async discoverPlugins() {
                try {
                    const response = await fetch('/api/plugins/discover', { method: 'POST' });
                    const result = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to discover plugins');
                    }
                    
                    await this.loadPlugins();
                    this.showMessage('Plugin discovery completed');
                } catch (error) {
                    this.error = error.message;
                }
            },

            async togglePlugin(pluginName, enabled) {
                try {
                    const endpoint = enabled ? 'enable' : 'disable';
                    const response = await fetch(`/api/plugins/${pluginName}/${endpoint}`, {
                        method: 'POST'
                    });

                    const result = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to toggle plugin');
                    }

                    // Update local state
                    this.plugins[pluginName].enabled = enabled;
                    this.showMessage(result.message);
                    
                } catch (error) {
                    this.error = error.message;
                    // Revert the toggle
                    this.plugins[pluginName].enabled = !enabled;
                }
            },

            async showPluginConfig(pluginName) {
                try {
                    const response = await fetch(`/api/plugins/${pluginName}/config`);
                    const result = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to load plugin config');
                    }
                    
                    this.selectedPlugin = {
                        name: pluginName,
                        config: result.config,
                        schema: result.schema
                    };
                    this.activeTab = 'configuration';
                    
                } catch (error) {
                    this.error = error.message;
                }
            },

            async savePluginConfig() {
                if (!this.selectedPlugin) return;
                
                try {
                    const response = await fetch(`/api/plugins/${this.selectedPlugin.name}/config`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(this.selectedPlugin.config)
                    });

                    const result = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to save config');
                    }

                    this.showMessage(result.message);
                    
                } catch (error) {
                    this.error = error.message;
                }
            },

            getEnabledCount() {
                return Object.values(this.plugins).filter(p => p.enabled).length;
            },

            getDisabledCount() {
                return Object.values(this.plugins).filter(p => !p.enabled).length;
            },

            showMessage(msg) {
                this.message = msg;
                setTimeout(() => {
                    this.message = null;
                }, 3000);
            },

            getPluginIcon(name) {
                const icons = {
                    'steam': 'fab fa-steam',
                    'netflix': 'fas fa-film',
                    'vpn': 'fas fa-shield-alt',
                    'canva': 'fas fa-palette',
                    'shopee': 'fas fa-shopping-cart'
                };
                return icons[name.toLowerCase()] || 'fas fa-puzzle-piece';
            },

            getPluginIconClass(name) {
                const classes = {
                    'steam': 'steam-icon',
                    'netflix': 'netflix-icon',
                    'vpn': 'vpn-icon',
                    'canva': 'canva-icon',
                    'shopee': 'shopee-icon'
                };
                return classes[name.toLowerCase()] || 'default-icon';
            },

            getPluginDescription(name) {
                const descriptions = {
                    'steam': 'Manage Steam game codes and redemption',
                    'netflix': 'Handle Netflix account management',
                    'vpn': 'VPN server and client management',
                    'canva': 'Canva design automation tools',
                    'shopee': 'Shopee marketplace integration'
                };
                return descriptions[name.toLowerCase()] || 'Plugin for SteamCodeTool';
            }
        };
    }
</script>
{% endblock %}
