#!/usr/bin/env python3
"""
Error Handling Validation Script

This script provides comprehensive validation of error handling mechanisms
including graceful degradation, recovery procedures, and user feedback quality.
"""

import sys
import os
import json
import tempfile
import shutil
import threading
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add plugin directory to Python path
plugin_dir = Path(__file__).parent
sys.path.insert(0, str(plugin_dir.parent.parent))

from plugins.openai_plus_redeem.models.chatgpt_account import ChatGPTAccount, AccountStatus
from plugins.openai_plus_redeem.services.chatgpt_account_service import ChatGPTAccountService
from plugins.openai_plus_redeem.services.order_redemption_service import OrderRedemptionService
from plugins.openai_plus_redeem.models.utils import load_json_data, save_json_data, backup_data_file


class ErrorHandlingValidator:
    """Error handling validation utility"""
    
    def __init__(self):
        self.test_dir = None
        self.original_get_data_file_path = None
        self.error_scenarios = []
        self.recovery_tests = []
        self.validation_results = {}
    
    def setup_test_environment(self):
        """Setup test environment with temporary directory"""
        import tempfile
        from unittest.mock import patch
        
        self.test_dir = tempfile.mkdtemp(prefix='opr_error_test_')
        print(f"📁 Created test directory: {self.test_dir}")
        
        # Mock data file paths to use test directory
        def mock_get_data_file_path(plugin_name, filename):
            return str(Path(self.test_dir) / filename)
        
        # Store original function
        import plugins.openai_plus_redeem.models.utils as utils_module
        self.original_get_data_file_path = utils_module.get_data_file_path
        utils_module.get_data_file_path = mock_get_data_file_path
        
        # Create initial data files
        self._create_initial_data_files()
        
        return True
    
    def _create_initial_data_files(self):
        """Create initial data files for testing"""
        initial_data = {
            'accounts': [],
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat()
            }
        }
        
        files_to_create = [
            'chatgpt_accounts.json',
            'order_redemptions.json', 
            'email_verifications.json',
            'account_cooldowns.json'
        ]
        
        for filename in files_to_create:
            file_path = Path(self.test_dir) / filename
            with open(file_path, 'w') as f:
                if 'redemptions' in filename:
                    json.dump({'redemptions': [], 'metadata': initial_data['metadata']}, f)
                elif 'verifications' in filename:
                    json.dump({'verification_logs': []}, f)
                elif 'cooldowns' in filename:
                    json.dump({'cooldowns': [], 'metadata': initial_data['metadata']}, f)
                else:
                    json.dump(initial_data, f)
    
    def cleanup_test_environment(self):
        """Cleanup test environment"""
        if self.test_dir and Path(self.test_dir).exists():
            shutil.rmtree(self.test_dir)
            print(f"🗑️  Cleaned up test directory: {self.test_dir}")
        
        # Restore original function
        if self.original_get_data_file_path:
            import plugins.openai_plus_redeem.models.utils as utils_module
            utils_module.get_data_file_path = self.original_get_data_file_path
    
    def test_file_system_error_scenarios(self):
        """Test file system error scenarios"""
        print("\n💾 Testing File System Error Scenarios")
        print("-" * 50)
        
        # Mock logger to capture messages
        class TestLogger:
            def __init__(self):
                self.messages = []
            
            def info(self, msg): 
                self.messages.append(('INFO', msg))
                print(f"  INFO: {msg}")
            
            def warning(self, msg): 
                self.messages.append(('WARNING', msg))
                print(f"  WARNING: {msg}")
            
            def error(self, msg): 
                self.messages.append(('ERROR', msg))
                print(f"  ERROR: {msg}")
            
            def debug(self, msg): 
                self.messages.append(('DEBUG', msg))
        
        logger = TestLogger()
        
        test_config = {
            'email_config': {
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }
        
        # Test 1: Corrupted data file handling
        print("  🧪 Testing corrupted data file handling...")
        
        # Corrupt the accounts file
        accounts_file = Path(self.test_dir) / 'chatgpt_accounts.json'
        with open(accounts_file, 'w') as f:
            f.write('{ invalid json content }')
        
        account_service = ChatGPTAccountService(test_config, logger)
        result = account_service.initialize()
        
        if result:
            print("    ✅ Service recovered from corrupted file")
            self.recovery_tests.append(('Corrupted File Recovery', True))
        else:
            print("    ❌ Service failed to handle corrupted file")
            self.error_scenarios.append(('Corrupted File Handling', 'Service failed to initialize'))
        
        # Test 2: Permission denied scenarios
        print("  🧪 Testing permission denied scenarios...")
        
        # Make directory read-only
        os.chmod(self.test_dir, 0o444)
        
        try:
            test_account = ChatGPTAccount(
                account_id='PERMISSION_TEST',
                email='<EMAIL>',
                password='password123',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            add_result = account_service.add_account(test_account)
            
            if not add_result['success'] and 'error' in add_result:
                print("    ✅ Permission errors handled gracefully")
                self.recovery_tests.append(('Permission Error Handling', True))
            else:
                print("    ❌ Permission errors not handled properly")
                self.error_scenarios.append(('Permission Error Handling', 'Errors not handled gracefully'))
        
        finally:
            # Restore permissions
            os.chmod(self.test_dir, 0o755)
        
        # Check error logging quality
        error_messages = [msg for level, msg in logger.messages if level == 'ERROR']
        if error_messages:
            print(f"    📝 Logged {len(error_messages)} error messages")
            for msg in error_messages[:3]:  # Show first 3
                print(f"      - {msg}")
        
        self.validation_results['file_system_errors'] = {
            'scenarios_tested': 2,
            'recovery_successful': len([t for t in self.recovery_tests if 'File' in t[0] or 'Permission' in t[0]]),
            'error_messages_logged': len(error_messages)
        }
    
    def test_invalid_data_handling(self):
        """Test invalid data handling"""
        print("\n📊 Testing Invalid Data Handling")
        print("-" * 50)
        
        class TestLogger:
            def __init__(self):
                self.messages = []
            def info(self, msg): self.messages.append(('INFO', msg))
            def warning(self, msg): self.messages.append(('WARNING', msg))
            def error(self, msg): self.messages.append(('ERROR', msg))
            def debug(self, msg): self.messages.append(('DEBUG', msg))
        
        logger = TestLogger()
        test_config = {
            'email_config': {
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }
        
        account_service = ChatGPTAccountService(test_config, logger)
        account_service.initialize()
        
        # Test various invalid data scenarios
        invalid_data_tests = [
            {
                'name': 'Empty Account ID',
                'account': ChatGPTAccount(
                    account_id='',  # Invalid
                    email='<EMAIL>',
                    password='password123',
                    max_concurrent_users=5,
                    current_users=0,
                    expiration_date=datetime.now() + timedelta(days=30),
                    status=AccountStatus.ACTIVE,
                    created_date=datetime.now()
                )
            },
            {
                'name': 'Invalid Email Format',
                'account': ChatGPTAccount(
                    account_id='INVALID_EMAIL_TEST',
                    email='not-an-email',  # Invalid
                    password='password123',
                    max_concurrent_users=5,
                    current_users=0,
                    expiration_date=datetime.now() + timedelta(days=30),
                    status=AccountStatus.ACTIVE,
                    created_date=datetime.now()
                )
            },
            {
                'name': 'Negative Max Users',
                'account': ChatGPTAccount(
                    account_id='NEGATIVE_USERS_TEST',
                    email='<EMAIL>',
                    password='password123',
                    max_concurrent_users=-1,  # Invalid
                    current_users=0,
                    expiration_date=datetime.now() + timedelta(days=30),
                    status=AccountStatus.ACTIVE,
                    created_date=datetime.now()
                )
            }
        ]
        
        validation_passed = 0
        
        for test_case in invalid_data_tests:
            print(f"  🧪 Testing {test_case['name']}...")
            
            result = account_service.add_account(test_case['account'])
            
            if not result['success'] and 'error' in result:
                print(f"    ✅ Invalid data rejected with error: {result['error'][:50]}...")
                validation_passed += 1
                
                # Check error message quality
                error_msg = result['error']
                if len(error_msg) > 10 and not any(word in error_msg.lower() for word in ['traceback', 'exception', 'internal']):
                    print("    ✅ Error message is user-friendly")
                else:
                    print("    ⚠️  Error message could be more user-friendly")
            else:
                print(f"    ❌ Invalid data was accepted")
                self.error_scenarios.append((test_case['name'], 'Invalid data was accepted'))
        
        self.validation_results['invalid_data_handling'] = {
            'tests_run': len(invalid_data_tests),
            'validation_passed': validation_passed,
            'success_rate': validation_passed / len(invalid_data_tests)
        }
    
    def test_concurrent_access_errors(self):
        """Test concurrent access error handling"""
        print("\n⚡ Testing Concurrent Access Error Handling")
        print("-" * 50)
        
        class TestLogger:
            def __init__(self):
                self.messages = []
            def info(self, msg): self.messages.append(('INFO', msg))
            def warning(self, msg): self.messages.append(('WARNING', msg))
            def error(self, msg): self.messages.append(('ERROR', msg))
            def debug(self, msg): self.messages.append(('DEBUG', msg))
        
        logger = TestLogger()
        test_config = {
            'email_config': {
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            }
        }
        
        account_service = ChatGPTAccountService(test_config, logger)
        account_service.initialize()
        
        # Concurrent access test
        def concurrent_worker(worker_id):
            account = ChatGPTAccount(
                account_id=f'CONCURRENT_ERROR_{worker_id}',
                email=f'concurrent{worker_id}@example.com',
                password=f'password{worker_id}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            try:
                result = account_service.add_account(account)
                return {'success': result['success'], 'worker_id': worker_id, 'error': None}
            except Exception as e:
                return {'success': False, 'worker_id': worker_id, 'error': str(e)}
        
        print("  🧪 Running concurrent operations...")
        
        # Run concurrent operations
        threads = []
        results = []
        
        def worker_wrapper(worker_id):
            result = concurrent_worker(worker_id)
            results.append(result)
        
        for i in range(10):
            thread = threading.Thread(target=worker_wrapper, args=(i,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Analyze results
        successful_operations = sum(1 for r in results if r['success'])
        failed_operations = len(results) - successful_operations
        exceptions = sum(1 for r in results if r['error'] is not None)
        
        print(f"    📊 Successful operations: {successful_operations}/{len(results)}")
        print(f"    📊 Failed operations: {failed_operations}")
        print(f"    📊 Exceptions: {exceptions}")
        
        if exceptions == 0:
            print("    ✅ No unhandled exceptions in concurrent access")
            self.recovery_tests.append(('Concurrent Access Handling', True))
        else:
            print("    ❌ Unhandled exceptions in concurrent access")
            self.error_scenarios.append(('Concurrent Access', f'{exceptions} unhandled exceptions'))
        
        self.validation_results['concurrent_access'] = {
            'operations_run': len(results),
            'successful_operations': successful_operations,
            'unhandled_exceptions': exceptions,
            'success_rate': successful_operations / len(results)
        }
    
    def test_recovery_mechanisms(self):
        """Test recovery mechanisms"""
        print("\n🛡️  Testing Recovery Mechanisms")
        print("-" * 50)
        
        # Test backup and recovery
        print("  🧪 Testing backup and recovery...")
        
        test_file = Path(self.test_dir) / 'recovery_test.json'
        test_data = {
            'test_data': 'recovery_test',
            'timestamp': datetime.now().isoformat()
        }
        
        # Save test data
        save_result = save_json_data(str(test_file), test_data)
        if not save_result:
            print("    ❌ Failed to save test data")
            return
        
        # Create backup
        backup_result = backup_data_file(str(test_file))
        if not backup_result['success']:
            print("    ❌ Failed to create backup")
            return
        
        print(f"    📦 Backup created: {backup_result['backup_path']}")
        
        # Simulate data corruption
        with open(test_file, 'w') as f:
            f.write('corrupted data')
        
        # Test recovery
        backup_data = load_json_data(backup_result['backup_path'])
        if backup_data and backup_data['test_data'] == 'recovery_test':
            print("    ✅ Data recovery successful")
            self.recovery_tests.append(('Data Backup Recovery', True))
        else:
            print("    ❌ Data recovery failed")
            self.error_scenarios.append(('Data Recovery', 'Failed to recover from backup'))
        
        self.validation_results['recovery_mechanisms'] = {
            'backup_creation': backup_result['success'],
            'data_recovery': backup_data is not None,
            'recovery_successful': backup_data and backup_data['test_data'] == 'recovery_test'
        }
    
    def generate_error_handling_report(self):
        """Generate comprehensive error handling report"""
        print("\n📊 ERROR HANDLING VALIDATION REPORT")
        print("=" * 80)
        
        # Summary statistics
        total_scenarios = len(self.error_scenarios) + len(self.recovery_tests)
        successful_recoveries = len(self.recovery_tests)
        error_scenarios_found = len(self.error_scenarios)
        
        print(f"📈 Total scenarios tested: {total_scenarios}")
        print(f"✅ Successful recoveries: {successful_recoveries}")
        print(f"❌ Error scenarios found: {error_scenarios_found}")
        
        if total_scenarios > 0:
            recovery_rate = successful_recoveries / total_scenarios
            print(f"📊 Recovery rate: {recovery_rate:.1%}")
        
        # Detailed results
        if self.validation_results:
            print("\n🔍 DETAILED RESULTS")
            print("-" * 40)
            
            for test_name, results in self.validation_results.items():
                print(f"\n{test_name.replace('_', ' ').title()}:")
                for key, value in results.items():
                    if isinstance(value, float):
                        print(f"  {key}: {value:.2f}")
                    else:
                        print(f"  {key}: {value}")
        
        # Error scenarios
        if self.error_scenarios:
            print("\n🚨 ERROR SCENARIOS FOUND")
            print("-" * 40)
            
            for i, (scenario, description) in enumerate(self.error_scenarios, 1):
                print(f"{i}. {scenario}")
                print(f"   Description: {description}")
        
        # Recovery tests
        if self.recovery_tests:
            print("\n🛡️  RECOVERY TESTS PASSED")
            print("-" * 40)
            
            for i, (test_name, success) in enumerate(self.recovery_tests, 1):
                status = "✅ PASSED" if success else "❌ FAILED"
                print(f"{i}. {test_name}: {status}")
        
        # Overall grade
        if total_scenarios > 0:
            score = (successful_recoveries / total_scenarios) * 100
            
            if score >= 90:
                grade = "A+"
                status = "EXCELLENT"
            elif score >= 80:
                grade = "A"
                status = "GOOD"
            elif score >= 70:
                grade = "B"
                status = "ACCEPTABLE"
            elif score >= 60:
                grade = "C"
                status = "NEEDS IMPROVEMENT"
            else:
                grade = "F"
                status = "CRITICAL ISSUES"
            
            print(f"\n🏆 ERROR HANDLING GRADE: {grade} ({status})")
            print(f"📊 SCORE: {score:.1f}/100")
        
        return {
            'total_scenarios': total_scenarios,
            'successful_recoveries': successful_recoveries,
            'error_scenarios': error_scenarios_found,
            'recovery_rate': recovery_rate if total_scenarios > 0 else 0,
            'validation_results': self.validation_results,
            'error_scenarios_list': self.error_scenarios,
            'recovery_tests_list': self.recovery_tests
        }
    
    def run_comprehensive_error_validation(self):
        """Run comprehensive error handling validation"""
        print("🚨 OpenAI Plus Redeem Plugin - Error Handling Validation")
        print("=" * 80)
        
        try:
            # Setup test environment
            if not self.setup_test_environment():
                print("❌ Failed to setup test environment")
                return False
            
            # Run error handling tests
            self.test_file_system_error_scenarios()
            self.test_invalid_data_handling()
            self.test_concurrent_access_errors()
            self.test_recovery_mechanisms()
            
            # Generate report
            report = self.generate_error_handling_report()
            
            return report
            
        except Exception as e:
            print(f"❌ Error validation failed: {e}")
            return None
        
        finally:
            # Always cleanup
            self.cleanup_test_environment()


def main():
    """Main entry point"""
    print("🚨 OpenAI Plus Redeem Plugin - Error Handling Validator")
    print("=" * 80)
    
    validator = ErrorHandlingValidator()
    
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        print("Usage:")
        print("  python validate_error_handling.py    # Run comprehensive error handling validation")
        return 0
    
    report = validator.run_comprehensive_error_validation()
    
    if report:
        if report['error_scenarios'] > 0:
            print("\n⚠️  Error handling issues found!")
            return 1
        else:
            print("\n✅ Error handling validation completed successfully!")
            return 0
    else:
        print("\n❌ Error handling validation failed!")
        return 1


if __name__ == '__main__':
    sys.exit(main())
