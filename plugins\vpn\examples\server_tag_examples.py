#!/usr/bin/env python3
"""
Example script showing how to configure server tags for different VPN products
This demonstrates the recommended server configurations for various SKU types
"""

# Example server configurations with appropriate tags

EXAMPLE_SERVER_CONFIGURATIONS = {
    "malaysia_basic_servers": [
        {
            "name": "MY-Shinjiru-Basic-01",
            "host": "my-basic1.shinjiru.com.my",
            "port": 22,
            "username": "root",
            "description": "Malaysia Basic VPN Server - Shinjiru",
            "tags": ["malaysia", "shinjiru", "basic"],
            "is_active": True,
            "recommended_for": ["my_", "my_15", "my_30", "my_60"]
        },
        {
            "name": "MY-Shinjiru-Basic-02", 
            "host": "my-basic2.shinjiru.com.my",
            "port": 22,
            "username": "root",
            "description": "Malaysia Basic VPN Server - Shinjiru Backup",
            "tags": ["malaysia", "shinjiru", "basic", "backup"],
            "is_active": True,
            "recommended_for": ["my_", "my_15", "my_30", "my_60"]
        }
    ],
    
    "malaysia_standard_servers": [
        {
            "name": "MY-Shinjiru-Standard-01",
            "host": "my-std1.shinjiru.com.my", 
            "port": 22,
            "username": "root",
            "description": "Malaysia Standard VPN Server",
            "tags": ["malaysia", "shinjiru", "standard"],
            "is_active": True,
            "recommended_for": ["my_standard", "my_standard_15", "my_standard_30"]
        }
    ],
    
    "malaysia_premium_servers": [
        {
            "name": "MY-Shinjiru-Premium-01",
            "host": "my-premium1.shinjiru.com.my",
            "port": 22, 
            "username": "root",
            "description": "Malaysia Premium VPN Server",
            "tags": ["malaysia", "shinjiru", "premium"],
            "is_active": True,
            "recommended_for": ["my_premium", "my_premium_30", "my_premium_60"]
        }
    ],
    
    "malaysia_highspeed_servers": [
        {
            "name": "MY-Global-HS-01",
            "host": "my-hs1.global.com",
            "port": 22,
            "username": "root", 
            "description": "Malaysia High-Speed Multi-Region Server",
            "tags": ["malaysia", "singapore", "highspeed", "premium", "multiregion"],
            "is_active": True,
            "recommended_for": ["my_highspeed", "my_highspeed_15", "my_highspeed_30", "my_highspeed_60"]
        },
        {
            "name": "SG-DO-HS-01",
            "host": "sg-hs1.digitalocean.com",
            "port": 22,
            "username": "root",
            "description": "Singapore High-Speed Server for MY customers",
            "tags": ["singapore", "digitalocean", "highspeed", "premium"],
            "is_active": True,
            "recommended_for": ["my_highspeed", "my_highspeed_premium"]
        }
    ],
    
    "singapore_basic_servers": [
        {
            "name": "SG-DO-Basic-01",
            "host": "sg-basic1.digitalocean.com",
            "port": 22,
            "username": "root",
            "description": "Singapore Basic VPN Server - Digital Ocean",
            "tags": ["singapore", "digitalocean", "basic"],
            "is_active": True,
            "recommended_for": ["sg_", "sg_15", "sg_30"]
        }
    ],
    
    "singapore_highspeed_servers": [
        {
            "name": "SG-DO-HS-02",
            "host": "sg-hs2.digitalocean.com",
            "port": 22,
            "username": "root",
            "description": "Singapore High-Speed Server",
            "tags": ["singapore", "digitalocean", "highspeed"],
            "is_active": True,
            "recommended_for": ["sg_highspeed", "sg_highspeed_15", "sg_highspeed_30"]
        }
    ],
    
    "singapore_premium_servers": [
        {
            "name": "SG-DO-Premium-01",
            "host": "sg-premium1.digitalocean.com",
            "port": 22,
            "username": "root",
            "description": "Singapore Premium VPN Server",
            "tags": ["singapore", "digitalocean", "premium"],
            "is_active": True,
            "recommended_for": ["sg_premium", "sg_premium_30", "sg_premium_60"]
        }
    ],
    
    "singapore_business_servers": [
        {
            "name": "SG-DO-Business-01",
            "host": "sg-biz1.digitalocean.com",
            "port": 22,
            "username": "root",
            "description": "Singapore Business VPN Server",
            "tags": ["singapore", "digitalocean", "business", "premium"],
            "is_active": True,
            "recommended_for": ["sg_business", "sg_business_30", "sg_business_90"]
        }
    ]
}

# SKU to expected server tags mapping (for validation)
SKU_EXPECTED_TAGS = {
    # Malaysia Basic
    "my_": ["malaysia", "shinjiru", "basic"],
    "my_15": ["malaysia", "shinjiru", "basic"],
    "my_30": ["malaysia", "shinjiru", "basic"],
    "my_60": ["malaysia", "shinjiru", "basic"],
    
    # Malaysia Standard
    "my_standard": ["malaysia", "shinjiru", "standard"],
    "my_standard_15": ["malaysia", "shinjiru", "standard"],
    "my_standard_30": ["malaysia", "shinjiru", "standard"],
    
    # Malaysia Premium
    "my_premium": ["malaysia", "shinjiru", "premium"],
    "my_premium_30": ["malaysia", "shinjiru", "premium"],
    "my_premium_60": ["malaysia", "shinjiru", "premium"],
    
    # Malaysia High-Speed
    "my_highspeed": ["malaysia", "singapore", "highspeed", "premium"],
    "my_highspeed_15": ["malaysia", "singapore", "highspeed", "premium"],
    "my_highspeed_30": ["malaysia", "singapore", "highspeed", "premium"],
    "my_highspeed_60": ["malaysia", "singapore", "highspeed", "premium"],
    "my_highspeed_premium": ["malaysia", "singapore", "highspeed", "premium", "business"],
    
    # Singapore Basic
    "sg_": ["singapore", "digitalocean", "basic"],
    "sg_15": ["singapore", "digitalocean", "basic"],
    "sg_30": ["singapore", "digitalocean", "basic"],
    
    # Singapore High-Speed
    "sg_highspeed": ["singapore", "digitalocean", "highspeed"],
    "sg_highspeed_15": ["singapore", "digitalocean", "highspeed"],
    "sg_highspeed_30": ["singapore", "digitalocean", "highspeed"],
    
    # Singapore Premium
    "sg_premium": ["singapore", "digitalocean", "premium"],
    "sg_premium_30": ["singapore", "digitalocean", "premium"],
    "sg_premium_60": ["singapore", "digitalocean", "premium"],
    
    # Singapore Business
    "sg_business": ["singapore", "digitalocean", "business", "premium"],
    "sg_business_30": ["singapore", "digitalocean", "business", "premium"],
    "sg_business_90": ["singapore", "digitalocean", "business", "premium"],
}

def print_server_configurations():
    """Print all example server configurations"""
    print("=" * 80)
    print("EXAMPLE SERVER CONFIGURATIONS WITH TAGS")
    print("=" * 80)
    
    for category, servers in EXAMPLE_SERVER_CONFIGURATIONS.items():
        print(f"\n{category.upper().replace('_', ' ')}:")
        print("-" * 50)
        
        for server in servers:
            print(f"  Name: {server['name']}")
            print(f"  Host: {server['host']}")
            print(f"  Tags: {', '.join(server['tags'])}")
            print(f"  Recommended for: {', '.join(server['recommended_for'])}")
            print(f"  Description: {server['description']}")
            print()

def validate_sku_coverage():
    """Validate that all SKUs have appropriate server coverage"""
    print("=" * 80)
    print("SKU TO SERVER TAG VALIDATION")
    print("=" * 80)
    
    # Get all servers
    all_servers = []
    for servers in EXAMPLE_SERVER_CONFIGURATIONS.values():
        all_servers.extend(servers)
    
    print(f"Total configured servers: {len(all_servers)}")
    print()
    
    # Check each SKU
    for sku, expected_tags in SKU_EXPECTED_TAGS.items():
        matching_servers = []
        
        for server in all_servers:
            server_tags = server['tags']
            # Check if server has any of the expected tags
            if any(tag in server_tags for tag in expected_tags):
                matching_servers.append(server['name'])
        
        status = "✓" if matching_servers else "✗"
        print(f"{status} SKU: {sku:25} Expected tags: {expected_tags}")
        if matching_servers:
            print(f"    Matching servers: {', '.join(matching_servers)}")
        else:
            print(f"    ⚠️  No servers found with required tags!")
        print()

def generate_server_creation_commands():
    """Generate example commands for creating servers via API"""
    print("=" * 80)
    print("EXAMPLE SERVER CREATION COMMANDS")
    print("=" * 80)
    
    print("# Example curl commands to create servers via VPN API")
    print()
    
    for category, servers in EXAMPLE_SERVER_CONFIGURATIONS.items():
        print(f"# {category.upper().replace('_', ' ')}")
        
        for server in servers:
            tags_str = ','.join(server['tags'])
            print(f"""curl -X POST "https://your-vpn-api.com/api/v1/servers/" \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "name": "{server['name']}",
    "host": "{server['host']}",
    "port": {server['port']},
    "username": "{server['username']}",
    "password": "YOUR_PASSWORD",
    "description": "{server['description']}",
    "tags": ["{tags_str.replace(',', '", "')}"],
    "is_active": {str(server['is_active']).lower()}
  }}'
""")
        print()

if __name__ == "__main__":
    print("VPN Server Tag Configuration Examples")
    print_server_configurations()
    validate_sku_coverage()
    generate_server_creation_commands()
