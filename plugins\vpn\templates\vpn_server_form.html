{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <form method="POST" action="" id="serverForm">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="name">Server Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name"
                                value="{{ server.name if server else '' }}" required>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="host">Host/IP Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="host" name="host"
                                        value="{{ server.host if server else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="port">SSH Port</label>
                                    <input type="number" class="form-control" id="port" name="port"
                                        value="{{ server.port if server else 22 }}" min="1" max="65535">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="username">SSH Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username"
                                value="{{ server.username if server else '' }}" required>
                        </div>

                        <div class="form-group">
                            <label for="password">SSH Password</label>
                            <input type="password" class="form-control" id="password" name="password"
                                placeholder="{{ 'Leave blank to keep current password' if action == 'edit' else 'Enter password' }}">
                            <small class="form-text text-muted">
                                You can use either password or SSH key for authentication
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="private_key">SSH Private Key</label>
                            <textarea class="form-control" id="private_key" name="private_key" rows="5"
                                placeholder="-----BEGIN RSA PRIVATE KEY-----&#10;...&#10;-----END RSA PRIVATE KEY-----">{{ server.private_key if server and server.private_key else '' }}</textarea>
                        </div>

                        <div class="form-group">
                            <label for="private_key_passphrase">Private Key Passphrase</label>
                            <input type="password" class="form-control" id="private_key_passphrase"
                                name="private_key_passphrase" placeholder="Enter passphrase if key is encrypted">
                        </div>

                        <!-- Connection Testing Section -->
                        <div class="card mt-3 mb-3 connection-testing-card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-network-wired"></i> Connection Testing</h5>
                                <small class="text-light">Test your server connectivity before creating</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-primary btn-block test-button" id="testSSHBtn"
                                                onclick="window.testSSHConnection()">
                                            <i class="fas fa-plug"></i> Test SSH Connection
                                        </button>
                                        <small class="text-muted d-block mt-1">Verify SSH credentials and connectivity</small>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-success btn-block test-button" id="testXrayBtn"
                                                onclick="window.testXrayService()">
                                            <i class="fas fa-cogs"></i> Test Xray Service
                                        </button>
                                        <small class="text-muted d-block mt-1">Check Xray configuration and service status</small>
                                    </div>
                                </div>
                                <div id="testResults" class="mt-3" style="display: none;">
                                    <div class="alert" id="testAlert" role="alert"></div>
                                    <div id="testDetails" class="small text-muted"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description"
                                rows="2">{{ server.description if server else '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="xray_config_path">Xray Config Path</label>
                                    <input type="text" class="form-control" id="xray_config_path"
                                        name="xray_config_path"
                                        value="{{ server.xray_config_path if server else '/etc/xray/config.json' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="xray_service_name">Xray Service Name</label>
                                    <input type="text" class="form-control" id="xray_service_name"
                                        name="xray_service_name"
                                        value="{{ server.xray_service_name if server else 'xray' }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="tags">Tags (comma-separated)</label>
                            <input type="text" class="form-control" id="tags" name="tags"
                                value="{{ ','.join(server.tags) if server and server.tags else '' }}"
                                placeholder="production, singapore, high-speed">
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active"
                                    {{ 'checked' if (server and server.is_active) or (not server) else '' }}>
                                <label class="custom-control-label" for="is_active">Server Active</label>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> {{ 'Update' if action == 'edit' else 'Create' }} Server
                        </button>
                        <a href="{{ url_for('vpn.servers') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Inline JavaScript to ensure it loads -->
<script>
console.log('=== VPN SERVER FORM INLINE SCRIPT LOADED ===');

// Define functions immediately in global scope
window.testSSHConnection = function() {
    console.log('SSH INLINE CLICK! Testing SSH connection...');
    const btn = document.getElementById('testSSHBtn');
    const originalText = btn.innerHTML;

    // Get form values
    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const privateKey = document.getElementById('private_key').value;

    // Validate required fields
    if (!host || !username) {
        window.showTestResult('error', 'Please fill in Host/IP Address and SSH Username before testing.');
        return;
    }

    if (!password && !privateKey) {
        window.showTestResult('error', 'Please provide either SSH Password or Private Key for authentication.');
        return;
    }

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing SSH Connection...';
    btn.disabled = true;
    btn.classList.add('btn-loading');

    // Prepare test data
    const testData = {
        host: host,
        port: parseInt(document.getElementById('port').value) || 22,
        username: username,
        password: password,
        private_key: privateKey,
        private_key_passphrase: document.getElementById('private_key_passphrase').value
    };

    console.log('Test data prepared:', testData);

    // Make fetch request
    const testUrl = window.location.origin + '/admin/vpn/api/test-ssh-credentials';
    console.log('Making request to:', testUrl);

    fetch(testUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('Success response:', data);
        if (data.success) {
            window.showTestResult('success', 'SSH Connection Successful!', data.details);
        } else {
            window.showTestResult('error', 'SSH Connection Failed: ' + (data.message || 'Unknown error'), data.details);
        }
    })
    .catch(error => {
        console.log('Error:', error);
        window.showTestResult('error', 'Connection test failed: ' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        btn.classList.remove('btn-loading');
    });
};

window.testXrayService = function() {
    console.log('XRAY INLINE CLICK! Testing Xray service...');
    const btn = document.getElementById('testXrayBtn');
    const originalText = btn.innerHTML;

    // Get form values
    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const privateKey = document.getElementById('private_key').value;

    // Validate required fields
    if (!host || !username) {
        window.showTestResult('error', 'Please fill in Host/IP Address and SSH Username before testing.');
        return;
    }

    if (!password && !privateKey) {
        window.showTestResult('error', 'Please provide either SSH Password or Private Key for authentication.');
        return;
    }

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing Xray Service...';
    btn.disabled = true;
    btn.classList.add('btn-loading');

    // Prepare test data
    const testData = {
        host: host,
        port: parseInt(document.getElementById('port').value) || 22,
        username: username,
        password: password,
        private_key: privateKey,
        private_key_passphrase: document.getElementById('private_key_passphrase').value,
        xray_config_path: document.getElementById('xray_config_path').value || '/etc/xray/config.json',
        xray_service_name: document.getElementById('xray_service_name').value || 'xray'
    };

    console.log('Xray test data prepared:', testData);

    // Make fetch request
    const testUrl = window.location.origin + '/admin/vpn/api/test-xray-service';
    console.log('Making Xray request to:', testUrl);

    fetch(testUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('Xray success response:', data);
        if (data.success) {
            window.showTestResult('success', 'Xray Service Test Successful!', data.details);
        } else {
            window.showTestResult('warning', 'Xray Service Issues Found: ' + (data.message || 'Unknown error'), data.details);
        }
    })
    .catch(error => {
        console.log('Xray error:', error);
        window.showTestResult('error', 'Xray service test failed: ' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        btn.classList.remove('btn-loading');
    });
};

window.showTestResult = function(type, message, details) {
    console.log('Showing test result:', type, message, details);

    const alertClass = type === 'success' ? 'alert-success' :
        type === 'warning' ? 'alert-warning' : 'alert-danger';

    const icon = type === 'success' ? 'fas fa-check-circle' :
        type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-times-circle';

    const testAlert = document.getElementById('testAlert');
    const testDetails = document.getElementById('testDetails');
    const testResults = document.getElementById('testResults');

    // Clear previous classes
    testAlert.className = 'alert ' + alertClass;
    testAlert.innerHTML = `<i class="${icon}"></i> ${message}`;

    if (details) {
        let detailsHtml = '<strong>Details:</strong><br>';
        if (typeof details === 'object') {
            for (const [key, value] of Object.entries(details)) {
                detailsHtml += `<strong>${key}:</strong> ${value}<br>`;
            }
        } else {
            detailsHtml += details;
        }
        testDetails.innerHTML = detailsHtml;
    } else {
        testDetails.innerHTML = '';
    }

    testResults.style.display = 'block';

    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            testResults.style.display = 'none';
        }, 5000);
    }
};

console.log('Functions defined inline:');
console.log('- testSSHConnection:', typeof window.testSSHConnection !== 'undefined');
console.log('- testXrayService:', typeof window.testXrayService !== 'undefined');
console.log('- showTestResult:', typeof window.showTestResult !== 'undefined');

// Test button existence
console.log('Button elements:');
console.log('- testSSHBtn:', document.getElementById('testSSHBtn') !== null);
console.log('- testXrayBtn:', document.getElementById('testXrayBtn') !== null);

// Add a simple test function to verify onclick works
window.testButtonClick = function() {
    alert('Button click test successful!');
    console.log('Button click test successful!');
};

// Add event listeners as backup
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, adding backup event listeners...');

    const sshBtn = document.getElementById('testSSHBtn');
    const xrayBtn = document.getElementById('testXrayBtn');

    if (sshBtn) {
        console.log('SSH button found, adding event listener');
        sshBtn.addEventListener('click', function(e) {
            console.log('SSH button clicked via event listener');
            if (typeof window.testSSHConnection === 'function') {
                window.testSSHConnection();
            } else {
                console.error('testSSHConnection function not found');
            }
        });
    } else {
        console.error('SSH button not found');
    }

    if (xrayBtn) {
        console.log('Xray button found, adding event listener');
        xrayBtn.addEventListener('click', function(e) {
            console.log('Xray button clicked via event listener');
            if (typeof window.testXrayService === 'function') {
                window.testXrayService();
            } else {
                console.error('testXrayService function not found');
            }
        });
    } else {
        console.error('Xray button not found');
    }
});
</script>

{% endblock %}

{% block styles %}
<style>
    .btn-loading {
        position: relative;
        pointer-events: none;
    }
    
    .btn-loading .fas.fa-spinner {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .connection-testing-card {
        border: 2px solid #17a2b8;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .test-button {
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .test-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .test-button:disabled {
        transform: none;
        box-shadow: none;
    }
    
    #testResults {
        animation: slideDown 0.3s ease-out;
    }
    
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .alert {
        border-radius: 8px;
        border: none;
        font-weight: 500;
    }
    
    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }
    
    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }
    
    .alert-warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }
</style>
{% endblock %}

