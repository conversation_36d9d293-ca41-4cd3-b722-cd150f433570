# Multi-URL Webhook Support

ShopeeAPI now supports sending webhooks to multiple URLs simultaneously. This allows you to integrate with multiple services like SteamCodeTool's chat commands plugin and other webhook consumers.

## Configuration

### New Multi-URL Format

```json
{
  "WEBHOOK": {
    "ENABLED": true,
    "MESSAGE_RECEIVED": {
      "ENABLED": true,
      "URLS": [
        {
          "URL": "http://admin.funinggong.com:5678/webhook/7a5c83b2-ddbf-4b01-bb8e-5339fea009a4",
          "NAME": "Original Webhook",
          "ENABLED": true
        },
        {
          "URL": "http://localhost:5000/chat-commands/api/webhook",
          "NAME": "SteamCodeTool Chat Commands",
          "ENABLED": true
        }
      ],
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    },
    "MESSAGE_SENT": {
      "ENABLED": true,
      "URLS": [
        {
          "URL": "http://admin.funinggong.com:5678/webhook/45118c75-3dee-48de-9036-cad2d4216bb0",
          "NAME": "Original Webhook",
          "ENABLED": true
        },
        {
          "URL": "http://localhost:5000/chat-commands/api/webhook",
          "NAME": "SteamCodeTool Chat Commands",
          "ENABLED": true
        }
      ],
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    }
  }
}
```

### URL Configuration Properties

Each URL configuration object supports:

- `URL`: The webhook endpoint URL (required)
- `NAME`: A descriptive name for the webhook (optional, for logging)
- `ENABLED`: Whether this specific URL is enabled (optional, defaults to true)

### Backward Compatibility

The old single-URL format is still supported:

```json
{
  "WEBHOOK": {
    "ENABLED": true,
    "MESSAGE_RECEIVED": {
      "ENABLED": true,
      "URL": "http://example.com/webhook",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    }
  }
}
```

## Behavior

### Multi-URL Sending

- Webhooks are sent to all enabled URLs in parallel
- Each URL has independent retry logic
- If at least one URL receives the webhook successfully, the operation is considered successful
- Failed URLs are logged but don't prevent other URLs from receiving the webhook

### Retry Logic

- Each URL has its own retry attempts based on `RETRY_COUNT`
- Retries use exponential backoff with `RETRY_DELAY` seconds between attempts
- Failed URLs are logged with detailed error information

### Logging

The system provides detailed logging for:
- Configuration loading and validation
- Successful webhook deliveries
- Failed webhook attempts and retries
- Summary statistics (e.g., "Successfully sent to 2/3 URLs")

## Integration with SteamCodeTool

To integrate with SteamCodeTool's chat commands plugin, add this URL to your webhook configuration:

```json
{
  "URL": "http://localhost:5000/chat-commands/api/webhook",
  "NAME": "SteamCodeTool Chat Commands",
  "ENABLED": true
}
```

Make sure the SteamCodeTool server is running on the specified port and the chat commands plugin is enabled.

## Monitoring

Check the logs for webhook delivery status:

```
INFO:utils.webhook:Successfully sent MESSAGE_RECEIVED webhook to SteamCodeTool Chat Commands (http://localhost:5000/chat-commands/api/webhook)
INFO:utils.webhook:Successfully sent MESSAGE_RECEIVED webhook to 2/2 URLs
```

## Troubleshooting

### Common Issues

1. **URL not reachable**: Check if the target service is running and accessible
2. **Authentication errors**: Ensure the webhook endpoint doesn't require authentication or configure it properly
3. **Timeout errors**: Increase the timeout value or check network connectivity

### Disabling Specific URLs

To temporarily disable a specific webhook URL without removing it:

```json
{
  "URL": "http://example.com/webhook",
  "NAME": "Temporarily Disabled",
  "ENABLED": false
}
```

### Testing

You can test webhook delivery using tools like httpbin.org:

```json
{
  "URL": "http://httpbin.org/post",
  "NAME": "Test Webhook",
  "ENABLED": true
}
```
