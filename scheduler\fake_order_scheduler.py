"""
Fake Order Scheduled Tasks

This module provides scheduled task management for fake order maintenance,
including automatic cleanup, archival, and optimization tasks.
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from services.fake_order_maintenance import fake_order_maintenance, CleanupConfig, MaintenanceStats

logger = logging.getLogger(__name__)

@dataclass
class ScheduledTask:
    """Configuration for a scheduled task"""
    name: str
    function: Callable
    trigger_type: str  # 'cron' or 'interval'
    trigger_config: Dict[str, Any]
    enabled: bool = True
    last_run: Optional[datetime] = None
    last_result: Optional[Dict[str, Any]] = None
    error_count: int = 0
    max_errors: int = 5

class FakeOrderScheduler:
    """Scheduler for fake order maintenance tasks"""
    
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.tasks: Dict[str, ScheduledTask] = {}
        self._lock = threading.RLock()
        self._running = False
        
        # Initialize default tasks
        self._setup_default_tasks()
    
    def _setup_default_tasks(self):
        """Setup default scheduled tasks"""
        
        # Daily cleanup task (runs at 2 AM)
        self.add_task(
            name="daily_cleanup",
            function=self._run_daily_cleanup,
            trigger_type="cron",
            trigger_config={"hour": 2, "minute": 0},
            enabled=True
        )
        
        # Weekly deep cleanup (runs Sunday at 3 AM)
        self.add_task(
            name="weekly_deep_cleanup",
            function=self._run_weekly_cleanup,
            trigger_type="cron",
            trigger_config={"day_of_week": 6, "hour": 3, "minute": 0},
            enabled=True
        )
        
        # Storage optimization (runs every 6 hours)
        self.add_task(
            name="storage_optimization",
            function=self._run_storage_optimization,
            trigger_type="interval",
            trigger_config={"hours": 6},
            enabled=True
        )
        
        # Archive cleanup (runs monthly on 1st at 4 AM)
        self.add_task(
            name="archive_cleanup",
            function=self._run_archive_cleanup,
            trigger_type="cron",
            trigger_config={"day": 1, "hour": 4, "minute": 0},
            enabled=False  # Disabled by default, can be enabled as needed
        )
    
    def add_task(self, name: str, function: Callable, trigger_type: str, 
                 trigger_config: Dict[str, Any], enabled: bool = True) -> bool:
        """Add a new scheduled task"""
        try:
            with self._lock:
                task = ScheduledTask(
                    name=name,
                    function=function,
                    trigger_type=trigger_type,
                    trigger_config=trigger_config,
                    enabled=enabled
                )
                
                self.tasks[name] = task
                
                if self._running and enabled:
                    self._schedule_task(task)
                
                logger.info(f"Added scheduled task: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to add task {name}: {e}")
            return False
    
    def _schedule_task(self, task: ScheduledTask):
        """Schedule a task with the APScheduler"""
        try:
            # Create trigger
            if task.trigger_type == "cron":
                trigger = CronTrigger(**task.trigger_config)
            elif task.trigger_type == "interval":
                trigger = IntervalTrigger(**task.trigger_config)
            else:
                raise ValueError(f"Unsupported trigger type: {task.trigger_type}")
            
            # Add job to scheduler
            self.scheduler.add_job(
                func=self._execute_task,
                trigger=trigger,
                args=[task.name],
                id=task.name,
                name=f"Fake Order Task: {task.name}",
                replace_existing=True,
                max_instances=1  # Prevent overlapping executions
            )
            
            logger.debug(f"Scheduled task: {task.name}")
            
        except Exception as e:
            logger.error(f"Failed to schedule task {task.name}: {e}")
    
    def _execute_task(self, task_name: str):
        """Execute a scheduled task with error handling"""
        task = self.tasks.get(task_name)
        if not task or not task.enabled:
            return
        
        start_time = datetime.now()
        
        try:
            logger.info(f"Executing scheduled task: {task_name}")
            
            # Execute the task function
            result = task.function()
            
            # Update task status
            task.last_run = start_time
            task.last_result = {
                'success': True,
                'result': result,
                'runtime_seconds': (datetime.now() - start_time).total_seconds(),
                'executed_at': start_time.isoformat()
            }
            task.error_count = 0  # Reset error count on success
            
            logger.info(f"Task {task_name} completed successfully")
            
        except Exception as e:
            error_msg = f"Task {task_name} failed: {e}"
            logger.error(error_msg)
            
            # Update task status
            task.last_run = start_time
            task.last_result = {
                'success': False,
                'error': str(e),
                'runtime_seconds': (datetime.now() - start_time).total_seconds(),
                'executed_at': start_time.isoformat()
            }
            task.error_count += 1
            
            # Disable task if too many errors
            if task.error_count >= task.max_errors:
                task.enabled = False
                logger.error(f"Task {task_name} disabled due to repeated failures")
                self.scheduler.remove_job(task_name)
    
    def _run_daily_cleanup(self) -> Dict[str, Any]:
        """Run daily cleanup maintenance"""
        config = CleanupConfig(
            older_than_days=7,
            processed_only=False,
            max_orders_per_cleanup=500,
            preserve_recent_processed=True,
            preserve_error_orders=True,
            archive_before_delete=True,
            cleanup_templates=False,
            cleanup_logs=True,
            log_retention_days=30
        )
        
        stats = fake_order_maintenance.run_scheduled_cleanup(config)
        
        return {
            'task_type': 'daily_cleanup',
            'maintenance_stats': stats.__dict__,
            'config_used': config.__dict__
        }
    
    def _run_weekly_cleanup(self) -> Dict[str, Any]:
        """Run weekly deep cleanup maintenance"""
        config = CleanupConfig(
            older_than_days=14,  # More aggressive cleanup
            processed_only=False,
            max_orders_per_cleanup=2000,
            preserve_recent_processed=True,
            preserve_error_orders=False,  # Clean up error orders in weekly cleanup
            archive_before_delete=True,
            cleanup_templates=True,  # Clean up templates weekly
            cleanup_logs=True,
            log_retention_days=60
        )
        
        stats = fake_order_maintenance.run_scheduled_cleanup(config)
        
        return {
            'task_type': 'weekly_deep_cleanup',
            'maintenance_stats': stats.__dict__,
            'config_used': config.__dict__
        }
    
    def _run_storage_optimization(self) -> Dict[str, Any]:
        """Run storage optimization only"""
        try:
            optimization_stats = fake_order_maintenance._optimize_storage()
            
            return {
                'task_type': 'storage_optimization',
                'optimization_stats': optimization_stats
            }
            
        except Exception as e:
            logger.error(f"Storage optimization failed: {e}")
            return {
                'task_type': 'storage_optimization',
                'error': str(e)
            }
    
    def _run_archive_cleanup(self) -> Dict[str, Any]:
        """Run archive cleanup (remove very old archives)"""
        try:
            # This would implement cleanup of very old archive files
            # For now, just return a placeholder
            return {
                'task_type': 'archive_cleanup',
                'message': 'Archive cleanup not yet implemented'
            }
            
        except Exception as e:
            logger.error(f"Archive cleanup failed: {e}")
            return {
                'task_type': 'archive_cleanup',
                'error': str(e)
            }
    
    def start(self) -> bool:
        """Start the scheduler"""
        try:
            with self._lock:
                if self._running:
                    logger.warning("Scheduler is already running")
                    return True
                
                # Schedule all enabled tasks
                for task in self.tasks.values():
                    if task.enabled:
                        self._schedule_task(task)
                
                # Start the scheduler
                self.scheduler.start()
                self._running = True
                
                logger.info("Fake order scheduler started")
                return True
                
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            return False
    
    def stop(self) -> bool:
        """Stop the scheduler"""
        try:
            with self._lock:
                if not self._running:
                    logger.warning("Scheduler is not running")
                    return True
                
                self.scheduler.shutdown(wait=True)
                self._running = False
                
                logger.info("Fake order scheduler stopped")
                return True
                
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {e}")
            return False
    
    def enable_task(self, task_name: str) -> bool:
        """Enable a scheduled task"""
        try:
            with self._lock:
                task = self.tasks.get(task_name)
                if not task:
                    logger.error(f"Task not found: {task_name}")
                    return False
                
                task.enabled = True
                task.error_count = 0  # Reset error count
                
                if self._running:
                    self._schedule_task(task)
                
                logger.info(f"Enabled task: {task_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to enable task {task_name}: {e}")
            return False
    
    def disable_task(self, task_name: str) -> bool:
        """Disable a scheduled task"""
        try:
            with self._lock:
                task = self.tasks.get(task_name)
                if not task:
                    logger.error(f"Task not found: {task_name}")
                    return False
                
                task.enabled = False
                
                if self._running:
                    try:
                        self.scheduler.remove_job(task_name)
                    except Exception:
                        pass  # Job might not exist
                
                logger.info(f"Disabled task: {task_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to disable task {task_name}: {e}")
            return False
    
    def run_task_now(self, task_name: str) -> Dict[str, Any]:
        """Run a task immediately (outside of schedule)"""
        try:
            task = self.tasks.get(task_name)
            if not task:
                return {
                    'success': False,
                    'error': f'Task not found: {task_name}'
                }
            
            logger.info(f"Running task immediately: {task_name}")
            
            start_time = datetime.now()
            result = task.function()
            runtime = (datetime.now() - start_time).total_seconds()
            
            return {
                'success': True,
                'task_name': task_name,
                'result': result,
                'runtime_seconds': runtime,
                'executed_at': start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to run task {task_name}: {e}")
            return {
                'success': False,
                'task_name': task_name,
                'error': str(e)
            }
    
    def get_task_status(self, task_name: Optional[str] = None) -> Dict[str, Any]:
        """Get status of tasks"""
        try:
            with self._lock:
                if task_name:
                    task = self.tasks.get(task_name)
                    if not task:
                        return {'error': f'Task not found: {task_name}'}
                    
                    return {
                        'name': task.name,
                        'enabled': task.enabled,
                        'trigger_type': task.trigger_type,
                        'trigger_config': task.trigger_config,
                        'last_run': task.last_run.isoformat() if task.last_run else None,
                        'last_result': task.last_result,
                        'error_count': task.error_count,
                        'max_errors': task.max_errors
                    }
                else:
                    # Return all tasks
                    return {
                        'scheduler_running': self._running,
                        'total_tasks': len(self.tasks),
                        'enabled_tasks': sum(1 for t in self.tasks.values() if t.enabled),
                        'tasks': {
                            name: {
                                'enabled': task.enabled,
                                'last_run': task.last_run.isoformat() if task.last_run else None,
                                'error_count': task.error_count,
                                'last_success': task.last_result.get('success', None) if task.last_result else None
                            }
                            for name, task in self.tasks.items()
                        }
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get task status: {e}")
            return {'error': str(e)}
    
    def get_next_run_times(self) -> Dict[str, Any]:
        """Get next scheduled run times for all tasks"""
        try:
            if not self._running:
                return {'error': 'Scheduler is not running'}
            
            next_runs = {}
            
            for job in self.scheduler.get_jobs():
                next_runs[job.id] = {
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                }
            
            return {
                'scheduler_running': True,
                'next_runs': next_runs
            }
            
        except Exception as e:
            logger.error(f"Failed to get next run times: {e}")
            return {'error': str(e)}

# Global scheduler instance
fake_order_scheduler = FakeOrderScheduler()