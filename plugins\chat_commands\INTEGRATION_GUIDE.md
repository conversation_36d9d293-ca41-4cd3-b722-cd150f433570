# Chat Commands Plugin Integration Guide

This guide explains how to integrate the Chat Commands plugin with your existing ShopeeAPI webhook system to enable real-time automated responses to customer chat commands.

## Overview

The Chat Commands plugin replaces the polling-based approach from Stools-MTYB with a modern webhook-based system that:

- ✅ **Real-time responses** - No polling delays
- ✅ **Lower server load** - Event-driven instead of continuous polling  
- ✅ **Better reliability** - Direct webhook integration
- ✅ **Easy management** - Web UI for command CRUD operations
- ✅ **Configurable VPN API** - Uses your preferred endpoint (blueblue.api.limjianhui.com)

## Integration Steps

### 1. Enable the Plugin

The plugin is automatically discovered by SteamCodeTool. To enable it:

1. Navigate to the admin plugins page in SteamCodeTool
2. Enable the "Chat Commands" plugin
3. The plugin will be available at `/chat-commands/`

### 2. Configure ShopeeAPI Webhooks

Update your ShopeeAPI configuration to send webhooks to the Chat Commands plugin:

**Option A: Update ShopeeAPI config.json**
```json
{
  "webhook": {
    "enabled": true,
    "message_received": {
      "enabled": true,
      "url": "http://localhost:5000/chat-commands/api/webhook",
      "retry_count": 3,
      "retry_delay": 5
    }
  }
}
```

**Option B: Use the ShopeeAPI webhook registration endpoint**
```bash
curl -X POST http://localhost:8000/api/webhooks/register \
  -H "Content-Type: application/json" \
  -d '{
    "url": "http://localhost:5000/chat-commands/api/webhook",
    "events": ["message_received"]
  }'
```

### 3. Configure VPN Settings

1. Go to `/chat-commands/` in your SteamCodeTool interface
2. Update the VPN Configuration section:
   - **API Endpoint**: `https://blueblue.api.limjianhui.com/openapi.json`
   - **Username**: `admin`
   - **Password**: `admin123`
   - **Enable VPN Config Generation**: ✅ Checked

### 4. Test the Integration

1. Use the test functionality in the web interface
2. Send a test message with `#help` to verify webhook processing
3. Try the `#config` command: `#config sg1 30 digi unlimited`

## Webhook Flow

```
Customer sends "#android_help" → ShopeeAPI receives message → 
ShopeeAPI sends webhook to Chat Commands plugin → 
Plugin processes command → Plugin sends response via ShopeeAPI → 
Customer receives tutorial text + image
```

## Available Commands

### Default Commands (Ready to Use)

| Command | Description | Response |
|---------|-------------|----------|
| `#help` | Show command list | Text with all available commands |
| `#android_help` | Android VPN setup | Tutorial text + image |
| `#ios_help` | iOS VPN setup | Tutorial text + image |
| `#android_digi` | Android Digi APN | Tutorial text + 2 images |
| `#ios_digi` | iOS Digi APN | Tutorial text + image |
| `#vpn` | VPN explanation | Informational text |
| `#bypass` | Bypass configs | Link to documentation |
| `#openwrt_help` | OpenWrt tutorial | YouTube link |
| `#config <params>` | Generate VPN config | Config info + tutorial + config file |

### Config Command Usage

```
#config <server> <days> <telco> <plan>
```

Example:
```
#config sg1 30 digi unlimited
```

This generates a VPN configuration using your configured API endpoint.

## Management Interface

Access the management interface at `/chat-commands/` to:

- **View all commands** in a table format
- **Add new commands** with text and image responses
- **Edit existing commands** including response text and images
- **Test commands** to see what responses they generate
- **Delete commands** you no longer need
- **Configure VPN settings** for the config command

### Adding Custom Commands

1. Click "Add Command" button
2. Fill in the form:
   - **Command Name**: The trigger word (without #)
   - **Description**: Human-readable description
   - **Response Text**: The message to send to customers
   - **Image URLs**: Optional images (one per line)
   - **Required Parameters**: For commands that need parameters
   - **Enable Command**: Whether the command is active
3. Click "Save Command"

## API Endpoints

The plugin provides REST API endpoints for integration:

### Commands Management
- `GET /chat-commands/api/commands` - List all commands
- `POST /chat-commands/api/commands` - Create new command
- `PUT /chat-commands/api/commands/<name>` - Update command
- `DELETE /chat-commands/api/commands/<name>` - Delete command

### Webhook Processing
- `POST /chat-commands/api/webhook` - Receive ShopeeAPI webhooks

### Testing
- `POST /chat-commands/api/test-command` - Test command responses

## Migration from Stools-MTYB

If you're migrating from the Stools-MTYB conversation handler:

### What Changes
- ❌ **Remove**: `tasks/conversationHandler.py` polling
- ❌ **Remove**: Scheduler job for conversation polling
- ✅ **Add**: Webhook-based real-time processing
- ✅ **Keep**: All existing commands and responses
- ✅ **Improve**: Web-based management interface

### Migration Steps
1. **Backup** your existing `tasks/config/commands.json` and `responses.json`
2. **Install** the Chat Commands plugin
3. **Import** your existing commands through the web interface
4. **Configure** the webhook integration
5. **Test** the new system
6. **Disable** the old conversation handler

## Troubleshooting

### Commands Not Responding

1. **Check plugin status**: Ensure the plugin is enabled
2. **Verify webhook**: Test the webhook endpoint manually
3. **Check ShopeeAPI**: Ensure it's sending webhooks correctly
4. **Review logs**: Check SteamCodeTool logs for errors

### VPN Config Generation Fails

1. **Test API endpoint**: Verify `blueblue.api.limjianhui.com` is accessible
2. **Check credentials**: Ensure username/password are correct
3. **Review parameters**: Verify the command format is correct
4. **Check logs**: Look for API error messages

### Webhook Integration Issues

1. **Network connectivity**: Ensure SteamCodeTool can receive webhooks
2. **URL configuration**: Verify the webhook URL is correct
3. **ShopeeAPI status**: Check if ShopeeAPI is running and configured
4. **Firewall**: Ensure ports are open for webhook delivery

## Advanced Configuration

### Custom VPN API

To use a different VPN API endpoint:

1. Update the VPN configuration in the web interface
2. Ensure your API follows the same request/response format
3. Test the integration with the test command feature

### Custom Response Logic

For complex command logic, you can modify the `MessageProcessor` class in `services.py`:

```python
def _handle_custom_command(self, command: ChatCommand, params: List[str]) -> List[CommandResponse]:
    # Your custom logic here
    pass
```

### Webhook Security

For production environments, consider:

1. **HTTPS**: Use HTTPS for webhook URLs
2. **Authentication**: Add webhook signature verification
3. **Rate limiting**: Implement rate limiting for webhook endpoints
4. **Monitoring**: Set up monitoring for webhook delivery

## Support

For issues or questions:

1. Check the plugin logs in SteamCodeTool
2. Review the test results using the built-in test functionality
3. Verify your ShopeeAPI webhook configuration
4. Test individual components using the provided test script

The Chat Commands plugin provides a robust, scalable solution for automated customer support that integrates seamlessly with your existing SteamCodeTool and ShopeeAPI infrastructure.
