"""
Log filtering system for Chat Commands Plugin
Controls various types of logs based on debug configuration
"""

import logging
import os
import json
from typing import Dict, Any


class ChatCommandsLogFilter(logging.Filter):
    """Custom log filter that controls log output based on debug configuration"""
    
    def __init__(self, plugin_dir: str):
        super().__init__()
        self.plugin_dir = plugin_dir
        self.config_file = os.path.join(plugin_dir, 'config.json')
        self._debug_config = {}
        self._load_debug_config()
    
    def _load_debug_config(self):
        """Load debug configuration from config file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self._debug_config = config_data.get('debug_config', {})
        except Exception:
            # If config loading fails, default to allowing all logs
            self._debug_config = {
                'enabled': True,
                'log_info_messages': True,
                'log_access_audit': True,
                'log_werkzeug': True,
                'log_service_operations': True
            }
    
    def filter(self, record):
        """Filter log records based on debug configuration"""
        # Reload config periodically to pick up changes
        if hasattr(self, '_last_config_check'):
            import time
            if time.time() - self._last_config_check > 5:  # Check every 5 seconds
                self._load_debug_config()
                self._last_config_check = time.time()
        else:
            import time
            self._last_config_check = time.time()

        # If debug is not enabled, filter out noisy logs by default
        debug_enabled = self._debug_config.get('enabled', False)

        logger_name = record.name
        message = record.getMessage()

        # Filter access_audit logs
        if logger_name == 'access_audit':
            if debug_enabled:
                return self._debug_config.get('log_access_audit', False)
            else:
                return False  # Block by default when debug is off

        # Filter werkzeug logs (Flask development server)
        if logger_name == 'werkzeug':
            if debug_enabled:
                return self._debug_config.get('log_werkzeug', False)
            else:
                return False  # Block by default when debug is off

        # Filter INFO level messages from chat_commands plugin
        if (logger_name.startswith('chat_commands') or
            logger_name.startswith('plugins.chat_commands')):
            if record.levelno == logging.INFO:
                if debug_enabled:
                    return self._debug_config.get('log_info_messages', False)
                else:
                    return False  # Block by default when debug is off

        # Filter service operation logs
        if ('Loaded' in message or 'Saved' in message or
            'Successfully' in message or 'configuration' in message.lower()):
            if record.levelno == logging.INFO:
                if debug_enabled:
                    return self._debug_config.get('log_service_operations', False)
                else:
                    return False  # Block by default when debug is off

        # Allow all other logs by default (errors, warnings, etc.)
        return True


def setup_log_filtering(plugin_dir: str):
    """Setup log filtering for the chat commands plugin"""
    try:
        # Create the filter
        log_filter = ChatCommandsLogFilter(plugin_dir)
        
        # Get the root logger
        root_logger = logging.getLogger()
        
        # Add filter to all handlers
        for handler in root_logger.handlers:
            handler.addFilter(log_filter)
        
        # Also add to specific loggers that might be created later
        loggers_to_filter = [
            'access_audit',
            'werkzeug', 
            'chat_commands',
            'plugins.chat_commands'
        ]
        
        for logger_name in loggers_to_filter:
            logger = logging.getLogger(logger_name)
            logger.addFilter(log_filter)
        
        return True
        
    except Exception as e:
        # If filtering setup fails, log the error but don't break the application
        logging.getLogger(__name__).error(f"Failed to setup log filtering: {e}")
        return False


def remove_log_filtering():
    """Remove log filtering (restore default logging behavior)"""
    try:
        # Get all loggers and remove our custom filters
        root_logger = logging.getLogger()
        
        # Remove filters from handlers
        for handler in root_logger.handlers:
            # Remove all ChatCommandsLogFilter instances
            handler.filters = [f for f in handler.filters 
                             if not isinstance(f, ChatCommandsLogFilter)]
        
        # Remove from specific loggers
        loggers_to_clean = [
            'access_audit',
            'werkzeug',
            'chat_commands', 
            'plugins.chat_commands'
        ]
        
        for logger_name in loggers_to_clean:
            logger = logging.getLogger(logger_name)
            logger.filters = [f for f in logger.filters 
                            if not isinstance(f, ChatCommandsLogFilter)]
        
        return True
        
    except Exception as e:
        logging.getLogger(__name__).error(f"Failed to remove log filtering: {e}")
        return False
