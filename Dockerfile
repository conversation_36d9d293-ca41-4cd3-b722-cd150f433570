FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=5000
ENV PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    sudo \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create necessary directories
RUN mkdir -p /app/logs
RUN mkdir -p /app/data
RUN mkdir -p /app/configs/core
RUN mkdir -p /app/configs/cache
RUN mkdir -p /app/configs/data
RUN mkdir -p /app/configs/services

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project
COPY . /app/

# Make entrypoint script executable
RUN chmod +x /app/docker-entrypoint.sh

# For automatic deployment, we'll run as root to avoid all permission issues
# This is acceptable for containerized environments where isolation is provided by <PERSON><PERSON>

# Ensure all necessary directories exist with proper permissions
RUN mkdir -p /app/configs/core \
    && mkdir -p /app/configs/cache \
    && mkdir -p /app/configs/data \
    && mkdir -p /app/configs/services \
    && mkdir -p /app/logs \
    && mkdir -p /app/data \
    && mkdir -p /app/data/netflix

# Set proper permissions for all directories (777 to ensure write access)
RUN chmod -R 777 /app/configs \
    && chmod -R 777 /app/logs \
    && chmod -R 777 /app/data

# Configuration will be initialized at runtime by the entrypoint script

# Note: Running as root for automatic deployment
# Docker provides container isolation, so this is acceptable

# Expose the port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Run the application using entrypoint script
CMD ["/app/docker-entrypoint.sh"]
