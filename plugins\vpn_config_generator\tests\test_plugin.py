"""
Tests for VPN Config Generator plugin.
"""

import unittest
import sys
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock

# Add plugin directory to path
plugin_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, plugin_dir)

from plugin import Plugin


class TestVPNConfigGeneratorPlugin(unittest.TestCase):
    """Test VPN Config Generator Plugin"""
    
    def setUp(self):
        """Set up test environment"""
        # Create mock plugin manager
        self.mock_plugin_manager = Mock()
        self.mock_plugin_manager.plugin_configs = {}
        self.mock_plugin_manager.get_plugin.return_value = None
        
        # Create temporary directory for plugin
        self.temp_dir = tempfile.mkdtemp()
        
        # Create plugin instance
        self.plugin = Plugin(self.mock_plugin_manager)
        self.plugin.plugin_dir = self.temp_dir
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_plugin_properties(self):
        """Test plugin basic properties"""
        self.assertEqual(self.plugin.name, "VPN Config Generator")
        self.assertEqual(self.plugin.version, "1.0.0")
        self.assertIn("VPN configuration generation", self.plugin.description)
        self.assertIsInstance(self.plugin.dependencies, list)
    
    def test_plugin_initialization_success(self):
        """Test successful plugin initialization"""
        # Mock successful initialization
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            
            result = self.plugin.initialize({})
            
            self.assertTrue(result)
            self.assertIsNotNone(self.plugin.config_service)
            self.assertIsNotNone(self.plugin.chat_command_service)
    
    def test_plugin_initialization_failure(self):
        """Test plugin initialization failure"""
        # Mock initialization failure
        with patch.object(self.plugin, '_register_api_endpoints', side_effect=Exception("Test error")):
            
            result = self.plugin.initialize({})
            
            self.assertFalse(result)
    
    def test_get_blueprint(self):
        """Test getting Flask blueprint"""
        # Initialize plugin first
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})
        
        blueprint = self.plugin.get_blueprint()
        
        self.assertIsNotNone(blueprint)
        self.assertEqual(blueprint.name, 'vpn_config_generator')
    
    def test_register_chat_commands(self):
        """Test registering chat commands"""
        # Mock chat commands plugin
        mock_chat_plugin = Mock()
        mock_chat_plugin.register_external_command.return_value = True
        self.mock_plugin_manager.get_plugin.return_value = mock_chat_plugin
        
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'):
            self.plugin.initialize({})
        
        # Verify chat command registration was attempted
        self.mock_plugin_manager.get_plugin.assert_called_with('chat_commands')
    
    def test_register_chat_commands_no_chat_plugin(self):
        """Test registering chat commands when chat plugin is not available"""
        # Mock no chat commands plugin
        self.mock_plugin_manager.get_plugin.return_value = None
        
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'):
            result = self.plugin.initialize({})
        
        # Should still succeed even without chat plugin
        self.assertTrue(result)
    
    def test_handle_config_command_success(self):
        """Test handling config command successfully"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})
        
        # Mock successful config generation
        mock_response = Mock()
        mock_response.success = True
        mock_response.created_date = '2024-01-01'
        mock_response.expired_date = '2024-01-31'
        mock_response.config = 'vmess://test-config'
        
        self.plugin.config_service.generate_config.return_value = mock_response
        
        # Mock message
        mock_message = Mock()
        mock_message.sender_name = 'testuser'
        
        # Test command handling
        params = ['server11', '30', 'digi', 'unlimited']
        responses = self.plugin._handle_config_command(mock_message, params)
        
        self.assertIsNotNone(responses)
        self.assertGreater(len(responses), 0)
        self.assertIn('ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ', responses[0].text)
    
    def test_handle_config_command_insufficient_params(self):
        """Test handling config command with insufficient parameters"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})
        
        # Mock message
        mock_message = Mock()
        mock_message.sender_name = 'testuser'
        
        # Test command handling with insufficient params
        params = ['server11', '30']  # Missing telco and plan
        responses = self.plugin._handle_config_command(mock_message, params)
        
        self.assertIsNotNone(responses)
        self.assertEqual(len(responses), 1)
        self.assertIn('Usage:', responses[0].text)
    
    def test_handle_config_command_generation_failure(self):
        """Test handling config command when generation fails"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})
        
        # Mock failed config generation
        mock_response = Mock()
        mock_response.success = False
        mock_response.error = 'API connection failed'
        
        self.plugin.config_service.generate_config.return_value = mock_response
        
        # Mock message
        mock_message = Mock()
        mock_message.sender_name = 'testuser'
        
        # Test command handling
        params = ['server11', '30', 'digi', 'unlimited']
        responses = self.plugin._handle_config_command(mock_message, params)
        
        self.assertIsNotNone(responses)
        self.assertEqual(len(responses), 1)
        self.assertIn('Failed to generate VPN config', responses[0].text)
    
    def test_plugin_shutdown(self):
        """Test plugin shutdown"""
        # Initialize plugin first
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})
        
        # Mock unregister method
        with patch.object(self.plugin, '_unregister_chat_commands'):
            result = self.plugin.shutdown()
        
        self.assertTrue(result)
        self.assertIsNone(self.plugin.config_service)
        self.assertIsNone(self.plugin.chat_command_service)
    
    def test_unregister_chat_commands(self):
        """Test unregistering chat commands"""
        # Mock chat commands plugin
        mock_chat_plugin = Mock()
        mock_chat_plugin.unregister_external_command.return_value = True
        self.mock_plugin_manager.get_plugin.return_value = mock_chat_plugin
        
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})
        
        # Test unregistering
        self.plugin._unregister_chat_commands()
        
        # Verify unregister was called
        mock_chat_plugin.unregister_external_command.assert_called_with('config', self.plugin.name)

    def test_handle_servers_command_success(self):
        """Test handling servers command successfully"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})

        # Mock VPN plugin and API service
        mock_vpn_plugin = Mock()
        mock_api_service = Mock()
        mock_vpn_plugin.api_service = mock_api_service

        # Mock successful authentication
        mock_api_service._ensure_authenticated.return_value = True

        # Mock servers response
        mock_servers_data = [
            {
                'id': 1,
                'name': 'SG-01',
                'is_active': True,
                'host': 'sg01.example.com',
                'description': 'Singapore Server 1',
                'health_status': 'healthy'
            },
            {
                'id': 2,
                'name': 'MY-KL',
                'is_active': False,
                'host': 'mykl.example.com',
                'description': 'Malaysia KL Server',
                'health_status': 'unhealthy'
            }
        ]
        mock_api_service.get_servers.return_value = mock_servers_data

        self.mock_plugin_manager.get_plugin.return_value = mock_vpn_plugin

        # Create mock message
        mock_message = Mock()
        mock_message.sender_name = 'testuser'

        # Test command handling
        params = []
        responses = self.plugin._handle_servers_command(mock_message, params)

        self.assertIsNotNone(responses)
        self.assertGreater(len(responses), 0)
        self.assertIn('Available VPN Servers', responses[0].text)
        self.assertIn('ID: 1', responses[0].text)
        self.assertIn('SG-01', responses[0].text)
        self.assertIn('ID: 2', responses[0].text)
        self.assertIn('MY-KL', responses[0].text)
        self.assertIn('2 total servers', responses[0].text)

    def test_handle_servers_command_no_vpn_plugin(self):
        """Test handling servers command when VPN plugin is not available"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})

        # Mock no VPN plugin
        self.mock_plugin_manager.get_plugin.return_value = None

        # Create mock message
        mock_message = Mock()
        mock_message.sender_name = 'testuser'

        # Test command handling
        params = []
        responses = self.plugin._handle_servers_command(mock_message, params)

        self.assertIsNotNone(responses)
        self.assertEqual(len(responses), 1)
        self.assertIn('VPN plugin not available', responses[0].text)

    def test_plugin_config_schema(self):
        """Test plugin configuration schema"""
        schema = self.plugin.get_config_schema()
        
        self.assertIsInstance(schema, dict)
        self.assertIn('api_config', schema)
        self.assertIn('generator_settings', schema)
        
        # Check API config schema
        api_config = schema['api_config']
        self.assertIn('enabled', api_config)
        self.assertIn('use_vpn_plugin_api', api_config)
        self.assertIn('timeout', api_config)
    
    def test_plugin_admin_routes(self):
        """Test plugin admin routes"""
        routes = self.plugin.get_admin_routes()

        self.assertIsInstance(routes, dict)
        self.assertIn('VPN Config Generator', routes)
        self.assertEqual(routes['VPN Config Generator'], '/vpn-config-generator/')

    def test_handle_help_command_success(self):
        """Test handling help command successfully"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})

        # Mock chat commands plugin for prefix
        mock_chat_plugin = Mock()
        mock_chat_service = Mock()
        mock_chat_service.get_command_prefix.return_value = "#"
        mock_chat_plugin.command_service = mock_chat_service
        self.mock_plugin_manager.get_plugin.return_value = mock_chat_plugin

        # Test help command
        mock_message = Mock()
        responses = self.plugin._handle_help_command(mock_message, [])

        # Verify response
        self.assertIsNotNone(responses)
        self.assertEqual(len(responses), 1)
        self.assertIn('VPN Commands Help', responses[0].text)
        self.assertIn('#v', responses[0].text)
        self.assertIn('#vhelp', responses[0].text)
        self.assertIn('#vuser', responses[0].text)
        self.assertIn('#vservers', responses[0].text)
        self.assertIn('Generate VPN Configuration', responses[0].text)
        self.assertIn('Usage:', responses[0].text)

    def test_handle_help_command_no_chat_plugin(self):
        """Test handling help command when chat plugin is not available"""
        # Initialize plugin
        with patch.object(self.plugin, '_register_api_endpoints'), \
             patch.object(self.plugin, '_register_chat_commands'):
            self.plugin.initialize({})

        # Mock no chat commands plugin
        self.mock_plugin_manager.get_plugin.return_value = None

        # Test help command
        mock_message = Mock()
        responses = self.plugin._handle_help_command(mock_message, [])

        # Verify response with default prefix
        self.assertIsNotNone(responses)
        self.assertEqual(len(responses), 1)
        self.assertIn('VPN Commands Help', responses[0].text)
        self.assertIn('#v', responses[0].text)  # Should use default # prefix


if __name__ == '__main__':
    unittest.main()
