from functools import wraps
from flask import request, jsonify, g
from config import API_KEY

def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check Authorization header (Bearer token format)
        auth_header = request.headers.get('Authorization')
        if auth_header:
            try:
                auth_type, token = auth_header.split()
                if auth_type.lower() == 'bearer' and token == API_KEY:
                    # Set user role for API key authenticated users
                    g.user_role = 'admin'  # API key users get admin privileges
                    g.user_id = 'api_user'
                    return f(*args, **kwargs)
            except ValueError:
                pass
        
        # Check X-API-Key header (direct API key format)
        api_key_header = request.headers.get('X-API-Key')
        if api_key_header and api_key_header == API_KEY:
            # Set user role for API key authenticated users
            g.user_role = 'admin'  # API key users get admin privileges
            g.user_id = 'api_user'
            return f(*args, **kwargs)
        
        return jsonify({"error": "Invalid or missing API key"}), 401
    return decorated_function