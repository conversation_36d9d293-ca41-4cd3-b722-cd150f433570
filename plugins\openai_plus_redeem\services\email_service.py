"""
Email Service

Service for Gmail IMAP integration to retrieve ChatGPT verification codes,
following the Steam plugin email pattern.
"""

import imaplib
import email
import re
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from threading import Lock

from .base_service import BaseService
from ..models.email_verification import EmailVerification, VerificationStatus
from ..models.utils import (
    load_json_data, save_json_data, validate_email, validate_verification_code,
    DataPersistenceError, ValidationError, get_data_file_path
)


class EmailService(BaseService):
    """
    Service for retrieving ChatGPT verification codes from Gmail
    
    Provides functionality for:
    - Gmail IMAP connection management
    - Verification code extraction
    - Email search and filtering
    - Connection testing
    - Verification logging
    """
    
    def __init__(self, config: Dict[str, Any], logger=None):
        super().__init__(config, logger)
        self.service_name = "EmailService"
        
        # Thread safety
        self.lock = Lock()
        
        # Data file path
        self.data_file = get_data_file_path('openai_plus_redeem', 'email_verification_logs.json')
        
        # Email configuration
        self.email_config = self._get_config_value('email_config', {})
        self.credentials = self.email_config.get('credentials', [])
        
        # IMAP settings
        self.imap_server = self.email_config.get('imap_server', 'imap.gmail.com')
        self.imap_port = self.email_config.get('imap_port', 993)
        self.use_ssl = self.email_config.get('use_ssl', True)
        
        # Search settings
        self.search_timeout_minutes = self.email_config.get('search_timeout_minutes', 10)
        self.max_search_attempts = self.email_config.get('max_search_attempts', 3)
        self.search_interval_seconds = self.email_config.get('search_interval_seconds', 30)
        
        # ChatGPT email patterns
        self.verification_code_patterns = [
            re.compile(r'verification code[:\s]*(\d{6})', re.IGNORECASE),
            re.compile(r'your code[:\s]*(\d{6})', re.IGNORECASE),
            re.compile(r'code[:\s]*(\d{6})', re.IGNORECASE),
            re.compile(r'(\d{6})', re.IGNORECASE)  # Fallback: any 6-digit number
        ]
        
        # Email search criteria
        self.sender_patterns = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        self.subject_patterns = [
            'verification',
            'verify',
            'code',
            'chatgpt',
            'openai'
        ]
        
        # Verification logs cache
        self._verification_logs: Dict[str, EmailVerification] = {}
        self._cache_loaded = False
    
    def initialize(self) -> bool:
        """Initialize the Email Service"""
        try:
            self.logger.info(f"Initializing {self.service_name}...")
            
            # Validate configuration
            if not self._validate_service_config():
                return False
            
            # Load verification logs
            if not self._load_verification_logs():
                self.logger.warning("Failed to load verification logs, starting with empty cache")
                self._verification_logs = {}
            
            self._cache_loaded = True
            
            # Test email connections
            self._test_email_connections()
            
            self._mark_initialized()
            return True
            
        except Exception as e:
            self._handle_service_error("initialize", e)
            return False
    
    def shutdown(self) -> bool:
        """Shutdown the Email Service"""
        try:
            self.logger.info(f"Shutting down {self.service_name}...")
            
            # Save verification logs
            if self._cache_loaded:
                self._save_verification_logs()
            
            # Clear cache
            self._verification_logs.clear()
            self._cache_loaded = False
            
            self._mark_shutdown()
            return True
            
        except Exception as e:
            self._handle_service_error("shutdown", e)
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        self._update_health_check_time()
        
        try:
            health_data = {
                'status': 'healthy',
                'service': self.service_name,
                'cache_loaded': self._cache_loaded,
                'total_credentials': len(self.credentials),
                'total_verification_logs': len(self._verification_logs),
                'timestamp': datetime.now().isoformat()
            }
            
            # Test email connections
            connection_results = []
            for cred in self.credentials[:3]:  # Test first 3 credentials only
                email_addr = cred.get('email', '')
                test_result = self.test_email_connection(email_addr, cred.get('password', ''))
                connection_results.append({
                    'email': email_addr,
                    'connected': test_result
                })
            
            health_data['connection_tests'] = connection_results
            
            # Check for issues
            issues = []
            if not self.credentials:
                issues.append("No email credentials configured")
            if not self._cache_loaded:
                issues.append("Cache not loaded")
            
            failed_connections = sum(1 for r in connection_results if not r['connected'])
            if failed_connections > 0:
                issues.append(f"{failed_connections} email connections failed")
            
            if issues:
                health_data['status'] = 'degraded'
                health_data['issues'] = issues
            
            return health_data
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': self.service_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def search_verification_code(self, account_email: str, redemption_id: str, 
                               timeout_minutes: int = None) -> Dict[str, Any]:
        """
        Search for ChatGPT verification code in email
        
        Args:
            account_email: ChatGPT account email to search for
            redemption_id: Associated redemption ID
            timeout_minutes: Search timeout (uses default if not provided)
            
        Returns:
            Dictionary with search result
        """
        with self.lock:
            try:
                # Create verification log entry
                verification = EmailVerification.create_for_redemption(
                    redemption_id=redemption_id,
                    account_email=account_email,
                    timeout_minutes=timeout_minutes or self.search_timeout_minutes,
                    max_attempts=self.max_search_attempts
                )
                
                self._verification_logs[verification.verification_id] = verification
                
                # Start search
                if not verification.start_search():
                    return {
                        'success': False,
                        'error': 'Cannot start search (max attempts reached or expired)',
                        'verification_id': verification.verification_id
                    }
                
                self._log_operation("start_verification_search", {
                    'verification_id': verification.verification_id,
                    'account_email': account_email,
                    'redemption_id': redemption_id
                })
                
                # Perform search
                search_result = self._perform_verification_search(verification)
                
                # Update verification log
                if search_result['success']:
                    verification.complete_search(
                        verification_code=search_result['verification_code'],
                        email_subject=search_result.get('email_subject', ''),
                        email_sender=search_result.get('email_sender', '')
                    )
                else:
                    verification.fail_search(search_result.get('error', 'Search failed'))
                
                # Save logs
                self._save_verification_logs()
                
                return {
                    'success': search_result['success'],
                    'verification_id': verification.verification_id,
                    'verification_code': search_result.get('verification_code'),
                    'error': search_result.get('error'),
                    'search_attempts': verification.search_attempts,
                    'email_details': search_result.get('email_details', {})
                }
                
            except Exception as e:
                self._handle_service_error("search_verification_code", e)
                return {
                    'success': False,
                    'error': f'Search error: {str(e)}'
                }
    
    def get_verification_status(self, verification_id: str) -> Dict[str, Any]:
        """
        Get verification status
        
        Args:
            verification_id: Verification ID
            
        Returns:
            Dictionary with verification status
        """
        verification = self._verification_logs.get(verification_id)
        if not verification:
            return {
                'found': False,
                'error': 'Verification not found'
            }
        
        return {
            'found': True,
            'verification_id': verification.verification_id,
            'redemption_id': verification.redemption_id,
            'account_email': verification.account_email,
            'status': verification.status.value,
            'search_attempts': verification.search_attempts,
            'verification_code': verification.verification_code,
            'created_at': verification.created_at,
            'expires_at': verification.expires_at,
            'is_expired': verification.is_expired(),
            'can_search': verification.can_search(),
            'error_message': verification.error_message
        }
    
    def test_email_connection(self, email_address: str, password: str) -> bool:
        """
        Test email connection
        
        Args:
            email_address: Email address
            password: Email password/app password
            
        Returns:
            True if connection successful
        """
        try:
            if self.use_ssl:
                mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            else:
                mail = imaplib.IMAP4(self.imap_server, self.imap_port)
            
            mail.login(email_address, password)
            mail.logout()
            
            self.logger.debug(f"Email connection test successful for {email_address}")
            return True
            
        except Exception as e:
            self.logger.warning(f"Email connection test failed for {email_address}: {e}")
            return False
    
    def get_available_email_accounts(self) -> List[str]:
        """
        Get list of available email accounts
        
        Returns:
            List of email addresses
        """
        return [cred.get('email', '') for cred in self.credentials if cred.get('email')]
    
    def _perform_verification_search(self, verification: EmailVerification) -> Dict[str, Any]:
        """
        Perform the actual verification code search
        
        Args:
            verification: EmailVerification object
            
        Returns:
            Dictionary with search result
        """
        try:
            # Find email credentials for the account
            account_creds = None
            for cred in self.credentials:
                if cred.get('email', '').lower() == verification.account_email.lower():
                    account_creds = cred
                    break
            
            if not account_creds:
                return {
                    'success': False,
                    'error': f'No email credentials found for {verification.account_email}'
                }
            
            email_address = account_creds['email']
            password = account_creds['password']
            
            self.logger.info(f"Searching for verification code in {email_address}")
            
            # Connect to IMAP server
            if self.use_ssl:
                mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            else:
                mail = imaplib.IMAP4(self.imap_server, self.imap_port)
            
            # Login
            mail.login(email_address, password)
            mail.select('inbox')
            
            # Search for verification emails
            search_criteria = self._build_search_criteria()
            status, messages = mail.search(None, search_criteria)
            
            if status != 'OK' or not messages[0]:
                mail.logout()
                return {
                    'success': False,
                    'error': 'No verification emails found'
                }
            
            # Get recent emails
            email_ids = messages[0].split()
            recent_emails = email_ids[-10:]  # Check last 10 emails
            
            # Search through emails for verification code
            for email_id in reversed(recent_emails):
                try:
                    status, msg_data = mail.fetch(email_id, '(RFC822)')
                    if status != 'OK':
                        continue
                    
                    email_body = msg_data[0][1]
                    email_message = email.message_from_bytes(email_body)
                    
                    # Check if email is recent enough
                    email_date = self._get_email_date(email_message)
                    if email_date and (datetime.now() - email_date).total_seconds() > (self.search_timeout_minutes * 60):
                        continue
                    
                    # Extract verification code
                    verification_code = self._extract_verification_code(email_message)
                    
                    if verification_code and validate_verification_code(verification_code):
                        mail.logout()
                        
                        return {
                            'success': True,
                            'verification_code': verification_code,
                            'email_subject': email_message.get('Subject', ''),
                            'email_sender': email_message.get('From', ''),
                            'email_details': {
                                'date': email_date.isoformat() if email_date else None,
                                'message_id': email_message.get('Message-ID', '')
                            }
                        }
                        
                except Exception as e:
                    self.logger.warning(f"Error processing email {email_id}: {e}")
                    continue
            
            mail.logout()
            
            return {
                'success': False,
                'error': 'No valid verification code found in recent emails'
            }
            
        except Exception as e:
            self.logger.error(f"Error during verification search: {e}")
            return {
                'success': False,
                'error': f'Search error: {str(e)}'
            }
    
    def _build_search_criteria(self) -> str:
        """Build IMAP search criteria for verification emails"""
        # Search for emails from OpenAI with verification-related subjects
        sender_criteria = ' OR '.join([f'FROM "{sender}"' for sender in self.sender_patterns])
        subject_criteria = ' OR '.join([f'SUBJECT "{subject}"' for subject in self.subject_patterns])
        
        # Combine criteria and limit to recent emails
        criteria = f'({sender_criteria}) ({subject_criteria})'
        
        return criteria
    
    def _extract_verification_code(self, email_message) -> Optional[str]:
        """
        Extract verification code from email message
        
        Args:
            email_message: Email message object
            
        Returns:
            Verification code or None
        """
        try:
            # Get email content
            content = ""
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() in ["text/plain", "text/html"]:
                        payload = part.get_payload(decode=True)
                        if payload:
                            content += payload.decode('utf-8', errors='ignore')
            else:
                payload = email_message.get_payload(decode=True)
                if payload:
                    content = payload.decode('utf-8', errors='ignore')
            
            # Try each pattern to find verification code
            for pattern in self.verification_code_patterns:
                matches = pattern.findall(content)
                if matches:
                    # Return the first valid 6-digit code
                    for match in matches:
                        if validate_verification_code(match):
                            return match
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error extracting verification code: {e}")
            return None
    
    def _get_email_date(self, email_message) -> Optional[datetime]:
        """Get email date from message"""
        try:
            date_str = email_message.get('Date', '')
            if date_str:
                # Parse email date (this is simplified, real implementation might need more robust parsing)
                from email.utils import parsedate_to_datetime
                return parsedate_to_datetime(date_str)
        except Exception as e:
            self.logger.warning(f"Error parsing email date: {e}")
        
        return None
    
    def _validate_service_config(self) -> bool:
        """Validate service configuration"""
        if not self.credentials:
            self.logger.error("No email credentials configured")
            return False
        
        # Validate each credential
        for i, cred in enumerate(self.credentials):
            if not cred.get('email') or not cred.get('password'):
                self.logger.error(f"Invalid credential at index {i}: missing email or password")
                return False
            
            if not validate_email(cred['email']):
                self.logger.error(f"Invalid email format in credential {i}: {cred['email']}")
                return False
        
        return True
    
    def _test_email_connections(self) -> None:
        """Test all configured email connections"""
        for cred in self.credentials:
            email_addr = cred.get('email', '')
            password = cred.get('password', '')
            
            if self.test_email_connection(email_addr, password):
                self.logger.info(f"Email connection verified for {email_addr}")
            else:
                self.logger.warning(f"Email connection failed for {email_addr}")
    
    def _load_verification_logs(self) -> bool:
        """Load verification logs from storage"""
        try:
            data = load_json_data(self.data_file)
            logs_data = data.get('verification_logs', [])
            
            self._verification_logs.clear()
            
            for log_data in logs_data:
                try:
                    verification = EmailVerification.from_dict(log_data)
                    self._verification_logs[verification.verification_id] = verification
                except Exception as e:
                    self.logger.warning(f"Failed to load verification log: {e}")
            
            self.logger.info(f"Loaded {len(self._verification_logs)} verification logs")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load verification logs: {e}")
            return False
    
    def _save_verification_logs(self) -> bool:
        """Save verification logs to storage"""
        try:
            logs_data = [log.to_dict() for log in self._verification_logs.values()]
            
            data = {
                'verification_logs': logs_data,
                'metadata': {
                    'version': '1.0.0',
                    'total_logs': len(logs_data),
                    'last_updated': datetime.now().isoformat()
                }
            }
            
            save_json_data(self.data_file, data)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save verification logs: {e}")
            return False
