{% extends "base.html" %}

{% block title %}Shopee Auto Boost Dashboard{% endblock %}

{% block header %}Shopee Auto Boost Dashboard{% endblock %}

{% block content %}
<div class="container-fluid" x-data="shopeeAutoBoostDashboard()">
    <!-- Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">Scheduler Status</h4>
                            <p class="card-text" x-text="status.scheduler_running ? 'Running' : 'Stopped'"></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">Total Boost Sessions</h4>
                            <p class="card-text" x-text="status.total_boost_sessions || 0"></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-rocket fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">Next Boost</h4>
                            <p class="card-text" x-text="formatDateTime(status.next_boost_time) || 'Not scheduled'"></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">Last Boost</h4>
                            <p class="card-text" x-text="formatDateTime(status.last_boost_time) || 'Never'"></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-history fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Panel -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Control Panel</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <button class="btn btn-success" 
                                        @click="triggerManualBoost()" 
                                        :disabled="loading">
                                    <i class="fas fa-rocket me-2"></i>
                                    <span x-show="!loading">Trigger Manual Boost</span>
                                    <span x-show="loading">Boosting...</span>
                                </button>
                                
                                <button class="btn btn-primary" 
                                        @click="status.scheduler_running ? stopScheduler() : startScheduler()"
                                        :disabled="loading">
                                    <i :class="status.scheduler_running ? 'fas fa-stop' : 'fas fa-play'" class="me-2"></i>
                                    <span x-text="status.scheduler_running ? 'Stop Scheduler' : 'Start Scheduler'"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Current Configuration</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Boost Interval:</strong> <span x-text="status.config?.boost_interval_hours || 4"></span> hours</li>
                                        <li><strong>Products per Boost:</strong> <span x-text="status.config?.products_per_boost || 5"></span></li>
                                        <li><strong>Strategy:</strong> <span x-text="status.config?.rotation_strategy || 'least_recently_boosted'"></span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recent Activity</h5>
                </div>
                <div class="card-body">
                    <div x-show="recentActivity.length === 0" class="text-center text-muted">
                        No recent activity
                    </div>
                    
                    <div x-show="recentActivity.length > 0">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Action</th>
                                        <th>Status</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="activity in recentActivity" :key="activity.id">
                                        <tr>
                                            <td x-text="formatDateTime(activity.timestamp)"></td>
                                            <td x-text="activity.action"></td>
                                            <td>
                                                <span :class="activity.success ? 'badge bg-success' : 'badge bg-danger'" 
                                                      x-text="activity.success ? 'Success' : 'Failed'"></span>
                                            </td>
                                            <td x-text="activity.details"></td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div x-show="alertMessage" 
         :class="'alert alert-' + alertType + ' alert-dismissible fade show'" 
         role="alert">
        <span x-text="alertMessage"></span>
        <button type="button" class="btn-close" @click="alertMessage = ''" aria-label="Close"></button>
    </div>
</div>

<script>
function shopeeAutoBoostDashboard() {
    return {
        status: {},
        recentActivity: [],
        loading: false,
        alertMessage: '',
        alertType: 'info',
        
        init() {
            this.loadStatus();
            // Refresh status every 30 seconds
            setInterval(() => this.loadStatus(), 30000);
        },
        
        async loadStatus() {
            try {
                const response = await fetch('/api/shopee_auto_boost/status');
                if (response.ok) {
                    this.status = await response.json();
                }
            } catch (error) {
                console.error('Error loading status:', error);
            }
        },
        
        async triggerManualBoost() {
            this.loading = true;
            try {
                const response = await fetch('/api/shopee_auto_boost/boost/manual', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showAlert('Manual boost completed successfully!', 'success');
                    this.addActivity('Manual Boost', true, `Boosted ${result.results?.success?.length || 0} products`);
                } else {
                    this.showAlert('Manual boost failed: ' + result.message, 'danger');
                    this.addActivity('Manual Boost', false, result.message);
                }
                
                this.loadStatus();
            } catch (error) {
                this.showAlert('Error triggering manual boost: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },
        
        async startScheduler() {
            this.loading = true;
            try {
                const response = await fetch('/api/shopee_auto_boost/scheduler/start', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showAlert('Scheduler started successfully!', 'success');
                    this.addActivity('Start Scheduler', true, 'Auto-boost scheduler started');
                } else {
                    this.showAlert('Failed to start scheduler: ' + result.message, 'danger');
                }
                
                this.loadStatus();
            } catch (error) {
                this.showAlert('Error starting scheduler: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },
        
        async stopScheduler() {
            this.loading = true;
            try {
                const response = await fetch('/api/shopee_auto_boost/scheduler/stop', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showAlert('Scheduler stopped successfully!', 'success');
                    this.addActivity('Stop Scheduler', true, 'Auto-boost scheduler stopped');
                } else {
                    this.showAlert('Failed to stop scheduler: ' + result.message, 'danger');
                }
                
                this.loadStatus();
            } catch (error) {
                this.showAlert('Error stopping scheduler: ' + error.message, 'danger');
            } finally {
                this.loading = false;
            }
        },
        
        formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString();
        },
        
        showAlert(message, type) {
            this.alertMessage = message;
            this.alertType = type;
            setTimeout(() => this.alertMessage = '', 5000);
        },
        
        addActivity(action, success, details) {
            this.recentActivity.unshift({
                id: Date.now(),
                timestamp: new Date().toISOString(),
                action: action,
                success: success,
                details: details
            });
            
            // Keep only last 10 activities
            if (this.recentActivity.length > 10) {
                this.recentActivity = this.recentActivity.slice(0, 10);
            }
        }
    }
}
</script>
{% endblock %}
