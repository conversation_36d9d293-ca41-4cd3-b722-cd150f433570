"""
Enhanced Fake Order API Routes

This module provides comprehensive API endpoints for the Universal Fake Order System,
including advanced order generation, batch processing, template management, and order management.
"""

from flask import Blueprint, jsonify, request
from services.fake_order_generator_service import fake_order_service, FakeOrderConfig
from services.order_data_factory import order_data_factory, OrderItemConfig, BuyerConfig
from services.product_template_engine import product_template_engine, ProductTemplate
from utils.auth import require_api_key
from utils.fake_order_security import (
    fake_order_security, 
    require_fake_order_permission, 
    log_fake_order_operation
)
from dataclasses import asdict
from datetime import datetime, timedelta
import json
import logging

logger = logging.getLogger(__name__)

fake_order_bp = Blueprint('fake_order', __name__, url_prefix='/api/fake-orders')

@fake_order_bp.route('/generate', methods=['POST'])
@require_api_key
@require_fake_order_permission('create_fake_orders')
@log_fake_order_operation('generate_fake_order')
def api_generate_fake_order():
    """
    Advanced fake order creation endpoint with full configuration support
    
    Expected payload:
    {
        "order_sn": "FAKE_ORDER_001",  // optional, will be generated if not provided
        "product_config": {
            "var_sku": "canva_30",
            "quantity": 1,
            "price_override": 15.00,  // optional
            "custom_metadata": {}     // optional
        },
        "buyer_config": {
            "username": "test_user",
            "name": "Test Customer",
            "phone": "+60123456789",
            "email": "<EMAIL>",
            "address": "123 Test Street, Test City"
        },
        "order_config": {
            "status": "To Ship",
            "payment_status": "paid",
            "created_date": "2025-01-15T10:30:00Z",  // optional
            "custom_timestamps": {},                  // optional
            "custom_metadata": {}                     // optional
        },
        "test_scenario": "canva_pro_basic_flow"
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                "success": False,
                "error": "JSON payload is required",
                "error_code": "MISSING_PAYLOAD"
            }), 400

        # Validate required sections
        if 'product_config' not in data:
            return jsonify({
                "success": False,
                "error": "product_config is required",
                "error_code": "MISSING_PRODUCT_CONFIG"
            }), 400

        product_config = data['product_config']
        if 'var_sku' not in product_config:
            return jsonify({
                "success": False,
                "error": "var_sku is required in product_config",
                "error_code": "MISSING_VAR_SKU"
            }), 400

        # Extract configurations with defaults
        buyer_config = data.get('buyer_config', {})
        order_config = data.get('order_config', {})
        
        # Create FakeOrderConfig for the generator service
        fake_order_config = FakeOrderConfig(
            order_sn=data.get('order_sn', ''),  # Will be generated if empty
            var_sku=product_config['var_sku'],
            buyer_username=buyer_config.get('username', 'test_user'),
            buyer_name=buyer_config.get('name', 'Test Customer'),
            buyer_phone=buyer_config.get('phone', '+60123456789'),
            buyer_email=buyer_config.get('email', '<EMAIL>'),
            buyer_address=buyer_config.get('address', '123 Test Street, Test City'),
            status=order_config.get('status', 'To Ship'),
            payment_status=order_config.get('payment_status', 'paid'),
            quantity=product_config.get('quantity', 1),
            custom_metadata=product_config.get('custom_metadata', {}),
            test_scenario=data.get('test_scenario', 'general_testing'),
            created_date=order_config.get('created_date'),
            price_override=product_config.get('price_override')
        )

        # Validate configuration before generation
        validation = fake_order_service.validate_order_config(fake_order_config)
        if not validation['is_valid']:
            return jsonify({
                "success": False,
                "error": "Invalid order configuration",
                "error_code": "VALIDATION_FAILED",
                "validation_errors": validation['errors'],
                "validation_warnings": validation.get('warnings', [])
            }), 400

        # Generate the fake order
        order_data = fake_order_service.generate_order(fake_order_config)
        
        # Prepare response with processing options
        response = {
            "success": True,
            "message": f"Fake order {order_data['data']['order_sn']} created successfully",
            "order_data": order_data,
            "order_sn": order_data['data']['order_sn'],
            "processing_options": {
                "process_order_url": f"/api/order/process_order",
                "get_order_details_url": f"/api/order/get_order_details?order_sn={order_data['data']['order_sn']}",
                "get_order_status_url": f"/api/order/get_order_status?order_sn={order_data['data']['order_sn']}"
            },
            "validation_warnings": validation.get('warnings', []),
            "generated_at": datetime.now().isoformat()
        }

        logger.info(f"Generated fake order via API: {order_data['data']['order_sn']}")
        return jsonify(response), 201

    except ValueError as e:
        logger.error(f"Validation error in fake order generation: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "error_code": "VALIDATION_ERROR"
        }), 400
    except Exception as e:
        logger.error(f"Failed to generate fake order: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to generate fake order: {str(e)}",
            "error_code": "GENERATION_FAILED"
        }), 500


@fake_order_bp.route('/batch-generate', methods=['POST'])
@require_api_key
@require_fake_order_permission('bulk_operations')
@log_fake_order_operation('batch_generate_fake_orders')
def api_batch_generate_fake_orders():
    """
    Batch order generation endpoint for multiple orders
    
    Expected payload:
    {
        "orders": [
            {
                "order_sn": "FAKE_BATCH_001",  // optional
                "product_config": { "var_sku": "canva_30", "quantity": 1 },
                "buyer_config": { "username": "user1" },
                "order_config": { "status": "To Ship" },
                "test_scenario": "batch_test_1"
            },
            // ... more orders
        ],
        "batch_config": {
            "continue_on_error": true,
            "generate_unique_order_sns": true,
            "default_test_scenario": "batch_testing"
        }
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                "success": False,
                "error": "JSON payload is required",
                "error_code": "MISSING_PAYLOAD"
            }), 400

        if 'orders' not in data or not isinstance(data['orders'], list):
            return jsonify({
                "success": False,
                "error": "orders array is required",
                "error_code": "MISSING_ORDERS_ARRAY"
            }), 400

        orders_config = data['orders']
        batch_config = data.get('batch_config', {})
        
        if len(orders_config) == 0:
            return jsonify({
                "success": False,
                "error": "At least one order configuration is required",
                "error_code": "EMPTY_ORDERS_ARRAY"
            }), 400

        # Validate batch size
        max_batch_size = 50  # Reasonable limit for batch processing
        if len(orders_config) > max_batch_size:
            return jsonify({
                "success": False,
                "error": f"Batch size exceeds maximum limit of {max_batch_size}",
                "error_code": "BATCH_SIZE_EXCEEDED"
            }), 400

        # Convert to FakeOrderConfig objects
        fake_order_configs = []
        validation_errors = []
        
        for i, order_data in enumerate(orders_config):
            try:
                # Extract configurations with defaults
                product_config = order_data.get('product_config', {})
                buyer_config = order_data.get('buyer_config', {})
                order_config = order_data.get('order_config', {})
                
                if 'var_sku' not in product_config:
                    validation_errors.append({
                        "index": i,
                        "error": "var_sku is required in product_config"
                    })
                    continue

                # Generate unique order SN if requested and not provided
                order_sn = order_data.get('order_sn', '')
                if batch_config.get('generate_unique_order_sns', True) and not order_sn:
                    order_sn = fake_order_service.generate_unique_order_sn(f"FAKE_BATCH_{i:03d}_")

                fake_order_config = FakeOrderConfig(
                    order_sn=order_sn,
                    var_sku=product_config['var_sku'],
                    buyer_username=buyer_config.get('username', f'batch_user_{i:03d}'),
                    buyer_name=buyer_config.get('name', f'Batch Customer {i+1}'),
                    buyer_phone=buyer_config.get('phone', '+60123456789'),
                    buyer_email=buyer_config.get('email', f'batch_user_{i:03d}@example.com'),
                    buyer_address=buyer_config.get('address', f'Batch Address {i+1}'),
                    status=order_config.get('status', 'To Ship'),
                    payment_status=order_config.get('payment_status', 'paid'),
                    quantity=product_config.get('quantity', 1),
                    custom_metadata=product_config.get('custom_metadata', {}),
                    test_scenario=order_data.get('test_scenario', batch_config.get('default_test_scenario', 'batch_testing')),
                    created_date=order_config.get('created_date'),
                    price_override=product_config.get('price_override')
                )
                
                fake_order_configs.append(fake_order_config)
                
            except Exception as e:
                validation_errors.append({
                    "index": i,
                    "error": f"Failed to parse order configuration: {str(e)}"
                })

        # Return validation errors if any and continue_on_error is False
        if validation_errors and not batch_config.get('continue_on_error', True):
            return jsonify({
                "success": False,
                "error": "Batch validation failed",
                "error_code": "BATCH_VALIDATION_FAILED",
                "validation_errors": validation_errors
            }), 400

        # Generate batch orders
        batch_result = fake_order_service.generate_batch_orders(fake_order_configs)
        
        # Prepare response with progress tracking
        successful_orders = batch_result['successful_orders']
        failed_orders = batch_result['failed_orders']
        
        # Add validation errors to failed orders
        for error in validation_errors:
            failed_orders.append({
                "index": error["index"],
                "success": False,
                "error": error["error"],
                "config": orders_config[error["index"]] if error["index"] < len(orders_config) else {}
            })

        response = {
            "success": len(successful_orders) > 0,
            "message": f"Batch generation completed: {len(successful_orders)} successful, {len(failed_orders)} failed",
            "batch_summary": {
                "total_requested": len(orders_config),
                "successful": len(successful_orders),
                "failed": len(failed_orders),
                "success_rate": len(successful_orders) / len(orders_config) * 100 if orders_config else 0
            },
            "successful_orders": successful_orders,
            "failed_orders": failed_orders,
            "processing_options": {
                "bulk_process_url": "/api/order/process_order",
                "batch_status_check": "/api/fake-orders/list"
            },
            "generated_at": datetime.now().isoformat()
        }

        status_code = 201 if len(successful_orders) > 0 else 400
        logger.info(f"Batch generated {len(successful_orders)} fake orders, {len(failed_orders)} failed")
        
        return jsonify(response), status_code

    except Exception as e:
        logger.error(f"Failed to generate batch fake orders: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to generate batch fake orders: {str(e)}",
            "error_code": "BATCH_GENERATION_FAILED"
        }), 500


@fake_order_bp.route('/templates', methods=['GET'])
@require_api_key
def api_get_templates():
    """
    Get available product templates
    
    Query parameters:
    - category: Filter by category (optional)
    - include_sample_data: Include sample data in response (default: false)
    """
    try:
        category_filter = request.args.get('category')
        include_sample_data = request.args.get('include_sample_data', 'false').lower() == 'true'
        
        if category_filter:
            templates = product_template_engine.get_templates_by_category(category_filter)
        else:
            templates = product_template_engine.get_all_templates()
        
        # Convert templates to response format
        templates_data = {}
        for sku, template in templates.items():
            template_data = {
                "var_sku": template.var_sku,
                "product_name": template.product_name,
                "category": template.category,
                "default_price": template.default_price,
                "metadata_schema": template.metadata_schema,
                "validation_rules": template.validation_rules
            }
            
            if include_sample_data:
                template_data["sample_data"] = template.sample_data
            
            templates_data[sku] = template_data
        
        # Get template summary
        summary = product_template_engine.get_template_summary()
        
        response = {
            "success": True,
            "templates": templates_data,
            "summary": summary,
            "filters_applied": {
                "category": category_filter,
                "include_sample_data": include_sample_data
            },
            "retrieved_at": datetime.now().isoformat()
        }
        
        return jsonify(response), 200

    except Exception as e:
        logger.error(f"Failed to get templates: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to get templates: {str(e)}",
            "error_code": "TEMPLATES_RETRIEVAL_FAILED"
        }), 500


@fake_order_bp.route('/templates/save', methods=['POST'])
@require_api_key
def api_save_custom_template():
    """
    Save custom order template
    
    Expected payload:
    {
        "var_sku": "custom_product_123",
        "product_name": "Custom Product Name",
        "category": "custom",
        "default_price": 25.00,
        "metadata_schema": {
            "custom_field": {"type": "str", "allowed_values": ["value1", "value2"]}
        },
        "validation_rules": {
            "required_fields": ["custom_field"],
            "optional_fields": []
        },
        "sample_data": {
            "custom_field": "value1",
            "description": "Sample custom product"
        }
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                "success": False,
                "error": "JSON payload is required",
                "error_code": "MISSING_PAYLOAD"
            }), 400

        # Validate required fields
        required_fields = ['var_sku', 'product_name', 'category', 'default_price']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "error": f"Required field '{field}' is missing",
                    "error_code": "MISSING_REQUIRED_FIELD"
                }), 400

        # Create ProductTemplate
        template = ProductTemplate(
            var_sku=data['var_sku'],
            product_name=data['product_name'],
            category=data['category'],
            default_price=data['default_price'],
            metadata_schema=data.get('metadata_schema', {}),
            validation_rules=data.get('validation_rules', {}),
            sample_data=data.get('sample_data', {})
        )

        # Register the template
        product_template_engine.register_product_template(template, save_to_file=True)
        
        response = {
            "success": True,
            "message": f"Custom template '{data['var_sku']}' saved successfully",
            "template": template.to_dict(),
            "saved_at": datetime.now().isoformat()
        }
        
        logger.info(f"Saved custom template: {data['var_sku']}")
        return jsonify(response), 201

    except ValueError as e:
        logger.error(f"Template validation error: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "error_code": "TEMPLATE_VALIDATION_FAILED"
        }), 400
    except Exception as e:
        logger.error(f"Failed to save custom template: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to save custom template: {str(e)}",
            "error_code": "TEMPLATE_SAVE_FAILED"
        }), 500


@fake_order_bp.route('/list', methods=['GET'])
@require_api_key
def api_list_fake_orders():
    """
    List fake orders with filtering and pagination
    
    Query parameters:
    - limit: Maximum number of orders to return (default: 50)
    - status: Filter by order status
    - var_sku: Filter by product SKU
    - test_scenario: Filter by test scenario
    - created_after: Filter orders created after this date (ISO format)
    - created_before: Filter orders created before this date (ISO format)
    """
    try:
        # Parse query parameters
        limit = int(request.args.get('limit', 50))
        status_filter = request.args.get('status')
        sku_filter = request.args.get('var_sku')
        scenario_filter = request.args.get('test_scenario')
        created_after = request.args.get('created_after')
        created_before = request.args.get('created_before')
        
        # Validate limit
        if limit > 200:
            limit = 200  # Cap at reasonable maximum
        
        # Get fake orders from service
        fake_orders = fake_order_service.get_fake_orders(
            limit=None,  # We'll apply limit after additional filtering
            status_filter=status_filter,
            sku_filter=sku_filter
        )
        
        # Apply additional filters
        filtered_orders = []
        for order in fake_orders:
            # Test scenario filter
            if scenario_filter and order.get('test_scenario') != scenario_filter:
                continue
            
            # Date filters
            if created_after or created_before:
                try:
                    order_date = datetime.fromisoformat(order.get('created_at', ''))
                    
                    if created_after:
                        after_date = datetime.fromisoformat(created_after)
                        if order_date < after_date:
                            continue
                    
                    if created_before:
                        before_date = datetime.fromisoformat(created_before)
                        if order_date > before_date:
                            continue
                            
                except (ValueError, TypeError):
                    # Skip orders with invalid dates
                    continue
            
            filtered_orders.append(order)
        
        # Apply limit
        if limit:
            filtered_orders = filtered_orders[:limit]
        
        # Prepare response with metadata
        response = {
            "success": True,
            "fake_orders": filtered_orders,
            "count": len(filtered_orders),
            "filters_applied": {
                "limit": limit,
                "status": status_filter,
                "var_sku": sku_filter,
                "test_scenario": scenario_filter,
                "created_after": created_after,
                "created_before": created_before
            },
            "management_options": {
                "bulk_delete_url": "/api/fake-orders/cleanup",
                "individual_delete_url": "/api/order/delete_fake_order/{order_sn}",
                "process_order_url": "/api/order/process_order"
            },
            "retrieved_at": datetime.now().isoformat()
        }
        
        return jsonify(response), 200

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": f"Invalid parameter: {str(e)}",
            "error_code": "INVALID_PARAMETER"
        }), 400
    except Exception as e:
        logger.error(f"Failed to list fake orders: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to list fake orders: {str(e)}",
            "error_code": "LIST_FAILED"
        }), 500


@fake_order_bp.route('/cleanup', methods=['DELETE'])
@require_api_key
@require_fake_order_permission('delete_fake_orders')
@log_fake_order_operation('cleanup_fake_orders')
def api_cleanup_fake_orders():
    """
    Bulk delete fake orders
    
    Query parameters:
    - older_than_days: Delete orders older than specified days (default: 7)
    - status: Delete orders with specific status
    - test_scenario: Delete orders with specific test scenario
    - confirm: Must be 'true' to proceed with deletion
    
    Or JSON payload for specific order SNs:
    {
        "order_sns": ["FAKE_ORDER_001", "FAKE_ORDER_002"],
        "confirm": true
    }
    """
    try:
        # Check confirmation
        confirm_param = request.args.get('confirm', 'false').lower() == 'true'
        
        # Check for JSON payload with specific order SNs
        data = request.json
        if data and 'order_sns' in data:
            if not data.get('confirm', False):
                return jsonify({
                    "success": False,
                    "error": "Confirmation required for bulk deletion",
                    "error_code": "CONFIRMATION_REQUIRED"
                }), 400
            
            # Delete specific orders
            order_sns = data['order_sns']
            if not isinstance(order_sns, list) or len(order_sns) == 0:
                return jsonify({
                    "success": False,
                    "error": "order_sns must be a non-empty array",
                    "error_code": "INVALID_ORDER_SNS"
                }), 400
            
            # Load current fake orders
            fake_orders = fake_order_service.get_fake_orders()
            original_count = len(fake_orders)
            
            # Filter out specified orders
            remaining_orders = [
                order for order in fake_orders 
                if order.get('order_sn') not in order_sns
            ]
            
            deleted_count = original_count - len(remaining_orders)
            
            # Save filtered orders (this would need to be implemented in the service)
            # For now, we'll return the result without actually deleting
            response = {
                "success": True,
                "message": f"Would delete {deleted_count} specific fake orders",
                "cleanup_summary": {
                    "original_count": original_count,
                    "deleted_count": deleted_count,
                    "remaining_count": len(remaining_orders),
                    "deletion_method": "specific_order_sns"
                },
                "deleted_order_sns": [sn for sn in order_sns if any(order.get('order_sn') == sn for order in fake_orders)],
                "not_found_order_sns": [sn for sn in order_sns if not any(order.get('order_sn') == sn for order in fake_orders)],
                "cleaned_at": datetime.now().isoformat()
            }
            
            logger.info(f"Specific cleanup would delete {deleted_count} fake orders")
            return jsonify(response), 200
        
        # Parameter-based cleanup
        if not confirm_param:
            return jsonify({
                "success": False,
                "error": "Confirmation required for bulk deletion. Add ?confirm=true to proceed",
                "error_code": "CONFIRMATION_REQUIRED"
            }), 400
        
        # Parse cleanup parameters
        older_than_days = int(request.args.get('older_than_days', 7))
        status_filter = request.args.get('status')
        scenario_filter = request.args.get('test_scenario')
        
        # Use service cleanup for date-based cleanup
        if not status_filter and not scenario_filter:
            cleanup_result = fake_order_service.cleanup_fake_orders(older_than_days)
            
            response = {
                "success": True,
                "message": f"Cleaned up {cleanup_result['removed_count']} fake orders older than {older_than_days} days",
                "cleanup_summary": cleanup_result,
                "cleanup_method": "date_based",
                "cleaned_at": datetime.now().isoformat()
            }
            
            logger.info(f"Date-based cleanup removed {cleanup_result['removed_count']} fake orders")
            return jsonify(response), 200
        
        # Custom filter-based cleanup
        fake_orders = fake_order_service.get_fake_orders()
        original_count = len(fake_orders)
        
        # Apply filters to determine what to delete
        orders_to_keep = []
        for order in fake_orders:
            should_delete = True
            
            if status_filter and order.get('status') != status_filter:
                should_delete = False
            
            if scenario_filter and order.get('test_scenario') != scenario_filter:
                should_delete = False
            
            if not should_delete:
                orders_to_keep.append(order)
        
        deleted_count = original_count - len(orders_to_keep)
        
        response = {
            "success": True,
            "message": f"Would delete {deleted_count} fake orders matching filters",
            "cleanup_summary": {
                "original_count": original_count,
                "deleted_count": deleted_count,
                "remaining_count": len(orders_to_keep),
                "deletion_method": "filter_based"
            },
            "filters_applied": {
                "status": status_filter,
                "test_scenario": scenario_filter
            },
            "cleaned_at": datetime.now().isoformat()
        }
        
        logger.info(f"Filter-based cleanup would delete {deleted_count} fake orders")
        return jsonify(response), 200

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": f"Invalid parameter: {str(e)}",
            "error_code": "INVALID_PARAMETER"
        }), 400
    except Exception as e:
        logger.error(f"Failed to cleanup fake orders: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to cleanup fake orders: {str(e)}",
            "error_code": "CLEANUP_FAILED"
        }), 500


@fake_order_bp.route('/maintenance/run', methods=['POST'])
@require_api_key
@require_fake_order_permission('maintenance_operations')
@log_fake_order_operation('run_maintenance')
def api_run_maintenance():
    """
    Run maintenance operations manually
    
    Expected payload:
    {
        "maintenance_type": "cleanup|optimization|archive|full",
        "config": {
            "older_than_days": 7,
            "processed_only": false,
            "max_orders_per_cleanup": 1000,
            "archive_before_delete": true
        }
    }
    """
    try:
        from services.fake_order_maintenance import fake_order_maintenance, CleanupConfig
        
        data = request.json or {}
        maintenance_type = data.get('maintenance_type', 'cleanup')
        config_data = data.get('config', {})
        
        # Create cleanup configuration
        config = CleanupConfig(
            older_than_days=config_data.get('older_than_days', 7),
            processed_only=config_data.get('processed_only', False),
            max_orders_per_cleanup=config_data.get('max_orders_per_cleanup', 1000),
            preserve_recent_processed=config_data.get('preserve_recent_processed', True),
            preserve_error_orders=config_data.get('preserve_error_orders', True),
            archive_before_delete=config_data.get('archive_before_delete', True),
            cleanup_templates=config_data.get('cleanup_templates', False),
            cleanup_logs=config_data.get('cleanup_logs', True),
            log_retention_days=config_data.get('log_retention_days', 30)
        )
        
        if maintenance_type == 'full':
            # Run full maintenance
            stats = fake_order_maintenance.run_scheduled_cleanup(config)
            
        elif maintenance_type == 'cleanup':
            # Run cleanup only
            cleanup_stats = fake_order_maintenance._cleanup_old_orders(config)
            stats = type('Stats', (), {
                'cleanup_stats': cleanup_stats,
                'archive_stats': {},
                'optimization_stats': {},
                'template_stats': {},
                'total_runtime_seconds': 0,
                'errors': [],
                'warnings': []
            })()
            
        elif maintenance_type == 'optimization':
            # Run optimization only
            optimization_stats = fake_order_maintenance._optimize_storage()
            stats = type('Stats', (), {
                'cleanup_stats': {},
                'archive_stats': {},
                'optimization_stats': optimization_stats,
                'template_stats': {},
                'total_runtime_seconds': 0,
                'errors': [],
                'warnings': []
            })()
            
        elif maintenance_type == 'archive':
            # Run archival only
            archive_stats = fake_order_maintenance._archive_old_orders(config)
            stats = type('Stats', (), {
                'cleanup_stats': {},
                'archive_stats': archive_stats,
                'optimization_stats': {},
                'template_stats': {},
                'total_runtime_seconds': 0,
                'errors': [],
                'warnings': []
            })()
            
        else:
            return jsonify({
                "success": False,
                "error": f"Invalid maintenance type: {maintenance_type}",
                "error_code": "INVALID_MAINTENANCE_TYPE",
                "valid_types": ["cleanup", "optimization", "archive", "full"]
            }), 400
        
        response = {
            "success": True,
            "message": f"Maintenance operation '{maintenance_type}' completed",
            "maintenance_type": maintenance_type,
            "stats": {
                "cleanup_stats": stats.cleanup_stats,
                "archive_stats": stats.archive_stats,
                "optimization_stats": stats.optimization_stats,
                "template_stats": stats.template_stats,
                "total_runtime_seconds": stats.total_runtime_seconds,
                "errors": stats.errors,
                "warnings": stats.warnings
            },
            "config_used": config.__dict__,
            "executed_at": datetime.now().isoformat()
        }
        
        logger.info(f"Manual maintenance '{maintenance_type}' completed")
        return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Failed to run maintenance: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to run maintenance: {str(e)}",
            "error_code": "MAINTENANCE_FAILED"
        }), 500


@fake_order_bp.route('/maintenance/status', methods=['GET'])
@require_api_key
def api_get_maintenance_status():
    """Get maintenance system status and statistics"""
    try:
        from services.fake_order_maintenance import fake_order_maintenance
        
        status = fake_order_maintenance.get_maintenance_status()
        
        response = {
            "success": True,
            "maintenance_status": status,
            "retrieved_at": datetime.now().isoformat()
        }
        
        return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Failed to get maintenance status: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to get maintenance status: {str(e)}",
            "error_code": "STATUS_RETRIEVAL_FAILED"
        }), 500


@fake_order_bp.route('/maintenance/config', methods=['GET', 'POST'])
@require_api_key
@require_fake_order_permission('maintenance_operations')
def api_maintenance_config():
    """Get or update maintenance configuration"""
    try:
        from services.fake_order_maintenance import fake_order_maintenance
        
        if request.method == 'GET':
            # Get current configuration
            config = fake_order_maintenance.default_config
            
            response = {
                "success": True,
                "config": {
                    "older_than_days": config.older_than_days,
                    "processed_only": config.processed_only,
                    "max_orders_per_cleanup": config.max_orders_per_cleanup,
                    "preserve_recent_processed": config.preserve_recent_processed,
                    "preserve_error_orders": config.preserve_error_orders,
                    "archive_before_delete": config.archive_before_delete,
                    "cleanup_templates": config.cleanup_templates,
                    "cleanup_logs": config.cleanup_logs,
                    "log_retention_days": config.log_retention_days
                },
                "retrieved_at": datetime.now().isoformat()
            }
            
            return jsonify(response), 200
            
        else:  # POST - Update configuration
            data = request.json
            if not data:
                return jsonify({
                    "success": False,
                    "error": "JSON payload is required",
                    "error_code": "MISSING_PAYLOAD"
                }), 400
            
            # Update configuration
            success = fake_order_maintenance.update_maintenance_config(data)
            
            if success:
                response = {
                    "success": True,
                    "message": "Maintenance configuration updated successfully",
                    "updated_config": data,
                    "updated_at": datetime.now().isoformat()
                }
                return jsonify(response), 200
            else:
                return jsonify({
                    "success": False,
                    "error": "Failed to update maintenance configuration",
                    "error_code": "CONFIG_UPDATE_FAILED"
                }), 500
                
    except Exception as e:
        logger.error(f"Failed to handle maintenance config: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to handle maintenance config: {str(e)}",
            "error_code": "CONFIG_OPERATION_FAILED"
        }), 500


@fake_order_bp.route('/scheduler/status', methods=['GET'])
@require_api_key
def api_get_scheduler_status():
    """Get scheduled task status"""
    try:
        from scheduler.fake_order_scheduler import fake_order_scheduler
        
        task_name = request.args.get('task_name')
        status = fake_order_scheduler.get_task_status(task_name)
        
        response = {
            "success": True,
            "scheduler_status": status,
            "retrieved_at": datetime.now().isoformat()
        }
        
        return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Failed to get scheduler status: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to get scheduler status: {str(e)}",
            "error_code": "SCHEDULER_STATUS_FAILED"
        }), 500


@fake_order_bp.route('/scheduler/task/<task_name>/run', methods=['POST'])
@require_api_key
@require_fake_order_permission('maintenance_operations')
def api_run_scheduled_task(task_name: str):
    """Run a scheduled task immediately"""
    try:
        from scheduler.fake_order_scheduler import fake_order_scheduler
        
        result = fake_order_scheduler.run_task_now(task_name)
        
        if result.get('success'):
            return jsonify(result), 200
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Failed to run scheduled task {task_name}: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to run scheduled task: {str(e)}",
            "error_code": "TASK_EXECUTION_FAILED"
        }), 500


@fake_order_bp.route('/scheduler/task/<task_name>/toggle', methods=['POST'])
@require_api_key
@require_fake_order_permission('maintenance_operations')
def api_toggle_scheduled_task(task_name: str):
    """Enable or disable a scheduled task"""
    try:
        from scheduler.fake_order_scheduler import fake_order_scheduler
        
        data = request.json or {}
        enable = data.get('enable', True)
        
        if enable:
            success = fake_order_scheduler.enable_task(task_name)
            action = "enabled"
        else:
            success = fake_order_scheduler.disable_task(task_name)
            action = "disabled"
        
        if success:
            response = {
                "success": True,
                "message": f"Task '{task_name}' {action} successfully",
                "task_name": task_name,
                "enabled": enable,
                "updated_at": datetime.now().isoformat()
            }
            return jsonify(response), 200
        else:
            return jsonify({
                "success": False,
                "error": f"Failed to {action.rstrip('d')} task '{task_name}'",
                "error_code": "TASK_TOGGLE_FAILED"
            }), 400
            
    except Exception as e:
        logger.error(f"Failed to toggle task {task_name}: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to toggle task: {str(e)}",
            "error_code": "TASK_TOGGLE_ERROR"
        }), 500


# Health check endpoint for the fake order system
@fake_order_bp.route('/health', methods=['GET'])
def api_fake_order_health():
    """Health check for fake order system with security statistics"""
    try:
        # Check service availability
        templates_count = len(product_template_engine.get_all_templates())
        fake_orders = fake_order_service.get_fake_orders(limit=None)
        fake_orders_count = len(fake_orders)
        
        # Calculate today's fake orders
        today = datetime.now().date()
        fake_orders_today = sum(1 for order in fake_orders 
                               if datetime.fromisoformat(order.get('created_at', '')).date() == today)
        
        # Get security statistics
        security_stats = fake_order_security.get_fake_order_statistics()
        
        health_status = {
            "status": "healthy",
            "service": "fake_order_system",
            "components": {
                "fake_order_generator": "operational",
                "product_template_engine": "operational",
                "order_data_factory": "operational",
                "security_manager": "operational"
            },
            "statistics": {
                "available_templates": templates_count,
                "total_fake_orders": fake_orders_count,
                "fake_orders_today": fake_orders_today,
                "security_level": security_stats.get('security_level', 'high'),
                "isolation_status": security_stats.get('isolation_status', 'active')
            },
            "security_features": {
                "fake_order_identification": "active",
                "visual_indicators": "enabled",
                "audit_logging": "enabled",
                "access_control": "enabled",
                "production_isolation": "enabled"
            },
            "checked_at": datetime.now().isoformat()
        }
        
        return jsonify(health_status), 200
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            "status": "unhealthy",
            "service": "fake_order_system",
            "error": str(e),
            "checked_at": datetime.now().isoformat()
        }), 503


@fake_order_bp.route('/security-validation', methods=['GET'])
@require_api_key
@require_fake_order_permission('view_fake_orders')
def api_fake_order_security_validation():
    """Validate security and isolation of fake orders"""
    try:
        fake_orders = fake_order_service.get_fake_orders(limit=None)
        
        validation_results = {
            "total_checked": len(fake_orders),
            "issues_found": 0,
            "warnings_found": 0,
            "isolation_status": "active",
            "security_issues": [],
            "validation_details": []
        }
        
        for order in fake_orders:
            # Validate each fake order
            safety_validation = fake_order_security.validate_fake_order_safety(order)
            
            validation_detail = {
                "order_sn": order.get('order_sn', 'unknown'),
                "is_safe": safety_validation['is_safe'],
                "warnings": safety_validation['warnings'],
                "errors": safety_validation['errors']
            }
            
            if not safety_validation['is_safe']:
                validation_results["issues_found"] += 1
                validation_results["security_issues"].extend(safety_validation['errors'])
            
            if safety_validation['warnings']:
                validation_results["warnings_found"] += len(safety_validation['warnings'])
            
            validation_results["validation_details"].append(validation_detail)
        
        # Log security validation
        fake_order_security.log_fake_order_operation(
            'security_validation_check',
            {"validation_type": "bulk_security_check"},
            {
                "total_orders": validation_results["total_checked"],
                "issues_found": validation_results["issues_found"],
                "warnings_found": validation_results["warnings_found"]
            }
        )
        
        return jsonify({
            "success": True,
            "validation_results": validation_results,
            "validated_at": datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Security validation failed: {e}")
        return jsonify({
            "success": False,
            "error": f"Security validation failed: {str(e)}",
            "error_code": "SECURITY_VALIDATION_FAILED"
        }), 500