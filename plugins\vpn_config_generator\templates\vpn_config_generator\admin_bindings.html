<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .modal {
            display: none;
        }
        .modal.show {
            display: flex;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ title }}</h1>
            <p class="text-gray-600">Manage server bindings and unbind configurations while preserving remaining days</p>
        </div>

        <!-- Configurations Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Server Bound Configurations</h2>
            </div>
            <div class="overflow-x-auto">
                {% if configs %}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Server</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Telco/Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining Days</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for username, config in configs.items() %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ username }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <code class="bg-gray-100 px-2 py-1 rounded text-xs">{{ config.client_id or config.numeric_id }}</code>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div>
                                    <div class="font-medium">{{ config.server_name or 'Unknown' }}</div>
                                    <div class="text-xs text-gray-400">ID: {{ config.server_id }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ config.telco|upper }} - {{ config.plan|title }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="flex items-center">
                                    {% if config.remaining_days > 7 %}
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                    {% elif config.remaining_days > 0 %}
                                    <div class="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                                    {% else %}
                                    <div class="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
                                    {% endif %}
                                    {{ config.remaining_days }} days
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if config.active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Inactive
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="showUnbindModal('{{ config.client_id or config.numeric_id }}', '{{ username }}', {{ config.remaining_days }})"
                                            class="text-orange-600 hover:text-orange-900">
                                        Unbind
                                    </button>
                                    <button onclick="showRebindModal('{{ config.client_id or config.numeric_id }}', '{{ username }}', {{ config.remaining_days }})"
                                            class="text-blue-600 hover:text-blue-900">
                                        Rebind
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <div class="p-8 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No bound configurations</h3>
                    <p class="mt-1 text-sm text-gray-500">No configurations are currently bound to servers.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Unbind Modal -->
    <div id="unbindModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Unbind Configuration</h3>
                    <button onclick="closeUnbindModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">Configuration: <span id="unbindUsername" class="font-medium"></span></p>
                    <p class="text-sm text-gray-600 mb-4">Client ID: <code id="unbindClientId" class="bg-gray-100 px-2 py-1 rounded text-xs"></code></p>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">Remaining Days: <span id="unbindRemainingDays"></span></h4>
                                <p class="text-sm text-yellow-700 mt-1">Choose whether to preserve the remaining days when unbinding.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="radio" name="preserveDays" value="true" checked class="mr-2">
                            <span class="text-sm">Preserve remaining days (recommended)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="preserveDays" value="false" class="mr-2">
                            <span class="text-sm">Reset days to 0</span>
                        </label>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button onclick="closeUnbindModal()" 
                            class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition duration-200">
                        Cancel
                    </button>
                    <button onclick="confirmUnbind()" 
                            class="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition duration-200">
                        Unbind Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rebind Modal -->
    <div id="rebindModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Rebind Configuration</h3>
                    <button onclick="closeRebindModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">Configuration: <span id="rebindUsername" class="font-medium"></span></p>
                    <p class="text-sm text-gray-600 mb-4">Client ID: <code id="rebindClientId" class="bg-gray-100 px-2 py-1 rounded text-xs"></code></p>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">New Server ID</label>
                            <input type="number" id="newServerId" placeholder="Enter server ID" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Extend Days (optional)</label>
                            <input type="number" id="extendDays" placeholder="0" min="0" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Additional days to add to remaining time</p>
                        </div>
                        
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <p class="text-sm text-blue-700">Current remaining days: <span id="rebindRemainingDays" class="font-medium"></span></p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button onclick="closeRebindModal()" 
                            class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition duration-200">
                        Cancel
                    </button>
                    <button onclick="confirmRebind()" 
                            class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition duration-200">
                        Rebind Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentClientId = null;
        
        function showUnbindModal(clientId, username, remainingDays) {
            currentClientId = clientId;
            document.getElementById('unbindClientId').textContent = clientId;
            document.getElementById('unbindUsername').textContent = username;
            document.getElementById('unbindRemainingDays').textContent = remainingDays + ' days';
            document.getElementById('unbindModal').classList.add('show');
        }
        
        function closeUnbindModal() {
            document.getElementById('unbindModal').classList.remove('show');
            currentClientId = null;
        }
        
        function showRebindModal(clientId, username, remainingDays) {
            currentClientId = clientId;
            document.getElementById('rebindClientId').textContent = clientId;
            document.getElementById('rebindUsername').textContent = username;
            document.getElementById('rebindRemainingDays').textContent = remainingDays;
            document.getElementById('rebindModal').classList.add('show');
        }
        
        function closeRebindModal() {
            document.getElementById('rebindModal').classList.remove('show');
            currentClientId = null;
            document.getElementById('newServerId').value = '';
            document.getElementById('extendDays').value = '';
        }
        
        async function confirmUnbind() {
            if (!currentClientId) return;
            
            const preserveDays = document.querySelector('input[name="preserveDays"]:checked').value === 'true';
            
            try {
                const response = await axios.post('/vpn-config-generator/admin/vpn-config-generator/api/unbind-config', {
                    client_id: currentClientId,
                    preserve_days: preserveDays
                });
                
                if (response.data.success) {
                    alert('Configuration unbound successfully!\n\n' + response.data.message);
                    location.reload();
                } else {
                    alert('Error: ' + response.data.error);
                }
            } catch (error) {
                alert('Error: ' + (error.response?.data?.error || error.message));
            }
            
            closeUnbindModal();
        }
        
        async function confirmRebind() {
            if (!currentClientId) return;
            
            const serverId = document.getElementById('newServerId').value;
            const extendDays = parseInt(document.getElementById('extendDays').value) || 0;
            
            if (!serverId) {
                alert('Please enter a server ID');
                return;
            }
            
            try {
                const response = await axios.post('/vpn-config-generator/admin/vpn-config-generator/api/rebind-config', {
                    client_id: currentClientId,
                    server_id: parseInt(serverId),
                    extend_days: extendDays
                });
                
                if (response.data.success) {
                    alert('Configuration rebound successfully!\n\n' + response.data.message);
                    location.reload();
                } else {
                    alert('Error: ' + response.data.error);
                }
            } catch (error) {
                alert('Error: ' + (error.response?.data?.error || error.message));
            }
            
            closeRebindModal();
        }
    </script>
</body>
</html>
