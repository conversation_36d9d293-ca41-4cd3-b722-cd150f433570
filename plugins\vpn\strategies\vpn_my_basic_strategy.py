"""
VPN Malaysia Basic Strategy
Strategy for Malaysia basic VPN products (my_*) that require:
- Creation only on Shinjiru servers
- Single server selection (first available)
- Standard configuration
Adapted from ShopeeRedeemBot for SteamCodeTool plugin architecture
"""

import logging
import random
from typing import Dict, List, Any
from .vpn_base_strategy import VPNBaseStrategy
from .strategy_factory import VPNStrategyFactory

logger = logging.getLogger(__name__)

class VPNMalaysiaBasicStrategy(VPNBaseStrategy):
    """
    Strategy for Malaysia basic VPN products (my_*).
    Creates users only on Shinjiru servers.
    """
    
    def get_target_servers(self, product_sku: str) -> List[Dict[str, Any]]:
        """
        Get servers for Malaysia basic products based on server tags.
        Uses the strategy factory to determine which server tags to use.
        """
        try:
            # Get required server tags for this SKU
            required_tags = VPNStrategyFactory.get_server_tags_for_sku(product_sku)
            logger.info(f"Malaysia basic strategy: Using server tags {required_tags} for SKU {product_sku}")

            # Get servers filtered by tags
            target_servers = self._get_servers_by_tags(required_tags)

            logger.info(f"Malaysia basic strategy: Found {len(target_servers)} servers for {product_sku}")
            for server in target_servers:
                logger.info(f"  - {server.get('name', 'Unknown')} (tags: {server.get('tags', [])})")

            if not target_servers:
                logger.warning(f"No servers found with tags {required_tags} for {product_sku}. This may cause issues.")

            return target_servers

        except Exception as e:
            logger.error(f"Error getting servers for {product_sku}: {str(e)}")
            return []
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create VPN user on a randomly selected Shinjiru server.
        For my_ products, users cannot choose server - it's automatically assigned.
        """
        try:
            if not servers:
                return {
                    "status": "error",
                    "message": "No Shinjiru servers available"
                }

            # Randomly select a Shinjiru server for my_ products
            selected_server = random.choice(servers)

            logger.info(f"my_ product: Randomly selected Shinjiru server: {selected_server['name']}")
            logger.info(f"Creating VPN user on server: {selected_server['name']}")

            result = self.vpn_service.create_client(
                server_id=selected_server['id'],
                email=user_data['customer_email'],
                shopee_username=user_data['shopee_username'],
                expired_date=user_data['expiry_date'],
                description=user_data['description']
            )
            
            if result.get('id'):
                logger.info(f"✅ Successfully created user on {selected_server['name']}")
                return {
                    "status": "success",
                    "message": "VPN user created successfully",
                    "server": selected_server,
                    "client": result,
                    "created_clients": [{
                        "server": selected_server,
                        "client": result
                    }]
                }
            else:
                error_msg = result.get('message', 'Unknown error')
                logger.error(f"❌ Failed to create user on {selected_server['name']}: {error_msg}")
                
                # Try other servers if available
                for backup_server in servers[1:]:
                    try:
                        logger.info(f"Trying backup server: {backup_server['name']}")
                        backup_result = self.vpn_service.create_client(
                            server_id=backup_server['id'],
                            email=user_data['customer_email'],
                            shopee_username=user_data['shopee_username'],
                            expired_date=user_data['expiry_date'],
                            description=f"{user_data['description']} - Backup server"
                        )
                        
                        if backup_result.get('id'):
                            logger.info(f"✅ Successfully created user on backup server {backup_server['name']}")
                            return {
                                "status": "success",
                                "message": f"VPN user created on backup server {backup_server['name']}",
                                "server": backup_server,
                                "client": backup_result,
                                "created_clients": [{
                                    "server": backup_server,
                                    "client": backup_result
                                }]
                            }
                    except Exception as e:
                        logger.error(f"❌ Backup server {backup_server['name']} also failed: {str(e)}")
                        continue
                
                return {
                    "status": "error",
                    "message": f"Failed to create user on all available servers. Last error: {error_msg}"
                }
                
        except Exception as e:
            logger.error(f"Error in basic Malaysia VPN user creation: {str(e)}")
            return {
                "status": "error",
                "message": f"Internal error during user creation: {str(e)}"
            }
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for basic Malaysia products.
        """
        base_response = super().format_success_response(user_data, vpn_result)
        
        # Add basic Malaysia specific information
        server_info = vpn_result.get('server', {})
        client_info = vpn_result.get('client', {})
        
        base_response["data"]["strategy"] = "malaysia_basic"
        base_response["data"]["creation_type"] = "single_server"
        base_response["data"]["server_type"] = "shinjiru"
        
        # Add server details
        base_response["data"]["server_details"] = [{
            "name": server_info.get('name'),
            "host": server_info.get('host'),
            "location": "Malaysia",
            "provider": "Shinjiru",
            "client_id": client_info.get('id'),
            "status": "active"
        }]
        
        # Add configuration information
        base_response["data"]["configuration"] = {
            "access_method": "single_server",
            "redundancy": "standard",
            "location": "Malaysia",
            "bandwidth": "standard"
        }
        
        return base_response


class VPNMalaysiaStandardStrategy(VPNMalaysiaBasicStrategy):
    """
    Standard variant of the basic Malaysia strategy with enhanced features.
    """
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create standard VPN users with enhanced configuration.
        """
        # Add standard-specific description
        user_data['description'] = f"{user_data['description']} - STANDARD"
        
        # Call parent method
        result = super().create_vpn_users(servers, user_data)
        
        # Add standard-specific metadata
        if result.get('status') == 'success':
            result['standard_features'] = {
                "bandwidth_limit": "100 Mbps",
                "concurrent_connections": "5",
                "support_level": "standard"
            }
        
        return result
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for standard Malaysia products.
        """
        response = super().format_success_response(user_data, vpn_result)
        
        # Add standard-specific information
        response["data"]["strategy"] = "malaysia_standard"
        response["data"]["tier"] = "standard"
        
        if vpn_result.get('standard_features'):
            response["data"]["features"] = vpn_result['standard_features']
        
        return response


class VPNMalaysiaPremiumStrategy(VPNMalaysiaBasicStrategy):
    """
    Premium variant of the basic Malaysia strategy with premium features.
    """
    
    def get_target_servers(self, product_sku: str) -> List[Dict[str, Any]]:
        """
        Get premium Shinjiru servers (prioritize newer/faster servers).
        """
        servers = super().get_target_servers(product_sku)
        
        # Sort servers by preference (newer servers first)
        # This is a simple example - you could implement more sophisticated logic
        premium_order = ['MY8', 'MY6', 'MY2', 'MY1']
        
        def server_priority(server):
            name = server.get('name', '')
            for i, preferred in enumerate(premium_order):
                if preferred in name:
                    return i
            return len(premium_order)
        
        sorted_servers = sorted(servers, key=server_priority)
        logger.info(f"Premium Malaysia strategy: Prioritized {len(sorted_servers)} servers")
        
        return sorted_servers
    
    def create_vpn_users(self, servers: List[Dict[str, Any]], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create premium VPN users with enhanced configuration.
        """
        # Add premium-specific description
        user_data['description'] = f"{user_data['description']} - PREMIUM"
        
        # Call parent method
        result = super().create_vpn_users(servers, user_data)
        
        # Add premium-specific metadata
        if result.get('status') == 'success':
            result['premium_features'] = {
                "bandwidth_limit": "unlimited",
                "concurrent_connections": "10",
                "support_level": "premium",
                "priority_routing": True
            }
        
        return result
    
    def format_success_response(self, user_data: Dict[str, Any], vpn_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format success response for premium Malaysia products.
        """
        response = super().format_success_response(user_data, vpn_result)
        
        # Add premium-specific information
        response["data"]["strategy"] = "malaysia_premium"
        response["data"]["tier"] = "premium"
        
        if vpn_result.get('premium_features'):
            response["data"]["premium_features"] = vpn_result['premium_features']
        
        return response
