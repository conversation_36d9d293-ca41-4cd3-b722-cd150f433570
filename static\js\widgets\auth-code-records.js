/**
 * Authentication Code Records Widget
 * Displays Steam authentication code request records with DataTable
 */

class AuthCodeRecordsWidget extends window.DashboardWidget {
    constructor(config) {
        super(config);
        this.records = [];
        this.dataTable = null;
    }

    async init() {
        try {
            await this.loadData();
            this.render();
        } catch (error) {
            console.error('Error initializing auth code records widget:', error);
            this.showError(error.message);
        }
    }

    async loadData() {
        try {
            const response = await fetch(this.config.data_endpoint);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.records = Array.isArray(result.data) ? result.data : [];
                this.lastRefresh = new Date();
            } else {
                throw new Error(result.error || 'Failed to load auth code records');
            }
        } catch (error) {
            console.error('Error loading auth code records:', error);
            this.records = []; // Ensure records is always an array
            this.showError(error.message);
        }
    }

    render() {
        if (!this.container) return;

        // Ensure records is an array
        if (!this.records || !Array.isArray(this.records)) {
            this.records = [];
        }

        // Build table rows HTML
        const rowsHtml = this.records.map(record => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${record.username}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${record.order_id}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${record.auth_code || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${record.timestamp}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${(record.status || 'Unknown') === 'Success' ? 'bg-green-100 text-green-800' :
                          (record.status || 'Unknown') === 'Attempt' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'}">
                        ${record.status || 'Unknown'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${record.error_message || 'N/A'}</td>
            </tr>
        `).join('');

        this.container.innerHTML = `
            <div class="mt-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-semibold">Authentication Code Request Records</h2>
                    <button id="resetRecordsBtn" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                        Reset Records
                    </button>
                </div>
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <table id="authCodeTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error Message</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${rowsHtml}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        // Initialize DataTable
        this.initDataTable();

        // Add event listeners
        document.getElementById('resetRecordsBtn')?.addEventListener('click', () => {
            this.resetRecords();
        });
    }

    initDataTable() {
        if (this.dataTable) {
            this.dataTable.destroy();
        }

        // Wait for jQuery and DataTables to be available
        if ($ && $.fn.DataTable) {
            this.dataTable = $('#authCodeTable').DataTable({
                responsive: true,
                order: [[3, 'desc']],
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                language: {
                    paginate: {
                        previous: "&#8592;",
                        next: "&#8594;"
                    },
                    lengthMenu: "_MENU_ per page",
                    search: "",
                    searchPlaceholder: "Search...",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries"
                },
                dom: "<'flex justify-between items-center'<l><f>>" +
                    "<'overflow-x-auto'tr>" +
                    "<'flex justify-between items-center'<i><p>>",
            });
        }
    }

    async resetRecords() {
        if (!confirm('Are you sure you want to reset the records? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/admin/reset_auth_code_records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            
            if (result.success || result.message) {
                alert(result.message || 'Records reset successfully');
                await this.refresh();
            } else {
                alert('Error resetting records: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            alert('Error resetting records: ' + error.message);
        }
    }

    destroy() {
        if (this.dataTable) {
            this.dataTable.destroy();
        }
    }
}

// Register widget
window.DashboardWidgets['auth-code-records'] = AuthCodeRecordsWidget; 