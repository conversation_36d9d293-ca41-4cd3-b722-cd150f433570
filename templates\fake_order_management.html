<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fake Order Management - Admin Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .fake-order-row {
            background-color: #fef3c7 !important;
            border-left: 4px solid #f59e0b !important;
        }
        
        .fake-order-indicator {
            color: #f59e0b;
            font-weight: bold;
        }
        
        .security-warning {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        
        .security-safe {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
        }
        
        .isolation-badge {
            background-color: #ddd6fe;
            color: #7c3aed;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
    </style>
</head>

<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-7xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">🧪 Fake Order Management</h1>
            <div class="flex space-x-2">
                <button onclick="refreshOrders()" 
                    class="bg-blue-500 text-whi