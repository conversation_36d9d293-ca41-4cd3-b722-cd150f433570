"""
Unit Tests for OpenAI Plus Redeem Plugin Services

Comprehensive unit tests for all services including mocking external dependencies.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock, mock_open
import tempfile
import os
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

# Import services
from ..services.base_service import BaseService
from ..services.chatgpt_account_service import ChatGPTAccountService
from ..services.order_redemption_service import OrderRedemptionService
from ..services.order_redeem_service import OrderRedeemService
from ..services.email_service import EmailService
from ..services.cooldown_service import CooldownService
from ..services.shopee_messaging_service import ShopeeMessagingService
from ..services.service_manager import ServiceManager, ServiceInitializationError

# Import models
from ..models.chatgpt_account import ChatGPTAccount, AccountStatus
from ..models.order_redemption import OrderRedemption, RedemptionStatus
from ..models.email_verification import EmailVerification, VerificationStatus
from ..models.account_cooldown import AccountCooldown, CooldownType


class TestBaseService(unittest.TestCase):
    """Test BaseService abstract class functionality"""
    
    def setUp(self):
        self.config = {
            'test_config': {
                'value': 'test'
            }
        }
        self.logger = Mock()
    
    def test_base_service_abstract_methods(self):
        """Test that BaseService cannot be instantiated directly"""
        with self.assertRaises(TypeError):
            BaseService(self.config, self.logger)
    
    def test_config_value_retrieval(self):
        """Test configuration value retrieval with defaults"""
        # Create a concrete implementation for testing
        class TestService(BaseService):
            def initialize(self): return True
            def shutdown(self): return True
            def health_check(self): return {}
        
        service = TestService(self.config, self.logger)
        
        # Test existing config value
        value = service._get_config_value('test_config.value', 'default')
        self.assertEqual(value, 'test')
        
        # Test non-existing config value with default
        value = service._get_config_value('non_existing.value', 'default')
        self.assertEqual(value, 'default')
        
        # Test nested config access
        nested_config = {'nested': {'deep': {'value': 'found'}}}
        service.config.update(nested_config)
        value = service._get_config_value('nested.deep.value', 'default')
        self.assertEqual(value, 'found')


class TestChatGPTAccountService(unittest.TestCase):
    """Test ChatGPT Account Service"""
    
    def setUp(self):
        self.config = {
            'data_config': {
                'backup_enabled': False
            }
        }
        self.logger = Mock()
        
        # Mock data file operations
        self.mock_data = {
            'accounts': [],
            'metadata': {
                'version': '1.0.0',
                'total_accounts': 0,
                'last_updated': datetime.now().isoformat()
            }
        }
    
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.save_json_data')
    def test_service_initialization(self, mock_save, mock_load):
        """Test service initialization"""
        mock_load.return_value = self.mock_data
        
        service = ChatGPTAccountService(self.config, self.logger)
        result = service.initialize()
        
        self.assertTrue(result)
        self.assertTrue(service.is_initialized())
        mock_load.assert_called_once()
    
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.save_json_data')
    def test_create_account(self, mock_save, mock_load):
        """Test account creation"""
        mock_load.return_value = self.mock_data
        
        service = ChatGPTAccountService(self.config, self.logger)
        service.initialize()
        
        # Test valid account creation
        account = service.create_account(
            email="<EMAIL>",
            password="password123",
            expiration_date=datetime.now() + timedelta(days=30),
            max_concurrent_users=5
        )
        
        self.assertIsNotNone(account)
        self.assertEqual(account.email, "<EMAIL>")
        self.assertEqual(account.max_concurrent_users, 5)
        self.assertEqual(account.status, AccountStatus.ACTIVE)
    
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.load_json_data')
    def test_find_best_account_for_assignment(self, mock_load):
        """Test finding best account for assignment"""
        # Create test accounts
        account1 = ChatGPTAccount.create_account(
            email="<EMAIL>",
            password="pass1",
            expiration_date=datetime.now() + timedelta(days=30),
            max_concurrent_users=5
        )
        account1.current_users = 2
        
        account2 = ChatGPTAccount.create_account(
            email="<EMAIL>", 
            password="pass2",
            expiration_date=datetime.now() + timedelta(days=30),
            max_concurrent_users=3
        )
        account2.current_users = 3  # Full capacity
        
        self.mock_data['accounts'] = [account1.to_dict(), account2.to_dict()]
        mock_load.return_value = self.mock_data
        
        service = ChatGPTAccountService(self.config, self.logger)
        service.initialize()
        
        # Should find account1 (has capacity)
        best_account = service.find_best_account_for_assignment("chatgpt_5_30")
        self.assertIsNotNone(best_account)
        self.assertEqual(best_account.email, "<EMAIL>")


class TestOrderRedemptionService(unittest.TestCase):
    """Test Order Redemption Service"""
    
    def setUp(self):
        self.config = {
            'data_config': {
                'backup_enabled': False
            }
        }
        self.logger = Mock()
        self.mock_data = {
            'redemptions': [],
            'metadata': {
                'version': '1.0.0',
                'total_redemptions': 0,
                'last_updated': datetime.now().isoformat()
            }
        }
    
    @patch('plugins.openai_plus_redeem.services.order_redemption_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.order_redemption_service.save_json_data')
    def test_create_redemption_from_order(self, mock_save, mock_load):
        """Test redemption creation from order"""
        mock_load.return_value = self.mock_data
        
        service = OrderRedemptionService(self.config, self.logger)
        service.initialize()
        
        redemption = service.create_redemption_from_order(
            order_id="ORDER123",
            buyer_username="testuser",
            sku="chatgpt_plus",
            var_sku="chatgpt_5_30"
        )
        
        self.assertIsNotNone(redemption)
        self.assertEqual(redemption.order_id, "ORDER123")
        self.assertEqual(redemption.buyer_username, "testuser")
        self.assertEqual(redemption.status, RedemptionStatus.PENDING)
    
    @patch('plugins.openai_plus_redeem.services.order_redemption_service.load_json_data')
    def test_get_user_cooldown_status(self, mock_load):
        """Test user cooldown status checking"""
        # Create test redemption with cooldown
        redemption = OrderRedemption.create_from_order(
            order_id="ORDER123",
            buyer_username="testuser",
            sku="chatgpt_plus",
            var_sku="chatgpt_5_30"
        )
        redemption.cooldown_until = datetime.now() + timedelta(hours=2)
        
        self.mock_data['redemptions'] = [redemption.to_dict()]
        mock_load.return_value = self.mock_data
        
        service = OrderRedemptionService(self.config, self.logger)
        service.initialize()
        
        status = service.get_user_cooldown_status("testuser")
        
        self.assertTrue(status['has_cooldown'])
        self.assertGreater(status['remaining_hours'], 0)


class TestEmailService(unittest.TestCase):
    """Test Email Service"""
    
    def setUp(self):
        self.config = {
            'email_config': {
                'credentials': [
                    {
                        'email': '<EMAIL>',
                        'password': 'app_password'
                    }
                ],
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'use_ssl': True
            }
        }
        self.logger = Mock()
    
    @patch('plugins.openai_plus_redeem.services.email_service.load_json_data')
    def test_service_initialization(self, mock_load):
        """Test email service initialization"""
        mock_load.return_value = {'verification_logs': []}
        
        service = EmailService(self.config, self.logger)
        result = service.initialize()
        
        self.assertTrue(result)
        self.assertTrue(service.is_initialized())
    
    @patch('imaplib.IMAP4_SSL')
    def test_email_connection_test(self, mock_imap):
        """Test email connection testing"""
        # Mock successful connection
        mock_mail = Mock()
        mock_imap.return_value = mock_mail
        
        service = EmailService(self.config, self.logger)
        
        result = service.test_email_connection('<EMAIL>', 'password')
        
        self.assertTrue(result)
        mock_mail.login.assert_called_once_with('<EMAIL>', 'password')
        mock_mail.logout.assert_called_once()
    
    @patch('imaplib.IMAP4_SSL')
    def test_email_connection_failure(self, mock_imap):
        """Test email connection failure handling"""
        # Mock connection failure
        mock_imap.side_effect = Exception("Connection failed")
        
        service = EmailService(self.config, self.logger)
        
        result = service.test_email_connection('<EMAIL>', 'password')
        
        self.assertFalse(result)


class TestCooldownService(unittest.TestCase):
    """Test Cooldown Service"""
    
    def setUp(self):
        self.config = {
            'cooldown_config': {
                'default_hours': 24,
                'max_hours': 168,
                'enable_admin_override': True
            }
        }
        self.logger = Mock()
        self.mock_data = {
            'cooldowns': [],
            'metadata': {
                'version': '1.0.0',
                'total_cooldowns': 0,
                'last_updated': datetime.now().isoformat()
            }
        }
    
    @patch('plugins.openai_plus_redeem.services.cooldown_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.cooldown_service.save_json_data')
    def test_set_user_cooldown(self, mock_save, mock_load):
        """Test setting user cooldown"""
        mock_load.return_value = self.mock_data
        
        service = CooldownService(self.config, self.logger)
        service.initialize()
        
        result = service.set_user_cooldown(
            username="testuser",
            hours=24,
            cooldown_type=CooldownType.REDEMPTION,
            reason="Test cooldown"
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['username'], "testuser")
        self.assertEqual(result['hours'], 24)
    
    @patch('plugins.openai_plus_redeem.services.cooldown_service.load_json_data')
    def test_get_user_cooldown_status(self, mock_load):
        """Test getting user cooldown status"""
        # Create test cooldown
        cooldown = AccountCooldown.create_cooldown(
            username="testuser",
            hours=24,
            cooldown_type=CooldownType.REDEMPTION,
            reason="Test"
        )
        
        self.mock_data['cooldowns'] = [cooldown.to_dict()]
        mock_load.return_value = self.mock_data
        
        service = CooldownService(self.config, self.logger)
        service.initialize()
        
        status = service.get_user_cooldown_status("testuser")
        
        self.assertTrue(status['has_cooldown'])
        self.assertEqual(status['username'], "testuser")
        self.assertGreater(status['remaining_hours'], 0)


class TestShopeeMessagingService(unittest.TestCase):
    """Test Shopee Messaging Service"""
    
    def setUp(self):
        self.config = {
            'shopee_config': {
                'enable_messaging': True
            },
            'message_config': {
                'auto_send_account_details': True,
                'auto_send_expiration_warnings': True
            },
            'message_templates': {}
        }
        self.logger = Mock()
    
    def test_service_initialization(self):
        """Test service initialization"""
        service = ShopeeMessagingService(self.config, self.logger)
        result = service.initialize()
        
        self.assertTrue(result)
        self.assertTrue(service.is_initialized())
    
    @patch('plugins.openai_plus_redeem.services.shopee_messaging_service.sys.path')
    def test_shopee_api_initialization(self, mock_path):
        """Test ShopeeAPI client initialization"""
        service = ShopeeMessagingService(self.config, self.logger)
        
        # Test with messaging disabled
        service.enable_messaging = False
        service.initialize()
        self.assertFalse(service._api_available)
        
        # Test with messaging enabled but API not available
        service.enable_messaging = True
        service._initialize_shopee_api()
        # Should handle import error gracefully
        self.assertFalse(service._api_available)


class TestServiceManager(unittest.TestCase):
    """Test Service Manager"""
    
    def setUp(self):
        self.config = {
            'data_config': {
                'backup_enabled': False
            },
            'email_config': {
                'credentials': [
                    {
                        'email': '<EMAIL>',
                        'password': 'app_password'
                    }
                ]
            },
            'cooldown_config': {
                'default_hours': 24
            },
            'shopee_config': {
                'enable_messaging': False  # Disable to avoid API dependencies
            }
        }
        self.logger = Mock()
    
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.order_redemption_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.email_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.cooldown_service.load_json_data')
    def test_service_manager_initialization(self, mock_cooldown_load, mock_email_load, 
                                          mock_redemption_load, mock_account_load):
        """Test service manager initialization"""
        # Mock data loading for all services
        mock_data = {'accounts': [], 'metadata': {}}
        mock_account_load.return_value = mock_data
        mock_redemption_load.return_value = {'redemptions': [], 'metadata': {}}
        mock_email_load.return_value = {'verification_logs': []}
        mock_cooldown_load.return_value = {'cooldowns': [], 'metadata': {}}
        
        manager = ServiceManager(self.config, self.logger)
        result = manager.initialize_all_services()
        
        self.assertTrue(result)
        self.assertTrue(manager.is_initialized())
        self.assertGreater(len(manager.services), 0)
    
    def test_service_dependency_calculation(self):
        """Test service dependency calculation"""
        manager = ServiceManager(self.config, self.logger)
        
        init_order = manager._calculate_initialization_order()
        
        # Verify that dependent services come after their dependencies
        order_redeem_index = init_order.index('order_redeem')
        chatgpt_account_index = init_order.index('chatgpt_account')
        
        self.assertLess(chatgpt_account_index, order_redeem_index)
    
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.load_json_data')
    def test_service_health_check(self, mock_load):
        """Test service health checking"""
        mock_load.return_value = {'accounts': [], 'metadata': {}}
        
        manager = ServiceManager(self.config, self.logger)
        
        # Test health check without initialization
        health = manager.health_check_all_services()
        self.assertEqual(health['total_services'], 0)
        
        # Test with initialized services
        manager.services['test_service'] = Mock()
        manager.services['test_service'].health_check.return_value = {
            'status': 'healthy'
        }
        
        health = manager.health_check_all_services()
        self.assertEqual(health['total_services'], 1)


class TestServiceIntegration(unittest.TestCase):
    """Test service integration scenarios"""
    
    def setUp(self):
        self.config = {
            'data_config': {
                'backup_enabled': False
            },
            'email_config': {
                'credentials': [
                    {
                        'email': '<EMAIL>',
                        'password': 'app_password'
                    }
                ]
            },
            'cooldown_config': {
                'default_hours': 24
            },
            'shopee_config': {
                'enable_messaging': False
            }
        }
        self.logger = Mock()
    
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.order_redemption_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.email_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.cooldown_service.load_json_data')
    @patch('plugins.openai_plus_redeem.services.chatgpt_account_service.save_json_data')
    @patch('plugins.openai_plus_redeem.services.order_redemption_service.save_json_data')
    def test_complete_redemption_workflow(self, mock_redemption_save, mock_account_save,
                                        mock_cooldown_load, mock_email_load,
                                        mock_redemption_load, mock_account_load):
        """Test complete redemption workflow integration"""
        # Setup mock data
        account = ChatGPTAccount.create_account(
            email="<EMAIL>",
            password="password123",
            expiration_date=datetime.now() + timedelta(days=30),
            max_concurrent_users=5
        )
        
        mock_account_data = {
            'accounts': [account.to_dict()],
            'metadata': {}
        }
        mock_account_load.return_value = mock_account_data
        mock_redemption_load.return_value = {'redemptions': [], 'metadata': {}}
        mock_email_load.return_value = {'verification_logs': []}
        mock_cooldown_load.return_value = {'cooldowns': [], 'metadata': {}}
        
        # Initialize service manager
        manager = ServiceManager(self.config, self.logger)
        manager.initialize_all_services()
        
        # Get order redeem service
        order_redeem_service = manager.get_service('order_redeem')
        self.assertIsNotNone(order_redeem_service)
        
        # Test order redemption process
        result = order_redeem_service.process_order_redemption(
            order_id="ORDER123",
            buyer_username="testuser",
            sku="chatgpt_plus",
            var_sku="chatgpt_5_30"
        )
        
        self.assertTrue(result['success'])
        self.assertIn('redemption_id', result)


if __name__ == '__main__':
    unittest.main()
