#!/usr/bin/env python3
"""
Test script to verify automatic command name updates work correctly
"""

import json
import os
import sys

def test_command_name_change():
    """Test that command names auto-update when main command changes"""
    
    # Get the plugin directory
    plugin_dir = os.path.dirname(os.path.abspath(__file__))
    commands_file = os.path.join(plugin_dir, 'commands.json')
    command_config_file = os.path.join(plugin_dir, 'command_config.json')
    
    print("🧪 Testing VPN Config Generator Command Name Auto-Update System")
    print("=" * 60)
    
    # Read current configuration
    try:
        with open(command_config_file, 'r', encoding='utf-8') as f:
            command_config = json.load(f)
        
        with open(commands_file, 'r', encoding='utf-8') as f:
            commands = json.load(f)
            
        print(f"✅ Current main command name: {command_config['command_name']}")
        print(f"✅ Found {len(commands)} commands in commands.json")
        
        # Check if all command names match the expected pattern
        main_command = command_config['command_name']
        expected_commands = {
            'v': main_command,
            'vlist': f"{main_command}list",
            'vuser': f"{main_command}user", 
            'vdel': f"{main_command}del",
            'vrenew': f"{main_command}renew",
            'vtest': f"{main_command}test",
            'vservers': f"{main_command}servers",
            'vhelp': f"{main_command}help"
        }
        
        print("\n📋 Command Name Verification:")
        print("-" * 40)
        
        all_correct = True
        for command_key, expected_name in expected_commands.items():
            if command_key in commands:
                actual_name = commands[command_key]['command']
                status = "✅" if actual_name == expected_name else "❌"
                print(f"{status} {command_key}: expected '{expected_name}', got '{actual_name}'")
                if actual_name != expected_name:
                    all_correct = False
            else:
                print(f"❌ {command_key}: NOT FOUND in commands.json")
                all_correct = False
        
        if all_correct:
            print(f"\n🎉 SUCCESS: All command names are correctly set to use prefix '{main_command}'!")
            print("The auto-update system is working properly.")
        else:
            print(f"\n⚠️  WARNING: Some command names don't match expected pattern for prefix '{main_command}'")
            print("This suggests the auto-update system may need to be triggered.")
            
        print(f"\n💡 To change command name:")
        print(f"1. Update command_name in {command_config_file}")
        print(f"2. Use the web interface to update the command configuration")
        print(f"3. All sub-commands will auto-update to match the new prefix")
        
        return all_correct
        
    except FileNotFoundError as e:
        print(f"❌ ERROR: Required file not found: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ ERROR: Invalid JSON in configuration file: {e}")
        return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_command_name_change()
    sys.exit(0 if success else 1)