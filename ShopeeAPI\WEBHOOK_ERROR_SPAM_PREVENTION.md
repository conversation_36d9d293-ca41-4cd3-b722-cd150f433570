# Webhook Error Spam Prevention

## 🐛 Problem Description

The webhook system was generating excessive error logs when services were unavailable, causing log spam:

```
ERROR:utils.webhook:Connection failed to SteamCodeTool Chat Commands (http://127.0.0.1:5000/chat-commands/api/webhook): Cannot connect to host 127.0.0.1:5000 ssl:default [The remote computer refused the network connection]. Service may not be running.
ERROR:utils.webhook:Connection failed to SteamCodeTool Chat Commands (http://127.0.0.1:5000/chat-commands/api/webhook): Cannot connect to host 127.0.0.1:5000 ssl:default [The remote computer refused the network connection]. Service may not be running.
ERROR:utils.webhook:Connection failed to SteamCodeTool Chat Commands (http://127.0.0.1:5000/chat-commands/api/webhook): Cannot connect to host 127.0.0.1:5000 ssl:default [The remote computer refused the network connection]. Service may not be running.
```

This occurred because:
1. **No error deduplication**: Same errors were logged repeatedly
2. **No rate limiting**: Every retry attempt generated a new error log
3. **No time-based suppression**: Errors continued indefinitely

## ✅ Solution Implemented

### 1. Error Rate Limiting System

Added intelligent error rate limiting to the `WebhookManager` class:

```python
# Error rate limiting to prevent spam
self.error_log_cache = {}  # url -> {last_error: str, last_log_time: datetime, count: int}

# Get rate limiting configuration
rate_limit_config = config.get("ERROR_RATE_LIMITING", {})
self.rate_limiting_enabled = rate_limit_config.get("ENABLED", True)
self.error_log_interval = rate_limit_config.get("LOG_INTERVAL_SECONDS", 300)  # 5 minutes default
self.max_error_count_per_interval = rate_limit_config.get("MAX_ERRORS_PER_INTERVAL", 3)
```

### 2. Smart Error Deduplication

Implemented `_should_log_error()` method that:
- **Tracks error history** per URL
- **Deduplicates identical errors** within time windows
- **Allows different errors** to be logged
- **Resets counters** after time intervals

```python
def _should_log_error(self, url: str, error_message: str) -> bool:
    # If rate limiting is disabled, always log
    if not self.rate_limiting_enabled:
        return True
        
    current_time = datetime.now()
    
    # Get or create error cache entry for this URL
    if url not in self.error_log_cache:
        self.error_log_cache[url] = {
            'last_error': '',
            'last_log_time': datetime.min,
            'count': 0
        }
    
    cache_entry = self.error_log_cache[url]
    
    # Check if this is the same error as before
    is_same_error = cache_entry['last_error'] == error_message
    time_since_last_log = (current_time - cache_entry['last_log_time']).total_seconds()
    
    # Reset count if enough time has passed
    if time_since_last_log > self.error_log_interval:
        cache_entry['count'] = 0
    
    # Log if it's a different error, or if enough time has passed, or if we haven't hit the limit
    should_log = (
        not is_same_error or 
        time_since_last_log > self.error_log_interval or 
        cache_entry['count'] < self.max_error_count_per_interval
    )
    
    if should_log:
        cache_entry['last_error'] = error_message
        cache_entry['last_log_time'] = current_time
        cache_entry['count'] += 1
    
    return should_log
```

### 3. Enhanced Error Handling

Updated all error logging to use rate limiting:

```python
except aiohttp.ClientConnectorError as e:
    error_msg = f"Connection failed to {name} ({url}): {e}. Service may not be running."
    if self._should_log_error(url, str(e)):
        logger.error(error_msg)
    else:
        logger.debug(f"[Rate Limited] {error_msg}")
```

### 4. Configurable Rate Limiting

Added configuration options in `config.json.example`:

```json
"WEBHOOK": {
  "ENABLED": true,
  "ERROR_RATE_LIMITING": {
    "ENABLED": true,
    "LOG_INTERVAL_SECONDS": 300,
    "MAX_ERRORS_PER_INTERVAL": 3
  },
  "MESSAGE_RECEIVED": {
    // ... existing config
  }
}
```

## 🎯 Rate Limiting Behavior

### Default Settings
- **Enabled**: `true` (can be disabled)
- **Log Interval**: `300` seconds (5 minutes)
- **Max Errors Per Interval**: `3` errors

### How It Works

1. **First 3 identical errors**: Logged normally as ERROR level
2. **Subsequent identical errors**: Suppressed and logged as DEBUG level with `[Rate Limited]` prefix
3. **After 5 minutes**: Error counter resets, allowing new errors to be logged
4. **Different errors**: Always logged (not subject to rate limiting)
5. **Rate limiting disabled**: All errors logged normally

### Example Behavior

```
# First occurrence - logged
ERROR:utils.webhook:Connection failed to SteamCodeTool Chat Commands: Connection refused

# Second occurrence - logged  
ERROR:utils.webhook:Connection failed to SteamCodeTool Chat Commands: Connection refused

# Third occurrence - logged
ERROR:utils.webhook:Connection failed to SteamCodeTool Chat Commands: Connection refused

# Fourth occurrence - suppressed
DEBUG:utils.webhook:[Rate Limited] Connection failed to SteamCodeTool Chat Commands: Connection refused

# After 5 minutes - logged again
ERROR:utils.webhook:Connection failed to SteamCodeTool Chat Commands: Connection refused
```

## 📊 Benefits

1. **Reduced Log Spam**: Eliminates repetitive error messages
2. **Preserved Important Information**: First few occurrences still logged for debugging
3. **Configurable**: Can be adjusted or disabled per deployment needs
4. **Smart Detection**: Different errors are not suppressed
5. **Time-based Reset**: Allows periodic status updates
6. **Backward Compatible**: Existing functionality unchanged

## 🔧 Configuration Options

### Enable/Disable Rate Limiting
```json
"ERROR_RATE_LIMITING": {
  "ENABLED": false  // Disable rate limiting completely
}
```

### Adjust Time Window
```json
"ERROR_RATE_LIMITING": {
  "LOG_INTERVAL_SECONDS": 600  // 10 minutes instead of 5
}
```

### Change Error Threshold
```json
"ERROR_RATE_LIMITING": {
  "MAX_ERRORS_PER_INTERVAL": 1  // Only log first occurrence
}
```

## 🧪 Testing

The implementation includes comprehensive testing that verifies:
- ✅ First 3 identical errors are logged
- ✅ Subsequent identical errors are suppressed  
- ✅ Different errors are always logged
- ✅ Rate limiting can be disabled
- ✅ Time-based reset functionality works

## 🔄 Impact

- **No Breaking Changes**: All existing webhook functionality preserved
- **Improved Performance**: Reduced I/O from excessive logging
- **Better Debugging**: Cleaner logs with less noise
- **Configurable**: Can be tuned for different environments

This solution effectively eliminates webhook error spam while maintaining visibility into connection issues and preserving all existing functionality.
