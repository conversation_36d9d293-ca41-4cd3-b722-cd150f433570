"""
ChatGPT Account Service

Service for managing ChatGPT Plus accounts including CRUD operations,
capacity tracking, assignment logic, and expiration handling.
"""

import os
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from .base_service import BaseService
from ..models.chatgpt_account import ChatGPTAccount
from ..models.utils import (
    load_json_data, save_json_data, validate_email, validate_password,
    DataPersistenceError, ValidationError, get_data_file_path
)


class ChatGPTAccountService(BaseService):
    """
    Service for managing ChatGPT Plus accounts
    
    Provides functionality for:
    - Account CRUD operations
    - Capacity tracking and management
    - Account assignment logic
    - Expiration handling
    - Data persistence
    """
    
    def __init__(self, config: Dict[str, Any], logger=None):
        super().__init__(config, logger)
        self.service_name = "ChatGPTAccountService"
        
        # Data file path
        self.data_file = get_data_file_path('openai_plus_redeem', 'chatgpt_accounts.json')
        
        # In-memory cache
        self._accounts_cache: Dict[str, ChatGPTAccount] = {}
        self._cache_loaded = False
        
        # Configuration
        self.auto_cleanup_expired = self._get_config_value('auto_cleanup_expired', True)
        self.max_accounts_per_sku = self._get_config_value('max_accounts_per_sku', 100)
        self.backup_enabled = self._get_config_value('backup_enabled', True)
    
    def initialize(self) -> bool:
        """Initialize the ChatGPT Account Service"""
        try:
            self.logger.info(f"Initializing {self.service_name}...")
            
            # Validate configuration
            if not self._validate_service_config():
                return False
            
            # Load accounts from storage
            if not self._load_accounts():
                self.logger.warning("Failed to load accounts, starting with empty cache")
                self._accounts_cache = {}
            
            self._cache_loaded = True
            
            # Perform initial cleanup if enabled
            if self.auto_cleanup_expired:
                self._cleanup_expired_accounts()
            
            self._mark_initialized()
            return True
            
        except Exception as e:
            self._handle_service_error("initialize", e)
            return False
    
    def shutdown(self) -> bool:
        """Shutdown the ChatGPT Account Service"""
        try:
            self.logger.info(f"Shutting down {self.service_name}...")
            
            # Save any pending changes
            if self._cache_loaded:
                self._save_accounts()
            
            # Clear cache
            self._accounts_cache.clear()
            self._cache_loaded = False
            
            self._mark_shutdown()
            return True
            
        except Exception as e:
            self._handle_service_error("shutdown", e)
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        self._update_health_check_time()
        
        try:
            # Basic health indicators
            health_data = {
                'status': 'healthy',
                'service': self.service_name,
                'cache_loaded': self._cache_loaded,
                'total_accounts': len(self._accounts_cache),
                'data_file_exists': os.path.exists(self.data_file),
                'timestamp': datetime.now().isoformat()
            }
            
            # Account statistics
            if self._cache_loaded:
                active_accounts = sum(1 for acc in self._accounts_cache.values() if acc.is_active)
                expired_accounts = sum(1 for acc in self._accounts_cache.values() if acc.is_expired())
                at_capacity = sum(1 for acc in self._accounts_cache.values() if acc.is_at_capacity())
                
                health_data.update({
                    'active_accounts': active_accounts,
                    'expired_accounts': expired_accounts,
                    'accounts_at_capacity': at_capacity,
                    'available_accounts': active_accounts - at_capacity
                })
            
            # Check for issues
            issues = []
            if not self._cache_loaded:
                issues.append("Cache not loaded")
            if not os.path.exists(self.data_file):
                issues.append("Data file missing")
            
            if issues:
                health_data['status'] = 'degraded'
                health_data['issues'] = issues
            
            return health_data
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': self.service_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def create_account(self, email: str, password: str, var_sku: str = "", 
                      max_users: int = None, validity_days: int = None) -> Optional[ChatGPTAccount]:
        """
        Create a new ChatGPT account
        
        Args:
            email: Account email
            password: Account password
            var_sku: Product variant SKU (for capacity parsing)
            max_users: Maximum concurrent users (overrides SKU parsing)
            validity_days: Validity period in days (overrides SKU parsing)
            
        Returns:
            Created ChatGPTAccount or None if creation failed
        """
        try:
            # Validate inputs
            if not validate_email(email):
                raise ValidationError(f"Invalid email format: {email}")
            
            if not validate_password(password):
                raise ValidationError("Password does not meet requirements")
            
            # Check for duplicate email
            if self.get_account_by_email(email):
                raise ValidationError(f"Account with email {email} already exists")
            
            # Create account
            if var_sku:
                account = ChatGPTAccount.from_sku(email, password, var_sku)
            else:
                account = ChatGPTAccount(
                    email=email,
                    password=password,
                    max_concurrent_users=max_users or 1,
                    validity_days=validity_days or 30
                )
            
            # Override parsed values if provided
            if max_users is not None:
                account.max_concurrent_users = max_users
            if validity_days is not None:
                account.validity_days = validity_days
                # Recalculate expiration
                account.expiration_date = (datetime.now() + timedelta(days=validity_days)).isoformat()
            
            # Add to cache
            self._accounts_cache[account.account_id] = account
            
            # Save to storage
            self._save_accounts()
            
            self._log_operation("create_account", {
                'account_id': account.account_id,
                'email': email,
                'max_users': account.max_concurrent_users,
                'validity_days': account.validity_days
            })
            
            return account
            
        except Exception as e:
            self._handle_service_error("create_account", e)
            return None
    
    def get_account(self, account_id: str) -> Optional[ChatGPTAccount]:
        """
        Get account by ID
        
        Args:
            account_id: Account ID
            
        Returns:
            ChatGPTAccount or None if not found
        """
        return self._accounts_cache.get(account_id)
    
    def get_account_by_email(self, email: str) -> Optional[ChatGPTAccount]:
        """
        Get account by email
        
        Args:
            email: Account email
            
        Returns:
            ChatGPTAccount or None if not found
        """
        for account in self._accounts_cache.values():
            if account.email.lower() == email.lower():
                return account
        return None
    
    def get_all_accounts(self, include_inactive: bool = True) -> List[ChatGPTAccount]:
        """
        Get all accounts
        
        Args:
            include_inactive: Whether to include inactive accounts
            
        Returns:
            List of ChatGPTAccount objects
        """
        accounts = list(self._accounts_cache.values())
        
        if not include_inactive:
            accounts = [acc for acc in accounts if acc.is_active]
        
        return accounts
    
    def get_available_accounts(self, min_capacity: int = 1) -> List[ChatGPTAccount]:
        """
        Get accounts that can accept new users
        
        Args:
            min_capacity: Minimum available capacity required
            
        Returns:
            List of available ChatGPTAccount objects
        """
        available = []
        
        for account in self._accounts_cache.values():
            if account.can_assign_user():
                available_capacity = account.max_concurrent_users - account.current_users
                if available_capacity >= min_capacity:
                    available.append(account)
        
        # Sort by available capacity (most available first)
        available.sort(key=lambda acc: acc.max_concurrent_users - acc.current_users, reverse=True)
        
        return available
    
    def assign_user_to_account(self, account_id: str) -> bool:
        """
        Assign a user to an account
        
        Args:
            account_id: Account ID
            
        Returns:
            True if assignment was successful
        """
        account = self.get_account(account_id)
        if not account:
            self.logger.warning(f"Account not found for assignment: {account_id}")
            return False
        
        if account.assign_user():
            self._save_accounts()
            self._log_operation("assign_user", {
                'account_id': account_id,
                'current_users': account.current_users,
                'max_users': account.max_concurrent_users
            })
            return True
        
        return False
    
    def release_user_from_account(self, account_id: str) -> bool:
        """
        Release a user from an account
        
        Args:
            account_id: Account ID
            
        Returns:
            True if release was successful
        """
        account = self.get_account(account_id)
        if not account:
            self.logger.warning(f"Account not found for release: {account_id}")
            return False
        
        if account.release_user():
            self._save_accounts()
            self._log_operation("release_user", {
                'account_id': account_id,
                'current_users': account.current_users,
                'max_users': account.max_concurrent_users
            })
            return True
        
        return False
    
    def update_account(self, account_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update account properties
        
        Args:
            account_id: Account ID
            updates: Dictionary of properties to update
            
        Returns:
            True if update was successful
        """
        try:
            account = self.get_account(account_id)
            if not account:
                return False
            
            # Validate updates
            if 'email' in updates and not validate_email(updates['email']):
                raise ValidationError("Invalid email format")
            
            if 'password' in updates and not validate_password(updates['password']):
                raise ValidationError("Invalid password")
            
            # Apply updates
            for key, value in updates.items():
                if hasattr(account, key):
                    setattr(account, key, value)
            
            # Update timestamp
            account.updated_at = datetime.now().isoformat()
            
            # Save changes
            self._save_accounts()
            
            self._log_operation("update_account", {
                'account_id': account_id,
                'updated_fields': list(updates.keys())
            })
            
            return True
            
        except Exception as e:
            self._handle_service_error("update_account", e)
            return False
    
    def delete_account(self, account_id: str) -> bool:
        """
        Delete an account
        
        Args:
            account_id: Account ID
            
        Returns:
            True if deletion was successful
        """
        try:
            if account_id not in self._accounts_cache:
                return False
            
            account = self._accounts_cache[account_id]
            
            # Check if account has active users
            if account.current_users > 0:
                self.logger.warning(f"Cannot delete account {account_id} with active users")
                return False
            
            # Remove from cache
            del self._accounts_cache[account_id]
            
            # Save changes
            self._save_accounts()
            
            self._log_operation("delete_account", {
                'account_id': account_id,
                'email': account.email
            })
            
            return True
            
        except Exception as e:
            self._handle_service_error("delete_account", e)
            return False
    
    def find_best_account_for_assignment(self, var_sku: str = "") -> Optional[ChatGPTAccount]:
        """
        Find the best account for user assignment
        
        Args:
            var_sku: Product variant SKU (for capacity matching)
            
        Returns:
            Best ChatGPTAccount for assignment or None
        """
        available_accounts = self.get_available_accounts()
        
        if not available_accounts:
            return None
        
        # If SKU provided, try to match capacity requirements
        if var_sku:
            required_users, _ = ChatGPTAccount.parse_capacity_from_sku(var_sku)
            
            # Find accounts that can handle the required capacity
            suitable_accounts = [
                acc for acc in available_accounts 
                if (acc.max_concurrent_users - acc.current_users) >= required_users
            ]
            
            if suitable_accounts:
                available_accounts = suitable_accounts
        
        # Return account with most available capacity
        return available_accounts[0]
    
    def _validate_service_config(self) -> bool:
        """Validate service configuration"""
        # No required config keys for this service
        return True
    
    def _load_accounts(self) -> bool:
        """Load accounts from storage"""
        try:
            data = load_json_data(self.data_file)
            accounts_data = data.get('accounts', [])
            
            self._accounts_cache.clear()
            
            for account_data in accounts_data:
                try:
                    account = ChatGPTAccount.from_dict(account_data)
                    self._accounts_cache[account.account_id] = account
                except Exception as e:
                    self.logger.warning(f"Failed to load account: {e}")
            
            self.logger.info(f"Loaded {len(self._accounts_cache)} accounts from storage")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load accounts: {e}")
            return False
    
    def _save_accounts(self) -> bool:
        """Save accounts to storage"""
        try:
            accounts_data = [account.to_dict() for account in self._accounts_cache.values()]
            
            data = {
                'accounts': accounts_data,
                'metadata': {
                    'version': '1.0.0',
                    'total_accounts': len(accounts_data),
                    'last_updated': datetime.now().isoformat()
                }
            }
            
            save_json_data(self.data_file, data)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save accounts: {e}")
            return False
    
    def _cleanup_expired_accounts(self) -> int:
        """
        Clean up expired accounts
        
        Returns:
            Number of accounts cleaned up
        """
        try:
            expired_count = 0
            expired_ids = []
            
            for account_id, account in self._accounts_cache.items():
                if account.is_expired() and account.current_users == 0:
                    expired_ids.append(account_id)
            
            for account_id in expired_ids:
                del self._accounts_cache[account_id]
                expired_count += 1
            
            if expired_count > 0:
                self._save_accounts()
                self.logger.info(f"Cleaned up {expired_count} expired accounts")
            
            return expired_count
            
        except Exception as e:
            self.logger.error(f"Error during account cleanup: {e}")
            return 0
