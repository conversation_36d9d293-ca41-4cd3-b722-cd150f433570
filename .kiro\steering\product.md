# SteamCodeTool Product Overview

SteamCodeTool is a comprehensive plugin-based system for managing digital product orders and customer service automation. It serves as a unified platform for handling Steam codes, Netflix sessions, VPN configurations, and Canva orders with integrated Shopee API support.

## Core Purpose
- **E-commerce Integration**: Seamlessly integrates with Shopee marketplace for order management
- **Digital Product Fulfillment**: Automates delivery of digital products (Steam codes, VPN configs, Netflix accounts, Canva Pro)
- **Customer Service Automation**: AI-powered chat responses and automated customer support
- **Multi-Service Management**: Unified dashboard for managing multiple digital services

## Key Features
- **Plugin Architecture**: Modular system allowing easy extension and customization
- **AI Chat Support**: Automated customer service with DeepSeek AI integration
- **Admin Dashboard**: Comprehensive management interface for all services
- **Docker Deployment**: One-click deployment with full containerization
- **Security**: Multi-level access control and audit logging
- **Real-time Monitoring**: Health checks and system monitoring

## Target Users
- Digital product sellers on Shopee marketplace
- E-commerce businesses selling VPN services, gaming codes, streaming accounts
- Customer service teams needing automation tools
- System administrators managing multiple digital services

## Business Model
Facilitates automated digital product sales and customer service for e-commerce platforms, particularly focused on the Malaysian market (Shopee Malaysia integration).