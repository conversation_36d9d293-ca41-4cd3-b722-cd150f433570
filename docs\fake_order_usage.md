# Fake Order System Usage Guide

## Overview

The fake order system allows you to create test orders for testing product processing without needing real Shopee orders. This is particularly useful for testing products like `canva_30` and other VAR SKUs.

## Features

- ✅ Create fake orders with custom order SNs and VAR SKUs
- ✅ Process fake orders through the normal order processing pipeline
- ✅ Support for all existing VAR SKUs (canva_30, netflix_30, text_based, etc.)
- ✅ Integration with existing order processing logic
- ✅ Web interface for managing fake orders

## Quick Start

### Method 1: Manual File Creation (Recommended for Testing)

1. **Create a fake order** by adding it to `configs/data/manual_orders.json`:

```json
[
  {
    "order_sn": "250612BWY37BUV",
    "var_sku": "canva_30",
    "buyer_username": "test_user",
    "status": "To Ship",
    "created_at": "2024-01-21T10:00:00",
    "is_fake_order": true,
    "order_type": "fake_test_order"
  }
]
```

2. **Test the order** by going to: http://localhost:5000/order

3. **Enter the order SN**: `250612BWY37BUV`

4. **Click "Process Order"** to test the functionality

### Method 2: Web Interface (Future Enhancement)

Visit: http://localhost:5000/fake_order (when server supports new routes)

### Method 3: API Endpoints

#### Create Fake Order
```bash
curl -X POST http://localhost:5000/api/create_fake_order \
  -H "Content-Type: application/json" \
  -d '{
    "order_sn": "250612BWY37BUV",
    "var_sku": "canva_30",
    "buyer_username": "test_user",
    "status": "To Ship"
  }'
```

#### List Fake Orders
```bash
curl http://localhost:5000/api/list_fake_orders
```

#### Delete Fake Order
```bash
curl -X DELETE http://localhost:5000/api/delete_fake_order/250612BWY37BUV
```

## Supported VAR SKUs

The system supports all configured VAR SKUs:

- `canva_30` - Canva 30 Days (unlimited stock)
- `netflix_30` - Netflix 30 Days (limited stock)
- `text_based` - Text Based Product (unlimited stock)
- `hulu_10` - Hulu 10 Days (limited stock)
- `steam_auth_code` - Steam Auth Code

## Example Test Scenarios

### Test Canva Product (Unlimited Stock)
```json
{
  "order_sn": "250612BWY37BUV",
  "var_sku": "canva_30",
  "buyer_username": "test_user",
  "status": "To Ship"
}
```

Expected result: Canva invitation message with unlimited stock

### Test Netflix Product (Limited Stock)
```json
{
  "order_sn": "TEST123NETFLIX",
  "var_sku": "netflix_30",
  "buyer_username": "netflix_tester",
  "status": "To Ship"
}
```

Expected result: Netflix account details (if stock available)

### Test Text-Based Product
```json
{
  "order_sn": "TEST456TEXTBASED",
  "var_sku": "text_based",
  "buyer_username": "text_tester",
  "status": "To Ship"
}
```

Expected result: Text-based message with unlimited stock

## How It Works

1. **Order Search**: When searching for an order, the system first checks manual invoices, then fake orders, then real Shopee API
2. **Order Details**: Fake orders return structured data similar to real Shopee orders
3. **Order Status**: Fake orders support all standard statuses (To Ship, Shipped, Completed, etc.)
4. **Order Processing**: Fake orders are processed through the same logic as real orders

## File Structure

```
configs/data/manual_orders.json  # Contains all fake orders
api/order_routes.py              # API endpoints for fake orders
services/order_service.py        # Core logic for fake order processing
templates/fake_order.html        # Web interface (future)
```

## Testing Workflow

1. **Create** a fake order with your desired VAR SKU
2. **Process** the order through http://localhost:5000/order
3. **Verify** the results match expected behavior
4. **Clean up** by removing the fake order when done

## Notes

- Fake orders are stored in `configs/data/manual_orders.json`
- They integrate seamlessly with existing order processing logic
- No real Shopee API calls are made for fake orders
- Perfect for testing without affecting real orders or inventory

## Troubleshooting

- **Order not found**: Check that the fake order exists in `manual_orders.json`
- **Processing fails**: Verify the VAR SKU is configured in `config.json`
- **API errors**: Some endpoints require API keys, but order processing doesn't

## Current Status

✅ **Working Features:**
- Manual fake order creation
- Order processing with fake orders
- Integration with existing VAR SKU system
- Support for all product types

🚧 **Future Enhancements:**
- Web interface for fake order management
- API endpoints (require server restart to register new routes)
- Bulk fake order creation
- Advanced testing scenarios
