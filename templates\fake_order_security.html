<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Fake Order Security Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-900">🧪 Fake Order Security Dashboard</h1>
                <div class="flex space-x-2">
                    <button onclick="runSecurityValidation()" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                        🔒 Run Security Check
                    </button>
                    <a href="/admin/audit-logs?filter=fake_order" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                        📋 View Audit Logs
                    </a>
                    <a href="/admin/dashboard" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>

            {% if error %}
            <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-red-700">{{ error }}</p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Security Status Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 font-semibold">🔒</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-green-600">Security Level</p>
                            <p class="text-2xl font-semibold text-green-900">{{ security_stats.security_level or 'High' }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-semibold">🧪</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-blue-600">Total Fake Orders</p>
                            <p class="text-2xl font-semibold text-blue-900">{{ security_stats.total_fake_orders or 0 }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <span class="text-yellow-600 font-semibold">⚠️</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-yellow-600">Security Violations</p>
                            <p class="text-2xl font-semibold text-yellow-900">{{ security_stats.security_violations or 0 }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span class="text-purple-600 font-semibold">🛡️</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-purple-600">Isolation Status</p>
                            <p class="text-2xl font-semibold text-purple-900">{{ security_stats.isolation_status or 'Active' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Features Status -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Security Features Status</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-sm text-gray-700">Fake Order Identification</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-sm text-gray-700">Visual Indicators (🧪)</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-sm text-gray-700">Audit Logging</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-sm text-gray-700">Access Control</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-sm text-gray-700">Production Isolation</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-sm text-gray-700">Report Filtering</span>
                    </div>
                </div>
            </div>

            <!-- Recent Security Operations -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Security Operations</h2>
                {% if recent_operations %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Timestamp
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Event Type
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Order SN
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Operation
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    User
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for operation in recent_operations %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ operation.timestamp }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    🧪 {{ operation.event_type }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ operation.order_sn }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ operation.operation }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ operation.user_id }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No recent operations</h3>
                    <p class="mt-1 text-sm text-gray-500">No fake order security operations have been logged recently.</p>
                </div>
                {% endif %}
            </div>

            <!-- Security Actions -->
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Security Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="runSecurityValidation()" 
                            class="flex items-center justify-center px-4 py-3 border border-blue-300 rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Run Security Validation
                    </button>
                    
                    <button onclick="cleanupOldFakeOrders()" 
                            class="flex items-center justify-center px-4 py-3 border border-yellow-300 rounded-md text-yellow-700 bg-yellow-50 hover:bg-yellow-100">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Cleanup Old Orders
                    </button>
                    
                    <a href="/admin/audit-logs?filter=fake_order" 
                       class="flex items-center justify-center px-4 py-3 border border-green-300 rounded-md text-green-700 bg-green-50 hover:bg-green-100">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        View Detailed Logs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function runSecurityValidation() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Running...';
            button.disabled = true;

            axios.get('/api/fake-orders/security-validation')
                .then(response => {
                    const validation = response.data.validation_results;
                    let message = `Security Validation Results:\n\n`;
                    message += `✅ Total Orders Checked: ${validation.total_checked}\n`;
                    message += `⚠️ Issues Found: ${validation.issues_found}\n`;
                    message += `🔍 Warnings: ${validation.warnings_found}\n`;
                    message += `🛡️ Isolation Status: ${validation.isolation_status}\n\n`;
                    
                    if (validation.issues_found > 0) {
                        message += `❌ Security issues detected! Please check the audit logs for details.\n`;
                        message += `Issues: ${validation.security_issues.join(', ')}`;
                    } else {
                        message += `✅ All fake orders are properly secured and isolated.`;
                    }
                    
                    alert(message);
                })
                .catch(error => {
                    console.error('Error during security validation:', error);
                    alert('Security validation failed. Check console for details.');
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }

        function cleanupOldFakeOrders() {
            if (confirm('This will remove fake orders older than 7 days. Continue?')) {
                axios.delete('/api/fake-orders/cleanup?older_than_days=7&confirm=true')
                    .then(response => {
                        const summary = response.data.cleanup_summary;
                        alert(`Cleanup completed:\n- Original count: ${summary.original_count}\n- Removed: ${summary.deleted_count}\n- Remaining: ${summary.remaining_count}`);
                        window.location.reload();
                    })
                    .catch(error => {
                        console.error('Error during cleanup:', error);
                        alert('Cleanup failed. Check console for details.');
                    });
            }
        }
    </script>
</body>
</html>