#!/usr/bin/env python3
"""
Performance Testing Script

This script provides comprehensive performance testing for the OpenAI Plus Redeem plugin
including load testing, memory monitoring, and response time analysis.
"""

import sys
import os
import time
import threading
import psutil
import statistics
from pathlib import Path
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Add plugin directory to Python path
plugin_dir = Path(__file__).parent
sys.path.insert(0, str(plugin_dir.parent.parent))

from plugins.openai_plus_redeem.models.chatgpt_account import ChatGPTAccount, AccountStatus
from plugins.openai_plus_redeem.services.chatgpt_account_service import ChatGPTAccountService


class PerformanceTester:
    """Performance testing utility"""
    
    def __init__(self):
        self.test_config = {
            'email_config': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'global_credentials': {
                    'email': '<EMAIL>',
                    'password': 'test_password'
                }
            },
            'cooldown_config': {
                'default_cooldown_hours': 24
            }
        }
        
        # Mock logger
        class MockLogger:
            def info(self, msg): print(f"INFO: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
            def debug(self, msg): pass
        
        self.logger = MockLogger()
        self.results = {}
    
    def test_service_initialization_performance(self):
        """Test service initialization performance"""
        print("\n🚀 Testing Service Initialization Performance")
        print("=" * 60)
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Initialize account service
        account_service = ChatGPTAccountService(self.test_config, self.logger)
        init_result = account_service.initialize()
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        initialization_time = end_time - start_time
        memory_increase = end_memory - start_memory
        
        print(f"✅ Service initialized: {init_result}")
        print(f"⏱️  Initialization time: {initialization_time:.3f} seconds")
        print(f"💾 Memory increase: {memory_increase:.2f} MB")
        
        self.results['initialization'] = {
            'time': initialization_time,
            'memory_increase': memory_increase,
            'success': init_result
        }
        
        return account_service
    
    def test_bulk_account_creation_performance(self, account_service, num_accounts=1000):
        """Test bulk account creation performance"""
        print(f"\n📊 Testing Bulk Account Creation ({num_accounts} accounts)")
        print("=" * 60)
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        successful_creations = 0
        failed_creations = 0
        
        for i in range(num_accounts):
            account = ChatGPTAccount(
                account_id=f'PERF_BULK_{i:05d}',
                email=f'bulk{i}@example.com',
                password=f'password{i}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            result = account_service.add_account(account)
            if result['success']:
                successful_creations += 1
            else:
                failed_creations += 1
            
            # Progress indicator
            if (i + 1) % 100 == 0:
                print(f"  📈 Created {i + 1}/{num_accounts} accounts...")
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        total_time = end_time - start_time
        memory_increase = end_memory - start_memory
        accounts_per_second = num_accounts / total_time
        
        print(f"✅ Successful creations: {successful_creations}")
        print(f"❌ Failed creations: {failed_creations}")
        print(f"⏱️  Total time: {total_time:.2f} seconds")
        print(f"🚀 Accounts per second: {accounts_per_second:.2f}")
        print(f"💾 Memory increase: {memory_increase:.2f} MB")
        print(f"📊 Memory per account: {memory_increase/num_accounts:.3f} MB")
        
        self.results['bulk_creation'] = {
            'total_time': total_time,
            'accounts_per_second': accounts_per_second,
            'memory_increase': memory_increase,
            'success_rate': successful_creations / num_accounts,
            'successful_creations': successful_creations,
            'failed_creations': failed_creations
        }
    
    def test_concurrent_operations_performance(self, account_service, num_workers=50):
        """Test concurrent operations performance"""
        print(f"\n⚡ Testing Concurrent Operations ({num_workers} workers)")
        print("=" * 60)
        
        def worker_task(worker_id):
            """Worker task for concurrent testing"""
            start_time = time.time()
            
            # Create account
            account = ChatGPTAccount(
                account_id=f'CONCURRENT_{worker_id:03d}',
                email=f'concurrent{worker_id}@example.com',
                password=f'password{worker_id}',
                max_concurrent_users=5,
                current_users=0,
                expiration_date=datetime.now() + timedelta(days=30),
                status=AccountStatus.ACTIVE,
                created_date=datetime.now()
            )
            
            result = account_service.add_account(account)
            
            # Query account
            if result['success']:
                retrieved_account = account_service.get_account_by_id(f'CONCURRENT_{worker_id:03d}')
                query_success = retrieved_account is not None
            else:
                query_success = False
            
            end_time = time.time()
            
            return {
                'worker_id': worker_id,
                'create_success': result['success'],
                'query_success': query_success,
                'response_time': end_time - start_time
            }
        
        # Run concurrent operations
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(worker_task, i) for i in range(num_workers)]
            results = [future.result() for future in as_completed(futures)]
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful_creates = sum(1 for r in results if r['create_success'])
        successful_queries = sum(1 for r in results if r['query_success'])
        response_times = [r['response_time'] for r in results]
        
        avg_response_time = statistics.mean(response_times)
        median_response_time = statistics.median(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        print(f"✅ Successful creates: {successful_creates}/{num_workers}")
        print(f"✅ Successful queries: {successful_queries}/{num_workers}")
        print(f"⏱️  Total time: {total_time:.2f} seconds")
        print(f"📊 Average response time: {avg_response_time:.3f} seconds")
        print(f"📊 Median response time: {median_response_time:.3f} seconds")
        print(f"📊 Min response time: {min_response_time:.3f} seconds")
        print(f"📊 Max response time: {max_response_time:.3f} seconds")
        print(f"🚀 Operations per second: {(num_workers * 2) / total_time:.2f}")
        
        self.results['concurrent_operations'] = {
            'total_time': total_time,
            'create_success_rate': successful_creates / num_workers,
            'query_success_rate': successful_queries / num_workers,
            'avg_response_time': avg_response_time,
            'median_response_time': median_response_time,
            'max_response_time': max_response_time,
            'operations_per_second': (num_workers * 2) / total_time
        }
    
    def test_query_performance(self, account_service):
        """Test query performance with large dataset"""
        print("\n🔍 Testing Query Performance")
        print("=" * 60)
        
        # Test different query operations
        query_tests = [
            ('get_all_accounts', lambda: account_service.get_all_accounts()),
            ('get_available_account', lambda: account_service.get_available_account(5, 30)),
            ('get_account_by_email', lambda: account_service.get_account_by_email('<EMAIL>')),
            ('get_account_by_id', lambda: account_service.get_account_by_id('PERF_BULK_00500'))
        ]
        
        for test_name, query_func in query_tests:
            # Warm up
            query_func()
            
            # Measure performance
            times = []
            for _ in range(10):  # Run 10 times for average
                start_time = time.time()
                result = query_func()
                end_time = time.time()
                times.append(end_time - start_time)
            
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"  📊 {test_name}:")
            print(f"    Average: {avg_time:.3f}s")
            print(f"    Min: {min_time:.3f}s")
            print(f"    Max: {max_time:.3f}s")
            
            self.results[f'query_{test_name}'] = {
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time
            }
    
    def test_memory_usage_patterns(self, account_service):
        """Test memory usage patterns"""
        print("\n💾 Testing Memory Usage Patterns")
        print("=" * 60)
        
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_samples = [initial_memory]
        
        # Create accounts in batches and monitor memory
        batch_size = 100
        num_batches = 10
        
        for batch in range(num_batches):
            batch_start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            for i in range(batch_size):
                account = ChatGPTAccount(
                    account_id=f'MEMORY_TEST_{batch:02d}_{i:03d}',
                    email=f'memory{batch}{i}@example.com',
                    password=f'password{batch}{i}',
                    max_concurrent_users=5,
                    current_users=0,
                    expiration_date=datetime.now() + timedelta(days=30),
                    status=AccountStatus.ACTIVE,
                    created_date=datetime.now()
                )
                
                account_service.add_account(account)
            
            batch_end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_samples.append(batch_end_memory)
            
            print(f"  📊 Batch {batch + 1}: {batch_end_memory - batch_start_memory:.2f} MB increase")
        
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        avg_per_batch = total_increase / num_batches
        
        print(f"💾 Initial memory: {initial_memory:.2f} MB")
        print(f"💾 Final memory: {final_memory:.2f} MB")
        print(f"💾 Total increase: {total_increase:.2f} MB")
        print(f"💾 Average per batch: {avg_per_batch:.2f} MB")
        
        self.results['memory_usage'] = {
            'initial_memory': initial_memory,
            'final_memory': final_memory,
            'total_increase': total_increase,
            'avg_per_batch': avg_per_batch,
            'memory_samples': memory_samples
        }
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        print("\n📊 PERFORMANCE REPORT")
        print("=" * 80)
        
        # Summary metrics
        if 'initialization' in self.results:
            init = self.results['initialization']
            print(f"🚀 Initialization: {init['time']:.3f}s, {init['memory_increase']:.2f}MB")
        
        if 'bulk_creation' in self.results:
            bulk = self.results['bulk_creation']
            print(f"📊 Bulk Creation: {bulk['accounts_per_second']:.2f} accounts/sec")
            print(f"   Success Rate: {bulk['success_rate']*100:.1f}%")
        
        if 'concurrent_operations' in self.results:
            concurrent = self.results['concurrent_operations']
            print(f"⚡ Concurrent Ops: {concurrent['operations_per_second']:.2f} ops/sec")
            print(f"   Avg Response: {concurrent['avg_response_time']:.3f}s")
        
        # Performance grades
        print("\n🏆 PERFORMANCE GRADES")
        print("-" * 40)
        
        grades = []
        
        # Grade initialization
        if 'initialization' in self.results:
            init_time = self.results['initialization']['time']
            if init_time < 1.0:
                grades.append(("Initialization", "A+"))
            elif init_time < 2.0:
                grades.append(("Initialization", "A"))
            elif init_time < 5.0:
                grades.append(("Initialization", "B"))
            else:
                grades.append(("Initialization", "C"))
        
        # Grade bulk operations
        if 'bulk_creation' in self.results:
            rate = self.results['bulk_creation']['accounts_per_second']
            if rate > 100:
                grades.append(("Bulk Creation", "A+"))
            elif rate > 50:
                grades.append(("Bulk Creation", "A"))
            elif rate > 20:
                grades.append(("Bulk Creation", "B"))
            else:
                grades.append(("Bulk Creation", "C"))
        
        for test_name, grade in grades:
            print(f"{test_name:20}: {grade}")
        
        return self.results
    
    def run_comprehensive_performance_test(self):
        """Run comprehensive performance test suite"""
        print("🚀 OpenAI Plus Redeem Plugin - Performance Testing")
        print("=" * 80)
        
        try:
            # Initialize service
            account_service = self.test_service_initialization_performance()
            
            # Run performance tests
            self.test_bulk_account_creation_performance(account_service, 1000)
            self.test_concurrent_operations_performance(account_service, 50)
            self.test_query_performance(account_service)
            self.test_memory_usage_patterns(account_service)
            
            # Generate report
            results = self.generate_performance_report()
            
            return results
            
        except Exception as e:
            print(f"❌ Performance test failed: {e}")
            return None


def main():
    """Main entry point"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        print("Usage:")
        print("  python run_performance_tests.py    # Run comprehensive performance tests")
        return 0
    
    tester = PerformanceTester()
    results = tester.run_comprehensive_performance_test()
    
    if results:
        print("\n✅ Performance testing completed successfully!")
        return 0
    else:
        print("\n❌ Performance testing failed!")
        return 1


if __name__ == '__main__':
    sys.exit(main())
