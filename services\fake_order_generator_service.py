"""
Fake Order Generator Service

This service provides comprehensive fake order generation capabilities that create
realistic orders matching the Shopee API structure for testing purposes.
"""

import json
import os
import random
import string
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from typing import List, Dict, Any, Optional
import logging

# Import the new product template engine
from .product_template_engine import ProductTemplateEngine, ProductTemplate

logger = logging.getLogger(__name__)

@dataclass
class FakeOrderConfig:
    """Configuration for generating a fake order"""
    order_sn: str = ""
    var_sku: str = "canva_30"
    buyer_username: str = "test_user"
    buyer_name: str = "Test Customer"
    buyer_phone: str = "+60123456789"
    buyer_email: str = "<EMAIL>"
    buyer_address: str = "123 Test Street, Test City, Test State"
    status: str = "To Ship"
    payment_status: str = "paid"
    quantity: int = 1
    custom_metadata: dict = field(default_factory=dict)
    test_scenario: str = "general_testing"
    created_date: Optional[str] = None
    price_override: Optional[float] = None

class FakeOrderGeneratorService:
    """Service for generating realistic fake orders"""
    
    def __init__(self):
        self.fake_orders_file = 'configs/data/fake_orders.json'
        self.template_engine = ProductTemplateEngine()
        self._ensure_data_files()
    
    def _ensure_data_files(self):
        """Ensure required data files exist"""
        os.makedirs('configs/data', exist_ok=True)
        
        if not os.path.exists(self.fake_orders_file):
            with open(self.fake_orders_file, 'w', encoding='utf-8') as f:
                json.dump([], f, indent=2)
    
    def generate_unique_order_sn(self, prefix: str = "FAKE_") -> str:
        """Generate a unique order serial number with conflict detection"""
        max_attempts = 100
        
        for _ in range(max_attempts):
            # Generate random alphanumeric string
            random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
            order_sn = f"{prefix}{random_part}"
            
            # Check for conflicts with existing fake orders
            if not self._order_sn_exists(order_sn):
                return order_sn
        
        # Fallback with timestamp if all attempts fail
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"{prefix}{timestamp}"
    
    def _order_sn_exists(self, order_sn: str) -> bool:
        """Check if order SN already exists in fake orders"""
        try:
            with open(self.fake_orders_file, 'r', encoding='utf-8') as f:
                fake_orders = json.load(f)
            
            return any(order.get('order_sn') == order_sn for order in fake_orders)
        except (FileNotFoundError, json.JSONDecodeError):
            return False
    
    def validate_order_config(self, config: FakeOrderConfig) -> Dict[str, Any]:
        """Validate order configuration and return validation result"""
        errors = []
        warnings = []
        
        # Validate SKU using template engine
        sku_validation = self.template_engine.validate_sku(config.var_sku)
        if not sku_validation["is_valid"]:
            errors.append(sku_validation["error"])
            if sku_validation.get("similar_skus"):
                warnings.append(f"Similar SKUs available: {sku_validation['similar_skus']}")
        
        # Validate quantity
        if config.quantity <= 0:
            errors.append("Quantity must be greater than 0")
        
        # Validate phone format
        if not config.buyer_phone.startswith('+'):
            warnings.append("Phone number should start with country code (+)")
        
        # Validate email format
        if '@' not in config.buyer_email:
            errors.append("Invalid email format")
        
        # Validate status
        valid_statuses = ["To Ship", "Shipped", "Completed", "Cancelled", "Unpaid"]
        if config.status not in valid_statuses:
            errors.append(f"Invalid status '{config.status}'. Valid statuses: {valid_statuses}")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def generate_order(self, config: FakeOrderConfig) -> Dict[str, Any]:
        """Generate a single fake order with complete Shopee API structure"""
        # Validate configuration
        validation = self.validate_order_config(config)
        if not validation["is_valid"]:
            raise ValueError(f"Invalid configuration: {validation['errors']}")
        
        # Generate order SN if not provided
        if not config.order_sn:
            config.order_sn = self.generate_unique_order_sn()
        
        # Get product template using template engine
        template = self.template_engine.get_template_for_sku(config.var_sku)
        if not template:
            raise ValueError(f"No template found for SKU: {config.var_sku}")
        
        # Generate product-specific metadata using template engine
        product_metadata = self.template_engine.generate_product_metadata(
            config.var_sku, 
            config.custom_metadata
        )
        
        # Generate timestamps
        created_time = config.created_date or datetime.now().isoformat()
        
        # Calculate price
        price = config.price_override or template.default_price
        total_price = price * config.quantity
        
        # Create complete order structure matching Shopee API format
        order_data = {
            "message": "Fake order details fetched successfully",
            "data": {
                "order_sn": config.order_sn,
                "buyer_user": {
                    "user_name": config.buyer_username
                },
                "order_items": [{
                    "var_sku": config.var_sku,
                    "product": {
                        "name": template.product_name
                    },
                    "item_model": {
                        "sku": config.var_sku
                    },
                    "quantity": config.quantity,
                    "price": price,
                    "total_price": price * config.quantity
                }],
                "total_price": total_price,
                "buyer_address_name": config.buyer_name,
                "buyer_address_phone": config.buyer_phone,
                "buyer_address_email": config.buyer_email,
                "buyer_address_full": config.buyer_address,
                "create_time": created_time,
                "status": config.status,
                "payment_status": config.payment_status,
                "is_fake_order": True,
                "fake_order_metadata": {
                    "created_by": "fake_order_generator_service",
                    "test_scenario": config.test_scenario,
                    "product_category": template.category,
                    "generated_at": datetime.now().isoformat(),
                    "template_data": product_metadata,
                    **config.custom_metadata
                }
            }
        }
        
        # Store the fake order
        self._store_fake_order(order_data)
        
        logger.info(f"Generated fake order: {config.order_sn} for SKU: {config.var_sku}")
        
        return order_data
    
    def generate_batch_orders(self, configs: List[FakeOrderConfig]) -> List[Dict[str, Any]]:
        """Generate multiple fake orders"""
        results = []
        errors = []
        
        for i, config in enumerate(configs):
            try:
                order_data = self.generate_order(config)
                results.append({
                    "index": i,
                    "success": True,
                    "order_data": order_data,
                    "order_sn": order_data["data"]["order_sn"]
                })
            except Exception as e:
                error_msg = str(e)
                errors.append({
                    "index": i,
                    "success": False,
                    "error": error_msg,
                    "config": asdict(config)
                })
                logger.error(f"Failed to generate fake order at index {i}: {error_msg}")
        
        logger.info(f"Batch generation completed: {len(results)} successful, {len(errors)} failed")
        
        return {
            "successful_orders": results,
            "failed_orders": errors,
            "summary": {
                "total_requested": len(configs),
                "successful": len(results),
                "failed": len(errors)
            }
        }
    
    def _store_fake_order(self, order_data: Dict[str, Any]):
        """Store fake order data to file"""
        try:
            # Load existing fake orders
            with open(self.fake_orders_file, 'r', encoding='utf-8') as f:
                fake_orders = json.load(f)
            
            # Add new order
            fake_orders.append({
                "order_sn": order_data["data"]["order_sn"],
                "var_sku": order_data["data"]["order_items"][0]["var_sku"],
                "buyer_username": order_data["data"]["buyer_user"]["user_name"],
                "status": order_data["data"]["status"],
                "created_at": order_data["data"]["create_time"],
                "is_fake_order": True,
                "order_type": "fake_test_order",
                "test_scenario": order_data["data"]["fake_order_metadata"]["test_scenario"],
                "full_order_data": order_data
            })
            
            # Save updated list
            with open(self.fake_orders_file, 'w', encoding='utf-8') as f:
                json.dump(fake_orders, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to store fake order: {e}")
            raise
    
    def get_product_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get all available product templates"""
        templates = self.template_engine.get_all_templates()
        return {
            sku: {
                "var_sku": template.var_sku,
                "product_name": template.product_name,
                "category": template.category,
                "default_price": template.default_price,
                "sample_data": template.sample_data
            }
            for sku, template in templates.items()
        }
    
    def get_fake_orders(self, limit: Optional[int] = None, 
                       status_filter: Optional[str] = None,
                       sku_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get stored fake orders with optional filtering"""
        try:
            with open(self.fake_orders_file, 'r', encoding='utf-8') as f:
                fake_orders = json.load(f)
            
            # Apply filters
            if status_filter:
                fake_orders = [order for order in fake_orders if order.get('status') == status_filter]
            
            if sku_filter:
                fake_orders = [order for order in fake_orders if order.get('var_sku') == sku_filter]
            
            # Apply limit
            if limit:
                fake_orders = fake_orders[:limit]
            
            return fake_orders
            
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def cleanup_fake_orders(self, older_than_days: int = 7) -> Dict[str, int]:
        """Clean up old fake orders"""
        try:
            with open(self.fake_orders_file, 'r', encoding='utf-8') as f:
                fake_orders = json.load(f)
            
            cutoff_date = datetime.now() - timedelta(days=older_than_days)
            original_count = len(fake_orders)
            
            # Filter out old orders
            filtered_orders = []
            for order in fake_orders:
                try:
                    created_at = datetime.fromisoformat(order.get('created_at', ''))
                    if created_at > cutoff_date:
                        filtered_orders.append(order)
                except (ValueError, TypeError):
                    # Keep orders with invalid dates
                    filtered_orders.append(order)
            
            # Save filtered orders
            with open(self.fake_orders_file, 'w', encoding='utf-8') as f:
                json.dump(filtered_orders, f, indent=2, ensure_ascii=False)
            
            removed_count = original_count - len(filtered_orders)
            logger.info(f"Cleaned up {removed_count} fake orders older than {older_than_days} days")
            
            return {
                "original_count": original_count,
                "removed_count": removed_count,
                "remaining_count": len(filtered_orders)
            }
            
        except Exception as e:
            logger.error(f"Failed to cleanup fake orders: {e}")
            raise


# Global service instance
fake_order_service = FakeOrderGeneratorService()