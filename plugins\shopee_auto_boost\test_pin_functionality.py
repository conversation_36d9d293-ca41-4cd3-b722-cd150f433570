#!/usr/bin/env python3
"""
Test script for Shopee Auto Boost Plugin PIN functionality

This script demonstrates how to use the PIN functionality to automatically
boost specific products with individual cooldown tracking.
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000/api/shopee_auto_boost"
EXAMPLE_PRODUCT_IDS = [123456789, 987654321, 111222333, 444555666, 777888999]

def test_pin_functionality():
    """Test the PIN functionality of the auto boost plugin"""
    
    print("=== Shopee Auto Boost Plugin - PIN Functionality Test ===\n")
    
    # 1. Get current status
    print("1. Getting current plugin status...")
    try:
        response = requests.get(f"{BASE_URL}/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   Plugin Status: {status.get('plugin_status', 'Unknown')}")
            print(f"   Scheduler Running: {status.get('scheduler_running', False)}")
            
            pin_config = status.get('config', {}).get('pinned_products', {})
            print(f"   PIN Mode Enabled: {pin_config.get('enabled', False)}")
            print(f"   Cooldown Hours: {pin_config.get('cooldown_hours', 4)}")
        else:
            print(f"   Error: {response.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # 2. Get current pinned products
    print("2. Getting current pinned products...")
    try:
        response = requests.get(f"{BASE_URL}/pinned")
        if response.status_code == 200:
            data = response.json()['data']
            print(f"   Total Pinned: {data.get('total_pinned', 0)}")
            print(f"   PIN Mode Enabled: {data.get('enabled', False)}")
            
            for product in data.get('pinned_products', []):
                print(f"   - Product {product['id']}: {product['name']} (Stock: {product['stock']})")
        else:
            print(f"   Error: {response.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # 3. Pin example products (if not already pinned)
    print("3. Pinning example products...")
    for product_id in EXAMPLE_PRODUCT_IDS[:3]:  # Pin first 3 as example
        try:
            response = requests.post(f"{BASE_URL}/pin/{product_id}")
            if response.status_code == 200:
                print(f"   ✓ Product {product_id} pinned successfully")
            elif response.status_code == 400:
                print(f"   - Product {product_id} already pinned")
            else:
                print(f"   ✗ Error pinning product {product_id}: {response.status_code}")
        except Exception as e:
            print(f"   ✗ Error pinning product {product_id}: {e}")
    
    print()
    
    # 4. Get cooldown status
    print("4. Getting cooldown status...")
    try:
        response = requests.get(f"{BASE_URL}/cooldowns")
        if response.status_code == 200:
            data = response.json()['data']
            print(f"   Total Pinned: {data.get('total_pinned', 0)}")
            print(f"   Available for Boost: {data.get('available_for_boost', 0)}")
            print(f"   On Cooldown: {data.get('on_cooldown', 0)}")
            
            for product in data.get('pinned_products', []):
                status_text = "On Cooldown" if product['on_cooldown'] else "Available"
                print(f"   - Product {product['product_id']}: {status_text}")
                if product.get('next_available_boost'):
                    print(f"     Next available: {product['next_available_boost']}")
                print(f"     Successful boosts: {product.get('total_successful_boosts', 0)}")
        else:
            print(f"   Error: {response.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # 5. Trigger manual boost to test PIN functionality
    print("5. Triggering manual boost to test PIN functionality...")
    try:
        response = requests.post(f"{BASE_URL}/boost/manual")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✓ Manual boost completed")
            print(f"   PIN Mode: {result.get('pin_mode', False)}")
            print(f"   Message: {result.get('message', 'No message')}")
            
            if 'results' in result:
                success_count = len(result['results'].get('success', []))
                failed_count = len(result['results'].get('failed', []))
                print(f"   Successful: {success_count}, Failed: {failed_count}")
                
                for success_item in result['results'].get('success', []):
                    print(f"   ✓ Boosted: {success_item.get('name', 'Unknown')} (ID: {success_item.get('id')})")
                
                for failed_item in result['results'].get('failed', []):
                    print(f"   ✗ Failed: {failed_item.get('name', 'Unknown')} (ID: {failed_item.get('id')})")
        else:
            print(f"   Error: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # 6. Check cooldown status after boost
    print("6. Checking cooldown status after boost...")
    try:
        response = requests.get(f"{BASE_URL}/cooldowns")
        if response.status_code == 200:
            data = response.json()['data']
            print(f"   Available for Boost: {data.get('available_for_boost', 0)}")
            print(f"   On Cooldown: {data.get('on_cooldown', 0)}")
            
            for product in data.get('pinned_products', []):
                status_text = "On Cooldown" if product['on_cooldown'] else "Available"
                print(f"   - Product {product['product_id']}: {status_text}")
                if product.get('last_successful_boost'):
                    print(f"     Last boosted: {product['last_successful_boost']}")
        else:
            print(f"   Error: {response.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # 7. Demonstrate unpinning
    print("7. Demonstrating unpin functionality...")
    if EXAMPLE_PRODUCT_IDS:
        product_to_unpin = EXAMPLE_PRODUCT_IDS[0]
        try:
            response = requests.delete(f"{BASE_URL}/pin/{product_to_unpin}")
            if response.status_code == 200:
                print(f"   ✓ Product {product_to_unpin} unpinned successfully")
            else:
                print(f"   Error unpinning product {product_to_unpin}: {response.status_code}")
        except Exception as e:
            print(f"   Error unpinning product {product_to_unpin}: {e}")
    
    print("\n=== Test Complete ===")
    print("\nNOTE: This test uses example product IDs that may not exist in your Shopee store.")
    print("Replace EXAMPLE_PRODUCT_IDS with actual product IDs from your store for real testing.")

if __name__ == "__main__":
    test_pin_functionality()
