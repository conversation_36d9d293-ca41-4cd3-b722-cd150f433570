# VPN Config Generator Plugin

A dedicated plugin for generating VPN configurations with chat command integration and webhook support.

## Overview

The VPN Config Generator plugin provides comprehensive VPN configuration generation capabilities, including:

- **Chat Command Integration**: Handles the `#config` command for VPN configuration generation
- **Webhook Support**: Receives and processes webhook messages from ShopeeAPI
- **Template Management**: Configurable VPN configuration templates
- **API Integration**: Supports both VPN plugin API and fallback API methods
- **Configuration Management**: Web-based configuration interface

## Features

### Chat Commands

- `#v <server> <days> <telco> <plan>` - Generate VPN configuration
- `#vlist` - List available servers, telcos, and plans
- `#vservers` - List all VPN servers with their IDs and status
- `#vuser [username]` - View VPN configurations for a user
- `#vdel <client_id>` - Delete a VPN configuration
- `#vrenew <client_id> <days>` - Renew/extend a VPN configuration
- `#vtest` - Test VPN API connectivity

Example:
```
#vservers                 # List all servers with IDs
#v 11 30 digi unlimited   # Generate VPN configuration using server ID 11
```

This will:
1. Generate a VPN configuration using the configured API
2. Send configuration information with creation and expiration dates
3. Send tutorial instructions
4. Send the actual configuration file

### Configuration Generation

The plugin supports two methods for VPN configuration generation:

1. **VPN Plugin API** (Recommended): Uses the existing VPN plugin's API service
2. **Fallback API**: Direct API calls to external VPN configuration services

### Telco & Plan Management System

The plugin uses a hierarchical configuration system organized by telecommunications providers (telcos) and their plans:

- **Telcos**: Telecommunications providers (e.g., Digi, Umobile, Celcom, Maxis, Yes, Yoodo, Unifi)
- **Plans**: Specific service plans within each telco (e.g., Digi Social, Umobile 5G Ready)
- **Templates**: Each plan has its own VPN configuration template with specific parameters

#### Structure:
```
Telco (e.g., Digi)
├── Plan 1 (e.g., Social) → Configuration Template
├── Plan 2 (e.g., 3Mbps Booster) → Configuration Template
└── Plan 3 (e.g., Unlimited) → Configuration Template
```

#### Supported Telcos and Plans:
- **Digi**: Social, 3Mbps Booster
- **U Mobile**: 5G Ready, Funz
- **Celcom**: Booster 1, Booster 2
- **Maxis**: Sabah Freeze
- **Yes**: No Plan
- **Yoodo**: Booster 1, Booster 2, PUBG, Mobile Legend
- **Unifi**: Bebas, 5G Wow

### Template System

- Each plan has its own configuration template with variable substitution
- Support for multiple VPN protocols (VLESS, VMess, Trojan, etc.)
- Organized by telco and plan for easy management
- Variables include: {uuid}, {server}, {identity}, {username}, {days}, {telco}, {plan}

## Configuration

### API Configuration

```json
{
  "api_config": {
    "enabled": true,
    "use_vpn_plugin_api": true,
    "fallback_api_endpoint": "https://blueblue.api.limjianhui.com/openapi.json",
    "fallback_username": "admin",
    "fallback_password": "admin",
    "timeout": 30
  }
}
```

### Generator Settings

```json
{
  "generator_settings": {
    "default_validity_days": 30,
    "username_prefix": "user",
    "add_random_suffix": true,
    "config_format": "vless"
  }
}
```

### Webhook Configuration

```json
{
  "webhook_config": {
    "enabled": true,
    "shopee_api_base_url": "https://shop.api.limjianhui.com",
    "steamcodetool_base_url": "http://localhost:5000",
    "webhook_endpoint": "/vpn-config-generator/api/webhook",
    "auto_register": true,
    "retry_count": 3,
    "retry_delay": 5,
    "timeout": 30
  }
}
```

### Telco Configuration Structure

The telco configurations are stored in `telco_configs.json`:

```json
{
  "telcos": {
    "digi": {
      "id": "digi",
      "name": "Digi",
      "description": "Digi Telecommunications Malaysia",
      "enabled": true,
      "plans": {
        "social": {
          "id": "social",
          "name": "Social",
          "description": "Digi Social 3Mbps Plan",
          "template": "vless://{uuid}@m.facebook.com.{server}:80?security=none&encryption=none&type=ws&headerType=none&path=/&host=m.facebook.com#DigiSocial3Mbps_{identity}",
          "variables": {
            "uuid": "Client UUID",
            "server": "Server domain",
            "identity": "User identity"
          },
          "enabled": true
        }
      }
    }
  }
}
```

## Web Interface

- `/vpn-config-generator/` - Main dashboard
- `/vpn-config-generator/telcos` - Telco and Plan management interface

## API Endpoints

### Configuration Generation

- `POST /vpn-config-generator/api/generate` - Generate VPN configuration
- `GET /vpn-config-generator/api/config` - Get current configuration
- `PUT /vpn-config-generator/api/config/api` - Update API configuration
- `PUT /vpn-config-generator/api/config/generator` - Update generator settings
- `POST /vpn-config-generator/api/test-connection` - Test VPN API connection

### Telco & Plan Management

- `GET /vpn-config-generator/api/telcos` - Get all telcos
- `GET /vpn-config-generator/api/telcos/<telco_id>` - Get specific telco
- `POST /vpn-config-generator/api/telcos` - Create new telco
- `PUT /vpn-config-generator/api/telcos/<telco_id>` - Update telco
- `DELETE /vpn-config-generator/api/telcos/<telco_id>` - Delete telco

### Plan Management

- `GET /vpn-config-generator/api/telcos/<telco_id>/plans` - Get all plans for a telco
- `GET /vpn-config-generator/api/telcos/<telco_id>/plans/<plan_id>` - Get specific plan
- `POST /vpn-config-generator/api/telcos/<telco_id>/plans` - Create new plan
- `PUT /vpn-config-generator/api/telcos/<telco_id>/plans/<plan_id>` - Update plan
- `DELETE /vpn-config-generator/api/telcos/<telco_id>/plans/<plan_id>` - Delete plan

### Legacy Template Management (Deprecated)

- `GET /vpn-config-generator/api/templates` - Get all templates
- `POST /vpn-config-generator/api/templates` - Create new template
- `GET /vpn-config-generator/api/templates/{id}` - Get specific template
- `PUT /vpn-config-generator/api/templates/{id}` - Update template
- `DELETE /vpn-config-generator/api/templates/{id}` - Delete template
- `POST /vpn-config-generator/api/templates/{id}/generate` - Generate config from template

### Webhook Integration

- `POST /vpn-config-generator/api/webhook` - Webhook endpoint for chat commands
- `GET /vpn-config-generator/api/webhook/config` - Get webhook configuration
- `PUT /vpn-config-generator/api/webhook/config` - Update webhook configuration
- `GET /vpn-config-generator/api/commands` - Get all VPN chat commands

## Installation and Setup

1. **Enable the Plugin**: Ensure the VPN Config Generator plugin is enabled in the plugin manager

2. **Configure API Settings**: Set up the VPN API configuration through the web interface

3. **Configure Webhook**: Set up webhook integration with ShopeeAPI

4. **Test Configuration**: Use the test endpoints to verify connectivity

## Integration with ShopeeAPI

The plugin automatically registers webhook endpoints with ShopeeAPI to receive chat messages. When a user sends a `#config` command, the plugin:

1. Receives the webhook message
2. Parses the command parameters
3. Generates the VPN configuration
4. Sends responses back to the user via ShopeeAPI

## Dependencies

- **VPN Plugin** (Optional): For VPN plugin API integration
- **ShopeeAPI**: For webhook message handling
- **Flask**: For web interface and API endpoints

## Migration from Chat Commands Plugin

This plugin replaces the VPN configuration functionality previously handled by the Chat Commands plugin. The migration includes:

- Moving the `#config` command handling
- Transferring VPN-related configurations
- Maintaining compatibility with existing workflows
- Enhanced features and better separation of concerns

## Troubleshooting

### Common Issues

1. **VPN Plugin API Not Available**: Ensure the VPN plugin is enabled and properly configured
2. **Webhook Not Receiving Messages**: Check webhook registration with ShopeeAPI
3. **Configuration Generation Fails**: Verify API credentials and connectivity
4. **Template Variables Not Substituted**: Check template syntax and variable names

### Debug Mode

Enable debug logging in the plugin configuration to get detailed information about:
- Webhook message processing
- API calls and responses
- Configuration generation steps
- Error details and stack traces

## Support

For issues and feature requests related to VPN configuration generation, please refer to the plugin logs and configuration settings. The plugin provides comprehensive error messages and debugging information to help troubleshoot issues.
