# VPN Order Management System - Complete Implementation

## Overview

This document describes the complete VPN order management system that has been implemented to handle VPN configuration generation based on Shopee orders. The system provides a comprehensive solution for managing VPN orders, user authentication, UUID management, and configuration generation.

## System Architecture

### Core Components

1. **VPNOrderService** - Handles order processing and user management
2. **Order Configuration Interface** - Web-based UI for order management
3. **API Endpoints** - RESTful APIs for order verification and config generation
4. **Telco & Plan Management** - Configuration templates for different providers
5. **UUID Management** - Handles creation and renewal of VPN configurations

### Key Features

- **Order Verification**: Validates Shopee orders and creates user profiles
- **UUID Management**: Creates new UUIDs or renews existing ones
- **Telco Restrictions**: SKU-based access control for different providers
- **Server Selection**: Intelligent server assignment based on SKU tags
- **Configuration Generation**: Creates VLESS configs using telco-specific templates
- **Renewal System**: Extends existing configurations without creating new UUIDs

## File Structure

```
plugins/vpn_config_generator/
├── templates/vpn_config_generator/
│   └── order_config.html          # Main order management interface
├── services.py                    # VPNOrderService and related services
├── routes.py                      # API endpoints and web routes
├── models.py                      # Data models and structures
├── telco_configs.json            # Telco and plan configurations
└── VPN_ORDER_MANAGEMENT_COMPLETE.md

configs/data/
├── vpn_users.json                # User profiles and configurations
└── manual_orders.json            # Test/fake orders for development
```

## Order Management Workflow

### 1. Order Verification Process

```
User enters Order ID → System verifies order → Creates/Updates user profile
```

**API Endpoint**: `POST /vpn-config-generator/api/order/verify`

**Request**:
```json
{
  "order_sn": "TEST001"
}
```

**Response**:
```json
{
  "success": true,
  "user_uuid": "uuid-string",
  "order_sn": "TEST001",
  "buyer_username": "testuser1",
  "sku": "vpn_monthly",
  "var_sku": "vpn_monthly_digi",
  "is_repeat_customer": false,
  "assigned_telco": null,
  "allowed_telcos": [],
  "is_restricted": false,
  "order_claimed": false,
  "message": "Order verified successfully"
}
```

### 2. Configuration Generation Process

The system supports two main actions:

#### A. Create New UUID
- Generates fresh VPN configurations across all relevant servers
- Creates new client entries in the VPN system
- Stores configuration details in user profile

#### B. Renew Existing UUID
- Extends validity of existing configurations
- Updates expiry dates without creating new clients
- Maintains same UUID across all servers

**API Endpoint**: `POST /vpn-config-generator/api/order/generate-config`

**Request**:
```json
{
  "user_uuid": "uuid-string",
  "order_sn": "TEST001",
  "server": "1",
  "days": "30",
  "telco": "digi",
  "plan": "booster",
  "action": "create"
}
```

**Response**:
```json
{
  "success": true,
  "client_id": "uuid-string",
  "numeric_id": 123,
  "config": "vless://uuid@server:port?params...",
  "created_date": "01-01-2024",
  "expired_date": "31-01-2024",
  "server_name": "SG-01",
  "message": "New configuration created successfully"
}
```

## Telco and Plan System

### Telco Configuration Structure

Each telco has multiple plans with specific VLESS templates:

```json
{
  "telcos": {
    "digi": {
      "id": "digi",
      "name": "Digi",
      "description": "Digi Telecommunications Malaysia",
      "enabled": true,
      "plans": {
        "booster": {
          "id": "booster",
          "name": "Booster",
          "description": "Digi Booster unlimited plan",
          "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#DU_{server_name}_{expired_date}",
          "variables": {
            "uuid": "Client UUID",
            "port": "Server port",
            "path": "WebSocket path",
            "server_domain": "Server domain",
            "server_name": "Server name",
            "expired_date": "Expiry date"
          },
          "enabled": true
        }
      }
    }
  }
}
```

### Available Telcos and Plans

1. **Digi**: booster, booster2, social, social2
2. **Maxis**: hotlink, postpaid, zerolution
3. **Celcom**: booster, booster2
4. **U Mobile**: funz, no_plan4
5. **TuneTalk**: booster, booster2, booster3
6. **Yes**: no_plan2
7. **Yoodo**: booster, booster2, booster3, pubg, mobilelegend
8. **Unifi**: bebas, wow

## SKU-Based Restrictions

### Restriction Types

1. **Open Access**: Regular SKUs allow access to all telcos
2. **Restricted Access**: SKUs with `my_` prefix have limitations
3. **Single Telco Lock**: Some SKUs lock users to first selected telco

### Implementation

```python
def can_user_access_telco(self, user_uuid: str, telco_id: str) -> Tuple[bool, str]:
    user = self.get_user(user_uuid)
    if not user:
        return False, "User not found"

    if not user.is_restricted:
        return True, ""

    if user.assigned_telco:
        if user.assigned_telco == telco_id:
            return True, ""
        else:
            return False, f"You are restricted to the {user.assigned_telco} telco."
    
    if user.allowed_telcos and telco_id not in user.allowed_telcos:
        return False, f"Your SKU does not permit access to the {telco_id} telco."

    return True, ""
```

## Server Selection Logic

### SKU-Based Server Tags

The system uses VPN strategy factory to determine appropriate servers:

```python
from plugins.vpn.strategies.strategy_factory import VPNStrategyFactory

server_tags = VPNStrategyFactory.get_server_tags_for_sku(sku)
```

### Server Selection Priority

1. **User Selection**: If user specifies a server ID
2. **SKU-Based**: Servers matching SKU tags
3. **Auto Selection**: First available online server
4. **Fallback**: Any available server

## User Interface

### Order Configuration Page

**URL**: `/vpn-config-generator/order-config`

**Features**:
- Order ID verification
- Action selection (Create/Renew)
- UUID selection for renewal
- Server and validity configuration
- Telco and plan selection
- Real-time configuration generation

### Interface Flow

1. **Order Input**: User enters Shopee order ID
2. **Verification**: System validates order and shows user info
3. **Action Selection**: Choose between creating new or renewing existing UUID
4. **Configuration**: Select server, validity, telco, and plan
5. **Generation**: System creates/renews VPN configuration
6. **Result**: Display generated VLESS configuration

## API Endpoints

### Order Management
- `POST /api/order/verify` - Verify Shopee order
- `POST /api/order/generate-config` - Generate VPN configuration
- `POST /api/order/user-configs` - Get user's existing configurations
- `POST /api/order/get-suggestions` - Get SKU-based suggestions

### Server Management
- `POST /api/servers/by-tags` - Get servers filtered by tags

### Configuration Management
- `GET /api/telcos` - Get all telco configurations
- `GET /api/config` - Get plugin configuration

## Testing

### Test Page

**URL**: `/vpn-config-generator/test-order`

**Features**:
- Order verification testing
- Telco configuration loading
- Server availability checking
- System status monitoring

### Test Data

Sample orders are provided in `configs/data/manual_orders.json`:

```json
[
  {
    "order_sn": "TEST001",
    "buyer_username": "testuser1",
    "var_sku": "vpn_monthly_digi",
    "sku": "vpn_monthly",
    "status": "COMPLETED"
  }
]
```

## Security Features

### Access Control
- Order ownership verification
- SKU-based telco restrictions
- User session management

### Data Protection
- UUID-based user identification
- Encrypted configuration storage
- Audit logging for all operations

## Error Handling

### Common Error Scenarios

1. **Order Not Found**: Invalid or non-existent order ID
2. **Access Denied**: User trying to access restricted telco
3. **Server Unavailable**: No servers available for configuration
4. **Configuration Failed**: VPN API errors during generation

### Error Response Format

```json
{
  "success": false,
  "error": "Detailed error message"
}
```

## Configuration Files

### Telco Configurations
- **File**: `plugins/vpn_config_generator/telco_configs.json`
- **Purpose**: Define telco providers and their plan templates
- **Format**: JSON with nested telco and plan structures

### User Data
- **File**: `configs/data/vpn_users.json`
- **Purpose**: Store user profiles and generated configurations
- **Format**: JSON with user UUID as keys

### Test Orders
- **File**: `configs/data/manual_orders.json`
- **Purpose**: Provide test data for development
- **Format**: JSON array of order objects

## Deployment

### Requirements
1. VPN plugin must be installed and configured
2. Telco configurations must be loaded
3. Server data must be available via VPN API
4. Persistent storage directories must be writable

### Installation Steps
1. Ensure all files are in place
2. Restart the application to load new routes
3. Verify telco configurations are loaded
4. Test with sample orders

## Troubleshooting

### Common Issues

1. **"VPNOrderService object has no attribute '_save_users'"**
   - **Solution**: Fixed by correcting method name to `save_users()`

2. **"Order service not available"**
   - **Solution**: Ensure VPNOrderService is properly initialized in plugin

3. **"VPN plugin not available"**
   - **Solution**: Verify VPN plugin is installed and running

4. **"No servers available"**
   - **Solution**: Check VPN API connectivity and server status

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger('plugins.vpn_config_generator').setLevel(logging.DEBUG)
```

## Future Enhancements

### Planned Features
1. **Bulk Operations**: Process multiple orders simultaneously
2. **Configuration History**: Track all changes and renewals
3. **Usage Analytics**: Monitor configuration usage patterns
4. **Automated Renewals**: Schedule automatic renewals before expiry
5. **Mobile Interface**: Responsive design for mobile devices

### Integration Opportunities
1. **Payment Gateway**: Direct payment processing
2. **Notification System**: Email/SMS alerts for renewals
3. **Customer Portal**: Self-service configuration management
4. **API Documentation**: Swagger/OpenAPI specifications

## Conclusion

The VPN Order Management System provides a comprehensive solution for handling VPN configuration orders through a web-based interface. It supports both new configuration creation and existing configuration renewal, with proper access control and error handling.

The system is designed to be scalable, maintainable, and user-friendly, providing a solid foundation for VPN service management in an e-commerce environment.

For support or questions, refer to the troubleshooting section or check the application logs for detailed error information.