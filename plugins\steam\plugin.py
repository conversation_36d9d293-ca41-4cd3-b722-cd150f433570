"""
Steam Plugin Implementation
Handles Steam authentication codes, inventory management, and order processing
"""

import logging
from typing import Dict, Any, Optional
from flask import Blueprint, jsonify, request
from core.plugin_manager import PluginInterface, DashboardWidget
from .services.steam_email_service import SteamEmailService
from .services.steam_inventory_service import SteamInventoryService
from .services.steam_order_service import SteamOrderService
from .routes.steam_routes import create_steam_blueprint

logger = logging.getLogger(__name__)

class Plugin(PluginInterface):
    """Steam Plugin Main Class"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "steam"
        self.version = "1.0.0"
        self.description = "Steam integration for authentication codes and inventory management"
        self.dependencies = []
        
        # Services
        self.email_service = None
        self.inventory_service = None
        self.order_service = None
        self.blueprint = None
        
    def initialize(self) -> bool:
        """Initialize the Steam plugin"""
        try:
            logger.info("Initializing Steam plugin...")
            
            # Initialize services
            self.email_service = SteamEmailService(self.config)
            self.inventory_service = SteamInventoryService(self.config)
            self.order_service = SteamOrderService(self.config)
            
            # Create blueprint with services
            self.blueprint = create_steam_blueprint(
                self.email_service,
                self.inventory_service,
                self.order_service
            )
            
            logger.info("Steam plugin initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Steam plugin: {e}")
            return False
            
    def shutdown(self) -> bool:
        """Shutdown the Steam plugin"""
        try:
            logger.info("Shutting down Steam plugin...")
            
            # Cleanup services
            if self.email_service:
                self.email_service.cleanup()
            if self.inventory_service:
                self.inventory_service.cleanup()
            if self.order_service:
                self.order_service.cleanup()
                
            logger.info("Steam plugin shutdown successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error shutting down Steam plugin: {e}")
            return False
            
    def get_blueprint(self) -> Optional[Blueprint]:
        """Return Flask blueprint for Steam routes"""
        return self.blueprint
        
    def get_config_schema(self) -> Dict[str, Any]:
        """Return configuration schema for Steam plugin"""
        return {
            "type": "object",
            "properties": {
                "enabled": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable Steam plugin"
                },
                "cooldown_minutes": {
                    "type": "integer",
                    "default": 5,
                    "description": "Cooldown period between auth code requests"
                },
                "max_retries": {
                    "type": "integer",
                    "default": 3,
                    "description": "Maximum retries for failed operations"
                },
                "cache_ttl": {
                    "type": "integer",
                    "default": 300,
                    "description": "Cache TTL in seconds"
                }
            }
        }
        
    def get_dashboard_widgets(self) -> list:
        """Return list of dashboard widgets provided by Steam plugin"""
        widgets = []
        
        # Steam session cooldowns widget
        cooldown_widget = DashboardWidget(
            widget_id="steam-cooldowns",
            title="Active Steam Session Cooldowns",
            position="main",
            order=40,
            size="large"
        )
        cooldown_widget.data_endpoint = "/api/steam/widget/cooldowns"
        cooldown_widget.refresh_interval = 30  # Refresh every 30 seconds
        widgets.append(cooldown_widget)
        
        return widgets
    
    def get_widget_data(self, widget_id: str) -> dict:
        """Get data for a specific widget"""
        if widget_id == "steam-cooldowns":
            # This would normally fetch real cooldown data
            # For now, return empty data structure
            return {
                "cooldowns": []
            }
        return {"error": f"Unknown widget: {widget_id}"}
        
    def get_status(self) -> Dict[str, Any]:
        """Return Steam plugin status"""
        status = super().get_status()
        
        if hasattr(self, '_initialized') and self._initialized:
            status.update({
                'services': {
                    'email_service': self.email_service is not None,
                    'inventory_service': self.inventory_service is not None,
                    'order_service': self.order_service is not None
                },
                'email_accounts': len(self.config.get('email_config', {}).get('credentials', [])),
                'inventory_items': self.inventory_service.get_item_count() if self.inventory_service else 0
            })
            
        return status
        
    def load_config(self, config: Dict[str, Any]):
        """Load Steam plugin configuration"""
        super().load_config(config)
        
        # Update services with new config
        if hasattr(self, '_initialized') and self._initialized:
            if self.email_service:
                self.email_service.update_config(config)
            if self.inventory_service:
                self.inventory_service.update_config(config)
            if self.order_service:
                self.order_service.update_config(config)

    def _register_api_methods(self):
        # Implementation of _register_api_methods method
        pass
