[{"id": "c42794a6-c4ad-4ee4-ac42-9ee70b1284fc", "name": "VLESS WebSocket", "description": "VLESS protocol with WebSocket transport", "template": "vless://{uuid}@{server}:{port}?encryption=none&type=ws&path={path}&host={host}#{name}", "variables": {"uuid": "Client UUID", "server": "Server address", "port": "Server port", "path": "WebSocket path", "host": "Host header", "name": "Configuration name"}, "category": "vless", "enabled": true, "created_at": "2025-06-17T01:38:07.870789", "updated_at": "2025-06-17T01:38:07.870796"}, {"id": "ffb81016-954d-48be-a938-600dd47f669c", "name": "VLESS TCP", "description": "VLESS protocol with TCP transport", "template": "vless://{uuid}@{server}:{port}?encryption=none&type=tcp#{name}", "variables": {"uuid": "Client UUID", "server": "Server address", "port": "Server port", "name": "Configuration name"}, "category": "vless", "enabled": true, "created_at": "2025-06-17T01:38:07.870805", "updated_at": "2025-06-17T01:38:07.870806"}, {"id": "cc012a8f-534e-4606-8730-0791e33dfd21", "name": "VMess WebSocket", "description": "VMess protocol with WebSocket transport", "template": "vmess://{base64_config}", "variables": {"base64_config": "Base64 encoded VMess configuration"}, "category": "vmess", "enabled": true, "created_at": "2025-06-17T01:38:07.870812", "updated_at": "2025-06-17T01:38:07.870813"}, {"id": "a719ce8f-7620-4914-8e28-fc3209b5f4c0", "name": "Trojan", "description": "Trojan protocol configuration", "template": "trojan://{password}@{server}:{port}?security=tls&type=tcp#{name}", "variables": {"password": "Trojan password", "server": "Server address", "port": "Server port", "name": "Configuration name"}, "category": "trojan", "enabled": true, "created_at": "2025-06-17T01:38:07.870817", "updated_at": "2025-06-17T01:38:07.870818"}]