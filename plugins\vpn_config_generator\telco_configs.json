{"telcos": {"digi": {"id": "digi", "name": "<PERSON><PERSON>", "description": "Digi Telecommunications Malaysia", "enabled": true, "plans": {"booster": {"id": "booster", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> unlimited plan", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#DU_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🚀 DIGI BOOSTER CONFIG 🚀\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Server: {server_name}\n⏰ Validity: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🎮 Optimized for Gaming\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster2": {"id": "booster2", "name": "Booster 2", "description": "<PERSON><PERSON> alternative plan", "template": "vless://{uuid}@opensignal.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=#DU2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🚀 DIGI BOOSTER 2 CONFIG 🚀\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Server: {server_name}\n⏰ Validity: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🎮 Alternative Booster Config\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "social": {"id": "social", "name": "Social", "description": "Digi Social media optimized plan", "template": "vless://{uuid}@ssl.google-analytics.com.api.digi.com.my.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=ssl.google-analytics.com#DS_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "📱 DIGI SOCIAL CONFIG 📱\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Server: {server_name}\n⏰ Validity: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n📲 Social Media Optimized\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "social2": {"id": "social2", "name": "Social 2", "description": "Digi Social alternative plan", "template": "vless://{uuid}@m.facebook.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.facebook.com#DS2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "📱 DIGI SOCIAL 2 CONFIG 📱\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Server: {server_name}\n⏰ Validity: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n📲 Facebook Optimized\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "maxis": {"id": "maxis", "name": "<PERSON><PERSON>", "description": "Maxis Communications Malaysia", "enabled": true, "plans": {"hotlink": {"id": "hotlink", "name": "Hotlink", "description": "Hotlink prepaid plan with Speedtest host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.speedtest.net&path=%2F%3Fed%3D2048#MAXIS-HOTLINK-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "🔥 MAXIS HOTLINK CONFIG 🔥\n═══════════════════════════════════════════════════\n📱 Server: {server_id}\n⏱️ Period: {days} Day(s)\n📡 Network: {telco} {plan}\n👤 User: {username}\n📅 Created: {created_date}\n⏳ Expires: {expired_date}\n🚀 High Speed Prepaid Connection\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "postpaid": {"id": "postpaid", "name": "Postpaid", "description": "Maxis postpaid plan with Fast.com host", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=fast.com&path=%2F%3Fed%3D2048#MAXIS-POSTPAID-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "info_message_template": "💎 MAXIS POSTPAID PREMIUM 💎\n\n📧 Account: {email}\n👤 User: {shopee_username}\n🖥️ Server: {server_name}\n📅 Activated: {created_date_formatted}\n⏰ Valid Until: {expired_date_formatted}\n📡 Network: {telco} {plan}\n⏳ Duration: {validity}\n🆔 Short ID: {short_uuid}\n\n✨ Premium Postpaid Experience ✨", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "zerolution": {"id": "zerolution", "name": "Zerolution", "description": "Maxis Zerolution plan", "template": "vless://{uuid}@{server}:80?encryption=none&security=none&type=ws&host=www.instagram.com&path=%2F%3Fed%3D2048#MAXIS-ZERO-{username}-{days}D", "variables": {"uuid": "Client UUID", "server": "Server domain", "username": "Username", "days": "Validity days"}, "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "celcom": {"id": "celcom", "name": "Celcom", "description": "Celcom Axiata Malaysia", "enabled": true, "plans": {"booster": {"id": "booster", "name": "<PERSON><PERSON><PERSON>", "description": "Celcom Booster plan with Speedtest host", "template": "vless://{uuid}@www.speedtest.net.cdn.cloudflare.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#CB_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🚀 CELCOM BOOSTER CONFIG 🚀\n┌─────────────────────────────────────────────────┐\n│ 📱 Server: {server_name}                        │\n│ ⏰ Duration: {days} Day(s)                      │\n│ 📡 Provider: {telco} {plan}                     │\n│ 👤 User: {username}                             │\n│ 📅 Created: {created_date}                      │\n│ ⏳ Expires: {expired_date}                      │\n│ 🏆 Speedtest Optimized                          │\n└─────────────────────────────────────────────────┘", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster2": {"id": "booster2", "name": "Booster 2", "description": "Celcom Booster alternative plan", "template": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#CB2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🚀 CELCOM BOOSTER 2 CONFIG 🚀\n┌─────────────────────────────────────────────────┐\n│ 📱 Server: {server_name}                        │\n│ ⏰ Duration: {days} Day(s)                      │\n│ 📡 Provider: {telco} {plan}                     │\n│ 👤 User: {username}                             │\n│ 📅 Created: {created_date}                      │\n│ ⏳ Expires: {expired_date}                      │\n│ 🏆 Alternative Booster Config                   │\n└─────────────────────────────────────────────────┘", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "umobile": {"id": "umobile", "name": "U Mobile", "description": "U Mobile Malaysia", "enabled": true, "plans": {"funz": {"id": "funz", "name": "Funz", "description": "U Mobile Funz plan optimized for gaming", "template": "vless://{uuid}@www.pubgmobile.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.pubgmobile.com#UN_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎮 U MOBILE FUNZ CONFIG 🎮\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🎯 PUBG Mobile Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "no_plan4": {"id": "no_plan4", "name": "No Plan 4", "description": "U Mobile No Plan 4 configuration", "template": "vless://{uuid}@***********:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=cityofpalacios.org.{server_domain}#UNP4_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🚀 U MOBILE NO PLAN 4 CONFIG 🚀\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🔥 Special No Plan Configuration\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "tunetalk": {"id": "tunetalk", "name": "TuneTalk", "description": "TuneTalk Malaysia", "enabled": true, "plans": {"booster": {"id": "booster", "name": "<PERSON><PERSON><PERSON>", "description": "TuneTalk Booster plan with Speedtest host", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#TB_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎵 TUNETALK BOOSTER CONFIG 🎵\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🏆 Speedtest Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster2": {"id": "booster2", "name": "Booster 2", "description": "TuneTalk Booster alternative plan", "template": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#TB2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎵 TUNETALK BOOSTER 2 CONFIG 🎵\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🏆 Alternative Booster\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster3": {"id": "booster3", "name": "Booster 3", "description": "TuneTalk Booster plan with Fast.com host", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=api.fast.com.{server_domain}#TB3_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎵 TUNETALK BOOSTER 3 CONFIG 🎵\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n⚡ Fast.com Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "yes": {"id": "yes", "name": "Yes", "description": "Yes Malaysia", "enabled": true, "plans": {"no_plan2": {"id": "no_plan2", "name": "No Plan 2", "description": "Yes No Plan 2 configuration", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=tap-database.who.int.{server_domain}#YN2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "✅ YES NO PLAN 2 CONFIG ✅\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🔥 Special No Plan Configuration\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "yoodo": {"id": "yoodo", "name": "<PERSON><PERSON><PERSON>", "description": "Yoodo Malaysia", "enabled": true, "plans": {"booster": {"id": "booster", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> plan with Speedtest host", "template": "vless://{uuid}@*************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#YoB_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎯 YOODO BOOSTER CONFIG 🎯\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🏆 Speedtest Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster2": {"id": "booster2", "name": "Booster 2", "description": "<PERSON><PERSON><PERSON> Booster alternative plan", "template": "vless://{uuid}@www.speedtest.net:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host={server_domain}#YoB2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎯 YOODO BOOSTER 2 CONFIG 🎯\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🏆 Alternative Booster\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "booster3": {"id": "booster3", "name": "Booster 3", "description": "<PERSON><PERSON><PERSON> plan with Fast.com host", "template": "vless://{uuid}@**************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=api.fast.com.{server_domain}#YoB3_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎯 YOODO BOOSTER 3 CONFIG 🎯\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n⚡ Fast.com Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "pubg": {"id": "pubg", "name": "PUBG", "description": "Yoodo PUBG gaming plan", "template": "vless://{uuid}@m.pubgmobile.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.pubgmobile.com#YoP_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🎮 YOODO PUBG CONFIG 🎮\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🎯 PUBG Mobile Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "mobilelegend": {"id": "mobilelegend", "name": "Mobile Legend", "description": "Yoodo Mobile Legends gaming plan", "template": "vless://{uuid}@m.mobilelegends.com.{server_domain}:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=m.mobilelegends.com#YoM_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🏆 YOODO MOBILE LEGENDS CONFIG 🏆\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🎯 Mobile Legends Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "unifi": {"id": "unifi", "name": "Unifi", "description": "Unifi Malaysia", "enabled": true, "plans": {"bebas": {"id": "bebas", "name": "<PERSON><PERSON>", "description": "Unifi Bebas plan with Speedtest host", "template": "vless://{uuid}@************:{port}?security=none&encryption=none&type=ws&headerType=none&path={path}&host=www.speedtest.net.{server_domain}#UnN_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🌐 UNIFI BEBAS CONFIG 🌐\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🏆 Speedtest Optimized\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, "wow": {"id": "wow", "name": "WOW", "description": "Unifi WOW plan", "template": "vless://{uuid}@covidnow.pages.dev:{port}?security=none&encryption=none&type=ws&headerType=none&path=ws://{server_domain}{path}&host=www.speedtest.net#UnN2_{server_name}_{expired_date}", "variables": {"uuid": "Client UUID", "port": "Server port", "path": "WebSocket path", "server_domain": "Server domain", "server_name": "Server name", "expired_date": "Expiry date"}, "info_message_template": "🌐 UNIFI WOW CONFIG 🌐\\n═══════════════════════════════════════════════════\\n📱 Server: {server_name}\\n⏰ Duration: {days} Day(s)\\n📡 Network: {telco} {plan}\\n👤 User: {username}\\n📅 Created: {created_date}\\n⏳ Expires: {expired_date}\\n🎉 WOW Special Config\\n═══════════════════════════════════════════════════", "enabled": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}}}